(()=>{var e={};e.id=7946,e.ids=[7946],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},8455:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>u}),r(40957),r(2223),r(78398),r(57757),r(48045);var n=r(40060),s=r(33581),i=r(57567),a=r.n(i),l=r(51650),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let u=["",{children:["inventory",{children:["suppliers",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,40957)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\suppliers\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2223)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\suppliers\\new\\page.tsx"],d="/inventory/suppliers/new/page",p={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/inventory/suppliers/new/page",pathname:"/inventory/suppliers/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},85363:(e,t,r)=>{Promise.resolve().then(r.bind(r,71462))},53074:(e,t,r)=>{Promise.resolve().then(r.bind(r,91952))},83179:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",r="minute",n="hour",s="week",i="month",a="quarter",l="year",o="date",u="Invalid Date",c=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,d=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,p=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},h="en",f={};f[h]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||"th")+"]"}};var m="$isDayjsObject",v=function(e){return e instanceof $||!(!e||!e[m])},x=function e(t,r,n){var s;if(!t)return h;if("string"==typeof t){var i=t.toLowerCase();f[i]&&(s=i),r&&(f[i]=r,s=i);var a=t.split("-");if(!s&&a.length>1)return e(a[0])}else{var l=t.name;f[l]=t,s=l}return!n&&s&&(h=s),s||!n&&h},y=function(e,t){if(v(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new $(r)},g={s:p,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+p(Math.floor(r/60),2,"0")+":"+p(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),s=t.clone().add(n,i),a=r-s<0,l=t.clone().add(n+(a?-1:1),i);return+(-(n+(r-s)/(a?s-l:l-s))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(u){return({M:i,y:l,w:s,d:"day",D:o,h:n,m:r,s:t,ms:e,Q:a})[u]||String(u||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};g.l=x,g.i=v,g.w=function(e,t){return y(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var $=function(){function p(e){this.$L=x(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[m]=!0}var h=p.prototype;return h.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(g.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(c);if(n){var s=n[2]-1||0,i=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,i)):new Date(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,i)}}return new Date(t)}(e),this.init()},h.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},h.$utils=function(){return g},h.isValid=function(){return this.$d.toString()!==u},h.isSame=function(e,t){var r=y(e);return this.startOf(t)<=r&&r<=this.endOf(t)},h.isAfter=function(e,t){return y(e)<this.startOf(t)},h.isBefore=function(e,t){return this.endOf(t)<y(e)},h.$g=function(e,t,r){return g.u(e)?this[t]:this.set(r,e)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(e,a){var u=this,c=!!g.u(a)||a,d=g.p(e),p=function(e,t){var r=g.w(u.$u?Date.UTC(u.$y,t,e):new Date(u.$y,t,e),u);return c?r:r.endOf("day")},h=function(e,t){return g.w(u.toDate()[e].apply(u.toDate("s"),(c?[0,0,0,0]:[23,59,59,999]).slice(t)),u)},f=this.$W,m=this.$M,v=this.$D,x="set"+(this.$u?"UTC":"");switch(d){case l:return c?p(1,0):p(31,11);case i:return c?p(1,m):p(0,m+1);case s:var y=this.$locale().weekStart||0,$=(f<y?f+7:f)-y;return p(c?v-$:v+(6-$),m);case"day":case o:return h(x+"Hours",0);case n:return h(x+"Minutes",1);case r:return h(x+"Seconds",2);case t:return h(x+"Milliseconds",3);default:return this.clone()}},h.endOf=function(e){return this.startOf(e,!1)},h.$set=function(s,a){var u,c=g.p(s),d="set"+(this.$u?"UTC":""),p=((u={}).day=d+"Date",u[o]=d+"Date",u[i]=d+"Month",u[l]=d+"FullYear",u[n]=d+"Hours",u[r]=d+"Minutes",u[t]=d+"Seconds",u[e]=d+"Milliseconds",u)[c],h="day"===c?this.$D+(a-this.$W):a;if(c===i||c===l){var f=this.clone().set(o,1);f.$d[p](h),f.init(),this.$d=f.set(o,Math.min(this.$D,f.daysInMonth())).$d}else p&&this.$d[p](h);return this.init(),this},h.set=function(e,t){return this.clone().$set(e,t)},h.get=function(e){return this[g.p(e)]()},h.add=function(e,a){var o,u=this;e=Number(e);var c=g.p(a),d=function(t){var r=y(u);return g.w(r.date(r.date()+Math.round(t*e)),u)};if(c===i)return this.set(i,this.$M+e);if(c===l)return this.set(l,this.$y+e);if("day"===c)return d(1);if(c===s)return d(7);var p=((o={})[r]=6e4,o[n]=36e5,o[t]=1e3,o)[c]||1,h=this.$d.getTime()+e*p;return g.w(h,this)},h.subtract=function(e,t){return this.add(-1*e,t)},h.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||u;var n=e||"YYYY-MM-DDTHH:mm:ssZ",s=g.z(this),i=this.$H,a=this.$m,l=this.$M,o=r.weekdays,c=r.months,p=r.meridiem,h=function(e,r,s,i){return e&&(e[r]||e(t,n))||s[r].slice(0,i)},f=function(e){return g.s(i%12||12,e,"0")},m=p||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n};return n.replace(d,function(e,n){return n||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return g.s(t.$y,4,"0");case"M":return l+1;case"MM":return g.s(l+1,2,"0");case"MMM":return h(r.monthsShort,l,c,3);case"MMMM":return h(c,l);case"D":return t.$D;case"DD":return g.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return h(r.weekdaysMin,t.$W,o,2);case"ddd":return h(r.weekdaysShort,t.$W,o,3);case"dddd":return o[t.$W];case"H":return String(i);case"HH":return g.s(i,2,"0");case"h":return f(1);case"hh":return f(2);case"a":return m(i,a,!0);case"A":return m(i,a,!1);case"m":return String(a);case"mm":return g.s(a,2,"0");case"s":return String(t.$s);case"ss":return g.s(t.$s,2,"0");case"SSS":return g.s(t.$ms,3,"0");case"Z":return s}return null}(e)||s.replace(":","")})},h.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},h.diff=function(e,o,u){var c,d=this,p=g.p(o),h=y(e),f=(h.utcOffset()-this.utcOffset())*6e4,m=this-h,v=function(){return g.m(d,h)};switch(p){case l:c=v()/12;break;case i:c=v();break;case a:c=v()/3;break;case s:c=(m-f)/6048e5;break;case"day":c=(m-f)/864e5;break;case n:c=m/36e5;break;case r:c=m/6e4;break;case t:c=m/1e3;break;default:c=m}return u?c:g.a(c)},h.daysInMonth=function(){return this.endOf(i).$D},h.$locale=function(){return f[this.$L]},h.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=x(e,t,!0);return n&&(r.$L=n),r},h.clone=function(){return g.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},p}(),b=$.prototype;return y.prototype=b,[["$ms",e],["$s",t],["$m",r],["$H",n],["$W","day"],["$M",i],["$y",l],["$D",o]].forEach(function(e){b[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),y.extend=function(e,t){return e.$i||(e(t,$,y),e.$i=!0),y},y.locale=x,y.isDayjs=v,y.unix=function(e){return y(1e3*e)},y.en=f[h],y.Ls=f,y.p={},y},e.exports=t()},18479:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,s=Array(n);++r<n;)s[r]=t(e[r],r,e);return s}},22060:(e,t,r)=>{var n=r(51858),s=r(18479),i=r(55813),a=r(15903),l=1/0,o=n?n.prototype:void 0,u=o?o.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return s(t,e)+"";if(a(t))return u?u.call(t):"";var r=t+"";return"0"==r&&1/t==-l?"-0":r}},15903:(e,t,r)=>{var n=r(55296),s=r(48377);e.exports=function(e){return"symbol"==typeof e||s(e)&&"[object Symbol]"==n(e)}},16266:(e,t,r)=>{var n=r(22060);e.exports=function(e){return null==e?"":n(e)}},3233:(e,t,r)=>{var n=r(16266),s=0;e.exports=function(e){var t=++s;return n(e)+t}},71462:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(98768);r(60343);var s=r(64837);function i({children:e}){return n.jsx(s.Z,{children:e})}},91952:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>$});var n=r(98768),s=r(60343),i=r(72548),a=r(66263),l=r(71890),o=r(52269),u=r(39544),c=r(60797),d=r(34376),p=r(13609),h=r(76342),f=r(69424),m=r(14734),v=r(78965),x=r(25394),y=r(90949);function g({supplierId:e}){let t=(0,f.useRouter)(),{toast:r}=(0,d.pm)(),[g,$]=(0,s.useState)([]),[b,j]=(0,s.useState)([]),[w,S]=(0,s.useState)([{name:"",email:"",phone:""}]),M=async()=>{let e=document.getElementById("supplier-name").value,n=document.getElementById("supplier-website").value,s=document.getElementById("supplier-phone").value,i=document.getElementById("supplier-email").value,a=document.getElementById("supplier-address").value,l=document.getElementById("supplier-notes").value;if(""===e)return r({variant:"destructive",title:"Error",description:"Please fill supplier's name!"});let o=await C({variables:{input:{name:e,address:a,website:n,email:i,phone:s,notes:l}}}),u=o.data?.createSupplier.id??0;if(0==u)return r({variant:"destructive",title:"Error",description:"Error creating new supplier"});w.forEach(async e=>{let t={...e,supplierID:u};delete t.id,await k({variables:{input:t}})}),t.back()},[C,{loading:N}]=(0,i.D)(h.aL5,{onCompleted:e=>{},onError:e=>{console.error("mutationcreateSupplier error",e)}}),[k,{loading:D}]=(0,i.D)(m.z,{onCompleted:e=>{},onError:e=>{console.error("mutationcreateSupplierContact error",e)}}),[_]=(0,i.D)(h.zfn,{onCompleted:e=>{let t=e.createSeaLogsFileLinks;t.id>0&&($([...g,t]),b?j([...b,{label:t.link,value:t.id}]):j([{label:t.link,value:t.id}]))},onError:e=>{console.error("createSeaLogsFileLinksEntry error",e)}}),L=e=>{j(b.filter(t=>t!==e))},P=e=>(0,n.jsxs)("div",{className:"flex justify-between align-middle mr-2 w-fit",children:[n.jsx(a.default,{href:e.label,target:"_blank",className:"ml-2 ",children:e.label}),n.jsx("div",{className:"ml-2 ",children:n.jsx(u.Button,{variant:"ghost",size:"icon",iconLeft:p.Z,iconOnly:!0,onClick:()=>L(e)})})]});return(0,n.jsxs)("div",{className:"space-y-6",children:[n.jsx(x.Bu,{title:"New Supplier"}),(0,n.jsxs)(x.Zb,{className:"mx-2.5",children:[(0,n.jsxs)(x.Ol,{children:[n.jsx(x.H4,{children:"Company Information"}),n.jsx(x.P,{children:"Enter the basic information about the supplier company including contact details and address."})]}),n.jsx(x.aY,{className:"space-y-8",children:(0,n.jsxs)("div",{className:"space-y-5",children:[n.jsx(c.Label,{label:"Company Name",htmlFor:"supplier-name",children:n.jsx(l.I,{id:"supplier-name",type:"text",placeholder:"Supplier name",className:"w-full"})}),n.jsx(c.Label,{label:"Website",htmlFor:"supplier-website",children:n.jsx(l.I,{id:"supplier-website",type:"text",placeholder:"Company website",className:"w-full",onKeyDown:async e=>{if("Enter"===e.key){let t=e.target.value;await _({variables:{input:{link:t}}}),r({title:"Website added",description:`Added ${t} to supplier links`})}}})}),(b||g).length>0&&n.jsx("div",{className:"w-full",children:n.jsx(c.Label,{label:"Linked Websites",children:n.jsx("div",{className:"flex flex-wrap gap-2",children:b?b.map(e=>n.jsx("div",{className:"inline-block",children:P(e)},e.value)):g.map(e=>n.jsx("div",{className:"inline-block",children:P(e)},e.value))})})}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5",children:[n.jsx(c.Label,{label:"Phone Number",htmlFor:"supplier-phone",children:n.jsx(l.I,{id:"supplier-phone",type:"text",placeholder:"Phone number"})}),n.jsx(c.Label,{label:"Email Address",htmlFor:"supplier-email",children:n.jsx(l.I,{id:"supplier-email",type:"email",placeholder:"Email address"})})]}),n.jsx(c.Label,{label:"Address",htmlFor:"supplier-address",children:n.jsx(o.Textarea,{id:"supplier-address",rows:3,placeholder:"Supplier address",className:"resize-none"})})]})})]}),(0,n.jsxs)(x.Zb,{className:"mx-2.5",children:[(0,n.jsxs)(x.Ol,{children:[n.jsx(x.H4,{children:"Contact Persons"}),n.jsx(x.P,{children:"Enter the contact details (name, phone, and email) of the supplier's representative."})]}),n.jsx(x.aY,{children:n.jsx(y.Z,{data:w,setData:S})})]}),(0,n.jsxs)(x.Zb,{className:"mx-2.5",children:[(0,n.jsxs)(x.Ol,{children:[n.jsx(x.H4,{children:"Additional Notes"}),n.jsx(x.P,{children:"Add any additional information about this supplier that might be useful."})]}),n.jsx(x.aY,{children:n.jsx(c.Label,{label:"Notes",htmlFor:"supplier-notes",children:n.jsx(o.Textarea,{id:"supplier-notes",rows:5,placeholder:"Enter any additional notes about this supplier...",className:"resize-none"})})})]}),(0,n.jsxs)(v.V,{children:[n.jsx(u.Button,{variant:"back",onClick:()=>t.back(),children:"Cancel"}),n.jsx(u.Button,{onClick:M,children:"Create supplier"})]})]})}function $(){return n.jsx(g,{supplierId:0})}},14734:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var n=r(45519);let s=(0,n.ZP)`
    mutation CreateSupplierContact($input: CreateSupplierContactInput!) {
        createSupplierContact(input: $input) {
            id
            name
            phone
            email
            clientID
            supplierID
        }
    }
`},90949:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var n=r(98768),s=r(13609),i=r(52016),a=r(71890),l=r(39544),o=r(60797);let u=function({data:e,setData:t}){let r=e=>{t(t=>{let r=[...t];return r.splice(e,1),r})},u=(e,r,n)=>{t(t=>{let s=[...t];return s[r][e]=n,s})};return(0,n.jsxs)("div",{className:"space-y-5",children:[e.map((e,t)=>(0,n.jsxs)("div",{className:`space-y-4 ${t>0?"border-t pt-4":""}`,children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[n.jsx(o.Label,{label:"Contact Name",htmlFor:`supplier-contact_name${t}`,className:"text-sm font-medium",children:n.jsx(a.I,{id:`supplier-contact_name${t}`,type:"text",placeholder:"Contact Name",value:e.name,onChange:e=>u("name",t,e.target.value),className:"w-full"})}),n.jsx(o.Label,{label:"Contact Phone",htmlFor:`supplier-contact_phone${t}`,className:"text-sm font-medium",children:n.jsx(a.I,{id:`supplier-contact_phone${t}`,type:"text",placeholder:"Contact Phone",value:e.phone,onChange:e=>u("phone",t,e.target.value),className:"w-full"})}),n.jsx(o.Label,{label:"Contact Email",htmlFor:`supplier-contact_email${t}`,className:"text-sm font-medium",children:n.jsx(a.I,{id:`supplier-contact_email${t}`,type:"email",value:e.email,placeholder:"Contact Email",onChange:e=>u("email",t,e.target.value),className:"w-full"})})]}),t>0&&n.jsx("div",{className:"flex justify-end",children:n.jsx(l.Button,{variant:"destructive",size:"sm",iconLeft:s.Z,onClick:()=>r(t),children:"Remove Contact"})})]},t)),n.jsx("div",{className:"flex justify-end mt-4",children:n.jsx(l.Button,{variant:"outline",iconLeft:i.Z,onClick:()=>{t(e=>[...e,{name:"",phone:"",email:""}])},children:"Add Contact"})})]})}},2223:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\inventory\layout.tsx#default`)},40957:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\inventory\suppliers\new\page.tsx#default`)},84961:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},46020:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},23379:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},7671:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[864,8865,3563,6263,8189,9507,7602,6451,4234,2925,5394,4837,6342],()=>r(8455));module.exports=n})();