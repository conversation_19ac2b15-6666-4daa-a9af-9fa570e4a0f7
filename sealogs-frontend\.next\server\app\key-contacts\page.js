(()=>{var e={};e.id=2242,e.ids=[2242],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},14751:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>l}),r(9972),r(72487),r(78398),r(57757),r(48045);var t=r(40060),n=r(33581),i=r(57567),a=r.n(i),o=r(51650),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(s,c);let l=["",{children:["key-contacts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9972)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\key-contacts\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,72487)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\key-contacts\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\key-contacts\\page.tsx"],u="/key-contacts/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/key-contacts/page",pathname:"/key-contacts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},65653:(e,s,r)=>{Promise.resolve().then(r.bind(r,89388))},65667:(e,s,r)=>{Promise.resolve().then(r.bind(r,25667))},89388:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(98768);r(60343);var n=r(64837);function i({children:e}){return t.jsx(n.Z,{children:e})}},25667:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>j});var t=r(98768),n=r(37042),i=r(79418),a=r(60343),o=r(17380),c=r(66263),l=r(94060);r(46776);var d=r(39544),u=r(53363),p=r(54214),x=r(25394);function h(){let[e,s]=(0,a.useState)([]),[r,h]=(0,a.useState)(!1),[j,y]=(0,a.useState)({EDIT_KEY_CONTACT:void 0,DELETE_KEY_CONTACT:void 0}),[f,{called:g,loading:v}]=(0,i.t)(l.id,{fetchPolicy:"cache-and-network",onError:e=>{console.error("querySupplier error",e)}}),C=async(e=[])=>{if(e.length>0){let r=e.map(async e=>await f({variables:{filter:e}})),t=await Promise.all(r);s(t=(t=(t=t.filter(e=>e.data.readKeyContacts.nodes.length>0)).flatMap(e=>e.data.readKeyContacts.nodes)).filter((e,s,r)=>r.findIndex(s=>s.id===e.id)===s))}else{let{data:e}=await f();s(e?.readKeyContacts.nodes??[])}};return(0,t.jsxs)(t.Fragment,{children:[t.jsx(x.Bu,{title:"Key Contacts",actions:(0,t.jsxs)("div",{className:"flex items-center gap-2.5",children:[t.jsx(d.Button,{variant:r?"primary":"primaryOutline",onClick:()=>h(e=>!e),iconLeft:u.Z,children:"Group by Company"}),j.EDIT_KEY_CONTACT&&t.jsx(c.default,{href:"/key-contacts/create",children:t.jsx(d.Button,{iconLeft:p.Z,children:"New Key Contact"})})]})}),(0,t.jsxs)(x.Zb,{className:"mt-8",children:[t.jsx(x.Ol,{children:t.jsx(n.Z,{onChange:({type:e,data:s})=>{let r=[];"keyword"===e&&""!==s.value&&(r=["firstName","surname","phone","email"].map(function(e){return{[e]:{contains:s.value}}})),C(r)}})}),t.jsx(x.aY,{children:g&&v?t.jsx(o.hM,{}):t.jsx(t.Fragment,{children:0==e.length?t.jsx("div",{className:"text-center font-semibold w-full flex items-center justify-center h-20",children:"No Data Found"}):t.jsx(m,{data:e,groupByCompany:r})})})]})]})}let m=({data:e,groupByCompany:s})=>{if(!s)return(0,t.jsxs)(x.iA,{children:[t.jsx(x.xD,{children:(0,t.jsxs)(x.SC,{children:[t.jsx(x.ss,{children:"Key Contacts"}),t.jsx(x.ss,{children:"Email"}),t.jsx(x.ss,{children:"Phone"}),t.jsx(x.ss,{children:"Address"}),t.jsx(x.ss,{children:"Company"})]})}),t.jsx(x.RM,{children:e.map((e,s)=>(0,t.jsxs)(x.SC,{children:[t.jsx(x.pj,{children:(0,t.jsxs)(c.default,{href:`/key-contacts/edit?id=${e.id}`,children:[e.firstName," ",e.surname]})}),t.jsx(x.pj,{children:e.email??"-"}),(0,t.jsxs)(x.pj,{children:[(0,t.jsxs)("div",{className:"mb-1",children:["Phone: ",e.phone??"-"]}),(0,t.jsxs)("div",{children:["Cell Phone: ",e.cellPhone??"-"]})]}),t.jsx(x.pj,{children:e.address??"-"}),t.jsx(x.pj,{children:e.company?.title??"-"})]},s))})]});let r=e.reduce((s,r)=>{let t=`${r.companyID??0}`;if(Object.hasOwn(s,t))return s;let n=e.filter(e=>e.companyID===r.companyID);return{...s,[t]:n}},{});return t.jsx("div",{className:"w-full flex flex-col gap-4",children:Object.entries(r).map(function(s){let[r,n]=s,i=n[0].company.title??"No Company";return(0,t.jsxs)("div",{children:[t.jsx("div",{className:"border border-b-0 font-bold border-secondary pl-5 max-w-sm rounded-t-lg py-2 px-3",children:i}),t.jsx("div",{className:"border rounded-tl-none rounded-lg border-secondary pt-3",children:(0,t.jsxs)(x.iA,{children:[t.jsx(x.xD,{children:(0,t.jsxs)(x.SC,{children:[t.jsx(x.ss,{children:"Key Contacts"}),t.jsx(x.ss,{children:"Email"}),t.jsx(x.ss,{children:"Phone"}),t.jsx(x.ss,{children:"Address"}),t.jsx(x.ss,{children:"Company"})]})}),t.jsx(x.RM,{children:e.map((e,s)=>(0,t.jsxs)(x.SC,{children:[t.jsx(x.pj,{children:(0,t.jsxs)(c.default,{href:`/key-contacts/edit?id=${e.id}`,children:[e.firstName," ",e.surname]})}),t.jsx(x.pj,{children:e.email??"-"}),(0,t.jsxs)(x.pj,{children:[(0,t.jsxs)("div",{className:"mb-1",children:["Phone:"," ",e.phone??"-"]}),(0,t.jsxs)("div",{children:["Cell Phone:"," ",e.cellPhone??"-"]})]}),t.jsx(x.pj,{children:e.address??"-"}),t.jsx(x.pj,{children:e.company?.title??"-"})]},s))})]})})]})})})};function j(){return t.jsx(h,{})}},72487:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\key-contacts\layout.tsx#default`)},9972:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\key-contacts\page.tsx#default`)},53363:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(97428).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7380],()=>r(14751));module.exports=t})();