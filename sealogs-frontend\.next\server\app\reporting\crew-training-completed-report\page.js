(()=>{var e={};e.id=939,e.ids=[939],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},23611:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),s(61691),s(71238),s(78398),s(57757),s(48045);var r=s(40060),i=s(33581),n=s(57567),a=s.n(n),l=s(51650),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["reporting",{children:["crew-training-completed-report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,61691)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\crew-training-completed-report\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\crew-training-completed-report\\page.tsx"],u="/reporting/crew-training-completed-report/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/reporting/crew-training-completed-report/page",pathname:"/reporting/crew-training-completed-report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},88913:(e,t,s)=>{Promise.resolve().then(s.bind(s,80991))},88332:(e,t,s)=>{Promise.resolve().then(s.bind(s,44517))},75546:(e,t,s)=>{"use strict";s.d(t,{Br:()=>p,fU:()=>m,o0:()=>c,p6:()=>d,vq:()=>u});var r=s(83179),i=s.n(r),n=s(7678),a=s.n(n),l=s(14826),o=s.n(l);let d=(e="",t=!0)=>{let s;if(a()(o()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[s,r,i]=e.split("-"),n=t?s.slice(-2):s,a=parseInt(i,10).toString().padStart(2,"0"),l=parseInt(r,10).toString().padStart(2,"0");return`${a}/${l}/${n}`}if(!(s=e&&"object"==typeof e?i()(e.toString()):i()(e)).isValid())return"";let r=s.format("DD"),n=s.format("MM"),l=t?s.format("YY"):s.format("YYYY");return`${r}/${n}/${l}`},c=(e="",t=!0)=>{let s;if(a()(o()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[s,r,i]=e.split("-"),n=t?s.slice(-2):s,a=parseInt(i,10).toString().padStart(2,"0"),l=parseInt(r,10).toString().padStart(2,"0");return`${a}/${l}/${n} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[s,r]=e.split(" "),[i,n,a]=s.split("-"),l=t?i.slice(-2):i,o=r.split(":"),d=o[0].padStart(2,"0"),c=o[1].padStart(2,"0"),u=parseInt(a,10).toString().padStart(2,"0"),p=parseInt(n,10).toString().padStart(2,"0");return`${u}/${p}/${l} ${d}:${c}`}if(!(s=e&&"object"==typeof e?i()(e.toString()):i()(e)).isValid())return"";let r=s.format("DD"),n=s.format("MM"),l=t?s.format("YY"):s.format("YYYY"),d=s.format("HH:mm");return`${r}/${n}/${l} ${d}`},u=(e="")=>a()(o()(e))?"":i()(e).format("YYYY-MM-DD HH:mm:ss"),p=(e="")=>a()(o()(e))?new Date:new Date(`${e}T10:00:00Z`),m=(e,t)=>{let s=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),r=e=>e.includes(" ")?e.replace(" ","T"):e,i=e=>{if(!e||"string"!=typeof e)return null;if(s(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(r(e))},n=i(e),a=i(t);return!n||!a||isNaN(n.getTime())||isNaN(a.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):a>n}},80991:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(98768);s(46776);var i=s(26100),n=s(14608),a=s(60343);function l(){let[e,t]=(0,a.useState)(!1),[s,l]=(0,a.useState)(!1);return e&&s?r.jsx(n.Z,{}):e?r.jsx(i.Z,{errorMessage:"OopsYou do not have the permission to view this section."}):r.jsx(i.Z,{})}},44517:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(98768);s(60343);var i=s(32993),n=s(64837);function a({children:e}){return r.jsx(i.default,{children:r.jsx(n.Z,{children:e})})}},14608:(e,t,s)=>{"use strict";s.d(t,{X:()=>b,Z:()=>j});var r=s(98768),i=s(94060),n=s(79418),a=s(60343),l=s(66263),o=s(17380),d=s(43692),c=s(37042),u=s(13842),p=s(75546),m=s(46776),h=s(26100),g=s(60797),x=s(51742),f=s(25394);let j=({memberId:e=0,vesselId:t=0})=>{let[s,l]=(0,a.useState)(!0),[p,g]=(0,a.useState)({totalCount:0,hasNextPage:!1,hasPreviousPage:!1}),[x,j]=(0,a.useState)([]),[v,N]=(0,a.useState)([]),[y,w]=(0,a.useState)(0),[S,M]=(0,a.useState)({}),[C,D]=(0,a.useState)([]),[I,T]=(0,a.useState)([]),[P,k]=(0,a.useState)([]),[Z,$]=(0,a.useState)([]),[q,{loading:R}]=(0,n.t)(i.ly,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTrainingSessions.nodes,s=Array.from(new Set(t.map(e=>e.vessel.id))).filter(e=>0!=+e),r=Array.from(new Set(t.flatMap(e=>e.trainingTypes.nodes.map(e=>e.id)))),i=Array.from(new Set(t.map(e=>e.trainerID))).filter(e=>0!=+e),n=Array.from(new Set(t.flatMap(e=>e.members.nodes.map(e=>e.id))));t&&(j(t),D(s),T(r),k(i),$(n)),g(e.readTrainingSessions.pageInfo)},onError:e=>{console.error("queryTrainingList error",e)}}),_=async(e=0,t={...S})=>{await q({variables:{filter:t,offset:100*e,limit:100}})},W=e=>{e<0||e===y||(w(e),O(S),_(e,S))},[Y,{loading:A}]=(0,n.t)(i.qX,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTrainingSessionDues.nodes;t&&N(Object.values(t.filter(e=>e.vessel.seaLogsMembers.nodes.some(t=>t.id===e.memberID)).map(e=>({...e,status:(0,u.nu)(e)})).filter(e=>e.status.isOverdue||!1===e.status.isOverdue&&!0===e.status.dueWithinSevenDays).reduce((e,t)=>{let s=`${t.vesselID}-${t.trainingTypeID}-${t.dueDate}`;return e[s]||(e[s]={id:t.id,vesselID:t.vesselID,vessel:t.vessel,trainingTypeID:t.trainingTypeID,trainingType:t.trainingType,dueDate:t.dueDate,status:t.status,members:[]}),e[s].members.push(t.member),e},{})).map(e=>{let t=e.members.reduce((e,t)=>{let s=e.find(e=>e.id===t.id);return s?(s.firstName=t.firstName,s.surname=t.surname):e.push(t),e},[]);return{id:e.id,vesselID:e.vesselID,vessel:e.vessel,trainingTypeID:e.trainingTypeID,trainingType:e.trainingType,status:e.status,dueDate:e.dueDate,members:t}}))},onError:e=>{console.error("readTrainingSessionDues error",e)}}),O=async s=>{let r={};e>0&&(r.memberID={eq:+e}),t>0&&(r.vesselID={eq:+t}),s.vesselID&&(r.vesselID=s.vesselID),s.trainingTypes&&(r.trainingTypeID={eq:s.trainingTypes.id.contains}),s.members&&(r.memberID={eq:s.members.id.contains}),s.date?r.dueDate=s.date:r.dueDate={ne:null},await Y({variables:{filter:r}})};(0,a.useEffect)(()=>{if(s){let t={...S};+e>0&&(t.members={id:{contains:+e}}),M(t),O(t),_(0,t),l(!1)}},[s]);let[G,B]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{B(m.Zu)},[]),G&&((0,m.Fs)("EDIT_TRAINING",G)||(0,m.Fs)("VIEW_TRAINING",G)||(0,m.Fs)("RECORD_TRAINING",G)||(0,m.Fs)("VIEW_MEMBER_TRAINING",G)))?r.jsx(f.Zb,{children:(0,r.jsxs)(f.aY,{className:"flex flex-col gap-4",children:[r.jsx(c.Z,{onChange:({type:e,data:t})=>{let s={...S};"vessel"===e&&(t?s.vesselID={eq:+t.value}:delete s.vesselID),"trainingType"===e&&(t?s.trainingTypes={id:{contains:+t.value}}:delete s.trainingTypes),"trainer"===e&&(t?s.trainer={id:{eq:+t.value}}:delete s.trainer),"member"===e&&(t?s.members={id:{contains:+t.value}}:delete s.members),"dateRange"===e&&(t.startDate&&t.endDate?s.date={gte:t.startDate,lte:t.endDate}:delete s.date),M(s),O(s),_(0,s)},vesselIdOptions:C,trainingTypeIdOptions:I,trainerIdOptions:P,memberIdOptions:Z}),R||A?r.jsx(o.hM,{}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(b,{trainingList:x,memberId:e,trainingSessionDues:v}),r.jsx(d.Z,{page:y,limit:100,visiblePageCount:5,...p,onClick:e=>W(e)})]})]})}):G?r.jsx(h.Z,{errorMessage:"OopsYou do not have the permission to view this section."}):r.jsx(h.Z,{})},v=({remainingMembers:e})=>(0,r.jsxs)(f.J2,{children:[r.jsx(f.CM,{asChild:!0,children:(0,r.jsxs)(f.zx,{variant:"primaryOutline",children:["+ ",e.length," more"]})}),r.jsx(f.yk,{className:"w-64",children:e.map(e=>r.jsx("div",{className:"flex cursor-pointer items-center overflow-auto",children:r.jsx("div",{className:"ps-3 py-2",children:r.jsx("div",{className:"",children:`${e.firstName??""} ${e.surname??""}`})})},e.id))})]}),b=({trainingList:e,trainingSessionDues:t,memberId:s=0,isVesselView:i=!1})=>{let n=[{accessorKey:"title",header:"Completed",cell:({row:e})=>{let t=e.original;return(0,r.jsxs)("div",{children:[r.jsx(l.default,{href:`/crew-training/info?id=${t.id}`,className:"  ",children:(0,p.p6)(t.date)}),(0,r.jsxs)("div",{className:"flex flex-wrap md:hidden",children:[t.trainingTypes.nodes?t.trainingTypes.nodes.map(e=>(0,r.jsxs)("span",{children:[e.title,","]},e.id)):"",!1==i&&(0,r.jsxs)("span",{className:"inline-block md:hidden",children:[":\xa0",t.vessel.title?t.vessel.title:""]})]}),r.jsx("div",{className:"flex flex-wrap lg:hidden ",children:t.members.nodes.map((e,s)=>s<2?i?(0,r.jsxs)("div",{className:"inline-block  border   rounded-lg  m-1 p-2 outline-none ",children:[`${e.firstName??""}`," "]},e.id):r.jsx("div",{className:"inline-block  border   rounded-lg  m-1 p-2 outline-none ",children:`${e.firstName??""} ${e.surname??""}`},e.id):2===s?r.jsx(v,{remainingMembers:t.members.nodes.slice(2)}):void 0)}),(0,r.jsxs)("div",{className:"flex flex-row gap-2 md:hidden items-baseline",children:[r.jsx(g.Label,{className:" !w-auto",children:"Trainer:"}),r.jsx(r.Fragment,{children:t.trainer?.id===+s?"You":`${t.trainer&&t.trainer.firstName||""} ${t.trainer&&t.trainer.surname||""}`})]})]})}},{accessorKey:"trainingDrill",header:"Training/drill",cell:({row:e})=>{let t=e.original;return r.jsx("td",{className:"hidden md:table-cell !w-1/3",children:t.trainingTypes.nodes?t.trainingTypes.nodes.map(e=>(0,r.jsxs)("span",{children:[e.title,",\xa0"]},e.id)):""})}},{accessorKey:"vessel",header:()=>r.jsx(r.Fragment,{children:!1==i&&"Where"}),cell:({row:e})=>{let t=e.original;return r.jsx("div",{children:!1==i&&r.jsx("div",{className:"hidden md:table-cell lg:align-middle",children:t.vessel.title?t.vessel.title:""})})}},{accessorKey:"who",header:"Who",cell:({row:e})=>{let t=e.original;return r.jsx("div",{className:"hidden lg:table-cell lg:align-middle",children:r.jsx("div",{className:"flex flex-wrap",children:t.members.nodes.map((e,s)=>s<2?i?(0,r.jsxs)("div",{className:"inline-block  border   rounded-lg  m-1 p-2 text-nowrap outline-none",children:[`${e.firstName??""} ${e.surname??""}`," "]},e.id):r.jsx("div",{className:"inline-block  border   rounded-lg  m-1 p-2 text-nowrap outline-none",children:`${e.firstName??""} ${e.surname??""}`},e.id):2===s?r.jsx(v,{remainingMembers:t.members.nodes.slice(2)}):void 0)})})}},{accessorKey:"trainer",header:"Trainer",cell:({row:e})=>{let t=e.original;return r.jsx("div",{className:"hidden md:table-cell lg:align-middle text-nowrap",children:r.jsx("div",{className:" border   rounded-lg  m-1 p-2 text-nowrap outline-none ",children:t.trainer?.id===+s?"You":`${t.trainer&&t.trainer.firstName||""} ${t.trainer&&t.trainer.surname||""}`})})}}];return r.jsx(r.Fragment,{children:e?.length>0?r.jsx(x.wQ,{columns:n,data:e,pageSize:20,showToolbar:!1}):r.jsx("tr",{className:"group border-b hover: ",children:r.jsx("td",{colSpan:4,className:"p-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-center gap-2 p-2 pt-4",children:[(0,r.jsxs)("svg",{className:"!w-[75px] h-auto",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 147 147.01",children:[r.jsx("path",{d:"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z",fill:"#ffffff",strokeWidth:"0px"}),r.jsx("path",{d:"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z",fill:"#052350",fillRule:"evenodd",opacity:".97",strokeWidth:"0px"}),r.jsx("path",{d:"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z",fill:"#2998e9",strokeWidth:"0px"}),r.jsx("path",{d:"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z",fill:"#2998e9",strokeWidth:"0px"}),r.jsx("path",{d:"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z",fill:"#024450",strokeWidth:"0px"}),r.jsx("path",{d:"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z",fill:"#052451",strokeWidth:"0px"}),r.jsx("path",{d:"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z",fill:"#052250",strokeWidth:"0px"}),r.jsx("path",{d:"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z",fill:"#2998e9",strokeWidth:"0px"}),r.jsx("path",{d:"M45.34,125.8h23.84v6.63h-23.84v-6.63Z",fill:"#052350",strokeWidth:"0"}),r.jsx("path",{d:"M70.17,125.8h6.58v6.63h-6.58v-6.63Z",fill:"#052250",strokeWidth:"0"}),r.jsx("path",{d:"M77.77,125.8h23.84v6.63h-23.84v-6.63Z",fill:"#052350",strokeWidth:"0"}),r.jsx("path",{d:"M67.98,127.01v4.2h-21.42v-4.2h21.42Z",fill:"#2a99ea",strokeWidth:"0"}),r.jsx("path",{d:"M75.58,127.01v4.2h-4.2v-4.2h4.2Z",fill:"#2a99ea",strokeWidth:"0"}),r.jsx("path",{d:"M78.99,127.01h21.42v4.2h-21.42v-4.2Z",fill:"#2a99ea",strokeWidth:"0"}),r.jsx("path",{d:"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z",fill:"#ffffff",strokeWidth:"0"})]}),r.jsx("p",{className:"  ",children:"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!"})]})})})})}},10901:(e,t,s)=>{"use strict";s.d(t,{k:()=>p});var r=s(98768),i=s(11652),n=s(41641),a=s(39303),l=s(40712),o=s(39544),d=s(24224),c=s(25394),u=s(50058);function p({table:e,pageSizeOptions:t=[10,20,30,40,50],showPageSizeSelector:s=!0}){let p=(0,u.k)();return r.jsx("div",{className:"flex items-center justify-center px-2",children:(0,r.jsxs)("div",{className:"flex gap-2 items-end phablet:items-center phablet:space-x-6 lg:space-x-8",children:[s&&r.jsx("div",{className:"flex items-center space-x-2",children:r.jsx(c.__,{label:"Rows per page",position:p.phablet?"left":"top",htmlFor:"rows-per-page",className:"text-sm",children:(0,r.jsxs)(d.Select,{value:`${e.getState().pagination.pageSize}`,onValueChange:t=>{e.setPageSize(Number(t))},children:[r.jsx(d.SelectTrigger,{className:"h-8 w-[70px]",children:r.jsx(d.SelectValue,{placeholder:e.getState().pagination.pageSize})}),r.jsx(d.SelectContent,{side:"top",children:t.map(e=>r.jsx(d.SelectItem,{value:`${e}`,children:e},e))})]})})}),(0,r.jsxs)("div",{className:"flex w-fit items-center justify-center text-sm",children:["Page ",e.getState().pagination.pageIndex+1," of"," ",e.getPageCount()]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(o.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>e.setPageIndex(0),disabled:!e.getCanPreviousPage(),children:[r.jsx("span",{className:"sr-only",children:"Go to first page"}),r.jsx(i.Z,{})]}),(0,r.jsxs)(o.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>e.previousPage(),disabled:!e.getCanPreviousPage(),children:[r.jsx("span",{className:"sr-only",children:"Go to previous page"}),r.jsx(n.Z,{})]}),(0,r.jsxs)(o.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>e.nextPage(),disabled:!e.getCanNextPage(),children:[r.jsx("span",{className:"sr-only",children:"Go to next page"}),r.jsx(a.Z,{})]}),(0,r.jsxs)(o.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>e.setPageIndex(e.getPageCount()-1),disabled:!e.getCanNextPage(),children:[r.jsx("span",{className:"sr-only",children:"Go to last page"}),r.jsx(l.Z,{})]})]})]})})}},6958:(e,t,s)=>{"use strict";s.d(t,{n:()=>n});var r=s(98768),i=s(37042);function n({table:e,onChange:t}){return r.jsx(i.Z,{onChange:t,table:e})}},51742:(e,t,s)=>{"use strict";s.d(t,{QT:()=>j,wQ:()=>f,wu:()=>m});var r=s(98768),i=s(26659),n=s(6958),a=s(10901),l=s(50058),o=s(60343),d=s(24109),c=s(23416),u=s(35024),p=s(56937);function m(e){return e}let h=e=>{switch(e){case"left":return"items-left justify-start justify-items-start text-left";case"right":return"items-right justify-end justify-items-end text-right";default:return"items-center justify-center justify-items-center text-center"}},g=e=>{switch(e){case"overdue":case"upcoming":return"rounded-md [&>td:first-child]:rounded-l-lg [&>td:last-child]:rounded-r-lg";default:return""}},x=e=>{switch(e){case"overdue":return"destructive";case"upcoming":return"warning";default:return}};function f({columns:e,data:t,showToolbar:s=!0,className:m,pageSize:f=10,pageSizeOptions:j=[10,20,30,40,50],showPageSizeSelector:v=!0,onChange:b,rowStatus:N}){let[y,w]=o.useState([]),[S,M]=o.useState([]),[C,D]=o.useState({pageIndex:0,pageSize:f}),I=(0,l.k)(),T=o.useMemo(()=>e.filter(e=>e.showOnlyBelow?!I[e.showOnlyBelow]:!e.breakpoint||I[e.breakpoint]),[e,I]);o.useEffect(()=>{D(e=>({...e,pageSize:f}))},[f]);let P=(0,d.b7)({data:t,columns:T,onSortingChange:w,getCoreRowModel:(0,c.sC)(),getPaginationRowModel:(0,c.G_)(),getSortedRowModel:(0,c.tj)(),onColumnFiltersChange:M,getFilteredRowModel:(0,c.vL)(),onPaginationChange:D,state:{sorting:y,columnFilters:S,pagination:C}});return(0,r.jsxs)("div",{className:"space-y-4 pb-8",children:[s&&r.jsx(u.Zb,{className:"p-2 md:p-auto",children:r.jsx(n.n,{table:P,onChange:b})}),(0,r.jsxs)(i.iA,{className:m||"p-0 phablet:p-8 lg:p-6 xl:p-8 shadow-none border-0 phablet:border border-border bg-card rounded-lg",children:[P.getHeaderGroups().some(e=>e.headers.some(e=>e.column.columnDef.header&&""!==e.column.columnDef.header))&&r.jsx(i.xD,{children:P.getHeaderGroups().map(e=>r.jsx(i.SC,{children:e.headers.map(e=>{let t=e.column.columnDef,s="title"===e.column.id?"left":t.cellAlignment||"center";return r.jsx(i.ss,{className:"title"===e.column.id?"items-left justify-items-start text-left":h(s),children:e.isPlaceholder?null:(0,d.ie)(e.column.columnDef.header,e.getContext())},e.id)})},e.id))}),r.jsx(i.RM,{children:P.getRowModel().rows.length?P.getRowModel().rows.map(e=>{let t=N?N(e.original):"normal",s=g(t);return r.jsx(i.SC,{"data-state":e.getIsSelected()?"selected":void 0,className:(0,p.cn)("mb-4",s),children:e.getVisibleCells().map(e=>{let s=e.column.columnDef,n="title"===e.column.id?"left":s.cellAlignment||"center";return r.jsx(i.pj,{statusOverlay:"normal"!==t,statusOverlayColor:x(t),className:(0,p.cn)("","title"===e.column.id?`${T.length>1?"w-auto":"w-full"} items-left justify-items-start text-left`:h(n),s.cellClassName),children:r.jsx("div",{className:(0,p.cn)("flex px-1.5 xs:px-2.5 flex-1",h(n)),children:(0,d.ie)(e.column.columnDef.cell,e.getContext())})},e.id)})},String(e.id))}):r.jsx(i.SC,{children:r.jsx(i.pj,{colSpan:T.length,className:"h-24 text-center",children:"No results."})})})]}),(P.getCanPreviousPage()||P.getCanNextPage())&&r.jsx("div",{className:"flex items-center justify-center phablet:justify-end space-x-2 py-4",children:r.jsx(a.k,{table:P,pageSizeOptions:j,showPageSizeSelector:v})})]})}let j=f},43692:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(98768);let i=({page:e=0,limit:t=0,onClick:s,totalCount:i=0,hasNextPage:n=!1,hasPreviousPage:a=!1,visiblePageCount:l=0})=>{let o=t>0?Math.ceil(i/t):0,d=t>0?l:0,c=e-d,u=e;c<0&&(c=0,u=d-1);let p=o-d,m=u+1!==p;u>=p&&(c=0,u=d-1),o<d&&(c=0,u=o-1);let h=Array.from({length:u-c+1},(e,t)=>c+t).slice(-d),g=Array.from({length:(t>0?Math.floor(i/t):0)-p+1},(e,t)=>p+t).slice(0,d);return g=(g=g.filter(e=>!h.includes(e))).filter(e=>e>=0),(h[h.length-1]+1===g[0]||h[h.length-1]-1===g[0]||g.length<=0)&&(m=!1),r.jsx("div",{className:"flex items-center justify-end p-4",children:r.jsx("nav",{"aria-label":"Log Entries pagination",children:(0,r.jsxs)("ul",{className:"inline-flex -space-x-px  h-10",children:[r.jsx("li",{children:a&&e>0&&r.jsx("button",{onClick:()=>s(0),className:" rounded-s-lg",children:"First"})}),r.jsx("li",{children:a&&r.jsx("button",{onClick:()=>s(e-1),className:"",children:"Previous"})}),Array.from({length:h.length},(e,t)=>r.jsx("li",{children:r.jsx("button",{onClick:()=>s(h[t]),className:"",children:h[t]+1})},t)),m&&r.jsx("li",{children:r.jsx("button",{onClick:()=>s(u+1),className:"flex items-center justify-center px-4 h-10 leading-tight  border   ",children:"..."})}),Array.from({length:g.length},(e,t)=>r.jsx("li",{children:r.jsx("button",{onClick:()=>s(g[t]),className:"",children:g[t]+1})},t)),r.jsx("li",{children:n&&r.jsx("button",{onClick:()=>s(e+1),className:"",children:"Next"})}),r.jsx("li",{children:n&&e*t<i&&r.jsx("button",{onClick:()=>s(o-1),className:" rounded-e-lg",children:"Last"})})]})})})}},61691:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\crew-training-completed-report\page.tsx#default`)},71238:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7380],()=>s(23611));module.exports=r})();