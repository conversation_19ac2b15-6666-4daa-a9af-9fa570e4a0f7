"use strict";exports.id=4837,exports.ids=[4837],exports.modules={46776:(e,t,r)=>{r.d(t,{E7:()=>p,Fs:()=>c,GJ:()=>o,PE:()=>i,UU:()=>s,Zu:()=>d,ay:()=>n,io:()=>h,j5:()=>l});var a=r(86708);let i=()=>{},o=()=>{let e="true"===localStorage.getItem("superAdmin"),t="true"===localStorage.getItem("admin");return e||t},l=()=>"true"===localStorage.getItem("superAdmin"),s=()=>{i()&&(window.location.href="/dashboard")},n=()=>{o()||(window.location.href="/dashboard")},c=(e,t)=>t.includes("ADMIN")||t.includes(e),d=e=>!1,h=async()=>{try{await a.Z.delete()}catch(e){console.error("[clearDB] deleting seaLogsDB failed: ",e)}try{await a.Z.open()}catch(e){console.error("[clearDB] opening seaLogsDB failed: ",e)}},p=()=>!1},39716:(e,t,r)=>{r.d(t,{SealogsCogIcon:()=>i});var a=r(98768);let i=(0,r(60343).forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=0,className:i="",...o},l)=>a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:t,height:t,viewBox:"0 0 24 24",fill:"none",stroke:e,strokeWidth:r,strokeLinecap:"round",strokeLinejoin:"round",className:i,ref:l,...o,children:(0,a.jsxs)("g",{transform:"translate(0 0)",children:[a.jsx("path",{d:"M13.0666 0.266602H11.4666H10.9333L10.6666 2.39994L9.33327 2.6666L7.73327 3.19993L5.8666 4.2666L4.53327 2.6666L2.93327 4.53327L4.2666 6.13327L3.4666 7.19994L2.93327 8.53327L2.13327 10.9333H0.266602V12.5333V13.0666H2.13327L2.6666 14.9333L3.4666 16.7999L4.2666 18.1333L2.93327 19.4666L4.53327 21.0666L6.13327 19.7333L8.2666 21.0666L10.9333 21.5999V23.7333H12.2666H13.0666V21.5999L14.3999 21.3333L16.5333 20.5333L18.1333 19.7333L19.4666 21.0666L19.7333 20.7999L21.0666 19.4666L19.7333 17.8666L20.2666 17.0666L21.3333 15.1999L21.5999 13.0666H23.7333V11.9999V10.9333L21.5999 10.6666L20.7999 8.2666L19.7333 6.13327L21.0666 4.53327L19.4666 2.93327L17.8666 4.2666L16.5333 3.4666L15.4666 2.93327L13.3333 2.39994L13.0666 0.266602Z",fill:"white"}),a.jsx("path",{d:"M.345,10.866a.1.1,0,0,0-.095.095V13.35a.1.1,0,0,0,.095.095H2.382a9.822,9.822,0,0,0,1.952,4.709L2.893,19.595a.1.1,0,0,0,0,.134l1.689,1.689a.1.1,0,0,0,.134,0l1.442-1.441a9.821,9.821,0,0,0,4.709,1.952v2.037a.1.1,0,0,0,.095.095H13.35a.1.1,0,0,0,.095-.095V21.928a9.821,9.821,0,0,0,4.709-1.952L19.6,21.418a.1.1,0,0,0,.134,0l1.689-1.689a.1.1,0,0,0,0-.134l-1.441-1.442a9.822,9.822,0,0,0,1.952-4.709h2.037a.1.1,0,0,0,.095-.095V10.961a.1.1,0,0,0-.095-.095H21.929a9.822,9.822,0,0,0-1.952-4.709l1.441-1.442a.094.094,0,0,0,0-.134L19.729,2.893a.094.094,0,0,0-.134,0L18.153,4.334a9.825,9.825,0,0,0-4.709-1.952V.345A.1.1,0,0,0,13.35.25H10.961a.1.1,0,0,0-.095.095V2.382A9.825,9.825,0,0,0,6.157,4.334L4.715,2.893a.094.094,0,0,0-.134,0L2.893,4.582a.094.094,0,0,0,0,.134L4.334,6.157a9.822,9.822,0,0,0-1.952,4.709Zm2.215.106A9.634,9.634,0,0,1,4.535,6.207a.1.1,0,0,0-.008-.125L3.094,4.648,4.649,3.094,6.082,4.527a.1.1,0,0,0,.125.008,9.632,9.632,0,0,1,4.765-1.975.1.1,0,0,0,.083-.094V.44h2.2V2.465a.1.1,0,0,0,.083.094A9.632,9.632,0,0,1,18.1,4.535a.1.1,0,0,0,.125-.008l1.433-1.433,1.555,1.555L19.784,6.082a.1.1,0,0,0-.008.125,9.634,9.634,0,0,1,1.975,4.765.1.1,0,0,0,.094.083h2.026v2.2H21.845a.1.1,0,0,0-.094.083A9.637,9.637,0,0,1,19.776,18.1a.1.1,0,0,0,.008.125l1.433,1.433-1.555,1.555-1.433-1.433a.094.094,0,0,0-.125-.008,9.639,9.639,0,0,1-4.765,1.975.1.1,0,0,0-.083.094v2.025h-2.2V21.845a.1.1,0,0,0-.083-.094,9.639,9.639,0,0,1-4.765-1.975.1.1,0,0,0-.125.008L4.649,21.216,3.094,19.662l1.433-1.433a.1.1,0,0,0,.008-.125,9.637,9.637,0,0,1-1.975-4.765.1.1,0,0,0-.094-.083H.44v-2.2H2.465A.1.1,0,0,0,2.559,10.972Z",transform:"translate(-0.155 -0.155)",fill:"currentColor"===e?"#f5f7fa":e}),a.jsx("path",{d:"M.345,10.866a.1.1,0,0,0-.095.095V13.35a.1.1,0,0,0,.095.095H2.382a9.822,9.822,0,0,0,1.952,4.709L2.893,19.595a.1.1,0,0,0,0,.134l1.689,1.689a.1.1,0,0,0,.134,0l1.442-1.441a9.821,9.821,0,0,0,4.709,1.952v2.037a.1.1,0,0,0,.095.095H13.35a.1.1,0,0,0,.095-.095V21.928a9.821,9.821,0,0,0,4.709-1.952L19.6,21.418a.1.1,0,0,0,.134,0l1.689-1.689a.1.1,0,0,0,0-.134l-1.441-1.442a9.822,9.822,0,0,0,1.952-4.709h2.037a.1.1,0,0,0,.095-.095V10.961a.1.1,0,0,0-.095-.095H21.929a9.822,9.822,0,0,0-1.952-4.709l1.441-1.442a.094.094,0,0,0,0-.134L19.729,2.893a.094.094,0,0,0-.134,0L18.153,4.334a9.825,9.825,0,0,0-4.709-1.952V.345A.1.1,0,0,0,13.35.25H10.961a.1.1,0,0,0-.095.095V2.382A9.825,9.825,0,0,0,6.157,4.334L4.715,2.893a.094.094,0,0,0-.134,0L2.893,4.582a.094.094,0,0,0,0,.134L4.334,6.157a9.822,9.822,0,0,0-1.952,4.709Zm2.215.106A9.634,9.634,0,0,1,4.535,6.207a.1.1,0,0,0-.008-.125L3.094,4.648,4.649,3.094,6.082,4.527a.1.1,0,0,0,.125.008,9.632,9.632,0,0,1,4.765-1.975.1.1,0,0,0,.083-.094V.44h2.2V2.465a.1.1,0,0,0,.083.094A9.632,9.632,0,0,1,18.1,4.535a.1.1,0,0,0,.125-.008l1.433-1.433,1.555,1.555L19.784,6.082a.1.1,0,0,0-.008.125,9.634,9.634,0,0,1,1.975,4.765.1.1,0,0,0,.094.083h2.026v2.2H21.845a.1.1,0,0,0-.094.083A9.637,9.637,0,0,1,19.776,18.1a.1.1,0,0,0,.008.125l1.433,1.433-1.555,1.555-1.433-1.433a.094.094,0,0,0-.125-.008,9.639,9.639,0,0,1-4.765,1.975.1.1,0,0,0-.083.094v2.025h-2.2V21.845a.1.1,0,0,0-.083-.094,9.639,9.639,0,0,1-4.765-1.975.1.1,0,0,0-.125.008L4.649,21.216,3.094,19.662l1.433-1.433a.1.1,0,0,0,.008-.125,9.637,9.637,0,0,1-1.975-4.765.1.1,0,0,0-.094-.083H.44v-2.2H2.465A.1.1,0,0,0,2.559,10.972Z",transform:"translate(-0.155 -0.155)",fill:"none",stroke:"#52606d",strokeMiterlimit:"10",strokeWidth:"0.5"}),a.jsx("path",{d:"M21.03,27.517a6.486,6.486,0,1,0-6.486-6.486,6.494,6.494,0,0,0,6.486,6.486",transform:"translate(-9.03 -9.03)",fill:"currentColor"===e?"#e3f8ff":e}),a.jsx("path",{d:"M21.03,27.517a6.486,6.486,0,1,0-6.486-6.486A6.494,6.494,0,0,0,21.03,27.517Zm0-12.783a6.3,6.3,0,1,1-6.3,6.3A6.3,6.3,0,0,1,21.03,14.734Z",transform:"translate(-9.03 -9.03)",fill:"none",stroke:"#52606d",strokeMiterlimit:"10",strokeWidth:"0.5"}),a.jsx("path",{d:"M27.077,29.872a2.794,2.794,0,1,0-2.794-2.794,2.8,2.8,0,0,0,2.794,2.794",transform:"translate(-15.077 -15.077)",fill:"currentColor"===e?"#fff":e}),a.jsx("path",{d:"M27.077,29.872a2.794,2.794,0,1,0-2.794-2.794A2.8,2.8,0,0,0,27.077,29.872Zm0-5.4a2.6,2.6,0,1,1-2.6,2.6A2.608,2.608,0,0,1,27.077,24.473Z",transform:"translate(-15.077 -15.077)",fill:"none",stroke:"#52606d",strokeMiterlimit:"10",strokeWidth:"0.5"})]})}));i.displayName="SealogsCogIcon"},81257:(e,t,r)=>{r.d(t,{K:()=>i});let a=r(60343);function i({...e}){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 126.43 121.34",className:"h-12 w-12 ring-1 p-0.5 rounded-full bg-[#fff]"},e),a.createElement("path",{d:"M62.01,9.87c2.45-.11,4.87.07,7.26.56,5.24,1.01,10.24,2.66,15.02,4.95,1.95,1.04,3.47,2.5,4.54,4.39,1.14,3.08.4,5.66-2.23,7.74-.98.72-1.99,1.39-3.05,2-.05.75-.15,1.5-.33,2.24-.08.23-.21.41-.41.56,1.26,6.76-1.6,10.86-8.58,12.29-7.12,1.25-14.22,1.15-21.3-.32-6.13-1.81-8.61-5.78-7.43-11.89-.38-.92-.6-1.87-.66-2.87-2.49-1.21-4.33-3.02-5.53-5.43-.43-1.65-.26-3.25.5-4.79,1.88-2.49,4.33-4.28,7.35-5.35,4.83-1.86,9.78-3.21,14.86-4.07h0Z",fill:"#fff",fillRule:"evenodd",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),a.createElement("path",{d:"M62.01,28.37c4.91.07,9.8.32,14.69.74.2.25.31.55.33.91.13.78-.2,1.19-.99,1.24-1.03.25-2.08.42-3.14.5-5.5.11-11.01.11-16.51,0-1.57-.07-3.11-.32-4.62-.74-.06-.3-.11-.61-.17-.91.02-.38.13-.71.33-.99,3.38-.3,6.74-.55,10.07-.74h0Z",fill:"#0c2745",fillRule:"evenodd"}),a.createElement("path",{d:"M102.79,34.64c4.67.17,9.19,1.09,13.54,2.76,2.1.7,3.94,1.78,5.53,3.23,2.24,2.99,1.88,5.69-1.07,8.12-.52.38-1.07.72-1.65,1.02-.12.67-.28,1.33-.5,1.97.69,5.22-1.68,8.32-7.1,9.3-1.15.22-2.3.37-3.47.47-2.59.11-5.17.11-7.76,0-2.63-.11-5.1-.74-7.43-1.89-2.79-2.07-3.79-4.75-2.97-8.04-.25-.59-.47-1.19-.66-1.81-1.77-.88-3.07-2.19-3.88-3.94-.5-1.7-.23-3.28.83-4.73,1.78-1.78,3.9-3.07,6.36-3.86,3.35-1.17,6.76-2.03,10.24-2.6h0Z",fill:"#fff",fillRule:"evenodd",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),a.createElement("path",{d:"M51.44,47.35c8.53-.03,17.06,0,25.59.08,2.74.7,4.41,2.43,5.04,5.2.11,9.25.11,18.49,0,27.74-1.2,3.81-3.82,5.57-7.84,5.28-.38,8.15-.79,16.29-1.24,24.43-.18,1.72-1.08,2.85-2.72,3.38-3.91.11-7.81.11-11.72,0-1.53-.38-2.44-1.34-2.72-2.89-.5-8.2-1-16.4-1.49-24.6-1.03-.28-2.11-.48-3.22-.58-2.82-.95-4.44-2.9-4.87-5.86-.11-8.75-.11-17.5,0-26.25.48-3.1,2.22-5.08,5.2-5.94h0Z",fill:"#fff",fillRule:"evenodd",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),a.createElement("path",{d:"M100.15,49c4.24-.07,8.48.07,12.71.41.36.42.41.88.17,1.4-.54.19-1.09.33-1.65.41-4.62.32-9.24.32-13.87,0-.68-.05-1.34-.19-1.98-.41-.16-.43-.22-.87-.17-1.32,1.62-.17,3.21-.34,4.79-.5h0Z",fill:"#0c2745",fillRule:"evenodd"}),a.createElement("path",{d:"M15.12,63.53c6-.03,12,0,17.99.08,2.83.41,4.46,2.04,4.87,4.87.11,6.55.11,13.1,0,19.65-.64,3.2-2.6,4.8-5.86,4.79-.27,6.17-.62,12.33-1.07,18.49-.37,1.09-1.12,1.78-2.23,2.06-3.03.11-6.05.11-9.08,0-1.78-.66-2.58-1.96-2.39-3.88-.35-5.56-.65-11.11-.91-16.67-3.28.11-5.28-1.43-6.03-4.62-.11-6.66-.11-13.32,0-19.98.5-2.67,2.07-4.27,4.71-4.79h0Z",fill:"#fff",fillRule:"evenodd",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),a.createElement("path",{d:"M95.19,63.53c6-.03,12,0,17.99.08,2.38.4,3.95,1.75,4.71,4.04.11,7.1.11,14.2,0,21.3-.95,2.69-2.85,4.01-5.7,3.96-.3,5.67-.61,11.34-.91,17,.04,1.77-.76,2.95-2.39,3.55-3.03.11-6.05.11-9.08,0-1.64-.59-2.43-1.78-2.39-3.55-.32-5.67-.68-11.34-1.07-17-3.12.04-5.07-1.44-5.86-4.46-.11-6.77-.11-13.54,0-20.31.57-2.57,2.13-4.12,4.71-4.62h0Z",fill:"#fff",fillRule:"evenodd",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),a.createElement("path",{d:"M23.2,34.71c4.67.17,9.19,1.09,13.54,2.76,2.1.7,3.94,1.78,5.53,3.23,2.24,2.99,1.88,5.69-1.07,8.12-.52.38-1.07.72-1.65,1.02-.12.67-.28,1.33-.5,1.97.69,5.22-1.68,8.32-7.1,9.3-1.15.22-2.3.37-3.47.47-2.59.11-5.17.11-7.76,0-2.63-.11-5.1-.74-7.43-1.89-2.79-2.07-3.79-4.75-2.97-8.04-.25-.59-.47-1.19-.66-1.81-1.77-.88-3.07-2.19-3.88-3.94-.5-1.7-.23-3.28.83-4.73,1.78-1.78,3.9-3.07,6.36-3.86,3.35-1.17,6.76-2.03,10.24-2.6h0Z",fill:"#fff",fillRule:"evenodd",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),a.createElement("path",{d:"M20.56,49.08c4.24-.07,8.48.07,12.71.41.36.42.41.88.17,1.4-.54.19-1.09.33-1.65.41-4.62.32-9.24.32-13.87,0-.68-.05-1.34-.19-1.98-.41-.16-.43-.22-.87-.17-1.32,1.62-.17,3.21-.34,4.79-.5h0Z",fill:"#0c2745",fillRule:"evenodd"}))}},80248:(e,t,r)=>{r.d(t,{E:()=>i});let a=r(60343);function i({...e}){return a.createElement("svg",Object.assign({className:"-ml-1.5 mr-2 h-6 w-6",xmlns:"http://www.w3.org/2000/svg",viewBox:"-1.5 0 22 18"},e),a.createElement("rect",{x:"2.5",y:".98",width:"5.44",height:"16.04",fill:"#f2f4f7",strokeWidth:"0px"}),a.createElement("polygon",{points:"9.04 1.61 11.95 17.39 17.29 16.4 14.39 .61 9.04 1.61",fill:"#f2f4f7",strokeWidth:"0px"}),a.createElement("path",{d:"M8.22,17.59H2.21c-.16,0-.28-.13-.28-.28V.7c0-.16.13-.28.28-.28h6.01c.16,0,.28.13.28.28v16.62c0,.14-.13.27-.28.27ZM2.5,17.02h5.44s0-16.04,0-16.04H2.5s0,16.04,0,16.04Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M7.1,6.49h-3.77c-.09,0-.14-.06-.14-.14V1.82c0-.09.06-.14.14-.14h3.77c.09,0,.14.06.14.14v4.53c0,.07-.07.14-.14.14ZM3.47,6.21h3.49s0-4.25,0-4.25h-3.49s0,4.25,0,4.25Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M5.22,16.03c-1.01,0-1.84-.82-1.84-1.84s.82-1.84,1.84-1.84,1.84.82,1.84,1.84-.84,1.84-1.84,1.84ZM5.22,12.64c-.85,0-1.56.69-1.56,1.56s.69,1.56,1.56,1.56,1.56-.69,1.56-1.56-.71-1.56-1.56-1.56Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M11.72,18c-.13,0-.26-.1-.28-.23L8.43,1.42c-.03-.16.07-.3.23-.33L14.56,0c.07-.01.16,0,.21.04s.1.11.11.18l3.02,16.34c.01.07,0,.16-.04.21s-.11.1-.18.11l-5.89,1.09s-.03.01-.06.01ZM9.04,1.61l2.9,15.78,5.34-.99L14.39.61l-5.34.99Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M10.84,6.88c-.07,0-.13-.04-.14-.11l-.82-4.46s0-.07.03-.1c.03-.03.06-.06.09-.06l3.71-.68c.07-.01.16.04.17.11l.81,4.45s0,.07-.03.1-.06.06-.09.06l-3.71.69h-.01ZM10.18,2.39l.77,4.18,3.43-.64-.77-4.18-3.43.64Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M14.12,15.95c-.86,0-1.64-.62-1.8-1.5-.09-.48.01-.98.3-1.37.28-.41.69-.68,1.18-.77.99-.18,1.96.48,2.14,1.47h0c.18.99-.48,1.96-1.47,2.14-.13.01-.24.03-.34.03ZM14.12,12.54c-.1,0-.18.01-.28.03-.41.07-.77.31-.99.65-.24.34-.33.75-.26,1.16.16.84.96,1.4,1.81,1.25.84-.16,1.4-.96,1.25-1.81-.14-.75-.79-1.28-1.53-1.28Z",fill:"#f2f4f7",strokeWidth:"0px"}))}},2908:(e,t,r)=>{r.d(t,{Q:()=>i});let a=r(60343);function i({...e}){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",viewBox:"0 0 112 112",strokeWidth:0,stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},e),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M67,42.7H10.1c-.9,0-1.6-.7-1.6-1.6V2.1c0-.9.7-1.6,1.6-1.6h56.9c.9,0,1.6.7,1.6,1.6v39.1c0,.9-.7,1.6-1.6,1.6ZM11.6,39.6h53.8V3.6H11.6s0,35.9,0,35.9Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M59.9,35.3H17.2c-.5,0-.8-.3-.8-.8V8.5c0-.5.3-.8.8-.8h42.7c.5,0,.8.3.8.8v26c0,.5-.3.8-.8.8ZM18,33.8h41.2V9.3H18v24.5Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M56.7,86.8H20.4c-.8,0-1.5-.6-1.6-1.5l-3.1-44.1c0-.5.2-.9.4-1.2.3-.3.7-.5,1.2-.5h42.6c.5,0,.9.2,1.2.5.3.3.5.8.4,1.2l-3.1,44.1c-.2.8-.8,1.5-1.6,1.5ZM21.8,83.7h33.4l3-41H18.9l2.9,41Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M91.4,99.5H7.4c-.9,0-1.6-.7-1.6-1.6v-12.7c0-.9.7-1.6,1.6-1.6h84c.9,0,1.6.7,1.6,1.6v12.7c0,.9-.7,1.6-1.6,1.6ZM8.9,96.4h80.9v-9.6H8.9v9.6Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M81.5,77.2c-7,0-12.7-5.7-12.7-12.7v-1.1c0-4.4-3.5-8-7.9-8h-1.9c-.9,0-1.6-.7-1.6-1.6s.7-1.6,1.6-1.6h1.9c6.1,0,11.1,5,11.1,11.1v1.1c0,5.3,4.3,9.6,9.6,9.6s9.6-4.3,9.6-9.6V19.1l-12.4-12.3c-.6-.6-.6-1.6,0-2.2s1.6-.6,2.2,0l12.8,12.7c.3.3.5.7.5,1.1v46.1c0,7-5.7,12.7-12.7,12.7Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M92.3,26.1c-.2,0-.2,0-.4,0l-8.2-2.2c-.7-.2-1.2-.8-1.2-1.5v-11.7c0-.9.7-1.6,1.6-1.6s1.6.7,1.6,1.6v10.5l7,1.9c.9.2,1.3,1.1,1.1,1.9-.2.7-.8,1.2-1.5,1.2Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M27.2,24.4c-.5,0-.8-.3-.8-.8v-6.9c0-.5.3-.8.8-.8s.8.3.8.8v6.9c0,.5-.4.8-.8.8Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M38.5,24.4c-.5,0-.8-.3-.8-.8v-6.9c0-.5.3-.8.8-.8s.8.3.8.8v6.9c0,.5-.4.8-.8.8Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M49.9,24.4c-.5,0-.8-.3-.8-.8v-6.9c0-.5.3-.8.8-.8s.8.3.8.8v6.9c0,.5-.4.8-.8.8Z"}))}},94446:(e,t,r)=>{r.d(t,{A:()=>o});var a=r(56937);let i=r(60343);function o({className:e="",...t}){return i.createElement("svg",Object.assign({className:(0,a.cn)("p-0.5 h-6 w-6",e),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 119.1 122.83"},t),i.createElement("path",{d:"M95.41,53.45l8.9-44.17c.04-.21-.07-.41-.26-.49L85.61.76h-.05s-.04,0-.06,0c-.1-.02-.2,0-.28.04h-.03c-.25.15-10.5,6.04-25.62,6.04S34.21.94,33.95.79h-.03c-.09-.05-.18-.06-.28-.04-.02,0-.04,0-.07,0h-.05L15.07,8.79c-.19.09-.3.29-.26.49l9.17,46.07L.93,73.79c-.11.09-.17.22-.17.35v47.42c0,.25.2.44.45.44h50.22c.12,0,.24-.05.32-.13l7.8-7.71,7.8,7.71c.08.08.2.13.32.13h50.22c.25,0,.47-.18.47-.43l-.02-47.44c0-.14-.06-.27-.17-.35l-23.04-18.44h0l.29-1.9ZM60.77,90.78l-.83-46.49c2.93-1.03,24.41-9.81,25.22-42.34.02-.64.68-1.08,1.28-.82l17.96,7.75-9.12,45.69c-.03.16.2,1.03.33,1.13l22.63,18.33v18.85M59.57,7.73c13.25,0,22.84-4.47,25.4-5.8-.35,33.43-23.49,41.91-25.4,42.55-1.9-.63-25.04-9.09-25.4-42.55,2.56,1.32,12.15,5.8,25.4,5.8h0ZM.9,92.97v-19.09l23.2-18.58c.13-.11.04-.22.01-.39L14.59,8.93,33.55,1.34c.31,33.43,23.34,42.23,26.38,43.28l.77,45.2M1.22,93.09l45.33.16-.12,10.86-45.68.12v-11.22l.47.09ZM59.53,114.3l-8.22,7.48-49.26-.18c-.72,0-1.3-.57-1.3-1.28l.15-16.24,44.87-.08c.22,1.58,1.59,3.3,3.26,3.3l10.75.33.08,5.47s-.33,1.21-.33,1.21ZM49.01,106.82c-1.32,0-2.39-1.06-2.39-2.36v-11.73c0-1.3,1.07-2.37,2.39-2.37h21.1c1.32,0,2.39,1.07,2.39,2.37v11.73c0,1.3-1.07,2.36-2.39,2.36h-21.11,0ZM117.77,121.95l-49.23.13c-.6,0-1.19-.23-1.61-.65l-7.08-7.21v-7.08l9.93.08c1.66,0,3.12-1.72,3.34-3.3l45.18-.13.03,17.55-.11.38-.45.24ZM117.88,103.87l-44.83-.12-.24-10.5,45.07.12v10.5Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"2px"}),i.createElement("path",{d:"M34.03,11.78l-17.35,7.9",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"2px"}),i.createElement("path",{d:"M17.72,25.44l18.49-8.42",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"2px"}),i.createElement("path",{d:"M102.01,19.86l-17.85-5.8",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"2px"}),i.createElement("path",{d:"M82.3,17.96l18.66,6.05",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"2px"}))}},40977:(e,t,r)=>{r.d(t,{V:()=>i});let a=r(60343);function i(e){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 124.34 122.73","aria-hidden":"true","data-slot":"icon"},e),a.createElement("path",{d:"M25.95,118.5h85.8V28.84l-25.7-24.7H25.95v114.36Z",fill:"#f2f4f7",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),a.createElement("path",{d:"M87.36,28.07l23.4-.12-23.52-22.36.12,22.49Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),a.createElement("path",{d:"M36.75,52.48h13.9v-13.26h-13.9v13.26Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),a.createElement("path",{d:"M36.75,78.52h13.9v-13.36h-13.9v13.36Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),a.createElement("path",{d:"M36.75,104.47h13.9v-13.26h-13.9v13.26Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),a.createElement("path",{d:"M101.95,42.79h-43.2c-.6,0-1-.2-1-.5s.4-.5,1-.5h43.2c.6,0,1,.2,1,.5,0,.25-.4.5-1,.3Z",fill:"##022450"}),a.createElement("path",{d:"M101.95,53.94h-43.2c-.6,0-1-.2-1-.5s.4-.5,1-.5h43.2c.6,0,1,.2,1,.5,0,.3-.4.5-1,.3Z",fill:"##022450"}),a.createElement("path",{d:"M101.95,68.74h-43.2c-.6,0-1-.2-1-.5s.4-.5,1-.5h43.2c.6,0,1,.2,1,.5,0,.3-.4.5-1,.3Z",fill:"##022450"}),a.createElement("path",{d:"M101.95,79.98h-43.2c-.6,0-1-.2-1-.5s.4-.5,1-.5h43.2c.6,0,1,.2,1,.5,0,.25-.4.5-1,.3Z",fill:"##022450"}),a.createElement("path",{d:"M101.95,94.78h-43.2c-.6,0-1-.2-1-.5s.4-.5,1-.5h43.2c.6,0,1,.2,1,.5,0,.25-.4.5-1,.3Z",fill:"##022450"}),a.createElement("path",{d:"M101.95,105.93h-43.2c-.6,0-1-.2-1-.5s.4-.5,1-.5h43.2c.6,0,1,.2,1,.5,0,.3-.4.5-1,.3Z",fill:"##022450"}))}},57659:(e,t,r)=>{r.d(t,{y:()=>o});var a=r(56937);let i=r(60343);function o({className:e,...t}){return i.createElement("svg",Object.assign({className:(0,a.cn)("w-12 h-12 ring-1 p-0.5 shrink-0 rounded-full bg-[#fff]",e),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 126.43 123.35","aria-hidden":"true","data-slot":"icon"},t),i.createElement("path",{d:"M18.54,122.36c-.48,0-.96-.19-1.34-.57l-12.55-12.55c-.57-.57-.77-1.44-.38-2.2l8.72-17.33c.19-.38.48-.67.96-.86l17.62-7.95,14.94-14.94c.77-.77,1.91-.77,2.68,0l11.3,11.3c.38.38.57.86.57,1.34s-.19.96-.57,1.34l-14.94,14.94-7.95,17.62c-.19.38-.48.77-.86.96l-17.33,8.72c-.29.1-.57.19-.86.19h0Z",fill:"#fff",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),i.createElement("path",{d:"M75.33,93.82c-2.3,0-4.5-.86-6.13-2.59l-34.09-34c-3.35-3.35-3.35-8.91,0-12.35l.29-.29c1.63-1.63,3.83-2.59,6.13-2.59s4.5.86,6.13,2.59l34.09,34.19c3.35,3.35,3.35,8.91,0,12.35l-.29.29c-1.53,1.53-3.74,2.39-6.13,2.39Z",fill:"#fff",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),i.createElement("path",{d:"M78.59,80.12c-.48,0-.96-.19-1.34-.57l-30.45-30.26c-.57-.57-.77-1.53-.29-2.3,1.05-2.01,2.78-4.69,4.6-7.57,2.11-3.26,4.21-6.7,5.75-9.58.1-.19.19-.29.29-.48l24.33-24.23c2.87-2.87,6.61-4.41,10.63-4.41,2.59,0,5.17.67,7.37,1.91,10.06,5.65,18.68,14.27,24.33,24.33,3.26,5.84,2.3,13.31-2.49,18l-24.23,24.23c-.1.1-.19.19-.38.29l-17.14,10.44c-.29.19-.67.19-.96.19h0Z",fill:"#fff",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),i.createElement("path",{d:"M92.18,16.17c-1.17,0-2.33.42-3.18,1.27l-19.42,19.44c-.85.84-1.27,2.01-1.27,3.17s.42,2.32,1.27,3.17l.21.21c.85.84,2.02,1.27,3.18,1.27s2.33-.42,3.18-1.27l19.42-19.33c1.8-1.8,1.8-4.54,0-6.34l-.21-.21c-.85-.84-1.91-1.37-3.18-1.37h0Z",fill:"#fff",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),i.createElement("path",{d:"M105.78,29.09c-1.19,0-2.38.43-3.25,1.3l-19.8,19.95c-.87.87-1.3,2.06-1.3,3.25s.43,2.39,1.3,3.25l.22.22c.87.87,2.06,1.3,3.25,1.3s2.38-.43,3.25-1.3l19.8-19.84c1.84-1.84,1.84-4.66,0-6.51l-.22-.22c-.87-.87-1.95-1.41-3.25-1.41h0Z",fill:"#fff",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}))}},32946:(e,t,r)=>{r.d(t,{k:()=>i});let a=r(60343);function i({...e}){return a.createElement("svg",Object.assign({className:"!w-[100pxfull] h-auto",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 148.02 147.99",fill:"currentColor",width:"75px","aria-hidden":"true","data-slot":"icon"},e),a.createElement("path",{d:"M77.22,1.87c16.33.68,32.06,7.03,44.45,17.92,1.45,1.28,3.52,3.29,4.91,4.75,6.1,6.47,10.96,14.04,14.33,22.28,3.16,7.74,4.9,15.83,5.26,24.41.06,1.49.03,5.46-.05,6.77-.36,5.7-1.19,10.63-2.67,15.83-2.81,9.86-7.83,19.18-14.57,27.04-7.36,8.59-16.57,15.32-26.95,19.7-6.93,2.92-14.32,4.73-21.87,5.37-3.41.29-7.72.31-11.14.07-15.22-1.08-29.46-6.84-41.21-16.66-2.97-2.48-5.95-5.43-8.53-8.43-9.82-11.43-15.82-25.72-17.1-40.74-.33-3.82-.34-8.1-.05-11.85,1-12.65,5.27-24.7,12.46-35.16,4.39-6.39,9.89-12.14,16.08-16.8,6.61-4.99,14.03-8.82,21.79-11.25,8.09-2.54,16.24-3.6,24.85-3.24Z",fill:"#ffffff",strokeWidth:"0px"}),a.createElement("path",{d:"M71.21.51c-6.79.3-12.79,1.32-18.98,3.24-3.79,1.17-7.22,2.55-10.87,4.36-5.55,2.75-10.46,6-15.13,10-2.62,2.25-5.48,5.09-7.79,7.74C8.49,37.29,2.27,51.76.84,66.75c-.28,2.92-.36,4.91-.32,8.13.03,2.95.13,4.62.42,7.3,1.1,10.08,4.4,20.06,9.57,28.9,4.28,7.33,9.89,13.94,16.46,19.42,5.95,4.96,12.56,8.9,19.79,11.79,6.92,2.77,14.11,4.44,21.56,5,2.15.17,3.03.2,5.77.19,3.37,0,5.17-.1,8.16-.43,14.52-1.61,28.33-7.59,39.44-17.09,2.86-2.44,5.89-5.47,8.28-8.27,9.55-11.2,15.51-24.95,17.11-39.47.34-3.04.43-4.79.43-8.23,0-2.89-.04-4.07-.24-6.32-.92-10.78-4.15-21.05-9.6-30.5-4.9-8.51-11.74-16.19-19.73-22.15-10.41-7.77-22.67-12.63-35.54-14.09-2.91-.33-4.48-.41-7.92-.43-1.6,0-3.08,0-3.29,0ZM77.22,1.87c16.33.68,32.06,7.03,44.45,17.92,1.45,1.28,3.52,3.29,4.91,4.75,6.1,6.47,10.96,14.04,14.33,22.28,3.16,7.74,4.9,15.83,5.26,24.41.06,1.49.03,5.46-.05,6.77-.36,5.7-1.19,10.63-2.67,15.83-2.81,9.86-7.83,19.18-14.57,27.04-7.36,8.59-16.57,15.32-26.95,19.7-6.93,2.92-14.32,4.73-21.87,5.37-3.41.29-7.72.31-11.14.07-15.22-1.08-29.46-6.84-41.21-16.66-2.97-2.48-5.95-5.43-8.53-8.43-9.82-11.43-15.82-25.72-17.1-40.74-.33-3.82-.34-8.1-.05-11.85,1-12.65,5.27-24.7,12.46-35.16,4.39-6.39,9.89-12.14,16.08-16.8,6.61-4.99,14.03-8.82,21.79-11.25,8.09-2.54,16.24-3.6,24.85-3.24Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M77.19,13.29c-.57.16-3.59.93-5.06,1.29-8.38,2.05-13.81,3.07-17.13,3.21-.66.03-1.12.07-1.3.12-.85.23-1.6.84-1.97,1.6-.44.91-.45,1.79,0,2.72l.2.41,5.01,5,5.01,5-.03,1.39c-.02,1.43-.12,3.28-.17,3.33-.01.02-.15,0-.29-.06-.69-.24-1.66.01-2.13.55-.23.26-.47.79-.57,1.24-.16.76-.09,2.38.27,5.91.11,1.05.15,1.73.16,2.58,0,.88.03,1.21.09,1.43.35,1.3,1.33,1.96,2.33,1.57.06-.02.08.04.1.4.26,3.43,1.32,6.21,3.39,8.89.51.66,1.77,1.94,2.43,2.46,2.41,1.93,4.91,3,7.99,3.44.84.12,3.16.12,4,0,3.01-.42,5.62-1.54,7.94-3.4.64-.52,1.83-1.7,2.32-2.31,1.5-1.88,2.49-3.84,3.07-6.1.19-.73.38-1.84.42-2.46.02-.24.04-.52.06-.62l.02-.19h.34c.28,0,.4-.02.61-.13.5-.24.92-.81,1.13-1.52.05-.19.08-.55.09-1.37,0-.79.06-1.57.16-2.6.48-4.91.48-5.73.03-6.69-.43-.91-1.22-1.27-2.43-1.1-.09.01-.1-.03-.16-1.08-.06-1-.06-1.11.01-1.23.07-.11.08-.31.08-1.32v-1.18l4.82-4.79c2.97-2.94,4.89-4.89,5.01-5.07.21-.31.39-.72.47-1.07.08-.34.06-1.04-.03-1.4-.21-.8-.78-1.53-1.49-1.9-.55-.29-.94-.37-1.87-.41-2.85-.12-6.77-.79-13.12-2.25-2.12-.49-6.33-1.54-8.25-2.05-.64-.17-1.21-.31-1.26-.31-.06,0-.2.03-.31.07ZM79.71,15.15c6.77,1.76,12.94,3.1,16.75,3.64,1.49.21,2.28.29,3.39.34.99.04,1.15.07,1.55.28.25.13.57.49.71.79.24.53.19,1.15-.14,1.63-.09.13-2.32,2.38-4.96,5.01l-4.8,4.77-.78.2c-2.01.52-4.9.99-7.7,1.25-3.51.33-7.49.38-11.02.12-3.45-.25-7.19-.82-9.62-1.47-.25-.07-.36-.18-4.67-4.45-5.6-5.56-5.37-5.32-5.53-5.64-.12-.23-.13-.31-.13-.72s.02-.49.13-.72c.34-.72.97-1.03,2.11-1.04,2.3,0,7.31-.86,13.93-2.39,1.67-.39,6.82-1.66,7.94-1.97.33-.09.62-.17.66-.17.03,0,1.02.24,2.19.55ZM64.22,33.32c5.25,1.16,11.98,1.61,17.96,1.19,3.28-.23,6.54-.7,9.17-1.32.21-.05.42-.09.46-.09.06,0,.07.11.07.64v.64l-.5.54c-2.72,2.99-6.53,5.1-10.5,5.83-1.35.25-1.81.29-3.43.29s-1.94-.02-3.2-.24c-3.94-.66-7.67-2.64-10.49-5.56l-.55-.57v-1.58l.13.03c.07.02.46.1.87.2ZM91.89,37.5c.03.59.06,1.18.05,1.33s-.02,2.37-.04,4.94c-.02,4.08-.01,4.72.05,5.04.14.7.08,2.41-.12,3.64-.23,1.42-.67,2.75-1.34,4.08-2.01,4.01-5.77,6.89-10.13,7.75-1.08.21-1.42.24-2.89.24-1.58,0-2.14-.07-3.41-.38-5.41-1.33-9.67-5.74-10.75-11.14-.27-1.36-.26-1.04-.28-7.22l-.02-5.71h-.1c-.06,0-.08-.01-.05-.04.04-.02.1-.62.16-1.75.06-.94.11-1.73.12-1.74s.19.14.4.33l.38.35v4.18c0,2.38.03,4.45.06,4.82.21,2.73,1.2,5.28,2.91,7.48.47.61,1.53,1.69,2.14,2.18.73.59,1.57,1.14,2.55,1.65,1.16.61,1.49.71,2.41.71.56,0,.77-.01,1.11-.1.58-.14,1.31-.43,1.86-.75l.46-.26.47.26c.55.31,1.27.61,1.86.75.58.14,1.65.15,2.12,0,.19-.06.34-.08.36-.05s.18,0,.37-.08c1.2-.45,2.52-1.21,3.6-2.07.53-.42,1.67-1.55,2.08-2.06,1.71-2.12,2.75-4.66,3.02-7.39.03-.3.05-2.28.05-5.07v-4.58l.2-.21c.11-.11.21-.21.23-.21.01,0,.06.48.1,1.06ZM89.95,46.72c-.37,3-1.82,5.78-4.05,7.76-.65.58-.67.59-.48.17.2-.43.47-1.22.6-1.73.11-.45.43-2.14.41-2.16,0,0-.35.24-.75.55-.93.72-1.21.9-1.75,1.17-1.18.59-2.42.66-3.42.2-.19-.09-.41-.22-.51-.31l-.18-.15-.44.08c-.61.1-1.09.24-1.55.44l-.4.17-.32-.14c-.39-.18-.82-.31-1.32-.41-.74-.14-.71-.14-.91.02-.27.21-.79.45-1.23.54-.49.11-1.28.08-1.79-.07-.8-.23-1.39-.57-2.56-1.46-.46-.35-.84-.63-.85-.62-.03.02.35,1.95.46,2.35.06.21.18.59.27.83l.16.45-.37-.36c-1.72-1.69-2.98-4.03-3.45-6.39-.21-1.09-.21-1.07-.23-5.27l-.02-4.04.3.21c2.99,2.13,6.16,3.34,9.89,3.77.81.1,3.32.08,4.15-.02,3.5-.43,6.42-1.52,9.24-3.43.31-.21.69-.48.86-.61l.3-.23v4.04c.02,3.46.01,4.13-.05,4.66ZM61.19,38.68c.07.08.17.29.23.47l.11.32v4.22c.03,4.41.01,4.82-.17,5.25-.1.24-.27.43-.36.43-.1,0-.31-.31-.41-.62-.08-.24-.09-.44-.09-1.28,0-.71-.03-1.33-.12-2.17-.16-1.63-.35-3.87-.39-4.65-.05-.96.07-1.61.33-1.89.14-.15.25-.2.52-.2.19,0,.24.01.34.14ZM94.25,38.62c.23.11.39.47.46,1.01.04.33.04.67,0,1.43-.06,1.01-.3,3.76-.39,4.5-.03.22-.07,1.02-.09,1.76-.03,1.27-.04,1.37-.14,1.61-.06.13-.16.29-.22.35-.14.13-.22.1-.39-.15l-.11-.16v-4.45c0-4.42,0-4.45-.1-4.45-.12,0-.12-.1,0-.13.1-.03.1-.05.07-.41-.03-.42,0-.57.2-.83.1-.14.15-.16.33-.16.12,0,.29.03.38.08ZM76.21,53.83c.38.12.56.23.89.52.17.14.31.26.33.26s.16-.12.33-.26c.34-.3.51-.4.96-.54.6-.2.71-.21.9-.09.49.31,1.3.54,2.06.59.68.04,1.62-.12,2.35-.42.18-.07.32-.11.32-.1,0,.09-.51,1.07-.74,1.43-.34.52-.99,1.17-1.38,1.37-.53.28-.75.33-1.41.32-.72,0-1.16-.12-1.95-.51-.58-.28-.73-.41-1.11-.91-.16-.21-.31-.37-.32-.37-.02,0-.17.17-.33.38-.39.5-.53.62-1.11.9-.8.39-1.24.5-1.97.51h-.62s-.56-.27-.56-.27c-.69-.33-1.08-.68-1.54-1.37-.25-.38-.78-1.38-.79-1.48,0-.02.12.02.28.08.87.35,1.89.51,2.67.41.64-.08,1.11-.21,1.58-.46l.41-.21.26.06c.15.03.37.1.5.14Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M76.88,16.78c-.99.34-1.59,1.3-1.42,2.28.11.65.57,1.28,1.13,1.54l.22.1v9.11h-.11c-.18,0-.75-.18-1.12-.35-.9-.43-1.59-1.13-2.01-2.04-.17-.37-.34-.88-.34-1.05,0-.05.13-.09.56-.15.31-.05.58-.1.61-.11.03-.02-.39-.71-1.12-1.83-.64-.99-1.18-1.8-1.2-1.8-.02,0-1.19,4.16-1.18,4.18,0,0,.21-.03.45-.07.25-.04.47-.06.51-.05.03.01.08.12.09.24.09.59.54,1.6.98,2.17.3.39.94,1.02,1.32,1.27,1.89,1.28,4.3,1.29,6.25.02.48-.31,1.28-1.14,1.59-1.65.37-.6.77-1.65.77-2.03,0-.11-.05-.11.62,0,.3.05.56.08.57.07.02-.02-1.1-4.08-1.14-4.13-.04-.04-2.39,3.53-2.36,3.59,0,.02.24.07.5.1s.49.08.51.1c.01.02-.02.21-.07.41-.35,1.35-1.34,2.44-2.64,2.91-.21.08-.46.15-.54.17l-.16.03v-9.1l.26-.12c.15-.07.38-.23.53-.38,1.1-1.1.64-2.96-.83-3.41-.38-.12-.9-.12-1.22,0ZM77.89,18.13c.17.1.35.43.35.62s-.19.52-.35.62c-.33.21-.83.09-1.03-.25-.2-.33-.08-.79.25-.99.21-.13.57-.13.78,0Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M69.36,41.41c-.28.07-.43.16-.64.4-.32.36-.39.82-.19,1.24.13.28.36.52.61.62.22.1.71.1.92,0,.22-.09.5-.35.62-.57.14-.27.14-.79,0-1.05-.26-.49-.82-.76-1.32-.64Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M84.94,41.39c-.42.1-.72.31-.9.65-.14.27-.14.79,0,1.06.12.22.39.48.62.57.23.1.72.08.95-.02.24-.11.56-.47.63-.71.22-.73-.25-1.45-1.02-1.55-.1-.01-.23-.01-.29,0Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M49.93,27.12c-.19.03-.51.14-.72.24-.36.17-.47.28-4.25,4.04-3.41,3.4-3.88,3.85-3.85,3.7.02-.09.04-.38.06-.64.02-.39,0-.54-.07-.86-.13-.5-.41-.95-.79-1.3-.84-.77-1.98-.93-3.01-.42-.32.15-.52.35-3.09,2.92-1.51,1.52-2.84,2.88-2.96,3.02-.58.68-1.1,1.63-1.41,2.56-.87,2.61-.37,5.56,1.32,7.83.36.49,2.35,2.49,2.93,2.97,2.55,2.06,6.07,2.42,8.97.93,1.22-.63.84-.28,8.43-7.87,5.02-5.03,6.97-7.01,7.09-7.21.66-1.09.5-2.42-.39-3.31-.35-.35-.92-.67-1.3-.73-.16-.03-.19-.05-.16-.11.29-.67.38-1.32.26-1.88-.13-.64-.61-1.32-1.19-1.7-.66-.42-1.56-.53-2.29-.27-.2.07-.37.13-.4.14-.02,0-.09-.13-.15-.31-.43-1.24-1.73-1.98-3.02-1.74ZM51.05,28.56c.31.16.59.49.67.81.08.26.04.75-.07.97-.04.09-1.94,2.03-4.21,4.3l-4.13,4.14.48.48.48.48,4.7-4.69c4.58-4.57,4.7-4.7,4.96-4.77.5-.15.95-.06,1.31.26.48.42.61,1.05.33,1.65-.05.11-1.71,1.81-4.73,4.83l-4.64,4.65.52.52.52.52,4.12-4.11c2.85-2.85,4.18-4.14,4.33-4.22.3-.16.89-.16,1.17,0,.3.16.46.31.62.6.12.22.14.3.14.63,0,.31-.02.42-.12.6-.08.15-1.96,2.07-5.96,6.09-3.21,3.23-5.85,5.87-5.86,5.86s.07-.76.18-1.67c.11-.91.2-1.79.2-1.96,0-.85-.45-1.69-1.18-2.18-1.11-.75-2.48-.62-3.43.32-.38.38-.6.77-.71,1.27-.08.36-.44,3.47-.41,3.5.03.02,1.25.16,1.28.15,0,0,.1-.74.2-1.63.1-.89.22-1.72.26-1.86.1-.32.35-.61.68-.77.22-.11.33-.13.61-.13.41,0,.61.09.91.36.31.28.45.6.45.99,0,.17-.13,1.4-.29,2.74l-.28,2.43-.25.22c-.95.84-2.11,1.41-3.41,1.66-.58.11-1.79.11-2.4,0-1.11-.21-2.07-.62-3.01-1.31-.41-.3-2.15-1.99-2.6-2.53-.83-1-1.4-2.19-1.63-3.43-.13-.69-.11-2.06.02-2.7.26-1.22.77-2.23,1.55-3.12.6-.67,3.22-3.26,3.22-3.18,0,.04-.11.97-.24,2.05-.18,1.45-.23,2.06-.21,2.3.08,1.04.77,1.94,1.77,2.31.35.13,1.07.18,1.47.1.65-.13,1.32-.61,1.7-1.2.28-.43.36-.74.53-2.15l.15-1.26,4.46-4.46c4.16-4.15,4.48-4.47,4.7-4.54.34-.1.73-.07,1.07.1ZM39.13,33.09c.45.24.72.68.72,1.16,0,.34-.62,5.34-.7,5.61-.09.33-.41.69-.74.83-.83.36-1.81-.21-1.88-1.09-.02-.27.55-5.17.66-5.64.2-.85,1.18-1.29,1.95-.87Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M67.25,65.78s-.05.44-.1.9c-.1,1.08-.33,2.58-.53,3.58-.15.73-.48,1.99-.52,2.04-.02.03-.04.01-.75-.57-.31-.26-.57-.47-.6-.47-.02,0-.43.58-.92,1.29s-.9,1.3-.92,1.32c-.02,0-.14-.2-.26-.46l-.22-.48-.55.31c-.96.55-2.19,1.14-3.27,1.57-1.25.5-1.35.55-1.63.8-.38.35-.47.63-.51,1.71-.09,1.92-.61,5.02-1.54,9.05-.26,1.12-1.01,4.14-1.04,4.2-.01.02,1.04,1.26,2.36,2.75,1.31,1.49,2.38,2.71,2.37,2.72s-1.5-.44-3.31-.99c-1.82-.55-3.31-.99-3.32-.99-.01.01,37.35,36.06,38.1,36.76.4.38.42.39.34.21-.19-.37-8.12-16.84-8.13-16.89,0-.03,4.62-4.52,10.28-9.98s10.28-9.94,10.27-9.95c-.01-.01-6,1.77-6.49,1.94-.13.04-.13.04-.07-.03.04-.05,1.08-1.24,2.31-2.64,1.24-1.41,2.27-2.6,2.3-2.65.05-.08,0-.32-.3-1.52-1.23-4.86-2.03-9.11-2.21-11.62l-.03-.47.26.08c1.26.37,3.4,1.17,4.75,1.8,2.78,1.28,5.06,2.91,6.38,4.56.84,1.05,1.66,2.42,2.31,3.84,2.79,6.12,4.49,15.85,5.06,28.93.12,2.83.16,4.97.16,9.05v4.39l.59.03c.32.02.62.02.66,0,.06-.02.07-.52.07-4.38,0-5.07-.06-7.61-.26-11.39-.78-14.42-3.09-24.71-6.75-30.19-2.19-3.29-5.84-5.68-11.58-7.59-1.42-.47-1.91-.61-1.94-.52-.02.05-.07.03-.2-.1-.22-.21-.46-.33-1.63-.79-1.04-.41-2.23-.98-3.19-1.52l-.6-.34-.2.38-.19.39-.88-1.27c-.48-.7-.89-1.28-.92-1.3-.02-.01-.32.21-.66.49-.34.28-.64.52-.66.53-.05.01-.3-.87-.49-1.73-.24-1.12-.48-2.71-.63-4.22l-.06-.6h-.14c-.08.01-.37.03-.65.05-.6.04-.55-.08-.44.93.23,2.21.56,4.05,1,5.56.09.32.2.66.24.76.07.17.07.19,0,.26-.12.11-10.15,8.51-10.21,8.55-.03.01-1.39-1.09-3.02-2.46-1.64-1.37-3.95-3.31-5.15-4.32l-2.18-1.83.1-.27c.5-1.43.97-3.86,1.21-6.2.1-1.07.15-.95-.39-.98-.26-.02-.55-.04-.65-.05-.12-.02-.19,0-.2.03ZM70.74,77.96c3.09,2.59,5.65,4.73,5.68,4.76.05.04-.4.44-2.12,1.88-2.08,1.75-2.18,1.82-2.27,1.75-.06-.04-1.26-1.04-2.68-2.23l-2.57-2.16-1.6-3.31c-.87-1.82-1.58-3.35-1.57-3.39.04-.16,1.39-2.06,1.45-2.03.03.01,2.59,2.14,5.68,4.73ZM90.69,74.21c.37.53.67,1,.67,1.03s-.73,1.58-1.64,3.45l-1.64,3.39-2.59,2.17c-1.43,1.2-2.61,2.17-2.63,2.17-.02,0-1-.83-2.19-1.82-1.77-1.48-2.14-1.82-2.09-1.86.31-.3,11.36-9.51,11.39-9.51.02,0,.34.45.71.99ZM61.91,74.89c.02.06.93,1.96,2.02,4.24,2.86,5.93,9.86,20.46,11.48,23.85.76,1.59,1.6,3.33,1.87,3.89,1.48,3.05,8.77,18.21,8.76,18.22-.01.02-29.21-28.14-29.19-28.16,0,0,1.3.37,2.88.85,1.58.47,2.88.85,2.89.84.01,0-1.18-1.39-2.66-3.07s-3.12-3.54-3.65-4.15l-.97-1.1.35-1.43c1.25-5.04,1.97-9.02,2.11-11.62.03-.62.04-.66.14-.73.06-.04.52-.23,1.01-.42.92-.35,2.26-.95,2.64-1.18.26-.16.27-.16.31-.02ZM93.89,75.36c.48.24,1.26.58,1.74.76,1.17.46,1.35.55,1.38.68,0,.06.04.5.07.97.16,2.5.9,6.54,2.09,11.28.18.7.32,1.3.32,1.34s-1.65,1.95-3.66,4.23c-2.01,2.29-3.64,4.16-3.62,4.16.04,0,5.75-1.72,5.81-1.75.03-.01-.13.16-.36.38-.58.59-15.99,15.44-16.01,15.42,0,0-.78-1.62-1.73-3.58l-1.72-3.58,7.01-14.55c8.3-17.24,7.8-16.2,7.81-16.2s.41.2.88.43ZM79.36,85.24l1.84,1.54-.13.15c-.08.08-.59.64-1.13,1.26l-1,1.12v.41c.02.22.13,2.64.26,5.37l.23,4.96-1.01,1.97c-.55,1.08-1.02,1.95-1.03,1.93-.02-.02-.51-1.02-1.08-2.21l-1.04-2.17.41-5.06c.23-2.78.42-5.09.41-5.13,0-.04-.51-.64-1.12-1.33-.62-.69-1.12-1.27-1.11-1.29.02-.05,3.58-3.04,3.63-3.05.02,0,.86.69,1.87,1.54ZM70.2,86.56c1.02.86,1.87,1.56,1.89,1.57.02,0,.2-.1.38-.25.18-.15.34-.27.34-.26.02.03,1.66,1.86,1.8,2.02l.13.14-.31,3.62c-.17,1.99-.31,3.66-.32,3.71,0,.06-5.75-11.76-5.9-12.14-.03-.09.1.02,1.97,1.58ZM86.38,85.62c-.17.35-1.44,2.99-2.82,5.86s-2.6,5.4-2.71,5.63c-.11.22-.2.37-.21.33-.01-.09-.35-7.2-.35-7.47,0-.18.05-.25.96-1.27l.96-1.08.29.24c.16.13.31.25.35.26.03,0,.9-.69,1.93-1.55,1.03-.87,1.88-1.57,1.88-1.58,0,0-.13.29-.29.63Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M41,71.14c-.31.79-.34.69.31.94,2.61,1.01,10.35,3.95,10.43,3.96.07,0,.13-.1.32-.59.2-.52.21-.6.15-.62-.04-.02-2.53-.97-5.53-2.11s-5.46-2.08-5.47-2.08c0,0-.11.22-.21.5Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M52.06,119.62v14.5h1.34v-29h-1.34v14.5Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("polygon",{points:"99.85 115.16 99.85 115.95 92.76 115.95 85.68 115.95 85.68 115.16 85.68 114.37 92.76 114.37 99.85 114.37 99.85 115.16",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M84.35,115.16v2.12h16.84v-4.24h-16.84v2.12ZM99.85,115.16v.79h-14.16v-1.57h14.16v.79Z",fill:"#2998e9",strokeWidth:"0px"}),a.createElement("path",{d:"M101.75,126.89v7.23h1.34v-14.45h-1.34v7.23Z",fill:"#022450",strokeWidth:"0px"}),a.createElement("path",{d:"M79.36,85.24l1.84,1.54-.13.15c-.08.08-.59.64-1.13,1.26l-1,1.12v.41c.02.22.13,2.64.26,5.37l.23,4.96-1.01,1.97c-.55,1.08-1.02,1.95-1.03,1.93-.02-.02-.51-1.02-1.08-2.21l-1.04-2.17.41-5.06c.23-2.78.42-5.09.41-5.13,0-.04-.51-.64-1.12-1.33-.62-.69-1.12-1.27-1.11-1.29.02-.05,3.58-3.04,3.63-3.05.02,0,.86.69,1.87,1.54Z",fill:"#2998e9",strokeWidth:"0px"}),a.createElement("path",{d:"M91.89,37.5c.03.59.06,1.18.05,1.33s-.02,2.37-.04,4.94c-.02,4.08-.01,4.72.05,5.04.14.7.08,2.41-.12,3.64-.23,1.42-.67,2.75-1.34,4.08-2.01,4.01-5.77,6.89-10.13,7.75-1.08.21-1.42.24-2.89.24-1.58,0-2.14-.07-3.41-.38-5.41-1.33-9.67-5.74-10.75-11.14-.27-1.36-.26-1.04-.28-7.22l-.02-5.71h-.1c-.06,0-.08-.01-.05-.04.04-.02.1-.62.16-1.75.06-.94.11-1.73.12-1.74s.19.14.4.33l.38.35v4.18c0,2.38.03,4.45.06,4.82.21,2.73,1.2,5.28,2.91,7.48.47.61,1.53,1.69,2.14,2.18.73.59,1.57,1.14,2.55,1.65,1.16.61,1.49.71,2.41.71.56,0,.77-.01,1.11-.1.58-.14,1.31-.43,1.86-.75l.46-.26.47.26c.55.31,1.27.61,1.86.75.58.14,1.65.15,2.12,0,.19-.06.34-.08.36-.05s.18,0,.37-.08c1.2-.45,2.52-1.21,3.6-2.07.53-.42,1.67-1.55,2.08-2.06,1.71-2.12,2.75-4.66,3.02-7.39.03-.3.05-2.28.05-5.07v-4.58l.2-.21c.11-.11.21-.21.23-.21.01,0,.06.48.1,1.06Z",fill:"#2998e9",strokeWidth:"0px"}))}},94575:(e,t,r)=>{r.d(t,{I:()=>i});let a=r(60343);function i({...e}){return a.createElement("svg",Object.assign({className:"!w-[75px] h-auto",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 147 147.01",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},e),a.createElement("path",{d:"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z",fill:"#ffffff",strokeWidth:"0px"}),a.createElement("path",{d:"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z",fill:"#052350",fillRule:"evenodd",opacity:".97",strokeWidth:"0px"}),a.createElement("path",{d:"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z",fill:"#2998e9",strokeWidth:"0px"}),a.createElement("path",{d:"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z",fill:"#2998e9",strokeWidth:"0px"}),a.createElement("path",{d:"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z",fill:"#024450",strokeWidth:"0px"}),a.createElement("path",{d:"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z",fill:"#052451",strokeWidth:"0px"}),a.createElement("path",{d:"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z",fill:"#052250",strokeWidth:"0px"}),a.createElement("path",{d:"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z",fill:"#2998e9",strokeWidth:"0px"}),a.createElement("path",{d:"M45.34,125.8h23.84v6.63h-23.84v-6.63Z",fill:"#052350",strokeWidth:"0"}),a.createElement("path",{d:"M70.17,125.8h6.58v6.63h-6.58v-6.63Z",fill:"#052250",strokeWidth:"0"}),a.createElement("path",{d:"M77.77,125.8h23.84v6.63h-23.84v-6.63Z",fill:"#052350",strokeWidth:"0"}),a.createElement("path",{d:"M67.98,127.01v4.2h-21.42v-4.2h21.42Z",fill:"#2a99ea",strokeWidth:"0"}),a.createElement("path",{d:"M75.58,127.01v4.2h-4.2v-4.2h4.2Z",fill:"#2a99ea",strokeWidth:"0"}),a.createElement("path",{d:"M78.99,127.01h21.42v4.2h-21.42v-4.2Z",fill:"#2a99ea",strokeWidth:"0"}),a.createElement("path",{d:"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z",fill:"#ffffff",strokeWidth:"0"}))}},29428:(e,t,r)=>{r.d(t,{_:()=>o});var a=r(56937);let i=r(60343);function o({className:e,...t}){return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"#f2f4f7",viewBox:"0 0 124.05 122.8",strokeMiterlimit:"10",strokeWidth:"0.25px",strokeLinecap:"round",strokeLinejoin:"round",stroke:"#022450","aria-hidden":"true","data-slot":"icon",className:(0,a.cn)("w-12 h-12 ring-1 p-0.5 shrink-0 rounded-full bg-[#fff]",e)},t),i.createElement("path",{d:"M121.11,84.65l-11.81-11.81c-2.15-2.15-5.53-2.48-8.05-.79l-9.43-16.84c.51-.36.82-.95.82-1.58v-.42h12.92v.42c0,1.07.87,1.94,1.94,1.94s1.94-.87,1.94-1.94v-16.91c0-1.07-.87-1.94-1.94-1.94s-1.94.87-1.94,1.94v.42h-12.92v-.42c0-1.07-.87-1.94-1.94-1.94h-10.32l-5.52-9.86c4.75-6.82,3.08-16.2-3.73-20.95-6.82-4.75-16.2-3.08-20.95,3.73-3.61,5.17-3.61,12.05,0,17.22l-26.39,47.13c-2.53-1.69-5.9-1.36-8.05.79l-11.81,11.81c-1.22,1.23-1.89,2.9-1.85,4.63.04,1.73.79,3.36,2.08,4.51,12,10.8,26.77,18.03,42.66,20.9l-.07.86c-.25,3,1.97,5.63,4.96,5.88.15.01.31.02.46.02h20.71c3.01,0,5.44-2.44,5.44-5.44,0-.15,0-.31-.02-.46l-.07-.86c15.89-2.87,30.66-10.1,42.66-20.9,1.29-1.15,2.04-2.78,2.08-4.51.04-1.73-.62-3.4-1.85-4.63ZM75.64,85.28c-.6-2.42-2.78-4.12-5.27-4.11h-.98v-15.33c3.79-.71,7.45-2.01,10.83-3.87l9.74,17.92c-4.51,2.42-9.33,4.23-14.32,5.38ZM49.38,85.28c-4.99-1.15-9.8-2.96-14.32-5.38l9.74-17.92c3.38,1.85,7.04,3.16,10.83,3.87v15.33h-.98c-2.5-.01-4.68,1.68-5.27,4.11ZM55.63,61.88c-3.14-.66-6.16-1.78-8.98-3.32l8.98-16.52v19.84ZM59.51,34.43s0,0,0-.01v-3.34c1.98.4,4.02.4,6,0v3.71h-1.45c-1.07,0-1.94.87-1.94,1.94v16.91c0,1.07.87,1.94,1.94,1.94h1.45v25.6h-6v-46.74ZM76.73,55.57l1.63,2.99c-2.81,1.54-5.84,2.66-8.98,3.32v-6.31h7.35ZM105.54,41.02v8.32h-12.92v-8.32h12.92ZM92.6,41.34l.04,12.41c0,.48-.38.88-.86.89l-27.73.81c-1.13.03-2.06-.88-2.05-2l.24-17.39c0-.66.55-1.2,1.21-1.19l12.22-.04h3.26l2.81-.04,9,.04c.59-.18,1.91.72,1.91,1.34l-.04,5.17ZM69.38,34.79v-5.08c.96-.5,1.87-1.09,2.7-1.78l3.84,6.86h-6.54ZM62.51,5.16c6.17,0,11.17,5,11.17,11.17s-5,11.17-11.17,11.17-11.17-5-11.17-11.17c0-6.17,5-11.16,11.17-11.17ZM74.01,117.07c-.3.32-.71.51-1.15.51h-20.71c-.87,0-1.57-.7-1.57-1.57,0-.04,0-.09,0-.13l2.39-28.09h0s.11-1.31.11-1.31c.06-.81.75-1.44,1.56-1.43h15.71c.82,0,1.5.62,1.56,1.43l.11,1.3h0s2.39,28.1,2.39,28.1c.04.44-.11.87-.41,1.19Z",fill:"none",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),i.createElement("path",{d:"M62.51,21.34c1.07,0,1.94-.87,1.94-1.94v-6.14c0-1.07-.87-1.94-1.94-1.94s-1.94.87-1.94,1.94v6.14c0,1.07.87,1.94,1.94,1.94Z",fill:"#7B879A",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),i.createElement("path",{d:"M62.51,96.3c-1.07,0-1.94.87-1.94,1.94v6.14c0,1.07.87,1.94,1.94,1.94s1.94-.87,1.94-1.94v-6.14c0-1.07-.87-1.94-1.94-1.94Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),i.createElement("path",{d:"M2.84,92.79c-.88-.79-.93-.9-1.68-2.23-.58-1.03-.6-2.23.22-3.08,1.16-1.2,2.19-2.51,2.21-2.53l12.3-12.38c1.09-1.1,5.02-2.06,6.19-1.06,9.2,8.02,15.53,11.43,27.44,14.09l-2.69,29.15c-17.84-3.3-30.51-9.83-43.99-21.97Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),i.createElement("path",{d:"M645.79,551.37l-79.77-145.99h42.28l69.4,123.65c-10.37,7.98-20.74,15.16-31.91,22.34Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),i.createElement("path",{d:"M118.98,95.27c-9.89,9.17-24.15,16.08-40.65,19.38l-2.44-29.07c13.07-5.31,12.7-6.9,26.57-14.23,3.54-1.47,4.58-.92,7.1,1.36l12.69,13.17c1.04,1.12,1.41,4.61.37,5.74-.03.03-.63.7-1.28,1.4l-1.34,1.34c-.07.09-.93.83-1.01.9Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}))}},9210:(e,t,r)=>{r.d(t,{HV:()=>a.SealogsCogIcon,Ko:()=>i.K,ll:()=>l,EU:()=>s.E,of:()=>c,A0:()=>d.A,r4:()=>p,V1:()=>u.V,y5:()=>m.y,_z:()=>f._,bF:()=>g.b});var a=r(39716),i=r(81257);let o=r(60343);function l({...e}){return o.createElement("svg",Object.assign({id:"a",dataname:"Layer 1",viewBox:"0 0 117.71 121.34"},e),o.createElement("path",{d:"M72.12,110.81h-17.18v-14.54c0-5.95-5.95-10.57-13.22-10.57s-13.22,4.63-13.22,10.57v14.54H11.33l11.89-48.24h37l11.89,48.24h0Z",fill:"#f4f5f7",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),o.createElement("path",{d:"M23.39,46.02v11.63H5.05v8.57h17.71l-11.38,44.7H.62v9.8h82.2v-9.8h-10.75l-11.38-44.7h17.71v-8.57h-18.34v-11.63",fill:"#fff",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),o.createElement("path",{d:"M29.83,110.81v-14.54c0-5.29,5.29-9.25,11.89-9.25s11.89,3.96,11.89,9.25v14.54h-23.79ZM11.33,25.91L41.72.8l31.06,25.11H11.33Z",fill:"#fff",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),o.createElement("polygon",{points:"117.05 28.87 117.05 50.68 63.53 39.45 117.05 28.87",fill:"none",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),o.createElement("rect",{x:"23.22",y:"26.06",width:"37",height:"31.5",fill:"none",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}))}var s=r(80248);r(60343),r(2908);let n=r(60343);function c({...e}){return n.createElement("svg",Object.assign({className:"-ml-1.5 mr-2 h-6 w-6",xmlns:"http://www.w3.org/2000/svg",viewBox:"-4 0 26 20"},e),n.createElement("rect",{x:".2658",y:"5.9435",width:"18.6775",height:"12.0407",rx:".6387",ry:".6387",fill:"#f2f4f7",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"0.25px"}),n.createElement("path",{d:"M19.084,17.3455V3.2669c0-.4297-.3497-.7795-.7795-.7795h-9.5224L7.0224.1531c-.0132-.0177-.0341-.028-.0561-.028H.9045C.4747.125.125.4748.125.9045v16.441c0,.4297.3497.7795.7795.7795h17.4001c.4297,0,.7795-.3497.7795-.7795ZM3.2614,5.8028H.9045c-.2644,0-.4977.133-.6387.3349V.9045C.2658.5523.5523.2658.9045.2658h6.0267l1.7597,2.3343c.0132.0177.0341.028.0561.028h9.5576c.3522,0,.6387.2865.6387.6387v2.8707c-.141-.2019-.3743-.3349-.6387-.3349h-2.3569M15.8069,5.8028H3.4022M.2658,17.3455V6.5821c0-.3521.2865-.6386.6387-.6386h17.4001c.3522,0,.6387.2865.6387.6386v10.7634c0,.3522-.2865.6387-.6387.6387H.9045c-.3522,0-.6387-.2865-.6387-.6387Z",fill:"#022450",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"0.25px"}),n.createElement("path",{d:"M13.3511,13.1097h-2.6351c-.0698,0-.1268.0567-.1268.1268v2.6351h-1.9696v-2.6351c0-.0701-.0569-.1268-.1268-.1268h-2.6351v-1.9694h2.6351c.0698,0,.1268-.0567.1268-.1268v-2.6351h1.9696v2.6351c0,.0701.0569.1268.1268.1268h2.6351v1.9694Z",fill:"#ffffff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"0.3885px"}),n.createElement("path",{d:"M13.4779,10.8868h-2.6351v-2.6351c0-.0701-.0569-.1268-.1268-.1268h-2.2231c-.0698,0-.1268.0567-.1268.1268v2.6351h-2.6351c-.0698,0-.1268.0567-.1268.1268v2.2229c0,.0701.0569.1268.1268.1268h2.6351v2.6351c0,.0701.0569.1268.1268.1268h2.2231c.0698,0,.1268-.0567.1268-.1268v-2.6351h2.6351c.0698,0,.1268-.0567.1268-.1268v-2.2229c0-.0701-.0569-.1268-.1268-.1268ZM13.3511,13.1097h-2.6351c-.0698,0-.1268.0567-.1268.1268v2.6351h-1.9696v-2.6351c0-.0701-.0569-.1268-.1268-.1268h-2.6351v-1.9694h2.6351c.0698,0,.1268-.0567.1268-.1268v-2.6351h1.9696v2.6351c0,.0701.0569.1268.1268.1268h2.6351v1.9694Z",fill:"#022450",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"0.3885px"}))}var d=r(94446);let h=r(60343);function p({...e}){return h.createElement("svg",Object.assign({className:"w-7 h-7 sm:size-9",viewBox:"0 0 78 91.1323",stroke:"#022450",fill:"#ffffff",strokeMiterlimit:"10",strokeWidth:".75px","aria-hidden":"true","data-slot":"icon"},e),h.createElement("path",{d:"M16.3623,67.2474c-.3242-.4395-.9844-.6133-1.438-.6133h-.8486c-7.2979,0-13.2354-5.9375-13.2354-13.2354V13.7357C.8403,6.4373,6.7778.4998,14.0757.4998h49.772c7.2979,0,13.2354,5.9375,13.2354,13.2358v39.6631c0,7.2979-5.9375,13.2354-13.2354,13.2354h-.8496c-.5312,0-1.041.2168-1.4336.6094l-22.5649,23.1729-22.6372-23.1689Z",fill:"#f4fafe",strokeWidth:"0px"}),h.createElement("path",{d:"M63.8472,1c7.0223,0,12.7354,5.7131,12.7354,12.7354v39.663c0,7.0223-5.7131,12.7354-12.7354,12.7354h-.8488c-.6652,0-1.3.2684-1.7968.7654l-22.2029,22.8009-22.242-22.7644c-.4819-.5897-1.2558-.8019-1.8324-.8019h-.8488c-7.0223,0-12.7354-5.7131-12.7354-12.7354V13.7354C1.3401,6.7131,7.0532,1,14.0756,1h49.7717M63.8472,0H14.0756C6.5134,0,.3401,6.1732,.3401,13.7354v39.663c0,7.5622,6.1732,13.7354,13.7354,13.7354h.8488c.3858,0,.8488.1543,1.0803.463l22.9953,23.5354,22.9181-23.5354c.3087-.3087.6945-.463,1.0803-.463h.8488c7.5622,0,13.7354-6.1732,13.7354-13.7354V13.7354c0-7.5622-6.1732-13.7354-13.7354-13.7354h0Z",fill:"#022450",strokeWidth:"0.5px"}),h.createElement("path",{d:"M30.5667,71.4197c.0454.0732.126.1182.2124.1182h16.8809c.0864,0,.167-.0449.2124-.1182l19.064-30.7402c.0405-.0649.0488-.1445.0229-.2163-.0259-.0723-.0835-.1279-.1558-.1523l-7.9189-2.6618v-15.9271c0-.1382-.1118-.25-.25-.25h-7.0308v-13.0513c0-.1382-.1118-.25-.25-.25h-24.2686c-.1382,0-.25.1118-.25.25v13.0513h-7.0308c-.1382,0-.25.1118-.25.25v15.9271l-7.9189,2.6618c-.0723.0244-.1299.0801-.1558.1523-.0259.0718-.0176.1514.0229.2163l19.064,30.7402ZM66.345,40.6839l-18.8242,30.354h-8.0513V31.6502l26.8755,9.0336ZM27.3352,8.6707h23.7686v12.8013h-23.7686v-12.8013ZM20.0545,21.9719h38.3301v15.509l-19.0854-6.4152c-.0155-.0052-.0317.0021-.0477,0-.012-.0018-.0194-.0132-.0319-.0132s-.0199.0114-.0319.0132c-.016.0021-.0322-.0052-.0477,0l-19.0854,6.4152v-15.509ZM38.9695,31.6502v39.3876h-8.0513l-18.8242-30.354,26.8755-9.0336Z"}),h.createElement("polygon",{points:"66.1255 40.6839 47.3013 71.0379 39.25 71.0379 39.25 31.6502 66.1255 40.6839"}),h.createElement("polygon",{points:"38.75 31.6502 38.75 71.0379 30.6987 71.0379 11.8745 40.6839 38.75 31.6502"}),h.createElement("path",{d:"M19.835,21.9719h38.3301v15.509l-19.0854-6.4152c-.0155-.0052-.0317.0021-.0477,0-.012-.0018-.0194-.0132-.0319-.0132s-.0199.0114-.0319.0132c-.016.0021-.0322-.0052-.0477,0l-19.0854,6.4152v-15.509Z"}),h.createElement("path",{d:"M44.2812,14.8215h-3.0493c-.1382,0-.25.1118-.25.25s.1118.25.25.25h3.0493c.1382,0,.25-.1118.25-.25s-.1118-.25-.25-.25Z",fill:"#022450"}),h.createElement("path",{d:"M36.7681,14.8215h-3.0493c-.1382,0-.25.1118-.25.25s.1118.25.25.25h3.0493c.1382,0,.25-.1118.25-.25s-.1118-.25-.25-.25Z",fill:"#022450"}))}var u=r(40977),m=r(57659);r(60343),r(60343),r(60343),r(32946),r(94575);var f=r(29428);r(60343);var g=r(99161)},86708:(e,t,r)=>{r.d(t,{Z:()=>i});let a=new(r(69507)).ZP("seaLogsDB");a.version(2).stores({AssetReporting_LogBookEntrySection:"id, idbCRUD",BarCrossingChecklist:"id, idbCRUD, vesselID",CGEventMission:"id, idbCRUD, vesselID",Client:"id, idbCRUD",ComponentMaintenanceCheck:"id, idbCRUD",ComponentMaintenanceSchedule:"id, idbCRUD",Consequence:"id, idbCRUD",CrewDuty:"id, idbCRUD",CrewMembers_LogBookEntrySection:"id, idbCRUD",CrewTraining_LogBookEntrySection:"id, idbCRUD",CrewWelfare_LogBookEntrySection:"id, idbCRUD",CustomisedComponentField:"id, idbCRUD, customisedLogBookComponentID",CustomisedLogBookConfig:"id, idbCRUD, customisedLogBookID",DangerousGood:"id, idbCRUD",DangerousGoodsChecklist:"id, idbCRUD, vesselID",DangerousGoodsRecord:"id, idbCRUD, tripReport_LogBookEntrySectionID",Engine:"id, idbCRUD",Engine_LogBookEntrySection:"id, idbCRUD",Engine_Usage:"id, idbCRUD, maintenanceScheduleID, engineID",Engineer_LogBookEntrySection:"id, idbCRUD",EventType:"id, idbCRUD",EventType_BarCrossing:"id, idbCRUD, tripEventID, geoLocationID, geoLocationCompletedID, barCrossingChecklistID",EventType_PassengerDropFacility:"id, idbCRUD, tripEventID, geoLocationID",EventType_PersonRescue:"id, idbCRUD, missionID, tripEventID",EventType_RestrictedVisibility:"id, idbCRUD, tripEventID, startLocationID, endLocationID",EventType_Supernumerary:"id, idbCRUD",EventType_Tasking:"id, idbCRUD, currentEntryID, groupID, pausedTaskID, openTaskID, completedTaskID, tripEventID, geoLocationID, vesselRescueID, personRescueID, towingChecklistID, parentTaskingID",EventType_VesselRescue:"id, idbCRUD, missionID, vesselLocationID, tripEventID",FavoriteLocation:"id, idbCRUD, memberID",FuelLog:"id, idbCRUD, refuellingBunkeringID, eventType_TaskingID, eventType_PassengerDropFacilityID, logBookEntryID, fuelTankID",FuelTank:"id, idbCRUD",Fuel_LogBookEntrySection:"id, idbCRUD",GeoLocation:"id, idbCRUD",Inventory:"id, idbCRUD, vesselID",InfringementNotice:"id, idbCRUD",InfringementNotice_Signature:"id, idbCRUD, infringementNoticeID",Likelihood:"id, idbCRUD",LogBookEntry:"id, idbCRUD, vehicleID",LogBookEntryOldConfigs:"id, idbCRUD, logBookEntryID",LogBookEntrySection_Signature:"id, idbCRUD, logBookEntrySectionID",LogBookSignOff_LogBookEntrySection:"id, idbCRUD",MaintenanceCheck:"id, idbCRUD, maintenanceScheduleID",MaintenanceCheck_Signature:"id, idbCRUD, maintenanceCheckID",MaintenanceSchedule:"id, idbCRUD",MaintenanceScheduleSubTask:"id, idbCRUD, componentMaintenanceScheduleID",MemberTraining_Signature:"id, idbCRUD, memberID, trainingSessionID",MissionTimeline:"id, idbCRUD, missionID, vesselRescueID, personRescueID, maintenanceCheckID, subTaskID",MitigationStrategy:"id, idbCRUD",Ports_LogBookEntrySection:"id, idbCRUD",RefuellingBunkering:"id, idbCRUD, tripEventID, geoLocationID",TripUpdate:"id, idbCRUD, tripEventID, geoLocationID",RiskFactor:"id, idbCRUD, vesselID, riskRatingID, consequenceID, likelihoodID, towingChecklistID, dangerousGoodsChecklistID, barCrossingChecklistID, type",RiskRating:"id, idbCRUD",SeaLogsMember:"id, idbCRUD",SectionMemberComment:"id, idbCRUD, logBookEntrySectionID, commentType, comment, hideComment",Supernumerary_LogBookEntrySection:"id, idbCRUD, supernumeraryID",TowingChecklist:"id, idbCRUD, vesselID",TrainingLocation:"id, idbCRUD",TrainingSession:"id, idbCRUD, trainerID, logBookEntrySectionID, logBookEntryID, vesselID, trainingLocationID, geoLocationID",TrainingSessionDue:"id, idbCRUD, memberID, trainingTypeID, vesselID, trainingSessionID",TrainingType:"id, idbCRUD",TripEvent:"id, idbCRUD, logBookEntrySectionID",TripReport_LogBookEntrySection:"id, idbCRUD",TripReport_Stop:"id, idbCRUD, stopLocationID, logBookEntrySectionID, tripReportScheduleStopID, dangerousGoodsChecklistID",VehiclePosition:"id, idbCRUD, vehicleID",Vessel:"id, idbCRUD",VesselDailyCheck_LogBookEntrySection:"id, idbCRUD",VoyageSummary_LogBookEntrySection:"id, idbCRUD",WeatherForecast:"id, idbCRUD, logBookEntryID",WeatherObservation:"id, idbCRUD, logBookEntryID, forecastID",WeatherTide:"id, idbCRUD, logBookEntryID"}),a.open().catch(function(e){console.error("[seaLogsDB] Open failed: "+e)});let i=a},1971:(e,t,r)=>{r.d(t,{Z:()=>s});var a=r(83179),i=r.n(a),o=r(86708);class l{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),r=t.id,a={...t,__typename:"GeoLocation",idbCRUD:"Update",idbCRUDDate:i()().format("YYYY-MM-DD HH:mm:ss")},l=await this.getById(r);return l?await o.Z.GeoLocation.update(r,a):await o.Z.GeoLocation.add(a),l=await this.getById(r),console.log("GeoLocation save",e,l),l}catch(t){console.error("GeoLocation save",e,t)}}async getAll(){try{let e=await o.Z.GeoLocation.toArray();return console.log("GeoLocation getAll",e),e}catch(e){console.error("GeoLocation getAll",e)}}async getById(e){try{let t=await o.Z.GeoLocation.get(`${e}`);return console.log("GeoLocation getById",e,t),t}catch(t){console.error("GeoLocation getById",e,t)}}async getByIds(e){try{let t=await o.Z.GeoLocation.where("id").anyOf(e).toArray();return console.log("GeoLocation getByIds",e,t),t}catch(t){console.error("GeoLocation getByIds",e,t)}}async bulkAdd(e){try{return await o.Z.GeoLocation.bulkAdd(e),console.log("GeoLocation bulkAdd",e),e}catch(t){if("BulkError"===t.name){let r=t.failuresByPos.map(e=>e.key),a=e.filter(e=>!r.includes(e.id));return await o.Z.GeoLocation.bulkAdd(a),console.log("GeoLocation bulkAdd::BulkError",e,t),e}console.error("GeoLocation bulkAdd",e,t)}}async setProperty(e){try{if(e){let t=await o.Z.GeoLocation.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=i()().format("YYYY-MM-DD HH:mm:ss"),await o.Z.GeoLocation.update(e,t),console.log("GeoLocation setProperty",e,t),t}}catch(t){console.error("GeoLocation setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await o.Z.GeoLocation.update(e.id,e)})),console.log("GeoLocation multiUpdate",e)}catch(t){console.error("GeoLocation multiUpdate",e,t)}}}let s=l},48755:(e,t,r)=>{r.d(t,{Z:()=>s});var a=r(83179),i=r.n(a),o=r(86708);class l{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),r=t.id,a={...t,idbCRUD:"Update",idbCRUDDate:i()().format("YYYY-MM-DD HH:mm:ss"),__typename:"LogBookEntry"},l=await this.getById(r);return l?await o.Z.LogBookEntry.update(e.id,a):await o.Z.LogBookEntry.add(a),l=await this.getById(r),console.log("LogBookEntry save",e,l),l}catch(t){console.error("LogBookEntry save",e,t)}}async getAll(){try{let e=await o.Z.LogBookEntry.toArray();return console.log("LogBookEntry getAll",e),e}catch(e){console.error("LogBookEntry getAll",e)}}async getById(e){try{let t=await o.Z.LogBookEntry.get(`${e}`);return console.log("LogBookEntry getById",e,t),t}catch(t){console.error("LogBookEntry getById",e,t)}}async getByVesselId(e){try{let t=await o.Z.LogBookEntry.where("vehicleID").equals(`${e}`).toArray();return console.log("LogBookEntry getByVesselId",e,t),t}catch(t){console.error("LogBookEntry getByVesselId",e,t)}}async bulkAdd(e){try{return await o.Z.LogBookEntry.bulkAdd(e),console.log("LogBookEntry bulkAdd",e),e}catch(t){if("BulkError"===t.name){let r=t.failuresByPos.map(e=>e.key),a=e.filter(e=>!r.includes(e.id));return await o.Z.LogBookEntry.bulkAdd(a),console.log("LogBookEntry bulkAdd::BulkError",e,t),e}console.error("LogBookEntry bulkAdd",e,t)}}async setProperty(e){try{if(e){let t=await o.Z.LogBookEntry.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=i()().format("YYYY-MM-DD HH:mm:ss"),await o.Z.LogBookEntry.update(e,t),console.log("LogBookEntry setProperty",e,t),t}}catch(t){console.error("LogBookEntry setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await o.Z.LogBookEntry.update(e.id,e)})),console.log("LogBookEntry multiUpdate",e)}catch(t){console.error("LogBookEntry multiUpdate",e,t)}}async delete(e){try{return await o.Z.LogBookEntry.delete(`${e}`),console.log("LogBookEntry delete",e,!0),!0}catch(t){console.error("LogBookEntry delete",e,t)}}}let s=l},73366:(e,t,r)=>{r.d(t,{Z:()=>s});var a=r(83179),i=r.n(a),o=r(86708);class l{async save(e){try{return await o.Z.SeaLogsMember.put({...e,idbCRUD:"Update",idbCRUDDate:i()().format("YYYY-MM-DD HH:mm:ss")}),console.log("SeaLogsMember save",e),e}catch(t){console.error("SeaLogsMember save",e,t)}}async getAll(){try{let e=await o.Z.SeaLogsMember.toArray();return console.log("SeaLogsMember getAll",e),e}catch(e){console.error("SeaLogsMember getAll",e)}}async getById(e){try{let t=await o.Z.SeaLogsMember.get(`${e}`);return console.log("SeaLogsMember getById",e,t),t}catch(t){console.error("SeaLogsMember getById",e,t)}}async getByIds(e){try{let t=await o.Z.SeaLogsMember.where("id").anyOf(e).toArray();return console.log("SeaLogsMember getByIds",e,t),t}catch(t){console.error("SeaLogsMember getByIds",e,t)}}async getByVesselId(e){try{let t=(await o.Z.SeaLogsMember.toArray()).filter(t=>t.vehicles.nodes.some(t=>t.id===`${e}`));return console.log("SeaLogsMember getByVesselID",e,t),t}catch(t){console.error("SeaLogsMember getByVesselID",e,t)}}async bulkAdd(e){try{return await o.Z.SeaLogsMember.bulkAdd(e),console.log("SeaLogsMember bulkAdd",e),e}catch(t){if("BulkError"===t.name){let r=t.failuresByPos.map(e=>e.key),a=e.filter(e=>!r.includes(e.id));return await o.Z.SeaLogsMember.bulkAdd(a),console.log("SeaLogsMember bulkAdd::BulkError",e,t),e}console.error("SeaLogsMember bulkAdd",e,t)}}async setProperty(e){try{if(e){let t=await o.Z.SeaLogsMember.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=i()().format("YYYY-MM-DD HH:mm:ss"),await o.Z.SeaLogsMember.update(e,t),console.log("SeaLogsMember setProperty",e,t),t}}catch(t){console.error("SeaLogsMember setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await o.Z.SeaLogsMember.update(e.id,e)})),console.log("SeaLogsMember multiUpdate",e)}catch(t){console.error("SeaLogsMember multiUpdate",e,t)}}async delete(e){try{return await o.Z.SeaLogsMember.delete(`${e}`),console.log("SeaLogsMember delete",e,!0),!0}catch(t){console.error("SeaLogsMember delete",e,t)}}}let s=l},46372:(e,t,r)=>{r.d(t,{Z:()=>n});var a=r(83179),i=r.n(a),o=r(86708),l=r(1971);class s{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),r=t.id,a={...t,idbCRUD:"Update",idbCRUDDate:i()().format("YYYY-MM-DD HH:mm:ss")},l=await this.getById(r);return l?await o.Z.VehiclePosition.update(r,a):await o.Z.VehiclePosition.add(a),l=await this.getById(r),console.log("VehiclePosition save",e,l),l}catch(t){console.error("VehiclePosition save",e,t)}}async getAll(){try{let e=await o.Z.VehiclePosition.toArray();return console.log("VehiclePosition getAll",e),e}catch(e){console.error("VehiclePosition getAll",e)}}async getById(e){try{let t=await o.Z.VehiclePosition.get(`${e}`),r=await this.addRelationships(t);return console.log("VehiclePosition getById",e,r),r}catch(t){console.error("VehiclePosition getById",e,t)}}async getByIds(e){try{let t=await o.Z.VehiclePosition.where("id").anyOf(e).toArray(),r=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("VehiclePosition getByIds",e,r),r}catch(t){console.error("VehiclePosition getByIds",e,t)}}async getByVehicleId(e){try{let t=await o.Z.VehiclePosition.where("vehicleID").equals(`${e}`).toArray(),r=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("VehiclePosition getByVehicleId",e,r),r}catch(t){console.error("VehiclePosition getByVehicleId",e,t)}}async bulkAdd(e){try{return await o.Z.VehiclePosition.bulkAdd(e),console.log("VehiclePosition bulkAdd",e),e}catch(t){if("BulkError"===t.name){let r=t.failuresByPos.map(e=>e.key),a=e.filter(e=>!r.includes(e.id));return await o.Z.VehiclePosition.bulkAdd(a),console.log("VehiclePosition bulkAdd::BulkError",e,t),e}console.error("VehiclePosition bulkAdd",e,t)}}async addRelationships(e){if(!e)return console.log("VehiclePosition addRelationships",e),e;{let t=+e.geoLocationID>0?await this.geoLocationModel.getById(e.geoLocationID):null;return console.log("VehiclePosition addRelationships",e),{...e,geoLocation:t}}}async setProperty(e){try{if(e){let t=await o.Z.VehiclePosition.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=i()().format("YYYY-MM-DD HH:mm:ss"),await o.Z.VehiclePosition.update(e,t),console.log("VehiclePosition setProperty",e,t),t}}catch(t){console.error("VehiclePosition setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await o.Z.VehiclePosition.update(e.id,e)})),console.log("VehiclePosition multiUpdate",e)}catch(t){console.error("VehiclePosition multiUpdate",e,t)}}constructor(){this.geoLocationModel=new l.Z}}let n=s},70413:(e,t,r)=>{r.d(t,{Z:()=>c});var a=r(83179),i=r.n(a),o=r(86708),l=r(48755),s=r(46372);class n{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),r=t.id,a={...t,idbCRUD:"Update",idbCRUDDate:i()().format("YYYY-MM-DD HH:mm:ss")},l=await this.getById(r);return l?await o.Z.Vessel.update(r,a):await o.Z.Vessel.add(a),l=await this.getById(r),console.log("Vessel save",e,l),l}catch(t){console.error("Vessel save",e,t)}}async getAll(){try{let e=await o.Z.Vessel.toArray();return console.log("Vessel getAll",e),e}catch(e){console.error("Vessel getAll",e)}}async getById(e){try{let t=await o.Z.Vessel.get(`${e}`),r=await this.addRelationships(t);return console.log("Vessel getById",e,r),r}catch(t){console.error("Vessel getById",e,t)}}async bulkAdd(e){try{return await o.Z.Vessel.bulkAdd(e),console.log("Vessel bulkAdd",e),e}catch(t){if("BulkError"===t.name){let r=t.failuresByPos.map(e=>e.key),a=e.filter(e=>!r.includes(e.id));return await o.Z.Vessel.bulkAdd(a),console.log("Vessel bulkAdd::BulkError",e,t),e}console.error("Vessel bulkAdd",e,t)}}async addRelationships(e){if(!e)return console.log("Vessel addRelationships",e),e;{let t=+e.id>0?await this.logBookEntryModel.getByVesselId(e.id):[],r=+e.id>0?await this.positionsModel.getByVehicleId(e.id):[];return console.log("Vessel addRelationships",e),{...e,logBookEntries:{nodes:t},vehiclePositions:{nodes:r}}}}async setProperty(e){try{if(e){let t=await o.Z.Vessel.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=i()().format("YYYY-MM-DD HH:mm:ss"),await o.Z.Vessel.update(e,t),console.log("Vessel setProperty",e,t),t}}catch(t){console.error("Vessel setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await o.Z.Vessel.update(e.id,e)})),console.log("Vessel multiUpdate",e)}catch(t){console.error("Vessel multiUpdate",e,t)}}async delete(e){try{return await o.Z.Vessel.delete(`${e}`),console.log("Vessel delete",e,!0),!0}catch(t){console.error("Vesselb delete",e,t)}}constructor(){this.logBookEntryModel=new l.Z,this.positionsModel=new s.Z}}let c=n},28254:(e,t,r)=>{r.d(t,{d:()=>i});var a=r(60343);function i(){let[e,t]=a.useState(void 0);return a.useEffect(()=>{let e=window.matchMedia("(max-width: 1024px)"),r=()=>{t(window.innerWidth<1024)};return e.addEventListener("change",r),t(window.innerWidth<1024),()=>e.removeEventListener("change",r)},[]),!!e}},64837:(e,t,r)=>{r.d(t,{Z:()=>Y});var a=r(98768),i=r(22995),o=r(60343),l=r.n(o),s=r(28147),n=r(66263),c=r(96056);function d({team:e}){let{theme:t,resolvedTheme:r}=(0,c.F)(),[l,d]=o.useState(!1),[h,p]=o.useState(!1);return o.useEffect(()=>{d(!0),p(!1)},[]),o.useEffect(()=>{l&&p(!1)},[t,r,l]),a.jsx(i.w3,{children:a.jsx(i.LO,{children:(0,a.jsxs)(n.default,{href:"/dashboard",className:"flex flex-nowrap gap-4 py-2",children:[a.jsx("div",{className:"flex aspect-square size-8 items-center justify-center rounded-lg",children:a.jsx(s.default,{src:h?`/${e.logo}`:(()=>{if(!l||"light"===(r||t))return`/${e.logo}`;{let t=e.logo.replace(".png","-dark.png");return`/${t}`}})(),alt:"Company Logo",width:220,height:50,onError:()=>{console.warn("Logo failed to load, falling back to default logo"),p(!0)},priority:!0,unoptimized:!0})}),(0,a.jsxs)("div",{className:"grid flex-1 text-left leading-tight font-semibold",children:[a.jsx("span",{className:"truncate ",children:e.name}),a.jsx("span",{className:"truncate font-normal text-xs text-muted-foreground ",children:e.plan})]})]})})})}var h=r(39303),p=r(78644);let u=p.fC,m=p.wy,f=p.Fw;function g({items:e}){return a.jsx(i.Hz,{children:a.jsx(i.w3,{children:e.map(e=>a.jsx(u,{asChild:!0,defaultOpen:e.isActive,className:"group/collapsible",children:(0,a.jsxs)(i.LO,{children:[a.jsx(m,{asChild:!0,children:(0,a.jsxs)(i.bb,{tooltip:e.title,className:"group-data-[collapsible=icon]:gap-0",children:[e.icon,a.jsx("span",{className:"group-data-[collapsible=icon]:hidden",children:e.title}),a.jsx(h.Z,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 group-data-[collapsible=icon]:hidden"})]})}),a.jsx(f,{children:a.jsx(i.YA,{children:e.items?.map(e=>a.jsx(i.gM,{children:a.jsx(i.Vf,{asChild:!0,children:a.jsx("a",{href:e.url,children:a.jsx("span",{children:e.title})})})},e.title))})})]})},e.title))})})}var b=r(84961),v=r(23379),x=r(27514);r(46776);var y=r(69424);r(3233);var k=r(11390);let w=()=>{let{theme:e,setTheme:t}=(0,c.F)();return(0,a.jsxs)(x.DropdownMenuSub,{children:[a.jsx(x.DropdownMenuSubTrigger,{children:(0,a.jsxs)("div",{className:"flex items-center gap-2.5",children:[(()=>{switch(e){case"dark":return"Dark";case"light":return"Light";default:return"System"}})()," Theme"]})}),a.jsx(x.DropdownMenuPortal,{children:(0,a.jsxs)(x.DropdownMenuSubContent,{children:[a.jsx(x.DropdownMenuItem,{onClick:()=>t("light"),children:"Light"}),a.jsx(x.DropdownMenuItem,{onClick:()=>t("dark"),children:"Dark"}),a.jsx(x.DropdownMenuItem,{onClick:()=>t("system"),children:"System"})]})})]})};function M({user:e}){let[t,r]=(0,o.useState)(!1),[l,s]=(0,o.useState)(!1),[c,d]=(0,o.useState)(!1);(0,y.usePathname)();let[h,p]=(0,o.useState)([]),{isMobile:u}=(0,i.Ap)(),{open:m,hide:f,show:g}=(0,k.zv)(),M=()=>{m("general","form")};return a.jsx(i.w3,{children:a.jsx(i.LO,{children:(0,a.jsxs)(x.DropdownMenu,{children:[a.jsx(x.DropdownMenuTrigger,{asChild:!0,children:(0,a.jsxs)(i.bb,{size:"lg",className:"data-[state=open]:bg-outer-space-800 hover:bg-outer-space-800 bg-outer-space-600 pb-2.5 data-[state=open]:text-outer-space-50",children:[(0,a.jsxs)("div",{className:"grid flex-1 text-left leading-tight text-outer-space-50 text-sm font-medium pb-2",children:[a.jsx("span",{className:"truncate ",children:e.name}),a.jsx("span",{className:"truncate text-xs font-normal text-outer-space-100",children:e.email})]}),a.jsx(b.Z,{className:"ml-auto size-6 text-outer-space-50"})]})}),(0,a.jsxs)(x.DropdownMenuContent,{className:"w-[--radix-dropdown-menu-trigger-width] bg-background rounded-lg",side:u?"bottom":"right",align:"end",sideOffset:4,children:[a.jsx(x.DropdownMenuLabel,{className:"p-0 ",children:a.jsx("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left ",children:(0,a.jsxs)("div",{className:"grid flex-1 text-left leading-tight text-sm font-medium",children:[a.jsx("span",{className:"truncate ",children:e.name}),a.jsx("span",{className:"truncate text-xs font-normal text-muted-foreground",children:e.email})]})})}),a.jsx(x.DropdownMenuSeparator,{}),(0,a.jsxs)(x.DropdownMenuGroup,{children:[a.jsx(w,{}),t&&a.jsx(n.default,{href:"/select-client",children:a.jsx(x.DropdownMenuItem,{hoverEffect:!0,children:"Switch Client"})}),(t||l)&&a.jsx(n.default,{href:"/company-details",children:a.jsx(x.DropdownMenuItem,{hoverEffect:!0,children:"Company Details"})}),(t||l)&&a.jsx(n.default,{href:"/select-department",children:a.jsx(x.DropdownMenuItem,{hoverEffect:!0,children:"Select Department"})}),(0,a.jsxs)(x.DropdownMenuSub,{children:[a.jsx(x.DropdownMenuSubTrigger,{children:"Settings"}),a.jsx(x.DropdownMenuPortal,{children:(0,a.jsxs)(x.DropdownMenuSubContent,{children:[a.jsx(n.default,{href:"/settings/crew-duty/list",children:a.jsx(x.DropdownMenuItem,{children:"Crew Duties"})}),a.jsx(n.default,{href:"/settings/inventory/category",children:a.jsx(x.DropdownMenuItem,{children:"Inventory Categories"})}),a.jsx(n.default,{href:"/settings/maintenance/category",children:a.jsx(x.DropdownMenuItem,{children:"Maintenance Categories"})}),a.jsx(n.default,{href:"/settings/user-role",children:a.jsx(x.DropdownMenuItem,{children:"User Roles"})}),a.jsx(n.default,{href:"/department",children:a.jsx(x.DropdownMenuItem,{children:"Departments"})}),a.jsx(n.default,{href:"/location",children:a.jsx(x.DropdownMenuItem,{children:"Locations"})}),a.jsx(n.default,{href:"/key-contacts",children:a.jsx(x.DropdownMenuItem,{children:"Key Contacts"})}),a.jsx(n.default,{href:"/trip-schedules",children:a.jsx(x.DropdownMenuItem,{children:"Import Timetables"})}),a.jsx(n.default,{href:"/trip-schedule-services",children:a.jsx(x.DropdownMenuItem,{children:"Trip Schedule Services"})}),a.jsx(n.default,{href:"/trip-report-schedules",children:a.jsx(x.DropdownMenuItem,{children:"Trip Report Schedules"})})]})})]}),(0,a.jsxs)(x.DropdownMenuSub,{children:[a.jsx(x.DropdownMenuSubTrigger,{children:"Help"}),a.jsx(x.DropdownMenuPortal,{children:(0,a.jsxs)(x.DropdownMenuSubContent,{children:[a.jsx(n.default,{href:"https://sealogsv2.tawk.help/",target:"_blank",children:a.jsx(x.DropdownMenuItem,{children:"Help docs"})}),a.jsx(x.DropdownMenuItem,{onClick:()=>M(),children:"Feedback"})]})})]})]}),a.jsx(x.DropdownMenuSeparator,{}),a.jsx(n.default,{href:"/logout",children:(0,a.jsxs)(x.DropdownMenuItem,{children:[a.jsx(v.Z,{}),"Log out"]})})]})]})})})}function D({projects:e}){let{isMobile:t}=(0,i.Ap)();return a.jsx(i.Hz,{children:a.jsx(i.w3,{children:e.map(e=>a.jsx(i.LO,{children:a.jsx(i.bb,{asChild:!0,tooltip:e.name,children:(0,a.jsxs)("a",{href:e.url,className:"group-data-[collapsible=icon]:gap-0 group-data-[collapsible=icon]:!p-1",children:[e.icon,a.jsx("span",{className:"group-data-[collapsible=icon]:hidden",children:e.name})]})})},e.name))})})}var L=r(9210);let Z={user:{name:"SeaLogs",email:"<EMAIL>"},team:{name:"South Inc",logo:"logo-small.png",plan:"powered by SeaLogs"},versions:["3.4.0","1.1.0-alpha","2.0.0-beta1"],singleLinks:[{name:"Home port",url:"/dashboard",icon:a.jsx(L.ll,{className:"h-9 w-9"})},{name:"All vessels",url:"/vessel",icon:a.jsx(L.bF,{className:"h-9 w-9 bg-accent/0 ring-0 rounded-none p-0 group-data-[collapsible=icon]:-ml-2"})}],navMain:[{title:"Crew",url:"#",icon:a.jsx(L.Ko,{className:"h-9 w-9 p-0.5"}),items:[{title:"All crew",url:"/crew"},{title:"Training / Drills",url:"/crew-training"}]},{title:"Health & safety",url:"#",icon:a.jsx(L.of,{className:"h-9 w-9"}),items:[{title:"Risk Evaluations",url:"/risk-evaluations"},{title:"Risk Strategies",url:"/risk-strategies"},{title:"Drills / training matrix",url:"/training-matrix"}]}],navMain2:[{title:"Inventory",url:"#",icon:a.jsx(L.A0,{className:"h-9 w-9"}),items:[{title:"All inventory",url:"/inventory"},{title:"Suppliers",url:"/inventory/suppliers"},{title:"Maintenance",url:"/maintenance"},{title:"Documents",url:"/document-locker"}]}]};function E({...e}){let[t,r]=(0,o.useState)(""),[l,s]=(0,o.useState)(!0),[n,c]=(0,o.useState)(""),[h,p]=(0,o.useState)(""),u=o.useMemo(()=>({...Z.team,name:t||Z.team.name}),[t]),m=o.useMemo(()=>({...Z.user,name:n||Z.user.name,email:h||Z.user.email}),[n,h]);return(0,a.jsxs)(i.DD,{collapsible:"icon",...e,children:[a.jsx(i.$l,{children:a.jsx(d,{team:u})}),(0,a.jsxs)(i.TZ,{children:[a.jsx(D,{projects:Z.singleLinks}),a.jsx(g,{items:Z.navMain}),a.jsx(g,{items:Z.navMain2})]}),a.jsx(i.ZW,{children:a.jsx(M,{user:m})}),a.jsx(i.wJ,{})]})}var j=r(25394),C=r(73404),I=(r(46020),r(56937));let R=o.forwardRef(({...e},t)=>a.jsx("nav",{ref:t,"aria-label":"breadcrumb",...e}));R.displayName="Breadcrumb";let S=o.forwardRef(({className:e,...t},r)=>a.jsx("ol",{ref:r,className:(0,I.cn)("flex flex-wrap items-center gap-1.5 break-words text-neutral-400 sm:gap-2.5",e),...t}));S.displayName="BreadcrumbList";let B=o.forwardRef(({className:e,...t},r)=>a.jsx("li",{ref:r,className:(0,I.cn)("inline-flex items-center gap-1.5",e),...t}));B.displayName="BreadcrumbItem";let V=o.forwardRef(({asChild:e,className:t,...r},i)=>{let o=e?C.g7:"a";return a.jsx(o,{ref:i,className:(0,I.cn)("transition-colors hover:text-primary",t),...r})});V.displayName="BreadcrumbLink";let N=o.forwardRef(({className:e,...t},r)=>a.jsx("span",{ref:r,role:"link","aria-disabled":"true","aria-current":"page",className:(0,I.cn)(" text-foreground",e),...t}));N.displayName="BreadcrumbPage";let U=({children:e,className:t,...r})=>a.jsx("li",{role:"presentation","aria-hidden":"true",className:(0,I.cn)("[&>svg]:w-3.5 [&>svg]:h-3.5",t),...r,children:e??a.jsx(h.Z,{})});U.displayName="BreadcrumbSeparator";var W=r(79418),A=r(94060),P=r(70413),H=r(73366);function _(){let{vesselID:e,vesselNameParam:t,vesselName:r,pathname:i}=function(){let e=(0,y.usePathname)(),t=(0,y.useSearchParams)(),r=0,a=t.get("name")||"";if(t.get("vesselID")?r=+t.get("vesselID"):t.get("id")&&e.includes("/vessel/")&&(r=+t.get("id")),e.startsWith("/maintenance")&&!r){let e=t.get("redirect_to");if(e){let t=new URLSearchParams(e.split("?")[1]?.split("%26")[0]||"");t.get("id")&&(r=+t.get("id")),t.get("name")&&(a=t.get("name"))}}let{vesselName:i}=function(e,t=!1){let[r,a]=(0,o.useState)(null),[i,l]=(0,o.useState)(!0);new P.Z;let[s]=(0,W.t)(A.rk,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneVessel;t&&t.title&&a(t.title),l(!1)},onError:e=>{console.error("queryVesselInfo error",e),l(!1)}});return(0,o.useRef)(e),{vesselName:r,isLoading:i}}(r);return{vesselID:r,vesselNameParam:a,vesselName:i,pathname:e}}(),{crewMemberID:s,crewMemberName:n}=function(){let e=(0,y.usePathname)(),t=(0,y.useSearchParams)(),r=0;t.get("id")&&e.includes("/crew/info")&&(r=+t.get("id"));let{crewMemberName:a}=function(e,t=!1){let[r,a]=(0,o.useState)(null),[i,l]=(0,o.useState)(!0);new H.Z;let[s]=(0,W.t)(A.Rc,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneSeaLogsMember;t&&(t.firstName||t.surname)&&a(`${t.firstName||""} ${t.surname||""}`.trim()),l(!1)},onError:e=>{console.error("queryCrewMemberInfo error",e),l(!1)}});return(0,o.useRef)(e),{crewMemberName:r,isLoading:i}}(r);return{crewMemberID:r,crewMemberName:a,pathname:e}}(),c={"":{label:"Port",path:"",href:"/"},vessel:{label:"Vessel",path:"vessel",parent:""},"vessel/info":{label:t||"Vessel Info",path:"vessel/info",parent:"vessel",dynamic:!0},crew:{label:"Crew",path:"crew",parent:""},"crew/info":{label:n||"Crew Info",path:"crew/info",parent:"crew",dynamic:!0},training:{label:"Crew Training",path:"training",parent:""},"training/info":{label:"Info",path:"training/info",parent:"training"},evaluations:{label:"Risk Evaluations",path:"evaluations",parent:""},strategies:{label:"Risk Strategies",path:"strategies",parent:""},inventory:{label:"Inventory",path:"inventory",parent:""},"inventory/view":{label:"View",path:"inventory/view",parent:"inventory"},"inventory/suppliers":{label:"Inventory Suppliers",path:"inventory/suppliers",parent:""},"inventory/suppliers/view":{label:"View",path:"inventory/suppliers/view",parent:"inventory/suppliers"},maintenance:{label:"Maintenance",path:"maintenance",parent:"vessel"},"maintenance/view":{label:"View",path:"maintenance/view",parent:"maintenance"},"maintenance/add":{label:"Add",path:"maintenance/add",parent:"maintenance"},"maintenance/edit":{label:"Edit",path:"maintenance/edit",parent:"maintenance"},locker:{label:"Document Locker",path:"locker",parent:""},"log-entries":{label:"Logbook",path:"log-entries",parent:"vessel"}},d=(0,o.useMemo)(()=>{let e=i.substring(1);return Object.keys(c).filter(t=>e===t||e.startsWith(t+"/")).sort((e,t)=>t.length-e.length)[0]||""},[i,c,n]),h=(0,o.useMemo)(()=>{let t=e=>{let r=c[e];return r?[...r.parent?t(r.parent):[],r]:[]},a=[c[""]];if(d){let i=t(d).filter(e=>""!==e.path);if(("log-entries"===d||"maintenance"===d||d.startsWith("maintenance/"))&&r&&e>0){let t=i.findIndex(e=>"vessel"===e.path),o={label:r,path:"vessel-name",href:`/vessel/info?id=${e}&name=${encodeURIComponent(r)}`};-1!==t?i.splice(t+1,0,o):(a.push(c.vessel),a.push(o))}a=[...a,...i]}return a.filter(e=>"vessel"!==e.path)},[d,c,e,r,n]);return a.jsx(R,{children:a.jsx(S,{className:"flex-nowrap",children:h.map((e,t)=>(0,a.jsxs)(l().Fragment,{children:[t>0&&a.jsx(U,{}),a.jsx(B,{className:"grid",children:t<h.length-1?a.jsx(V,{className:"truncate min-w-7",href:e.href||`/${e.path}`,children:e.label}):a.jsx(N,{children:e.label})})]},e.path))})})}function T(){return a.jsx(a.Fragment,{children:a.jsx("aside",{className:"fixed top-0 right-0 z-40 w-64 h-screen p-4 overflow-y-auto transition-transform  translate-x-full",children:(0,a.jsxs)("div",{className:"h-screen sticky top-0",children:[a.jsx("div",{className:"mb-4 flex justify-end pt-4"}),a.jsx("div",{className:"my-4"})]})})})}function Y({children:e}){return a.jsx("div",{className:"flex flex-row bg-card phablet:bg-background min-h-screen max-h-screen h-full p-0 md:px-2 md:pt-2 lg:px-3 lg:pt-3 overflow-hidden w-auto text-foreground",children:(0,a.jsxs)(i.Hn,{children:[a.jsx(E,{}),(0,a.jsxs)(i.kV,{children:[(0,a.jsxs)("header",{className:"flex h-12 shrink-0 items-center gap-2 px-4 border-b border-border text-sm",children:[a.jsx(i.vP,{className:"-ml-1 text-primary"}),a.jsx(j.Z0,{orientation:"vertical",className:"mr-2 h-4"}),a.jsx(_,{})]}),a.jsx(j.xr,{className:"bg-card phablet:bg-background",children:a.jsx("div",{className:"flex-1 flex flex-col p-3 small:p-4 phablet:p-5 lg:p-6 xl:p-8",children:e})}),a.jsx(T,{})]})]})})}},22995:(e,t,r)=>{r.d(t,{$l:()=>M,Ap:()=>b,DD:()=>x,Hn:()=>v,Hz:()=>Z,LO:()=>j,TZ:()=>L,Vf:()=>B,YA:()=>R,ZW:()=>D,bb:()=>I,gM:()=>S,kV:()=>w,vP:()=>y,w3:()=>E,wJ:()=>k});var a=r(98768),i=r(60343),o=r(73404),l=r(85745),s=r(7671),n=r(28254),c=r(56937),d=r(39544),h=r(71890),p=r(26509),u=r(69852),m=r(62861),f=r(70684);let g=i.createContext(null);function b(){let e=i.useContext(g);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let v=i.forwardRef(({defaultOpen:e=!0,open:t,onOpenChange:r,className:o,style:l,children:s,...d},h)=>{let p=(0,n.d)(),[u,m]=i.useState(!1),[b,v]=i.useState(e),x=t??b,y=i.useCallback(e=>{let t="function"==typeof e?e(x):e;r?r(t):v(t),document.cookie=`sidebar:state=${t}; path=/; max-age=604800`},[r,x]),k=i.useCallback(()=>p?m(e=>!e):y(e=>!e),[p,y,m]);i.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),k())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[k]);let w=x?"expanded":"collapsed",M=i.useMemo(()=>({state:w,open:x,setOpen:y,isMobile:p,openMobile:u,setOpenMobile:m,toggleSidebar:k}),[w,x,y,p,u,m,k]);return a.jsx(g.Provider,{value:M,children:a.jsx(f.TooltipProvider,{delayDuration:0,children:a.jsx("div",{style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...l},className:(0,c.cn)("group/sidebar-wrapper border border-border shadow-lg overflow-hidden rounded-lg flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar",o),ref:h,...d,children:s})})})});v.displayName="SidebarProvider";let x=i.forwardRef(({side:e="left",variant:t="sidebar",collapsible:r="offcanvas",className:i,children:o,...l},s)=>{let{isMobile:n,state:d,openMobile:h,setOpenMobile:p}=b();return"none"===r?a.jsx("div",{className:(0,c.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground text-sm font-normal",i),ref:s,...l,children:o}):n?a.jsx(u.Sheet,{open:h,onOpenChange:p,...l,children:a.jsx(u.SheetContent,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] shadow-[0_1px_3px_rgba(0,0,0,0.1)] bg-sidebar p-0 text-sidebar-foreground text-sm font-normal [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:a.jsx("div",{className:"flex h-full w-full flex-col",children:o})})}):(0,a.jsxs)("div",{ref:s,className:"group peer hidden text-sidebar-foreground sm:block bg-sidebar","data-state":d,"data-collapsible":"collapsed"===d?r:"","data-variant":t,"data-side":e,children:[a.jsx("div",{className:(0,c.cn)("relative h-svh w-[--sidebar-width] transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===t||"inset"===t?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),a.jsx("div",{className:(0,c.cn)("fixed inset-y-0 z-10 lg:m-3 sm:m-2 m-1 hidden h-svh shadow-[0_1px_3px_rgba(0,0,0,0.1)] bg-sidebar-background w-[--sidebar-width] transition-[left,right,width] duration-200 ease-linear sm:flex border-r-border","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===t||"inset"===t?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",i),...l,children:a.jsx("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col rounded-l-xl overflow-hidden group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:o})})]})});x.displayName="Sidebar";let y=i.forwardRef(({className:e,onClick:t,...r},i)=>{let{toggleSidebar:o}=b();return(0,a.jsxs)(d.Button,{ref:i,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,c.cn)("h-7 w-7",e),onClick:e=>{t?.(e),o()},...r,children:[a.jsx(s.Z,{}),a.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})});y.displayName="SidebarTrigger";let k=i.forwardRef(({className:e,...t},r)=>{let{toggleSidebar:i}=b();return a.jsx("button",{ref:r,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:i,title:"Toggle Sidebar",className:(0,c.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...t})});k.displayName="SidebarRail";let w=i.forwardRef(({className:e,...t},r)=>a.jsx("main",{ref:r,className:(0,c.cn)("relative w-full flex flex-col shadow-lg rounded-r-xl","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] sm:peer-data-[variant=inset]:m-2 sm:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 sm:peer-data-[variant=inset]:ml-0 sm:peer-data-[variant=inset]:rounded-xl sm:peer-data-[variant=inset]:shadow","overflow-y-auto",e),...t}));w.displayName="SidebarInset",i.forwardRef(({className:e,...t},r)=>a.jsx(h.I,{ref:r,"data-sidebar":"input",className:(0,c.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",e),...t})).displayName="SidebarInput";let M=i.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,"data-sidebar":"header",className:(0,c.cn)("flex flex-col rounded-tl-xl gap-2 p-3",e),...t}));M.displayName="SidebarHeader";let D=i.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,"data-sidebar":"footer",className:(0,c.cn)("flex flex-col gap-2 p-2",e),...t}));D.displayName="SidebarFooter",i.forwardRef(({className:e,...t},r)=>a.jsx(p.Separator,{ref:r,"data-sidebar":"separator",className:(0,c.cn)("mx-2 w-auto bg-sidebar-border",e),...t})).displayName="SidebarSeparator";let L=i.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,"data-sidebar":"content",className:(0,c.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t}));L.displayName="SidebarContent";let Z=i.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,"data-sidebar":"group",className:(0,c.cn)("relative flex w-full min-w-0 flex-col px-2",e),...t}));Z.displayName="SidebarGroup",i.forwardRef(({className:e,asChild:t=!1,...r},i)=>{let l=t?o.g7:"div";return a.jsx(l,{ref:i,"data-sidebar":"group-label",className:(0,c.cn)("flex h-8 shrink-0 items-center rounded-md px-2  font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...r})}).displayName="SidebarGroupLabel",i.forwardRef(({className:e,asChild:t=!1,...r},i)=>{let l=t?o.g7:"button";return a.jsx(l,{ref:i,"data-sidebar":"group-action",className:(0,c.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-primary-foreground hover:text-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:sm:hidden","group-data-[collapsible=icon]:hidden",e),...r})}).displayName="SidebarGroupAction",i.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,"data-sidebar":"group-content",className:(0,c.cn)("w-full ",e),...t})).displayName="SidebarGroupContent";let E=i.forwardRef(({className:e,...t},r)=>a.jsx("ul",{ref:r,"data-sidebar":"menu",className:(0,c.cn)("flex w-full min-w-0 flex-col gap-2",e),...t}));E.displayName="SidebarMenu";let j=i.forwardRef(({className:e,...t},r)=>a.jsx("li",{ref:r,"data-sidebar":"menu-item",className:(0,c.cn)("group/menu-item relative",e),...t}));j.displayName="SidebarMenuItem";let C=(0,l.j)("peer/menu-button flex w-full items-center gap-4 overflow-hidden rounded-md p-3 text-left outline-none ring-sidebar-ringtransition-[width,height,padding] hover:bg-primary-foreground hover:text-sidebar-primary focus-visible:ring-2 active:bg-primary-foreground active:text-sidebar-primary disabled:pointer-events-none disabled:opacity-100 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-100 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-primary data-[state=open]:hover:bg-primary-foreground data-[state=open]:hover:text-sidebar-primary group-data-[collapsible=icon]:!size-9 group-data-[collapsible=icon]:!p-1 [&>span:last-child]:truncate",{variants:{variant:{default:"hover:bg-accent hover:text-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-primary-foreground hover:text-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-12 ",lg:"h-12 group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}}),I=i.forwardRef(({asChild:e=!1,isActive:t=!1,variant:r="default",size:i="default",tooltip:l,className:s,...n},d)=>{let h=e?o.g7:"button",{isMobile:p,state:u}=b(),m=a.jsx(h,{ref:d,"data-sidebar":"menu-button","data-size":i,"data-active":t,className:(0,c.cn)(C({variant:r,size:i}),s),...n});return l?("string"==typeof l&&(l={children:l}),(0,a.jsxs)(f.Tooltip,{children:[a.jsx(f.TooltipTrigger,{asChild:!0,children:m}),a.jsx(f.TooltipContent,{side:"right",align:"center",hidden:"collapsed"!==u||p,...l})]})):m});I.displayName="SidebarMenuButton",i.forwardRef(({className:e,asChild:t=!1,showOnHover:r=!1,...i},l)=>{let s=t?o.g7:"button";return a.jsx(s,{ref:l,"data-sidebar":"menu-action",className:(0,c.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-primary-foreground hover:text-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-primary [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:sm:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",r&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-primary sm:opacity-0",e),...i})}).displayName="SidebarMenuAction",i.forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,"data-sidebar":"menu-badge",className:(0,c.cn)("pointer-events-none absolute right-1 flex h-5 min-w-5 select-none items-center justify-center rounded-md px-1  font-medium tabular-nums text-sidebar-foreground","peer-hover/menu-button:text-sidebar-primary peer-data-[active=true]/menu-button:text-sidebar-primary","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",e),...t})).displayName="SidebarMenuBadge",i.forwardRef(({className:e,showIcon:t=!1,...r},o)=>{let l=i.useMemo(()=>`${Math.floor(40*Math.random())+50}%`,[]);return(0,a.jsxs)("div",{ref:o,"data-sidebar":"menu-skeleton",className:(0,c.cn)("flex h-8 items-center gap-2 rounded-md px-2",e),...r,children:[t&&a.jsx(m.O,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),a.jsx(m.O,{className:"h-4 max-w-[--skeleton-width] flex-1","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":l}})]})}).displayName="SidebarMenuSkeleton";let R=i.forwardRef(({className:e,...t},r)=>a.jsx("ul",{ref:r,"data-sidebar":"menu-sub",className:(0,c.cn)("ml-6 mt-4 flex min-w-0 translate-x-px flex-col gap-3 border-l border-dashed border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...t}));R.displayName="SidebarMenuSub";let S=i.forwardRef(({...e},t)=>a.jsx("li",{ref:t,...e}));S.displayName="SidebarMenuSubItem";let B=i.forwardRef(({asChild:e=!1,size:t="md",isActive:r,className:i,...l},s)=>{let n=e?o.g7:"a";return a.jsx(n,{ref:s,"data-sidebar":"menu-sub-button","data-size":t,"data-active":r,className:(0,c.cn)("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-accent hover:text-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-primary disabled:pointer-events-none disabled:opacity-100 aria-disabled:pointer-events-none aria-disabled:opacity-100 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-primary","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-primary","sm"===t&&"","md"===t&&"","group-data-[collapsible=icon]:hidden",i),...l})});B.displayName="SidebarMenuSubButton"}};