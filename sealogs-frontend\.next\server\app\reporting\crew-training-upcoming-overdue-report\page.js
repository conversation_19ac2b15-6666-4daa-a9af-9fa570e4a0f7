(()=>{var e={};e.id=4708,e.ids=[4708],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},61458:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),s(12202),s(71238),s(78398),s(57757),s(48045);var r=s(40060),i=s(33581),n=s(57567),a=s.n(n),l=s(51650),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["reporting",{children:["crew-training-upcoming-overdue-report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,12202)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\crew-training-upcoming-overdue-report\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\crew-training-upcoming-overdue-report\\page.tsx"],u="/reporting/crew-training-upcoming-overdue-report/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/reporting/crew-training-upcoming-overdue-report/page",pathname:"/reporting/crew-training-upcoming-overdue-report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},51873:(e,t,s)=>{Promise.resolve().then(s.bind(s,39012))},88332:(e,t,s)=>{Promise.resolve().then(s.bind(s,44517))},75546:(e,t,s)=>{"use strict";s.d(t,{Br:()=>p,fU:()=>m,o0:()=>c,p6:()=>d,vq:()=>u});var r=s(83179),i=s.n(r),n=s(7678),a=s.n(n),l=s(14826),o=s.n(l);let d=(e="",t=!0)=>{let s;if(a()(o()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[s,r,i]=e.split("-"),n=t?s.slice(-2):s,a=parseInt(i,10).toString().padStart(2,"0"),l=parseInt(r,10).toString().padStart(2,"0");return`${a}/${l}/${n}`}if(!(s=e&&"object"==typeof e?i()(e.toString()):i()(e)).isValid())return"";let r=s.format("DD"),n=s.format("MM"),l=t?s.format("YY"):s.format("YYYY");return`${r}/${n}/${l}`},c=(e="",t=!0)=>{let s;if(a()(o()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[s,r,i]=e.split("-"),n=t?s.slice(-2):s,a=parseInt(i,10).toString().padStart(2,"0"),l=parseInt(r,10).toString().padStart(2,"0");return`${a}/${l}/${n} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[s,r]=e.split(" "),[i,n,a]=s.split("-"),l=t?i.slice(-2):i,o=r.split(":"),d=o[0].padStart(2,"0"),c=o[1].padStart(2,"0"),u=parseInt(a,10).toString().padStart(2,"0"),p=parseInt(n,10).toString().padStart(2,"0");return`${u}/${p}/${l} ${d}:${c}`}if(!(s=e&&"object"==typeof e?i()(e.toString()):i()(e)).isValid())return"";let r=s.format("DD"),n=s.format("MM"),l=t?s.format("YY"):s.format("YYYY"),d=s.format("HH:mm");return`${r}/${n}/${l} ${d}`},u=(e="")=>a()(o()(e))?"":i()(e).format("YYYY-MM-DD HH:mm:ss"),p=(e="")=>a()(o()(e))?new Date:new Date(`${e}T10:00:00Z`),m=(e,t)=>{let s=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),r=e=>e.includes(" ")?e.replace(" ","T"):e,i=e=>{if(!e||"string"!=typeof e)return null;if(s(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(r(e))},n=i(e),a=i(t);return!n||!a||isNaN(n.getTime())||isNaN(a.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):a>n}},39012:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var r=s(98768),i=s(46776),n=s(26100),a=s(94060),l=s(79418),o=s(60343),d=s(66263),c=s(17380),u=s(43692),p=s(37042),m=s(13842),g=s(75546),x=s(25394),h=s(51742);let f=({memberId:e=0,vesselId:t=0})=>{let[s,d]=(0,o.useState)(!0),[g,x]=(0,o.useState)({totalCount:0,hasNextPage:!1,hasPreviousPage:!1}),[h,f]=(0,o.useState)([]),[v,j]=(0,o.useState)([]),[b,S]=(0,o.useState)(0),[N,w]=(0,o.useState)({}),[D,C]=(0,o.useState)([]),[I,P]=(0,o.useState)([]),[T,$]=(0,o.useState)([]),[M,q]=(0,o.useState)([]),[k,{loading:O}]=(0,l.t)(a.ly,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTrainingSessions.nodes,s=Array.from(new Set(t.map(e=>e.vessel.id))).filter(e=>0!=+e),r=Array.from(new Set(t.flatMap(e=>e.trainingTypes.nodes.map(e=>e.id)))),i=Array.from(new Set(t.map(e=>e.trainerID))).filter(e=>0!=+e),n=Array.from(new Set(t.flatMap(e=>e.members.nodes.map(e=>e.id))));t&&(f(t),C(s),P(r),$(i),q(n)),x(e.readTrainingSessions.pageInfo)},onError:e=>{console.error("queryTrainingList error",e)}}),_=async(e=0,t={...N})=>{await k({variables:{filter:t,offset:100*e,limit:100}})},A=e=>{e<0||e===b||(S(e),V(N),_(e,N))},[R,{loading:E}]=(0,l.t)(a.qX,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTrainingSessionDues.nodes;t&&j(Object.values(t.filter(e=>e.vessel.seaLogsMembers.nodes.some(t=>t.id===e.memberID)).map(e=>({...e,status:(0,m.nu)(e)})).filter(e=>e.status.isOverdue||!1===e.status.isOverdue&&!0===e.status.dueWithinSevenDays).reduce((e,t)=>{let s=`${t.vesselID}-${t.trainingTypeID}-${t.dueDate}`;return e[s]||(e[s]={id:t.id,vesselID:t.vesselID,vessel:t.vessel,trainingTypeID:t.trainingTypeID,trainingType:t.trainingType,dueDate:t.dueDate,status:t.status,members:[]}),e[s].members.push(t.member),e},{})).map(e=>{let t=e.members.reduce((e,t)=>{let s=e.find(e=>e.id===t.id);return s?(s.firstName=t.firstName,s.surname=t.surname):e.push(t),e},[]);return{id:e.id,vesselID:e.vesselID,vessel:e.vessel,trainingTypeID:e.trainingTypeID,trainingType:e.trainingType,status:e.status,dueDate:e.dueDate,members:t}}))},onError:e=>{console.error("readTrainingSessionDues error",e)}}),V=async s=>{let r={};e>0&&(r.memberID={eq:+e}),t>0&&(r.vesselID={eq:+t}),s.vesselID&&(r.vesselID=s.vesselID),s.trainingTypes&&(r.trainingTypeID={eq:s.trainingTypes.id.contains}),s.members&&(r.memberID={eq:s.members.id.contains}),s.date?r.dueDate=s.date:r.dueDate={ne:null},await R({variables:{filter:r}})};(0,o.useEffect)(()=>{if(s){let t={...N};+e>0&&(t.members={id:{contains:+e}}),w(t),V(t),_(0,t),d(!1)}},[s]);let[Y,G]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{G(i.Zu)},[]),Y&&((0,i.Fs)("EDIT_TRAINING",Y)||(0,i.Fs)("VIEW_TRAINING",Y)||(0,i.Fs)("RECORD_TRAINING",Y)||(0,i.Fs)("VIEW_MEMBER_TRAINING",Y)))?(0,r.jsxs)("div",{className:"w-full",children:[r.jsx(p.Z,{onChange:({type:e,data:t})=>{let s={...N};"vessel"===e&&(t?s.vesselID={eq:+t.value}:delete s.vesselID),"trainingType"===e&&(t?s.trainingTypes={id:{contains:+t.value}}:delete s.trainingTypes),"trainer"===e&&(t?s.trainer={id:{eq:+t.value}}:delete s.trainer),"member"===e&&(t?s.members={id:{contains:+t.value}}:delete s.members),"dateRange"===e&&(t.startDate&&t.endDate?s.date={gte:t.startDate,lte:t.endDate}:delete s.date),w(s),V(s),_(0,s)},vesselIdOptions:D,trainingTypeIdOptions:I,trainerIdOptions:T,memberIdOptions:M}),O||E?r.jsx(c.hM,{}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(y,{trainingSessionDues:v}),r.jsx(u.Z,{page:b,limit:100,visiblePageCount:5,...g,onClick:e=>A(e)})]})]}):Y?r.jsx(n.Z,{errorMessage:"OopsYou do not have the permission to view this section."}):r.jsx(n.Z,{})},v=(e=!1)=>(0,h.wu)([{accessorKey:"dueDate",header:"Overdue / Upcoming",cellAlignment:"left",cell:({row:e})=>{let t=e.original;return r.jsx(d.default,{href:`/crew-training/info?id=${t.id}`,className:"font-medium hover:underline",children:(0,g.p6)(t.dueDate)})}},{accessorKey:"trainingType",header:"Training/drill",cellAlignment:"left",breakpoint:"tablet-md",cell:({row:t})=>{let s=t.original;return(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{children:[s.trainingType.title,!e&&(0,r.jsxs)("span",{className:"ml-1 inline-block tablet-md:hidden",children:[": ",s.vessel.title]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2 tablet-md:hidden",children:[r.jsx(j,{members:s.members,isVesselView:e}),r.jsx(x.OE,{isOverdue:s.status.isOverdue,isUpcoming:s.status.dueWithinSevenDays,label:s.status.label})]})]})}},...e?[]:[{accessorKey:"vessel",header:"Where",cellAlignment:"left",breakpoint:"tablet-md",cell:({row:e})=>e.original.vessel.title}],{accessorKey:"members",header:"Who",cellAlignment:"left",breakpoint:"landscape",cell:({row:t})=>{let s=t.original;return r.jsx(j,{members:s.members,isVesselView:e})}},{accessorKey:"status",header:"",cellAlignment:"right",breakpoint:"tablet-md",cell:({row:e})=>{let t=e.original;return r.jsx(x.OE,{isOverdue:t.status.isOverdue,isUpcoming:t.status.dueWithinSevenDays,label:t.status.label})}}]),j=({members:e,isVesselView:t=!1})=>{let s=e.slice(0,3),i=e.slice(3);return(0,r.jsxs)("div",{className:"flex items-center flex-wrap gap-2",children:[s.map(e=>r.jsx("div",{className:"inline-block border rounded-md px-2 py-1 text-nowrap bg-muted/50 text-sm",children:t?e.firstName||"":`${e.firstName||""} ${e.surname||""}`.trim()},e.id)),i.length>0&&(0,r.jsxs)(x.J2,{children:[r.jsx(x.CM,{asChild:!0,children:(0,r.jsxs)(x.zx,{variant:"secondary",size:"sm",children:["+",i.length," more"]})}),r.jsx(x.yk,{className:"p-2 w-64 max-h-64 overflow-auto",children:r.jsx("div",{className:"space-y-2",children:i.map(e=>r.jsx("div",{className:"py-1",children:t?e.firstName||"":`${e.firstName||""} ${e.surname||""}`.trim()},e.id))})})]})]})},b=e=>e.status.isOverdue?"overdue":e.status.dueWithinSevenDays?"upcoming":"normal",y=({trainingSessionDues:e,isVesselView:t=!1})=>{if(!e?.length)return null;let s=v(t);return r.jsx(h.QT,{columns:s,data:e,showToolbar:!1,rowStatus:b,pageSize:e.length})};function S(){let[e,t]=(0,o.useState)(!1),[s,i]=(0,o.useState)(!1);return e&&s?r.jsx(f,{}):e?r.jsx(n.Z,{errorMessage:"OopsYou do not have the permission to view this section."}):r.jsx(n.Z,{})}},44517:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(98768);s(60343);var i=s(32993),n=s(64837);function a({children:e}){return r.jsx(i.default,{children:r.jsx(n.Z,{children:e})})}},10901:(e,t,s)=>{"use strict";s.d(t,{k:()=>p});var r=s(98768),i=s(11652),n=s(41641),a=s(39303),l=s(40712),o=s(39544),d=s(24224),c=s(25394),u=s(50058);function p({table:e,pageSizeOptions:t=[10,20,30,40,50],showPageSizeSelector:s=!0}){let p=(0,u.k)();return r.jsx("div",{className:"flex items-center justify-center px-2",children:(0,r.jsxs)("div",{className:"flex gap-2 items-end phablet:items-center phablet:space-x-6 lg:space-x-8",children:[s&&r.jsx("div",{className:"flex items-center space-x-2",children:r.jsx(c.__,{label:"Rows per page",position:p.phablet?"left":"top",htmlFor:"rows-per-page",className:"text-sm",children:(0,r.jsxs)(d.Select,{value:`${e.getState().pagination.pageSize}`,onValueChange:t=>{e.setPageSize(Number(t))},children:[r.jsx(d.SelectTrigger,{className:"h-8 w-[70px]",children:r.jsx(d.SelectValue,{placeholder:e.getState().pagination.pageSize})}),r.jsx(d.SelectContent,{side:"top",children:t.map(e=>r.jsx(d.SelectItem,{value:`${e}`,children:e},e))})]})})}),(0,r.jsxs)("div",{className:"flex w-fit items-center justify-center text-sm",children:["Page ",e.getState().pagination.pageIndex+1," of"," ",e.getPageCount()]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(o.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>e.setPageIndex(0),disabled:!e.getCanPreviousPage(),children:[r.jsx("span",{className:"sr-only",children:"Go to first page"}),r.jsx(i.Z,{})]}),(0,r.jsxs)(o.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>e.previousPage(),disabled:!e.getCanPreviousPage(),children:[r.jsx("span",{className:"sr-only",children:"Go to previous page"}),r.jsx(n.Z,{})]}),(0,r.jsxs)(o.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>e.nextPage(),disabled:!e.getCanNextPage(),children:[r.jsx("span",{className:"sr-only",children:"Go to next page"}),r.jsx(a.Z,{})]}),(0,r.jsxs)(o.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>e.setPageIndex(e.getPageCount()-1),disabled:!e.getCanNextPage(),children:[r.jsx("span",{className:"sr-only",children:"Go to last page"}),r.jsx(l.Z,{})]})]})]})})}},6958:(e,t,s)=>{"use strict";s.d(t,{n:()=>n});var r=s(98768),i=s(37042);function n({table:e,onChange:t}){return r.jsx(i.Z,{onChange:t,table:e})}},51742:(e,t,s)=>{"use strict";s.d(t,{QT:()=>v,wQ:()=>f,wu:()=>m});var r=s(98768),i=s(26659),n=s(6958),a=s(10901),l=s(50058),o=s(60343),d=s(24109),c=s(23416),u=s(35024),p=s(56937);function m(e){return e}let g=e=>{switch(e){case"left":return"items-left justify-start justify-items-start text-left";case"right":return"items-right justify-end justify-items-end text-right";default:return"items-center justify-center justify-items-center text-center"}},x=e=>{switch(e){case"overdue":case"upcoming":return"rounded-md [&>td:first-child]:rounded-l-lg [&>td:last-child]:rounded-r-lg";default:return""}},h=e=>{switch(e){case"overdue":return"destructive";case"upcoming":return"warning";default:return}};function f({columns:e,data:t,showToolbar:s=!0,className:m,pageSize:f=10,pageSizeOptions:v=[10,20,30,40,50],showPageSizeSelector:j=!0,onChange:b,rowStatus:y}){let[S,N]=o.useState([]),[w,D]=o.useState([]),[C,I]=o.useState({pageIndex:0,pageSize:f}),P=(0,l.k)(),T=o.useMemo(()=>e.filter(e=>e.showOnlyBelow?!P[e.showOnlyBelow]:!e.breakpoint||P[e.breakpoint]),[e,P]);o.useEffect(()=>{I(e=>({...e,pageSize:f}))},[f]);let $=(0,d.b7)({data:t,columns:T,onSortingChange:N,getCoreRowModel:(0,c.sC)(),getPaginationRowModel:(0,c.G_)(),getSortedRowModel:(0,c.tj)(),onColumnFiltersChange:D,getFilteredRowModel:(0,c.vL)(),onPaginationChange:I,state:{sorting:S,columnFilters:w,pagination:C}});return(0,r.jsxs)("div",{className:"space-y-4 pb-8",children:[s&&r.jsx(u.Zb,{className:"p-2 md:p-auto",children:r.jsx(n.n,{table:$,onChange:b})}),(0,r.jsxs)(i.iA,{className:m||"p-0 phablet:p-8 lg:p-6 xl:p-8 shadow-none border-0 phablet:border border-border bg-card rounded-lg",children:[$.getHeaderGroups().some(e=>e.headers.some(e=>e.column.columnDef.header&&""!==e.column.columnDef.header))&&r.jsx(i.xD,{children:$.getHeaderGroups().map(e=>r.jsx(i.SC,{children:e.headers.map(e=>{let t=e.column.columnDef,s="title"===e.column.id?"left":t.cellAlignment||"center";return r.jsx(i.ss,{className:"title"===e.column.id?"items-left justify-items-start text-left":g(s),children:e.isPlaceholder?null:(0,d.ie)(e.column.columnDef.header,e.getContext())},e.id)})},e.id))}),r.jsx(i.RM,{children:$.getRowModel().rows.length?$.getRowModel().rows.map(e=>{let t=y?y(e.original):"normal",s=x(t);return r.jsx(i.SC,{"data-state":e.getIsSelected()?"selected":void 0,className:(0,p.cn)("mb-4",s),children:e.getVisibleCells().map(e=>{let s=e.column.columnDef,n="title"===e.column.id?"left":s.cellAlignment||"center";return r.jsx(i.pj,{statusOverlay:"normal"!==t,statusOverlayColor:h(t),className:(0,p.cn)("","title"===e.column.id?`${T.length>1?"w-auto":"w-full"} items-left justify-items-start text-left`:g(n),s.cellClassName),children:r.jsx("div",{className:(0,p.cn)("flex px-1.5 xs:px-2.5 flex-1",g(n)),children:(0,d.ie)(e.column.columnDef.cell,e.getContext())})},e.id)})},String(e.id))}):r.jsx(i.SC,{children:r.jsx(i.pj,{colSpan:T.length,className:"h-24 text-center",children:"No results."})})})]}),($.getCanPreviousPage()||$.getCanNextPage())&&r.jsx("div",{className:"flex items-center justify-center phablet:justify-end space-x-2 py-4",children:r.jsx(a.k,{table:$,pageSizeOptions:v,showPageSizeSelector:j})})]})}let v=f},43692:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(98768);let i=({page:e=0,limit:t=0,onClick:s,totalCount:i=0,hasNextPage:n=!1,hasPreviousPage:a=!1,visiblePageCount:l=0})=>{let o=t>0?Math.ceil(i/t):0,d=t>0?l:0,c=e-d,u=e;c<0&&(c=0,u=d-1);let p=o-d,m=u+1!==p;u>=p&&(c=0,u=d-1),o<d&&(c=0,u=o-1);let g=Array.from({length:u-c+1},(e,t)=>c+t).slice(-d),x=Array.from({length:(t>0?Math.floor(i/t):0)-p+1},(e,t)=>p+t).slice(0,d);return x=(x=x.filter(e=>!g.includes(e))).filter(e=>e>=0),(g[g.length-1]+1===x[0]||g[g.length-1]-1===x[0]||x.length<=0)&&(m=!1),r.jsx("div",{className:"flex items-center justify-end p-4",children:r.jsx("nav",{"aria-label":"Log Entries pagination",children:(0,r.jsxs)("ul",{className:"inline-flex -space-x-px  h-10",children:[r.jsx("li",{children:a&&e>0&&r.jsx("button",{onClick:()=>s(0),className:" rounded-s-lg",children:"First"})}),r.jsx("li",{children:a&&r.jsx("button",{onClick:()=>s(e-1),className:"",children:"Previous"})}),Array.from({length:g.length},(e,t)=>r.jsx("li",{children:r.jsx("button",{onClick:()=>s(g[t]),className:"",children:g[t]+1})},t)),m&&r.jsx("li",{children:r.jsx("button",{onClick:()=>s(u+1),className:"flex items-center justify-center px-4 h-10 leading-tight  border   ",children:"..."})}),Array.from({length:x.length},(e,t)=>r.jsx("li",{children:r.jsx("button",{onClick:()=>s(x[t]),className:"",children:x[t]+1})},t)),r.jsx("li",{children:n&&r.jsx("button",{onClick:()=>s(e+1),className:"",children:"Next"})}),r.jsx("li",{children:n&&e*t<i&&r.jsx("button",{onClick:()=>s(o-1),className:" rounded-e-lg",children:"Last"})})]})})})}},12202:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\crew-training-upcoming-overdue-report\page.tsx#default`)},71238:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7380],()=>s(61458));module.exports=r})();