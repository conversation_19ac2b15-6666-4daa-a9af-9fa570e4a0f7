(()=>{var e={};e.id=7827,e.ids=[7827],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},47770:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>d,pages:()=>l,routeModule:()=>f,tree:()=>c}),r(73414),r(50681),r(78398),r(57757),r(48045);var n=r(40060),s=r(33581),a=r(57567),i=r.n(a),o=r(51650),u={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);r.d(t,u);let c=["",{children:["settings",{children:["maintenance",{children:["category",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,73414)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\maintenance\\category\\new\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50681)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\maintenance\\category\\new\\page.tsx"],d="/settings/maintenance/category/new/page",h={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/settings/maintenance/category/new/page",pathname:"/settings/maintenance/category/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},31941:(e,t,r)=>{Promise.resolve().then(r.bind(r,84313))},41509:(e,t,r)=>{Promise.resolve().then(r.bind(r,91038))},83179:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",r="minute",n="hour",s="week",a="month",i="quarter",o="year",u="date",c="Invalid Date",l=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,d=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,h=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},f="en",g={};g[f]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||"th")+"]"}};var p="$isDayjsObject",y=function(e){return e instanceof x||!(!e||!e[p])},m=function e(t,r,n){var s;if(!t)return f;if("string"==typeof t){var a=t.toLowerCase();g[a]&&(s=a),r&&(g[a]=r,s=a);var i=t.split("-");if(!s&&i.length>1)return e(i[0])}else{var o=t.name;g[o]=t,s=o}return!n&&s&&(f=s),s||!n&&f},v=function(e,t){if(y(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new x(r)},$={s:h,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+h(Math.floor(r/60),2,"0")+":"+h(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),s=t.clone().add(n,a),i=r-s<0,o=t.clone().add(n+(i?-1:1),a);return+(-(n+(r-s)/(i?s-o:o-s))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(c){return({M:a,y:o,w:s,d:"day",D:u,h:n,m:r,s:t,ms:e,Q:i})[c]||String(c||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};$.l=m,$.i=y,$.w=function(e,t){return v(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var x=function(){function h(e){this.$L=m(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[p]=!0}var f=h.prototype;return f.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if($.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(l);if(n){var s=n[2]-1||0,a=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,a)):new Date(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,a)}}return new Date(t)}(e),this.init()},f.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},f.$utils=function(){return $},f.isValid=function(){return this.$d.toString()!==c},f.isSame=function(e,t){var r=v(e);return this.startOf(t)<=r&&r<=this.endOf(t)},f.isAfter=function(e,t){return v(e)<this.startOf(t)},f.isBefore=function(e,t){return this.endOf(t)<v(e)},f.$g=function(e,t,r){return $.u(e)?this[t]:this.set(r,e)},f.unix=function(){return Math.floor(this.valueOf()/1e3)},f.valueOf=function(){return this.$d.getTime()},f.startOf=function(e,i){var c=this,l=!!$.u(i)||i,d=$.p(e),h=function(e,t){var r=$.w(c.$u?Date.UTC(c.$y,t,e):new Date(c.$y,t,e),c);return l?r:r.endOf("day")},f=function(e,t){return $.w(c.toDate()[e].apply(c.toDate("s"),(l?[0,0,0,0]:[23,59,59,999]).slice(t)),c)},g=this.$W,p=this.$M,y=this.$D,m="set"+(this.$u?"UTC":"");switch(d){case o:return l?h(1,0):h(31,11);case a:return l?h(1,p):h(0,p+1);case s:var v=this.$locale().weekStart||0,x=(g<v?g+7:g)-v;return h(l?y-x:y+(6-x),p);case"day":case u:return f(m+"Hours",0);case n:return f(m+"Minutes",1);case r:return f(m+"Seconds",2);case t:return f(m+"Milliseconds",3);default:return this.clone()}},f.endOf=function(e){return this.startOf(e,!1)},f.$set=function(s,i){var c,l=$.p(s),d="set"+(this.$u?"UTC":""),h=((c={}).day=d+"Date",c[u]=d+"Date",c[a]=d+"Month",c[o]=d+"FullYear",c[n]=d+"Hours",c[r]=d+"Minutes",c[t]=d+"Seconds",c[e]=d+"Milliseconds",c)[l],f="day"===l?this.$D+(i-this.$W):i;if(l===a||l===o){var g=this.clone().set(u,1);g.$d[h](f),g.init(),this.$d=g.set(u,Math.min(this.$D,g.daysInMonth())).$d}else h&&this.$d[h](f);return this.init(),this},f.set=function(e,t){return this.clone().$set(e,t)},f.get=function(e){return this[$.p(e)]()},f.add=function(e,i){var u,c=this;e=Number(e);var l=$.p(i),d=function(t){var r=v(c);return $.w(r.date(r.date()+Math.round(t*e)),c)};if(l===a)return this.set(a,this.$M+e);if(l===o)return this.set(o,this.$y+e);if("day"===l)return d(1);if(l===s)return d(7);var h=((u={})[r]=6e4,u[n]=36e5,u[t]=1e3,u)[l]||1,f=this.$d.getTime()+e*h;return $.w(f,this)},f.subtract=function(e,t){return this.add(-1*e,t)},f.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||c;var n=e||"YYYY-MM-DDTHH:mm:ssZ",s=$.z(this),a=this.$H,i=this.$m,o=this.$M,u=r.weekdays,l=r.months,h=r.meridiem,f=function(e,r,s,a){return e&&(e[r]||e(t,n))||s[r].slice(0,a)},g=function(e){return $.s(a%12||12,e,"0")},p=h||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n};return n.replace(d,function(e,n){return n||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return $.s(t.$y,4,"0");case"M":return o+1;case"MM":return $.s(o+1,2,"0");case"MMM":return f(r.monthsShort,o,l,3);case"MMMM":return f(l,o);case"D":return t.$D;case"DD":return $.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return f(r.weekdaysMin,t.$W,u,2);case"ddd":return f(r.weekdaysShort,t.$W,u,3);case"dddd":return u[t.$W];case"H":return String(a);case"HH":return $.s(a,2,"0");case"h":return g(1);case"hh":return g(2);case"a":return p(a,i,!0);case"A":return p(a,i,!1);case"m":return String(i);case"mm":return $.s(i,2,"0");case"s":return String(t.$s);case"ss":return $.s(t.$s,2,"0");case"SSS":return $.s(t.$ms,3,"0");case"Z":return s}return null}(e)||s.replace(":","")})},f.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},f.diff=function(e,u,c){var l,d=this,h=$.p(u),f=v(e),g=(f.utcOffset()-this.utcOffset())*6e4,p=this-f,y=function(){return $.m(d,f)};switch(h){case o:l=y()/12;break;case a:l=y();break;case i:l=y()/3;break;case s:l=(p-g)/6048e5;break;case"day":l=(p-g)/864e5;break;case n:l=p/36e5;break;case r:l=p/6e4;break;case t:l=p/1e3;break;default:l=p}return c?l:$.a(l)},f.daysInMonth=function(){return this.endOf(a).$D},f.$locale=function(){return g[this.$L]},f.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=m(e,t,!0);return n&&(r.$L=n),r},f.clone=function(){return $.w(this.$d,this)},f.toDate=function(){return new Date(this.valueOf())},f.toJSON=function(){return this.isValid()?this.toISOString():null},f.toISOString=function(){return this.$d.toISOString()},f.toString=function(){return this.$d.toUTCString()},h}(),M=x.prototype;return v.prototype=M,[["$ms",e],["$s",t],["$m",r],["$H",n],["$W","day"],["$M",a],["$y",o],["$D",u]].forEach(function(e){M[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),v.extend=function(e,t){return e.$i||(e(t,x,v),e.$i=!0),v},v.locale=m,v.isDayjs=y,v.unix=function(e){return v(1e3*e)},v.en=g[f],v.Ls=g,v.p={},v},e.exports=t()},18479:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,s=Array(n);++r<n;)s[r]=t(e[r],r,e);return s}},22060:(e,t,r)=>{var n=r(51858),s=r(18479),a=r(55813),i=r(15903),o=1/0,u=n?n.prototype:void 0,c=u?u.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(a(t))return s(t,e)+"";if(i(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-o?"-0":r}},15903:(e,t,r)=>{var n=r(55296),s=r(48377);e.exports=function(e){return"symbol"==typeof e||s(e)&&"[object Symbol]"==n(e)}},16266:(e,t,r)=>{var n=r(22060);e.exports=function(e){return null==e?"":n(e)}},3233:(e,t,r)=>{var n=r(16266),s=0;e.exports=function(e){var t=++s;return n(e)+t}},84313:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(98768);r(60343);var s=r(64837);function a({children:e}){return n.jsx(s.Z,{children:e})}},91038:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var n=r(98768);r(60343);var s=r(72548),a=r(76342),i=r(69424);r(46776);var o=r(71890),u=r(78965),c=r(50088),l=r(25394);function d(){let e=(0,i.useRouter)(),t=async()=>{let e=document.getElementById("category-name")?.value,t=document.getElementById("category-abbr")?.value;if(""!==e)return await r({variables:{input:{name:e,abbreviation:t}}})},[r,{loading:d}]=(0,s.D)(a.xak,{onCompleted:t=>{t.createMaintenanceCategory.id>0?e.back():console.error("mutationcreateMaintenanceCategory error",t)},onError:e=>{console.error("mutationcreateMaintenanceCategory error",e)}});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(l.Zb,{className:"mb-2",children:[n.jsx(l.Ol,{children:n.jsx(l.ll,{children:n.jsx(l.H3,{children:"Create Maintenance Category"})})}),n.jsx(l.aY,{children:n.jsx("div",{className:"grid grid-cols-2",children:(0,n.jsxs)("div",{className:"flex flex-col gap-4",children:[n.jsx(l.__,{label:"Category name",children:n.jsx(o.I,{id:"category-name",type:"text",placeholder:"Category name"})}),n.jsx(l.__,{label:"Abbreviation",children:n.jsx(o.I,{id:"category-abbr",type:"text",placeholder:"Abbreviation"})})]})})})]}),(0,n.jsxs)(u.V,{children:[n.jsx(l.zx,{variant:"back",onClick:()=>e.push("/settings/maintenance/category"),children:"Back"}),n.jsx(c.Z,{text:"Create Category",type:"primary",icon:"check",color:"sky",action:t})]})]})}function h(){return n.jsx(d,{})}},50681:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\layout.tsx#default`)},73414:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\maintenance\category\new\page.tsx#default`)},84961:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},46020:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},23379:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},7671:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[864,8865,3563,6263,8189,9507,7602,6451,4234,2925,5394,4837,6342,88],()=>r(47770));module.exports=n})();