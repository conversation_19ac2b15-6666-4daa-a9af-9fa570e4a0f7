(()=>{var e={};e.id=5243,e.ids=[5243],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},91391:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),s(57626),s(71238),s(78398),s(57757),s(48045);var r=s(40060),a=s(33581),i=s(57567),o=s.n(i),n=s(51650),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c=["",{children:["reporting",{children:["service-report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,57626)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\service-report\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\service-report\\page.tsx"],u="/reporting/service-report/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/reporting/service-report/page",pathname:"/reporting/service-report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88332:(e,t,s)=>{Promise.resolve().then(s.bind(s,44517))},9714:(e,t,s)=>{Promise.resolve().then(s.bind(s,59462))},91973:(e,t,s)=>{"use strict";s.d(t,{u:()=>r});let r=(e,t="report.csv")=>{let s=new Blob([e.map(e=>e.join(",")).join("\n")],{type:"text/csv;charset=utf-8,"}),r=URL.createObjectURL(s);Object.assign(document.createElement("a"),{href:r,download:t}).click(),URL.revokeObjectURL(r)}},2604:(e,t,s)=>{"use strict";s.d(t,{S:()=>o});var r=s(9707),a=s(58774),i=s.n(a);function o(e,t){let s=new r.default(t);i()(s,{head:e.headers,body:e.body,foot:e.footers,margin:5,...e.userOptions}),s.save(e.fileName||"report.pdf")}},44517:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(98768);s(60343);var a=s(32993),i=s(64837);function o({children:e}){return r.jsx(a.default,{children:r.jsx(i.Z,{children:e})})}},59462:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(98768);s(46776);var a=s(26100),i=s(60343),o=s(37042),n=s(79418),l=s(69424),c=s(17203),d=s(39544),u=s(49517),m=s(26659),p=s(88006),g=s(10706),x=s(91973),v=s(2604),h=s(85114),y=s(25394),j=s(35024);let f=["Vessel Name","Voyage (days)","Available for Voyage (days)","Out of Service (days)"],S=e=>e.reduce((e,t)=>{let{onVoyage:s,availableForVoyage:r,outOfService:a}=e;return{onVoyage:s+=t.statusSummary.onVoyage,availableForVoyage:r+=t.statusSummary.availableForVoyage,outOfService:a+=t.statusSummary.outOfService}},{onVoyage:0,availableForVoyage:0,outOfService:0}),b=e=>{let t=e.onVoyage+e.availableForVoyage,s=e.onVoyage+e.availableForVoyage+e.outOfService;return 0===s?0:parseFloat((t/s*100).toFixed(2))};function V(){let e=(0,l.useRouter)(),[t,s]=(0,i.useState)("summary"),[a,V]=(0,i.useState)([]),[w,D]=(0,i.useState)([]),[q,F]=(0,i.useState)({startDate:new Date,endDate:new Date}),[P,{called:M,loading:L}]=(0,n.t)(p.p1,{fetchPolicy:"cache-and-network",onError:e=>{console.error("queryLogBookEntrySections error",e)}}),[C]=(0,n.t)(p.j9,{fetchPolicy:"cache-and-network",onError:e=>{console.error("queryLogBookEntrySections error",e)}}),k=async()=>{let e={},t={};if(null!==q.startDate&&null!==q.endDate)e.statusFilter={date:{gte:q.startDate,lte:q.endDate}};else{V([]);return}w.length>0&&(t={id:{in:w.map(e=>e.value)},filter:{archived:{eq:!1}}},e.vesselFilter=t);let{data:s}=await P({variables:e}),{data:r}=await C({variables:{vesselFilter:t,statusFilter:{date:{lte:q.startDate}}}}),a=s?.readVessels?.nodes??[];if(0===a.length){V([]);return}let i=r?.readVessels?.nodes??[];V(a.map(e=>{let t=e.statusHistory.nodes.map(e=>({id:e.id,date:new Date(e.date),status:e.status})),s=i.filter(t=>t.id==e.id).shift(),r=s?.statusHistory?.nodes??[],a=(0,h.O)(r);return{vesselID:e.id,vesselName:e.title,statusHistories:t,startingStatus:a,statusSummary:(0,h.D)(t,a,q.startDate,q.endDate)}}))},R=(0,i.useMemo)(()=>S(a),[a]);return(0,r.jsxs)(r.Fragment,{children:[r.jsx(y.Bu,{title:"Service Report",actions:r.jsx(d.Button,{variant:"back",iconLeft:c.Z,onClick:()=>e.push("/reporting"),children:"Back"})}),(0,r.jsxs)(j.Zb,{className:"mt-8",children:[r.jsx(j.Ol,{children:r.jsx(y.mQ,{value:t,onValueChange:e=>s(e),children:(0,r.jsxs)(y.dr,{children:[r.jsx(y.SP,{value:"summary",children:"Summarized Report"}),r.jsx(y.SP,{value:"detailed",children:"Detailed Report"})]})})}),(0,r.jsxs)(j.aY,{className:"flex flex-col gap-4",children:[r.jsx(o.Z,{onChange:({type:e,data:t})=>{switch(e){case"dateRange":F(t);break;case"vessels":D(t)}},onClick:k}),r.jsx(u.Z,{onDownloadPdf:()=>{if(0===a.length)return;let e=a.map(function(e){return[e.vesselName+"",e.statusSummary.onVoyage.toLocaleString(),e.statusSummary.availableForVoyage.toLocaleString(),e.statusSummary.outOfService.toLocaleString()]});(0,v.S)({headers:[[{content:"Vessel"},{content:"On Voyage (days)"},{content:"Available for Voyage (days)"},{content:"Out of Service (days)"}]],body:e,footers:[[{content:"Total"},{content:R.onVoyage.toLocaleString()},{content:R.availableForVoyage.toLocaleString()},{content:R.outOfService.toLocaleString()}],[{content:"In Service"},{content:`${b(R)}%`},{content:""},{content:""}]],userOptions:{showFoot:"lastPage"}})},onDownloadCsv:()=>{if(0===a.length)return;let e=[];e.push(["vessel name","voyage","available for voyage","out of service"]),a.forEach(t=>{e.push([t.vesselName,t.statusSummary.onVoyage,t.statusSummary.availableForVoyage,t.statusSummary.outOfService])}),(0,x.u)(e)}}),"summary"===t&&(0,r.jsxs)(m.iA,{children:[r.jsx(m.xD,{children:r.jsx(m.SC,{children:f.map(e=>r.jsx(m.ss,{children:e},e))})}),r.jsx(N,{data:a,isLoading:M&&L}),M&&L?r.jsx(r.Fragment,{}):r.jsx(O,{summary:R})]}),"detailed"===t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid md:grid-cols-4",children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[r.jsx("div",{className:"font-medium",children:"Vessels in Service:"}),(0,r.jsxs)("div",{children:[b(R),"%"]})]}),(0,r.jsxs)("div",{className:"grid gap-1",children:[r.jsx("div",{className:"font-medium",children:"On voyage:"}),(0,r.jsxs)("div",{children:[R.onVoyage," days"]})]}),(0,r.jsxs)("div",{className:"grid gap-1",children:[r.jsx("div",{className:"font-medium",children:"Available for voyage:"}),(0,r.jsxs)("div",{children:[R.availableForVoyage," ","days"]})]}),(0,r.jsxs)("div",{className:"grid gap-1",children:[r.jsx("div",{className:"font-medium",children:"Out of Service:"}),(0,r.jsxs)("div",{children:[R.outOfService," days"]})]})]}),a.map(e=>(0,r.jsxs)("div",{className:"bg-[white] shadow rounded-lg border border-border p-4",children:[(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-4 mb-3 items-center",children:[r.jsx("div",{children:r.jsx("h3",{className:"font-semibold text-lg",children:e.vesselName})}),r.jsx("div",{className:"md:col-span-2",children:(0,r.jsxs)("div",{className:"grid md:grid-cols-3",children:[(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium",children:"On voyage:"})," ",e.statusSummary.onVoyage," ","days"]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium",children:"Available for voyage:"})," ",e.statusSummary.availableForVoyage," ","days"]}),(0,r.jsxs)("div",{children:[r.jsx("span",{className:"font-medium",children:"Out of Service:"})," ",e.statusSummary.outOfService," ","days"]})]})})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-2 p-2 border-t border-border",children:[0===e.statusHistories.filter(e=>g.WU(e.date,"dd/MM/yy")===g.WU(q.startDate,"dd/MM/yy")).length&&(0,r.jsxs)("div",{className:"flex gap-4",children:[r.jsx("div",{children:g.WU(q.startDate,"dd/MM/yy")}),r.jsx("div",{children:e.startingStatus.replace(/[A-Z]/g," $&").slice(1)})]}),e.statusHistories.map(e=>(0,r.jsxs)("div",{className:"flex gap-4",children:[r.jsx("div",{children:g.WU(e.date,"dd/MM/yy")}),r.jsx("div",{children:e.status.replace(/[A-Z]/g," $&").slice(1)})]},e.id))]})]},e.vesselID))]})]})]})]})}let N=({data:e,isLoading:t})=>t?r.jsx(m.RM,{children:r.jsx(m.SC,{children:r.jsx(m.pj,{colSpan:f.length,className:"text-center",children:"Loading..."})})}):0===e.length?r.jsx(m.RM,{children:r.jsx(m.SC,{children:r.jsx(m.pj,{colSpan:f.length,className:"text-center",children:"No Data Found"})})}):r.jsx(m.RM,{children:e.map(e=>(0,r.jsxs)(m.SC,{children:[r.jsx(m.pj,{children:e.vesselName}),r.jsx(m.pj,{className:"text-center",children:e.statusSummary.onVoyage}),r.jsx(m.pj,{className:"text-center",children:e.statusSummary.availableForVoyage}),r.jsx(m.pj,{className:"text-center",children:e.statusSummary.outOfService})]},e.vesselID))}),O=({summary:e})=>(0,r.jsxs)(m.yt,{children:[(0,r.jsxs)(m.SC,{children:[r.jsx(m.pj,{children:"Total"}),r.jsx(m.pj,{className:"text-center",children:e.onVoyage}),r.jsx(m.pj,{className:"text-center",children:e.availableForVoyage}),r.jsx(m.pj,{className:"text-center",children:e.outOfService})]}),(0,r.jsxs)(m.SC,{children:[r.jsx(m.pj,{children:"Vessels in Service"}),(0,r.jsxs)(m.pj,{className:"text-center",children:[b(e),"%"]}),r.jsx(m.pj,{colSpan:2})]})]});function w(){let[e,t]=(0,i.useState)(!1),[s,o]=(0,i.useState)(!1);return e&&s?r.jsx(V,{}):e?r.jsx(a.Z,{errorMessage:"Oops You do not have the permission to view this section."}):r.jsx(a.Z,{})}},49517:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var r=s(98768),a=s(39544),i=s(76915);function o({onDownloadCsv:e,onDownloadPdf:t}){return(0,r.jsxs)("div",{className:"flex gap-3 mb-3",children:[e&&r.jsx(a.Button,{iconLeft:i.Z,type:"button",onClick:e,children:"Download CSV"}),t&&r.jsx(a.Button,{iconLeft:i.Z,type:"button",onClick:t,children:"Download PDF"})]})}},85114:(e,t,s)=>{"use strict";s.d(t,{D:()=>o,O:()=>i});var r=s(54110),a=s(10706);let i=e=>{let t=[...e].sort((e,t)=>new Date(t.created??t.date).getTime()-new Date(e.created??e.date).getTime()).shift();return t?.status??"AvailableForVoyage"},o=(e,t,s,i)=>{if(0===e.length){let e=i,a=new Date;r.w(i,a)>0&&(e=a);let o=r.w(e,s)+1;return{onVoyage:"OnVoyage"===t?o:0,availableForVoyage:"AvailableForVoyage"===t?o:0,outOfService:"OutOfService"===t?o:0}}let o="OnVoyage"===t?1:0,n="AvailableForVoyage"===t?1:0,l="OutOfService"===t?1:0,c=null,d=null,u=e[0];if(a.WU(u.date,"yyyy-MM-dd")!==a.WU(s,"yyyy-MM-dd")&&(c=s,d=t),e.forEach(e=>{if(null===c){c=e.date,d=e.status;return}let t=r.w(e.date,c);if(0!==t)switch(d){case"OnVoyage":o+=t;break;case"AvailableForVoyage":n+=t;break;case"OutOfService":l+=t}c=e.date,d=e.status}),r.w(i,c)>0){let e=i,t=new Date;r.w(i,t)>0&&(e=t);let s=r.w(e,c);switch(d){case"OnVoyage":o+=s;break;case"AvailableForVoyage":n+=s;break;case"OutOfService":l+=s}}return{onVoyage:o,availableForVoyage:n,outOfService:l}}},71238:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)},57626:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\service-report\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,9707,6250,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042],()=>s(91391));module.exports=r})();