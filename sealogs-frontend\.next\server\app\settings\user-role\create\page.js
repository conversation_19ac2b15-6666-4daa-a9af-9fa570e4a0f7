(()=>{var e={};e.id=2663,e.ids=[2663],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},76:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d}),r(88169),r(50681),r(78398),r(57757),r(48045);var t=r(40060),a=r(33581),o=r(57567),l=r.n(o),i=r(51650),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);r.d(s,n);let d=["",{children:["settings",{children:["user-role",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,88169)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\user-role\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50681)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\user-role\\create\\page.tsx"],p="/settings/user-role/create/page",u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/settings/user-role/create/page",pathname:"/settings/user-role/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},31941:(e,s,r)=>{Promise.resolve().then(r.bind(r,84313))},28893:(e,s,r)=>{Promise.resolve().then(r.bind(r,61791))},84313:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(98768);r(60343);var a=r(64837);function o({children:e}){return t.jsx(a.Z,{children:e})}},61791:(e,s,r)=>{"use strict";r.d(s,{default:()=>y});var t=r(98768),a=r(69424),o=r(60343),l=r(14826),i=r.n(l),n=r(7678),d=r.n(n),c=r(72548),p=r(79418),u=r(26100),m=r(94060),x=r(76342),g=r(46776),h=r(71890),f=r(60797),v=r(78965),j=r(50088),b=r(25394);let y=({roleID:e=0})=>{let[s,r]=(0,o.useState)(-1),l=(0,a.useRouter)(),[n,y]=(0,o.useState)({}),[P,N]=(0,o.useState)({}),[C,S]=(0,o.useState)([]),[w,_]=(0,o.useState)(!0),[E,L]=(0,o.useState)([]),[k,M]=(0,o.useState)(!1);(0,o.useEffect)(()=>{r((0,g.GJ)())},[]);let T=s=>{let{name:r,value:t}=s.target;y({...n,id:e,[r]:t})},[R,{loading:G}]=(0,c.D)(x.EuY,{onCompleted:e=>{e.createSeaLogsGroup&&l.push("/settings/user-role")},onError:e=>{N({...P,response:e.message})}}),[U,{loading:q}]=(0,c.D)(x.KZM,{onCompleted:e=>{e.updateSeaLogsGroup&&l.push("/settings/user-role")},onError:e=>{N({...P,response:e.message})}}),V=async()=>{if(k)return;if(d()(i()(n?.title))){N({...P,title:"Title is required"});return}N({});let s={...n};delete s.__typename,delete s.permissions,delete s.members,s.permissionCodes=E.join(","),e>0?await U({variables:{input:s}}):await R({variables:{input:s}})},[B,{loading:D}]=(0,p.t)(m.Wu,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readOneSeaLogsGroup;s&&(L(s.permissions.nodes.map(e=>e.code)),y(s),M("admin"===s.code))},onError:e=>{console.error("readOneSealLogsGroup error",e)}}),Z=async()=>{await B({variables:{id:e}})},[O,{loading:A}]=(0,p.t)(m.F5,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readPermissionTypes;if(s){let e=s.reduce((e,s)=>{let{category:r,...t}=s;return e[r]=e[r]||[],e[r].push(t),e[r].sort((e,s)=>e.sort>s.sort),e},{});console.info("permissions",e),S(e)}},onError:e=>{console.error("readPermissionTypes error",e)}}),F=async()=>{await O()},H=(e,s)=>{L(r=>e?[...r,s]:r.filter(e=>e!==s))};return((0,o.useEffect)(()=>{e>0&&Z()},[e]),(0,o.useEffect)(()=>{w&&((0,g.UU)(),F(),_(!1))},[w]),(0,o.useEffect)(()=>{0==e&&L(Object.values(C).flatMap(e=>e.filter(e=>!0===e.default).map(e=>e.code)))},[C]),!1===s)?t.jsx(u.Z,{errorMessage:"Oops You do not have the permission to view this section."}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(b.Zb,{className:"mb-4",children:[t.jsx(b.ll,{children:(0,t.jsxs)(b.H3,{children:[e>0?"Edit":"New"," User Role"]})}),(0,t.jsxs)(b.aY,{className:"flex flex-col gap-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3",children:[t.jsx(b.H3,{className:"text-lg",children:"Role Details"}),(0,t.jsxs)("div",{className:"flex flex-col gap-4 md:col-span-2",children:[P?.response&&t.jsx(b.bZ,{variant:"destructive",children:P?.response}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)(f.Label,{label:"Title",children:[t.jsx(h.I,{name:"title",type:"text",value:n?.title??"",placeholder:"Title",onChange:T,disabled:k}),t.jsx("small",{className:"text-red-500",children:P?.title})]}),t.jsx(f.Label,{label:"Code",children:t.jsx(h.I,{name:"code",type:"text",value:n?.code??"",placeholder:"Code",onChange:T,disabled:k})})]}),t.jsx(f.Label,{label:"Description",children:t.jsx(b.gx,{name:"description",value:n?.description??"",placeholder:"Description",onChange:T,rows:5,disabled:k})})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-3",children:[t.jsx(b.H3,{className:"text-lg",children:"Permissions"}),t.jsx("div",{className:"flex flex-col gap-2 md:col-span-2",children:Object.keys(C).map(e=>(0,t.jsxs)("div",{className:"flex flex-col border rounded-lg p-3 border-secondary",children:[t.jsx(b.P,{className:"font-medium mb-4",children:e}),t.jsx("div",{className:"flex flex-col gap-2.5",children:C[e].map(e=>(0,t.jsxs)("div",{className:"items-top flex space-x-2",children:[t.jsx(b.XZ,{id:e.code,value:e.code,checked:E.includes(e.code),onCheckedChange:s=>H(!0===s,e.code)}),(0,t.jsxs)("div",{className:"grid gap-1.5 leading-none",children:[t.jsx("label",{htmlFor:e.code,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:e.name}),e.help&&t.jsx("p",{className:"text-sm text-muted-foreground",children:e.help})]})]},e.code))})]},e))})]})]})]}),(0,t.jsxs)(v.V,{children:[t.jsx(b.zx,{variant:"back",onClick:()=>l.back(),disabled:G||D||q,children:"Cancel"}),!k&&t.jsx(j.Z,{text:`${e>0?"Update":"Create"} Role`,type:"primary",icon:"check",color:"sky",action:V,isDisabled:G||D||q})]})]})}},50681:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\layout.tsx#default`)},88169:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(96141);let a=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\user-role\form.tsx#default`),o=()=>t.jsx(a,{roleID:0})}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[864,8865,3563,6263,8189,9507,7602,9414,6451,4234,2925,5394,4837,6342,88],()=>r(76));module.exports=t})();