(()=>{var e={};e.id=4354,e.ids=[4354],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71408:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>g,tree:()=>c}),s(81754),s(74399),s(78398),s(57757),s(48045);var t=s(40060),i=s(33581),a=s(57567),n=s.n(a),l=s(51650),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(r,o);let c=["",{children:["crew-training",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,81754)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew-training\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,74399)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew-training\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew-training\\page.tsx"],u="/crew-training/page",m={require:s,loadChunk:()=>Promise.resolve()},g=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/crew-training/page",pathname:"/crew-training",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},93854:(e,r,s)=>{Promise.resolve().then(s.bind(s,30261))},8431:(e,r,s)=>{Promise.resolve().then(s.bind(s,78176))},30261:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(98768);s(60343);var i=s(64837);function a({children:e}){return t.jsx(i.Z,{children:e})}},78176:(e,r,s)=>{"use strict";s.d(r,{default:()=>Q});var t,i=s(98768),a=s(60343),n=s(59445),l=s(23160),o=s(29428),c=s(39650),d=s(13006),u=s(79418),m=s(94060),g=s(66263),p=s(10706),x=s(25394),v=s(60797),f=s(60336),y=s(99891),h=s(56937),b=s(51742),D=s(30905),j=s(13842);!function(e){e[e.OVERDUE=1]="OVERDUE",e[e.UPCOMING=2]="UPCOMING",e[e.COMPLETED=3]="COMPLETED"}(t||(t={}));let N=(e,r)=>{if(!e||!Array.isArray(e))return[];try{return e.map(e=>{let s=e.vessel||{id:0,title:"Unknown"};if(r&&e.vessel?.id)try{(s=r(e.vessel.id,e.vessel))&&"object"==typeof s||(s=e.vessel),e.vessel.position&&!s.position&&(s.position=e.vessel.position),e.trainingLocationType&&!s.trainingLocationType&&(s.trainingLocationType=e.trainingLocationType)}catch(r){console.warn("Failed to enhance vessel data for training:",e.id,r),s=e.vessel}let t=(e.members?.nodes||[]).reduce((e,r)=>{let s=e.find(e=>e.id===r.id);return s?(s.firstName=r.firstName||s.firstName,s.surname=r.surname||s.surname,s.email=r.email||s.email):e.push({id:r.id,firstName:r.firstName||"",surname:r.surname||"",email:r.email||"",...r}),e},[]);return{id:e.id,dueDate:e.date,vesselID:e.vessel?.id||0,vessel:s,trainingTypeID:e.trainingTypes?.nodes?.[0]?.id||0,trainingType:e.trainingTypes?.nodes?.[0]||{id:0,title:"Unknown"},members:t,status:{label:"Completed",isOverdue:!1,class:"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center",dueWithinSevenDays:!1},category:"completed",originalData:e}})}catch(e){return console.error("Error transforming completed training data:",e),[]}},T=e=>{if(!e||!Array.isArray(e))return[];try{let r=e.filter(e=>e&&e.memberID&&e.vesselID?(e.vessel?.seaLogsMembers?.nodes?.some(r=>r&&r.id===e.memberID),!0):(console.warn("\uD83D\uDD0D [crew-training-utils] Skipping invalid item:",e),!1)).map(e=>{try{let r=(0,j.nu)(e);return{...e,status:r}}catch(r){return console.error("\uD83D\uDD0D [crew-training-utils] Error calculating status for:",e,r),{...e,status:{class:"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center",label:"Unknown",isOverdue:!1,dueWithinSevenDays:!1}}}}).reduce((e,r)=>{try{let s=`${r.vesselID||0}-${r.trainingTypeID||0}-${r.dueDate||"unknown"}`;return e[s]||(e[s]={id:r.id,vesselID:r.vesselID||0,vessel:r.vessel||{id:0,title:"Unknown"},trainingTypeID:r.trainingTypeID||0,trainingType:r.trainingType||{id:0,title:"Unknown"},dueDate:r.dueDate,status:r.status,trainingLocationType:r.trainingSession?.trainingLocationType,members:[]}),r.member&&e[s].members.push(r.member),e}catch(s){return console.error("\uD83D\uDD0D [crew-training-utils] Error grouping due:",r,s),e}},{});return Object.values(r).map(e=>{let r=new Map;e.members.forEach(e=>{if(e&&e.id){let s=r.get(e.id);s?r.set(e.id,{...s,firstName:e.firstName||s.firstName,surname:e.surname||s.surname,email:e.email||s.email,...e}):r.set(e.id,{id:e.id,firstName:e.firstName||"",surname:e.surname||"",email:e.email||"",...e})}else console.warn("Invalid member:",e)});let s=Array.from(r.values());try{let r;e.status?.isOverdue?r="overdue":(e.status?.dueWithinSevenDays,r="upcoming");let t={...e.vessel||{id:0,title:"Unknown"},...e.trainingLocationType&&{trainingLocationType:e.trainingLocationType}};return{id:e.id,dueDate:e.dueDate,vesselID:e.vesselID,vessel:t,trainingTypeID:e.trainingTypeID,trainingType:e.trainingType||{id:0,title:"Unknown"},members:s,status:e.status,category:r,originalData:e}}catch(r){return console.error("Error creating unified record:",r,"for group:",e),null}}).filter(Boolean)}catch(e){return console.error("Error in transformTrainingSessionDuesToUnifiedFormat:",e),[]}},w=e=>{switch(e.category){case"overdue":return 1;case"upcoming":return 2;default:return 3}},I=e=>{if(!e||!Array.isArray(e))return[];try{return e.sort((e,r)=>{try{let s=w(e)-w(r);if(0!==s)return s;let t=e.dueDate?new Date(e.dueDate).getTime():0,i=r.dueDate?new Date(r.dueDate).getTime():0;if(isNaN(t)&&isNaN(i))return 0;if(isNaN(t))return 1;if(isNaN(i))return -1;if("overdue"===e.category||"upcoming"===e.category)return t-i;return i-t}catch(s){return console.error("\uD83D\uDD0D [crew-training-utils] Error comparing items in sort:",s,{a:e,b:r}),0}})}catch(r){return console.error("\uD83D\uDD0D [crew-training-utils] Error in sorting:",r),e}},C=({trainingSessionDues:e=[],completedTrainingList:r=[],getVesselWithIcon:s,includeCompleted:t=!0,debug:i=!1})=>{try{let a=T(e),n=t?N(r,s):[],l=[...a,...n],o=I(l);return i&&q(o,"Final Merged Training Data"),o}catch(e){return console.error("Error merging and sorting crew training data:",e),[]}},q=(e,r="Training Data")=>{};var E=s(96268),A=s(50058);let k=e=>{if(!e)return"";try{let r=new Date(e);return(0,p.WU)(r,"dd/MM/yy")}catch{return""}},M=e=>e.status.isOverdue?"overdue":e.status.dueWithinSevenDays?"upcoming":"normal",S=e=>e.status.isOverdue?"text-destructive/80 hover:text-destructive":e.status.dueWithinSevenDays?"text-warning/80 hover:text-warning":"hover:text-curious-blue-400",_=({data:e})=>{let r=(0,A.k)(),s="completed"===e.category,t="overdue"===e.category,a=e.members||[],n=e.trainingType?.title||"";return(0,i.jsxs)("div",{className:"w-full space-y-2.5 tablet-md:border-none py-3",children:[(0,i.jsxs)("div",{className:"flex-1 flex flex-wrap justify-between items-center",children:[s?i.jsx(g.default,{href:`/crew-training/info?id=${e.id}`,className:"font-semibold text-base hover:text-primary",children:k(e.dueDate)}):i.jsx("div",{className:(0,h.cn)("font-semibold text-base",S(e)),children:k(e.dueDate)}),!r.landscape&&i.jsx(x.OE,{isOverdue:e.status.isOverdue,isUpcoming:e.status.dueWithinSevenDays,label:e.status.label||e.category})]}),!r.laptop&&i.jsx("div",{className:"text-sm line-clamp-2",children:s&&e.originalData?.trainingTypes?.nodes?e.originalData.trainingTypes.nodes.map(e=>e.title).join(", "):n}),!r["tablet-md"]&&(0,i.jsxs)("div",{className:"flex flex-wrap gap-2.5 items-center",children:[i.jsx(v.Label,{className:"text-sm m-0 text-muted-foreground",children:s?"Location:":"Vessel:"}),(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[e.vessel&&i.jsx("div",{className:"size-9 flex items-center justify-center flex-shrink-0 [&_img]:!size-8 [&_svg]:!size-8",children:i.jsx(y.Z,{vessel:e.vessel})}),i.jsx(f.F,{vessel:e.vessel,iconClassName:"size-8"})]})]}),!r["tablet-lg"]&&(0,i.jsxs)("div",{className:"flex gap-1",children:[a.slice(0,r["tablet-md"]?8:6).map(e=>(0,i.jsxs)(x.u,{children:[i.jsx(x.aJ,{children:i.jsx(x.qE,{size:"sm",variant:!s&&t?"destructive":"secondary",children:i.jsx(x.Q5,{className:"text-sm",children:(0,x.xE)(e.firstName,e.surname)})})}),(0,i.jsxs)(x._v,{children:[e.firstName," ",e.surname??""]})]},e.id)),a.length>(r["tablet-md"]?8:6)&&(0,i.jsxs)(x.J2,{children:[i.jsx(x.CM,{className:"w-fit",asChild:!0,children:(0,i.jsxs)(x.zx,{variant:"outline",size:"sm",className:"w-fit h-8",children:["+",a.length-(r["tablet-md"]?8:6)," ","more"]})}),i.jsx(x.yk,{className:"w-64",children:i.jsx("div",{className:"p-3 max-h-64 overflow-auto",children:i.jsx("div",{className:"space-y-2",children:a.slice(r["tablet-md"]?8:6).map(e=>(0,i.jsxs)("div",{className:"text-sm flex items-center gap-2",children:[i.jsx(x.qE,{size:"xs",variant:"secondary",children:i.jsx(x.Q5,{className:"text-xs",children:(0,x.xE)(e.firstName,e.surname)})}),`${e.firstName??""} ${e.surname??""}`]},e.id))})})})]})]})]})},P=({trainingSessionDues:e=[],completedTrainingList:r=[],unifiedData:s,getVesselWithIcon:t,includeCompleted:n=!0,memberId:l,isVesselView:o=!1,showToolbar:c=!1,pageSize:d})=>{let u=(0,E.ac)("(min-width: 720px)"),m=(0,a.useMemo)(()=>s&&Array.isArray(s)?s:C({trainingSessionDues:e,completedTrainingList:r,getVesselWithIcon:t,includeCompleted:n}),[s,e,r,t,n]),g=m.some(e=>"overdue"===e.category||"upcoming"===e.category),p=m.some(e=>"completed"===e.category),v=()=>[{accessorKey:"title",header:({column:e})=>i.jsx(D.u,{column:e,title:"Date"}),cellAlignment:"left",cellClassName:"w-ful xs:w-auto",cell:({row:e})=>{let r=e.original;return i.jsx(_,{data:r})},sortingFn:(e,r)=>{let s=e.original,t=r.original,i="overdue"===s.category?1:"upcoming"===s.category?2:3,a="overdue"===t.category?1:"upcoming"===t.category?2:3;if(i!==a)return i-a;let n=new Date(s.dueDate).getTime(),l=new Date(t.dueDate).getTime();return"completed"===s.category?l-n:n-l}},{accessorKey:"trainingType",cellAlignment:"left",header:({column:e})=>i.jsx(D.u,{column:e,title:"Training/drill"}),breakpoint:"laptop",cell:({row:e})=>{let r=e.original,s="completed"===r.category;return i.jsx(x.P,{children:s&&r.originalData?.trainingTypes?.nodes?r.originalData.trainingTypes.nodes.map(e=>e.title).join(", "):r.trainingType?.title||""})},sortingFn:(e,r)=>{let s=e?.original?.originalData?.trainingTypes?.nodes?.[0]?.title||e?.original?.trainingType?.title||"",t=r?.original?.originalData?.trainingTypes?.nodes?.[0]?.title||r?.original?.trainingType?.title||"";return s.localeCompare(t)}},{accessorKey:"vessel",cellAlignment:"left",header:({column:e})=>i.jsx(D.u,{column:e,title:o?"":"Where"}),breakpoint:"tablet-md",cell:({row:e})=>{let r=e.original;return o?i.jsx("div",{}):(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[r.vessel&&i.jsx("div",{className:"size-9 flex items-center justify-center flex-shrink-0 [&_img]:!size-8 [&_svg]:!size-8",children:i.jsx(y.Z,{vessel:r.vessel})}),i.jsx("span",{className:"text-sm hidden phablet:block text-nowrap",children:r.vessel?.title||r.originalData?.trainingLocationType||""}),i.jsx(f.F,{vessel:r.vessel,iconClassName:"size-8"})]})},sortingFn:(e,r)=>{let s=e?.original?.vessel?.title||"",t=r?.original?.vessel?.title||"";return s.localeCompare(t)}},{accessorKey:"crew",cellAlignment:"left",header:({column:e})=>i.jsx(D.u,{column:e,title:"Who"}),breakpoint:"tablet-lg",cell:({row:e})=>{let r=e.original,s=r.originalData?.members?.nodes||r.members||[];return u?(0,i.jsxs)("div",{className:"flex gap-1 items-center",children:[s.slice(0,3).map((e,s)=>(0,i.jsxs)(x.u,{children:[i.jsx(x.aJ,{children:i.jsx(x.qE,{size:"sm",variant:r.status.isOverdue?"destructive":"secondary",children:i.jsx(x.Q5,{className:"text-sm",children:(0,x.xE)(e.firstName,e.surname)})})}),(0,i.jsxs)(x._v,{children:[e.firstName," ",e.surname??""]})]},e.id||s)),s.length>3&&(0,i.jsxs)(x.J2,{children:[i.jsx(x.CM,{asChild:!0,children:(0,i.jsxs)(x.zx,{variant:"outline",size:"sm",className:"h-8 px-2 text-xs",children:["+",s.length-3," more"]})}),i.jsx(x.yk,{className:"w-64",children:i.jsx("div",{className:"p-3 max-h-64 overflow-auto",children:i.jsx("div",{className:"space-y-2",children:s.slice(3).map(e=>(0,i.jsxs)("div",{className:"text-sm flex items-center gap-2",children:[i.jsx(x.qE,{size:"xs",variant:"secondary",children:i.jsx(x.Q5,{className:"text-xs",children:(0,x.xE)(e.firstName,e.surname)})}),`${e.firstName??""} ${e.surname??""}`]},e.id))})})})]})]}):i.jsx("div",{className:(0,h.cn)("!rounded-full size-10 flex items-center justify-center text-sm font-medium",r.status?.class),children:s.length})},sortingFn:(e,r)=>{let s=e?.original?.originalData?.members?.nodes||e?.original?.members||[],t=r?.original?.originalData?.members?.nodes||r?.original?.members||[],i=`${s?.[0]?.firstName??""} ${s?.[0]?.surname??""}`||"",a=`${t?.[0]?.firstName??""} ${t?.[0]?.surname??""}`||"";return i.localeCompare(a)}},{accessorKey:"trainer",cellAlignment:"center",header:({column:e})=>i.jsx(D.u,{column:e,title:"Trainer"}),breakpoint:"tablet-md",cell:({row:e})=>{let r=e.original,s=r.originalData?.trainer;return s&&"completed"===r.category?i.jsx("div",{className:"text-nowrap",children:(0,i.jsxs)(x.u,{children:[i.jsx(x.aJ,{children:i.jsx(x.qE,{size:"sm",variant:"secondary",children:i.jsx(x.Q5,{className:"text-sm",children:(0,x.xE)(s.firstName,s.surname)})})}),(0,i.jsxs)(x._v,{children:[s.firstName," ",s.surname??""]})]})}):i.jsx("div",{className:"text-center text-muted-foreground",children:"-"})},sortingFn:(e,r)=>{let s=`${e?.original?.originalData?.trainer?.firstName||""} ${e?.original?.originalData?.trainer?.surname||""}`||"",t=`${r?.original?.originalData?.trainer?.firstName||""} ${r?.original?.originalData?.trainer?.surname||""}`||"";return s.localeCompare(t)}},{accessorKey:"status",cellAlignment:"right",header:({column:e})=>i.jsx(D.u,{column:e,title:"Status"}),breakpoint:"landscape",cell:({row:e})=>{let r=e.original;return i.jsx(x.OE,{isOverdue:r.status.isOverdue,isUpcoming:r.status.dueWithinSevenDays,label:r.status.label||r.category})},sortingFn:(e,r)=>{let s=e?.original?.status?.label||"",t=r?.original?.status?.label||"";return s.localeCompare(t)}}],j=(0,a.useMemo)(()=>(0,b.wu)(v()),[g,p,o,u]);return m?.length?i.jsx(b.QT,{columns:j,data:m,showToolbar:c,rowStatus:M,pageSize:d||20}):i.jsx("div",{className:"text-center py-8 text-muted-foreground",children:"No training data available"})};var O=s(52241),L=s(11232),U=s(26034),F=s(99303),z=s(29342),R=s(94440),Z=s(81524);let $=(0,a.memo)(({onChange:e,vesselIdOptions:r=[],trainingTypeIdOptions:s=[],memberId:t=0,trainerIdOptions:n=[],memberIdOptions:l=[]})=>{let o=[{label:"All Categories",value:"all"},{label:"\uD83D\uDD34 Overdue",value:"overdue"},{label:"\uD83D\uDFE1 Upcoming",value:"upcoming"},{label:"\uD83D\uDFE2 Completed",value:"completed"}],[c,d]=(0,a.useState)(o[0]),u=(0,a.useCallback)((r,s)=>{e({type:r,data:s})},[e]),m=(0,a.useCallback)(e=>{d(e),u("category",e?.value||"all")},[u]),g=(0,A.k)(),p=(0,i.jsxs)("div",{className:"grid xs:grid-cols-12 gap-2.5",children:[i.jsx("div",{className:"flex xs:col-span-6 sm:col-span-2 lg:col-span-2",children:i.jsx(Z.Combobox,{options:o,value:c,onChange:m,placeholder:"All Categories",buttonClassName:"w-full",searchThreshold:10})}),i.jsx("div",{className:"flex xs:col-span-6 sm:col-span-2 lg:col-span-3",children:i.jsx(L.Z,{isClearable:!0,onChange:e=>u("vessel",e),vesselIdOptions:r})}),i.jsx("div",{className:"flex xs:col-span-6 sm:col-span-3 lg:col-span-3",children:i.jsx(U.Z,{isClearable:!0,onChange:e=>u("trainingType",e),trainingTypeIdOptions:s})}),i.jsx("div",{className:"xs:col-span-6 sm:col-span-5 lg:col-span-4",children:i.jsx(z.Z,{onChange:e=>u("dateRange",e),dateFormat:g.laptop?"MMM do, yyyy":g["tablet-md"]?"MMM d, yyyy":"M/d/yy",clearable:!0})})," ",i.jsx("div",{className:"flex flex-1 xs:col-span-12 sm:col-span-6 lg:col-span-6",children:i.jsx(F.Z,{label:"",placeholder:"Trainer",isClearable:!0,multi:!0,controlClasses:"filter",onChange:e=>{u("trainer",e)},filterByTrainingSessionMemberId:t,trainerIdOptions:n})}),i.jsx("div",{className:"flex flex-1 xs:col-span-12 sm:col-span-6 lg:col-span-6",children:i.jsx(F.Z,{isClearable:!0,label:"",multi:!0,controlClasses:"filter",placeholder:"Crew",onChange:e=>{u("member",e)},filterByTrainingSessionMemberId:t,memberIdOptions:l})})]});return i.jsx(i.Fragment,{children:g.phablet?p:i.jsx(R.UQ,{type:"single",collapsible:!0,className:"w-full mt-2.5",children:(0,i.jsxs)(R.Qd,{value:"unified-training-filters",children:[i.jsx(R.o4,{children:"Filters"}),i.jsx(R.vF,{children:p})]})})})});var V=s(46776),B=s(26100),G=s(67537);let W=({vesselId:e=0,memberId:r=0,isVesselView:s=!1})=>{let[t,n]=(0,a.useState)(!0),[l,o]=(0,a.useState)([]),[c,d]=(0,a.useState)([]),[g,p]=(0,a.useState)(!1),{getVesselWithIcon:v}=(0,O.P)();(0,a.useEffect)(()=>{p(V.Zu)},[]);let[f,{loading:y}]=(0,u.t)(m.qX,{fetchPolicy:"cache-and-network",onCompleted:e=>{o(e.readTrainingSessionDues.nodes||[])},onError:e=>{console.error("Error loading training session dues:",e)}}),[h,{loading:b}]=(0,u.t)(m.ly,{fetchPolicy:"cache-and-network",onCompleted:e=>{d(e.readTrainingSessions.nodes||[])},onError:e=>{console.error("Error loading completed training:",e)}}),D=(0,a.useCallback)(async s=>{let t={};r&&r>0&&(t.memberID={eq:+r}),e&&e>0&&(t.vesselID={eq:+e}),s.vesselID&&(t.vesselID=s.vesselID),s.trainingTypes&&(t.trainingTypeID={eq:s.trainingTypes.id.contains}),s.members&&(t.memberID={eq:s.members.id.contains}),s.date?t.dueDate=s.date:t.dueDate={ne:null},await f({variables:{filter:t}})},[r,e,f]),j=(0,a.useCallback)(async(r=0,s={})=>{let t={};e&&e>0&&(t.vesselID={eq:+e}),s.vesselID&&(t.vesselID=s.vesselID),await h({variables:{filter:t,offset:20*r,limit:20}})},[e,h]),N=(0,a.useMemo)(()=>C({trainingSessionDues:l,completedTrainingList:c,getVesselWithIcon:v,includeCompleted:!0}),[l,c,v,!0]),{handleFilterChange:T,filteredData:w}=function(e){let{initialFilter:r,unifiedData:s}=e,[t,i]=(0,a.useState)(()=>({})),[n,l]=(0,a.useState)(()=>r);(0,a.useRef)(null);let o=(0,a.useCallback)(({type:e,data:r})=>{let s={...t};if("vessel"===e&&(Array.isArray(r)&&r.length?s.vesselID={in:r.map(e=>+e.value)}:r&&!Array.isArray(r)?s.vesselID={eq:+r.value}:delete s.vesselID),"trainingType"===e){if(Array.isArray(r)&&r.length){let e=r.map(e=>+e.value);s.trainingTypes={id:{in:e}}}else if(r&&!Array.isArray(r)){let e=+r.value;s.trainingTypes={id:{contains:e}}}else delete s.trainingTypes}if("trainer"===e){if(Array.isArray(r)&&r.length){let e=r.map(e=>+e.value);s.trainer={id:{in:e}}}else if(r&&!Array.isArray(r)){let e=+r.value;s.trainer={id:{eq:e}}}else delete s.trainer}if("member"===e){if(Array.isArray(r)&&r.length){let e=r.map(e=>+e.value);s.members={id:{in:e}}}else if(r&&!Array.isArray(r)){let e=+r.value;s.members={id:{eq:e}}}else delete s.members}"dateRange"===e&&(r?.startDate&&r?.endDate?s.date={gte:r.startDate,lte:r.endDate}:delete s.date),"category"===e&&(r&&"all"!==r?s.category=r:delete s.category),i(s)},[t]);return{filter:t,setFilter:i,handleFilterChange:o,filteredData:(0,a.useMemo)(()=>s&&Array.isArray(s)?s.filter(e=>{if(n.category&&"all"!==n.category&&e.category!==n.category)return!1;if(n.vesselID){let r="string"==typeof e.vesselID?parseInt(e.vesselID,10):e.vesselID;if(n.vesselID.eq&&r!==n.vesselID.eq||n.vesselID.in&&!n.vesselID.in.includes(r))return!1}if(n.trainingTypes){let r=e.trainingTypeID||e.trainingType?.id,s="string"==typeof r?parseInt(r,10):r;if(n.trainingTypes.id?.contains&&s!==n.trainingTypes.id.contains||n.trainingTypes.id?.in&&!n.trainingTypes.id.in.includes(s))return!1}if(n.trainer){if(e.originalData){let r=e.originalData.trainerID||e.originalData.trainer?.id,s="string"==typeof r?parseInt(r,10):r;if(s){if(n.trainer.id?.eq&&s!==n.trainer.id.eq||n.trainer.id?.in&&!n.trainer.id.in.includes(s))return!1}else if(n.trainer.id)return!1}else{let r=e.trainerID||e.trainer?.id,s="string"==typeof r?parseInt(r,10):r;if(s){if(n.trainer.id?.eq&&s!==n.trainer.id.eq||n.trainer.id?.in&&!n.trainer.id.in.includes(s))return!1}else if(n.trainer.id&&"overdue"!==e.category&&"upcoming"!==e.category)return!1}}if(n.members&&(n.members.id?.eq||n.members.id?.in)){if(!e.members||!(e.members.length>0))return!1;{let r=e.members.map(e=>{let r=e.id;return"string"==typeof r?parseInt(r,10):r});if(n.members.id?.eq&&!r.includes(n.members.id.eq)||n.members.id?.in&&n.members.id.in.length>0&&!n.members.id.in.some(e=>r.includes(e)))return!1}}if(n.date&&e.dueDate){let r=new Date(e.dueDate);if(n.date.gte&&r<n.date.gte||n.date.lte&&r>n.date.lte)return!1}return!0}):[],[s,n])}}({initialFilter:{},unifiedData:N}),I=(0,a.useCallback)(async()=>{n(!0);let s={};e&&e>0&&(s.vesselID={eq:+e}),r&&+r>0&&(s.members={id:{contains:+r}}),await D(s);let t={};e&&e>0&&(t.vesselID={eq:+e}),await j(0,t),n(!1)},[e,r,D,j]);(0,a.useEffect)(()=>{I()},[I]);let q=l.length>0||c.length>0,E=y||b||t;return g&&((0,V.Fs)("EDIT_TRAINING",g)||(0,V.Fs)("VIEW_TRAINING",g)||(0,V.Fs)("RECORD_TRAINING",g)||(0,V.Fs)("VIEW_MEMBER_TRAINING",g))?(0,i.jsxs)("div",{className:"w-full space-y-6",children:[i.jsx(x.Zb,{children:i.jsx($,{memberId:r,onChange:T})}),(0,i.jsxs)("div",{className:"space-y-6",children:[i.jsx("div",{children:E&&!q?i.jsx("div",{className:"flex items-center justify-center py-8",children:(0,i.jsxs)("div",{className:"text-center space-y-3",children:[i.jsx(G.Z,{className:"h-8 w-8 animate-spin mx-auto text-primary"}),i.jsx("p",{className:"text-sm font-medium",children:"Loading training data..."})]})}):i.jsx(P,{unifiedData:w,getVesselWithIcon:v,includeCompleted:!0,memberId:r,isVesselView:s,showToolbar:!1,pageSize:20})}),!E&&0===N.length&&(0,i.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[i.jsx("p",{className:"text-sm",children:"No training data available"}),i.jsx("p",{className:"text-xs mt-1",children:"Try adjusting your filters or refresh the data"})]})]})]}):g?i.jsx(B.Z,{errorMessage:"Oops You do not have the permission to view this section."}):i.jsx(B.Z,{})},Q=()=>{let e=(0,a.useRef)(null),[r,s]=(0,d.v1)("overdue",d.AE.withDefault(!1)),t=(r,t)=>{"overdue"===r&&s(!!t),e.current?.apply({type:r,data:t})};return(0,i.jsxs)("div",{className:"block w-full",children:[i.jsx(c.ListHeader,{icon:i.jsx(o._,{className:"h-12 w-12 ring-1 p-0.5 rounded-full bg-[#fff]"}),title:`${r?"Completed":"Overdue and upcoming"} trainings`,actions:i.jsx(l.E,{onChange:e=>{t("overdue",e)},overdueList:r})}),(0,i.jsxs)("div",{className:"mt-16",children:[i.jsx(n.Z,{applyFilterRef:e}),i.jsx(W,{})]})]})}},33849:(e,r,s)=>{"use strict";s.d(r,{Z:()=>l});var t=s(98768);s(60343);var i=s(47520);s(30854);var a=s(56937);let n=(0,i.default)(async()=>{},{loadableGenerated:{modules:["app\\ui\\editor.tsx -> react-quill"]},ssr:!1});function l(e,r){return t.jsx(n,{value:e.content,placeholder:e.placeholder,onChange:e.handleEditorChange,onBlur:e.handleEditorBlur,modules:{toolbar:[[{header:[1,2,3,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"}],["link","image"],[{align:[]},{color:[]}],["clean"]]},formats:["header","bold","italic","underline","strike","blockquote","list","bullet","link","image","align","color","code-block"],className:(0,a.cn)("w-full min-h-60 mb-2 bg-card border overflow-auto text-input border-border rounded-lg",r)})}},74399:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\crew-training\layout.tsx#default`)},81754:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(96141);let i=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\crew-training\crew-training-client.tsx#default`),a=()=>t.jsx(i,{})},8087:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(97428).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,8822,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7380,7944,336,9445],()=>s(71408));module.exports=t})();