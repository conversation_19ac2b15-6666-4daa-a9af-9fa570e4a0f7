(()=>{var e={};e.id=5167,e.ids=[5167],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},73313:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),r(83319),r(47908),r(78398),r(57757),r(48045);var t=r(40060),i=r(33581),n=r(57567),a=r.n(n),l=r(51650),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let c=["",{children:["training-type",{children:["info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,83319)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\training-type\\info\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,47908)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\training-type\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\training-type\\info\\page.tsx"],u="/training-type/info/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/training-type/info/page",pathname:"/training-type/info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},62373:(e,s,r)=>{Promise.resolve().then(r.bind(r,67320))},92229:(e,s,r)=>{Promise.resolve().then(r.bind(r,40043))},52241:(e,s,r)=>{"use strict";r.d(s,{P:()=>a});var t=r(60343),i=r(79418),n=r(94060);let a=()=>{let[e,s]=(0,t.useState)({}),[r,a]=(0,t.useState)(!0),[l]=(0,i.t)(n.N5,{fetchPolicy:"cache-and-network",onCompleted:e=>{if(e.readVessels.nodes){let r={};e.readVessels.nodes.forEach(e=>{r[e.id]={...e,vesselPosition:e.vehiclePositions?.nodes?.[0]||null}}),s(r),a(!1)}},onError:e=>{console.error("queryVesselsWithIcons error",e),a(!1)}});return(0,t.useEffect)(()=>{l({variables:{filter:{archived:{eq:!1}}}})},[]),{vesselIconData:e,loading:r,getVesselWithIcon:(s,r)=>e[s]||r||{id:s,title:"Unknown Vessel"}}}},67320:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>N});var t=r(98768),i=r(69424),n=r(60343),a=r(17380),l=r(13842),o=r(46776),c=r(52241),d=r(99891),u=r(26100),p=r(39544),x=r(35024),h=r(87175),m=r(26509),g=r(79015),f=r(56937),j=r(25394);function v({label:e,value:s,className:r}){return s||0===s?t.jsx("div",{className:(0,f.cn)("space-y-2",r),children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-2 md:gap-4",children:[t.jsx(j.P,{className:"flex items-center justify-start",children:e}),t.jsx("div",{className:"md:col-span-3 text-sm",children:s})]})}):null}let y=({trainingTypeId:e})=>{e<=0&&(0,i.redirect)("/training-type");let s=(0,i.useRouter)(),[r,f]=(0,n.useState)(),{getVesselWithIcon:y}=(0,c.P)();(0,l.$J)(e,f);let[N,b]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{b(o.Zu)},[]),N&&((0,o.Fs)("EDIT_TRAINING",N)||(0,o.Fs)("VIEW_TRAINING",N)||(0,o.Fs)("RECORD_TRAINING",N)))?t.jsx("div",{className:"w-full space-y-8",children:r?(0,t.jsxs)(t.Fragment,{children:[t.jsx(x.Zb,{children:t.jsx(x.Ol,{children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[t.jsx(j.H3,{children:"Training Types"}),t.jsx(x.SZ,{children:"View and manage training type information"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 w-full sm:w-auto",children:[t.jsx(p.Button,{variant:"back",onClick:()=>s.back(),children:"Back"}),t.jsx(p.Button,{variant:"outline",iconLeft:t.jsx(g.Z,{}),onClick:()=>{s.push(`/training-type/edit?id=${e}`)},children:"Edit"}),N&&(0,o.Fs)("RECORD_TRAINING",N)&&t.jsx(p.Button,{onClick:()=>s.push(`/crew-training/create?trainingTypeId=${e}`),children:"Record training"})]})]})})}),(0,t.jsxs)(x.Zb,{children:[(0,t.jsxs)(x.Ol,{children:[t.jsx(j.H4,{children:"Training Details"}),t.jsx(x.SZ,{children:"Complete information about this training type"})]}),(0,t.jsxs)(x.aY,{className:"space-y-6",children:[t.jsx(v,{label:"Nature Of Training",value:r?.title&&t.jsx("span",{className:"font-medium",children:r.title})}),r?.occursEvery?(0,t.jsxs)(t.Fragment,{children:[t.jsx(m.Separator,{}),t.jsx(v,{label:"Occurs Every",value:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(h.C,{variant:"secondary",className:"",children:r.occursEvery}),t.jsx("span",{className:"text-muted-foreground",children:r.occursEvery>1?"days":"day"})]})})]}):null,r?.mediumWarnWithin?t.jsx(t.Fragment,{children:t.jsx(v,{label:"Medium Warning Within",value:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(h.C,{variant:"warning",children:r.mediumWarnWithin}),t.jsx("span",{className:"text-muted-foreground",children:r.mediumWarnWithin>1?"days":"day"})]})})}):null,r?.highWarnWithin?t.jsx(t.Fragment,{children:t.jsx(v,{label:"High Warning Within",value:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(h.C,{variant:"destructive",className:"",children:r.highWarnWithin}),t.jsx("span",{className:"text-muted-foreground",children:r.highWarnWithin>1?"days":"day"})]})})}):null,r?.procedure?(0,t.jsxs)(t.Fragment,{children:[t.jsx(m.Separator,{}),t.jsx(v,{label:"Procedure",value:t.jsx("div",{className:"prose prose-sm max-w-none dark:prose-invert text-outer-space-600",children:t.jsx("div",{dangerouslySetInnerHTML:{__html:r.procedure}})})})]}):null,r?.vessels?.nodes?.length>0?(0,t.jsxs)(t.Fragment,{children:[t.jsx(m.Separator,{}),t.jsx(v,{label:"Vessels",value:t.jsx("div",{className:"flex flex-wrap gap-2",children:r.vessels.nodes.map((e,s)=>{let r=y(e.id,e);return(0,t.jsxs)(h.C,{type:"normal",variant:"outline",className:"flex items-center border-border border-dashed gap-2",children:[t.jsx("div",{className:"size-6 flex items-center justify-center flex-shrink-0 [&_img]:!size-6 [&_svg]:!size-6",children:t.jsx(d.Z,{vessel:r})}),e.title]},s)})})})]}):null]})]})]}):t.jsx(x.Zb,{children:t.jsx(x.aY,{children:t.jsx(a.p2,{})})})}):N?t.jsx(u.Z,{errorMessage:"Oops You do not have the permission to view this section."}):t.jsx(u.Z,{})},N=()=>{let e=(0,i.useSearchParams)().get("id")??0;return t.jsx(y,{trainingTypeId:+e})}},40043:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(98768);r(60343);var i=r(64837);function n({children:e}){return t.jsx(i.Z,{children:e})}},83319:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\training-type\info\page.tsx#default`)},47908:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\training-type\layout.tsx#default`)},79015:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(97428).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[864,8865,3563,6263,8189,9507,7602,2935,6451,4234,2925,5394,4837,3842,7380],()=>r(73313));module.exports=t})();