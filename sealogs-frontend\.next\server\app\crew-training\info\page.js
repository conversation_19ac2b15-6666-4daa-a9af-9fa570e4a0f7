(()=>{var e={};e.id=266,e.ids=[266],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},21781:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(1206),r(74399),r(78398),r(57757),r(48045);var s=r(40060),i=r(33581),a=r(57567),n=r.n(a),l=r(51650),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let d=["",{children:["crew-training",{children:["info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1206)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew-training\\info\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,74399)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew-training\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew-training\\info\\page.tsx"],u="/crew-training/info/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/crew-training/info/page",pathname:"/crew-training/info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3609:(e,t,r)=>{Promise.resolve().then(r.bind(r,32628))},93854:(e,t,r)=>{Promise.resolve().then(r.bind(r,30261))},60826:e=>{e.exports=function(e){return e.split("")}},829:e=>{e.exports=function(e,t,r,s){for(var i=e.length,a=r+(s?1:-1);s?a--:++a<i;)if(t(e[a],a,e))return a;return -1}},65337:(e,t,r)=>{var s=r(829),i=r(35447),a=r(28026);e.exports=function(e,t,r){return t==t?a(e,t,r):s(e,i,r)}},35447:e=>{e.exports=function(e){return e!=e}},77420:e=>{e.exports=function(e,t,r){var s=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(r=r>i?i:r)<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++s<i;)a[s]=e[s+t];return a}},49513:(e,t,r)=>{var s=r(70458),i=/^\s+/;e.exports=function(e){return e?e.slice(0,s(e)+1).replace(i,""):e}},30482:(e,t,r)=>{var s=r(77420);e.exports=function(e,t,r){var i=e.length;return r=void 0===r?i:r,!t&&r>=i?e:s(e,t,r)}},74783:(e,t,r)=>{var s=r(65337);e.exports=function(e,t){for(var r=e.length;r--&&s(t,e[r],0)>-1;);return r}},41200:(e,t,r)=>{var s=r(65337);e.exports=function(e,t){for(var r=-1,i=e.length;++r<i&&s(t,e[r],0)>-1;);return r}},73211:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},28026:e=>{e.exports=function(e,t,r){for(var s=r-1,i=e.length;++s<i;)if(e[s]===t)return s;return -1}},66095:(e,t,r)=>{var s=r(60826),i=r(73211),a=r(92115);e.exports=function(e){return i(e)?a(e):s(e)}},70458:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},92115:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",s="\ud83c[\udffb-\udfff]",i="[^"+t+"]",a="(?:\ud83c[\udde6-\uddff]){2}",n="[\ud800-\udbff][\udc00-\udfff]",l="(?:"+r+"|"+s+")?",o="[\\ufe0e\\ufe0f]?",d="(?:\\u200d(?:"+[i,a,n].join("|")+")"+o+l+")*",c=RegExp(s+"(?="+s+")|(?:"+[i+r+"?",r,a,n,"["+t+"]"].join("|")+")"+(o+l+d),"g");e.exports=function(e){return e.match(c)||[]}},14826:(e,t,r)=>{var s=r(22060),i=r(49513),a=r(30482),n=r(74783),l=r(41200),o=r(66095),d=r(16266);e.exports=function(e,t,r){if((e=d(e))&&(r||void 0===t))return i(e);if(!e||!(t=s(t)))return e;var c=o(e),u=o(t),m=l(c,u),p=n(c,u)+1;return a(c,m,p).join("")}},32628:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(98768),i=r(69424),a=r(60343),n=r(66263),l=r(17380),o=r(28147),d=r(13842),c=r(75546),u=r(77664),m=r(39544),p=r(26509),f=r(74602),x=r(68648),h=r(25394),g=r(69748),v=r(94060),j=r(79418),N=r(9210),b=r(78965),y=r(50058),w=r(93488);let S=({trainingID:e})=>{e<=0&&(0,i.redirect)("/crew-training");let[t,r]=(0,a.useState)(),[S,C]=(0,a.useState)(""),[$,k]=(0,a.useState)(!1),[D,q]=(0,a.useState)(""),[T,I]=(0,a.useState)(!1),P=(0,i.useRouter)(),E=(0,y.k)();(0,d.MX)(e,r);let[M]=(0,j.t)(v.HC,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readCaptureImages.nodes;t&&I(t)},onError:e=>{console.error("getFieldImages error",e)}});(0,a.useEffect)(()=>{M({variables:{filter:{trainingSessionID:{eq:e}}}})},[]);let _=async()=>{await M({variables:{filter:{trainingSessionID:{eq:e}}}})};if(!t)return s.jsx(l.X_,{});let R=()=>t?.trainingTypes?.nodes?.map(e=>e.customisedComponentField.nodes.length>0?{id:e.id,title:e.title,fields:[...e.customisedComponentField.nodes].sort((e,t)=>e.sortOrder-t.sortOrder)}:null).filter(e=>null!==e),F=e=>{let r=t?.procedureFields?.nodes?.find(t=>t.customisedComponentFieldID==e.id);return r?.status||""},Y=e=>{let r=t?.procedureFields?.nodes?.find(t=>t.customisedComponentFieldID==e.id);return r?.comment||e.comment},A=e=>{let r=t?.procedureFields?.nodes?.find(t=>t.customisedComponentFieldID==e.id);q(r?.comment||""),k(!0)};return(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx(h.Bu,{icon:s.jsx(N._z,{className:"h-12 w-12 ring-1 p-0.5 rounded-full bg-[#fff]"}),title:`Training Session: ${t?.vessel.title} - ${t?.date?(0,c.p6)(t.date,!1):"No date set"}`}),(0,s.jsxs)("div",{className:"mt-16 space-y-6 mx-2",children:[(0,s.jsxs)(h.Zb,{children:[(0,s.jsxs)("div",{children:[s.jsx(f.H4,{children:"Training Details"}),s.jsx(f.P,{children:"Information about the trainer, type of training conducted, and participating crew members."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("h5",{className:"font-medium text-foreground",children:"Trainer"}),s.jsx("p",{className:"text-base",children:`${t?.trainer?.firstName||""} ${t?.trainer?.surname||""}`})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("h5",{className:"font-medium text-foreground",children:"Nature of Training"}),s.jsx("p",{className:"text-base",children:t?.trainingTypes?.nodes.map(e=>e.title).join(", ")})]})]}),s.jsx("div",{className:"space-y-4 mt-6",children:(0,s.jsxs)("div",{children:[s.jsx("h5",{className:"font-medium text-foreground mb-3",children:"Participating Crew Members"}),s.jsx("div",{className:"flex flex-wrap gap-3",children:t?.members?.nodes.map((e,t)=>s.jsxs(h.u,{children:[s.jsx(h.aJ,{children:s.jsx(g.Avatar,{size:"md",variant:"secondary",children:s.jsx(g.AvatarFallback,{className:"text-sm",children:g.getCrewInitials(e.firstName,e.surname)})})}),s.jsx(h._v,{children:s.jsx("p",{children:`${e.firstName||""} ${e.surname||""}`})})]},t))})]})})]}),(0,s.jsxs)(h.Zb,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[s.jsx(f.H4,{children:"Training Summary"}),s.jsx(f.P,{children:"Training procedures completed and overall session summary."})]}),(0,s.jsxs)("div",{className:"bg-muted p-4 rounded-md",children:[R().length>0&&s.jsx("div",{className:"space-y-4 mb-4",children:R().map(t=>(0,s.jsxs)("div",{className:"bg-sllightblue-100 border border-sllightblue-200 rounded-md p-4",children:[s.jsx("h3",{className:"text-lg font-medium leading-6 text-gray-9000 mb-4",children:t.title}),(0,s.jsxs)(x.Cz,{children:[t.fields.filter(e=>"Required"===e.status).length>0&&s.jsx(x.xc,{}),s.jsx(x.yE,{children:t.fields.map(t=>s.jsx(x.Mw,{locked:!0,displayField:"Required"===t.status,displayDescription:t.description,displayLabel:t.fieldName,inputId:t.id,defaultNoChecked:"Not_Ok"===F(t),defaultYesChecked:"Ok"===F(t),commentAction:()=>A(t),comment:Y(t),handleNoChange:()=>{},handleYesChange:()=>{},displayImage:!0,fieldImages:T,onImageUpload:_,sectionData:{id:e,sectionName:"trainingSessionID"}},t.id))})]})]},t.id))}),s.jsx("div",{className:"whitespace-pre-line",children:t.trainingSummary?(0,u.j)(t.trainingSummary):"No summary provided."})]})]}),(0,s.jsxs)(h.Zb,{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx(f.H4,{children:"Signatures"}),s.jsx(f.P,{children:"Digital signatures from training participants confirming completion."})]}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:t.signatures?.nodes.map(e=>s.jsxs("div",{className:"border border-border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow",children:[s.jsx("div",{className:"p-4 bg-accent",children:s.jsxs("h5",{className:"text-base font-medium",children:[e.member.firstName," ",e.member.surname]})}),s.jsx("div",{className:"bg-background border-t border-border p-4 h-[120px] flex items-center justify-center",children:e.signatureData?s.jsx(o.default,{src:e.signatureData||"/placeholder.svg",alt:`Signature of ${e.member.firstName} ${e.member.surname}`,width:220,height:80,className:"object-contain"}):s.jsx("span",{className:"text-muted-foreground text-sm italic",children:"No signature provided"})})]},e.memberID))})]})]}),(0,s.jsxs)(b.V,{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx("span",{children:(0,w.p)(E.phablet,`ID: ${t.id}`,`Training ID: ${t.id}`)}),s.jsx(p.Separator,{orientation:"vertical",className:"h-4 hidden small:block"}),s.jsx("span",{className:"hidden small:block",children:(0,w.p)(E.phablet,`Updated: ${(0,c.p6)(t.updatedAt||t.date)}`,`Last updated: ${(0,c.p6)(t.updatedAt||t.date)}`)})]}),(0,s.jsxs)("div",{className:"flex gap-2.5 justify-end",children:[s.jsx(m.Button,{variant:"back",onClick:()=>P.back(),children:"Back"}),s.jsx(m.Button,{asChild:!0,variant:"outline",children:s.jsx(n.default,{href:`/crew-training/edit?id=${t.id}`,children:(0,w.p)(E.phablet,"Edit","Edit Session")})})]})]})]})},C=()=>{let e=(0,i.useSearchParams)().get("id")??0;return s.jsx(S,{trainingID:+e})}},30261:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(98768);r(60343);var i=r(64837);function a({children:e}){return s.jsx(i.Z,{children:e})}},75546:(e,t,r)=>{"use strict";r.d(t,{Br:()=>m,fU:()=>p,o0:()=>c,p6:()=>d,vq:()=>u});var s=r(83179),i=r.n(s),a=r(7678),n=r.n(a),l=r(14826),o=r.n(l);let d=(e="",t=!0)=>{let r;if(n()(o()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,s,i]=e.split("-"),a=t?r.slice(-2):r,n=parseInt(i,10).toString().padStart(2,"0"),l=parseInt(s,10).toString().padStart(2,"0");return`${n}/${l}/${a}`}if(!(r=e&&"object"==typeof e?i()(e.toString()):i()(e)).isValid())return"";let s=r.format("DD"),a=r.format("MM"),l=t?r.format("YY"):r.format("YYYY");return`${s}/${a}/${l}`},c=(e="",t=!0)=>{let r;if(n()(o()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,s,i]=e.split("-"),a=t?r.slice(-2):r,n=parseInt(i,10).toString().padStart(2,"0"),l=parseInt(s,10).toString().padStart(2,"0");return`${n}/${l}/${a} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[r,s]=e.split(" "),[i,a,n]=r.split("-"),l=t?i.slice(-2):i,o=s.split(":"),d=o[0].padStart(2,"0"),c=o[1].padStart(2,"0"),u=parseInt(n,10).toString().padStart(2,"0"),m=parseInt(a,10).toString().padStart(2,"0");return`${u}/${m}/${l} ${d}:${c}`}if(!(r=e&&"object"==typeof e?i()(e.toString()):i()(e)).isValid())return"";let s=r.format("DD"),a=r.format("MM"),l=t?r.format("YY"):r.format("YYYY"),d=r.format("HH:mm");return`${s}/${a}/${l} ${d}`},u=(e="")=>n()(o()(e))?"":i()(e).format("YYYY-MM-DD HH:mm:ss"),m=(e="")=>n()(o()(e))?new Date:new Date(`${e}T10:00:00Z`),p=(e,t)=>{let r=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),s=e=>e.includes(" ")?e.replace(" ","T"):e,i=e=>{if(!e||"string"!=typeof e)return null;if(r(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(s(e))},a=i(e),n=i(t);return!a||!n||isNaN(a.getTime())||isNaN(n.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):n>a}},77664:(e,t,r)=>{"use strict";r.d(t,{j:()=>s});let s=e=>{let t=document.createElement("div");return t.innerHTML=e,t.textContent||t.innerText||""}},68648:(e,t,r)=>{"use strict";r.d(t,{Cz:()=>v,Mw:()=>w,a3:()=>N,eN:()=>j,xc:()=>b,yE:()=>y});var s=r(98768),i=r(60343),a=r.n(i),n=r(39544),l=r(81515),o=r(49581),d=r(58830),c=r(67537),u=r(52269),m=r(57103),p=r(70906),f=r(56937),x=r(74602),h=r(69852),g=r(75776);let v=a().forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,f.cn)("h-fit bg-card",e),...t}));v.displayName="CheckField";let j=a().forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,f.cn)("flex flex-col space-y-1.5 pb-2.5",e),...t}));j.displayName="CheckFieldHeader";let N=a().forwardRef(({className:e,children:t,...r},i)=>s.jsx(x.H4,{ref:i,className:(0,f.cn)("",e),...r,children:t}));N.displayName="CheckFieldTitle";let b=a().forwardRef(({className:e,...t},r)=>(0,s.jsxs)("div",{ref:r,className:(0,f.cn)("text-input font-medium border-b border-border flex justify-end",e),...t,children:[s.jsx("div",{className:"flex w-12 h-10 rounded-t-md text-destructive justify-center items-center",children:"No"}),s.jsx("div",{className:"flex w-12 h-10 rounded-t-md text-bright-turquoise-600 text justify-center items-center",children:"Yes"})]}));b.displayName="CheckFieldTopContent";let y=a().forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,f.cn)("pt-0",e),...t}));function w({locked:e=!1,displayField:t=!0,displayLabel:r="",displayDescription:a="",descriptionType:v,inputId:j,handleNoChange:N,handleYesChange:b,defaultNoChecked:y=!1,defaultYesChecked:w=!1,comment:S="",setDescriptionPanelContent:C,setOpenDescriptionPanel:$,setDescriptionPanelHeading:k,className:D="",innerWrapperClassName:q="",onCommentSave:T,onCommentDelete:I,commentAction:P,offline:E=!1,fieldId:M,onError:_,disabled:R=!1,required:F=!1,hideCommentButton:Y=!1,displayImage:A=!1,fieldImages:z=!1,onImageUpload:B,sectionData:H={id:0,sectionName:"logBookEntryID"},...L}){let[V,Z]=(0,i.useState)(w),[O,U]=(0,i.useState)(y),[G,J]=(0,i.useState)(!1),[X,K]=(0,i.useState)(S),[Q,W]=(0,i.useState)(S),[ee,et]=(0,i.useState)(!1),[er,es]=(0,i.useState)(!1);(0,i.useRef)(!0);let ei=e||R,[ea,en]=(0,i.useState)(!1),[el,eo]=(0,i.useState)(""),[ed,ec]=(0,i.useState)(""),eu=async()=>{if(!ei){et(!0),es(!1);try{if(K(Q),T){let e=T(Q);e instanceof Promise&&await e}J(!1)}catch(e){console.error("Error saving comment:",e),es(!0),_&&e instanceof Error&&_(e)}finally{et(!1)}}},em=async()=>{if(!ei){et(!0),es(!1);try{if(I)try{let e=I();e instanceof Promise&&await e}catch(e){console.error("Error in onCommentDelete callback:",e)}K(""),W(""),J(!1)}catch(e){console.error("Error deleting comment:",e),es(!0),_&&e instanceof Error&&_(e)}finally{et(!1)}}};return t?(console.log(H,A),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:(0,f.cn)("flex gap-2.5 min-h-16 !mt-0 border-b border-border ",q),children:[(0,s.jsxs)("div",{className:"flex-1 flex flex-col phablet:flex-row pt-2.5 phablet:pt-0 phablet:gap-2.5 items-end phablet:items-center",children:[(0,s.jsxs)(x.P,{className:"text-card-foreground text-base w-full",children:[r,F&&s.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-5 py-2.5",children:[A&&s.jsx(g.Z,{file:!!(z&&Array.isArray(z))&&z.filter(e=>e.fieldName===j).sort((e,t)=>t.id-e.id),setFile:B,inputId:j,sectionData:H}),a&&s.jsx(n.Button,{variant:"ghost",size:"icon",iconOnly:!0,title:"View description",iconLeft:s.jsx(l.Z,{className:"text-light-blue-vivid-900 fill-light-blue-vivid-50",size:24}),onClick:()=>{C&&$&&k&&(C(a),$(!0),k(r)),eo(a),en(!0),ec(r)}}),!Y&&s.jsx(n.Button,{variant:"ghost",size:"icon",iconOnly:!0,title:"Add comment",className:"group",iconLeft:X?s.jsx(o.Z,{className:(0,f.cn)("text-curious-blue-400"),size:24}):s.jsx(d.Z,{className:(0,f.cn)("text-neutral-400 group-hover:text-neutral-400/50","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300"),size:24}),onClick:()=>{P?P():J(!0)}})]})]}),(0,s.jsxs)(p.Ee,{...L,variant:"horizontal",className:(0,f.cn)({"opacity-60":ei}),gap:"none",value:V?"yes":O?"no":"",onValueChange:e=>{ei||("yes"===e?function(){if(!ei)try{b(M),Z(!0),U(!1)}catch(e){console.error("Error handling Yes change:",e),_&&e instanceof Error&&_(e)}}():"no"!==e||function(){if(!ei)try{N(M),U(!0),Z(!1)}catch(e){console.error("Error handling No change:",e),_&&e instanceof Error&&_(e)}}())},disabled:ei,children:[s.jsx("div",{className:(0,f.cn)("flex w-12 bg-destructive-foreground justify-center phablet:p-0 items-center"),children:s.jsx(p.mJ,{value:"no",id:`${j}-no_radio`,variant:"destructive",size:"lg"})}),s.jsx("div",{className:(0,f.cn)("flex w-12 bg-bright-turquoise-100 justify-center items-center "),children:s.jsx(p.mJ,{value:"yes",id:`${j}-yes_radio`,variant:"success",size:"lg"})})]})]}),s.jsx(h.Sheet,{open:ea,onOpenChange:en,children:(0,s.jsxs)(h.SheetContent,{side:"left",className:"w-[90%] sm:w-[540px] max-w-2xl",onInteractOutside:()=>{en(!1),eo(""),ec("")},children:[s.jsx(h.SheetHeader,{children:s.jsx(h.SheetTitle,{children:ed})}),s.jsx(h.SheetBody,{children:"string"==typeof el?s.jsx("div",{className:"prose prose-sm max-w-none leading-7",dangerouslySetInnerHTML:{__html:el}}):s.jsx("div",{className:"prose prose-sm max-w-none leading-7",children:el})})]})}),s.jsx(m.AlertDialogNew,{openDialog:G,setOpenDialog:J,title:X?"Edit comment":"Add comment",handleCreate:ei?void 0:eu,handleDestructiveAction:!ei&&X?em:void 0,showDestructiveAction:!ei&&!!X,actionText:ee?"Saving...":"Save",destructiveActionText:"Delete",destructiveLoading:ee,noButton:ei,cancelText:ei?"Close":"Cancel",loading:ee,children:(0,s.jsxs)("div",{className:"flex flex-col",children:[er&&(0,s.jsxs)("div",{className:"text-destructive mb-2 text-sm",children:["Error ",X?"updating":"saving"," ","comment. Please try again."]}),s.jsx(u.Textarea,{id:`${j}-comment`,disabled:ei||ee,rows:4,autoResize:!0,placeholder:"Comment",value:Q,onChange:e=>W(e.target.value),className:(0,f.cn)("max-h-[60svh]",{"border-destructive":er})}),ee&&(0,s.jsxs)("div",{className:"flex items-center justify-center mt-2",children:[s.jsx(c.Z,{className:"h-4 w-4 animate-spin mr-2"}),s.jsx("span",{className:"text-sm",children:"Saving..."})]})]})})]})):null}y.displayName="CheckFieldContent",a().forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,f.cn)("flex items-center p-4 pt-0",e),...t})).displayName="CheckFieldFooter"},50058:(e,t,r)=>{"use strict";r.d(t,{k:()=>a});var s=r(9999);let i={tiny:"320px",small:"375px",standard:"430px",phablet:"480px","tablet-sm":"600px","tablet-md":"768px","tablet-lg":"834px",landscape:"1024px",laptop:"1280px",desktop:"1536px"};function a(e){let t={...i,...e},r={};return Object.keys(t).forEach(e=>{let i=t[e];r[e]=(0,s.a)(`(min-width: ${i})`)}),r}},1206:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\crew-training\info\page.tsx#default`)},74399:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\crew-training\layout.tsx#default`)},9999:(e,t,r)=>{"use strict";r.d(t,{a:()=>i});var s=r(60343);function i(e,t,{getInitialValueInEffect:r}={getInitialValueInEffect:!0}){let[i,a]=(0,s.useState)(!!r&&t);return(0,s.useRef)(null),i||!1}},49581:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97428).Z)("MessageSquareText",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}],["path",{d:"M13 8H7",key:"14i4kc"}],["path",{d:"M17 12H7",key:"16if0g"}]])},58830:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97428).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,6451,4234,2925,5394,4837,6342,3842,7380,5776],()=>r(21781));module.exports=s})();