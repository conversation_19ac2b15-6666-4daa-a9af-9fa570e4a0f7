(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1896],{84257:function(e,t,n){Promise.resolve().then(n.bind(n,27451))},54497:function(e,t,n){"use strict";var l=n(12803),s=n(52285);t.Z=function(e){let{message:t="Loading ...",errorMessage:n=""}=e;return(0,l.jsxs)("div",{className:"h-screen w-full flex flex-col items-center justify-center",children:[(0,l.jsx)("div",{children:(0,l.jsx)(s.default,{src:"/sealogs-loading.gif",alt:"Sealogs Logo",priority:!0,width:300,height:300,unoptimized:!0})}),n?(0,l.jsx)("div",{className:"text-destructive ",children:n}):(0,l.jsx)("div",{children:t})]})}},27451:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return b}});var l=n(12803),s=n(60333),i=n(20049),a=n(63213),o=n(6916),r=n(84143),c=n(97735),d=n(50072),u=n(54497),h=n(43917),g=n(99160),x=n(25123),m=n(74234),p=n(62007),f=n(10341),j=n(7262),v=n(77643),N=()=>{let e=(0,p.useRouter)(),[t,n]=(0,s.useState)(!0),[N,w]=(0,s.useState)([]),[b,{loading:C}]=(0,a.t)(i.IR,{fetchPolicy:"cache-and-network",onCompleted:e=>{let{nodes:t}=e.readGeoLocations;t&&w(t)},onError:e=>{console.error("queryGeoLocations error",e)}}),S=async()=>{await b({variables:{limit:1e3,offset:0}})};(0,s.useEffect)(()=>{t&&(S(),n(!1))},[t]);let y=(0,r.wu)([{accessorKey:"title",header:e=>{let{column:t}=e;return(0,l.jsx)(c.u,{column:t,title:"Location"})},cellAlignment:"left",cell:e=>{let{row:t}=e,n=t.original;return(0,l.jsx)(v.default,{href:"/location/edit?id=".concat(n.id),children:(0,l.jsx)("div",{className:" text-medium ",children:n.title||"No Title"})})}},{accessorKey:"lat",header:e=>{let{column:t}=e;return(0,l.jsx)(c.u,{column:t,title:"Latitude"})},cell:e=>{let{row:t}=e,n=t.original;return(0,l.jsx)("div",{children:n.lat})}},{accessorKey:"long",header:e=>{let{column:t}=e;return(0,l.jsx)(c.u,{column:t,title:"Longitude"})},cell:e=>{let{row:t}=e,n=t.original;return(0,l.jsx)("div",{children:n.long})}},{accessorKey:"map",header:()=>(0,l.jsx)("div",{className:"text-center",children:"Map"}),cell:e=>{let{row:t}=e,n=t.original;return(0,l.jsx)("div",{className:"flex justify-center",children:(0,l.jsxs)(h.Dialog,{children:[(0,l.jsx)(h.DialogTrigger,{onClick:e=>e.stopPropagation(),asChild:!0,children:(0,l.jsx)(j.r4,{})}),(0,l.jsxs)(h.DialogContent,{className:"max-w-6xl w-[95vw] p-0 overflow-hidden",children:[(0,l.jsx)(h.DialogHeader,{className:"p-4 pb-0",children:(0,l.jsxs)(h.DialogTitle,{className:"flex items-center gap-2",children:[(0,l.jsx)(x.Z,{size:18}),n.title||"Location Map",n.lat&&n.long&&(0,l.jsxs)(f.P,{className:"text-muted-foreground leading-none",children:["(",n.lat.toFixed(6),","," ",n.long.toFixed(6),")"]})]})}),(0,l.jsx)("div",{className:"h-[80vh] pt-0 grid",children:(0,l.jsx)(o.Z,{enableResize:!0,vessel:{title:n.title},position:[n.lat||0,n.long||0],zoom:15,className:"size-full"})})]})]})})},enableSorting:!1}]);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(d.ListHeader,{icon:(0,l.jsx)(j.r4,{className:"size-12"}),title:"Locations",actions:(0,l.jsx)(g.Button,{iconLeft:(0,l.jsx)(m.Z,{}),onClick:()=>e.push("/location/create"),children:"Add Location"})}),(0,l.jsx)("div",{className:"mt-16",children:t||C?(0,l.jsx)(u.Z,{}):(0,l.jsx)(r.wQ,{columns:y,data:N,showToolbar:!1,pageSize:20})})]})},w=n(8644),b=()=>((0,p.useRouter)(),(0,s.useEffect)(()=>{(0,w.UU)()},[]),(0,l.jsx)(N,{}))},15470:function(e,t,n){"use strict";n.d(t,{k:function(){return h}});var l=n(12803),s=n(23800),i=n(49559),a=n(50785),o=n(16489),r=n(99160),c=n(62148),d=n(10341),u=n(79909);function h(e){let{table:t,pageSizeOptions:n=[10,20,30,40,50],showPageSizeSelector:h=!0}=e,g=(0,u.k)();return(0,l.jsx)("div",{className:"flex items-center justify-center px-2",children:(0,l.jsxs)("div",{className:"flex gap-2 items-end phablet:items-center phablet:space-x-6 lg:space-x-8",children:[h&&(0,l.jsx)("div",{className:"flex items-center space-x-2",children:(0,l.jsx)(d.__,{label:"Rows per page",position:g.phablet?"left":"top",htmlFor:"rows-per-page",className:"text-sm",children:(0,l.jsxs)(c.Select,{value:"".concat(t.getState().pagination.pageSize),onValueChange:e=>{t.setPageSize(Number(e))},children:[(0,l.jsx)(c.SelectTrigger,{className:"h-8 w-[70px]",children:(0,l.jsx)(c.SelectValue,{placeholder:t.getState().pagination.pageSize})}),(0,l.jsx)(c.SelectContent,{side:"top",children:n.map(e=>(0,l.jsx)(c.SelectItem,{value:"".concat(e),children:e},e))})]})})}),(0,l.jsxs)("div",{className:"flex w-fit items-center justify-center text-sm",children:["Page ",t.getState().pagination.pageIndex+1," of"," ",t.getPageCount()]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsxs)(r.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>t.setPageIndex(0),disabled:!t.getCanPreviousPage(),children:[(0,l.jsx)("span",{className:"sr-only",children:"Go to first page"}),(0,l.jsx)(s.Z,{})]}),(0,l.jsxs)(r.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>t.previousPage(),disabled:!t.getCanPreviousPage(),children:[(0,l.jsx)("span",{className:"sr-only",children:"Go to previous page"}),(0,l.jsx)(i.Z,{})]}),(0,l.jsxs)(r.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>t.nextPage(),disabled:!t.getCanNextPage(),children:[(0,l.jsx)("span",{className:"sr-only",children:"Go to next page"}),(0,l.jsx)(a.Z,{})]}),(0,l.jsxs)(r.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>t.setPageIndex(t.getPageCount()-1),disabled:!t.getCanNextPage(),children:[(0,l.jsx)("span",{className:"sr-only",children:"Go to last page"}),(0,l.jsx)(o.Z,{})]})]})]})})}},97735:function(e,t,n){"use strict";n.d(t,{u:function(){return d}});var l=n(12803),s=n(53068),i=n(54678),a=n(51685),o=n(49529),r=n(99160),c=n(78045);function d(e){let{column:t,title:n,className:d}=e;return t.getCanSort()?(0,l.jsx)("div",{children:(0,l.jsxs)(c.DropdownMenu,{children:[(0,l.jsx)(c.DropdownMenuTrigger,{asChild:!0,children:(0,l.jsx)(r.Button,{variant:"ghost",size:"sm",iconRight:"desc"===t.getIsSorted()?(0,l.jsx)(s.Z,{className:"w-4 h-4"}):"asc"===t.getIsSorted()?(0,l.jsx)(i.Z,{className:"w-4 h-4"}):(0,l.jsx)(a.Z,{className:"w-4 h-4"}),className:(0,o.cn)("h-8 small:p-auto cursor-default relative z-10 bg-card text-sm text-neutral-400 font-normal items-center px-1",d),children:n})}),(0,l.jsxs)(c.DropdownMenuContent,{align:"start",children:[(0,l.jsxs)(c.DropdownMenuItem,{onClick:()=>t.toggleSorting(!1),children:[(0,l.jsx)(i.Z,{className:"h-3.5 w-3.5 text-input"}),"Asc"]}),(0,l.jsxs)(c.DropdownMenuItem,{onClick:()=>t.toggleSorting(!0),children:[(0,l.jsx)(s.Z,{className:"h-3.5 w-3.5 text-input"}),"Desc"]}),t.getIsSorted()&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(c.DropdownMenuSeparator,{}),(0,l.jsx)(c.DropdownMenuItem,{onClick:()=>t.clearSorting(),children:"Clear"})]})]})]})}):(0,l.jsx)("div",{className:(0,o.cn)(d),children:n})}},28171:function(e,t,n){"use strict";n.d(t,{n:function(){return i}});var l=n(12803),s=n(30542);function i(e){let{table:t,onChange:n}=e;return(0,l.jsx)(s.Z,{onChange:n,table:t})}},84143:function(e,t,n){"use strict";n.d(t,{QT:function(){return j},wQ:function(){return f},wu:function(){return g}});var l=n(12803),s=n(57820),i=n(28171),a=n(15470),o=n(79909),r=n(60333),c=n(27713),d=n(10175),u=n(85034),h=n(49529);function g(e){return e}let x=e=>{switch(e){case"left":return"items-left justify-start justify-items-start text-left";case"right":return"items-right justify-end justify-items-end text-right";default:return"items-center justify-center justify-items-center text-center"}},m=e=>{switch(e){case"overdue":case"upcoming":return"rounded-md [&>td:first-child]:rounded-l-lg [&>td:last-child]:rounded-r-lg";default:return""}},p=e=>{switch(e){case"overdue":return"destructive";case"upcoming":return"warning";default:return}};function f(e){let{columns:t,data:n,showToolbar:g=!0,className:f,pageSize:j=10,pageSizeOptions:v=[10,20,30,40,50],showPageSizeSelector:N=!0,onChange:w,rowStatus:b}=e,[C,S]=r.useState([]),[y,P]=r.useState([]),[k,M]=r.useState({pageIndex:0,pageSize:j}),z=(0,o.k)(),D=r.useMemo(()=>t.filter(e=>e.showOnlyBelow?!z[e.showOnlyBelow]:!e.breakpoint||z[e.breakpoint]),[t,z]);r.useEffect(()=>{M(e=>({...e,pageSize:j}))},[j]);let Z=(0,c.b7)({data:n,columns:D,onSortingChange:S,getCoreRowModel:(0,d.sC)(),getPaginationRowModel:(0,d.G_)(),getSortedRowModel:(0,d.tj)(),onColumnFiltersChange:P,getFilteredRowModel:(0,d.vL)(),onPaginationChange:M,state:{sorting:C,columnFilters:y,pagination:k}});return(0,l.jsxs)("div",{className:"space-y-4 pb-8",children:[g&&(0,l.jsx)(u.Zb,{className:"p-2 md:p-auto",children:(0,l.jsx)(i.n,{table:Z,onChange:w})}),(0,l.jsxs)(s.iA,{className:f||"p-0 phablet:p-8 lg:p-6 xl:p-8 shadow-none border-0 phablet:border border-border bg-card rounded-lg",children:[Z.getHeaderGroups().some(e=>e.headers.some(e=>e.column.columnDef.header&&""!==e.column.columnDef.header))&&(0,l.jsx)(s.xD,{children:Z.getHeaderGroups().map(e=>(0,l.jsx)(s.SC,{children:e.headers.map(e=>{let t=e.column.columnDef,n="title"===e.column.id?"left":t.cellAlignment||"center";return(0,l.jsx)(s.ss,{className:"title"===e.column.id?"items-left justify-items-start text-left":x(n),children:e.isPlaceholder?null:(0,c.ie)(e.column.columnDef.header,e.getContext())},e.id)})},e.id))}),(0,l.jsx)(s.RM,{children:Z.getRowModel().rows.length?Z.getRowModel().rows.map(e=>{let t=b?b(e.original):"normal",n=m(t);return(0,l.jsx)(s.SC,{"data-state":e.getIsSelected()?"selected":void 0,className:(0,h.cn)("mb-4",n),children:e.getVisibleCells().map(e=>{let n=e.column.columnDef,i="title"===e.column.id?"left":n.cellAlignment||"center";return(0,l.jsx)(s.pj,{statusOverlay:"normal"!==t,statusOverlayColor:p(t),className:(0,h.cn)("","title"===e.column.id?"".concat(D.length>1?"w-auto":"w-full"," items-left justify-items-start text-left"):x(i),n.cellClassName),children:(0,l.jsx)("div",{className:(0,h.cn)("flex px-1.5 xs:px-2.5 flex-1",x(i)),children:(0,c.ie)(e.column.columnDef.cell,e.getContext())})},e.id)})},String(e.id))}):(0,l.jsx)(s.SC,{children:(0,l.jsx)(s.pj,{colSpan:D.length,className:"h-24 text-center",children:"No results."})})})]}),(Z.getCanPreviousPage()||Z.getCanNextPage())&&(0,l.jsx)("div",{className:"flex items-center justify-center phablet:justify-end space-x-2 py-4",children:(0,l.jsx)(a.k,{table:Z,pageSizeOptions:v,showPageSizeSelector:N})})]})}let j=f},6916:function(e,t,n){"use strict";n.d(t,{Z:function(){return j}});var l=n(12803),s=n(69334);n(12924),n(61370);var i=n(69303),a=n(97402),o=n(60333),r=n(63213),c=n(20049),d=n(10911),u=n(653),h=n.n(u);let g=(0,s.default)(()=>n.e(8015).then(n.bind(n,78015)).then(e=>e.MapContainer),{loadableGenerated:{webpack:()=>[78015]},ssr:!1}),x=(0,s.default)(()=>n.e(8015).then(n.bind(n,78015)).then(e=>e.Marker),{loadableGenerated:{webpack:()=>[78015]},ssr:!1}),m=(0,s.default)(()=>n.e(8015).then(n.bind(n,78015)).then(e=>e.TileLayer),{loadableGenerated:{webpack:()=>[78015]},ssr:!1}),p=e=>{let[t,l]=(0,o.useState)([]);(0,o.useEffect)(()=>{e&&"Photo"===e.iconMode&&s(e.photoID)},[e]);let s=async e=>{await i({variables:{id:[e]}})},[i]=(0,r.t)(c.ZX,{fetchPolicy:"cache-and-network",onCompleted:e=>{l([e.readFiles.nodes[0]])},onError:e=>{console.error("queryFilesEntry error",e)}}),a=n(6176),d="";if((null==e?void 0:e.iconMode)==="Photo"&&t.length>0){var u;d="".concat("https://api.sealogs.com/assets/").concat(null===(u=t[0])||void 0===u?void 0:u.fileFilename)}else d=(null==e?void 0:e.iconMode)==="Icon"&&(null==e?void 0:e.icon)!=null?"/vessel-icons/".concat(null==e?void 0:e.icon,".svg"):"/vessel.svg";return a?a.icon({iconUrl:d,className:" ring-1  rounded-full",iconSize:[40,40]}):null},f=e=>{let{debounceMs:t=150}=e,n=(0,i.Sx)(),{run:l}=(0,d.DI)(()=>{n&&n.invalidateSize()},t);return(0,o.useEffect)(()=>{let e=n.getContainer();if(!e)return;let t=new ResizeObserver(e=>{e.some(e=>{let{width:t,height:n}=e.contentRect;return t>0&&n>0})&&l()});t.observe(e);let s=e.parentElement;s&&t.observe(s);let i=setTimeout(()=>{n&&n.invalidateSize()},100);return()=>{t.disconnect(),clearTimeout(i)}},[n,l]),null};function j(e){let{position:t,vessel:n,vessels:s,zoom:o=13,onPositionChange:r,className:c="h-full",enableResize:d=!0,scrollWheelZoom:u=!1,style:j,resizeDebounceMs:v=150,enableClickToSetPosition:N=!1}=e,w=[isNaN(t[0])?0:t[0],isNaN(t[1])?0:t[1]],b=h()((e,t)=>{"number"!=typeof e||"number"!=typeof t||isNaN(e)||isNaN(t)||null==r||r([e,t])},100),C=e=>{var t,n;if((null==e?void 0:null===(t=e.latlng)||void 0===t?void 0:t.lat)!==void 0&&(null==e?void 0:null===(n=e.latlng)||void 0===n?void 0:n.lng)!==void 0){let{lat:t,lng:n}=e.latlng;b(t,n)}},S=e=>{let{position:t,vessel:n}=e;return(0,i.zV)({dblclick(e){N&&C(e)}}),(0,l.jsx)(x,{position:t,icon:p(n),children:(null==n?void 0:n.title)&&(0,l.jsx)(a.u,{children:n.title})})},y=(()=>{if(s&&s.length>0){let e=s.find(e=>{var t,n;return(null===(t=e.vesselPosition)||void 0===t?void 0:t.lat)&&(null===(n=e.vesselPosition)||void 0===n?void 0:n.long)});if(e)return[e.vesselPosition.lat||0,e.vesselPosition.long||0]}return w})(),P="map-".concat(Math.round(1e3*y[0]),"-").concat(Math.round(1e3*y[1]));return(0,l.jsxs)(g,{center:y,zoom:o,scrollWheelZoom:u,className:c,style:{minHeight:"200px",height:"100%",...j},children:[(0,l.jsx)(m,{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),s&&s.length>0?s.filter(e=>{var t,n,l;return(null===(t=e.vesselPosition)||void 0===t?void 0:t.lat)!=0||(null===(l=e.vesselPosition)||void 0===l?void 0:null===(n=l.geoLocation)||void 0===n?void 0:n.id)>0}).map((e,t)=>{var n;return(null==e?void 0:null===(n=e.vesselPosition)||void 0===n?void 0:n.id)>0&&(0,l.jsx)(S,{position:[(e.vesselPosition.lat||0)+.001+.004*Math.random(),e.vesselPosition.long||0],vessel:e},t)}):n&&(0,l.jsx)(S,{position:w,vessel:n}),d&&(0,l.jsx)(f,{debounceMs:v})]},P)}}},function(e){e.O(0,[7836,5290,205,5722,3213,6059,2444,2551,6972,9511,1098,9444,8115,3265,2447,1121,7643,529,7225,4314,9296,3026,1244,6928,49,6203,341,1750,7262,9266,298,7788,1243,542,9396,6953,1744],function(){return e(e.s=84257)}),_N_E=e.O()}]);