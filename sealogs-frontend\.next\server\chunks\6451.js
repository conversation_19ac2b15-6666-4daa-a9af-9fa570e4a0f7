exports.id=6451,exports.ids=[6451],exports.modules={64788:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,24489,23)),Promise.resolve().then(s.t.bind(s,8332,23)),Promise.resolve().then(s.t.bind(s,20439,23)),Promise.resolve().then(s.t.bind(s,85380,23)),Promise.resolve().then(s.t.bind(s,74737,23)),Promise.resolve().then(s.t.bind(s,4533,23))},54365:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,68472,23))},58080:(e,t,s)=>{Promise.resolve().then(s.bind(s,89542)),Promise.resolve().then(s.bind(s,18123)),Promise.resolve().then(s.bind(s,73217)),Promise.resolve().then(s.bind(s,32993))},18123:(e,t,s)=>{"use strict";s.d(t,{ApolloWrapper:()=>p});var r=s(98768),i=s(48900),o=s(21606),a=s(61398),n=s(13715),d=s(26660);let l=({children:e})=>((0,d.x)(),e);s(27849);let c=new a.ND({typePolicies:{Query:{fields:{readLogBookEntries:{read:(e,{args:t,toReference:s})=>e||s({__typename:"LogBookEntry",id:t?.id})},readSeaLogsMember:{read:(e,{args:t,toReference:s})=>e||s({__typename:"SeaLogsMember",id:t?.id})},readCrewMembers_LogBookEntrySections:{read:(e,{args:t,toReference:s})=>e||s({__typename:"CrewMembers_LogBookEntrySection",id:t?.id})},readMissionTimelines:{keyArgs:["filter",["maintenanceCheckID","archived"]],merge:(e,t,{args:s})=>t}}},MissionTimeline:{keyFields:["id"]}}});function u(){let e=(0,n.v)(async(e,{headers:t,token:s})=>({headers:{...t,...s?{authorization:`Bearer ${s}`}:{}}}));return new a.p0({cache:c,link:i.i.from([new a.a4({stripDefer:!0}),e.concat(new o.u({uri:"https://api.sealogs.com/graphql/"}))]),connectToDevTools:!0})}function p({children:e}){return r.jsx(a.e$,{makeClient:u,children:r.jsx(l,{children:e})})}},56937:(e,t,s)=>{"use strict";s.d(t,{cn:()=>o});var r=s(28411),i=s(5001);function o(...e){return(0,i.m6)((0,r.W)(e))}},26100:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var r=s(98768),i=s(28147);let o=function({message:e="Loading ...",errorMessage:t=""}){return(0,r.jsxs)("div",{className:"h-screen w-full flex flex-col items-center justify-center",children:[r.jsx("div",{children:r.jsx(i.default,{src:"/sealogs-loading.gif",alt:"Sealogs Logo",priority:!0,width:300,height:300,unoptimized:!0})}),t?r.jsx("div",{className:"text-destructive ",children:t}):r.jsx("div",{children:e})]})}},73217:(e,t,s)=>{"use strict";s.d(t,{ThemeProviders:()=>l});var r=s(98768);s(60343);var i=s(96056);function o({children:e,...t}){return r.jsx(i.f,{...t,children:e})}var a=s(34376),n=s(26731);function d(){let{toasts:e}=(0,a.pm)();return(0,r.jsxs)(n.VW,{children:[e.map(function({id:e,title:t,description:s,action:i,...o}){return(0,r.jsxs)(n.FN,{...o,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[t&&r.jsx(n.Mi,{children:t}),s&&r.jsx(n.lj,{children:s})]}),i,r.jsx(n.sA,{})]},e)}),r.jsx(n._i,{})]})}function l({children:e}){return(0,r.jsxs)(o,{attribute:"class",defaultTheme:"light",enableSystem:!0,children:[e,r.jsx(d,{})]})}},32993:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var r=s(98768),i=s(69424),o=s(60343);s(86418),s(7678);var a=s(11390),n=s(26100);let d=({children:e})=>{let t=(0,i.useRouter)(),s=(0,i.usePathname)(),[d,l]=(0,o.useState)(),[c,u]=(0,o.useState)(!0);return((0,o.useEffect)(()=>{["/login","/lost-password","/reset-password","/redirect"].includes(s)&&console.log("AuthProvider: Exempted path",s),l(!0),u(!1)},[s]),c)?r.jsx(n.Z,{}):!1!==d||s.startsWith("/login")?r.jsx(a.BX,{token:"P-otInIwsjplJMgK8EfvZiYsT3R",children:e}):(t.push("/login"),r.jsx(n.Z,{}))}},26731:(e,t,s)=>{"use strict";s.d(t,{FN:()=>p,Mi:()=>f,VW:()=>l,_i:()=>c,lj:()=>v,sA:()=>m});var r=s(98768),i=s(60343),o=s(58301),a=s(85745),n=s(13609),d=s(56937);let l=o.Provider,c=i.forwardRef(({className:e,...t},s)=>r.jsx(o.Viewport,{ref:s,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse items-end p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[600px]",e),...t}));c.displayName=o.Viewport.displayName;let u=(0,a.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),p=i.forwardRef(({className:e,variant:t,...s},i)=>r.jsx(o.Root,{ref:i,className:(0,d.cn)(u({variant:t}),e),...s}));p.displayName=o.Root.displayName,i.forwardRef(({className:e,...t},s)=>r.jsx(o.Action,{ref:s,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=o.Action.displayName;let m=i.forwardRef(({className:e,...t},s)=>r.jsx(o.Close,{ref:s,className:(0,d.cn)("absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:r.jsx(n.Z,{className:"h-4 w-4"})}));m.displayName=o.Close.displayName;let f=i.forwardRef(({className:e,...t},s)=>r.jsx(o.Title,{ref:s,className:(0,d.cn)("text-sm font-semibold [&+div]:text-xs",e),...t}));f.displayName=o.Title.displayName;let v=i.forwardRef(({className:e,...t},s)=>r.jsx(o.Description,{ref:s,className:(0,d.cn)("text-sm opacity-90",e),...t}));v.displayName=o.Description.displayName},34376:(e,t,s)=>{"use strict";s.d(t,{Am:()=>u,pm:()=>p});var r=s(60343);let i=0,o=new Map,a=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?a(s):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function c(e){l=n(l,e),d.forEach(e=>{e(l)})}function u({...e}){let t=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function p(){let[e,t]=r.useState(l);return r.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},78398:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,metadata:()=>l,viewport:()=>c});var r=s(96141),i=s(38851);let o=(0,i.createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\lib\ApolloWrapper.tsx#ApolloWrapper`);s(65083);let a=(0,i.createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\providers.tsx#ThemeProviders`),n=(0,i.createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\components\auth-provider.tsx#default`);var d=s(25058);let l={applicationName:"SeaLogs",title:{default:"SeaLogs",template:"SeaLogs"},description:"SeaLogs Application",manifest:"/manifest.json",appleWebApp:{capable:!0,statusBarStyle:"default",title:"SeaLogs"},formatDetection:{telephone:!1}},c={themeColor:"#FFFFFF"};function u({children:e}){return r.jsx("html",{lang:"en",suppressHydrationWarning:!0,children:r.jsx("body",{suppressHydrationWarning:!0,children:r.jsx(d.Y,{children:r.jsx(o,{children:r.jsx(a,{children:r.jsx(n,{children:r.jsx("div",{className:"app-root",children:e})})})})})})})}},57757:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(96141),i=s(17784);let o=function({message:e="Loading ...",errorMessage:t=""}){return(0,r.jsxs)("div",{className:"h-screen w-full flex flex-col items-center justify-center",children:[r.jsx("div",{children:r.jsx(i.default,{src:"/sealogs-loading.gif",alt:"Sealogs Logo",priority:!0,width:300,height:300,unoptimized:!0})}),t?r.jsx("div",{className:"text-destructive ",children:t}):r.jsx("div",{children:e})]})}},65083:()=>{}};