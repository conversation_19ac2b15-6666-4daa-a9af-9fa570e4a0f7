(()=>{var e={};e.id=2890,e.ids=[2890],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},23158:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d}),t(79507),t(2223),t(78398),t(57757),t(48045);var a=t(40060),r=t(33581),i=t(57567),l=t.n(i),n=t(51650),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d=["",{children:["inventory",{children:["suppliers",{children:["view",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,79507)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\suppliers\\view\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,2223)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\suppliers\\view\\page.tsx"],p="/inventory/suppliers/view/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/inventory/suppliers/view/page",pathname:"/inventory/suppliers/view",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},85363:(e,s,t)=>{Promise.resolve().then(t.bind(t,71462))},13016:(e,s,t)=>{Promise.resolve().then(t.bind(t,79989))},71462:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(98768);t(60343);var r=t(64837);function i({children:e}){return a.jsx(r.Z,{children:e})}},79989:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>D});var a=t(98768),r=t(60343),i=t(72548),l=t(35024),n=t(76342),o=t(69424),d=t(13842),c=t(66263),p=t(90949),u=t(45519);let m=(0,u.ZP)`
    mutation DeleteSuppliers($ids: [ID]!) {
        deleteSuppliers(ids: $ids)
    }
`,h=(0,u.ZP)`
    mutation DeleteSupplierContacts($ids: [ID]!) {
        deleteSupplierContacts(ids: $ids)
    }
`;var x=t(14734);let v=(0,u.ZP)`
    mutation UpdateSupplierContact($input: UpdateSupplierContactInput!) {
        updateSupplierContact(input: $input) {
            id
            name
            phone
            email
            clientID
            supplierID
        }
    }
`;var g=t(71890),f=t(29054),j=t(97428);let y=(0,j.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),N=(0,j.Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),b=(0,j.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);var w=t(49008);let C=(0,j.Z)("StickyNote",[["path",{d:"M16 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8Z",key:"qazsjp"}],["path",{d:"M15 3v4a2 2 0 0 0 2 2h4",key:"40519r"}]]);var k=t(17203),S=t(75535),P=t(52269),q=t(39544),Z=t(26509),M=t(78965),E=t(50088),_=t(34376);function I({supplierID:e}){let[s,t]=(0,r.useState)(),u=(0,o.useRouter)(),[j,I]=(0,r.useState)([{name:"",email:"",phone:""}]),[D,B]=(0,r.useState)([]),[L,V]=(0,r.useState)([]);(0,d.qD)(e,e=>{t(e),e.supplierContacts.nodes.length>0&&I(e.supplierContacts.nodes.map(e=>({id:e.id,name:e.name??"",phone:e.phone??"",email:e.email??""})))});let $=async s=>{let t=document.getElementById("supplier-name").value,a=document.getElementById("supplier-website").value,r=document.getElementById("supplier-phone").value,i=document.getElementById("supplier-email").value,l=document.getElementById("supplier-address").value,n=document.getElementById("supplier-notes").value;if(""===t)return(0,_.Am)({description:"Please fill supplier's name!",variant:"destructive"});Promise.all([U({variables:{input:{id:parseInt(e.toString()),name:t,address:l,website:a,email:i,phone:r,notes:n}}}),A(),T()]).then(()=>u.back()).catch(e=>{(0,_.Am)({description:"There was a problem updating supplier",variant:"destructive"}),console.error("Error updating supplier",e)})},A=async()=>{j.forEach(async s=>{void 0===s.id?await F({variables:{input:{...s,supplierID:e}}}):await G({variables:{input:s}})})},T=async()=>{let e=new Set(s.supplierContacts.nodes.map(e=>e.id)),t=new Set(j.filter(e=>void 0!==e.id).map(e=>e.id)),a=e.difference(t);a.size>0&&await W({variables:{ids:Array.from(a)}})},[U,{loading:z}]=(0,i.D)(n.D7q,{onCompleted:e=>{},onError:e=>{console.error("mutationupdateSupplier error",e)}}),[F,{loading:R}]=(0,i.D)(x.z,{onCompleted:e=>{},onError:e=>{console.error("mutationCreateSupplierContact error",e)}}),[G,{loading:H}]=(0,i.D)(v,{onCompleted:e=>{},onError:e=>{console.error("mutationUpdateSupplierContact error",e)}}),O=async()=>{Promise.all([Y({variables:{ids:[e]}}),W({variables:{ids:s.supplierContacts.nodes.map(e=>e.id)}})]).then(()=>{(0,_.Am)({description:"Delete supplier success!"}),u.back()}).catch(e=>{console.error(e),(0,_.Am)({description:"There was a problem deleting supplier!",variant:"destructive"})})},[Y,{loading:K}]=(0,i.D)(m,{onCompleted:e=>{},onError:e=>{console.error("mutationdeleteSupplier error",e)}}),[W,{loading:X}]=(0,i.D)(h,{onCompleted:e=>{},onError:e=>{console.error("mutationdeleteSupplier error",e)}}),[J]=(0,i.D)(n.zfn,{onCompleted:e=>{let s=e.createSeaLogsFileLinks;s.id>0&&(B([...D,s]),L?V([...L,{label:s.link,value:s.id}]):V([{label:s.link,value:s.id}]))},onError:e=>{console.error("createSeaLogsFileLinksEntry error",e)}}),Q=e=>{V(L.filter(s=>s!==e))},ee=e=>(0,a.jsxs)("div",{className:"flex justify-between align-middle mr-2 w-fit",children:[a.jsx(c.default,{href:e.label,target:"_blank",className:"ml-2 ",children:e.label}),a.jsx("div",{className:"ml-2 ",children:a.jsx(E.Z,{icon:"cross_icon",action:()=>Q(e)})})]});return(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{className:"bg-muted/30 border-b",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(f.Z,{className:"h-6 w-6 text-primary"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-muted-foreground",children:"Supplier"}),a.jsx("h2",{className:"text-2xl font-medium",children:s?.name})]})]})}),(0,a.jsxs)(l.aY,{className:"p-6 space-y-8",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2 text-primary mb-2",children:[a.jsx(f.Z,{className:"h-5 w-5"}),"Supplier Details"]}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Basic information about the supplier including contact details and website."})]}),(0,a.jsxs)("div",{className:"md:col-span-2 space-y-6",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"supplier-name",className:"text-sm font-medium flex items-center gap-1",children:[a.jsx(f.Z,{className:"h-4 w-4"}),"Company Name"]}),a.jsx(g.I,{id:"supplier-name",type:"text",defaultValue:s?.name,placeholder:"Supplier name",className:"w-full"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"supplier-website",className:"text-sm font-medium flex items-center gap-1",children:[a.jsx(y,{className:"h-4 w-4"}),"Website"]}),a.jsx(g.I,{id:"supplier-website",type:"text",defaultValue:s?.website,placeholder:"Type the website and press Enter",className:"w-full",onKeyDown:async e=>{if("Enter"===e.key){let s=e.target.value;await J({variables:{input:{link:s}}})}}})]})]}),(L?.length>0||D?.length>0)&&a.jsx("div",{className:"flex flex-wrap gap-2 p-3 bg-muted/20 rounded-md",children:L?L.map(e=>a.jsx("div",{className:"inline-block",children:ee(e)},e.value)):D.map(e=>a.jsx("div",{className:"inline-block",children:ee(e)},e.value))}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"supplier-phone",className:"text-sm font-medium flex items-center gap-1",children:[a.jsx(N,{className:"h-4 w-4"}),"Phone"]}),a.jsx(g.I,{id:"supplier-phone",defaultValue:s?.phone,type:"text",placeholder:"Phone",className:"w-full"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"supplier-email",className:"text-sm font-medium flex items-center gap-1",children:[a.jsx(b,{className:"h-4 w-4"}),"Email"]}),a.jsx(g.I,{id:"supplier-email",defaultValue:s?.email,type:"email",placeholder:"Email",className:"w-full"})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{htmlFor:"supplier-address",className:"text-sm font-medium flex items-center gap-1",children:[a.jsx(w.Z,{className:"h-4 w-4"}),"Address"]}),a.jsx(P.Textarea,{id:"supplier-address",defaultValue:s?.address,placeholder:"Supplier address",className:"w-full"})]})]})]}),a.jsx(Z.Separator,{}),(0,a.jsxs)(l.Zb,{children:[a.jsx(l.Ol,{className:"p-4 bg-muted/20",children:a.jsx("h3",{className:"text-lg font-medium",children:"Contact Persons"})}),a.jsx(l.aY,{className:"p-4",children:a.jsx(p.Z,{data:j,setData:I})})]}),a.jsx(Z.Separator,{}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-medium flex items-center gap-2 text-primary mb-2",children:[a.jsx(C,{className:"h-5 w-5"}),"Notes"]}),a.jsx("p",{className:"text-sm text-muted-foreground",children:"Additional information about this supplier."})]}),a.jsx("div",{className:"md:col-span-2",children:a.jsx(P.Textarea,{id:"supplier-notes",rows:5,defaultValue:s?.notes,placeholder:"Notes",className:"w-full"})})]})]}),(0,a.jsxs)(M.V,{className:"flex justify-between p-6 bg-muted/20 border-t",children:[(0,a.jsxs)(q.Button,{variant:"outline",onClick:()=>u.back(),className:"gap-2",children:[a.jsx(k.Z,{className:"h-4 w-4"}),"Back"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(q.Button,{variant:"destructive",onClick:O,className:"gap-2",children:[a.jsx(S.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"hidden sm:inline",children:"Delete Supplier"}),a.jsx("span",{className:"sm:hidden",children:"Delete"})]}),(0,a.jsxs)(q.Button,{onClick:$,children:[a.jsx("span",{className:"hidden sm:inline",children:"Update Supplier"}),a.jsx("span",{className:"sm:hidden",children:"Update"})]})]})]})]})}function D(){let e=parseInt((0,o.useSearchParams)().get("id")||"");return a.jsx(I,{supplierID:e})}},14734:(e,s,t)=>{"use strict";t.d(s,{z:()=>r});var a=t(45519);let r=(0,a.ZP)`
    mutation CreateSupplierContact($input: CreateSupplierContactInput!) {
        createSupplierContact(input: $input) {
            id
            name
            phone
            email
            clientID
            supplierID
        }
    }
`},90949:(e,s,t)=>{"use strict";t.d(s,{Z:()=>d});var a=t(98768),r=t(13609),i=t(52016),l=t(71890),n=t(39544),o=t(60797);let d=function({data:e,setData:s}){let t=e=>{s(s=>{let t=[...s];return t.splice(e,1),t})},d=(e,t,a)=>{s(s=>{let r=[...s];return r[t][e]=a,r})};return(0,a.jsxs)("div",{className:"space-y-5",children:[e.map((e,s)=>(0,a.jsxs)("div",{className:`space-y-4 ${s>0?"border-t pt-4":""}`,children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[a.jsx(o.Label,{label:"Contact Name",htmlFor:`supplier-contact_name${s}`,className:"text-sm font-medium",children:a.jsx(l.I,{id:`supplier-contact_name${s}`,type:"text",placeholder:"Contact Name",value:e.name,onChange:e=>d("name",s,e.target.value),className:"w-full"})}),a.jsx(o.Label,{label:"Contact Phone",htmlFor:`supplier-contact_phone${s}`,className:"text-sm font-medium",children:a.jsx(l.I,{id:`supplier-contact_phone${s}`,type:"text",placeholder:"Contact Phone",value:e.phone,onChange:e=>d("phone",s,e.target.value),className:"w-full"})}),a.jsx(o.Label,{label:"Contact Email",htmlFor:`supplier-contact_email${s}`,className:"text-sm font-medium",children:a.jsx(l.I,{id:`supplier-contact_email${s}`,type:"email",value:e.email,placeholder:"Contact Email",onChange:e=>d("email",s,e.target.value),className:"w-full"})})]}),s>0&&a.jsx("div",{className:"flex justify-end",children:a.jsx(n.Button,{variant:"destructive",size:"sm",iconLeft:r.Z,onClick:()=>t(s),children:"Remove Contact"})})]},s)),a.jsx("div",{className:"flex justify-end mt-4",children:a.jsx(n.Button,{variant:"outline",iconLeft:i.Z,onClick:()=>{s(e=>[...e,{name:"",phone:"",email:""}])},children:"Add Contact"})})]})}},2223:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\inventory\layout.tsx#default`)},79507:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\inventory\suppliers\view\page.tsx#default`)},29054:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97428).Z)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},49008:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97428).Z)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},75535:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(97428).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[864,8865,3563,6263,8189,9507,7602,2935,6451,4234,2925,5394,4837,6342,3842,88],()=>t(23158));module.exports=a})();