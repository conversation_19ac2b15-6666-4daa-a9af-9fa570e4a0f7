(()=>{var e={};e.id=8206,e.ids=[8206],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},78546:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>p,pages:()=>d,routeModule:()=>b,tree:()=>c}),r(60673),r(83569),r(78398),r(57757),r(48045);var n=r(40060),i=r(33581),t=r(57567),a=r.n(t),l=r(51650),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let c=["",{children:["log-entries",{children:["pdf",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,60673)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\log-entries\\pdf\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,83569)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\log-entries\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\log-entries\\pdf\\page.tsx"],p="/log-entries/pdf/page",m={require:r,loadChunk:()=>Promise.resolve()},b=new n.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/log-entries/pdf/page",pathname:"/log-entries/pdf",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},15785:(e,s,r)=>{Promise.resolve().then(r.bind(r,50621))},11957:e=>{e.exports=function(e,s,r,n){var i=-1,t=null==e?0:e.length;for(n&&t&&(r=e[++i]);++i<t;)r=s(r,e[i],i,e);return r}},60826:e=>{e.exports=function(e){return e.split("")}},38309:e=>{var s=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;e.exports=function(e){return e.match(s)||[]}},829:e=>{e.exports=function(e,s,r,n){for(var i=e.length,t=r+(n?1:-1);n?t--:++t<i;)if(s(e[t],t,e))return t;return -1}},65337:(e,s,r)=>{var n=r(829),i=r(35447),t=r(28026);e.exports=function(e,s,r){return s==s?t(e,s,r):n(e,i,r)}},35447:e=>{e.exports=function(e){return e!=e}},5554:e=>{e.exports=function(e){return function(s){return null==e?void 0:e[s]}}},77420:e=>{e.exports=function(e,s,r){var n=-1,i=e.length;s<0&&(s=-s>i?0:i+s),(r=r>i?i:r)<0&&(r+=i),i=s>r?0:r-s>>>0,s>>>=0;for(var t=Array(i);++n<i;)t[n]=e[n+s];return t}},49513:(e,s,r)=>{var n=r(70458),i=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(i,""):e}},30482:(e,s,r)=>{var n=r(77420);e.exports=function(e,s,r){var i=e.length;return r=void 0===r?i:r,!s&&r>=i?e:n(e,s,r)}},74783:(e,s,r)=>{var n=r(65337);e.exports=function(e,s){for(var r=e.length;r--&&n(s,e[r],0)>-1;);return r}},41200:(e,s,r)=>{var n=r(65337);e.exports=function(e,s){for(var r=-1,i=e.length;++r<i&&n(s,e[r],0)>-1;);return r}},95743:(e,s,r)=>{var n=r(30482),i=r(73211),t=r(66095),a=r(16266);e.exports=function(e){return function(s){var r=i(s=a(s))?t(s):void 0,l=r?r[0]:s.charAt(0),o=r?n(r,1).join(""):s.slice(1);return l[e]()+o}}},5395:(e,s,r)=>{var n=r(11957),i=r(66124),t=r(66027),a=RegExp("['’]","g");e.exports=function(e){return function(s){return n(t(i(s).replace(a,"")),e,"")}}},28095:(e,s,r)=>{var n=r(5554)({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"});e.exports=n},73211:e=>{var s=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return s.test(e)}},11307:e=>{var s=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;e.exports=function(e){return s.test(e)}},28026:e=>{e.exports=function(e,s,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===s)return n;return -1}},66095:(e,s,r)=>{var n=r(60826),i=r(73211),t=r(92115);e.exports=function(e){return i(e)?t(e):n(e)}},70458:e=>{var s=/\s/;e.exports=function(e){for(var r=e.length;r--&&s.test(e.charAt(r)););return r}},92115:e=>{var s="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",i="[^"+s+"]",t="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",l="(?:"+r+"|"+n+")?",o="[\\ufe0e\\ufe0f]?",c="(?:\\u200d(?:"+[i,t,a].join("|")+")"+o+l+")*",d=RegExp(n+"(?="+n+")|(?:"+[i+r+"?",r,t,a,"["+s+"]"].join("|")+")"+(o+l+c),"g");e.exports=function(e){return e.match(d)||[]}},89495:e=>{var s="\ud800-\udfff",r="\\u2700-\\u27bf",n="a-z\\xdf-\\xf6\\xf8-\\xff",i="A-Z\\xc0-\\xd6\\xd8-\\xde",t="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",a="['’]",l="["+t+"]",o="["+n+"]",c="[^"+s+t+"\\d+"+r+n+i+"]",d="(?:\ud83c[\udde6-\uddff]){2}",p="[\ud800-\udbff][\udc00-\udfff]",m="["+i+"]",b="(?:"+o+"|"+c+")",h="(?:"+a+"(?:d|ll|m|re|s|t|ve))?",x="(?:"+a+"(?:D|LL|M|RE|S|T|VE))?",g="(?:[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]|\ud83c[\udffb-\udfff])?",u="[\\ufe0e\\ufe0f]?",j="(?:\\u200d(?:"+["[^"+s+"]",d,p].join("|")+")"+u+g+")*",f="(?:"+["["+r+"]",d,p].join("|")+")"+(u+g+j),N=RegExp([m+"?"+o+"+"+h+"(?="+[l,m,"$"].join("|")+")","(?:"+m+"|"+c+")+"+x+"(?="+[l,m+b,"$"].join("|")+")",m+"?"+b+"+"+h,m+"+"+x,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])","\\d+",f].join("|"),"g");e.exports=function(e){return e.match(N)||[]}},11893:(e,s,r)=>{var n=r(20533),i=r(5395)(function(e,s,r){return s=s.toLowerCase(),e+(r?n(s):s)});e.exports=i},20533:(e,s,r)=>{var n=r(16266),i=r(30531);e.exports=function(e){return i(n(e).toLowerCase())}},66124:(e,s,r)=>{var n=r(28095),i=r(16266),t=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,a=RegExp("[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]","g");e.exports=function(e){return(e=i(e))&&e.replace(t,n).replace(a,"")}},28288:e=>{e.exports=function(e){return null==e}},14826:(e,s,r)=>{var n=r(22060),i=r(49513),t=r(30482),a=r(74783),l=r(41200),o=r(66095),c=r(16266);e.exports=function(e,s,r){if((e=c(e))&&(r||void 0===s))return i(e);if(!e||!(s=n(s)))return e;var d=o(e),p=o(s),m=l(d,p),b=a(d,p)+1;return t(d,m,b).join("")}},30531:(e,s,r)=>{var n=r(95743)("toUpperCase");e.exports=n},66027:(e,s,r)=>{var n=r(38309),i=r(11307),t=r(16266),a=r(89495);e.exports=function(e,s,r){return(e=t(e),void 0===(s=r?void 0:s))?i(e)?a(e):n(e):e.match(s)||[]}},50621:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>f});var n=r(98768),i=r(69424),t=r(60343),a=r(79418),l=r(94060),o=r(81608),c=r(9707),d=r(28147),p=r(13842),m=r(75546);r(83962);var b=r(83179),h=r.n(b),x=r(56950),g=r(26100),u=r(76643);r(11893);var j=r(85283);function f(){let e=(0,i.useSearchParams)(),s=e.get("vesselID")??0,r=e.get("logentryID")??0,[b,f]=(0,t.useState)(!0),N=(0,i.useRouter)(),[v,k]=(0,t.useState)(),[y,w]=(0,t.useState)(),[T,S]=(0,t.useState)(),[_,C]=(0,t.useState)(),[L,$]=(0,t.useState)(),[O,E]=(0,t.useState)(),[F,B]=(0,t.useState)(),[R,P]=(0,t.useState)(!1),[D,A]=(0,t.useState)(!1),[I,V]=(0,t.useState)(!1),[M,G]=(0,t.useState)(),[U,q]=(0,t.useState)(),[W,H]=(0,t.useState)(),[Z,z]=(0,t.useState)(),[J,K]=(0,t.useState)(!1),[Y,Q]=(0,t.useState)(),[X,ee]=(0,t.useState)(),[es,er]=(0,t.useState)(),[en,ei]=(0,t.useState)(),[et,ea]=(0,t.useState)(),[el,eo]=(0,t.useState)(),[ec,ed]=(0,t.useState)(),[ep,em]=(0,t.useState)(),[eb,eh]=(0,t.useState)(),[ex,eg]=(0,t.useState)(),[eu,ej]=(0,t.useState)(),[ef,eN]=(0,t.useState)(),[ev,ek]=(0,t.useState)(),[ey,ew]=(0,t.useState)(!1),[eT,eS]=(0,t.useState)(!1),[e_,eC]=(0,t.useState)(!1),[eL,e$]=(0,t.useState)({checks:!1,client:!1,config:!1,crew_members:!1,daily_checks:!1,daily_comments:!1,sign_off:!1,sign_off_comments:!1,trip_report:!1,vessel:!1}),[eO,eE]=(0,t.useState)(!1),eF=e=>{let s=ey?.customisedLogBookComponents?.nodes?.filter(e=>"EventType_LogBookComponent"===e.componentClass);return!!(s?.length>0&&s[0]?.customisedComponentFields?.nodes.filter(s=>s.fieldName===e&&"Off"!==s.status).length>0)};(0,p.BJ)(+s,e=>{C(e),e$({...eL,vessel:!0})}),(0,p.s3)(e=>{w(e),e$({...eL,client:!0})});let[eB]=(0,a.t)(l.Yo,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readOneCustomisedLogBookConfig;s&&(ew(s),e$({...eL,config:!0}))},onError:e=>{console.error("queryLogBookConfig error",e)}}),[eR]=(0,a.t)(l.tg,{fetchPolicy:"cache-and-network",onCompleted:e=>{q(e.readEngine_LogBookEntrySections.nodes)},onError:e=>{console.error("Engine_LogBookEntrySection error",e)}}),[eP]=(0,a.t)(l.SB,{fetchPolicy:"cache-and-network",onCompleted:e=>{H(e.readFuel_LogBookEntrySections.nodes)},onError:e=>{console.error("Fuel_LogBookEntrySection error",e)}}),[eD]=(0,a.t)(l.gx,{fetchPolicy:"cache-and-network",onCompleted:e=>{z(e.readPorts_LogBookEntrySections.nodes)},onError:e=>{console.error("Ports_LogBookEntrySection error",e)}}),[eA]=(0,a.t)(l.Sk,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readVesselDailyCheck_LogBookEntrySections.nodes;K(s[0]),e$({...eL,daily_checks:!0}),eM({variables:{filter:{logBookEntrySectionID:{eq:s[0].id}}}})},onError:e=>{console.error("VesselDailyCheck_LogBookEntrySection error",e)}}),[eI]=(0,a.t)(l.w$,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readLogBookSignOff_LogBookEntrySections.nodes;Q(s[0]),e$({...eL,sign_off:!0}),eV({variables:{filter:{logBookEntrySectionID:{eq:s[0].id}}}})},onError:e=>{console.error("LogBookSignOff_LogBookEntrySection error",e)}}),[eV]=(0,a.t)(l.ec,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readSectionMemberComments.nodes;s&&(ej(s),e$({...eL,sign_off_comments:!0}))},onError:e=>{console.error("querySectionMemberComments error",e)}}),[eM]=(0,a.t)(l.ec,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readSectionMemberComments.nodes;s&&(eN(s),e$({...eL,daily_comments:!0}))},onError:e=>{console.error("queryDailyCheckMemberComments error",e)}}),[eG]=(0,a.t)(l.ui,{fetchPolicy:"cache-and-network",onCompleted:e=>{ee(e.readVoyageSummary_LogBookEntrySections.nodes)},onError:e=>{console.error("VoyageSummary_LogBookEntrySection error",e)}}),[eU]=(0,a.t)(l.LW,{fetchPolicy:"cache-and-network",onCompleted:e=>{eC(e.readInfringementNotices.nodes)},onError:e=>{console.error("getInfringements error",e)}}),[eq]=(0,a.t)(l.Ye,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readTripReport_LogBookEntrySections.nodes;er(s),e$({...eL,trip_report:!0});let r=s.map(e=>e.tripEvents.nodes.map(e=>e.infringementNoticeID)).flat().filter(e=>e&&0!==e);r.length>0&&eU({variables:{id:r}})},onError:e=>{console.error("TripReport_LogBookEntrySection error",e)}}),[eW]=(0,a.t)(l.Y,{fetchPolicy:"cache-and-network",onCompleted:e=>{ei(e.readCrewMembers_LogBookEntrySections.nodes),e$({...eL,crew_members:!0})},onError:e=>{console.error("CrewMembers_LogBookEntrySection error",e)}}),[eH]=(0,a.t)(l.dh,{fetchPolicy:"cache-and-network",onCompleted:e=>{eg(Array.from(new Set(e.readCrewMembers_LogBookEntrySections.nodes.flatMap(e=>e.crewMember).flatMap(e=>+e.id))))},onError:e=>{console.error("CrewMembers_LogBookEntrySection error",e)}});(0,p.d)(e=>{let s=e.filter(e=>!M?.filter(e=>"SeaLogs\\CrewMembers_LogBookEntrySection"===e.className)?.flatMap(e=>+e.ids)?.includes(e));s&&s.length>0&&eH({variables:{ids:s}})});let[eZ]=(0,a.t)(l.JS,{fetchPolicy:"cache-and-network",onCompleted:e=>{ea(e.readCrewTraining_LogBookEntrySections.nodes)},onError:e=>{console.error("CrewTraining_LogBookEntrySection error",e)}}),[ez]=(0,a.t)(l.Zy,{fetchPolicy:"cache-and-network",onCompleted:e=>{eo(e.readSupernumerary_LogBookEntrySections.nodes)},onError:e=>{console.error("Supernumerary_LogBookEntrySection error",e)}}),[eJ]=(0,a.t)(l.t1,{fetchPolicy:"cache-and-network",onCompleted:e=>{ed(e.readEngineer_LogBookEntrySections.nodes)},onError:e=>{console.error("Engineer_LogBookEntrySection error",e)}}),[eK]=(0,a.t)(l.r3,{fetchPolicy:"cache-and-network",onCompleted:e=>{em(e.readAssetReporting_LogBookEntrySections.nodes)},onError:e=>{console.error("AssetReporting_LogBookEntrySection error",e)}}),[eY]=(0,a.t)(l.UU,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readCrewWelfare_LogBookEntrySections.nodes;eh(s),eQ({variables:{filter:{logBookEntrySectionID:{eq:s[0].id}}}})},onError:e=>{console.error("CrewWelfare_LogBookEntrySection error",e)}}),[eQ]=(0,a.t)(l.ec,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readSectionMemberComments.nodes;s&&ek(s)},onError:e=>{console.error("querySectionMemberComments error",e)}}),eX=e=>{S(e),k(e.master),e$({...eL,master:!0,logbook:!0}),eB({variables:{id:e.logBook.id}}),"Locked"===e.state?V(!0):V(!1);let s=Array.from(new Set(e.logBookEntrySections.nodes.map(e=>e.className))).map(s=>({className:s,ids:e.logBookEntrySections.nodes.filter(e=>e.className===s).map(e=>e.id)}));G(s),s.find(e=>"SeaLogs\\TripReport_LogBookEntrySection"===e.className)&&e$({...eL,daily_checks:!0,daily_comments:!0}),s.find(e=>"SeaLogs\\LogBookSignOff_LogBookEntrySection"===e.className)&&e$({...eL,sign_off:!0,sign_off_comments:!0}),s.forEach(e=>{if("SeaLogs\\Engine_LogBookEntrySection"===e.className&&eR({variables:{id:e.ids}}),"SeaLogs\\Fuel_LogBookEntrySection"===e.className&&eP({variables:{id:e.ids}}),"SeaLogs\\Ports_LogBookEntrySection"===e.className&&eD({variables:{id:e.ids}}),"SeaLogs\\VesselDailyCheck_LogBookEntrySection"===e.className&&(e$({...eL,daily_checks:!1,daily_comments:!1}),eA({variables:{id:e.ids}})),"SeaLogs\\LogBookSignOff_LogBookEntrySection"===e.className&&(e$({...eL,sign_off:!1,sign_off_comments:!1}),eI({variables:{id:e.ids}})),"SeaLogs\\CrewWelfare_LogBookEntrySection"===e.className&&eY({variables:{id:e.ids}}),"SeaLogs\\VoyageSummary_LogBookEntrySection"===e.className&&eG({variables:{id:e.ids}}),"SeaLogs\\TripReport_LogBookEntrySection"===e.className&&eq({variables:{id:e.ids}}),"SeaLogs\\CrewMembers_LogBookEntrySection"===e.className){let s={};s.id={in:e.ids},eW({variables:{filter:s}})}"SeaLogs\\CrewTraining_LogBookEntrySection"===e.className&&eZ({variables:{id:e.ids}}),"SeaLogs\\Supernumerary_LogBookEntrySection"===e.className&&ez({variables:{id:e.ids}}),"SeaLogs\\Engineer_LogBookEntrySection"===e.className&&eJ({variables:{id:e.ids}}),"SeaLogs\\AssetReporting_LogBookEntrySection"===e.className&&eK({variables:{id:e.ids}})}),P(!0)},[e0]=(0,a.t)(l.MI,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readOneLogBookEntry;s&&eX(s)},onError:e=>{console.error("queryLogBookEntry error",e)}}),e4=()=>{let e=document.getElementById("logReport"),n=document.getElementById("crew_section"),i=document.getElementById("daily_checks"),t=document.getElementById("trip_report");if(n&&i&&n?.clientHeight+i?.clientHeight>2240&&n?.style.setProperty("min-height","2240px"),t&&t.clientHeight>1920){t.style.setProperty("min-height","2240px");let e=Array.from(t.children),s=0,r=document.createElement("div");r.className="page_break min-h-[2240px]",e.forEach(e=>{let n=e.clientHeight;s+n>1920&&(t.appendChild(r),(r=document.createElement("div")).className="page_break min-h-[2240px] mt-8",s=0),r.appendChild(e),s+=n}),r.children.length>0&&t.appendChild(r),t.removeAttribute("id")}e?.style.setProperty("width","1600px"),e&&(e?.style.setProperty("width","1600px"),(0,o.default)(e,{scale:1,useCORS:!0,allowTaint:!0,logging:!0,windowWidth:e.scrollWidth,windowHeight:e.scrollHeight}).then(n=>{let i=n.toDataURL("image/png"),t=new c.jsPDF("p","mm",[1400,1979],!0),a=t.getImageProperties(i),l=t.internal.pageSize.getWidth(),o=a.height*l/a.width,d=0;for(;d<n.height-1979;)0!==d&&t.addPage(),t.addImage(i,"PNG",0,-d,l,o),d+=1979;t.save("report.pdf"),N.push(`/log-entries?vesselID=${+s}&logentryID=${+r}`),e.style.width="100%"}))},e6=(e,s="FieldComment")=>{let r=ef?.length>0&&ef.filter(r=>r.fieldName===e&&r.commentType===s);return r.length>0&&r[0]},e1=(e,s="FieldComment")=>{let r=eu?.length>0&&eu.filter(r=>r.fieldName===e&&r.commentType===s);return r.length>0?r[0]:""},e2=e=>{let s=0,r=e.tripEvents?.nodes?.filter(e=>"Supernumerary"===e.eventCategory);r.length>0&&r.forEach(e=>{s+=e.supernumerary.totalGuest});let n=e.tripReport_Stops.nodes.reduce((e,s)=>e+s.paxJoined-s.paxDeparted,0);return e?.pob+en?.length+s+n},e8=[{label:"Class 1 Explosives",value:"1"},{label:"Class 2 Gases",value:"2"},{label:"Class 2.1 - Flammable gases",value:"2.1"},{label:"Class 2.2 - Non-Flammable Non-Toxic Gases",value:"2.2"},{label:"Class 2.3 - Toxic Gases",value:"2.3"},{label:"Class 3 Flammable Liquids",value:"3"},{label:"Class 4 Flammable Solids",value:"4"},{label:"Class 4.1 - Flammable Solids",value:"4.1"},{label:"Class 4.2 - Spontaneously Combustible Substances",value:"4.2"},{label:"Class 4.3 - Substances Flammable When Wet",value:"4.3"},{label:"Class 5 Oxidizing Substances and Organic Peroxides",value:"5"},{label:"Class 5.1 - Oxidising Substances",value:"5.1"},{label:"Class 5.2 - Organic Peroxides",value:"5.2"},{label:"Class 6 Toxic and Infectious Substances",value:"6"},{label:"Class 6.1 - Toxic Substances",value:"6.1"},{label:"Class 6.2 - Infectious Substances",value:"6.2"},{label:"Class 7 Radioactive Substances",value:"7"},{label:"Class 8 Corrosive Substances",value:"8"},{label:"Class 9 Miscellaneous Hazardous Substance",value:"9"}],e3=(e,s)=>e?.parentTaskingID>0?s?.tripEvents?.nodes?.filter(s=>s.eventType_Tasking.id===e.parentTaskingID)[0]?.eventType_Tasking?.operationType?.replace(/_/g," "):e?.operationType?.replace(/_/g," "),e5=(e,s)=>s.tripEvents.nodes.find(s=>s.eventType_Tasking.id===e.parentTaskingID).eventType_Tasking.towingChecklist,e7=e=>{let s=ey?.customisedLogBookComponents?.nodes?.filter(e=>"TripReport_LogBookComponent"===e.componentClass);return!!(s?.length>0&&s[0]?.customisedComponentFields?.nodes.filter(s=>s.fieldName===e&&"Off"!==s.status).length>0)},e9=(e,s)=>{let r=e.map(e=>e.name),n=Array.from(new Set(s.map(e=>e.groupTo)));return s.filter(e=>r.includes(e.fieldName)&&!n.includes(e.fieldName)).map(s=>({...s,customisedFieldTitle:e.find(e=>e.name===s.fieldName)?.label}))},se=(e,s,r=!1)=>e.filter(e=>"Ok"!==e.checked&&(0,x.w_)(e.name,ey)),ss=e=>{switch(e.value){case"Hull":return(0,n.jsxs)(n.Fragment,{children:[u.i$(ey,J)[0]?.group?.map(e=>se(e,eT).filter(e=>!e6(e.name,"FieldComment")).map(e=>e.label).join(", ")),se(u.i$(ey,J)[1]?.individual,eT,!0).map(e=>e.label).join(", ")]});case"Engine":return(0,n.jsxs)(n.Fragment,{children:[u.mW(ey,J)[0]?.group?.map(e=>se(e,eT).filter(e=>!e6(e.name,"FieldComment")).map(e=>e.label).join(", ")),se(u.mW(ey,J)[1]?.individual,eT,!0).map(e=>e.label).join(", ")]});case"Safety":return(0,n.jsxs)(n.Fragment,{children:[u.VM(ey,J)[0]?.group?.map(e=>se(e,eT).filter(e=>!e6(e.name,"FieldComment")).map(e=>e.label).join(", ")),se(u.VM(ey,J)[1]?.individual,eT,!0).map(e=>e.label).join(", ")]});case"Navigation":return(0,n.jsxs)(n.Fragment,{children:[u.Ti(ey,J)[0]?.group?.map(e=>se(e,eT).filter(e=>!e6(e.name,"FieldComment")).map(e=>e.label).join(", ")),se(u.Ti(ey,J)[1]?.individual,eT,!0).map(e=>e.label).join(", ")]});default:return eT?.filter(s=>s.fieldSet===e.title&&"Not_Ok"===s.value&&!e6(s.fieldName)).map(e=>x.N3(e.fieldName,ey)).join(", ")}},sr=e=>{switch(e.value){case"Hull":return(0,n.jsxs)(n.Fragment,{children:[u.i$(ey,J)[0]?.group?.map(s=>e9(s,eT)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e6(s.fieldName,"FieldComment")).map(e=>e.customisedFieldTitle??x.N3(e.fieldName,ey)).join(", ")).filter(e=>""!==e).join(", "),e9(u.i$(ey,J)[1]?.individual,eT)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e6(s.fieldName,"FieldComment")).map(e=>x.N3(e.fieldName,ey)).join(", ")]});case"Engine":return(0,n.jsxs)(n.Fragment,{children:[u.mW(ey,J)[0]?.group?.map(s=>e9(s,eT)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e6(s.fieldName,"FieldComment")).map(e=>e.customisedFieldTitle??x.N3(e.fieldName,ey)).join(", ")).filter(e=>""!==e).join(", "),e9(u.mW(ey,J)[1]?.individual,eT)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e6(s.fieldName,"FieldComment")).map(e=>x.N3(e.fieldName,ey)).join(", ")]});case"Safety":return(0,n.jsxs)(n.Fragment,{children:[u.VM(ey,J)[0]?.group?.map(s=>e9(s,eT)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e6(s.fieldName,"FieldComment")).map(e=>e.customisedFieldTitle??x.N3(e.fieldName,ey)).join(", ")).filter(e=>""!==e).join(", "),e9(u.VM(ey,J)[1]?.individual,eT)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e6(s.fieldName,"FieldComment")).map(e=>x.N3(e.fieldName,ey)).join(", ")]});case"Navigation":return(0,n.jsxs)(n.Fragment,{children:[u.Ti(ey,J)[0]?.group?.map(s=>e9(s,eT)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e6(s.fieldName,"FieldComment")).map(e=>e.customisedFieldTitle??x.N3(e.fieldName,ey)).join(", ")).filter(e=>""!==e).join(", "),e9(u.Ti(ey,J)[1]?.individual,eT)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e6(s.fieldName,"FieldComment")).map(e=>x.N3(e.fieldName,ey)).join(", ")]});default:return eT?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e6(s.fieldName,"FieldComment")).map(e=>x.N3(e.fieldName,ey)).join(", ")}},sn=()=>n.jsx(n.Fragment,{children:u.el(ey,eO)[1]?.individual?.filter(e=>"Ok"!==e.checked&&x.Pr(e.name,ey)).map(e=>e.label).join(", ")}),si=()=>n.jsx(n.Fragment,{children:e9(u.el(ey,eO)[1]?.individual,eO)?.filter(e=>"Ok"===e.value&&!e6(e.fieldName,"FieldComment")).map(e=>x.g(e.fieldName,ey)).join(", ")}),st=(e,s="FieldComment")=>{let r=ev?.length>0&&ev.filter(r=>r.fieldName===e&&r.commentType===s);return r.length>0&&r[0]},sa=()=>n.jsx(n.Fragment,{children:u.aV(eb[0])?.filter(e=>"Ok"!==e.checked&&!st(e.name,"FieldComment")).map(e=>e.label).join(", ")}),sl=()=>n.jsx(n.Fragment,{children:u.aV(eb[0])?.filter(e=>"Ok"===e.checked&&!st(e.name,"FieldComment")).map(e=>e.label).join(", ")}),so=[{lifeJacket:"Life Jacket infringement issued"},{speedOrNavigation:"Speed / navigation infringement issued"},{towing:"Towing infringement issued"},{swimmingOrDiving:"Swimming / diving infringement issued"},{mooring:"Mooring / embarkation / ramps or jetty infringements"},{other:"Other"},{FailingToCarryLifejackets:"Failing to carry accessible and sufficient lifejackets of appropriate size for each person on board vessel"},{FailingToWearLifejacketWhenInstructed:"Failing to wear properly secured lifejacket of appropriate size when instructed by person in charge of recreational vessel"},{FailingToEnsureLifejacketOnVessel6mOrLess:"Failing to ensure persons on recreational vessel 6 metres or less wear proper lifejacket when vessel is making way"},{FailingToEnsureLifejacketForChildrenOnVesselGreaterThan6m:"Failing to ensure persons 10 years or younger on recreational vessel greater than 6 metres wear proper lifejacket at all times"},{FailingToEnsureLifejacketOnJetBoat:"Failing to ensure persons on recreational jet boat wear proper lifejacket when making way"},{FailingToEnsureLifejacketInDanger:"Failing to ensure persons on recreational vessel wear proper lifejacket in situations of danger or risk"},{TowingWithoutLifejacketOver5Knots:"Towing person who is not wearing properly secured lifejacket of appropriate size from vessel at speed exceeding 5 knots, or being towed from vessel at speed exceeding 5 knots when not wearing properly secured lifejacket of appropriate size"},{UnsupervisedUnderagePersonOperatingVessel:"An unsupervised underage person operating powered vessel capable of exceeding 10 knots"},{Option9:"Option 9"},{AllowingUnsupervisedUnderagePerson:"Allowing an unsupervised underage person to operate powered vessel capable of exceeding 10 knots"},{Exceeding5KnotRestriction50Metres:"Exceeding 5-knot speed restriction within 50 metres of vessel, floating structure, or person"},{Exceeding5KnotRestriction200MetresShore:"Exceeding 5-knot speed restriction within 200 metres of shore or structure"},{Exceeding5KnotRestriction200MetresFlagA:"Exceeding 5-knot speed restriction within 200 metres of vessel flying Flag A of International Code of Signals"},{Exceeding5KnotRestrictionBodyExtending:"Exceeding 5-knot speed restriction while person has part of body extending from powered vessel"},{BeingTowedExceeding5Knots:"Being towed at speed of more than 5 knots in restricted-speed locations"},{NavigatingWithoutDueCare:"Navigating vessel without due care and caution or at speed or in manner so as to endanger any person"},{FailingToKeepStarboard:"Failing to ensure vessel keeps to starboard side of river channel"},{FailingToGiveWayDownstream:"Failing to give way to vessel coming downstream when going upstream"},{OperatingInUnsafeConditions:"Operating vessel in conditions that do not permit safe operation"},{Exceeding5KnotLimitLakes:"Exceeding 5-knot speed limit on specified lakes in powered vessel"},{TowingExceeding5KnotsNoLookout:"Towing person from vessel at speed exceeding 5 knots without lookout of appropriate age"},{BeingTowedExceeding5KnotsNoLookout:"Being towed from vessel at speed exceeding 5 knots without lookout of appropriate age"},{TowingExceeding5KnotsNight:"Towing person from vessel between sunset and sunrise or in restricted visibility"},{BeingTowedExceeding5KnotsNight:"Being towed from vessel between sunset and sunrise"},{ParasailingFranktonArm:"Lake Wakatipu: operating vessel involved in parasailing in Frankton arm of lake"},{CreatingNuisance:"Creating nuisance through use or control of vessel, speed of vessel, or speed of item or object towed behind or used with vessel"},{SwimmingNearJettyWithNoSwimmingSign:"Swimming, diving, jumping, or related activities from or within 50 metres of jetty or wharf with “no swimming” signage"},{SwimmingInHarbourmasterArea:"Swimming or diving in area identified by harbourmaster"},{EmbarkingOrDisembarkingWhileVesselIsMakingWay:"Embarking or disembarking while vessel is making way"},{AnchoringVesselInMannerThatObstructsPassageOfVessels:"Anchoring vessel in manner that obstructs passage of vessels or obstructs approach to wharf, pier, or jetty, or creates hazard to vessels at anchor"},{FailingToMoorVesselInSecureManner:"Failing to moor vessel in secure manner or without adequate or safe means of accessing vessel"},{PlacingObstructionInWatersLikelyToRestrictNavigation:"Placing obstruction in waters likely to restrict navigation, or cause injury, death, or damage"},{TyingVesselToNavigationAidWithoutPermission:"Tying vessel to navigation aid without permission"},{DamagingRemovingDefacingOrInterferingWithNavigationAid:"Damaging, removing, defacing, or otherwise interfering with navigation aid"},{ObstructingUseOfJettyWharfRampOrLaunchFacility:"Obstructing use of jetty, wharf, ramp, or launch facility owned or operated by Queenstown Lakes District Council"},{RefuellingVesselWithPassengersOnBoard:"Refuelling vessel with passengers on board"},{PermittingVesselToContinueAfterWaterSkiDropped:"Permitting vessel to continue onwards after water ski or similar object dropped by person being towed"},{FailingToEnsureWakeSafety:"Failing to ensure wake does not prevent people from safely using waterway, or does not cause danger or risk of danger, or does not cause risk of harm"},{CreatingNuisanceThroughVesselUse:"Creating nuisance through use or control of vessel, speed of vessel, or speed of item or object towed behind or used with vessel"},{FailingToKeepVesselSeaworthy:"Failing to keep vessel in seaworthy condition or leaving vessel sunk, stranded, or abandoned"},{FailingToConductHotWorkSafely:"Failing to conduct hot work operations in accordance with Code of Safe Working Practices for Merchant Seafarers"},{FailingToTakeFirePrecautions:"Failing to take fire precautions before or during hot work operations"},{FailingToMaintainDangerousGoodsRecord:"Failing to maintain or make available written record of dangerous goods loaded or unloaded onto vessel"},{FailingToApplyForOrganisedWaterActivity:"Failing to apply to the harbourmaster when intending to conduct organised water activity"}],sc=e=>{if(!e_)return null;let s=e_?.find(s=>s.id===e),r=JSON.parse(s?.infringementData),i=[],t=[];return s.time&&i.push({Time:s.time}),s?.geoLocation?.id>0&&i.push({Location:s.geoLocation.title}),s?.waterwaysOfficer?.id>0&&i.push({"Waterways officer":`${s.waterwaysOfficer.firstName} ${s.waterwaysOfficer.surname}`}),s?.vesselName&&i.push({"Vessel name":s.vesselName}),s?.vesselType&&i.push({"Vessel type":s.vesselType}),s?.vesselReg&&i.push({"Vessel registration":s.vesselReg}),s?.ownerFullName&&i.push({"Owner name":s.ownerFullName}),s?.ownerEmail&&i.push({"Owner email":s.ownerEmail}),s?.ownerAddress&&i.push({Address:s.ownerAddress}),s?.ownerDOB&&i.push({DOB:s.ownerDOB}),s?.ownerOccupation&&i.push({"Owner occupation":s.ownerOccupation}),s?.ownerPhone&&i.push({"Owner phone":s.ownerPhone}),r?.infringementUsed?.length>0&&(t.push({"Infringement used":r.infringementUsed.map(e=>{let s=so.find(s=>Object.keys(s)[0]===e);return s?s[e]:""}).join(", ")}),r?.lifeJacket?.length>0&&t.push({"Life Jacket infringement issued":r.lifeJacket.map(e=>{let s=so.find(s=>Object.keys(s)[0]===e);return s?s[e]:""}).join(", ")}),r?.mooring?.length>0&&t.push({"Mooring / embarkation / ramps or jetty infringements":r.mooring.map(e=>{let s=so.find(s=>Object.keys(s)[0]===e);return s?s[e]:""}).join(", ")}),r?.speedOrNavigation?.length>0&&t.push({"Speed / navigation infringement issued":r.speedOrNavigation.map(e=>{let s=so.find(s=>Object.keys(s)[0]===e);return s?s[e]:""}).join(", ")}),r?.swimmingOrDiving?.length>0&&t.push({"Swimming / diving infringement issued":r.swimmingOrDiving.map(e=>{let s=so.find(s=>Object.keys(s)[0]===e);return s?s[e]:""}).join(", ")}),r?.towing?.length>0&&t.push({"Towing infringement issued":r.towing.map(e=>{let s=so.find(s=>Object.keys(s)[0]===e);return s?s[e]:""}).join(", ")}),r?.other?.length>0&&t.push({Other:r.other.map(e=>{let s=so.find(s=>Object.keys(s)[0]===e);return s?s[e]:""}).join(", ")})),n.jsx(n.Fragment,{children:(0,n.jsxs)("tbody",{children:[i.length>0&&Array.from({length:Math.ceil(i.length/2)}).map((e,s)=>{let r=i[2*s],t=i[2*s+1];return(0,n.jsxs)("tr",{className:" border-b last:border-b-0",children:[r&&(0,n.jsxs)("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:Object.keys(r)[0]}),r[Object.keys(r)[0]]]}),t&&(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:Object.keys(t)[0]}),t[Object.keys(t)[0]]]})]},s)}),t.length>0&&Array.from({length:Math.ceil(t.length)}).map((e,s)=>{let r=t[s];return(0,n.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:n.jsx("span",{className:"w-48 inline-block",children:Object.keys(r)[0]})}),n.jsx("td",{className:"px-6 pb-4",children:r[Object.keys(r)[0]]})]},s)}),s?.otherDescription&&(0,n.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:n.jsx("span",{className:"w-48 inline-block",children:"Other description"})}),n.jsx("td",{className:"px-6 pb-4",children:n.jsx("div",{dangerouslySetInnerHTML:{__html:s.otherDescription}})})]})]})})};return n.jsx(n.Fragment,{children:R?(0,n.jsxs)("div",{children:[!Object.values(eL).some(e=>!1===e)&&(0,n.jsxs)("p",{children:["If the PDF does not download automatically,"," ",n.jsx("span",{onClick:e4,className:"inline-block underline cursor-pointer",children:"click here"})]}),n.jsx("button",{id:"downloadPdf",onClick:e4,className:"hidden",children:"Download PDF"}),(0,n.jsxs)("div",{id:"logReport",className:"w-full px-4",children:[(0,n.jsxs)("div",{className:"page_break min-h-[2240px]",children:[(0,n.jsxs)("div",{id:"crew_section",children:[(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsxs)("h5",{className:" ",children:[(0,n.jsxs)("span",{className:"font-medium",children:["Log Entry"," "]}),_?.title," -"," ",T?.startDate?(0,m.p6)(T.startDate):""]}),n.jsx(d.default,{src:"/sealogs-horizontal-logo.png",alt:"",width:220,height:50})]}),n.jsx("div",{className:"w-full h-full-screen items-center justify-center  rounded-lg",children:(0,n.jsxs)("div",{className:"w-full p-0 overflow-hidden  border-t border-l border-r  rounded-lg ",children:[n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:(0,n.jsxs)("tbody",{children:[(0,n.jsxs)("tr",{className:" border-b hover:",children:[(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Company"})," ",y?.title]}),(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Vessel"})," ",_?.title]})]}),(0,n.jsxs)("tr",{className:" border-b   ",children:[(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Master"})," ",v?.firstName," ",v?.surname]}),(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Sign off time"})," ",(0,m.p6)(Y?.created)," ",Y?.completedTime]})]}),e1("LogBookSignOff","Section")&&n.jsx("tr",{className:" border-b    ",children:n.jsx("td",{className:"px-6 pb-4",colSpan:2,children:(0,n.jsxs)("div",{className:"flex",children:[n.jsx("span",{className:"min-w-48 inline-block",children:"Sign off comment"}),n.jsx("div",{className:"inline-block",children:e1("LogBookSignOff","Section")&&e1("LogBookSignOff","Section")?.comment})]})})}),n.jsx("tr",{className:" border-b    ",children:(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"End location"})," ",Y?.endLocation?.geoLocationID>0?Y?.endLocation?.geoLocation.title:Y?.endLocation?.lat&&Y?.endLocation?.long?"Lat - "+Y?.endLocation?.lat+" Long - "+Y?.endLocation?.long:""]})})]})}),n.jsx("table",{className:"w-full  text-left rtl:text-right ",children:n.jsx("tbody",{children:(0,n.jsxs)("tr",{className:" border-b    ",children:[(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Signature"})," "]}),n.jsx("td",{className:"px-6 pb-4",children:Y?.sectionSignature?.id>0&&n.jsx(j.Z,{className:"w-1/2",id:Y?.sectionSignature?.id,alt:""})})]})})})]})}),en&&en?.filter(e=>e.crewMemberID>0).length>0&&(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"mt-8 mb-4",children:n.jsx("h5",{className:" ",children:"Crew members"})}),n.jsx("div",{className:"w-full h-full-screen  items-center justify-center  rounded-lg",children:n.jsx("div",{className:"w-full p-0 overflow-hidden  border-t border-l border-r  rounded-lg  ",children:n.jsx("div",{className:"relative overflow-x-auto",children:(0,n.jsxs)("table",{className:"w-full  text-left rtl:text-right  ",children:[n.jsx("thead",{className:"   ",children:(0,n.jsxs)("tr",{children:[n.jsx("th",{className:"px-6 pb-4",children:"Name"}),n.jsx("th",{className:"px-6 pb-4",children:"Duty"}),n.jsx("th",{className:"px-6 pb-4",children:"Sign In"}),n.jsx("th",{className:"px-6 pb-4",children:"Sign Out"})]})}),n.jsx("tbody",{children:en.filter(e=>e.crewMemberID>0).map(e=>(0,n.jsxs)("tr",{className:" border-b  ",children:[(0,n.jsxs)("td",{className:"px-6 pb-4",children:[e.crewMember.firstName," ",e.crewMember.surname]}),n.jsx("td",{className:"px-6 pb-4",children:e.dutyPerformed.title}),n.jsx("td",{className:"px-6 pb-4",children:(0,m.o0)(e.punchIn)}),n.jsx("td",{className:"px-6 pb-4",children:(0,m.o0)(e.punchOut)})]},e.crewMemberID))})]})})})})]})]}),J&&eT&&(0,n.jsxs)("div",{id:"daily_checks",children:[n.jsx("div",{className:"mt-8 mb-4",children:n.jsx("h5",{className:" ",children:"Daily Check"})}),n.jsx("div",{className:"w-full h-full-screen  items-center justify-center  rounded-lg",children:n.jsx("div",{className:"w-full p-0 overflow-hidden  border-t border-l border-r  rounded-lg  ",children:n.jsx("div",{className:"relative overflow-x-auto",children:(0,n.jsxs)("table",{className:"w-full  text-left rtl:text-right  ",children:[n.jsx("thead",{className:"    ",children:(0,n.jsxs)("tr",{children:[(0,n.jsxs)("th",{className:"px-6 pb-4",children:[" ","Section"," "]}),(0,n.jsxs)("th",{className:"px-6 pb-4",children:[" ","Fields"," "]}),(0,n.jsxs)("th",{className:"px-6 pb-4",children:[" ","Status"," "]})]})}),(0,n.jsxs)("tbody",{children:[eb&&eb.length>0&&n.jsx(n.Fragment,{children:(0,n.jsxs)("tr",{className:" border-b  ",children:[n.jsx("td",{className:"px-6 pb-4 border-r-1",children:n.jsx("div",{children:"Crew welfare"})}),n.jsx("td",{colSpan:2,children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:(0,n.jsxs)("tbody",{children:[sl()?.props?.children?.length>0&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:sl()}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Ok"})]}),u.aV(eb[0])?.filter(e=>"Ok"===e.checked&&st(e.name,"FieldComment")).map(e=>n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:`${e.label}: ${st(e.name)?.comment}`}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Ok"})]}))]}),sa()?.props?.children?.length>0&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:sa()}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]}),u.aV(eb[0])?.filter(e=>"Ok"!==e.checked&&st(e.name,"FieldComment")).map(e=>n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:`${e.label}: ${st(e.name)?.comment}`}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]}))]})]})})})]})}),[{title:"Safety Checks",value:"Safety"},{title:"Engine Checks",value:"Engine"},{title:"Jet Specific Checks",value:"JetSpecific"},{title:"Cleaning Checks",value:"Cleaning"},{title:"Navigation",value:"Navigation"},{title:"Deck operations and exterior checks",value:"Hull"},{title:"HVAC",value:"HVAC"},{title:"Plumbing",value:"Plumbing"},{title:"Sail",value:"Sail"},{title:"Biosecurity",value:"Biosecurity"}].filter(e=>eT?.filter(s=>s.fieldSet===e.title).length>0).map(e=>n.jsx(n.Fragment,{children:(0,n.jsxs)("tr",{className:" border-b  ",children:[n.jsx("td",{className:"px-6 pb-4 border-r-1",children:n.jsx("div",{children:e6(e.value,"Section")?(0,n.jsxs)(n.Fragment,{children:[e.title,n.jsx("br",{}),n.jsx("br",{}),"Comment:"," ",e6(e.value,"Section")?.comment]}):e.title})}),n.jsx("td",{colSpan:2,children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:(0,n.jsxs)("tbody",{children:[sr(e)?.props?.children?.filter(e=>""!==e)?.length>0&&(0,n.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[(0,n.jsxs)("td",{className:"px-6 pb-4",children:[sr(e),sr(e)]}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Ok"})]}),eT?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&e6(s.fieldName)).map(e=>n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:`${x.N3(e.fieldName,ey)}: ${e6(e.fieldName)?.comment}`}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Ok"})]},e.id)),ss(e)?.props?.children[0]?.filter(e=>""!==e)?.length>0||ss(e)?.props?.children[1]?.length>0&&(0,n.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:ss(e)}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},e.title),"Hull"===e.value?n.jsx(n.Fragment,{children:u.i$(ey,J)[0]?.group?.map(e=>se(e,eT).filter(e=>e6(e.name,"FieldComment")).map(e=>n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:`${e.label}: ${e6(e.name)?.comment}`}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},e.id)))}):"Engine"===e.value?n.jsx(n.Fragment,{children:u.mW(ey,J)[0]?.group?.map(e=>se(e,eT).filter(e=>e6(e.name,"FieldComment")).map(e=>n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:`${e.label}: ${e6(e.name)?.comment}`}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},e.id)))}):"Safety"===e.value?n.jsx(n.Fragment,{children:u.VM(ey,J)[0]?.group?.map(e=>se(e,eT).filter(e=>e6(e.name,"FieldComment")).map(e=>n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:`${e.label}: ${e6(e.name)?.comment}`}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},e.id)))}):"Navigation"===e.value?n.jsx(n.Fragment,{children:u.Ti(ey,J)[0]?.group?.map(e=>se(e,eT).filter(e=>e6(e.name,"FieldComment")).map(e=>n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:`${e.label}: ${e6(e.name)?.comment}`}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},e.id)))}):eT?.filter(s=>s.fieldSet===e.title&&"Not_Ok"===s.value&&e6(s.fieldName)).map(e=>n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:`${x.N3(e.fieldName,ey)}: ${e6(e.fieldName)?.comment}`}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},e.id))]})})})]},`${e.title}-1`)})),eO&&eO.length>0&&n.jsx(n.Fragment,{children:(0,n.jsxs)("tr",{className:" border-b  ",children:[n.jsx("td",{className:"px-6 pb-4 border-r-1",children:n.jsx("div",{children:"Sign off"})}),n.jsx("td",{colSpan:2,children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:(0,n.jsxs)("tbody",{children:[si()?.props?.children?.length>0&&(0,n.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:si()}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Ok"})]}),sn()?.props?.children?.length>0&&(0,n.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4",children:sn()}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]})]})})})]})})]})]})})})})]})]}),(0,n.jsxs)("div",{className:"page_break min-h-[2240px]",id:"trip_report",children:[es&&n.jsx("div",{className:"mt-8 mb-4",children:n.jsx("h5",{className:" ",children:"Trip report"})}),es&&es.map(e=>n.jsx("div",{className:"w-full mb-8 last:mb-0 h-full-screen  items-center justify-center  rounded-lg",children:n.jsx("div",{className:"w-full p-0 overflow-hidden  border-t border-l border-r  rounded-lg  ",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:(0,n.jsxs)("tbody",{children:[(0,n.jsxs)("tr",{className:" border-b    ",children:[(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Departure"})," ",e?.departTime]}),(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Depart location"})," ",e?.fromLocation?.id>0?e?.fromLocation?.title:e?.fromLocation?.lat+" "+e?.fromLocation?.long]})]}),(0,n.jsxs)("tr",{className:" border-b    ",children:[(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"P.O.B"})," ",n.jsx("div",{className:" inline-block",children:e2(e)})]}),(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Vehicles on board"})," ",e?.totalVehiclesCarried]})]}),e?.designatedDangerousGoodsSailing&&e7("DesignatedDangerousGoodsSailing")&&n.jsx("tr",{className:" border-b    ",children:n.jsx("td",{className:"px-6 pb-4",colSpan:2,children:"This is a designated dangerous goods sailing"})}),e?.dangerousGoodsRecords?.nodes?.length>0&&n.jsx("tr",{className:" border-b    ",children:(0,n.jsxs)("td",{className:"px-6 pb-4",colSpan:2,children:[n.jsx("span",{className:"w-48 inline-block",children:"Dangerous goods"}),n.jsx("div",{className:"inline-block",children:e?.dangerousGoodsRecords?.nodes?.map((e,s)=>n.jsxs(n.Fragment,{children:[s>0&&", ",e8.find(s=>s.value===e?.type)?.label," - ",n.jsx("div",{className:"inline-block",dangerouslySetInnerHTML:{__html:e?.comment}})]}))})]})}),eF("PassengerVehiclePickDrop")&&e?.dangerousGoodsChecklist?.id>0&&n.jsx("tr",{className:" border-b    ",children:n.jsx("td",{className:"",colSpan:2,children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsx("tbody",{children:(0,n.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:n.jsx("span",{className:"w-48 inline-block",children:"Dangerous goods checklist"})}),n.jsx("td",{className:"",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:(0,n.jsxs)("tbody",{children:[Object.entries(e.dangerousGoodsChecklist).filter(([e,s])=>!0===s).length>0&&(0,n.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 border-r-1",children:Object.entries(e.dangerousGoodsChecklist).filter(([e,s])=>!0===s).map(([e,s])=>({key:e,value:s})).map(e=>e.key.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),n.jsx("td",{className:"px-6 pb-4",children:"Ok"})]}),Object.entries(e.dangerousGoodsChecklist).filter(([e,s])=>!1===s).length>0&&(0,n.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 border-r-1",children:Object.entries(e.dangerousGoodsChecklist).filter(([e,s])=>!1===s).map(([e,s])=>({key:e,value:s})).map(e=>e.key.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]}),e?.dangerousGoodsChecklist?.riskFactors?.nodes?.length>0&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsx("td",{className:"px-6 pb-4",colSpan:2,children:e?.dangerousGoodsChecklist.riskFactors.nodes.map((e,s)=>n.jsxs("div",{className:"inline-block",children:[s>0?", ":"Risk factors: ",`${e.title} - ${e.impact} - ${e.probability}/10`]},e.id))})}),e?.dangerousGoodsChecklist?.member?.id>0&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsx("td",{className:"px-6 pb-4",colSpan:2,children:`Author: ${e.dangerousGoodsChecklist.member?.firstName} ${e.dangerousGoodsChecklist.member?.surname}`})})]})})})]})})})})}),e?.tripReport_Stops?.nodes?.length>0&&n.jsx(n.Fragment,{children:e?.tripReport_Stops?.nodes?.map(e=>n.jsx("tr",{className:" border-b    ",children:n.jsx("td",{className:"",colSpan:2,children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsx("tbody",{children:n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:n.jsx("span",{className:"w-48 inline-block",children:"Trip stop"})}),n.jsx("td",{className:"",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsxs("tbody",{children:[n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${e?.stopLocation?.id>0?e?.stopLocation?.title:""}`]}),n.jsx("td",{className:"px-6 pb-4",children:e?.designatedDangerousGoodsSailing&&e7("DesignatedDangerousGoodsSailing")?"This is a designated dangerous goods sailing":"-"})]},`${e.id}-stop-location`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Arrival"}),`${e?.arriveTime}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Departure"}),`${e?.departTime}`]})]},`${e.id}-arr-time`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Pax joined"}),`${e?.paxJoined}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Pax departed"}),`${e?.paxDeparted}`]})]},`${e.id}-pax`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Vehicles joined"}),`${e?.vehiclesJoined}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Vehicles departed"}),`${e?.vehiclesDeparted}`]})]},`${e.id}-vehicles`),e?.otherCargo&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[n.jsx("span",{className:"w-48 inline-block",children:"Other cargo: "}),`${e?.otherCargo}`]})},`${e.id}-otherCargo`),e?.comments&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[n.jsx("span",{className:"w-48 inline-block",children:"Comments: "}),`${e?.comments}`]})},`${e.id}-comments`)]})})})]})})})})}))}),e?.tripEvents?.nodes?.length>0&&n.jsx(n.Fragment,{children:n.jsx("tr",{className:" border-b    ",children:n.jsx("td",{className:"",colSpan:2,children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsx("tbody",{children:e?.tripEvents?.nodes?.map(s=>n.jsxs(n.Fragment,{children:["BarCrossing"===s.eventCategory&&n.jsx(n.Fragment,{children:n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:n.jsx("span",{className:"w-48 inline-block",children:"Activity - Bar crossing"})}),n.jsx("td",{className:"",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsxs("tbody",{children:[n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Event Type"}),"Bar crossing"]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${s?.eventType_BarCrossing?.geoLocation?.id>0?s?.eventType_BarCrossing?.geoLocation?.title:""}`]})]},`${s.eventType_BarCrossing.id}-bc-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Crossing start"}),`${s?.eventType_BarCrossing.time}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Crossing end"}),`${s?.eventType_BarCrossing.timeCompleted}`]})]},`${s.id}-crossing-time`),s?.eventType_BarCrossing?.barCrossingChecklist?.id>0&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsx("td",{className:"",colSpan:2,children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsx("tbody",{children:n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:n.jsx("span",{className:"w-48 inline-block",children:"Bar crossing checklist"})}),n.jsx("td",{className:"p-0",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsxs("tbody",{children:[Object.entries(s?.eventType_BarCrossing?.barCrossingChecklist).filter(([e,s])=>!0===s).length>0&&n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 border-r-1",children:Object.entries(s?.eventType_BarCrossing?.barCrossingChecklist).filter(([e,s])=>!0===s).map(([e,s])=>e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),n.jsx("td",{className:"px-6 pb-4",children:"Ok"})]},`${s.id}-checklist-y`),Object.entries(s?.eventType_BarCrossing?.barCrossingChecklist).filter(([e,s])=>!1===s).length>0&&n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 border-r-1",children:Object.entries(s?.eventType_BarCrossing?.barCrossingChecklist).filter(([e,s])=>!1===s).map(([e,s])=>e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},`${s.id}-checklist-n`)]})})})]})})})})},`${s.id}-checklist`),e?.eventType_BarCrossing?.barCrossingChecklist?.riskFactors?.nodes?.length>0&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsx("td",{className:"px-6 pb-4",colSpan:2,children:e?.eventType_BarCrossing?.barCrossingChecklist.riskFactors.nodes.map((e,s)=>n.jsxs("div",{className:"inline-block",children:[s>0?", ":"Risk factors: ",`${e.title} - ${e.impact} - ${e.probability}/10`]},e.id))})})]})})})]})}),"RestrictedVisibility"===s.eventCategory&&n.jsx(n.Fragment,{children:n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:n.jsx("span",{className:"w-48 inline-block",children:"Activity - Restricted visibility"})}),n.jsx("td",{className:"",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsxs("tbody",{children:[n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[n.jsx("span",{className:"w-48 inline-block",children:"Event Type"}),"Restricted visibility"]})},`${s.eventType_RestrictedVisibility.id}-rv-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Start location"}),`${s?.eventType_RestrictedVisibility?.startLocation?.id>0?s?.eventType_RestrictedVisibility?.startLocation?.title:""}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"End location"}),`${s?.eventType_RestrictedVisibility?.endLocation?.id>0?s?.eventType_RestrictedVisibility?.endLocation?.title:""}`]})]},`${s.eventType_RestrictedVisibility.id}-locationrv`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Crossing Time"}),`${s?.eventType_RestrictedVisibility?.crossingTime}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Crossed time"}),`${s?.eventType_RestrictedVisibility?.crossedTime}`]})]},`${s.id}-locationbc`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Safe speed"}),`${s?.eventType_RestrictedVisibility?.estSafeSpeed}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Avg speed"}),`${s?.eventType_RestrictedVisibility?.approxSafeSpeed}`]})]},`${s.id}-speed`),n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsx("td",{className:"",colSpan:2,children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsx("tbody",{children:n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:n.jsx("span",{className:"w-48 inline-block",children:"Safe operating procedures checklist"})}),n.jsx("td",{className:"p-0",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsxs("tbody",{children:[Object.entries(s?.eventType_RestrictedVisibility).filter(([e,s])=>!0===s).length>0&&n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 border-r-1",children:Object.entries(s?.eventType_RestrictedVisibility).filter(([e,s])=>!0===s).map(([e,s])=>e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),n.jsx("td",{className:"px-6 pb-4",children:"Ok"})]},`${s.id}-checklist-y`),Object.entries(s?.eventType_RestrictedVisibility).filter(([e,s])=>!1===s).length>0&&n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 border-r-1",children:Object.entries(s?.eventType_RestrictedVisibility).filter(([e,s])=>!1===s).map(([e,s])=>e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},`${s.id}-checklist-n`)]})})})]})})})})},`${s.id}-checklist`)]})})})]})}),"CrewTraining"===s.eventCategory&&n.jsx(n.Fragment,{children:n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:n.jsx("span",{className:"w-48 inline-block",children:"Activity - Training"})}),n.jsx("td",{className:"",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsxs("tbody",{children:[n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Event type"}),"Crew training"]}),n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Training date"}),`${m.p6(s.crewTraining.date)}`]})]},`${s.crewTraining.id}-ct-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Start Time"}),`${s?.crewTraining?.startTime}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"End time"}),`${s?.crewTraining?.finishTime}`]})]},`${s.crewTraining.id}-locationbc`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Trainer"}),`${s?.crewTraining?.trainer?.id>0?s?.crewTraining?.trainer?.firstName+" "+s?.crewTraining?.trainer?.surname:""}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${s?.crewTraining?.geoLocation?.id>0?s?.crewTraining?.geoLocation?.title:""}`]})]},`${s.crewTraining.id}-trainer`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Crew trained"}),n.jsx("div",{className:"inline-block max-w-[calc(100%-12rem)]",children:s?.crewTraining?.members?.nodes?.map((e,s)=>n.jsxs(n.Fragment,{children:[s>0&&", ",`${e.firstName} ${e.surname}`]}))})]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Training type"}),n.jsx("div",{className:"inline-block max-w-[calc(100%-12rem)]",children:s?.crewTraining?.trainingTypes?.nodes?.map((e,s)=>n.jsxs(n.Fragment,{children:[s>0&&", ",`${e.title}`]}))})]})]},`${s.crewTraining.id}-crew-trained`),s?.crewTraining?.trainingSummary&&n.jsx("tr",{className:" border-b last:border-b-0    ",children:n.jsx("td",{className:"px-6 pb-4",colSpan:2,children:n.jsxs("div",{className:"flex",children:[n.jsx("span",{className:"min-w-48 inline-block",children:"Training summary"}),n.jsx("div",{className:"inline-block",children:n.jsx("div",{dangerouslySetInnerHTML:{__html:s?.crewTraining?.trainingSummary}})})]})})})]})})})]})}),"Tasking"===s.eventCategory&&n.jsx(n.Fragment,{children:n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:n.jsx("span",{className:"w-48 inline-block",children:"Activity - Tasking"})}),n.jsx("td",{className:"",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsxs("tbody",{children:[n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Event type"}),`${s?.eventType_Tasking?.type.replace(/([A-Z])/g," $1")}`]}),n.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Time"}),`${s?.eventType_Tasking?.time}`]})]},`${s.eventType_Tasking.id}-et-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Title"}),`${s?.eventType_Tasking?.title}`]}),n.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${s?.eventType_Tasking?.geoLocation?.id>0?s?.eventType_Tasking?.geoLocation?.title:""}`]})]},`${s.eventType_Tasking.id}-et2-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Activity type"}),e3(s.eventType_Tasking,e)]}),n.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Fuel"}),`${s?.eventType_Tasking?.fuelLog?.nodes?.length?s?.eventType_Tasking?.fuelLog?.nodes[0]?.fuelAfter:"-"}`]})]},`${s.eventType_Tasking.id}-et3-type`),s?.eventType_Tasking?.vesselRescueID>0&&s?.eventType_Tasking?.type==="TaskingStartUnderway"&&n.jsxs(n.Fragment,{children:[n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Target vessel"}),`${s?.eventType_Tasking?.vesselRescue?.vesselName}`]}),n.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Call sign"}),`${s?.eventType_Tasking?.vesselRescue?.callSign}`]})]},`${s.eventType_Tasking.id}-et-vr1-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"P.O.B"}),`${s?.eventType_Tasking?.vesselRescue?.pob}`]}),n.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${s?.eventType_Tasking?.vesselRescue?.vesselLocation?.id>0?s?.eventType_Tasking?.vesselRescue?.vesselLocation?.title:""}`]})]},`${s.eventType_Tasking.id}-et-vr2-type`),s?.eventType_Tasking?.vesselRescue?.locationDescription&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[n.jsx("span",{className:"w-48 inline-block",children:"Location description"}),n.jsx("div",{className:"inline-block max-w-[calc(100%-12rem)]",children:`${s?.eventType_Tasking?.vesselRescue?.locationDescription}`})]})},`${s.eventType_Tasking.id}-et-vr3-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Vessel length"}),`${s?.eventType_Tasking?.vesselRescue?.vesselLength}`]}),n.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Vessel type"}),`${s?.eventType_Tasking?.vesselRescue?.vesselType}`]})]},`${s.eventType_Tasking.id}-et-vr4-type`),s?.eventType_Tasking?.vesselRescue?.vesselTypeDescription&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[n.jsx("span",{className:"w-48 inline-block",children:"Vessel description"}),n.jsx("div",{className:"inline-block max-w-[calc(100%-12rem)]",children:`${s?.eventType_Tasking?.vesselRescue?.vesselTypeDescription}`})]})},`${s.eventType_Tasking.id}-et-vr5-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Make and model"}),`${s?.eventType_Tasking?.vesselRescue?.makeAndModel}`]}),n.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Vessel color"}),`${s?.eventType_Tasking?.vesselRescue?.color}`]})]},`${s.eventType_Tasking.id}-et-vr6-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Owner name"}),`${s?.eventType_Tasking?.vesselRescue?.ownerName}`]}),n.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Owner on board?"}),`${s?.eventType_Tasking?.vesselRescue?.ownerOnBoard?"Yes":"No"}`]})]},`${s.eventType_Tasking.id}-et-vr7-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Owner phone"}),`${s?.eventType_Tasking?.vesselRescue?.phone}`]}),n.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"CG Membership"}),`${s?.eventType_Tasking?.vesselRescue?.cgMembership}`]})]},`${s.eventType_Tasking.id}-et-vr8-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Owner email"}),`${s?.eventType_Tasking?.vesselRescue?.email}`]}),n.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[n.jsx("span",{className:"w-48 inline-block",children:"Owner address"}),`${s?.eventType_Tasking?.vesselRescue?.address}`]})]},`${s.eventType_Tasking.id}-et-vr9-type`),s?.eventType_Tasking?.cgop&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[n.jsx("span",{className:"w-48 inline-block",children:"Incident details"}),`CoastGuard Rescue - ${s?.eventType_Tasking?.cgop}`]})},`${s.eventType_Tasking.id}-et-incident-cgop`),s?.eventType_Tasking?.sarop&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[n.jsx("span",{className:"w-48 inline-block",children:"Incident details"}),`SAROP - ${s?.eventType_Tasking?.sarop}`]})},`${s.eventType_Tasking.id}-et-incident-sarop`)]}),s?.eventType_Tasking?.vesselRescueID>0&&s?.eventType_Tasking?.type==="TaskingComplete"&&n.jsxs(n.Fragment,{children:[s?.eventType_Tasking?.vesselRescue?.mission?.id>0&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[n.jsx("span",{className:"w-48 inline-block",children:"Mission outcome"}),`${s?.eventType_Tasking?.vesselRescue?.mission?.operationOutcome?.replace(/_/g," ")}`]})},`${s.eventType_Tasking.id}-et-mission`),s?.eventType_Tasking?.vesselRescue?.missionTimeline?.nodes?.length>0&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsxs("td",{className:"px-6",colSpan:2,children:[n.jsx("div",{className:"pb-4",children:"Mission notes/comments"}),n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsx("tbody",{children:s?.eventType_Tasking?.vesselRescue?.missionTimeline?.nodes?.map(e=>n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"pr-6 pb-4",children:`${m.o0(e?.time)} - ${e?.commentType}`}),n.jsx("td",{className:"px-6 pb-4",children:n.jsx("div",{dangerouslySetInnerHTML:{__html:e?.description}})}),n.jsx("td",{className:"px-6 pb-4",children:`${e?.author?.id>0?e?.author?.firstName+" "+e?.author?.surname:""}`})]},`${e.id}-et-mission-timeline`))})})]})},`${s.eventType_Tasking.id}-et-mission-timeline`)]}),s?.eventType_Tasking?.vesselRescueID>0&&s?.eventType_Tasking?.type==="TaskingOnTow"&&n.jsx(n.Fragment,{children:s?.eventType_Tasking?.vesselRescue?.mission?.id>0&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsxs("td",{className:"px-6",colSpan:2,children:[n.jsx("div",{className:"pb-4",children:"Towing checklist - risk analysis"}),n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsxs("tbody",{children:[Object.entries(e5(s.eventType_Tasking,e)).filter(([e,s])=>!0===s).length>0&&n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"pr-6 pb-4 border-r-1",children:Object.entries(e5(s.eventType_Tasking,e)).filter(([e,s])=>!0===s).map(([e,s])=>({key:e,value:s})).map(e=>e.key.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),n.jsx("td",{className:"px-6 pb-4",children:"Ok"})]}),Object.entries(e5(s.eventType_Tasking,e)).filter(([e,s])=>!1===s).length>0&&n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"pr-6 pb-4 border-r-1",children:Object.entries(e5(s.eventType_Tasking,e)).filter(([e,s])=>!1===s).map(([e,s])=>({key:e,value:s})).map(e=>e.key.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),n.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]}),e5(s.eventType_Tasking,e)?.riskFactors?.nodes?.length>0&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsx("td",{className:"pr-6 pb-4",colSpan:2,children:e5(s.eventType_Tasking,e)?.riskFactors?.nodes?.map((e,s)=>n.jsxs("div",{className:"inline-block",children:[s>0?", ":"Risk factors: ",`${e.title} - ${e.impact} - ${e.probability}/10`]},e.id))})}),e5(s.eventType_Tasking,e)?.member?.id>0&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsx("td",{className:"pr-6 pb-4",colSpan:2,children:`Author: ${e5(s.eventType_Tasking,e)?.member?.firstName} ${e5(s.eventType_Tasking,e)?.member?.surname}`})})]})})]})},`${s.eventType_Tasking.id}-et-mission`)})]})})})]})}),"RefuellingBunkering"===s.eventCategory&&n.jsx(n.Fragment,{children:n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:n.jsx("span",{className:"w-48 inline-block",children:"Activity - Refueling and Bunkering"})}),n.jsx("td",{className:"",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsxs("tbody",{children:[n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Date"}),`${m.o0(s?.eventType_RefuellingBunkering?.date)}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${s?.eventType_RefuellingBunkering?.geoLocation?.id>0?s?.eventType_RefuellingBunkering?.geoLocation?.title:""}`]})]},`${s.eventType_RefuellingBunkering.id}-rb-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Fuel added"}),`${s?.eventType_RefuellingBunkering?.fuelLog?.nodes?.length?s?.eventType_RefuellingBunkering?.fuelLog?.nodes[0]?.fuelAdded:"-"}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Fuel level"}),`${s?.eventType_RefuellingBunkering?.fuelLog?.nodes?.length?s?.eventType_RefuellingBunkering?.fuelLog?.nodes[0]?.fuelAfter:"-"}`]})]},`${s.id}-fuel-details`),s?.eventType_RefuellingBunkering?.notes&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsx("td",{className:"px-6 pb-4",colSpan:2,children:n.jsxs("div",{className:"flex",children:[n.jsx("span",{className:"min-w-48 inline-block",children:"Notes"}),n.jsx("div",{className:"inline-block",children:n.jsx("div",{dangerouslySetInnerHTML:{__html:s?.eventType_RefuellingBunkering?.notes}})})]})})})]})})})]})}),"PassengerDropFacility"===s.eventCategory&&n.jsx(n.Fragment,{children:n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:n.jsx("span",{className:"w-48 inline-block",children:`Activity - ${s?.eventType_PassengerDropFacility?.type.replace("Passenger","")}`})}),n.jsx("td",{className:"",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsxs("tbody",{children:[n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Title"}),`${s?.eventType_PassengerDropFacility?.title}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Time"}),`${s?.eventType_PassengerDropFacility?.time}`]})]},`${s.eventType_PassengerDropFacility.id}-pdf-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${s?.eventType_PassengerDropFacility?.geoLocation?.id>0?s?.eventType_PassengerDropFacility?.geoLocation?.title:"-"}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Fuel"}),`${s?.eventType_PassengerDropFacility?.fuelLog?.nodes?.length?s?.eventType_PassengerDropFacility?.fuelLog?.nodes[0]?.fuelAfter:"-"}`]})]},`${s.id}-location-fuel`)]})})})]})}),"EventSupernumerary"===s.eventCategory&&n.jsx(n.Fragment,{children:n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:n.jsx("span",{className:"w-48 inline-block",children:"Activity - Supernumerary"})}),n.jsx("td",{className:"",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:n.jsxs("tbody",{children:[n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Title"}),`${s?.supernumerary?.title}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Briefing time"}),`${s?.supernumerary?.briefingTime?s?.supernumerary?.briefingTime:"-"}`]})]},`${s.supernumerary.id}-sup-type`),n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[n.jsx("span",{className:"w-48 inline-block",children:"Total guests"}),`${s?.supernumerary?.totalGuest}`]}),n.jsxs("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Is briefed?"}),`${s?.supernumerary?.isBriefed?"Yes":"No"}`]})]},`${s.id}-sup-time`),s?.supernumerary?.guestList?.nodes?.length>0&&n.jsx("tr",{className:" border-b last:border-b-0  ",children:n.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[n.jsx("span",{className:"w-48 inline-block",children:"Guests list"}),s?.supernumerary?.guestList?.nodes?.map(e=>n.jsx("div",{children:`${e.firstName} ${e.surname}`},`${e.id}-sup-gl`))]})},`${s.id}-guest-list`)]})})})]})}),"InfringementNotice"===s.eventCategory&&n.jsx(n.Fragment,{children:n.jsxs("tr",{className:" border-b last:border-b-0  ",children:[n.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:n.jsx("span",{className:"w-48 inline-block",children:"Activity - InfringementNotice"})}),n.jsx("td",{className:"",children:n.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:sc(s.infringementNoticeID)})})]})})]}))})})})})}),(0,n.jsxs)("tr",{className:" border-b    ",children:[(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Arrival"}),`Expected time: ${e?.arriveTime} - Actual time: ${h()(e?.arrive).format("HH:mm:ss")}`]}),(0,n.jsxs)("td",{className:"px-6 pb-4",children:[n.jsx("span",{className:"w-48 inline-block",children:"Arrival location"})," ",e?.toLocation?.id>0?e?.toLocation?.title:e?.toLocation?.lat+" "+e?.toLocation?.long]})]}),e?.comment&&n.jsx("tr",{className:" border-b    ",children:n.jsx("td",{className:"px-6 pb-4",colSpan:2,children:(0,n.jsxs)("div",{className:"flex",children:[n.jsx("span",{className:"min-w-48 inline-block",children:"Comment"}),n.jsx("div",{className:"inline-block",children:n.jsx("div",{dangerouslySetInnerHTML:{__html:e?.comment}})})]})})})]})})})},e.id))]})]})]}):n.jsx(g.Z,{message:"Generating the PDF report..."})})}},85283:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a});var n=r(98768);r(60343);var i=r(83048);let t=new(r.n(i)()).S3({endpoint:"https://ddde1c1cd1aa25641691808dcbafdeb7.r2.cloudflarestorage.com",accessKeyId:"06c3e13a539f24e6fdf7075bf381bf5e",secretAccessKey:"****************************************************************",signatureVersion:"v4",region:"auto"});async function a(e){let s="signature/"+e.id+".png";try{let r=await t.getObject({Bucket:"signature",Key:s}).promise();if(r.Body){let s=Buffer.from(r.Body).toString("base64"),i=`data:image/png;base64,${s}`;return n.jsx("img",{src:i,alt:e?.alt,width:e?.width,height:e?.height,className:e?.className})}}catch(e){console.error(e)}return n.jsx(n.Fragment,{})}},60673:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\log-entries\pdf\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),n=s.X(0,[864,8865,3563,6263,8189,9507,7602,2935,9707,1608,6451,4234,2925,5394,4837,3842,7033,2654],()=>r(78546));module.exports=n})();