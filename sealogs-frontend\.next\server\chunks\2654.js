exports.id=2654,exports.ids=[2654],exports.modules={61707:(e,r,a)=>{Promise.resolve().then(a.bind(a,23745))},76643:(e,r,a)=>{"use strict";a.d(r,{$9:()=>v,$o:()=>E,$y:()=>g,Ak:()=>L,BX:()=>s,CH:()=>t,CO:()=>h,Dl:()=>p,FY:()=>F,Fp:()=>i,Lb:()=>T,P$:()=>R,Q$:()=>k,RA:()=>f,Ti:()=>x,VH:()=>W,VM:()=>B,Wk:()=>P,XS:()=>C,aV:()=>I,bA:()=>A,el:()=>D,fk:()=>b,g:()=>w,g6:()=>m,hs:()=>S,i$:()=>o,lE:()=>d,ld:()=>c,mW:()=>H,nx:()=>u,uD:()=>y,w5:()=>M});var l=a(98768),n=a(56950);let s=(e,r)=>[{name:"Hull_HullStructure",label:"Hull structure",value:"hull_HullStructure",sortOrder:(0,n.L9)("Hull_HullStructure",e),checked:r?.hull_HullStructure},{name:"HullStructure",label:"Hull and superstructure inspections",value:"hullStructure",sortOrder:(0,n.L9)("HullStructure",e),checked:r?.hullStructure},{name:"PontoonPressure",label:"Pontoon pressure (for inflatables)",value:"pontoonPressure",sortOrder:(0,n.L9)("PontoonPressure",e),checked:r?.pontoonPressure},{name:"BungsInPlace",label:"Bungs in place",value:"bungsInPlace",sortOrder:(0,n.L9)("BungsInPlace",e),checked:r?.bungsInPlace},{name:"Hatches",label:"Hatches, doors, windows, and portholes security for watertight integrity",value:"hatches",sortOrder:(0,n.L9)("Hatches",e),checked:r?.hatches}],i=(e,r)=>[{name:"Hull_DeckEquipment",label:"Deck Equipment",value:"hull_DeckEquipment",sortOrder:(0,n.L9)("Hull_DeckEquipment",e),checked:r?.hull_DeckEquipment},{name:"DeckEquipment",label:"Check deck equipment (lines, fenders, etc.), cleats, chocks, rails, lifelines, loose equipment",value:"deckEquipment",sortOrder:(0,n.L9)("DeckEquipment",e),checked:r?.deckEquipment},{name:"SwimPlatformLadder",label:"Swim platform, ladder",value:"swimPlatformLadder",sortOrder:(0,n.L9)("SwimPlatformLadder",e),checked:r?.swimPlatformLadder},{name:"BiminiTopsCanvasCovers",label:"Bimini tops, canvas covers",value:"biminiTopsCanvasCovers",sortOrder:(0,n.L9)("BiminiTopsCanvasCovers",e),checked:r?.biminiTopsCanvasCovers}],t=(e,r)=>[{name:"Hull_DayShapes",label:"Day shapes",value:"dayShapes",sortOrder:(0,n.L9)("Hull_DayShapes",e),checked:r?.dayShapes},{name:"DayShapes",label:"Day shapes",value:"dayShapes",sortOrder:(0,n.L9)("DayShapes",e),checked:r?.dayShapes},{name:"HullNavigationLights",label:"Navigation lights",value:"dayShapes",sortOrder:(0,n.L9)("HullNavigationLights",e),checked:r?.dayShapes}],c=(e,r)=>[{name:"Anchor",label:"Anchor, warps, chains, windlass/hauling equipment",value:"anchor",sortOrder:(0,n.L9)("Anchor",e),checked:r?.anchor},{name:"WindscreenCheck",label:"Windscreen check",value:"windscreenCheck",sortOrder:(0,n.L9)("WindscreenCheck",e),checked:r?.windscreenCheck},{name:"NightLineDockLinesRelease",label:"Night line / dock lines release",value:"nightLineDockLinesRelease",sortOrder:(0,n.L9)("NightLineDockLinesRelease",e),checked:r?.nightLineDockLinesRelease},{name:"TenderOperationalChecks",label:"Tender operational checks",value:"tenderOperationalChecks",sortOrder:(0,n.L9)("TenderOperationalChecks",e),checked:r?.tenderOperationalChecks}],o=(e,r)=>[{group:[s(e,r),i(e,r),t(e,r)]},{individual:[...c(e,r)]}],d=(e,r)=>[{name:"PreEngineAndPropulsion",label:"Engine and propulsion",value:"preEngineAndPropulsion",sortOrder:(0,n.L9)("PreEngineAndPropulsion",e),checked:r?.preEngineAndPropulsion},{name:"FuelShutoffs",label:"Fuel shutoffs",value:"fuelShutoffs",sortOrder:(0,n.L9)("FuelShutoffs",e),checked:r?.fuelShutoffs},{name:"Separators",label:"Separators",value:"separators",sortOrder:(0,n.L9)("Separators",e),checked:r?.separators},{name:"FuelFilters",label:"Fuel Filters",value:"fuelFilters",sortOrder:(0,n.L9)("FuelFilters",e),checked:r?.fuelFilters},{name:"EngineCheckPropellers",label:"Propellers",value:"engineCheckPropellers",sortOrder:(0,n.L9)("EngineCheckPropellers",e),checked:r?.engineCheckPropellers}],h=(e,r)=>[{name:"EngineOilWater",label:"Engine oil and water",value:"engineOilWater",sortOrder:(0,n.L9)("EngineOilWater",e),checked:r?.engineOilWater},{name:"EngineOil",label:"Check engine oil levels",value:"engineOil",sortOrder:(0,n.L9)("EngineOil",e),checked:r?.engineOil},{name:"OilWater",label:"Oil and water pressure within range",value:"oilWater",sortOrder:(0,n.L9)("OilWater",e),checked:r?.oilWater}],u=(e,r)=>[{name:"EngineMountsAndStabilisers",label:"Engine mounts and stabilisers",value:"engineMountsAndStabilisers",sortOrder:(0,n.L9)("EngineMountsAndStabilisers",e),checked:r?.engineMountsAndStabilisers},{name:"Stabilizers",label:"Inspect stabilizers",value:"stabilizers",sortOrder:(0,n.L9)("Stabilizers",e),checked:r?.stabilizers},{name:"EngineMounts",label:"Engine mounts",value:"engineMounts",sortOrder:(0,n.L9)("EngineMounts",e),checked:r?.engineMounts}],g=(e,r)=>[{name:"ElectricalChecks",label:"Battery checks",value:"electricalChecks",sortOrder:(0,n.L9)("ElectricalChecks",e),checked:r?.electricalChecks},{name:"PropulsionBatteriesStatus",label:"Starter battery, alternator, generator status",value:"batteries",sortOrder:(0,n.L9)("PropulsionBatteriesStatus",e),checked:r?.batteries},{name:"HouseBatteriesStatus",label:"House battery status",value:"houseBatteriesStatus",sortOrder:(0,n.L9)("HouseBatteriesStatus",e),checked:r?.houseBatteriesStatus}],m=(e,r)=>[{name:"ElectricalVisualFields",label:"Electrical Visual Inspection",value:"electricalPanels",sortOrder:(0,n.L9)("ElectricalVisualFields",e),checked:r?.electricalPanels},{name:"ElectricalPanels",label:"Visual inspection of electrical panels ",value:"electricalPanels",sortOrder:(0,n.L9)("ElectricalPanels",e),checked:r?.electricalPanels},{name:"Wiring",label:"Wiring",value:"wiring",sortOrder:(0,n.L9)("Wiring",e),checked:r?.wiring}],k=(e,r)=>[{name:"SteeringChecks",label:"Steering",value:"steeringChecks",sortOrder:(0,n.L9)("SteeringChecks",e),checked:r?.steeringChecks},{name:"SteeringFluid",label:"Steering Fluid",value:"steeringFluid",sortOrder:(0,n.L9)("SteeringFluid",e),checked:r?.steeringFluid},{name:"SteeringRams",label:"Rams",value:"steeringRams",sortOrder:(0,n.L9)("SteeringRams",e),checked:r?.steeringRams},{name:"SteeringHydraulicSystems",label:"Hydraulic systems",value:"steeringHydraulicSystems",sortOrder:(0,n.L9)("SteeringHydraulicSystems",e),checked:r?.steeringHydraulicSystems},{name:"SteeringHoses",label:"Steering hoses",value:"steeringHoses",sortOrder:(0,n.L9)("SteeringHoses",e),checked:r?.steeringHoses},{name:"SteeringRudders",label:"Rudders",value:"steeringRudders",sortOrder:(0,n.L9)("SteeringRudders",e),checked:r?.steeringRudders},{name:"ThrottleAndCable",label:"Throttle & cables",value:"throttleAndCable",sortOrder:(0,n.L9)("ThrottleAndCable",e),checked:r?.throttleAndCable},{name:"SandTraps",label:"Sand traps",value:"sandTraps",sortOrder:(0,n.L9)("SandTraps",e),checked:r?.sandTraps},{name:"CablesFRPullies",label:"Cables & pullies",value:"cablesFRPullies",sortOrder:(0,n.L9)("CablesFRPullies",e),checked:r?.cablesFRPullies}],v=(e,r)=>[{name:"PostEngineAndPropulsion",label:"Engine and propulsion",value:"postEngineAndPropulsion",sortOrder:(0,n.L9)("PostEngineAndPropulsion",e),checked:r?.postEngineAndPropulsion},{name:"CheckOilPressure",label:"Check oil pressure",value:"checkOilPressure",sortOrder:(0,n.L9)("CheckOilPressure",e),checked:r?.checkOilPressure},{name:"CoolantLevels",label:"Coolant Levels",value:"coolantLevels",sortOrder:(0,n.L9)("CoolantLevels",e),checked:r?.coolantLevels}],p=(e,r)=>[{name:"PostElectricalStrainers",label:"Check Sea Strainers, Sea Valves, Tank levels (Blackwater, Freshwater)",value:"postElectricalStrainers",sortOrder:(0,n.L9)("group",e),checked:r?.postElectricalStrainers},{name:"SeaStrainers",label:"Check Sea Strainers, Sea Valves, Tank levels (Blackwater, Freshwater)",value:"seaStrainers",sortOrder:(0,n.L9)("SeaStrainers",e),checked:r?.seaStrainers},{name:"Exhaust",label:"Exhaust system",value:"exhaust",sortOrder:(0,n.L9)("Exhaust",e),checked:r?.exhaust},{name:"Cooling",label:"Cooling",value:"cooling",sortOrder:(0,n.L9)("Cooling",e),checked:r?.cooling},{name:"FuelTanks",label:"Fuel Tanks",value:"fuelTanks",sortOrder:(0,n.L9)("FuelTanks",e),checked:r?.fuelTanks}],L=(e,r)=>[{name:"PostElectrical",label:"Electrical",value:"postElectrical",sortOrder:(0,n.L9)("PostElectrical",e),checked:r?.postElectrical},{name:"BatteryIsCharging",label:"Battery is charging",value:"batteryIsCharging",sortOrder:(0,n.L9)("BatteryIsCharging",e),checked:r?.batteryIsCharging},{name:"ShorePowerIsDisconnected",label:"Shore power is disconnected",value:"shorePowerIsDisconnected",sortOrder:(0,n.L9)("ShorePowerIsDisconnected",e),checked:r?.shorePowerIsDisconnected}],b=(e,r)=>[{name:"Steering",label:"Steering",value:"steering",sortOrder:(0,n.L9)("Steering",e),checked:r?.steering},{name:"LockToLockSteering",label:"Lock to lock steering",value:"lockToLockSteering",sortOrder:(0,n.L9)("LockToLockSteering",e),checked:r?.lockToLockSteering},{name:"SteeringTrimTabs",label:"Trim tabs",value:"steeringTrimTabs",sortOrder:(0,n.L9)("SteeringTrimTabs",e),checked:r?.steeringTrimTabs}],O=(e,r)=>[{name:"EngineCheckPropellers",label:"EngineCheckPropellers",value:"engineCheckPropellers",sortOrder:(0,n.L9)("EngineCheckPropellers",e),checked:r?.engineCheckPropellers},{name:"Generator",label:"Generator",value:"generator",sortOrder:(0,n.L9)("Generator",e),checked:r?.generator},{name:"ShorePower",label:"ShorePower",value:"shorePower",sortOrder:(0,n.L9)("ShorePower",e),checked:r?.shorePower},{name:"ForwardReverse",label:"ForwardReverse",value:"forwardReverse",sortOrder:(0,n.L9)("ForwardReverse",e),checked:r?.forwardReverse}],S=(e,r)=>[{name:"DriveShaftsChecks",label:"Drive shaft checks",value:"driveShaftsChecks",sortOrder:(0,n.L9)("group",e),checked:r?.driveShaftsChecks},{name:"DriveShafts",label:"Drive shafts",value:"driveShafts",sortOrder:(0,n.L9)("DriveShafts",e),checked:r?.driveShafts},{name:"GearBox",label:"Gear box",value:"gearBox",sortOrder:(0,n.L9)("GearBox",e),checked:r?.gearBox},{name:"Propeller",label:"Propeller",value:"propellers",sortOrder:(0,n.L9)("Propeller",e),checked:r?.propellers},{name:"Skeg",label:"Skeg",value:"skeg",sortOrder:(0,n.L9)("Skeg",e),checked:r?.skeg}],C=(e,r)=>[{name:"OtherEngineFields",label:"Other engine fields",value:"otherEngineFields",sortOrder:(0,n.L9)("group",e),checked:r?.otherEngineFields},{name:"BeltsHosesClamps",label:"Belts, hoses, clamps and fittings",value:"beltsHosesClamps",sortOrder:(0,n.L9)("BeltsHosesClamps",e),checked:r?.beltsHosesClamps},{name:"SteeringIsFit",label:"Steering checked",value:"steeringIsFit",sortOrder:(0,n.L9)("SteeringIsFit",e),checked:r?.steeringIsFit},{name:"OperationalTestsOfHelm",label:"Operational tests of helm",value:"operationalTestsOfHelms",sortOrder:(0,n.L9)("OperationalTestsOfHelm",e),checked:r?.operationalTestsOfHelms},{name:"SteeringTillers",label:"Steering tiller",value:"steeringTillers",sortOrder:(0,n.L9)("SteeringTillers",e),checked:r?.steeringTillers},{name:"Batteries",label:"Batteries",value:"batteries",sortOrder:(0,n.L9)("Batteries",e),checked:r?.batteries},{name:"Bilge",label:"Bilge",value:"bilge",sortOrder:(0,n.L9)("Bilge",e),checked:r?.bilge},{name:"Throttle",label:"Throttle",value:"throttle",sortOrder:(0,n.L9)("Throttle",e),checked:r?.throttle},{name:"JetUnit",label:"Jet unit",value:"jetUnit",sortOrder:(0,n.L9)("JetUnit",e),checked:r?.jetUnit},{name:"ThrottleAndCableChecks",label:"Throttle and cable checks",value:"throttleAndCableChecks",sortOrder:(0,n.L9)("ThrottleAndCableChecks",e),checked:r?.throttleAndCableChecks}],E=(e,r)=>[{name:"MainEngineChecks",label:"Main engine(s) checks",value:"mainEngineChecks",sortOrder:(0,n.L9)("group",e),checked:r?.mainEngineChecks},{name:"PropulsionEngineChecks",label:"Engine checks",value:"propulsionEngineChecks",sortOrder:(0,n.L9)("PropulsionEngineChecks",e),checked:r?.propulsionEngineChecks},{name:"EngineIsFit",label:"Engine controls tested",value:"engineIsFit",sortOrder:(0,n.L9)("EngineIsFit",e),checked:r?.engineIsFit},{name:"EngineTellTale",label:"Engine telltale signs",value:"engineTellTale",sortOrder:(0,n.L9)("EngineTellTale",e),checked:r?.engineTellTale},{name:"OilAndWater",label:"Machinery oil and water",value:"oilAndWater",sortOrder:(0,n.L9)("OilAndWater",e),checked:r?.oilAndWater}],f=(e,r)=>[{name:"EngineRoomVisualInspection",label:"Main engine(s) checks",value:"engineRoomVisualInspection",sortOrder:(0,n.L9)("group",e),checked:r?.engineRoomVisualInspection},{name:"AirShutoffs",label:"Air shut-offs",value:"airShutoffs",sortOrder:(0,n.L9)("AirShutoffs",e),checked:r?.airShutoffs},{name:"EngineRoom",label:"Engine room",value:"engineRoom",sortOrder:(0,n.L9)("EngineRoom",e),checked:r?.engineRoom},{name:"EngineRoomChecks",label:"Engine room checks",value:"engineRoomChecks",sortOrder:(0,n.L9)("EngineRoomChecks",e),checked:r?.engineRoomChecks},{name:"FireDampeners",label:"Fire dampeners",value:"fireDampeners",sortOrder:(0,n.L9)("FireDampeners",e),checked:r?.fireDampeners}],P=(e,r)=>[{name:"FuelSystems",label:"Fuel systems",value:"fuelSystems",sortOrder:(0,n.L9)("group",e),checked:r?.fuelSystems},{name:"FuelLevel",label:"Fuel level",value:"fuelLevel",sortOrder:(0,n.L9)("FuelLevel",e),checked:r?.fuelLevel}],y=(e,r)=>[{name:"PropulsionCheck",label:"Propulsion check",value:"propulsionCheck",sortOrder:(0,n.L9)("group",e),checked:r?.propulsionCheck},{name:"PropulsionPropulsion",label:"Propulsion",value:"propulsionPropulsion",sortOrder:(0,n.L9)("PropulsionPropulsion",e),checked:r?.propulsionPropulsion},{name:"MainEngine",label:"Main engine",value:"mainEngine",sortOrder:(0,n.L9)("MainEngine",e),checked:r?.mainEngine},{name:"Transmission",label:"Transmission",value:"transmission",sortOrder:(0,n.L9)("Transmission",e),checked:r?.transmission},{name:"SteeringPropulsion",label:"Steering propulsion",value:"steeringPropultion",sortOrder:(0,n.L9)("SteeringPropulsion",e),checked:r?.steeringPropultion},{name:"TrimTabs",label:"Trim tabs",value:"trimTabs",sortOrder:(0,n.L9)("TrimTabs",e),checked:r?.trimTabs}],A=(e,r)=>[{name:"EngrMechanical",label:"Mechanical",value:"engrMechanical",sortOrder:(0,n.L9)("group",e),checked:r?.engrMechanical},{name:"MechCrankcaseOilLevel",label:"Crankcase Oil Level",value:"mechCrankcaseOilLevel",sortOrder:(0,n.L9)("MechCrankcaseOilLevel",e),checked:r?.mechCrankcaseOilLevel},{name:"MechCoolingWaterLevel",label:"Cooling Water Level",value:"mechCoolingWaterLevel",sortOrder:(0,n.L9)("MechCoolingWaterLevel",e),checked:r?.mechCoolingWaterLevel},{name:"MechTransmissionOilLevel",label:"Transmission Oil Level",value:"mechTransmissionOilLevel",sortOrder:(0,n.L9)("MechTransmissionOilLevel",e),checked:r?.mechTransmissionOilLevel},{name:"MechInspectPipework",label:"Inspect Pipework",value:"mechInspectPipework",sortOrder:(0,n.L9)("MechInspectPipework",e),checked:r?.mechInspectPipework},{name:"MechHydraulicSteeringOilLevel",label:"Hydraulic Steering Oil Level",value:"mechHydraulicSteeringOilLevel",sortOrder:(0,n.L9)("MechHydraulicSteeringOilLevel",e),checked:r?.mechHydraulicSteeringOilLevel},{name:"MechGearBoxOilLevel",label:"Gear Box Oil Level",value:"mechGearBoxOilLevel",sortOrder:(0,n.L9)("MechGearBoxOilLevel",e),checked:r?.mechGearBoxOilLevel},{name:"MechInspectVeeBelts",label:"Inspect Vee Belts",value:"mechInspectVeeBelts",sortOrder:(0,n.L9)("MechInspectVeeBelts",e),checked:r?.mechInspectVeeBelts}],F=(e,r)=>[{name:"EngrGenerator",label:"Generator",value:"engrGenerator",sortOrder:(0,n.L9)("group",e),checked:r?.engrGenerator},{name:"GenCrankcaseOilLevel",label:"Crankcase Oil Level",value:"genCrankcaseOilLevel",sortOrder:(0,n.L9)("GenCrankcaseOilLevel",e),checked:r?.genCrankcaseOilLevel},{name:"GenCoolingWaterLevel",label:"Cooling Water Level",value:"genCoolingWaterLevel",sortOrder:(0,n.L9)("GenCoolingWaterLevel",e),checked:r?.genCoolingWaterLevel},{name:"GenElectrical",label:"Electrical",value:"genElectrical",sortOrder:(0,n.L9)("GenElectrical",e),checked:r?.genElectrical},{name:"GenPracxisSystemOperative",label:"Check Pracxis System Operative",value:"genPracxisSystemOperative",sortOrder:(0,n.L9)("GenPracxisSystemOperative",e),checked:r?.genPracxisSystemOperative},{name:"GenTest24VLighting",label:"Test 24V Lighting",value:"genTest24VLighting",sortOrder:(0,n.L9)("GenTest24VLighting",e),checked:r?.genTest24VLighting},{name:"GenRunningTankFuelLevel",label:"Record Running Tank Fuel Level per Shipping Movement",value:"genRunningTankFuelLevel",sortOrder:(0,n.L9)("GenRunningTankFuelLevel",e),checked:r?.genRunningTankFuelLevel}],T=(e,r)=>[{name:"EngrElectronics",label:"Electronics",value:"engrElectronics",sortOrder:(0,n.L9)("group",e),checked:r?.engrElectronics},{name:"ElectrDeckLights",label:"Deck Lights",value:"electrDeckLights",sortOrder:(0,n.L9)("ElectrDeckLights",e),checked:r?.electrDeckLights},{name:"ElectrSearchLights",label:"Search Lights",value:"electrSearchLights",sortOrder:(0,n.L9)("ElectrSearchLights",e),checked:r?.electrSearchLights},{name:"ElectrChart",label:"Chart",value:"electrChart",sortOrder:(0,n.L9)("ElectrChart",e),checked:r?.electrChart}],w=(e,r)=>[{name:"EngrTowlineWinch",label:"Towline & Winch",value:"engrTowlineWinch",sortOrder:(0,n.L9)("group",e),checked:r?.engrTowlineWinch},{name:"TowCheckWinchCondition",label:"Check Winch condition",value:"towCheckWinchCondition",sortOrder:(0,n.L9)("TowCheckWinchCondition",e),checked:r?.towCheckWinchCondition},{name:"TowProveWinchOperation",label:"Prove Winch Operation",value:"towProveWinchOperation",sortOrder:(0,n.L9)("TowProveWinchOperation",e),checked:r?.towProveWinchOperation},{name:"TowSelectControlStation",label:"Select Control Station",value:"towSelectControlStation",sortOrder:(0,n.L9)("TowSelectControlStation",e),checked:r?.towSelectControlStation},{name:"TowCheckTowlineCondition",label:"Check Towline Condition",value:"towCheckTowlineCondition",sortOrder:(0,n.L9)("TowCheckTowlineCondition",e),checked:r?.towCheckTowlineCondition}],H=(e,r)=>[{group:[d(e,r),h(e,r),u(e,r),g(e,r),m(e,r),k(e,r),v(e,r),p(e,r),L(e,r),b(e,r)]},{individual:[...O(e,r)]}],R=(e,r)=>[{name:"EPIRB",label:"EPIRB",value:"epirb",sortOrder:(0,n.L9)("EPIRB",e),checked:r?.epirb},{name:"LifeJackets",label:"Life jackets",value:"lifeJackets",sortOrder:(0,n.L9)("LifeJackets",e),checked:r?.lifeJackets},{name:"LifeRings",label:"Life rings",value:"lifeRings",sortOrder:(0,n.L9)("LifeRings",e),checked:r?.lifeRings},{name:"Flares",label:"Flares, visual distress signals",value:"flares",sortOrder:(0,n.L9)("Flares",e),checked:r?.flares},{name:"FireExtinguisher",label:"Fire equipment",value:"fireExtinguisher",sortOrder:(0,n.L9)("FireExtinguisher",e),checked:r?.fireExtinguisher},{name:"SafetyEquipment",label:"Safety equipment",value:"safetyEquipment",sortOrder:(0,n.L9)("SafetyEquipment",e),checked:r?.safetyEquipment},{name:"FireHoses",label:"Fire hoses, nozzles and cam locks fitted as expected",value:"fireHoses",sortOrder:(0,n.L9)("FireHoses",e),checked:r?.fireHoses},{name:"FireBuckets",label:"Fire buckets",value:"fireBuckets",sortOrder:(0,n.L9)("FireBuckets",e),checked:r?.fireBuckets},{name:"FireBlanket",label:"Fire blankets",value:"fireBlanket",sortOrder:(0,n.L9)("FireBlanket",e),checked:r?.fireBlanket},{name:"FireAxes",label:"Fire axes",value:"fireAxes",sortOrder:(0,n.L9)("FireAxes",e),checked:r?.fireAxes},{name:"FirePump",label:"Fire pump",value:"firePump",sortOrder:(0,n.L9)("FirePump",e),checked:r?.firePump},{name:"FireFlaps",label:"Fire Flaps",value:"fireFlaps",sortOrder:(0,n.L9)("FireFlaps",e),checked:r?.fireFlaps},{name:"LifeRaft",label:"Life raft",value:"lifeRaft",sortOrder:(0,n.L9)("LifeRaft",e),checked:r?.lifeRaft},{name:"HighWaterAlarm",label:"High water alarm",value:"highWaterAlarm",sortOrder:(0,n.L9)("HighWaterAlarm",e),checked:r?.highWaterAlarm},{name:"FirstAid",label:"First aid",value:"firstAid",sortOrder:(0,n.L9)("FirstAid",e),checked:r?.firstAid},{name:"PersonOverboardRescueEquipment",label:"Person overboard rescue equipment",value:"personOverboardRescueEquipment",sortOrder:(0,n.L9)("PersonOverboardRescueEquipment",e),checked:r?.personOverboardRescueEquipment},{name:"SmokeDetectors",label:"Smoke and carbon monoxide detectors test",value:"smokeDetectors",sortOrder:(0,n.L9)("SmokeDetectors",e),checked:r?.smokeDetectors}],B=(e,r)=>[{group:[]},{individual:[...R(e,r)]}],W=(e,r)=>[{name:"Radio",label:"Radio",value:"radio",sortOrder:(0,n.L9)("Radio",e),checked:r?.radio},{name:"GPS",label:"GPS",value:"gps",sortOrder:(0,n.L9)("GPS",e),checked:r?.gps},{name:"NavigationChecks",label:"Navigation Checks",value:"navigationChecks",sortOrder:(0,n.L9)("NavigationChecks",e),checked:r?.navigationChecks},{name:"DepthSounder",label:"Depth Sounder",value:"depthSounder",sortOrder:(0,n.L9)("DepthSounder",e),checked:r?.depthSounder},{name:"Radar",label:"Radar",value:"radar",sortOrder:(0,n.L9)("Radar",e),checked:r?.radar},{name:"ChartPlotter",label:"Chart Plotter",value:"chartPlotter",sortOrder:(0,n.L9)("ChartPlotter",e),checked:r?.chartPlotter},{name:"SART",label:"SART",value:"sart",sortOrder:(0,n.L9)("SART",e),checked:r?.sart},{name:"VHF",label:"VHF",value:"vhf",sortOrder:(0,n.L9)("VHF",e),checked:r?.vhf},{name:"UHF",label:"UHF",value:"uhf",sortOrder:(0,n.L9)("UHF",e),checked:r?.uhf},{name:"TracPlus",label:"TracPlus",value:"tracPlus",sortOrder:(0,n.L9)("TracPlus",e),checked:r?.tracPlus},{name:"AISOperational",label:"AIS Operational",value:"aisOperational",sortOrder:(0,n.L9)("AISOperational",e),checked:r?.aisOperational},{name:"OtherNavigation",label:"Other Navigation",value:"otherNavigation",sortOrder:(0,n.L9)("OtherNavigation",e),checked:r?.otherNavigation},{name:"NavigationLights",label:"Navigation Lights",value:"navigationLights",sortOrder:(0,n.L9)("NavigationLights",e),checked:r?.navigationLights},{name:"Compass",label:"Compass",value:"compass",sortOrder:(0,n.L9)("Compass",e),checked:r?.compass},{name:"SoundSignallingDevices",label:"Sound Signalling Devices",value:"soundSignallingDevices",sortOrder:(0,n.L9)("SoundSignallingDevices",e),checked:r?.soundSignallingDevices},{name:"NavigationHazards",label:"Navigation Hazards",value:"navigationHazards",sortOrder:(0,n.L9)("NavigationHazards",e),checked:r?.navigationHazards},{name:"Wheelhouse",label:"Wheelhouse",value:"wheelhouse",sortOrder:(0,n.L9)("Wheelhouse",e),checked:r?.wheelhouse},{name:"Charts",label:"Charts in place for proposed sailing area",value:"charts",sortOrder:(0,n.L9)("Charts",e),checked:r?.charts},{name:"NavigationCharts",label:"Navigation Charts",value:"navigationCharts",sortOrder:(0,n.L9)("NavigationCharts",e),checked:r?.navigationCharts}],x=(e,r)=>[{group:[]},{individual:[...W(e,r)]}],M=(e,r)=>[{name:"Review",label:"Logbook entry review",value:"review",checked:r?.review,sortOrder:(0,n.L9)("Review",e)},{name:"SafetyEquipmentCheck",label:"Safety equipment check",value:"safetyEquipmentCheck",checked:r?.safetyEquipmentCheck,sortOrder:(0,n.L9)("SafetyEquipmentCheck",e)},{name:"ForecastAccuracy",label:"Forecast Accuracy",value:"forecastAccuracy",checked:r?.forecastAccuracy,sortOrder:(0,n.L9)("ForecastAccuracy",e)},{name:"EngineHoursEnd",label:"Hours Run",value:"engineHoursEnd",checked:r?.engineHoursEnd,sortOrder:(0,n.L9)("EngineHoursEnd",e)},{name:"EngineHours",label:"Engine Hours",value:"engineHours",checked:r?.engineHours,sortOrder:(0,n.L9)("EngineHours",e)},{name:"FuelEnd",label:"Fuel end",value:"fuelEnd",checked:r?.fuelEnd,sortOrder:(0,n.L9)("FuelEnd",e)},{name:"AIS",label:"Automatic Identification System (AIS)",value:"ais",checked:r?.ais,sortOrder:(0,n.L9)("AIS",e)},{name:"NavigationLightsAndShapes",label:"Navigation lights and shapes",value:"navigationLightsAndShapes",checked:r?.navigationLightsAndShapes,sortOrder:(0,n.L9)("NavigationLightsAndShapes",e)},{name:"ElectronicNavigationalAids",label:"Electronic navigational aids",value:"electronicNavigationalAids",checked:r?.electronicNavigationalAids,sortOrder:(0,n.L9)("ElectronicNavigationalAids",e)},{name:"MainEngines",label:"Main engines",value:"mainEngines",checked:r?.mainEngines,sortOrder:(0,n.L9)("MainEngines",e)},{name:"AuxiliarySystems",label:"Auxiliary systems",value:"auxiliarySystems",checked:r?.auxiliarySystems,sortOrder:(0,n.L9)("AuxiliarySystems",e)},{name:"FuelAndOil",label:"Fuel and oil systems",value:"fuelAndOil",checked:r?.fuelAndOil,sortOrder:(0,n.L9)("FuelAndOil",e)},{name:"BilgeSystems",label:"Bilge Systems",value:"bilgeSystems",checked:r?.bilgeSystems,sortOrder:(0,n.L9)("BilgeSystems",e)},{name:"Power",label:"Main and emergency power",value:"power",checked:r?.power,sortOrder:(0,n.L9)("Power",e)},{name:"BatteryMaintenance",label:"Battery maintenance",value:"batteryMaintenance",checked:r?.batteryMaintenance,sortOrder:(0,n.L9)("BatteryMaintenance",e)},{name:"CircuitInspections",label:"Circuit inspections",value:"circuitInspections",checked:r?.circuitInspections,sortOrder:(0,n.L9)("CircuitInspections",e)},{name:"MooringAndAnchoring",label:"Mooring and anchoring",value:"mooringAndAnchoring",checked:r?.mooringAndAnchoring,sortOrder:(0,n.L9)("MooringAndAnchoring",e)},{name:"CargoAndAccessEquipment",label:"Cargo and access equipment",value:"cargoAndAccessEquipment",checked:r?.cargoAndAccessEquipment,sortOrder:(0,n.L9)("CargoAndAccessEquipment",e)},{name:"HatchesAndWatertightDoors",label:"Hatches and watertight doors",value:"hatchesAndWatertightDoors",checked:r?.hatchesAndWatertightDoors,sortOrder:(0,n.L9)("HatchesAndWatertightDoors",e)},{name:"GalleyAppliances",label:"Galley appliances",value:"galleyAppliances",checked:r?.galleyAppliances,sortOrder:(0,n.L9)("GalleyAppliances",e)},{name:"WasteManagement",label:"Waste management",value:"wasteManagement",checked:r?.wasteManagement,sortOrder:(0,n.L9)("WasteManagement",e)},{name:"VentilationAndAirConditioning",label:"Ventilation and air conditioning",value:"ventilationAndAirConditioning",checked:r?.ventilationAndAirConditioning,sortOrder:(0,n.L9)("VentilationAndAirConditioning",e)},{name:"EmergencyReadiness",label:"Emergency readiness",value:"emergencyReadiness",checked:r?.emergencyReadiness,sortOrder:(0,n.L9)("EmergencyReadiness",e)},{name:"EnvironmentalCompliance",label:"Environmental compliance",value:"environmentalCompliance",checked:r?.environmentalCompliance,sortOrder:(0,n.L9)("EnvironmentalCompliance",e)}],D=(e,r)=>[{group:[]},{individual:[...M(e,r)]}],I=e=>[{name:"Fitness",label:"Safe crewing assessment",value:"fitness",checked:e?.fitness,description:l.jsx(l.Fragment,{children:"Ensure that there are sufficient crew and sufficient qualified crew on board to operate the vessel safely which may exceed the vessels minimum crewing."})},{name:"SafetyActions",label:"Risk assessments completed",value:"safetyActions",checked:e?.safetyActions},{name:"WaterQuality",label:"Health, safety and environment actions",value:"waterQuality",checked:e?.waterQuality,description:l.jsx(l.Fragment,{children:"This could include drinking water quality check and toolbox agendas."})},{name:"IMSafe",label:"IMSAFE",value:"imSafe",checked:e?.imSafe,description:(0,l.jsxs)(l.Fragment,{children:["Free of ",l.jsx("b",{children:"illness"})," and symptoms. Safe ",l.jsx("b",{children:"medication"})," ","only. Managing ",l.jsx("b",{children:"stress"})," well at home and at work. Free of ",l.jsx("b",{children:"alcohol"})," and drugs and their effects. Rested, slept and ",l.jsx("b",{children:"fatigue"})," free. ",l.jsx("b",{children:"Eaten"}),", watered, and ready to go"]})}]},23745:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>s});var l=a(98768);a(60343);var n=a(64837);function s({children:e}){return l.jsx(n.Z,{children:e})}},83569:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>l});let l=(0,a(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\log-entries\layout.tsx#default`)}};