"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4490],{72998:function(t,n){Object.defineProperty(n,"__esModule",{value:!0}),n.CONSTANTS=void 0,n.CONSTANTS={arcTooltipClassname:"gauge-component-arc-tooltip",tickLineClassname:"tick-line",tickValueClassname:"tick-value",valueLabelClassname:"value-text",debugTicksRadius:!1,debugSingleGauge:!1,rangeBetweenCenteredTickValueLabel:[.35,.65]},n.default=n.CONSTANTS},57208:function(t,n,e){var r=this&&this.__createBinding||(Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]}),i=this&&this.__setModuleDefault||(Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&r(n,t,e);return i(n,t),n},u=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(n,"__esModule",{value:!0}),n.validateArcs=n.clearOuterArcs=n.clearArcs=n.redrawArcs=n.getCoordByValue=n.createGradientElement=n.getColors=n.applyGradientColors=n.getArcDataByPercentage=n.getArcDataByValue=n.applyColors=n.setupTooltip=n.setupArcs=n.drawArc=n.setArcData=n.hideTooltip=void 0;var a=o(e(76507)),c=e(29813),f=o(e(57208)),l=u(e(72998)),s=e(20237),h=e(16593),d=e(40055),p=function(t,n,e){void 0!=n.data.tooltip&&(n.data.tooltip.text!=e.tooltip.current.text()&&(e.tooltip.current.html(n.data.tooltip.text).style("position","absolute").style("display","block").style("opacity",1),v(n.data.tooltip,n.data.color,e)),e.tooltip.current.style("left",t.pageX+15+"px").style("top",t.pageY-10+"px")),void 0!=n.data.onMouseMove&&n.data.onMouseMove(t)},v=function(t,n,e){Object.entries(s.defaultTooltipStyle).forEach(function(t){var n=t[0],r=t[1];return e.tooltip.current.style(a.camelCaseToKebabCase(n),r)}),e.tooltip.current.style("background-color",n),void 0!=t.style&&Object.entries(t.style).forEach(function(t){var n=t[0],r=t[1];return e.tooltip.current.style(a.camelCaseToKebabCase(n),r)})},g=function(t,e,r,i){i.cancel(),(0,n.hideTooltip)(r),void 0!=e.data.onMouseLeave&&e.data.onMouseLeave(t)};n.hideTooltip=function(t){t.tooltip.current.html(" ").style("display","none")};var y=function(t,n,e){t.target.style.stroke="none"},b=function(t,n){void 0!=n.data.onMouseClick&&n.data.onMouseClick(t)};n.setArcData=function(t){var e,r,i=t.props.arc,o=t.props.minValue,u=t.props.maxValue,c=(null==i?void 0:i.nbSubArcs)||(null===(e=null==i?void 0:i.subArcs)||void 0===e?void 0:e.length)||1,f=(0,n.getColors)(c,t);if((null==i?void 0:i.subArcs)&&!(null==i?void 0:i.nbSubArcs)){var l=0,s=0,h=[],d=[],p=[];null===(r=null==i?void 0:i.subArcs)||void 0===r||r.forEach(function(n,e){var r,c=0,f=0,v=n.limit;if(void 0!=n.length)c=n.length,v=a.getCurrentGaugeValueByPercentage(c+s,t);else if(void 0==n.limit){f=l;var g=void 0,y=null===(r=null==i?void 0:i.subArcs)||void 0===r?void 0:r.slice(e),b=(1-a.calculatePercentage(o,u,l))*100;g||(g=b/Math.max((null==y?void 0:y.length)||1,1)/100),v=l+100*g,c=g}else f=v-l,c=0!==e?a.calculatePercentage(o,u,v)-s:a.calculatePercentage(o,u,f);h.push(c),d.push(v),s=h.reduce(function(t,n){return t+n},0),l=v,void 0!=n.tooltip&&p.push(n.tooltip)});var v=i.subArcs;t.arcData.current=h.map(function(t,n){return{value:t,limit:d[n],color:f[n],showTick:v[n].showTick||!1,tooltip:v[n].tooltip||void 0,onMouseMove:v[n].onMouseMove,onMouseLeave:v[n].onMouseLeave,onMouseClick:v[n].onClick}})}else{var g=u/c;t.arcData.current=Array.from({length:c},function(t,n){return{value:g,limit:(n+1)*g,color:f[n],tooltip:void 0}})}};var m=function(t,e){void 0===e&&(e=void 0);var r,i=void 0!=e?e:a.calculatePercentage(t.props.minValue,t.props.maxValue,t.props.value),o=(0,n.getArcDataByPercentage)(i,t);return[{value:i,color:(null==o?void 0:o.color)||"white"},{value:1-i,color:null===(r=t.props.arc)||void 0===r?void 0:r.emptyColor}]},_=function(t,e){void 0===e&&(e=!1);var r=t.dimensions.current.outerRadius;if(t.props.type==h.GaugeType.Grafana&&e){t.doughnut.current.selectAll(".outerSubArc").remove();var i=(0,c.arc)().outerRadius(r+7).innerRadius(r+2).cornerRadius(0).padAngle(0),o=t.doughnut.current.selectAll("anyString").data(t.pieChart.current(t.arcData.current)).enter().append("g").attr("class","outerSubArc"),u=o.append("path").attr("d",i);(0,n.applyColors)(u,t);var a=(0,d.throttle)(function(n,e){return p(n,e,t)},20);o.on("mouseleave",function(n,e){return g(n,e,t,a)}).on("mouseout",function(n,e){return y(n,e,t)}).on("mousemove",a).on("click",function(t,n){return b(t,n)})}};n.drawArc=function(t,e){void 0===e&&(e=void 0);var r,i,o=t.props.arc,u=o.padding,a=o.cornerRadius,f=t.dimensions.current,l=f.innerRadius,s=f.outerRadius,v={};v=(null===(i=null===(r=t.props)||void 0===r?void 0:r.arc)||void 0===i?void 0:i.gradient)?[{value:1}]:t.arcData.current,t.props.type==h.GaugeType.Grafana&&(v=m(t,e));var _=t.props.type==h.GaugeType.Grafana?0:u,x=t.props.type==h.GaugeType.Grafana?0:a,w=(0,c.arc)().outerRadius(s).innerRadius(l).cornerRadius(x).padAngle(_),M=t.doughnut.current.selectAll("anyString").data(t.pieChart.current(v)).enter().append("g").attr("class","subArc"),A=M.append("path").attr("d",w);(0,n.applyColors)(A,t);var T=(0,d.throttle)(function(n,e){return p(n,e,t)},20);M.on("mouseleave",function(n,e){return g(n,e,t,T)}).on("mouseout",function(n,e){return y(n,e,t)}).on("mousemove",T).on("click",function(t,n){return b(t,n)})},n.setupArcs=function(t,e){void 0===e&&(e=!1),(0,n.setupTooltip)(t),_(t,e),(0,n.drawArc)(t)},n.setupTooltip=function(t){0!=document.getElementsByClassName(l.default.arcTooltipClassname).length||(0,c.select)("body").append("div").attr("class",l.default.arcTooltipClassname),t.tooltip.current=(0,c.select)(".".concat(l.default.arcTooltipClassname)),t.tooltip.current.on("mouseleave",function(){return f.hideTooltip(t)}).on("mouseout",function(){return f.hideTooltip(t)})},n.applyColors=function(t,e){var r,i;if(null===(i=null===(r=e.props)||void 0===r?void 0:r.arc)||void 0===i?void 0:i.gradient){var o="subArc-linear-gradient-".concat(Math.random()),u=(0,n.createGradientElement)(e.doughnut.current,o);(0,n.applyGradientColors)(u,e),t.style("fill",function(t){return"url(#".concat(o,")")})}else t.style("fill",function(t){return t.data.color})},n.getArcDataByValue=function(t,n){return n.arcData.current.find(function(n){return t<=n.limit})},n.getArcDataByPercentage=function(t,e){return(0,n.getArcDataByValue)(a.getCurrentGaugeValueByPercentage(t,e),e)},n.applyGradientColors=function(t,n){n.arcData.current.forEach(function(e){var r,i,o,u,c=a.normalize(null==e?void 0:e.limit,null!==(i=null===(r=null==n?void 0:n.props)||void 0===r?void 0:r.minValue)&&void 0!==i?i:0,null!==(u=null===(o=null==n?void 0:n.props)||void 0===o?void 0:o.maxValue)&&void 0!==u?u:100);t.append("stop").attr("offset","".concat(c,"%")).style("stop-color",e.color).style("stop-opacity",1)})},n.getColors=function(t,n){var e,r=n.props.arc,i=[];if(r.colorArray)i=r.colorArray;else{var o=null===(e=r.subArcs)||void 0===e?void 0:e.map(function(t){return t.color});i=(null==o?void 0:o.some(function(t){return void 0!=t}))?o:l.default.defaultColors}if(i||(i=["#fff"]),t===(null==i?void 0:i.length))return i;for(var u=(0,c.scaleLinear)().domain([1,t]).range([i[0],i[i.length-1]]).interpolate(c.interpolateHsl),a=[],f=1;f<=t;f++)a.push(u(f));return a},n.createGradientElement=function(t,n){return t.append("defs").append("linearGradient").attr("id",n).attr("x1","0%").attr("x2","100%").attr("y1","0%").attr("y2","0%")},n.getCoordByValue=function(t,n,e,r,i){void 0===e&&(e="inner"),void 0===r&&(r=0),void 0===i&&(i=1);var o,u=({outer:function(){return n.dimensions.current.outerRadius-r+2},inner:function(){return n.dimensions.current.innerRadius*i-r+9},between:function(){var t=n.dimensions.current.outerRadius-n.dimensions.current.innerRadius;return n.dimensions.current.innerRadius+t-5}})[e]();n.props.type===h.GaugeType.Grafana?u+=5:n.props.type===h.GaugeType.Semicircle&&(u+=-2);var c=a.calculatePercentage(n.props.minValue,n.props.maxValue,t),f=((o={})[h.GaugeType.Grafana]={startAngle:a.degToRad(-23),endAngle:a.degToRad(203)},o[h.GaugeType.Semicircle]={startAngle:a.degToRad(.9),endAngle:a.degToRad(179.1)},o[h.GaugeType.Radial]={startAngle:a.degToRad(-39),endAngle:a.degToRad(219)},o)[n.props.type],l=f.startAngle,s=l+c*(f.endAngle-l),d=[0,-(n.dimensions.current.width/500*1)/2],p=[d[0]-u*Math.cos(s),d[1]-u*Math.sin(s)],v=[n.dimensions.current.outerRadius,n.dimensions.current.outerRadius];return{x:v[0]+p[0],y:v[1]+p[1]}},n.redrawArcs=function(t){(0,n.clearArcs)(t),(0,n.setArcData)(t),(0,n.setupArcs)(t)},n.clearArcs=function(t){t.doughnut.current.selectAll(".subArc").remove()},n.clearOuterArcs=function(t){t.doughnut.current.selectAll(".outerSubArc").remove()},n.validateArcs=function(t){x(t)};var x=function(t){for(var n,e=t.props.minValue,r=t.props.maxValue,i=t.props.arc.subArcs,o=void 0,u=0,a=(null===(n=t.props.arc)||void 0===n?void 0:n.subArcs)||[];u<a.length;u++){var c=a[u].limit;if(void 0!==c){if(c<e||c>r)throw Error("The limit of a subArc must be between the minValue and maxValue. The limit of the subArc is ".concat(c));if(void 0!==o&&c<=o)throw Error("The limit of a subArc must be greater than the limit of the previous subArc. The limit of the subArc is ".concat(c,'. If you\'re trying to specify length in percent of the arc, use property "length". refer to: https://github.com/antoniolago/react-gauge-component'));o=c}}if(i.length>0){var f=i[i.length-1];f.limit<r&&(f.limit=r)}}},60971:function(t,n,e){var r=this&&this.__createBinding||(Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]}),i=this&&this.__setModuleDefault||(Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&r(n,t,e);return i(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.clearChart=n.centerGraph=n.calculateRadius=n.updateDimensions=n.renderChart=n.calculateAngles=n.initChart=void 0;var u=e(16593),a=o(e(57208)),c=o(e(30997)),f=o(e(54150));n.initChart=function(t,e){var r,i,o,u,a=t.dimensions.current.angles;if((null===(i=null===(r=t.resizeObserver)||void 0===r?void 0:r.current)||void 0===i?void 0:i.disconnect)&&(null===(u=null===(o=t.resizeObserver)||void 0===o?void 0:o.current)||void 0===u||u.disconnect()),JSON.stringify(t.prevProps.current.value)!==JSON.stringify(t.props.value)&&!e){(0,n.renderChart)(t,!1);return}t.container.current.select("svg").remove(),t.svg.current=t.container.current.append("svg"),t.g.current=t.svg.current.append("g"),t.doughnut.current=t.g.current.append("g").attr("class","doughnut"),(0,n.calculateAngles)(t),t.pieChart.current.value(function(t){return t.value}).startAngle(a.startAngle).endAngle(a.endAngle).sort(null),f.addPointerElement(t),(0,n.renderChart)(t,!0)},n.calculateAngles=function(t){var n=t.dimensions.current.angles;t.props.type==u.GaugeType.Semicircle?(n.startAngle=-Math.PI/2+.02,n.endAngle=Math.PI/2-.02):t.props.type==u.GaugeType.Radial?(n.startAngle=-Math.PI/1.37,n.endAngle=Math.PI/1.37):t.props.type==u.GaugeType.Grafana&&(n.startAngle=-Math.PI/1.6,n.endAngle=Math.PI/1.6)},n.renderChart=function(t,e){void 0===e&&(e=!1);var r,i,o,l,s,h,d=t.dimensions,p=t.props.arc,v=t.props.labels;if(e){(0,n.updateDimensions)(t),t.g.current.attr("transform","translate("+d.current.margin.left+", 35)"),(0,n.calculateRadius)(t),t.doughnut.current.attr("transform","translate("+d.current.outerRadius+", "+d.current.outerRadius+")"),t.doughnut.current.on("mouseleave",function(){return a.hideTooltip(t)}).on("mouseout",function(){return a.hideTooltip(t)});var g=p.width;d.current.innerRadius=d.current.outerRadius*(1-g),(0,n.clearChart)(t),a.setArcData(t),a.setupArcs(t,e),c.setupLabels(t),(null===(o=null===(i=t.props)||void 0===i?void 0:i.pointer)||void 0===o?void 0:o.hide)||f.drawPointer(t,e);var y=((r={})[u.GaugeType.Semicircle]=50,r[u.GaugeType.Radial]=55,r[u.GaugeType.Grafana]=55,r),b=t.doughnut.current.node().getBoundingClientRect().height,m=t.container.current.node().getBoundingClientRect().width,_=t.props.type;t.svg.current.attr("width",m).attr("height",b+y[_])}else{var x=JSON.stringify(t.prevProps.current.arc)!==JSON.stringify(t.props.arc),w=JSON.stringify(t.prevProps.current.pointer)!==JSON.stringify(t.props.pointer),M=JSON.stringify(t.prevProps.current.value)!==JSON.stringify(t.props.value),A=JSON.stringify(null===(l=t.prevProps.current.labels)||void 0===l?void 0:l.tickLabels)!==JSON.stringify(v.tickLabels);x&&(a.clearArcs(t),a.setArcData(t),a.setupArcs(t,e)),(w||M&&!(null===(h=null===(s=t.props)||void 0===s?void 0:s.pointer)||void 0===h?void 0:h.hide))&&f.drawPointer(t),(x||A)&&(c.clearTicks(t),c.setupTicks(t)),M&&(c.clearValueLabel(t),c.setupValueLabel(t))}},n.updateDimensions=function(t){var n=t.props.marginInPercent,e=t.dimensions,r=t.container.current.node().getBoundingClientRect(),i=r.width,o=r.height;0==e.current.fixedHeight&&(e.current.fixedHeight=o+200);var u="number"==typeof n,a=u?n:n.left,c=u?n:n.right,f=u?n:n.top,l=u?n:n.bottom;e.current.margin.left=i*a,e.current.margin.right=i*c,e.current.width=i-e.current.margin.left-e.current.margin.right,e.current.margin.top=e.current.fixedHeight*f,e.current.margin.bottom=e.current.fixedHeight*l,e.current.height=e.current.width/2-e.current.margin.top-e.current.margin.bottom},n.calculateRadius=function(t){var e=t.dimensions;e.current.width<2*e.current.height?e.current.outerRadius=(e.current.width-e.current.margin.left-e.current.margin.right)/2:e.current.outerRadius=e.current.height-e.current.margin.top-e.current.margin.bottom+35,(0,n.centerGraph)(t)},n.centerGraph=function(t){var n=t.dimensions;n.current.margin.left=n.current.width/2-n.current.outerRadius+n.current.margin.right,t.g.current.attr("transform","translate("+n.current.margin.left+", "+n.current.margin.top+")")},n.clearChart=function(t){c.clearTicks(t),c.clearValueLabel(t),f.clearPointerElement(t),a.clearArcs(t)}},30997:function(t,n,e){var r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)},i=this&&this.__createBinding||(Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]}),o=this&&this.__setModuleDefault||(Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n}),u=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&i(n,t,e);return o(n,t),n},a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(n,"__esModule",{value:!0}),n.calculateAnchorAndAngleByValue=n.clearTicks=n.clearValueLabel=n.addValueText=n.addText=n.getLabelCoordsByValue=n.addTick=n.addTickValue=n.addTickLine=n.mapTick=n.addArcTicks=n.setupTicks=n.setupValueLabel=n.setupLabels=void 0;var c=u(e(76507)),f=a(e(72998)),l=e(1699),s=u(e(29813)),h=e(16593),d=e(57208);n.setupLabels=function(t){(0,n.setupValueLabel)(t),(0,n.setupTicks)(t)},n.setupValueLabel=function(t){var e,r=t.props.labels;(null===(e=null==r?void 0:r.valueLabel)||void 0===e?void 0:e.hide)||(0,n.addValueText)(t)},n.setupTicks=function(t){var e,r,i,o,u,a,c,l,s,h=t.props.labels,d=t.props.minValue,p=t.props.maxValue;if(f.default.debugTicksRadius)for(var v=0;v<p;v++){var g=(0,n.mapTick)(v,t);(0,n.addTick)(g,t)}else if(!(null===(e=h.tickLabels)||void 0===e?void 0:e.hideMinMax)){if(!(null===(i=null===(r=h.tickLabels)||void 0===r?void 0:r.ticks)||void 0===i?void 0:i.some(function(t){return t.value==d}))){var y=(0,n.mapTick)(d,t);(0,n.addTick)(y,t)}if(!(null===(u=null===(o=h.tickLabels)||void 0===o?void 0:o.ticks)||void 0===u?void 0:u.some(function(t){return t.value==p}))){var b=(0,n.mapTick)(p,t);(0,n.addTick)(b,t)}}(null===(c=null===(a=h.tickLabels)||void 0===a?void 0:a.ticks)||void 0===c?void 0:c.length)>0&&(null===(s=null===(l=h.tickLabels)||void 0===l?void 0:l.ticks)||void 0===s||s.forEach(function(e){(0,n.addTick)(e,t)})),(0,n.addArcTicks)(t)},n.addArcTicks=function(t){var e;null===(e=t.arcData.current)||void 0===e||e.map(function(t){if(t.showTick)return t.limit}).forEach(function(e){e&&(0,n.addTick)((0,n.mapTick)(e,t),t)})},n.mapTick=function(t,n){var e=n.props.labels.tickLabels;return{value:t,valueConfig:null==e?void 0:e.defaultTickValueConfig,lineConfig:null==e?void 0:e.defaultTickLineConfig}},n.addTickLine=function(t,e){var r,i,o,u,a,c,h,d,p,v,g,y,b,m,_,x,w,M,A,T,S,k=e.props.labels,P=(0,n.calculateAnchorAndAngleByValue)(null==t?void 0:t.value,e),E=(P.tickAnchor,P.angle),C=(null===(r=t.lineConfig)||void 0===r?void 0:r.distanceFromArc)||(null===(o=null===(i=null==k?void 0:k.tickLabels)||void 0===i?void 0:i.defaultTickLineConfig)||void 0===o?void 0:o.distanceFromArc)||0;(null===(a=null===(u=e.props.labels)||void 0===u?void 0:u.tickLabels)||void 0===a?void 0:a.type)=="outer"&&(C=-C);var N=(0,n.getLabelCoordsByValue)(null==t?void 0:t.value,e,C),R=(null===(c=t.lineConfig)||void 0===c?void 0:c.color)||(null===(d=null===(h=null==k?void 0:k.tickLabels)||void 0===h?void 0:h.defaultTickLineConfig)||void 0===d?void 0:d.color)||(null===(p=l.defaultTickLabels.defaultTickLineConfig)||void 0===p?void 0:p.color),O=(null===(v=t.lineConfig)||void 0===v?void 0:v.width)||(null===(y=null===(g=null==k?void 0:k.tickLabels)||void 0===g?void 0:g.defaultTickLineConfig)||void 0===y?void 0:y.width)||(null===(b=l.defaultTickLabels.defaultTickLineConfig)||void 0===b?void 0:b.width),Z=(null===(m=t.lineConfig)||void 0===m?void 0:m.length)||(null===(x=null===(_=null==k?void 0:k.tickLabels)||void 0===_?void 0:_.defaultTickLineConfig)||void 0===x?void 0:x.length)||(null===(w=l.defaultTickLabels.defaultTickLineConfig)||void 0===w?void 0:w.length);(null===(M=null==k?void 0:k.tickLabels)||void 0===M?void 0:M.type)=="inner"?(A=N.x+Z*Math.cos(E*Math.PI/180),T=N.y+Z*Math.sin(E*Math.PI/180)):(A=N.x-Z*Math.cos(E*Math.PI/180),T=N.y-Z*Math.sin(E*Math.PI/180));var z=s.line();S=[[N.x,N.y],[A,T]],e.g.current.append("path").datum(S).attr("class",f.default.tickLineClassname).attr("d",z).attr("stroke",R).attr("stroke-width",O).attr("fill","none")},n.addTickValue=function(t,e){var i,o,u,a,l,s,h,d,p,g,y,b,m,_,x,w,M,A=e.props.labels,T=e.props.arc.width,S=null==t?void 0:t.value,k=(0,n.calculateAnchorAndAngleByValue)(S,e).tickAnchor,P=27-10*T,E=(null===(i=null==A?void 0:A.tickLabels)||void 0===i?void 0:i.type)=="inner";E?P-=10:P=10*T-10;var C=(null===(o=t.lineConfig)||void 0===o?void 0:o.distanceFromArc)||(null===(a=null===(u=null==A?void 0:A.tickLabels)||void 0===u?void 0:u.defaultTickLineConfig)||void 0===a?void 0:a.distanceFromArc)||0,N=(null===(l=t.lineConfig)||void 0===l?void 0:l.length)||(null===(h=null===(s=null==A?void 0:A.tickLabels)||void 0===s?void 0:s.defaultTickLineConfig)||void 0===h?void 0:h.length)||0;v(t,e)||(E?P+=C+N:(P-=C,P-=N));var R=(0,n.getLabelCoordsByValue)(S,e,P),O=(null===(d=t.valueConfig)||void 0===d?void 0:d.style)||(null===(g=null===(p=null==A?void 0:A.tickLabels)||void 0===p?void 0:p.defaultTickValueConfig)||void 0===g?void 0:g.style)||{};O=r({},O);var Z="",z=(null===(y=t.valueConfig)||void 0===y?void 0:y.maxDecimalDigits)||(null===(m=null===(b=null==A?void 0:A.tickLabels)||void 0===b?void 0:b.defaultTickValueConfig)||void 0===m?void 0:m.maxDecimalDigits);Z=(null===(_=t.valueConfig)||void 0===_?void 0:_.formatTextValue)?t.valueConfig.formatTextValue(c.floatingNumber(S,z)):(null===(w=null===(x=null==A?void 0:A.tickLabels)||void 0===x?void 0:x.defaultTickValueConfig)||void 0===w?void 0:w.formatTextValue)?A.tickLabels.defaultTickValueConfig.formatTextValue(c.floatingNumber(S,z)):0===e.props.minValue&&100===e.props.maxValue?c.floatingNumber(S,z).toString()+"%":c.floatingNumber(S,z).toString(),(null===(M=null==A?void 0:A.tickLabels)||void 0===M?void 0:M.type)=="inner"?("end"===k&&(R.x+=10),"start"===k&&(R.x-=10)):"middle"===k&&(R.y+=2),"middle"===k?R.y+=0:R.y+=3,O.textAnchor=k,(0,n.addText)(Z,R.x,R.y,e,O,f.default.tickValueClassname)},n.addTick=function(t,e){e.props.labels;var r=v(t,e),i=g(t,e);r||(0,n.addTickLine)(t,e),f.default.debugTicksRadius||i||(0,n.addTickValue)(t,e)},n.getLabelCoordsByValue=function(t,n,e){void 0===e&&(e=0);var r,i=n.props.labels,o=n.props.minValue,u=n.props.maxValue,a=null===(r=i.tickLabels)||void 0===r?void 0:r.type,f=(0,d.getCoordByValue)(t,n,a,e,.93),l=f.x,s=f.y;return c.calculatePercentage(o,u,t),n.props.type==h.GaugeType.Radial&&(s+=3),{x:l,y:s}},n.addText=function(t,n,e,r,i,o,u){void 0===u&&(u=0);var a=r.g.current.append("g").attr("class",o).attr("transform","translate(".concat(n,", ").concat(e,")")).append("text").text(t);p(a,i),a.attr("transform","rotate(".concat(u,")"))};var p=function(t,n){Object.entries(n).forEach(function(n){var e=n[0],r=n[1];return t.style(c.camelCaseToKebabCase(e),r)}),void 0!=n&&Object.entries(n).forEach(function(n){var e=n[0],r=n[1];return t.style(c.camelCaseToKebabCase(e),r)})};n.addValueText=function(t){var e,i,o,u=t.props.labels,a=t.props.value,l=null==u?void 0:u.valueLabel,s="",p=null===(e=null==u?void 0:u.valueLabel)||void 0===e?void 0:e.maxDecimalDigits,v=c.floatingNumber(a,p),g=(null==(s=l.formatTextValue?l.formatTextValue(v):0===t.props.minValue&&100===t.props.maxValue?v.toString()+"%":v.toString())?void 0:s.length)||0,y=g>4?4/g*1.5:1,b=null===(i=null==l?void 0:l.style)||void 0===i?void 0:i.fontSize,m=r({},l.style),_=t.dimensions.current.outerRadius,x=0;m.textAnchor="middle",t.props.type==h.GaugeType.Semicircle?x=t.dimensions.current.outerRadius/1.5+20:t.props.type==h.GaugeType.Radial?x=1.45*t.dimensions.current.outerRadius+20:t.props.type==h.GaugeType.Grafana&&(x=1*t.dimensions.current.outerRadius+20);var w=(t.props.type,h.GaugeType.Radial,.003);y=t.dimensions.current.width*w*y;var M=parseInt(b,10)*y;m.fontSize=M+"px",l.matchColorWithArc&&(m.fill=(null===(o=(0,d.getArcDataByValue)(a,t))||void 0===o?void 0:o.color)||"white"),(0,n.addText)(s,_,x,t,m,f.default.valueLabelClassname)},n.clearValueLabel=function(t){return t.g.current.selectAll(".".concat(f.default.valueLabelClassname)).remove()},n.clearTicks=function(t){t.g.current.selectAll(".".concat(f.default.tickLineClassname)).remove(),t.g.current.selectAll(".".concat(f.default.tickValueClassname)).remove()},n.calculateAnchorAndAngleByValue=function(t,n){var e,r,i=n.props.labels,o=n.props.minValue,u=n.props.maxValue,a=c.calculatePercentage(o,u,t),l=((e={})[h.GaugeType.Grafana]={startAngle:-20,endAngle:220},e[h.GaugeType.Semicircle]={startAngle:0,endAngle:180},e[h.GaugeType.Radial]={startAngle:-42,endAngle:266},e)[n.props.type],s=l.startAngle,d=l.endAngle,p=a>f.default.rangeBetweenCenteredTickValueLabel[0]&&a<f.default.rangeBetweenCenteredTickValueLabel[1],v=(null===(r=null==i?void 0:i.tickLabels)||void 0===r?void 0:r.type)=="inner";return{tickAnchor:p?"middle":a<.5?v?"start":"end":v?"end":"start",angle:s+100*a*d/100}};var v=function(t,n){var e,r,i,o,u=n.props.labels,a=null===(e=l.defaultTickLabels.defaultTickLineConfig)||void 0===e?void 0:e.hide,c=null===(i=null===(r=null==u?void 0:u.tickLabels)||void 0===r?void 0:r.defaultTickLineConfig)||void 0===i?void 0:i.hide;void 0!=c&&(a=c);var f=null===(o=t.lineConfig)||void 0===o?void 0:o.hide;return void 0!=f&&(a=f),a},g=function(t,n){var e,r,i,o,u=n.props.labels,a=null===(e=l.defaultTickLabels.defaultTickValueConfig)||void 0===e?void 0:e.hide,c=null===(i=null===(r=null==u?void 0:u.tickLabels)||void 0===r?void 0:r.defaultTickValueConfig)||void 0===i?void 0:i.hide;void 0!=c&&(a=c);var f=null===(o=t.valueConfig)||void 0===o?void 0:o.hide;return void 0!=f&&(a=f),a}},54150:function(t,n,e){var r=this&&this.__createBinding||(Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]}),i=this&&this.__setModuleDefault||(Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n}),o=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&r(n,t,e);return i(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.clearPointerElement=n.addPointerElement=n.translatePointer=n.drawPointer=void 0;var u=e(29813),a=e(16887),c=e(57208),f=o(e(76507)),l=o(e(57208)),s=e(16593);n.drawPointer=function(t,n){void 0===n&&(n=!1),t.pointer.current.context=h(t);var e,r=t.pointer.current.context,i=r.prevPercent,o=r.currentPercent,a=r.prevProgress,c=t.props.pointer,f=(null===(e=t.prevProps)||void 0===e?void 0:e.current.value)==void 0;(f||n)&&t.props.type!=s.GaugeType.Grafana&&d(t),(!n||f)&&c.animate?t.doughnut.current.transition().delay(c.animationDelay).ease(c.elastic?u.easeElastic:u.easeExpOut).duration(c.animationDuration).tween("progress",function(){var n=(0,u.interpolateNumber)(i,o);return function(e){var r=n(e);g(r,a,t)&&(t.props.type==s.GaugeType.Grafana?(l.clearArcs(t),l.drawArc(t,r)):p(r,t)),t.pointer.current.context.prevProgress=r}}):p(o,t)};var h=function(t){var n,e=t.props.value,r=t.props.pointer,i=r.length,o=t.props.minValue,u=t.props.maxValue;t.pointer.current.context.pointerPath;var c=b(t),l=r.type==a.PointerType.Needle?i:.2,s=[a.PointerType.Needle,a.PointerType.Arrow];return{centerPoint:[0,-c/2],pointerRadius:b(t),pathLength:t.dimensions.current.outerRadius*l,currentPercent:f.calculatePercentage(o,u,e),prevPercent:f.calculatePercentage(o,u,(null===(n=t.prevProps)||void 0===n?void 0:n.current.value)||o),prevProgress:0,pathStr:"",shouldDrawPath:s.includes(r.type),prevColor:""}},d=function(t){var n=t.props.value,e=t.props.pointer,r=t.pointer.current.context,i=r.shouldDrawPath,o=r.centerPoint,u=r.pointerRadius,c=(r.pathStr,r.currentPercent),f=r.prevPercent;i&&(t.pointer.current.context.pathStr=y(t,f||c),t.pointer.current.path=t.pointer.current.element.append("path").attr("d",t.pointer.current.context.pathStr).attr("fill",e.color)),e.type==a.PointerType.Needle?t.pointer.current.element.append("circle").attr("cx",o[0]).attr("cy",o[1]).attr("r",u).attr("fill",e.color):e.type==a.PointerType.Blob&&t.pointer.current.element.append("circle").attr("cx",o[0]).attr("cy",o[1]).attr("r",u).attr("fill",e.baseColor).attr("stroke",e.color).attr("stroke-width",e.strokeWidth*u/10),v(u,n,t)},p=function(t,n){var e,r=n.props.pointer,i=n.pointer.current.context,o=i.pointerRadius,u=i.shouldDrawPath,c=i.prevColor;if(v(o,t,n),u&&n.props.type!=s.GaugeType.Grafana&&n.pointer.current.path.attr("d",y(n,t)),r.type==a.PointerType.Blob){var f=null===(e=l.getArcDataByPercentage(t,n))||void 0===e?void 0:e.color;f!=c&&n.pointer.current.element.select("circle").attr("stroke",f);var h=r.strokeWidth*o/10;n.pointer.current.element.select("circle").attr("stroke-width",h),n.pointer.current.context.prevColor=f}},v=function(t,e,r){var i,o=r.props.pointer.type,u=r.dimensions,l=f.getCurrentGaugeValueByPercentage(e,r);return((i={})[a.PointerType.Needle]=function(){(0,n.translatePointer)(u.current.outerRadius,u.current.outerRadius,r)},i[a.PointerType.Arrow]=function(){var e=(0,c.getCoordByValue)(l,r,"inner",0,.7),i=e.x,o=e.y;i-=1,o+=t-3,(0,n.translatePointer)(i,o,r)},i[a.PointerType.Blob]=function(){var e=(0,c.getCoordByValue)(l,r,"between",0,.75),i=e.x,o=e.y;i-=1,o+=t,(0,n.translatePointer)(i,o,r)},i)[o]()},g=function(t,n,e){return!(1e-4>Math.abs(t-n))&&t!=n&&!(t>1||t<0)},y=function(t,n){var e=t.pointer.current.context,r=e.centerPoint,i=e.pointerRadius,o=e.pathLength,u=f.degToRad(t.props.type==s.GaugeType.Semicircle?0:-42),a=u+n*(f.degToRad(t.props.type==s.GaugeType.Semicircle?180:223)-u),c=[r[0]-o*Math.cos(a),r[1]-o*Math.sin(a)],l=a-Math.PI/2,h=[r[0]-i*Math.cos(l),r[1]-i*Math.sin(l)],d=a+Math.PI/2,p=[r[0]-i*Math.cos(d),r[1]-i*Math.sin(d)];return"M ".concat(h[0]," ").concat(h[1]," L ").concat(c[0]," ").concat(c[1]," L ").concat(p[0]," ").concat(p[1])},b=function(t){var n=t.props.pointer.width;return t.dimensions.current.width/500*n};n.translatePointer=function(t,n,e){return e.pointer.current.element.attr("transform","translate("+t+", "+n+")")},n.addPointerElement=function(t){return t.pointer.current.element=t.g.current.append("g").attr("class","pointer")},n.clearPointerElement=function(t){return t.pointer.current.element.selectAll("*").remove()}},76507:function(t,n){var e=this&&this.__assign||function(){return(e=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)};Object.defineProperty(n,"__esModule",{value:!0}),n.camelCaseToKebabCase=n.getCurrentGaugeValueByPercentage=n.getCurrentGaugePercentageByValue=n.degToRad=n.normalize=n.floatingNumber=n.percentToRad=n.mergeObjects=n.isEmptyObject=n.calculatePercentage=void 0,n.calculatePercentage=function(t,n,e){return e<t?0:e>n?1:(e-t)/(n-t)},n.isEmptyObject=function(t){return 0===Object.keys(t).length&&t.constructor===Object},n.mergeObjects=function(t,r){var i=e({},t);return Object.keys(r).forEach(function(e){var o=t[e],u=r[e];Array.isArray(o)&&Array.isArray(u)?i[e]=u:"object"==typeof o&&"object"==typeof u?i[e]=(0,n.mergeObjects)(o,u):void 0!==u&&(i[e]=u)}),i},n.percentToRad=function(t,n){return Math.PI/n*t},n.floatingNumber=function(t,n){return void 0===n&&(n=2),Math.round(t*Math.pow(10,n))/Math.pow(10,n)},n.normalize=function(t,n,e){return(t-n)/(e-n)*100},n.degToRad=function(t){return Math.PI/180*t},n.getCurrentGaugePercentageByValue=function(t,e){return(0,n.calculatePercentage)(e.minValue,e.maxValue,t)},n.getCurrentGaugeValueByPercentage=function(t,n){var e=n.props.minValue;return e+t*(n.props.maxValue-e)},n.camelCaseToKebabCase=function(t){return t.replace(/[A-Z]/g,function(t){return"-".concat(t.toLowerCase())})}},17169:function(t,n,e){var r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)},i=this&&this.__createBinding||(Object.create?function(t,n,e,r){void 0===r&&(r=e);var i=Object.getOwnPropertyDescriptor(n,e);(!i||("get"in i?!n.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return n[e]}}),Object.defineProperty(t,r,i)}:function(t,n,e,r){void 0===r&&(r=e),t[r]=n[e]}),o=this&&this.__setModuleDefault||(Object.create?function(t,n){Object.defineProperty(t,"default",{enumerable:!0,value:n})}:function(t,n){t.default=n}),u=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var n={};if(null!=t)for(var e in t)"default"!==e&&Object.prototype.hasOwnProperty.call(t,e)&&i(n,t,e);return o(n,t),n};Object.defineProperty(n,"__esModule",{value:!0}),n.GaugeComponent=void 0;var a=u(e(60333)),c=e(29813),f=e(16593),l=u(e(60971)),s=u(e(57208)),h=e(76507),d=e(69156),p=e(16887),v=e(6965),g=function(t){var n=(0,a.useRef)({}),e=(0,a.useRef)({}),i=(0,a.useRef)({}),o=(0,a.useRef)({}),u=(0,a.useRef)(!0),g=(0,a.useRef)(0),y=(0,a.useRef)(r({},p.defaultPointerRef)),b=(0,a.useRef)({}),m=(0,a.useRef)([]),_=(0,a.useRef)((0,c.pie)()),x=(0,a.useRef)(r({},d.defaultDimensions)),w=(0,a.useRef)(t),M=(0,a.useRef)({}),A=(0,a.useRef)({}),T=(0,a.useRef)(null),S={props:w.current,resizeObserver:A,prevProps:M,svg:n,g:i,dimensions:x,doughnut:o,isFirstRun:u,currentProgress:g,pointer:y,container:b,arcData:m,pieChart:_,tooltip:e},k=function(){var n,e,i=r({},f.defaultGaugeProps);S.props=w.current=(0,h.mergeObjects)(i,t),(null===(n=S.props.arc)||void 0===n?void 0:n.width)==(null===(e=f.defaultGaugeProps.arc)||void 0===e?void 0:e.width)&&(w.current.arc.width=(0,v.getArcWidthByType)(S.props.type)),S.props.marginInPercent==f.defaultGaugeProps.marginInPercent&&(w.current.marginInPercent=(0,f.getGaugeMarginByType)(S.props.type)),s.validateArcs(S)},P=function(){var t=JSON.stringify(M.current.arc)!==JSON.stringify(w.current.arc),n=JSON.stringify(M.current.pointer)!==JSON.stringify(w.current.pointer),e=JSON.stringify(M.current.value)!==JSON.stringify(w.current.value),r=JSON.stringify(M.current.minValue)!==JSON.stringify(w.current.minValue),i=JSON.stringify(M.current.maxValue)!==JSON.stringify(w.current.maxValue);return t||n||e||r||i};(0,a.useLayoutEffect)(function(){k(),u.current=(0,h.isEmptyObject)(b.current),u.current&&(b.current=(0,c.select)(T.current)),P()&&l.initChart(S,u.current),S.prevProps.current=w.current},[t]),(0,a.useEffect)(function(){var t=function(){return l.renderChart(S,!0)};return window.addEventListener("resize",t),function(){return window.removeEventListener("resize",t)}},[t]),(0,a.useEffect)(function(){var t=T.current;if(t){var n=new ResizeObserver(function(){l.renderChart(S,!0)});return S.resizeObserver.current=n,t.parentNode&&n.observe(t.parentNode),function(){var t;S.resizeObserver&&(null===(t=S.resizeObserver.current)||void 0===t||t.disconnect(),delete S.resizeObserver.current)}}},[]);var E=t.id,C=t.style,N=t.className;return t.type,a.default.createElement("div",{id:E,className:"".concat(S.props.type,"-gauge").concat(N?" "+N:""),style:C,ref:function(t){return T.current=t}})};n.GaugeComponent=g,n.default=g},6965:function(t,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.defaultArc=n.getArcWidthByType=n.defaultSubArcs=void 0;var r=e(16593);n.defaultSubArcs=[{limit:33,color:"#5BE12C"},{limit:66,color:"#F5CD19"},{color:"#EA4228"}],n.getArcWidthByType=function(t){var n,e=((n={})[r.GaugeType.Grafana]=.25,n[r.GaugeType.Semicircle]=.15,n[r.GaugeType.Radial]=.2,n);return t||(t=r.defaultGaugeProps.type),e[t]},n.defaultArc={padding:.05,width:.25,cornerRadius:7,nbSubArcs:void 0,emptyColor:"#5C5C5C",colorArray:void 0,subArcs:n.defaultSubArcs,gradient:!1}},69156:function(t,n){Object.defineProperty(n,"__esModule",{value:!0}),n.defaultDimensions=n.defaultAngles=n.defaultMargins=void 0,n.defaultMargins={top:0,right:0,bottom:0,left:0},n.defaultAngles={startAngle:0,endAngle:0,startAngleDeg:0,endAngleDeg:0},n.defaultDimensions={width:0,height:0,margin:n.defaultMargins,outerRadius:0,innerRadius:0,angles:n.defaultAngles,fixedHeight:0}},16593:function(t,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.getGaugeMarginByType=n.defaultGaugeProps=n.GaugeType=void 0;var r,i,o=e(6965),u=e(27031),a=e(16887);(r=i||(n.GaugeType=i={})).Semicircle="semicircle",r.Radial="radial",r.Grafana="grafana",n.defaultGaugeProps={id:"",className:"gauge-component-class",style:{width:"100%"},marginInPercent:.07,value:33,minValue:0,maxValue:100,arc:o.defaultArc,labels:u.defaultLabels,pointer:a.defaultPointer,type:i.Grafana},n.getGaugeMarginByType=function(t){var n;return((n={})[i.Grafana]={top:.12,bottom:0,left:.07,right:.07},n[i.Semicircle]={top:.08,bottom:0,left:.08,right:.08},n[i.Radial]={top:.07,bottom:0,left:.07,right:.07},n)[t]}},27031:function(t,n,e){Object.defineProperty(n,"__esModule",{value:!0}),n.defaultLabels=n.defaultValueLabel=void 0;var r=e(1699);n.defaultValueLabel={formatTextValue:void 0,matchColorWithArc:!1,maxDecimalDigits:2,style:{fontSize:"35px",fill:"#fff",textShadow:"black 1px 0.5px 0px, black 0px 0px 0.03em, black 0px 0px 0.01em"},hide:!1},n.defaultLabels={valueLabel:n.defaultValueLabel,tickLabels:r.defaultTickLabels}},16887:function(t,n){var e,r;Object.defineProperty(n,"__esModule",{value:!0}),n.defaultPointer=n.defaultPointerRef=n.defaultPointerContext=n.PointerType=void 0,(r=e||(n.PointerType=e={})).Needle="needle",r.Blob="blob",r.Arrow="arrow",n.defaultPointerContext={centerPoint:[0,0],pointerRadius:0,pathLength:0,currentPercent:0,prevPercent:0,prevProgress:0,pathStr:"",shouldDrawPath:!1,prevColor:""},n.defaultPointerRef={element:void 0,path:void 0,context:n.defaultPointerContext},n.defaultPointer={type:e.Needle,color:"#5A5A5A",baseColor:"white",length:.7,width:20,animate:!0,elastic:!1,hide:!1,animationDuration:3e3,animationDelay:100,strokeWidth:8}},1699:function(t,n){Object.defineProperty(n,"__esModule",{value:!0}),n.defaultTickLabels=void 0,n.defaultTickLabels={type:"outer",hideMinMax:!1,ticks:[],defaultTickValueConfig:{formatTextValue:void 0,maxDecimalDigits:2,style:{fontSize:"10px",fill:"rgb(173 172 171)"},hide:!1},defaultTickLineConfig:{color:"rgb(173 172 171)",length:7,width:1,distanceFromArc:3,hide:!1}}},20237:function(t,n){Object.defineProperty(n,"__esModule",{value:!0}),n.defaultTooltipStyle=void 0,n.defaultTooltipStyle={borderColor:"#5A5A5A",borderStyle:"solid",borderWidth:"1px",borderRadius:"5px",color:"white",padding:"5px",fontSize:"15px",textShadow:"1px 1px 2px black, 0 0 1em black, 0 0 0.2em black"}},74490:function(t,n,e){var r=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(n,"__esModule",{value:!0}),n.GaugeComponent=void 0;var i=r(e(17169));n.GaugeComponent=i.default,n.default=i.default},29813:function(t,n,e){let r,i,o,u;e.r(n),e.d(n,{Adder:function(){return k},Delaunay:function(){return iy},FormatSpecifier:function(){return ui.v},InternMap:function(){return C.L},InternSet:function(){return C.H},Node:function(){return lp},Path:function(){return ra.y$},Voronoi:function(){return ih},ZoomTransform:function(){return pK},active:function(){return eP},arc:function(){return dx},area:function(){return dw.Z},areaRadial:function(){return dZ},ascending:function(){return c.Z},autoType:function(){return i$},axisBottom:function(){return t$},axisLeft:function(){return tX},axisRight:function(){return tY},axisTop:function(){return tH},bin:function(){return K},bisect:function(){return a.ZP},bisectCenter:function(){return a.ZR},bisectLeft:function(){return a.Nw},bisectRight:function(){return a.ml},bisector:function(){return f.Z},blob:function(){return oT},blur:function(){return l},blur2:function(){return s},blurImage:function(){return h},brush:function(){return e6},brushSelection:function(){return e0},brushX:function(){return e1},brushY:function(){return e2},buffer:function(){return ok},chord:function(){return rr},chordDirected:function(){return ro},chordTranspose:function(){return ri},cluster:function(){return la},color:function(){return ed.ZP},contourDensity:function(){return r2},contours:function(){return rX},count:function(){return y},create:function(){return du},creator:function(){return nx},cross:function(){return x},csv:function(){return oR},csvFormat:function(){return iZ},csvFormatBody:function(){return iz},csvFormatRow:function(){return iL},csvFormatRows:function(){return iB},csvFormatValue:function(){return iV},csvParse:function(){return iR},csvParseRows:function(){return iO},cubehelix:function(){return rj},cumsum:function(){return w},curveBasis:function(){return d4.ZP},curveBasisClosed:function(){return d8.Z},curveBasisOpen:function(){return d3.Z},curveBumpX:function(){return dB.sj},curveBumpY:function(){return dB.BW},curveBundle:function(){return d7},curveCardinal:function(){return pe},curveCardinalClosed:function(){return pi},curveCardinalOpen:function(){return pu},curveCatmullRom:function(){return pf},curveCatmullRomClosed:function(){return ps},curveCatmullRomOpen:function(){return pd},curveLinear:function(){return dP.Z},curveLinearClosed:function(){return pp.Z},curveMonotoneX:function(){return pv.Z},curveMonotoneY:function(){return pv.s},curveNatural:function(){return pg.Z},curveStep:function(){return py.ZP},curveStepAfter:function(){return py.cD},curveStepBefore:function(){return py.RN},descending:function(){return M.Z},deviation:function(){return T},difference:function(){return tR},disjoint:function(){return tO},dispatch:function(){return tQ},drag:function(){return iA},dragDisable:function(){return nV},dragEnable:function(){return nD},dsv:function(){return oN},dsvFormat:function(){return iC},easeBack:function(){return om},easeBackIn:function(){return oy},easeBackInOut:function(){return om},easeBackOut:function(){return ob},easeBounce:function(){return ov},easeBounceIn:function(){return op},easeBounceInOut:function(){return og},easeBounceOut:function(){return ov},easeCircle:function(){return or},easeCircleIn:function(){return on},easeCircleInOut:function(){return or},easeCircleOut:function(){return oe},easeCubic:function(){return eT},easeCubicIn:function(){return eM},easeCubicInOut:function(){return eT},easeCubicOut:function(){return eA},easeElastic:function(){return ow},easeElasticIn:function(){return ox},easeElasticInOut:function(){return oM},easeElasticOut:function(){return ow},easeExp:function(){return ot},easeExpIn:function(){return i7},easeExpInOut:function(){return ot},easeExpOut:function(){return i9},easeLinear:function(){return iW},easePoly:function(){return i1},easePolyIn:function(){return iQ},easePolyInOut:function(){return i1},easePolyOut:function(){return i0},easeQuad:function(){return iJ},easeQuadIn:function(){return iK},easeQuadInOut:function(){return iJ},easeQuadOut:function(){return iU},easeSin:function(){return i4},easeSinIn:function(){return i8},easeSinInOut:function(){return i4},easeSinOut:function(){return i3},every:function(){return tS},extent:function(){return S},fcumsum:function(){return E},filter:function(){return tP},flatGroup:function(){return z},flatRollup:function(){return B},forceCenter:function(){return oj},forceCollide:function(){return o0},forceLink:function(){return o6},forceManyBody:function(){return o7},forceRadial:function(){return o9},forceSimulation:function(){return o5},forceX:function(){return ut},forceY:function(){return un},format:function(){return ue.WU},formatDefaultLocale:function(){return ue.ZP},formatLocale:function(){return ur.Z},formatPrefix:function(){return ue.jH},formatSpecifier:function(){return ui.Z},fsum:function(){return P},geoAlbers:function(){return fB},geoAlbersUsa:function(){return fL},geoArea:function(){return uW},geoAzimuthalEqualArea:function(){return fj},geoAzimuthalEqualAreaRaw:function(){return fG},geoAzimuthalEquidistant:function(){return fq},geoAzimuthalEquidistantRaw:function(){return fI},geoBounds:function(){return ai},geoCentroid:function(){return ag},geoCircle:function(){return ak},geoClipAntimeridian:function(){return aV},geoClipCircle:function(){return aD},geoClipExtent:function(){return aj},geoClipRectangle:function(){return aG},geoConicConformal:function(){return fW},geoConicConformalRaw:function(){return fX},geoConicEqualArea:function(){return fz},geoConicEqualAreaRaw:function(){return fZ},geoConicEquidistant:function(){return fQ},geoConicEquidistantRaw:function(){return fJ},geoContains:function(){return a6},geoDistance:function(){return aW},geoEqualEarth:function(){return f2},geoEqualEarthRaw:function(){return f1},geoEquirectangular:function(){return fU},geoEquirectangularRaw:function(){return fK},geoGnomonic:function(){return f8},geoGnomonicRaw:function(){return f6},geoGraticule:function(){return a4},geoGraticule10:function(){return a5},geoIdentity:function(){return f3},geoInterpolate:function(){return a7},geoLength:function(){return aY},geoMercator:function(){return fH},geoMercatorRaw:function(){return fF},geoNaturalEarth1:function(){return f5},geoNaturalEarth1Raw:function(){return f4},geoOrthographic:function(){return f9},geoOrthographicRaw:function(){return f7},geoPath:function(){return fb},geoProjection:function(){return fN},geoProjectionMutator:function(){return fR},geoRotation:function(){return aA},geoStereographic:function(){return ln},geoStereographicRaw:function(){return lt},geoStream:function(){return uz},geoTransform:function(){return fm},geoTransverseMercator:function(){return lr},geoTransverseMercatorRaw:function(){return le},gray:function(){return rP},greatest:function(){return ty.Z},greatestIndex:function(){return tb},group:function(){return R},groupSort:function(){return F},groups:function(){return O},hcl:function(){return rL},hierarchy:function(){return lf},histogram:function(){return K},hsl:function(){return ed.Ym},html:function(){return oD},image:function(){return oZ},index:function(){return D},indexes:function(){return G},interpolate:function(){return nG.Z},interpolateArray:function(){return l7.Z},interpolateBasis:function(){return l9.Z},interpolateBasisClosed:function(){return st.Z},interpolateBlues:function(){return hI},interpolateBrBG:function(){return ht},interpolateBuGn:function(){return hb},interpolateBuPu:function(){return h_},interpolateCividis:function(){return hQ},interpolateCool:function(){return h2},interpolateCubehelix:function(){return sb},interpolateCubehelixDefault:function(){return h0},interpolateCubehelixLong:function(){return sm},interpolateDate:function(){return sn.Z},interpolateDiscrete:function(){return se},interpolateGnBu:function(){return hw},interpolateGreens:function(){return hF},interpolateGreys:function(){return hY},interpolateHcl:function(){return sv},interpolateHclLong:function(){return sg},interpolateHsl:function(){return ss},interpolateHslLong:function(){return sh},interpolateHue:function(){return si},interpolateInferno:function(){return dr},interpolateLab:function(){return sd},interpolateMagma:function(){return de},interpolateNumber:function(){return eo.Z},interpolateNumberArray:function(){return so.Z},interpolateObject:function(){return su.Z},interpolateOrRd:function(){return hA},interpolateOranges:function(){return hJ},interpolatePRGn:function(){return he},interpolatePiYG:function(){return hi},interpolatePlasma:function(){return di},interpolatePuBu:function(){return hP},interpolatePuBuGn:function(){return hS},interpolatePuOr:function(){return hu},interpolatePuRd:function(){return hC},interpolatePurples:function(){return hX},interpolateRainbow:function(){return h8},interpolateRdBu:function(){return hc},interpolateRdGy:function(){return hl},interpolateRdPu:function(){return hR},interpolateRdYlBu:function(){return hh},interpolateRdYlGn:function(){return hp},interpolateReds:function(){return hK},interpolateRgb:function(){return ep.ZP},interpolateRgbBasis:function(){return ep.hD},interpolateRgbBasisClosed:function(){return ep.YD},interpolateRound:function(){return sa.Z},interpolateSinebow:function(){return h7},interpolateSpectral:function(){return hg},interpolateString:function(){return ev.Z},interpolateTransformCss:function(){return el},interpolateTransformSvg:function(){return es},interpolateTurbo:function(){return h9},interpolateViridis:function(){return dn},interpolateWarm:function(){return h1},interpolateYlGn:function(){return hB},interpolateYlGnBu:function(){return hZ},interpolateYlOrBr:function(){return hV},interpolateYlOrRd:function(){return hG},interpolateZoom:function(){return sf},interrupt:function(){return ei},intersection:function(){return tZ},interval:function(){return p$},isoFormat:function(){return pH},isoParse:function(){return pY},json:function(){return oB},lab:function(){return rE},lch:function(){return rB},least:function(){return tv},leastIndex:function(){return tg},line:function(){return dM.Z},lineRadial:function(){return dO},link:function(){return dG},linkHorizontal:function(){return dj},linkRadial:function(){return dq},linkVertical:function(){return dI},local:function(){return dc},map:function(){return tE},matcher:function(){return t3},max:function(){return tt.Z},maxIndex:function(){return tn.Z},mean:function(){return te},median:function(){return tr},medianIndex:function(){return ti},merge:function(){return to},min:function(){return tu.Z},minIndex:function(){return ta.Z},mode:function(){return tc},namespace:function(){return nf},namespaces:function(){return nc},nice:function(){return X},now:function(){return nQ},pack:function(){return lZ},packEnclose:function(){return l_},packSiblings:function(){return lR},pairs:function(){return tf},partition:function(){return lG},path:function(){return ra.ET},pathRound:function(){return ra.xK},permute:function(){return ts.Z},pie:function(){return dk},piecewise:function(){return s_.Z},pointRadial:function(){return dz.Z},pointer:function(){return nI},pointers:function(){return dl},polygonArea:function(){return sw},polygonCentroid:function(){return sM},polygonContains:function(){return sk},polygonHull:function(){return sS},polygonLength:function(){return sP},precisionFixed:function(){return uo.Z},precisionPrefix:function(){return uu.Z},precisionRound:function(){return ua.Z},quadtree:function(){return oY},quantile:function(){return U.ZP},quantileIndex:function(){return U.Cr},quantileSorted:function(){return U.s7},quantize:function(){return sx},quickselect:function(){return th.Z},radialArea:function(){return dZ},radialLine:function(){return dO},randomBates:function(){return sz},randomBernoulli:function(){return sV},randomBeta:function(){return sj},randomBinomial:function(){return sI},randomCauchy:function(){return sF},randomExponential:function(){return sB},randomGamma:function(){return sG},randomGeometric:function(){return sD},randomInt:function(){return sN},randomIrwinHall:function(){return sZ},randomLcg:function(){return sX},randomLogNormal:function(){return sO},randomLogistic:function(){return sH},randomNormal:function(){return sR},randomPareto:function(){return sL},randomPoisson:function(){return sY},randomUniform:function(){return sC},randomWeibull:function(){return sq},range:function(){return td.Z},rank:function(){return tp},reduce:function(){return tC},reverse:function(){return tN},rgb:function(){return ed.B8},ribbon:function(){return rb},ribbonArrow:function(){return rm},rollup:function(){return L},rollups:function(){return V},scaleBand:function(){return sW.ti},scaleDiverging:function(){return sW.AB},scaleDivergingLog:function(){return sW.Wr},scaleDivergingPow:function(){return sW.dK},scaleDivergingSqrt:function(){return sW.KR},scaleDivergingSymlog:function(){return sW.b4},scaleIdentity:function(){return sW.ez},scaleImplicit:function(){return sW.qm},scaleLinear:function(){return sW.BY},scaleLog:function(){return sW.p2},scaleOrdinal:function(){return sW.PK},scalePoint:function(){return sW.q2},scalePow:function(){return sW.vY},scaleQuantile:function(){return sW.FT},scaleQuantize:function(){return sW.aE},scaleRadial:function(){return sW.s$},scaleSequential:function(){return sW.cJ},scaleSequentialLog:function(){return sW.$l},scaleSequentialPow:function(){return sW.bE},scaleSequentialQuantile:function(){return sW.IO},scaleSequentialSqrt:function(){return sW.aA},scaleSequentialSymlog:function(){return sW.lQ},scaleSqrt:function(){return sW.PU},scaleSymlog:function(){return sW.eh},scaleThreshold:function(){return sW.ut},scaleTime:function(){return sW.Xf},scaleUtc:function(){return sW.KY},scan:function(){return tm},schemeAccent:function(){return sJ},schemeBlues:function(){return hj},schemeBrBG:function(){return s9},schemeBuGn:function(){return hy},schemeBuPu:function(){return hm},schemeCategory10:function(){return sU},schemeDark2:function(){return sQ},schemeGnBu:function(){return hx},schemeGreens:function(){return hq},schemeGreys:function(){return hH},schemeObservable10:function(){return s0},schemeOrRd:function(){return hM},schemeOranges:function(){return hU},schemePRGn:function(){return hn},schemePaired:function(){return s1},schemePastel1:function(){return s2},schemePastel2:function(){return s6},schemePiYG:function(){return hr},schemePuBu:function(){return hk},schemePuBuGn:function(){return hT},schemePuOr:function(){return ho},schemePuRd:function(){return hE},schemePurples:function(){return h$},schemeRdBu:function(){return ha},schemeRdGy:function(){return hf},schemeRdPu:function(){return hN},schemeRdYlBu:function(){return hs},schemeRdYlGn:function(){return hd},schemeReds:function(){return hW},schemeSet1:function(){return s8},schemeSet2:function(){return s3},schemeSet3:function(){return s4},schemeSpectral:function(){return hv},schemeTableau10:function(){return s5},schemeYlGn:function(){return hz},schemeYlGnBu:function(){return hO},schemeYlOrBr:function(){return hL},schemeYlOrRd:function(){return hD},select:function(){return nO},selectAll:function(){return ds},selection:function(){return nR},selector:function(){return t1},selectorAll:function(){return t8},shuffle:function(){return t_},shuffler:function(){return tx},some:function(){return tk},sort:function(){return q.ZP},stack:function(){return pb.Z},stackOffsetDiverging:function(){return p_},stackOffsetExpand:function(){return pm.Z},stackOffsetNone:function(){return px.Z},stackOffsetSilhouette:function(){return pw.Z},stackOffsetWiggle:function(){return pM.Z},stackOrderAppearance:function(){return pT},stackOrderAscending:function(){return pk},stackOrderDescending:function(){return pE},stackOrderInsideOut:function(){return pC},stackOrderNone:function(){return pA.Z},stackOrderReverse:function(){return pN},stratify:function(){return lY},style:function(){return ns},subset:function(){return tV},sum:function(){return tw},superset:function(){return tB},svg:function(){return oG},symbol:function(){return dF.ZP},symbolAsterisk:function(){return dH.Z},symbolCircle:function(){return dY.Z},symbolCross:function(){return d$.Z},symbolDiamond:function(){return dX.Z},symbolDiamond2:function(){return dW.Z},symbolPlus:function(){return dK.Z},symbolSquare:function(){return dU.Z},symbolSquare2:function(){return dJ.Z},symbolStar:function(){return dQ.Z},symbolTimes:function(){return d6.Z},symbolTriangle:function(){return d0.Z},symbolTriangle2:function(){return d1.Z},symbolWye:function(){return d2.Z},symbolX:function(){return d6.Z},symbols:function(){return dF.Hl},symbolsFill:function(){return dF.Hl},symbolsStroke:function(){return dF.WY},text:function(){return oE},thresholdFreedmanDiaconis:function(){return J},thresholdScott:function(){return Q},thresholdSturges:function(){return W},tickFormat:function(){return sW.uk},tickIncrement:function(){return $.G9},tickStep:function(){return $.ly},ticks:function(){return $.ZP},timeDay:function(){return pL.rr},timeDays:function(){return pL.Nu},timeFormat:function(){return pI.i$},timeFormatDefaultLocale:function(){return pI.ZP},timeFormatLocale:function(){return pq.Z},timeFriday:function(){return pV.y2},timeFridays:function(){return pV.$N},timeHour:function(){return pB.WQ},timeHours:function(){return pB.w8},timeInterval:function(){return pR.J},timeMillisecond:function(){return pO.A},timeMilliseconds:function(){return pO.m},timeMinute:function(){return pz.Z_},timeMinutes:function(){return pz.Xo},timeMonday:function(){return pV.Ox},timeMondays:function(){return pV.ip},timeMonth:function(){return pD.F0},timeMonths:function(){return pD.K_},timeParse:function(){return pI.Z1},timeSaturday:function(){return pV.Lq},timeSaturdays:function(){return pV.X2},timeSecond:function(){return pZ.E},timeSeconds:function(){return pZ.m},timeSunday:function(){return pV.Zy},timeSundays:function(){return pV.WC},timeThursday:function(){return pV.Ig},timeThursdays:function(){return pV.Pl},timeTickInterval:function(){return pj._g},timeTicks:function(){return pj.jK},timeTuesday:function(){return pV.YD},timeTuesdays:function(){return pV.RA},timeWednesday:function(){return pV.EF},timeWednesdays:function(){return pV.S3},timeWeek:function(){return pV.Zy},timeWeeks:function(){return pV.WC},timeYear:function(){return pG.jB},timeYears:function(){return pG.HK},timeout:function(){return n5},timer:function(){return n2},timerFlush:function(){return n6},transition:function(){return ex},transpose:function(){return tM},tree:function(){return lQ},treemap:function(){return l8},treemapBinary:function(){return l3},treemapDice:function(){return lD},treemapResquarify:function(){return l5},treemapSlice:function(){return l0},treemapSliceDice:function(){return l4},treemapSquarify:function(){return l6},tsv:function(){return oO},tsvFormat:function(){return iI},tsvFormatBody:function(){return iq},tsvFormatRow:function(){return iH},tsvFormatRows:function(){return iF},tsvFormatValue:function(){return iY},tsvParse:function(){return iG},tsvParseRows:function(){return ij},union:function(){return tD},unixDay:function(){return pL.KB},unixDays:function(){return pL.iE},utcDay:function(){return pL.AN},utcDays:function(){return pL.yw},utcFormat:function(){return pI.g0},utcFriday:function(){return pV.QQ},utcFridays:function(){return pV.fz},utcHour:function(){return pB.lM},utcHours:function(){return pB.Xt},utcMillisecond:function(){return pO.A},utcMilliseconds:function(){return pO.m},utcMinute:function(){return pz.rz},utcMinutes:function(){return pz.NH},utcMonday:function(){return pV.l6},utcMondays:function(){return pV.$3},utcMonth:function(){return pD.me},utcMonths:function(){return pD.Ks},utcParse:function(){return pI.wp},utcSaturday:function(){return pV.g4},utcSaturdays:function(){return pV.Q_},utcSecond:function(){return pZ.E},utcSeconds:function(){return pZ.m},utcSunday:function(){return pV.pI},utcSundays:function(){return pV.SU},utcThursday:function(){return pV.hB},utcThursdays:function(){return pV.xj},utcTickInterval:function(){return pj.jo},utcTicks:function(){return pj.WG},utcTuesday:function(){return pV.J1},utcTuesdays:function(){return pV.DK},utcWednesday:function(){return pV.b3},utcWednesdays:function(){return pV.uy},utcWeek:function(){return pV.pI},utcWeeks:function(){return pV.SU},utcYear:function(){return pG.ol},utcYears:function(){return pG.DX},variance:function(){return A},window:function(){return nl},xml:function(){return oV},zip:function(){return tT},zoom:function(){return p5},zoomIdentity:function(){return pU},zoomTransform:function(){return pJ}});var a=e(96985),c=e(77132),f=e(15016);function l(t,n){if(!((n=+n)>=0))throw RangeError("invalid r");let e=t.length;if(!((e=Math.floor(e))>=0))throw RangeError("invalid length");if(!e||!n)return t;let r=g(n),i=t.slice();return r(t,i,0,e,1),r(i,t,0,e,1),r(t,i,0,e,1),t}let s=d(g),h=d(function(t){let n=g(t);return(t,e,r,i,o)=>{n(t,e,(r<<=2)+0,(i<<=2)+0,o<<=2),n(t,e,r+1,i+1,o),n(t,e,r+2,i+2,o),n(t,e,r+3,i+3,o)}});function d(t){return function(n,e,r=e){if(!((e=+e)>=0))throw RangeError("invalid rx");if(!((r=+r)>=0))throw RangeError("invalid ry");let{data:i,width:o,height:u}=n;if(!((o=Math.floor(o))>=0))throw RangeError("invalid width");if(!((u=Math.floor(void 0!==u?u:i.length/o))>=0))throw RangeError("invalid height");if(!o||!u||!e&&!r)return n;let a=e&&t(e),c=r&&t(r),f=i.slice();return a&&c?(p(a,f,i,o,u),p(a,i,f,o,u),p(a,f,i,o,u),v(c,i,f,o,u),v(c,f,i,o,u),v(c,i,f,o,u)):a?(p(a,i,f,o,u),p(a,f,i,o,u),p(a,i,f,o,u)):c&&(v(c,i,f,o,u),v(c,f,i,o,u),v(c,i,f,o,u)),n}}function p(t,n,e,r,i){for(let o=0,u=r*i;o<u;)t(n,e,o,o+=r,1)}function v(t,n,e,r,i){for(let o=0,u=r*i;o<r;++o)t(n,e,o,o+u,r)}function g(t){let n=Math.floor(t);if(n===t)return function(t){let n=2*t+1;return(e,r,i,o,u)=>{if(!((o-=u)>=i))return;let a=t*r[i],c=u*t;for(let t=i,n=i+c;t<n;t+=u)a+=r[Math.min(o,t)];for(let t=i,f=o;t<=f;t+=u)a+=r[Math.min(o,t+c)],e[t]=a/n,a-=r[Math.max(i,t-c)]}}(t);let e=t-n,r=2*t+1;return(t,i,o,u,a)=>{if(!((u-=a)>=o))return;let c=n*i[o],f=a*n,l=f+a;for(let t=o,n=o+f;t<n;t+=a)c+=i[Math.min(u,t)];for(let n=o,s=u;n<=s;n+=a)c+=i[Math.min(u,n+f)],t[n]=(c+e*(i[Math.max(o,n-l)]+i[Math.min(u,n+l)]))/r,c-=i[Math.max(o,n-f)]}}function y(t,n){let e=0;if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&++e;else{let r=-1;for(let i of t)null!=(i=n(i,++r,t))&&(i=+i)>=i&&++e}return e}function b(t){return 0|t.length}function m(t){return!(t>0)}function _(t){return"object"!=typeof t||"length"in t?t:Array.from(t)}function x(...t){var n;let e="function"==typeof t[t.length-1]&&(n=t.pop(),t=>n(...t)),r=(t=t.map(_)).map(b),i=t.length-1,o=Array(i+1).fill(0),u=[];if(i<0||r.some(m))return u;for(;;){u.push(o.map((n,e)=>t[e][n]));let n=i;for(;++o[n]===r[n];){if(0===n)return e?u.map(e):u;o[n--]=0}}}function w(t,n){var e=0,r=0;return Float64Array.from(t,void 0===n?t=>e+=+t||0:i=>e+=+n(i,r++,t)||0)}var M=e(58308);function A(t,n){let e,r=0,i=0,o=0;if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&(e=n-i,i+=e/++r,o+=e*(n-i));else{let u=-1;for(let a of t)null!=(a=n(a,++u,t))&&(a=+a)>=a&&(e=a-i,i+=e/++r,o+=e*(a-i))}if(r>1)return o/(r-1)}function T(t,n){let e=A(t,n);return e?Math.sqrt(e):e}function S(t,n){let e,r;if(void 0===n)for(let n of t)null!=n&&(void 0===e?n>=n&&(e=r=n):(e>n&&(e=n),r<n&&(r=n)));else{let i=-1;for(let o of t)null!=(o=n(o,++i,t))&&(void 0===e?o>=o&&(e=r=o):(e>o&&(e=o),r<o&&(r=o)))}return[e,r]}class k{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){let n=this._partials,e=0;for(let r=0;r<this._n&&r<32;r++){let i=n[r],o=t+i,u=Math.abs(t)<Math.abs(i)?t-(o-i):i-(o-t);u&&(n[e++]=u),t=o}return n[e]=t,this._n=e+1,this}valueOf(){let t=this._partials,n=this._n,e,r,i,o=0;if(n>0){for(o=t[--n];n>0&&(o=(e=o)+(r=t[--n]),!(i=r-(o-e))););n>0&&(i<0&&t[n-1]<0||i>0&&t[n-1]>0)&&(e=o+(r=2*i),r==e-o&&(o=e))}return o}}function P(t,n){let e=new k;if(void 0===n)for(let n of t)(n=+n)&&e.add(n);else{let r=-1;for(let i of t)(i=+n(i,++r,t))&&e.add(i)}return+e}function E(t,n){let e=new k,r=-1;return Float64Array.from(t,void 0===n?t=>e.add(+t||0):i=>e.add(+n(i,++r,t)||0))}var C=e(44311);function N(t){return t}function R(t,...n){return I(t,N,N,n)}function O(t,...n){return I(t,Array.from,N,n)}function Z(t,n){for(let e=1,r=n.length;e<r;++e)t=t.flatMap(t=>t.pop().map(([n,e])=>[...t,n,e]));return t}function z(t,...n){return Z(O(t,...n),n)}function B(t,n,...e){return Z(V(t,n,...e),e)}function L(t,n,...e){return I(t,N,n,e)}function V(t,n,...e){return I(t,Array.from,n,e)}function D(t,...n){return I(t,N,j,n)}function G(t,...n){return I(t,Array.from,j,n)}function j(t){if(1!==t.length)throw Error("duplicate key");return t[0]}function I(t,n,e,r){return function t(i,o){if(o>=r.length)return e(i);let u=new C.L,a=r[o++],c=-1;for(let t of i){let n=a(t,++c,i),e=u.get(n);e?e.push(t):u.set(n,[t])}for(let[n,e]of u)u.set(n,t(e,o));return n(u)}(t,0)}var q=e(26583);function F(t,n,e){return(2!==n.length?(0,q.ZP)(L(t,n,e),([t,n],[e,r])=>(0,c.Z)(n,r)||(0,c.Z)(t,e)):(0,q.ZP)(R(t,e),([t,e],[r,i])=>n(e,i)||(0,c.Z)(t,r))).map(([t])=>t)}var H=Array.prototype,Y=H.slice;H.map;var $=e(2968);function X(t,n,e){let r;for(;;){let i=(0,$.G9)(t,n,e);if(i===r||0===i||!isFinite(i))return[t,n];i>0?(t=Math.floor(t/i)*i,n=Math.ceil(n/i)*i):i<0&&(t=Math.ceil(t*i)/i,n=Math.floor(n*i)/i),r=i}}function W(t){return Math.max(1,Math.ceil(Math.log(y(t))/Math.LN2)+1)}function K(){var t=N,n=S,e=W;function r(r){Array.isArray(r)||(r=Array.from(r));var i,o,u,c=r.length,f=Array(c);for(i=0;i<c;++i)f[i]=t(r[i],i,r);var l=n(f),s=l[0],h=l[1],d=e(f,s,h);if(!Array.isArray(d)){let t=h,e=+d;if(n===S&&([s,h]=X(s,h,e)),(d=(0,$.ZP)(s,h,e))[0]<=s&&(u=(0,$.G9)(s,h,e)),d[d.length-1]>=h){if(t>=h&&n===S){let t=(0,$.G9)(s,h,e);isFinite(t)&&(t>0?h=(Math.floor(h/t)+1)*t:t<0&&(h=-((Math.ceil(-(h*t))+1)/t)))}else d.pop()}}for(var p=d.length,v=0,g=p;d[v]<=s;)++v;for(;d[g-1]>h;)--g;(v||g<p)&&(d=d.slice(v,g),p=g-v);var y,b=Array(p+1);for(i=0;i<=p;++i)(y=b[i]=[]).x0=i>0?d[i-1]:s,y.x1=i<p?d[i]:h;if(isFinite(u)){if(u>0)for(i=0;i<c;++i)null!=(o=f[i])&&s<=o&&o<=h&&b[Math.min(p,Math.floor((o-s)/u))].push(r[i]);else if(u<0){for(i=0;i<c;++i)if(null!=(o=f[i])&&s<=o&&o<=h){let t=Math.floor((s-o)*u);b[Math.min(p,t+(d[t]<=o))].push(r[i])}}}else for(i=0;i<c;++i)null!=(o=f[i])&&s<=o&&o<=h&&b[(0,a.ZP)(d,o,0,p)].push(r[i]);return b}return r.value=function(n){return arguments.length?(t="function"==typeof n?n:()=>n,r):t},r.domain=function(t){var e;return arguments.length?(n="function"==typeof t?t:(e=[t[0],t[1]],()=>e),r):n},r.thresholds=function(t){var n;return arguments.length?(e="function"==typeof t?t:(n=Array.isArray(t)?Y.call(t):t,()=>n),r):e},r}var U=e(92637);function J(t,n,e){let r=y(t),i=(0,U.ZP)(t,.75)-(0,U.ZP)(t,.25);return r&&i?Math.ceil((e-n)/(2*i*Math.pow(r,-1/3))):1}function Q(t,n,e){let r=y(t),i=T(t);return r&&i?Math.ceil((e-n)*Math.cbrt(r)/(3.49*i)):1}var tt=e(80728),tn=e(1629);function te(t,n){let e=0,r=0;if(void 0===n)for(let n of t)null!=n&&(n=+n)>=n&&(++e,r+=n);else{let i=-1;for(let o of t)null!=(o=n(o,++i,t))&&(o=+o)>=o&&(++e,r+=o)}if(e)return r/e}function tr(t,n){return(0,U.ZP)(t,.5,n)}function ti(t,n){return(0,U.Cr)(t,.5,n)}function to(t){return Array.from(function*(t){for(let n of t)yield*n}(t))}var tu=e(53064),ta=e(43566);function tc(t,n){let e;let r=new C.L;if(void 0===n)for(let n of t)null!=n&&n>=n&&r.set(n,(r.get(n)||0)+1);else{let e=-1;for(let i of t)null!=(i=n(i,++e,t))&&i>=i&&r.set(i,(r.get(i)||0)+1)}let i=0;for(let[t,n]of r)n>i&&(i=n,e=t);return e}function tf(t,n=tl){let e;let r=[],i=!1;for(let o of t)i&&r.push(n(e,o)),e=o,i=!0;return r}function tl(t,n){return[t,n]}var ts=e(14500),th=e(89766),td=e(98395);function tp(t,n=c.Z){let e,r;if("function"!=typeof t[Symbol.iterator])throw TypeError("values is not iterable");let i=Array.from(t),o=new Float64Array(i.length);2!==n.length&&(i=i.map(n),n=c.Z);let u=(t,e)=>n(i[t],i[e]);return(t=Uint32Array.from(i,(t,n)=>n)).sort(n===c.Z?(t,n)=>(0,q.Wv)(i[t],i[n]):(0,q.di)(u)),t.forEach((t,n)=>{let i=u(t,void 0===e?t:e);i>=0?((void 0===e||i>0)&&(e=t,r=n),o[t]=r):o[t]=NaN}),o}function tv(t,n=c.Z){let e;let r=!1;if(1===n.length){let i;for(let o of t){let t=n(o);(r?0>(0,c.Z)(t,i):0===(0,c.Z)(t,t))&&(e=o,i=t,r=!0)}}else for(let i of t)(r?0>n(i,e):0===n(i,i))&&(e=i,r=!0);return e}function tg(t,n=c.Z){let e;if(1===n.length)return(0,ta.Z)(t,n);let r=-1,i=-1;for(let o of t)++i,(r<0?0===n(o,o):0>n(o,e))&&(e=o,r=i);return r}var ty=e(8629);function tb(t,n=c.Z){let e;if(1===n.length)return(0,tn.Z)(t,n);let r=-1,i=-1;for(let o of t)++i,(r<0?0===n(o,o):n(o,e)>0)&&(e=o,r=i);return r}function tm(t,n){let e=tg(t,n);return e<0?void 0:e}var t_=tx(Math.random);function tx(t){return function(n,e=0,r=n.length){let i=r-(e=+e);for(;i;){let r=t()*i--|0,o=n[i+e];n[i+e]=n[r+e],n[r+e]=o}return n}}function tw(t,n){let e=0;if(void 0===n)for(let n of t)(n=+n)&&(e+=n);else{let r=-1;for(let i of t)(i=+n(i,++r,t))&&(e+=i)}return e}function tM(t){if(!(i=t.length))return[];for(var n=-1,e=(0,tu.Z)(t,tA),r=Array(e);++n<e;)for(var i,o=-1,u=r[n]=Array(i);++o<i;)u[o]=t[o][n];return r}function tA(t){return t.length}function tT(){return tM(arguments)}function tS(t,n){if("function"!=typeof n)throw TypeError("test is not a function");let e=-1;for(let r of t)if(!n(r,++e,t))return!1;return!0}function tk(t,n){if("function"!=typeof n)throw TypeError("test is not a function");let e=-1;for(let r of t)if(n(r,++e,t))return!0;return!1}function tP(t,n){if("function"!=typeof n)throw TypeError("test is not a function");let e=[],r=-1;for(let i of t)n(i,++r,t)&&e.push(i);return e}function tE(t,n){if("function"!=typeof t[Symbol.iterator])throw TypeError("values is not iterable");if("function"!=typeof n)throw TypeError("mapper is not a function");return Array.from(t,(e,r)=>n(e,r,t))}function tC(t,n,e){if("function"!=typeof n)throw TypeError("reducer is not a function");let r=t[Symbol.iterator](),i,o,u=-1;if(arguments.length<3){if({done:i,value:e}=r.next(),i)return;++u}for(;{done:i,value:o}=r.next(),!i;)e=n(e,o,++u,t);return e}function tN(t){if("function"!=typeof t[Symbol.iterator])throw TypeError("values is not iterable");return Array.from(t).reverse()}function tR(t,...n){for(let e of(t=new C.H(t),n))for(let n of e)t.delete(n);return t}function tO(t,n){let e=n[Symbol.iterator](),r=new C.H;for(let n of t){let t,i;if(r.has(n))return!1;for(;({value:t,done:i}=e.next())&&!i;){if(Object.is(n,t))return!1;r.add(t)}}return!0}function tZ(t,...n){t=new C.H(t),n=n.map(tz);t:for(let e of t)for(let r of n)if(!r.has(e)){t.delete(e);continue t}return t}function tz(t){return t instanceof C.H?t:new C.H(t)}function tB(t,n){let e=t[Symbol.iterator](),r=new Set;for(let t of n){let n,i;let o=tL(t);if(!r.has(o))for(;{value:n,done:i}=e.next();){if(i)return!1;let t=tL(n);if(r.add(t),Object.is(o,t))break}}return!0}function tL(t){return null!==t&&"object"==typeof t?t.valueOf():t}function tV(t,n){return tB(n,t)}function tD(...t){let n=new C.H;for(let e of t)for(let t of e)n.add(t);return n}function tG(t){return t}function tj(t){return"translate("+t+",0)"}function tI(t){return"translate(0,"+t+")"}function tq(){return!this.__axis}function tF(t,n){var e=[],r=null,i=null,o=6,u=6,a=3,c="undefined"!=typeof window&&window.devicePixelRatio>1?0:.5,f=1===t||4===t?-1:1,l=4===t||2===t?"x":"y",s=1===t||3===t?tj:tI;function h(h){var d=null==r?n.ticks?n.ticks.apply(n,e):n.domain():r,p=null==i?n.tickFormat?n.tickFormat.apply(n,e):tG:i,v=Math.max(o,0)+a,g=n.range(),y=+g[0]+c,b=+g[g.length-1]+c,m=(n.bandwidth?function(t,n){return n=Math.max(0,t.bandwidth()-2*n)/2,t.round()&&(n=Math.round(n)),e=>+t(e)+n}:function(t){return n=>+t(n)})(n.copy(),c),_=h.selection?h.selection():h,x=_.selectAll(".domain").data([null]),w=_.selectAll(".tick").data(d,n).order(),M=w.exit(),A=w.enter().append("g").attr("class","tick"),T=w.select("line"),S=w.select("text");x=x.merge(x.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),w=w.merge(A),T=T.merge(A.append("line").attr("stroke","currentColor").attr(l+"2",f*o)),S=S.merge(A.append("text").attr("fill","currentColor").attr(l,f*v).attr("dy",1===t?"0em":3===t?"0.71em":"0.32em")),h!==_&&(x=x.transition(h),w=w.transition(h),T=T.transition(h),S=S.transition(h),M=M.transition(h).attr("opacity",1e-6).attr("transform",function(t){return isFinite(t=m(t))?s(t+c):this.getAttribute("transform")}),A.attr("opacity",1e-6).attr("transform",function(t){var n=this.parentNode.__axis;return s((n&&isFinite(n=n(t))?n:m(t))+c)})),M.remove(),x.attr("d",4===t||2===t?u?"M"+f*u+","+y+"H"+c+"V"+b+"H"+f*u:"M"+c+","+y+"V"+b:u?"M"+y+","+f*u+"V"+c+"H"+b+"V"+f*u:"M"+y+","+c+"H"+b),w.attr("opacity",1).attr("transform",function(t){return s(m(t)+c)}),T.attr(l+"2",f*o),S.attr(l,f*v).text(p),_.filter(tq).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",2===t?"start":4===t?"end":"middle"),_.each(function(){this.__axis=m})}return h.scale=function(t){return arguments.length?(n=t,h):n},h.ticks=function(){return e=Array.from(arguments),h},h.tickArguments=function(t){return arguments.length?(e=null==t?[]:Array.from(t),h):e.slice()},h.tickValues=function(t){return arguments.length?(r=null==t?null:Array.from(t),h):r&&r.slice()},h.tickFormat=function(t){return arguments.length?(i=t,h):i},h.tickSize=function(t){return arguments.length?(o=u=+t,h):o},h.tickSizeInner=function(t){return arguments.length?(o=+t,h):o},h.tickSizeOuter=function(t){return arguments.length?(u=+t,h):u},h.tickPadding=function(t){return arguments.length?(a=+t,h):a},h.offset=function(t){return arguments.length?(c=+t,h):c},h}function tH(t){return tF(1,t)}function tY(t){return tF(2,t)}function t$(t){return tF(3,t)}function tX(t){return tF(4,t)}var tW={value:()=>{}};function tK(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r||/[\s.]/.test(t))throw Error("illegal type: "+t);r[t]=[]}return new tU(r)}function tU(t){this._=t}function tJ(t,n,e){for(var r=0,i=t.length;r<i;++r)if(t[r].name===n){t[r]=tW,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}tU.prototype=tK.prototype={constructor:tU,on:function(t,n){var e,r=this._,i=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw Error("unknown type: "+t);return{type:t,name:n}}),o=-1,u=i.length;if(arguments.length<2){for(;++o<u;)if((e=(t=i[o]).type)&&(e=function(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}(r[e],t.name)))return e;return}if(null!=n&&"function"!=typeof n)throw Error("invalid callback: "+n);for(;++o<u;)if(e=(t=i[o]).type)r[e]=tJ(r[e],t.name,n);else if(null==n)for(e in r)r[e]=tJ(r[e],t.name,null);return this},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new tU(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(r=this._[t],o=0,e=r.length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};var tQ=tK;function t0(){}function t1(t){return null==t?t0:function(){return this.querySelector(t)}}function t2(t){return null==t?[]:Array.isArray(t)?t:Array.from(t)}function t6(){return[]}function t8(t){return null==t?t6:function(){return this.querySelectorAll(t)}}function t3(t){return function(){return this.matches(t)}}function t4(t){return function(n){return n.matches(t)}}var t5=Array.prototype.find;function t7(){return this.firstElementChild}var t9=Array.prototype.filter;function nt(){return Array.from(this.children)}function nn(t){return Array(t.length)}function ne(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}function nr(t,n,e,r,i,o){for(var u,a=0,c=n.length,f=o.length;a<f;++a)(u=n[a])?(u.__data__=o[a],r[a]=u):e[a]=new ne(t,o[a]);for(;a<c;++a)(u=n[a])&&(i[a]=u)}function ni(t,n,e,r,i,o,u){var a,c,f,l=new Map,s=n.length,h=o.length,d=Array(s);for(a=0;a<s;++a)(c=n[a])&&(d[a]=f=u.call(c,c.__data__,a,n)+"",l.has(f)?i[a]=c:l.set(f,c));for(a=0;a<h;++a)f=u.call(t,o[a],a,o)+"",(c=l.get(f))?(r[a]=c,c.__data__=o[a],l.delete(f)):e[a]=new ne(t,o[a]);for(a=0;a<s;++a)(c=n[a])&&l.get(d[a])===c&&(i[a]=c)}function no(t){return t.__data__}function nu(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}ne.prototype={constructor:ne,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var na="http://www.w3.org/1999/xhtml",nc={svg:"http://www.w3.org/2000/svg",xhtml:na,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function nf(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),nc.hasOwnProperty(n)?{space:nc[n],local:t}:t}function nl(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView}function ns(t,n){return t.style.getPropertyValue(n)||nl(t).getComputedStyle(t,null).getPropertyValue(n)}function nh(t){return t.trim().split(/^|\s+/)}function nd(t){return t.classList||new np(t)}function np(t){this._node=t,this._names=nh(t.getAttribute("class")||"")}function nv(t,n){for(var e=nd(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function ng(t,n){for(var e=nd(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}function ny(){this.textContent=""}function nb(){this.innerHTML=""}function nm(){this.nextSibling&&this.parentNode.appendChild(this)}function n_(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function nx(t){var n=nf(t);return(n.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===na&&n.documentElement.namespaceURI===na?n.createElement(t):n.createElementNS(e,t)}})(n)}function nw(){return null}function nM(){var t=this.parentNode;t&&t.removeChild(this)}function nA(){var t=this.cloneNode(!1),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function nT(){var t=this.cloneNode(!0),n=this.parentNode;return n?n.insertBefore(t,this.nextSibling):t}function nS(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)(e=n[r],t.type&&e.type!==t.type||e.name!==t.name)?n[++i]=e:this.removeEventListener(e.type,e.listener,e.options);++i?n.length=i:delete this.__on}}}function nk(t,n,e){return function(){var r,i=this.__on,o=function(t){n.call(this,t,this.__data__)};if(i){for(var u=0,a=i.length;u<a;++u)if((r=i[u]).type===t.type&&r.name===t.name){this.removeEventListener(r.type,r.listener,r.options),this.addEventListener(r.type,r.listener=o,r.options=e),r.value=n;return}}this.addEventListener(t.type,o,e),r={type:t.type,name:t.name,value:n,listener:o,options:e},i?i.push(r):this.__on=[r]}}function nP(t,n,e){var r=nl(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}np.prototype={add:function(t){0>this._names.indexOf(t)&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};var nE=[null];function nC(t,n){this._groups=t,this._parents=n}function nN(){return new nC([[document.documentElement]],nE)}nC.prototype=nN.prototype={constructor:nC,select:function(t){"function"!=typeof t&&(t=t1(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,u,a=n[i],c=a.length,f=r[i]=Array(c),l=0;l<c;++l)(o=a[l])&&(u=t.call(o,o.__data__,l,a))&&("__data__"in o&&(u.__data__=o.__data__),f[l]=u);return new nC(r,this._parents)},selectAll:function(t){if("function"==typeof t){var n;n=t,t=function(){return t2(n.apply(this,arguments))}}else t=t8(t);for(var e=this._groups,r=e.length,i=[],o=[],u=0;u<r;++u)for(var a,c=e[u],f=c.length,l=0;l<f;++l)(a=c[l])&&(i.push(t.call(a,a.__data__,l,c)),o.push(a));return new nC(i,o)},selectChild:function(t){var n;return this.select(null==t?t7:(n="function"==typeof t?t:t4(t),function(){return t5.call(this.children,n)}))},selectChildren:function(t){var n;return this.selectAll(null==t?nt:(n="function"==typeof t?t:t4(t),function(){return t9.call(this.children,n)}))},filter:function(t){"function"!=typeof t&&(t=t3(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,u=n[i],a=u.length,c=r[i]=[],f=0;f<a;++f)(o=u[f])&&t.call(o,o.__data__,f,u)&&c.push(o);return new nC(r,this._parents)},data:function(t,n){if(!arguments.length)return Array.from(this,no);var e=n?ni:nr,r=this._parents,i=this._groups;"function"!=typeof t&&(b=t,t=function(){return b});for(var o=i.length,u=Array(o),a=Array(o),c=Array(o),f=0;f<o;++f){var l=r[f],s=i[f],h=s.length,d="object"==typeof(y=t.call(l,l&&l.__data__,f,r))&&"length"in y?y:Array.from(y),p=d.length,v=a[f]=Array(p),g=u[f]=Array(p);e(l,s,v,g,c[f]=Array(h),d,n);for(var y,b,m,_,x=0,w=0;x<p;++x)if(m=v[x]){for(x>=w&&(w=x+1);!(_=g[w])&&++w<p;);m._next=_||null}}return(u=new nC(u,r))._enter=a,u._exit=c,u},enter:function(){return new nC(this._enter||this._groups.map(nn),this._parents)},exit:function(){return new nC(this._exit||this._groups.map(nn),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return"function"==typeof t?(r=t(r))&&(r=r.selection()):r=r.append(t+""),null!=n&&(i=n(i))&&(i=i.selection()),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=t.selection?t.selection():t,e=this._groups,r=n._groups,i=e.length,o=r.length,u=Math.min(i,o),a=Array(i),c=0;c<u;++c)for(var f,l=e[c],s=r[c],h=l.length,d=a[c]=Array(h),p=0;p<h;++p)(f=l[p]||s[p])&&(d[p]=f);for(;c<i;++c)a[c]=e[c];return new nC(a,this._parents)},selection:function(){return this},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,u=i[o];--o>=0;)(r=i[o])&&(u&&4^r.compareDocumentPosition(u)&&u.parentNode.insertBefore(r,u),u=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=nu);for(var e=this._groups,r=e.length,i=Array(r),o=0;o<r;++o){for(var u,a=e[o],c=a.length,f=i[o]=Array(c),l=0;l<c;++l)(u=a[l])&&(f[l]=u);f.sort(n)}return new nC(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var u=r[i];if(u)return u}return null},size:function(){let t=0;for(let n of this)++t;return t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],u=0,a=o.length;u<a;++u)(i=o[u])&&t.call(i,i.__data__,u,o);return this},attr:function(t,n){var e=nf(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof n?e.local?function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}:function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}:e.local?function(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}:function(t,n){return function(){this.setAttribute(t,n)}})(e,n))},style:function(t,n,e){return arguments.length>1?this.each((null==n?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof n?function(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}:function(t,n,e){return function(){this.style.setProperty(t,n,e)}})(t,n,null==e?"":e)):ns(this.node(),t)},property:function(t,n){return arguments.length>1?this.each((null==n?function(t){return function(){delete this[t]}}:"function"==typeof n?function(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}:function(t,n){return function(){this[t]=n}})(t,n)):this.node()[t]},classed:function(t,n){var e=nh(t+"");if(arguments.length<2){for(var r=nd(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?function(t,n){return function(){(n.apply(this,arguments)?nv:ng)(this,t)}}:n?function(t){return function(){nv(this,t)}}:function(t){return function(){ng(this,t)}})(e,n))},text:function(t){return arguments.length?this.each(null==t?ny:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?nb:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(nm)},lower:function(){return this.each(n_)},append:function(t){var n="function"==typeof t?t:nx(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:nx(t),r=null==n?nw:"function"==typeof n?n:t1(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(nM)},clone:function(t){return this.select(t?nT:nA)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}}),u=o.length;if(arguments.length<2){var a=this.node().__on;if(a){for(var c,f=0,l=a.length;f<l;++f)for(r=0,c=a[f];r<u;++r)if((i=o[r]).type===c.type&&i.name===c.name)return c.value}return}for(r=0,a=n?nk:nS;r<u;++r)this.each(a(o[r],n,e));return this},dispatch:function(t,n){return this.each(("function"==typeof n?function(t,n){return function(){return nP(this,t,n.apply(this,arguments))}}:function(t,n){return function(){return nP(this,t,n)}})(t,n))},[Symbol.iterator]:function*(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r,i=t[n],o=0,u=i.length;o<u;++o)(r=i[o])&&(yield r)}};var nR=nN;function nO(t){return"string"==typeof t?new nC([[document.querySelector(t)]],[document.documentElement]):new nC([[t]],nE)}let nZ={passive:!1},nz={capture:!0,passive:!1};function nB(t){t.stopImmediatePropagation()}function nL(t){t.preventDefault(),t.stopImmediatePropagation()}function nV(t){var n=t.document.documentElement,e=nO(t).on("dragstart.drag",nL,nz);"onselectstart"in n?e.on("selectstart.drag",nL,nz):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")}function nD(t,n){var e=t.document.documentElement,r=nO(t).on("dragstart.drag",null);n&&(r.on("click.drag",nL,nz),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}var nG=e(84899);function nj(t){let n;for(;n=t.sourceEvent;)t=n;return t}function nI(t,n){if(t=nj(t),void 0===n&&(n=t.currentTarget),n){var e=n.ownerSVGElement||n;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=t.clientX,r.y=t.clientY,[(r=r.matrixTransform(n.getScreenCTM().inverse())).x,r.y]}if(n.getBoundingClientRect){var i=n.getBoundingClientRect();return[t.clientX-i.left-n.clientLeft,t.clientY-i.top-n.clientTop]}}return[t.pageX,t.pageY]}var nq,nF,nH=0,nY=0,n$=0,nX=0,nW=0,nK=0,nU="object"==typeof performance&&performance.now?performance:Date,nJ="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function nQ(){return nW||(nJ(n0),nW=nU.now()+nK)}function n0(){nW=0}function n1(){this._call=this._time=this._next=null}function n2(t,n,e){var r=new n1;return r.restart(t,n,e),r}function n6(){nQ(),++nH;for(var t,n=nq;n;)(t=nW-n._time)>=0&&n._call.call(void 0,t),n=n._next;--nH}function n8(){nW=(nX=nU.now())+nK,nH=nY=0;try{n6()}finally{nH=0,function(){for(var t,n,e=nq,r=1/0;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:nq=n);nF=t,n4(r)}(),nW=0}}function n3(){var t=nU.now(),n=t-nX;n>1e3&&(nK-=n,nX=t)}function n4(t){!nH&&(nY&&(nY=clearTimeout(nY)),t-nW>24?(t<1/0&&(nY=setTimeout(n8,t-nU.now()-nK)),n$&&(n$=clearInterval(n$))):(n$||(nX=nU.now(),n$=setInterval(n3,1e3)),nH=1,nJ(n8)))}function n5(t,n,e){var r=new n1;return n=null==n?0:+n,r.restart(e=>{r.stop(),t(e+n)},n,e),r}n1.prototype=n2.prototype={constructor:n1,restart:function(t,n,e){if("function"!=typeof t)throw TypeError("callback is not a function");e=(null==e?nQ():+e)+(null==n?0:+n),this._next||nF===this||(nF?nF._next=this:nq=this,nF=this),this._call=t,this._time=e,n4()},stop:function(){this._call&&(this._call=null,this._time=1/0,n4())}};var n7=tQ("start","end","cancel","interrupt"),n9=[];function et(t,n,e,r,i,o){var u=t.__transition;if(u){if(e in u)return}else t.__transition={};!function(t,n,e){var r,i=t.__transition;function o(c){var f,l,s,h;if(1!==e.state)return a();for(f in i)if((h=i[f]).name===e.name){if(3===h.state)return n5(o);4===h.state?(h.state=6,h.timer.stop(),h.on.call("interrupt",t,t.__data__,h.index,h.group),delete i[f]):+f<n&&(h.state=6,h.timer.stop(),h.on.call("cancel",t,t.__data__,h.index,h.group),delete i[f])}if(n5(function(){3===e.state&&(e.state=4,e.timer.restart(u,e.delay,e.time),u(c))}),e.state=2,e.on.call("start",t,t.__data__,e.index,e.group),2===e.state){for(f=0,e.state=3,r=Array(s=e.tween.length),l=-1;f<s;++f)(h=e.tween[f].value.call(t,t.__data__,e.index,e.group))&&(r[++l]=h);r.length=l+1}}function u(n){for(var i=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(a),e.state=5,1),o=-1,u=r.length;++o<u;)r[o].call(t,i);5===e.state&&(e.on.call("end",t,t.__data__,e.index,e.group),a())}function a(){for(var r in e.state=6,e.timer.stop(),delete i[n],i)return;delete t.__transition}i[n]=e,e.timer=n2(function(t){e.state=1,e.timer.restart(o,e.delay,e.time),e.delay<=t&&o(t-e.delay)},0,e.time)}(t,e,{name:n,index:r,group:i,on:n7,tween:n9,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:0})}function en(t,n){var e=er(t,n);if(e.state>0)throw Error("too late; already scheduled");return e}function ee(t,n){var e=er(t,n);if(e.state>3)throw Error("too late; already running");return e}function er(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw Error("transition not found");return e}function ei(t,n){var e,r,i,o=t.__transition,u=!0;if(o){for(i in n=null==n?null:n+"",o){if((e=o[i]).name!==n){u=!1;continue}r=e.state>2&&e.state<5,e.state=6,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]}u&&delete t.__transition}}var eo=e(50432),eu=180/Math.PI,ea={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function ec(t,n,e,r,i,o){var u,a,c;return(u=Math.sqrt(t*t+n*n))&&(t/=u,n/=u),(c=t*e+n*r)&&(e-=t*c,r-=n*c),(a=Math.sqrt(e*e+r*r))&&(e/=a,r/=a,c/=a),t*r<n*e&&(t=-t,n=-n,c=-c,u=-u),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*eu,skewX:Math.atan(c)*eu,scaleX:u,scaleY:a}}function ef(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,u){var a,c,f,l,s=[],h=[];return o=t(o),u=t(u),!function(t,r,i,o,u,a){if(t!==i||r!==o){var c=u.push("translate(",null,n,null,e);a.push({i:c-4,x:(0,eo.Z)(t,i)},{i:c-2,x:(0,eo.Z)(r,o)})}else(i||o)&&u.push("translate("+i+n+o+e)}(o.translateX,o.translateY,u.translateX,u.translateY,s,h),(a=o.rotate)!==(c=u.rotate)?(a-c>180?c+=360:c-a>180&&(a+=360),h.push({i:s.push(i(s)+"rotate(",null,r)-2,x:(0,eo.Z)(a,c)})):c&&s.push(i(s)+"rotate("+c+r),(f=o.skewX)!==(l=u.skewX)?h.push({i:s.push(i(s)+"skewX(",null,r)-2,x:(0,eo.Z)(f,l)}):l&&s.push(i(s)+"skewX("+l+r),!function(t,n,e,r,o,u){if(t!==e||n!==r){var a=o.push(i(o)+"scale(",null,",",null,")");u.push({i:a-4,x:(0,eo.Z)(t,e)},{i:a-2,x:(0,eo.Z)(n,r)})}else(1!==e||1!==r)&&o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,u.scaleX,u.scaleY,s,h),o=u=null,function(t){for(var n,e=-1,r=h.length;++e<r;)s[(n=h[e]).i]=n.x(t);return s.join("")}}}var el=ef(function(t){let n=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(t+"");return n.isIdentity?ea:ec(n.a,n.b,n.c,n.d,n.e,n.f)},"px, ","px)","deg)"),es=ef(function(t){return null==t?ea:(uB||(uB=document.createElementNS("http://www.w3.org/2000/svg","g")),uB.setAttribute("transform",t),t=uB.transform.baseVal.consolidate())?ec((t=t.matrix).a,t.b,t.c,t.d,t.e,t.f):ea},", ",")",")");function eh(t,n,e){var r=t._id;return t.each(function(){var t=ee(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return er(t,r).value[n]}}var ed=e(36420),ep=e(16381),ev=e(22003);function eg(t,n){var e;return("number"==typeof n?eo.Z:n instanceof ed.ZP?ep.ZP:(e=(0,ed.ZP)(n))?(n=e,ep.ZP):ev.Z)(t,n)}var ey=nR.prototype.constructor;function eb(t){return function(){this.style.removeProperty(t)}}var em=0;function e_(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function ex(t){return nR().transition(t)}var ew=nR.prototype;function eM(t){return t*t*t}function eA(t){return--t*t*t+1}function eT(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}e_.prototype=ex.prototype={constructor:e_,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=t1(t));for(var r=this._groups,i=r.length,o=Array(i),u=0;u<i;++u)for(var a,c,f=r[u],l=f.length,s=o[u]=Array(l),h=0;h<l;++h)(a=f[h])&&(c=t.call(a,a.__data__,h,f))&&("__data__"in a&&(c.__data__=a.__data__),s[h]=c,et(s[h],n,e,h,s,er(a,e)));return new e_(o,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=t8(t));for(var r=this._groups,i=r.length,o=[],u=[],a=0;a<i;++a)for(var c,f=r[a],l=f.length,s=0;s<l;++s)if(c=f[s]){for(var h,d=t.call(c,c.__data__,s,f),p=er(c,e),v=0,g=d.length;v<g;++v)(h=d[v])&&et(h,n,e,v,d,p);o.push(d),u.push(c)}return new e_(o,u,n,e)},selectChild:ew.selectChild,selectChildren:ew.selectChildren,filter:function(t){"function"!=typeof t&&(t=t3(t));for(var n=this._groups,e=n.length,r=Array(e),i=0;i<e;++i)for(var o,u=n[i],a=u.length,c=r[i]=[],f=0;f<a;++f)(o=u[f])&&t.call(o,o.__data__,f,u)&&c.push(o);return new e_(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw Error();for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=Array(r),a=0;a<o;++a)for(var c,f=n[a],l=e[a],s=f.length,h=u[a]=Array(s),d=0;d<s;++d)(c=f[d]||l[d])&&(h[d]=c);for(;a<r;++a)u[a]=n[a];return new e_(u,this._parents,this._name,this._id)},selection:function(){return new ey(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=++em,r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],c=a.length,f=0;f<c;++f)if(u=a[f]){var l=er(u,n);et(u,t,e,f,a,{time:l.time+l.delay+l.duration,delay:0,duration:l.duration,ease:l.ease})}return new e_(r,this._parents,t,e)},call:ew.call,nodes:ew.nodes,node:ew.node,size:ew.size,empty:ew.empty,each:ew.each,on:function(t,n){var e,r,i,o=this._id;return arguments.length<2?er(this.node(),o).on.on(t):this.each((i=(t+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})?en:ee,function(){var u=i(this,o),a=u.on;a!==e&&(r=(e=a).copy()).on(t,n),u.on=r}))},attr:function(t,n){var e=nf(t),r="transform"===e?es:eg;return this.attrTween(t,"function"==typeof n?(e.local?function(t,n,e){var r,i,o;return function(){var u,a,c=e(this);return null==c?void this.removeAttributeNS(t.space,t.local):(u=this.getAttributeNS(t.space,t.local))===(a=c+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,c))}}:function(t,n,e){var r,i,o;return function(){var u,a,c=e(this);return null==c?void this.removeAttribute(t):(u=this.getAttribute(t))===(a=c+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,c))}})(e,r,eh(this,"attr."+t,n)):null==n?(e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(e):(e.local?function(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttributeNS(t.space,t.local);return u===o?null:u===r?i:i=n(r=u,e)}}:function(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttribute(t);return u===o?null:u===r?i:i=n(r=u,e)}})(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw Error();var r=nf(t);return this.tween(e,(r.local?function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttributeNS(t.space,t.local,i.call(this,n))}),e}return i._value=n,i}:function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(n){this.setAttribute(t,i.call(this,n))}),e}return i._value=n,i})(r,n))},style:function(t,n,e){var r,i,o,u,a,c,f,l,s,h,d,p,v,g,y,b,m,_,x,w,M,A="transform"==(t+="")?el:eg;return null==n?this.styleTween(t,(r=t,function(){var t=ns(this,r),n=(this.style.removeProperty(r),ns(this,r));return t===n?null:t===i&&n===o?u:u=A(i=t,o=n)})).on("end.style."+t,eb(t)):"function"==typeof n?this.styleTween(t,(a=t,c=eh(this,"style."+t,n),function(){var t=ns(this,a),n=c(this),e=n+"";return null==n&&(this.style.removeProperty(a),e=n=ns(this,a)),t===e?null:t===f&&e===l?s:(l=e,s=A(f=t,n))})).each((h=this._id,m="end."+(b="style."+(d=t)),function(){var t=ee(this,h),n=t.on,e=null==t.value[b]?y||(y=eb(d)):void 0;(n!==p||g!==e)&&(v=(p=n).copy()).on(m,g=e),t.on=v})):this.styleTween(t,(_=t,M=n+"",function(){var t=ns(this,_);return t===M?null:t===x?w:w=A(x=t,n)}),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw Error();return this.tween(r,function(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&function(n){this.style.setProperty(t,o.call(this,n),e)}),r}return o._value=n,o}(t,n,null==e?"":e))},text:function(t){var n,e;return this.tween("text","function"==typeof t?(n=eh(this,"text",t),function(){var t=n(this);this.textContent=null==t?"":t}):(e=null==t?"":t+"",function(){this.textContent=e}))},textTween:function(t){var n="text";if(arguments.length<1)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw Error();return this.tween(n,function(t){var n,e;function r(){var r=t.apply(this,arguments);return r!==e&&(n=(e=r)&&function(t){this.textContent=r.call(this,t)}),n}return r._value=t,r}(t))},remove:function(){var t;return this.on("end.remove",(t=this._id,function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}))},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=er(this.node(),e).tween,o=0,u=i.length;o<u;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?function(t,n){var e,r;return function(){var i=ee(this,t),o=i.tween;if(o!==e){r=e=o;for(var u=0,a=r.length;u<a;++u)if(r[u].name===n){(r=r.slice()).splice(u,1);break}}i.tween=r}}:function(t,n,e){var r,i;if("function"!=typeof e)throw Error();return function(){var o=ee(this,t),u=o.tween;if(u!==r){i=(r=u).slice();for(var a={name:n,value:e},c=0,f=i.length;c<f;++c)if(i[c].name===n){i[c]=a;break}c===f&&i.push(a)}o.tween=i}})(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){en(this,t).delay=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){en(this,t).delay=n}})(n,t)):er(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){ee(this,t).duration=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){ee(this,t).duration=n}})(n,t)):er(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw Error();return function(){ee(this,t).ease=n}}(n,t)):er(this.node(),n).ease},easeVarying:function(t){var n;if("function"!=typeof t)throw Error();return this.each((n=this._id,function(){var e=t.apply(this,arguments);if("function"!=typeof e)throw Error();ee(this,n).ease=e}))},end:function(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,u){var a={value:u},c={value:function(){0==--i&&o()}};e.each(function(){var e=ee(this,r),i=e.on;i!==t&&((n=(t=i).copy())._.cancel.push(a),n._.interrupt.push(a),n._.end.push(c)),e.on=n}),0===i&&o()})},[Symbol.iterator]:ew[Symbol.iterator]};var eS={time:null,delay:0,duration:250,ease:eT};nR.prototype.interrupt=function(t){return this.each(function(){ei(this,t)})},nR.prototype.transition=function(t){var n,e;t instanceof e_?(n=t._id,t=t._name):(n=++em,(e=eS).time=nQ(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],c=a.length,f=0;f<c;++f)(u=a[f])&&et(u,t,n,f,a,e||function(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))throw Error(`transition ${n} not found`);return e}(u,n));return new e_(r,this._parents,t,n)};var ek=[null];function eP(t,n){var e,r,i=t.__transition;if(i){for(r in n=null==n?null:n+"",i)if((e=i[r]).state>1&&e.name===n)return new e_([[t]],ek,n,+r)}return null}var eE=t=>()=>t;function eC(t,{sourceEvent:n,target:e,selection:r,mode:i,dispatch:o}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},selection:{value:r,enumerable:!0,configurable:!0},mode:{value:i,enumerable:!0,configurable:!0},_:{value:o}})}function eN(t){t.preventDefault(),t.stopImmediatePropagation()}var eR={name:"drag"},eO={name:"space"},eZ={name:"handle"},ez={name:"center"};let{abs:eB,max:eL,min:eV}=Math;function eD(t){return[+t[0],+t[1]]}function eG(t){return[eD(t[0]),eD(t[1])]}var ej={name:"x",handles:["w","e"].map(eW),input:function(t,n){return null==t?null:[[+t[0],n[0][1]],[+t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},eI={name:"y",handles:["n","s"].map(eW),input:function(t,n){return null==t?null:[[n[0][0],+t[0]],[n[1][0],+t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},eq={name:"xy",handles:["n","w","e","s","nw","ne","sw","se"].map(eW),input:function(t){return null==t?null:eG(t)},output:function(t){return t}},eF={overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},eH={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},eY={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},e$={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},eX={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function eW(t){return{type:t}}function eK(t){return!t.ctrlKey&&!t.button}function eU(){var t=this.ownerSVGElement||this;return t.hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function eJ(){return navigator.maxTouchPoints||"ontouchstart"in this}function eQ(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function e0(t){var n=t.__brush;return n?n.dim.output(n.selection):null}function e1(){return e8(ej)}function e2(){return e8(eI)}function e6(){return e8(eq)}function e8(t){var n,e=eU,r=eK,i=eJ,o=!0,u=tQ("start","brush","end"),a=6;function c(n){var e=n.property("__brush",v).selectAll(".overlay").data([eW("overlay")]);e.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",eF.overlay).merge(e).each(function(){var t=eQ(this).extent;nO(this).attr("x",t[0][0]).attr("y",t[0][1]).attr("width",t[1][0]-t[0][0]).attr("height",t[1][1]-t[0][1])}),n.selectAll(".selection").data([eW("selection")]).enter().append("rect").attr("class","selection").attr("cursor",eF.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges");var r=n.selectAll(".handle").data(t.handles,function(t){return t.type});r.exit().remove(),r.enter().append("rect").attr("class",function(t){return"handle handle--"+t.type}).attr("cursor",function(t){return eF[t.type]}),n.each(f).attr("fill","none").attr("pointer-events","all").on("mousedown.brush",h).filter(i).on("touchstart.brush",h).on("touchmove.brush",d).on("touchend.brush touchcancel.brush",p).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function f(){var t=nO(this),n=eQ(this).selection;n?(t.selectAll(".selection").style("display",null).attr("x",n[0][0]).attr("y",n[0][1]).attr("width",n[1][0]-n[0][0]).attr("height",n[1][1]-n[0][1]),t.selectAll(".handle").style("display",null).attr("x",function(t){return"e"===t.type[t.type.length-1]?n[1][0]-a/2:n[0][0]-a/2}).attr("y",function(t){return"s"===t.type[0]?n[1][1]-a/2:n[0][1]-a/2}).attr("width",function(t){return"n"===t.type||"s"===t.type?n[1][0]-n[0][0]+a:a}).attr("height",function(t){return"e"===t.type||"w"===t.type?n[1][1]-n[0][1]+a:a})):t.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}function l(t,n,e){var r=t.__brush.emitter;return!r||e&&r.clean?new s(t,n,e):r}function s(t,n,e){this.that=t,this.args=n,this.state=t.__brush,this.active=0,this.clean=e}function h(e){if((!n||e.touches)&&r.apply(this,arguments)){var i,u,a,c,s,h,d,p,v,g,y,b=this,m=e.target.__data__.type,_=(o&&e.metaKey?m="overlay":m)==="selection"?eR:o&&e.altKey?ez:eZ,x=t===eI?null:e$[m],w=t===ej?null:eX[m],M=eQ(b),A=M.extent,T=M.selection,S=A[0][0],k=A[0][1],P=A[1][0],E=A[1][1],C=0,N=0,R=x&&w&&o&&e.shiftKey,O=Array.from(e.touches||[e],t=>{let n=t.identifier;return(t=nI(t,b)).point0=t.slice(),t.identifier=n,t});ei(b);var Z=l(b,arguments,!0).beforestart();if("overlay"===m){T&&(v=!0);let n=[O[0],O[1]||O[0]];M.selection=T=[[i=t===eI?S:eV(n[0][0],n[1][0]),a=t===ej?k:eV(n[0][1],n[1][1])],[s=t===eI?P:eL(n[0][0],n[1][0]),d=t===ej?E:eL(n[0][1],n[1][1])]],O.length>1&&D(e)}else i=T[0][0],a=T[0][1],s=T[1][0],d=T[1][1];u=i,c=a,h=s,p=d;var z=nO(b).attr("pointer-events","none"),B=z.selectAll(".overlay").attr("cursor",eF[m]);if(e.touches)Z.moved=V,Z.ended=G;else{var L=nO(e.view).on("mousemove.brush",V,!0).on("mouseup.brush",G,!0);o&&L.on("keydown.brush",function(t){switch(t.keyCode){case 16:R=x&&w;break;case 18:_===eZ&&(x&&(s=h-C*x,i=u+C*x),w&&(d=p-N*w,a=c+N*w),_=ez,D(t));break;case 32:(_===eZ||_===ez)&&(x<0?s=h-C:x>0&&(i=u-C),w<0?d=p-N:w>0&&(a=c-N),_=eO,B.attr("cursor",eF.selection),D(t));break;default:return}eN(t)},!0).on("keyup.brush",function(t){switch(t.keyCode){case 16:R&&(g=y=R=!1,D(t));break;case 18:_===ez&&(x<0?s=h:x>0&&(i=u),w<0?d=p:w>0&&(a=c),_=eZ,D(t));break;case 32:_===eO&&(t.altKey?(x&&(s=h-C*x,i=u+C*x),w&&(d=p-N*w,a=c+N*w),_=ez):(x<0?s=h:x>0&&(i=u),w<0?d=p:w>0&&(a=c),_=eZ),B.attr("cursor",eF[m]),D(t));break;default:return}eN(t)},!0),nV(e.view)}f.call(b),Z.start(e,_.name)}function V(t){for(let n of t.changedTouches||[t])for(let t of O)t.identifier===n.identifier&&(t.cur=nI(n,b));if(R&&!g&&!y&&1===O.length){let t=O[0];eB(t.cur[0]-t[0])>eB(t.cur[1]-t[1])?y=!0:g=!0}for(let t of O)t.cur&&(t[0]=t.cur[0],t[1]=t.cur[1]);v=!0,eN(t),D(t)}function D(t){var n;let e=O[0],r=e.point0;switch(C=e[0]-r[0],N=e[1]-r[1],_){case eO:case eR:x&&(C=eL(S-i,eV(P-s,C)),u=i+C,h=s+C),w&&(N=eL(k-a,eV(E-d,N)),c=a+N,p=d+N);break;case eZ:O[1]?(x&&(u=eL(S,eV(P,O[0][0])),h=eL(S,eV(P,O[1][0])),x=1),w&&(c=eL(k,eV(E,O[0][1])),p=eL(k,eV(E,O[1][1])),w=1)):(x<0?(C=eL(S-i,eV(P-i,C)),u=i+C,h=s):x>0&&(C=eL(S-s,eV(P-s,C)),u=i,h=s+C),w<0?(N=eL(k-a,eV(E-a,N)),c=a+N,p=d):w>0&&(N=eL(k-d,eV(E-d,N)),c=a,p=d+N));break;case ez:x&&(u=eL(S,eV(P,i-C*x)),h=eL(S,eV(P,s+C*x))),w&&(c=eL(k,eV(E,a-N*w)),p=eL(k,eV(E,d+N*w)))}h<u&&(x*=-1,n=i,i=s,s=n,n=u,u=h,h=n,m in eH&&B.attr("cursor",eF[m=eH[m]])),p<c&&(w*=-1,n=a,a=d,d=n,n=c,c=p,p=n,m in eY&&B.attr("cursor",eF[m=eY[m]])),M.selection&&(T=M.selection),g&&(u=T[0][0],h=T[1][0]),y&&(c=T[0][1],p=T[1][1]),(T[0][0]!==u||T[0][1]!==c||T[1][0]!==h||T[1][1]!==p)&&(M.selection=[[u,c],[h,p]],f.call(b),Z.brush(t,_.name))}function G(t){var e;if(!function(t){t.stopImmediatePropagation()}(t),t.touches){if(t.touches.length)return;n&&clearTimeout(n),n=setTimeout(function(){n=null},500)}else nD(t.view,v),L.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null);z.attr("pointer-events","all"),B.attr("cursor",eF.overlay),M.selection&&(T=M.selection),((e=T)[0][0]===e[1][0]||e[0][1]===e[1][1])&&(M.selection=null,f.call(b)),Z.end(t,_.name)}}function d(t){l(this,arguments).moved(t)}function p(t){l(this,arguments).ended(t)}function v(){var n=this.__brush||{selection:null};return n.extent=eG(e.apply(this,arguments)),n.dim=t,n}return c.move=function(n,e,r){n.tween?n.on("start.brush",function(t){l(this,arguments).beforestart().start(t)}).on("interrupt.brush end.brush",function(t){l(this,arguments).end(t)}).tween("brush",function(){var n=this,r=n.__brush,i=l(n,arguments),o=r.selection,u=t.input("function"==typeof e?e.apply(this,arguments):e,r.extent),a=(0,nG.Z)(o,u);function c(t){r.selection=1===t&&null===u?null:a(t),f.call(n),i.brush()}return null!==o&&null!==u?c:c(1)}):n.each(function(){var n=arguments,i=this.__brush,o=t.input("function"==typeof e?e.apply(this,n):e,i.extent),u=l(this,n).beforestart();ei(this),i.selection=null===o?null:o,f.call(this),u.start(r).brush(r).end(r)})},c.clear=function(t,n){c.move(t,null,n)},s.prototype={beforestart:function(){return 1==++this.active&&(this.state.emitter=this,this.starting=!0),this},start:function(t,n){return this.starting?(this.starting=!1,this.emit("start",t,n)):this.emit("brush",t),this},brush:function(t,n){return this.emit("brush",t,n),this},end:function(t,n){return 0==--this.active&&(delete this.state.emitter,this.emit("end",t,n)),this},emit:function(n,e,r){var i=nO(this.that).datum();u.call(n,this.that,new eC(n,{sourceEvent:e,target:c,selection:t.output(this.state.selection),mode:r,dispatch:u}),i)}},c.extent=function(t){return arguments.length?(e="function"==typeof t?t:eE(eG(t)),c):e},c.filter=function(t){return arguments.length?(r="function"==typeof t?t:eE(!!t),c):r},c.touchable=function(t){return arguments.length?(i="function"==typeof t?t:eE(!!t),c):i},c.handleSize=function(t){return arguments.length?(a=+t,c):a},c.keyModifiers=function(t){return arguments.length?(o=!!t,c):o},c.on=function(){var t=u.on.apply(u,arguments);return t===u?c:t},c}var e3=Math.abs,e4=Math.cos,e5=Math.sin,e7=Math.PI,e9=e7/2,rt=2*e7,rn=Math.max;function re(t,n){return Array.from({length:n-t},(n,e)=>t+e)}function rr(){return ru(!1,!1)}function ri(){return ru(!1,!0)}function ro(){return ru(!0,!1)}function ru(t,n){var e=0,r=null,i=null,o=null;function u(u){var a,c=u.length,f=Array(c),l=re(0,c),s=Array(c*c),h=Array(c),d=0;u=Float64Array.from({length:c*c},n?(t,n)=>u[n%c][n/c|0]:(t,n)=>u[n/c|0][n%c]);for(let n=0;n<c;++n){let e=0;for(let r=0;r<c;++r)e+=u[n*c+r]+t*u[r*c+n];d+=f[n]=e}a=(d=rn(0,rt-e*c)/d)?e:rt/c;{let n=0;for(let e of(r&&l.sort((t,n)=>r(f[t],f[n])),l)){let r=n;if(t){let t=re(~c+1,c).filter(t=>t<0?u[~t*c+e]:u[e*c+t]);for(let r of(i&&t.sort((t,n)=>i(t<0?-u[~t*c+e]:u[e*c+t],n<0?-u[~n*c+e]:u[e*c+n])),t))r<0?(s[~r*c+e]||(s[~r*c+e]={source:null,target:null})).target={index:e,startAngle:n,endAngle:n+=u[~r*c+e]*d,value:u[~r*c+e]}:(s[e*c+r]||(s[e*c+r]={source:null,target:null})).source={index:e,startAngle:n,endAngle:n+=u[e*c+r]*d,value:u[e*c+r]};h[e]={index:e,startAngle:r,endAngle:n,value:f[e]}}else{let t=re(0,c).filter(t=>u[e*c+t]||u[t*c+e]);for(let r of(i&&t.sort((t,n)=>i(u[e*c+t],u[e*c+n])),t)){let t;if(e<r?(t=s[e*c+r]||(s[e*c+r]={source:null,target:null})).source={index:e,startAngle:n,endAngle:n+=u[e*c+r]*d,value:u[e*c+r]}:((t=s[r*c+e]||(s[r*c+e]={source:null,target:null})).target={index:e,startAngle:n,endAngle:n+=u[e*c+r]*d,value:u[e*c+r]},e===r&&(t.source=t.target)),t.source&&t.target&&t.source.value<t.target.value){let n=t.source;t.source=t.target,t.target=n}}h[e]={index:e,startAngle:r,endAngle:n,value:f[e]}}n+=a}}return(s=Object.values(s)).groups=h,o?s.sort(o):s}return u.padAngle=function(t){return arguments.length?(e=rn(0,t),u):e},u.sortGroups=function(t){return arguments.length?(r=t,u):r},u.sortSubgroups=function(t){return arguments.length?(i=t,u):i},u.sortChords=function(t){return arguments.length?(null==t?o=null:(o=function(n,e){return t(n.source.value+n.target.value,e.source.value+e.target.value)})._=t,u):o&&o._},u}var ra=e(59284),rc=Array.prototype.slice;function rf(t){return function(){return t}}function rl(t){return t.source}function rs(t){return t.target}function rh(t){return t.radius}function rd(t){return t.startAngle}function rp(t){return t.endAngle}function rv(){return 0}function rg(){return 10}function ry(t){var n=rl,e=rs,r=rh,i=rh,o=rd,u=rp,a=rv,c=null;function f(){var f,l=n.apply(this,arguments),s=e.apply(this,arguments),h=a.apply(this,arguments)/2,d=rc.call(arguments),p=+r.apply(this,(d[0]=l,d)),v=o.apply(this,d)-e9,g=u.apply(this,d)-e9,y=+i.apply(this,(d[0]=s,d)),b=o.apply(this,d)-e9,m=u.apply(this,d)-e9;if(c||(c=f=(0,ra.ET)()),h>1e-12&&(e3(g-v)>2*h+1e-12?g>v?(v+=h,g-=h):(v-=h,g+=h):v=g=(v+g)/2,e3(m-b)>2*h+1e-12?m>b?(b+=h,m-=h):(b-=h,m+=h):b=m=(b+m)/2),c.moveTo(p*e4(v),p*e5(v)),c.arc(0,0,p,v,g),v!==b||g!==m){if(t){var _=+t.apply(this,arguments),x=y-_,w=(b+m)/2;c.quadraticCurveTo(0,0,x*e4(b),x*e5(b)),c.lineTo(y*e4(w),y*e5(w)),c.lineTo(x*e4(m),x*e5(m))}else c.quadraticCurveTo(0,0,y*e4(b),y*e5(b)),c.arc(0,0,y,b,m)}if(c.quadraticCurveTo(0,0,p*e4(v),p*e5(v)),c.closePath(),f)return c=null,f+""||null}return t&&(f.headRadius=function(n){return arguments.length?(t="function"==typeof n?n:rf(+n),f):t}),f.radius=function(t){return arguments.length?(r=i="function"==typeof t?t:rf(+t),f):r},f.sourceRadius=function(t){return arguments.length?(r="function"==typeof t?t:rf(+t),f):r},f.targetRadius=function(t){return arguments.length?(i="function"==typeof t?t:rf(+t),f):i},f.startAngle=function(t){return arguments.length?(o="function"==typeof t?t:rf(+t),f):o},f.endAngle=function(t){return arguments.length?(u="function"==typeof t?t:rf(+t),f):u},f.padAngle=function(t){return arguments.length?(a="function"==typeof t?t:rf(+t),f):a},f.source=function(t){return arguments.length?(n=t,f):n},f.target=function(t){return arguments.length?(e=t,f):e},f.context=function(t){return arguments.length?(c=null==t?null:t,f):c},f}function rb(){return ry()}function rm(){return ry(rg)}var r_=e(23348);let rx=Math.PI/180,rw=180/Math.PI,rM=4/29,rA=6/29,rT=6/29*3*(6/29),rS=6/29*(6/29)*(6/29);function rk(t){if(t instanceof rC)return new rC(t.l,t.a,t.b,t.opacity);if(t instanceof rV)return rD(t);t instanceof ed.Ss||(t=(0,ed.SU)(t));var n,e,r=rZ(t.r),i=rZ(t.g),o=rZ(t.b),u=rN((.2225045*r+.7168786*i+.0606169*o)/1);return r===i&&i===o?n=e=u:(n=rN((.4360747*r+.3850649*i+.1430804*o)/.96422),e=rN((.0139322*r+.0971045*i+.7141733*o)/.82521)),new rC(116*u-16,500*(n-u),200*(u-e),t.opacity)}function rP(t,n){return new rC(t,0,0,null==n?1:n)}function rE(t,n,e,r){return 1==arguments.length?rk(t):new rC(t,n,e,null==r?1:r)}function rC(t,n,e,r){this.l=+t,this.a=+n,this.b=+e,this.opacity=+r}function rN(t){return t>rS?Math.pow(t,1/3):t/rT+rM}function rR(t){return t>rA?t*t*t:rT*(t-rM)}function rO(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function rZ(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function rz(t){if(t instanceof rV)return new rV(t.h,t.c,t.l,t.opacity);if(t instanceof rC||(t=rk(t)),0===t.a&&0===t.b)return new rV(NaN,0<t.l&&t.l<100?0:NaN,t.l,t.opacity);var n=Math.atan2(t.b,t.a)*rw;return new rV(n<0?n+360:n,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function rB(t,n,e,r){return 1==arguments.length?rz(t):new rV(e,n,t,null==r?1:r)}function rL(t,n,e,r){return 1==arguments.length?rz(t):new rV(t,n,e,null==r?1:r)}function rV(t,n,e,r){this.h=+t,this.c=+n,this.l=+e,this.opacity=+r}function rD(t){if(isNaN(t.h))return new rC(t.l,0,0,t.opacity);var n=t.h*rx;return new rC(t.l,Math.cos(n)*t.c,Math.sin(n)*t.c,t.opacity)}(0,r_.Z)(rC,rE,(0,r_.l)(ed.Il,{brighter(t){return new rC(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker(t){return new rC(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb(){var t=(this.l+16)/116,n=isNaN(this.a)?t:t+this.a/500,e=isNaN(this.b)?t:t-this.b/200;return n=.96422*rR(n),t=1*rR(t),e=.82521*rR(e),new ed.Ss(rO(3.1338561*n-1.6168667*t-.4906146*e),rO(-.9787684*n+1.9161415*t+.033454*e),rO(.0719453*n-.2289914*t+1.4052427*e),this.opacity)}})),(0,r_.Z)(rV,rL,(0,r_.l)(ed.Il,{brighter(t){return new rV(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker(t){return new rV(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb(){return rD(this).rgb()}}));var rG=-1.78277*.29227-.1347134789;function rj(t,n,e,r){return 1==arguments.length?function(t){if(t instanceof rI)return new rI(t.h,t.s,t.l,t.opacity);t instanceof ed.Ss||(t=(0,ed.SU)(t));var n=t.r/255,e=t.g/255,r=t.b/255,i=(rG*r+-1.7884503806*n-3.5172982438*e)/(rG+-1.7884503806-3.5172982438),o=r-i,u=-((1.97294*(e-i)- -.29227*o)/.90649),a=Math.sqrt(u*u+o*o)/(1.97294*i*(1-i)),c=a?Math.atan2(u,o)*rw-120:NaN;return new rI(c<0?c+360:c,a,i,t.opacity)}(t):new rI(t,n,e,null==r?1:r)}function rI(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}(0,r_.Z)(rI,rj,(0,r_.l)(ed.Il,{brighter(t){return t=null==t?ed.J5:Math.pow(ed.J5,t),new rI(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?ed.xV:Math.pow(ed.xV,t),new rI(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=isNaN(this.h)?0:(this.h+120)*rx,n=+this.l,e=isNaN(this.s)?0:this.s*n*(1-n),r=Math.cos(t),i=Math.sin(t);return new ed.Ss(255*(n+e*(-.14861*r+1.78277*i)),255*(n+e*(-.29227*r+-.90649*i)),255*(n+1.97294*r*e),this.opacity)}}));var rq=Array.prototype.slice;function rF(t,n){return t-n}var rH=t=>()=>t;function rY(){}var r$=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]];function rX(){var t=1,n=1,e=W,r=a;function i(t){var n=e(t);if(Array.isArray(n))n=n.slice().sort(rF);else{let e=S(t,rW);for(n=(0,$.ZP)(...X(e[0],e[1],n),n);n[n.length-1]>=e[1];)n.pop();for(;n[1]<e[0];)n.shift()}return n.map(n=>o(t,n))}function o(e,i){let o=null==i?NaN:+i;if(isNaN(o))throw Error(`invalid value: ${i}`);var a=[],c=[];return function(e,r,i){var o,a,c,f,l,s,h=[],d=[];for(o=a=-1,r$[(f=rK(e[0],r))<<1].forEach(p);++o<t-1;)r$[(c=f)|(f=rK(e[o+1],r))<<1].forEach(p);for(r$[f<<0].forEach(p);++a<n-1;){for(o=-1,r$[(f=rK(e[a*t+t],r))<<1|(l=rK(e[a*t],r))<<2].forEach(p);++o<t-1;)c=f,f=rK(e[a*t+t+o+1],r),s=l,r$[c|f<<1|(l=rK(e[a*t+o+1],r))<<2|s<<3].forEach(p);r$[f|l<<3].forEach(p)}for(o=-1,r$[(l=e[a*t]>=r)<<2].forEach(p);++o<t-1;)s=l,r$[(l=rK(e[a*t+o+1],r))<<2|s<<3].forEach(p);function p(t){var n,e,r=[t[0][0]+o,t[0][1]+a],c=[t[1][0]+o,t[1][1]+a],f=u(r),l=u(c);(n=d[f])?(e=h[l])?(delete d[n.end],delete h[e.start],n===e?(n.ring.push(c),i(n.ring)):h[n.start]=d[e.end]={start:n.start,end:e.end,ring:n.ring.concat(e.ring)}):(delete d[n.end],n.ring.push(c),d[n.end=l]=n):(n=h[l])?(e=d[f])?(delete h[n.start],delete d[e.end],n===e?(n.ring.push(c),i(n.ring)):h[e.start]=d[n.end]={start:e.start,end:n.end,ring:e.ring.concat(n.ring)}):(delete h[n.start],n.ring.unshift(r),h[n.start=f]=n):h[f]=d[l]={start:f,end:l,ring:[r,c]}}r$[l<<3].forEach(p)}(e,o,function(t){r(t,e,o),function(t){for(var n=0,e=t.length,r=t[e-1][1]*t[0][0]-t[e-1][0]*t[0][1];++n<e;)r+=t[n-1][1]*t[n][0]-t[n-1][0]*t[n][1];return r}(t)>0?a.push([t]):c.push(t)}),c.forEach(function(t){for(var n,e=0,r=a.length;e<r;++e)if(-1!==function(t,n){for(var e,r=-1,i=n.length;++r<i;)if(e=function(t,n){for(var e=n[0],r=n[1],i=-1,o=0,u=t.length,a=u-1;o<u;a=o++){var c=t[o],f=c[0],l=c[1],s=t[a],h=s[0],d=s[1];if(function(t,n,e){var r,i,o,u;return(n[0]-t[0])*(e[1]-t[1])==(e[0]-t[0])*(n[1]-t[1])&&(i=t[r=+(t[0]===n[0])],o=e[r],u=n[r],i<=o&&o<=u||u<=o&&o<=i)}(c,s,n))return 0;l>r!=d>r&&e<(h-f)*(r-l)/(d-l)+f&&(i=-i)}return i}(t,n[r]))return e;return 0}((n=a[e])[0],t)){n.push(t);return}}),{type:"MultiPolygon",value:i,coordinates:a}}function u(n){return 2*n[0]+n[1]*(t+1)*4}function a(e,r,i){e.forEach(function(e){var o=e[0],u=e[1],a=0|o,c=0|u,f=rU(r[c*t+a]);o>0&&o<t&&a===o&&(e[0]=rJ(o,rU(r[c*t+a-1]),f,i)),u>0&&u<n&&c===u&&(e[1]=rJ(u,rU(r[(c-1)*t+a]),f,i))})}return i.contour=o,i.size=function(e){if(!arguments.length)return[t,n];var r=Math.floor(e[0]),o=Math.floor(e[1]);if(!(r>=0&&o>=0))throw Error("invalid size");return t=r,n=o,i},i.thresholds=function(t){return arguments.length?(e="function"==typeof t?t:Array.isArray(t)?rH(rq.call(t)):rH(t),i):e},i.smooth=function(t){return arguments.length?(r=t?a:rY,i):r===a},i}function rW(t){return isFinite(t)?t:NaN}function rK(t,n){return null!=t&&+t>=n}function rU(t){return null==t||isNaN(t=+t)?-1/0:t}function rJ(t,n,e,r){let i=r-n,o=e-n,u=isFinite(i)||isFinite(o)?i/o:Math.sign(i)/Math.sign(o);return isNaN(u)?t:t+u-.5}function rQ(t){return t[0]}function r0(t){return t[1]}function r1(){return 1}function r2(){var t=rQ,n=r0,e=r1,r=960,i=500,o=20,u=2,a=60,c=270,f=155,l=rH(20);function h(r){var i=new Float32Array(c*f),l=Math.pow(2,-u),h=-1;for(let o of r){var d=(t(o,++h,r)+a)*l,p=(n(o,h,r)+a)*l,v=+e(o,h,r);if(v&&d>=0&&d<c&&p>=0&&p<f){var g=Math.floor(d),y=Math.floor(p),b=d-g-.5,m=p-y-.5;i[g+y*c]+=(1-b)*(1-m)*v,i[g+1+y*c]+=b*(1-m)*v,i[g+1+(y+1)*c]+=b*m*v,i[g+(y+1)*c]+=(1-b)*m*v}}return s({data:i,width:c,height:f},o*l),i}function d(t){var n=h(t),e=l(n),r=Math.pow(2,2*u);return Array.isArray(e)||(e=(0,$.ZP)(Number.MIN_VALUE,(0,tt.Z)(n)/r,e)),rX().size([c,f]).thresholds(e.map(t=>t*r))(n).map((t,n)=>(t.value=+e[n],p(t)))}function p(t){return t.coordinates.forEach(v),t}function v(t){t.forEach(g)}function g(t){t.forEach(y)}function y(t){t[0]=t[0]*Math.pow(2,u)-a,t[1]=t[1]*Math.pow(2,u)-a}function b(){return c=r+2*(a=3*o)>>u,f=i+2*a>>u,d}return d.contours=function(t){var n=h(t),e=rX().size([c,f]),r=Math.pow(2,2*u),i=t=>{t=+t;var i=p(e.contour(n,t*r));return i.value=t,i};return Object.defineProperty(i,"max",{get:()=>(0,tt.Z)(n)/r}),i},d.x=function(n){return arguments.length?(t="function"==typeof n?n:rH(+n),d):t},d.y=function(t){return arguments.length?(n="function"==typeof t?t:rH(+t),d):n},d.weight=function(t){return arguments.length?(e="function"==typeof t?t:rH(+t),d):e},d.size=function(t){if(!arguments.length)return[r,i];var n=+t[0],e=+t[1];if(!(n>=0&&e>=0))throw Error("invalid size");return r=n,i=e,b()},d.cellSize=function(t){if(!arguments.length)return 1<<u;if(!((t=+t)>=1))throw Error("invalid cell size");return u=Math.floor(Math.log(t)/Math.LN2),b()},d.thresholds=function(t){return arguments.length?(l="function"==typeof t?t:Array.isArray(t)?rH(rq.call(t)):rH(t),d):l},d.bandwidth=function(t){if(!arguments.length)return Math.sqrt(o*(o+1));if(!((t=+t)>=0))throw Error("invalid bandwidth");return o=(Math.sqrt(4*t*t+1)-1)/2,b()},d}function r6(t,n,e,r,i){let o,u,a,c;let f=n[0],l=r[0],s=0,h=0;l>f==l>-f?(o=f,f=n[++s]):(o=l,l=r[++h]);let d=0;if(s<t&&h<e)for(l>f==l>-f?(u=f+o,a=o-(u-f),f=n[++s]):(u=l+o,a=o-(u-l),l=r[++h]),o=u,0!==a&&(i[d++]=a);s<t&&h<e;)l>f==l>-f?(c=(u=o+f)-o,a=o-(u-c)+(f-c),f=n[++s]):(c=(u=o+l)-o,a=o-(u-c)+(l-c),l=r[++h]),o=u,0!==a&&(i[d++]=a);for(;s<t;)c=(u=o+f)-o,a=o-(u-c)+(f-c),f=n[++s],o=u,0!==a&&(i[d++]=a);for(;h<e;)c=(u=o+l)-o,a=o-(u-c)+(l-c),l=r[++h],o=u,0!==a&&(i[d++]=a);return(0!==o||0===d)&&(i[d++]=o),d}function r8(t){return new Float64Array(t)}let r3=r8(4),r4=r8(8),r5=r8(12),r7=r8(16),r9=r8(4);function it(t,n,e,r,i,o){let u=(n-o)*(e-i),a=(t-i)*(r-o),c=u-a,f=Math.abs(u+a);return Math.abs(c)>=33306690738754716e-32*f?c:-function(t,n,e,r,i,o,u){let a,c,f,l,s,h,d,p,v,g,y,b,m,_,x,w,M,A;let T=t-i,S=e-i,k=n-o,P=r-o;_=T*P,d=(h=134217729*T)-(h-T),p=T-d,v=(h=134217729*P)-(h-P),x=p*(g=P-v)-(_-d*v-p*v-d*g),w=k*S,d=(h=134217729*k)-(h-k),p=k-d,v=(h=134217729*S)-(h-S),y=x-(M=p*(g=S-v)-(w-d*v-p*v-d*g)),s=x-y,r3[0]=x-(y+s)+(s-M),s=(b=_+y)-_,y=(m=_-(b-s)+(y-s))-w,s=m-y,r3[1]=m-(y+s)+(s-w),s=(A=b+y)-b,r3[2]=b-(A-s)+(y-s),r3[3]=A;let E=function(t,n){let e=n[0];for(let t=1;t<4;t++)e+=n[t];return e}(0,r3),C=22204460492503146e-32*u;if(E>=C||-E>=C||(s=t-T,a=t-(T+s)+(s-i),s=e-S,f=e-(S+s)+(s-i),s=n-k,c=n-(k+s)+(s-o),s=r-P,l=r-(P+s)+(s-o),0===a&&0===c&&0===f&&0===l)||(C=11093356479670487e-47*u+33306690738754706e-32*Math.abs(E),(E+=T*l+P*a-(k*f+S*c))>=C||-E>=C))return E;_=a*P,d=(h=134217729*a)-(h-a),p=a-d,v=(h=134217729*P)-(h-P),x=p*(g=P-v)-(_-d*v-p*v-d*g),w=c*S,d=(h=134217729*c)-(h-c),p=c-d,v=(h=134217729*S)-(h-S),y=x-(M=p*(g=S-v)-(w-d*v-p*v-d*g)),s=x-y,r9[0]=x-(y+s)+(s-M),s=(b=_+y)-_,y=(m=_-(b-s)+(y-s))-w,s=m-y,r9[1]=m-(y+s)+(s-w),s=(A=b+y)-b,r9[2]=b-(A-s)+(y-s),r9[3]=A;let N=r6(4,r3,4,r9,r4);_=T*l,d=(h=134217729*T)-(h-T),p=T-d,v=(h=134217729*l)-(h-l),x=p*(g=l-v)-(_-d*v-p*v-d*g),w=k*f,d=(h=134217729*k)-(h-k),p=k-d,v=(h=134217729*f)-(h-f),y=x-(M=p*(g=f-v)-(w-d*v-p*v-d*g)),s=x-y,r9[0]=x-(y+s)+(s-M),s=(b=_+y)-_,y=(m=_-(b-s)+(y-s))-w,s=m-y,r9[1]=m-(y+s)+(s-w),s=(A=b+y)-b,r9[2]=b-(A-s)+(y-s),r9[3]=A;let R=r6(N,r4,4,r9,r5);_=a*l,d=(h=134217729*a)-(h-a),p=a-d,v=(h=134217729*l)-(h-l),x=p*(g=l-v)-(_-d*v-p*v-d*g),w=c*f,d=(h=134217729*c)-(h-c),p=c-d,v=(h=134217729*f)-(h-f),y=x-(M=p*(g=f-v)-(w-d*v-p*v-d*g)),s=x-y,r9[0]=x-(y+s)+(s-M),s=(b=_+y)-_,y=(m=_-(b-s)+(y-s))-w,s=m-y,r9[1]=m-(y+s)+(s-w),s=(A=b+y)-b,r9[2]=b-(A-s)+(y-s),r9[3]=A;let O=r6(R,r5,4,r9,r7);return r7[O-1]}(t,n,e,r,i,o,f)}r8(4),r8(4),r8(4),r8(4),r8(4),r8(4),r8(4),r8(4),r8(4),r8(8),r8(8),r8(8),r8(4),r8(8),r8(8),r8(8),r8(12),r8(192),r8(192),r8(4),r8(4),r8(4),r8(4),r8(4),r8(4),r8(4),r8(4),r8(8),r8(8),r8(8),r8(8),r8(8),r8(8),r8(8),r8(8),r8(8),r8(4),r8(4),r8(4),r8(8),r8(16),r8(16),r8(16),r8(32),r8(32),r8(48),r8(64),r8(1152),r8(1152),r8(4),r8(4),r8(4),r8(4),r8(4),r8(4),r8(4),r8(4),r8(4),r8(4),r8(24),r8(24),r8(24),r8(24),r8(24),r8(24),r8(24),r8(24),r8(24),r8(24),r8(1152),r8(1152),r8(1152),r8(1152),r8(1152),r8(2304),r8(2304),r8(3456),r8(5760),r8(8),r8(8),r8(8),r8(16),r8(24),r8(48),r8(48),r8(96),r8(192),r8(384),r8(384),r8(384),r8(768),r8(96),r8(96),r8(96),r8(1152);let ie=new Uint32Array(512);class ir{static from(t,n=ia,e=ic){let r=t.length,i=new Float64Array(2*r);for(let o=0;o<r;o++){let r=t[o];i[2*o]=n(r),i[2*o+1]=e(r)}return new ir(i)}constructor(t){let n=t.length>>1;if(n>0&&"number"!=typeof t[0])throw Error("Expected coords to contain numbers.");this.coords=t;let e=Math.max(2*n-5,0);this._triangles=new Uint32Array(3*e),this._halfedges=new Int32Array(3*e),this._hashSize=Math.ceil(Math.sqrt(n)),this._hullPrev=new Uint32Array(n),this._hullNext=new Uint32Array(n),this._hullTri=new Uint32Array(n),this._hullHash=new Int32Array(this._hashSize),this._ids=new Uint32Array(n),this._dists=new Float64Array(n),this.update()}update(){let t,n,e;let{coords:r,_hullPrev:i,_hullNext:o,_hullTri:u,_hullHash:a}=this,c=r.length>>1,f=1/0,l=1/0,s=-1/0,h=-1/0;for(let t=0;t<c;t++){let n=r[2*t],e=r[2*t+1];n<f&&(f=n),e<l&&(l=e),n>s&&(s=n),e>h&&(h=e),this._ids[t]=t}let d=(f+s)/2,p=(l+h)/2;for(let n=0,e=1/0;n<c;n++){let i=ii(d,p,r[2*n],r[2*n+1]);i<e&&(t=n,e=i)}let v=r[2*t],g=r[2*t+1];for(let e=0,i=1/0;e<c;e++){if(e===t)continue;let o=ii(v,g,r[2*e],r[2*e+1]);o<i&&o>0&&(n=e,i=o)}let y=r[2*n],b=r[2*n+1],m=1/0;for(let i=0;i<c;i++){if(i===t||i===n)continue;let o=function(t,n,e,r,i,o){let u=e-t,a=r-n,c=i-t,f=o-n,l=u*u+a*a,s=c*c+f*f,h=.5/(u*f-a*c),d=(f*l-a*s)*h,p=(u*s-c*l)*h;return d*d+p*p}(v,g,y,b,r[2*i],r[2*i+1]);o<m&&(e=i,m=o)}let _=r[2*e],x=r[2*e+1];if(m===1/0){for(let t=0;t<c;t++)this._dists[t]=r[2*t]-r[0]||r[2*t+1]-r[1];io(this._ids,this._dists,0,c-1);let t=new Uint32Array(c),n=0;for(let e=0,r=-1/0;e<c;e++){let i=this._ids[e],o=this._dists[i];o>r&&(t[n++]=i,r=o)}this.hull=t.subarray(0,n),this.triangles=new Uint32Array(0),this.halfedges=new Uint32Array(0);return}if(0>it(v,g,y,b,_,x)){let t=n,r=y,i=b;n=e,y=_,b=x,e=t,_=r,x=i}let w=function(t,n,e,r,i,o){let u=e-t,a=r-n,c=i-t,f=o-n,l=u*u+a*a,s=c*c+f*f,h=.5/(u*f-a*c);return{x:t+(f*l-a*s)*h,y:n+(u*s-c*l)*h}}(v,g,y,b,_,x);this._cx=w.x,this._cy=w.y;for(let t=0;t<c;t++)this._dists[t]=ii(r[2*t],r[2*t+1],w.x,w.y);io(this._ids,this._dists,0,c-1),this._hullStart=t;let M=3;o[t]=i[e]=n,o[n]=i[t]=e,o[e]=i[n]=t,u[t]=0,u[n]=1,u[e]=2,a.fill(-1),a[this._hashKey(v,g)]=t,a[this._hashKey(y,b)]=n,a[this._hashKey(_,x)]=e,this.trianglesLen=0,this._addTriangle(t,n,e,-1,-1,-1);for(let c=0,f,l;c<this._ids.length;c++){let s=this._ids[c],h=r[2*s],d=r[2*s+1];if(c>0&&2220446049250313e-31>=Math.abs(h-f)&&2220446049250313e-31>=Math.abs(d-l)||(f=h,l=d,s===t||s===n||s===e))continue;let p=0;for(let t=0,n=this._hashKey(h,d);t<this._hashSize&&(-1===(p=a[(n+t)%this._hashSize])||p===o[p]);t++);let v=p=i[p],g;for(;g=o[v],it(h,d,r[2*v],r[2*v+1],r[2*g],r[2*g+1])>=0;)if((v=g)===p){v=-1;break}if(-1===v)continue;let y=this._addTriangle(v,s,o[v],-1,-1,u[v]);u[s]=this._legalize(y+2),u[v]=y,M++;let b=o[v];for(;g=o[b],0>it(h,d,r[2*b],r[2*b+1],r[2*g],r[2*g+1]);)y=this._addTriangle(b,s,g,u[s],-1,u[b]),u[s]=this._legalize(y+2),o[b]=b,M--,b=g;if(v===p)for(;0>it(h,d,r[2*(g=i[v])],r[2*g+1],r[2*v],r[2*v+1]);)y=this._addTriangle(g,s,v,-1,u[v],u[g]),this._legalize(y+2),u[g]=y,o[v]=v,M--,v=g;this._hullStart=i[s]=v,o[v]=i[b]=s,o[s]=b,a[this._hashKey(h,d)]=s,a[this._hashKey(r[2*v],r[2*v+1])]=v}this.hull=new Uint32Array(M);for(let t=0,n=this._hullStart;t<M;t++)this.hull[t]=n,n=o[n];this.triangles=this._triangles.subarray(0,this.trianglesLen),this.halfedges=this._halfedges.subarray(0,this.trianglesLen)}_hashKey(t,n){return Math.floor(function(t,n){let e=t/(Math.abs(t)+Math.abs(n));return(n>0?3-e:1+e)/4}(t-this._cx,n-this._cy)*this._hashSize)%this._hashSize}_legalize(t){let{_triangles:n,_halfedges:e,coords:r}=this,i=0,o=0;for(;;){let u=e[t],a=t-t%3;if(o=a+(t+2)%3,-1===u){if(0===i)break;t=ie[--i];continue}let c=u-u%3,f=a+(t+1)%3,l=c+(u+2)%3,s=n[o],h=n[t],d=n[f],p=n[l];if(function(t,n,e,r,i,o,u,a){let c=t-u,f=n-a,l=e-u,s=r-a,h=i-u,d=o-a,p=l*l+s*s,v=h*h+d*d;return c*(s*v-p*d)-f*(l*v-p*h)+(c*c+f*f)*(l*d-s*h)<0}(r[2*s],r[2*s+1],r[2*h],r[2*h+1],r[2*d],r[2*d+1],r[2*p],r[2*p+1])){n[t]=p,n[u]=s;let r=e[l];if(-1===r){let n=this._hullStart;do{if(this._hullTri[n]===l){this._hullTri[n]=t;break}n=this._hullPrev[n]}while(n!==this._hullStart)}this._link(t,r),this._link(u,e[o]),this._link(o,l);let a=c+(u+1)%3;i<ie.length&&(ie[i++]=a)}else{if(0===i)break;t=ie[--i]}}return o}_link(t,n){this._halfedges[t]=n,-1!==n&&(this._halfedges[n]=t)}_addTriangle(t,n,e,r,i,o){let u=this.trianglesLen;return this._triangles[u]=t,this._triangles[u+1]=n,this._triangles[u+2]=e,this._link(u,r),this._link(u+1,i),this._link(u+2,o),this.trianglesLen+=3,u}}function ii(t,n,e,r){let i=t-e,o=n-r;return i*i+o*o}function io(t,n,e,r){if(r-e<=20)for(let i=e+1;i<=r;i++){let r=t[i],o=n[r],u=i-1;for(;u>=e&&n[t[u]]>o;)t[u+1]=t[u--];t[u+1]=r}else{let i=e+r>>1,o=e+1,u=r;iu(t,i,o),n[t[e]]>n[t[r]]&&iu(t,e,r),n[t[o]]>n[t[r]]&&iu(t,o,r),n[t[e]]>n[t[o]]&&iu(t,e,o);let a=t[o],c=n[a];for(;;){do o++;while(n[t[o]]<c);do u--;while(n[t[u]]>c);if(u<o)break;iu(t,o,u)}t[e+1]=t[u],t[u]=a,r-o+1>=u-e?(io(t,n,o,r),io(t,n,e,u-1)):(io(t,n,e,u-1),io(t,n,o,r))}}function iu(t,n,e){let r=t[n];t[n]=t[e],t[e]=r}function ia(t){return t[0]}function ic(t){return t[1]}class il{constructor(){this._x0=this._y0=this._x1=this._y1=null,this._=""}moveTo(t,n){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")}lineTo(t,n){this._+=`L${this._x1=+t},${this._y1=+n}`}arc(t,n,e){t=+t,n=+n;let r=t+(e=+e),i=n;if(e<0)throw Error("negative radius");null===this._x1?this._+=`M${r},${i}`:(Math.abs(this._x1-r)>1e-6||Math.abs(this._y1-i)>1e-6)&&(this._+="L"+r+","+i),e&&(this._+=`A${e},${e},0,1,1,${t-e},${n}A${e},${e},0,1,1,${this._x1=r},${this._y1=i}`)}rect(t,n,e,r){this._+=`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}h${+e}v${+r}h${-e}Z`}value(){return this._||null}}class is{constructor(){this._=[]}moveTo(t,n){this._.push([t,n])}closePath(){this._.push(this._[0].slice())}lineTo(t,n){this._.push([t,n])}value(){return this._.length?this._:null}}class ih{constructor(t,[n,e,r,i]=[0,0,960,500]){if(!((r=+r)>=(n=+n))||!((i=+i)>=(e=+e)))throw Error("invalid bounds");this.delaunay=t,this._circumcenters=new Float64Array(2*t.points.length),this.vectors=new Float64Array(2*t.points.length),this.xmax=r,this.xmin=n,this.ymax=i,this.ymin=e,this._init()}update(){return this.delaunay.update(),this._init(),this}_init(){let t,n;let{delaunay:{points:e,hull:r,triangles:i},vectors:o}=this,u=this.circumcenters=this._circumcenters.subarray(0,i.length/3*2);for(let o=0,a=0,c=i.length,f,l;o<c;o+=3,a+=2){let c=2*i[o],s=2*i[o+1],h=2*i[o+2],d=e[c],p=e[c+1],v=e[s],g=e[s+1],y=e[h],b=e[h+1],m=v-d,_=g-p,x=y-d,w=b-p,M=(m*w-_*x)*2;if(1e-9>Math.abs(M)){if(void 0===t){for(let i of(t=n=0,r))t+=e[2*i],n+=e[2*i+1];t/=r.length,n/=r.length}let i=1e9*Math.sign((t-d)*w-(n-p)*x);f=(d+y)/2-i*w,l=(p+b)/2+i*x}else{let t=1/M,n=m*m+_*_,e=x*x+w*w;f=d+(w*n-_*e)*t,l=p+(m*e-x*n)*t}u[a]=f,u[a+1]=l}let a=r[r.length-1],c,f=4*a,l,s=e[2*a],h,d=e[2*a+1];o.fill(0);for(let t=0;t<r.length;++t)a=r[t],c=f,l=s,h=d,f=4*a,s=e[2*a],d=e[2*a+1],o[c+2]=o[f]=h-d,o[c+3]=o[f+1]=s-l}render(t){let n=null==t?t=new il:void 0,{delaunay:{halfedges:e,inedges:r,hull:i},circumcenters:o,vectors:u}=this;if(i.length<=1)return null;for(let n=0,r=e.length;n<r;++n){let r=e[n];if(r<n)continue;let i=2*Math.floor(n/3),u=2*Math.floor(r/3),a=o[i],c=o[i+1],f=o[u],l=o[u+1];this._renderSegment(a,c,f,l,t)}let a,c=i[i.length-1];for(let n=0;n<i.length;++n){a=c;let e=2*Math.floor(r[c=i[n]]/3),f=o[e],l=o[e+1],s=4*a,h=this._project(f,l,u[s+2],u[s+3]);h&&this._renderSegment(f,l,h[0],h[1],t)}return n&&n.value()}renderBounds(t){let n=null==t?t=new il:void 0;return t.rect(this.xmin,this.ymin,this.xmax-this.xmin,this.ymax-this.ymin),n&&n.value()}renderCell(t,n){let e=null==n?n=new il:void 0,r=this._clip(t);if(null===r||!r.length)return;n.moveTo(r[0],r[1]);let i=r.length;for(;r[0]===r[i-2]&&r[1]===r[i-1]&&i>1;)i-=2;for(let t=2;t<i;t+=2)(r[t]!==r[t-2]||r[t+1]!==r[t-1])&&n.lineTo(r[t],r[t+1]);return n.closePath(),e&&e.value()}*cellPolygons(){let{delaunay:{points:t}}=this;for(let n=0,e=t.length/2;n<e;++n){let t=this.cellPolygon(n);t&&(t.index=n,yield t)}}cellPolygon(t){let n=new is;return this.renderCell(t,n),n.value()}_renderSegment(t,n,e,r,i){let o;let u=this._regioncode(t,n),a=this._regioncode(e,r);0===u&&0===a?(i.moveTo(t,n),i.lineTo(e,r)):(o=this._clipSegment(t,n,e,r,u,a))&&(i.moveTo(o[0],o[1]),i.lineTo(o[2],o[3]))}contains(t,n,e){return(n=+n)==n&&(e=+e)==e&&this.delaunay._step(t,n,e)===t}*neighbors(t){let n=this._clip(t);if(n)for(let e of this.delaunay.neighbors(t)){let t=this._clip(e);if(t){n:for(let r=0,i=n.length;r<i;r+=2)for(let o=0,u=t.length;o<u;o+=2)if(n[r]===t[o]&&n[r+1]===t[o+1]&&n[(r+2)%i]===t[(o+u-2)%u]&&n[(r+3)%i]===t[(o+u-1)%u]){yield e;break n}}}}_cell(t){let{circumcenters:n,delaunay:{inedges:e,halfedges:r,triangles:i}}=this,o=e[t];if(-1===o)return null;let u=[],a=o;do{let e=Math.floor(a/3);if(u.push(n[2*e],n[2*e+1]),i[a=a%3==2?a-2:a+1]!==t)break;a=r[a]}while(a!==o&&-1!==a);return u}_clip(t){if(0===t&&1===this.delaunay.hull.length)return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];let n=this._cell(t);if(null===n)return null;let{vectors:e}=this,r=4*t;return this._simplify(e[r]||e[r+1]?this._clipInfinite(t,n,e[r],e[r+1],e[r+2],e[r+3]):this._clipFinite(t,n))}_clipFinite(t,n){let e=n.length,r=null,i,o,u=n[e-2],a=n[e-1],c,f=this._regioncode(u,a),l,s=0;for(let h=0;h<e;h+=2)if(i=u,o=a,u=n[h],a=n[h+1],c=f,f=this._regioncode(u,a),0===c&&0===f)l=s,s=0,r?r.push(u,a):r=[u,a];else{let n,e,h,d,p;if(0===c){if(null===(n=this._clipSegment(i,o,u,a,c,f)))continue;[e,h,d,p]=n}else{if(null===(n=this._clipSegment(u,a,i,o,f,c)))continue;[d,p,e,h]=n,l=s,s=this._edgecode(e,h),l&&s&&this._edge(t,l,s,r,r.length),r?r.push(e,h):r=[e,h]}l=s,s=this._edgecode(d,p),l&&s&&this._edge(t,l,s,r,r.length),r?r.push(d,p):r=[d,p]}if(r)l=s,s=this._edgecode(r[0],r[1]),l&&s&&this._edge(t,l,s,r,r.length);else if(this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2))return[this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax,this.xmin,this.ymin];return r}_clipSegment(t,n,e,r,i,o){let u=i<o;for(u&&([t,n,e,r,i,o]=[e,r,t,n,o,i]);;){if(0===i&&0===o)return u?[e,r,t,n]:[t,n,e,r];if(i&o)return null;let a,c,f=i||o;8&f?(a=t+(e-t)*(this.ymax-n)/(r-n),c=this.ymax):4&f?(a=t+(e-t)*(this.ymin-n)/(r-n),c=this.ymin):2&f?(c=n+(r-n)*(this.xmax-t)/(e-t),a=this.xmax):(c=n+(r-n)*(this.xmin-t)/(e-t),a=this.xmin),i?(t=a,n=c,i=this._regioncode(t,n)):(e=a,r=c,o=this._regioncode(e,r))}}_clipInfinite(t,n,e,r,i,o){let u=Array.from(n),a;if((a=this._project(u[0],u[1],e,r))&&u.unshift(a[0],a[1]),(a=this._project(u[u.length-2],u[u.length-1],i,o))&&u.push(a[0],a[1]),u=this._clipFinite(t,u))for(let n=0,e=u.length,r,i=this._edgecode(u[e-2],u[e-1]);n<e;n+=2)r=i,i=this._edgecode(u[n],u[n+1]),r&&i&&(n=this._edge(t,r,i,u,n),e=u.length);else this.contains(t,(this.xmin+this.xmax)/2,(this.ymin+this.ymax)/2)&&(u=[this.xmin,this.ymin,this.xmax,this.ymin,this.xmax,this.ymax,this.xmin,this.ymax]);return u}_edge(t,n,e,r,i){for(;n!==e;){let e,o;switch(n){case 5:n=4;continue;case 4:n=6,e=this.xmax,o=this.ymin;break;case 6:n=2;continue;case 2:n=10,e=this.xmax,o=this.ymax;break;case 10:n=8;continue;case 8:n=9,e=this.xmin,o=this.ymax;break;case 9:n=1;continue;case 1:n=5,e=this.xmin,o=this.ymin}(r[i]!==e||r[i+1]!==o)&&this.contains(t,e,o)&&(r.splice(i,0,e,o),i+=2)}return i}_project(t,n,e,r){let i=1/0,o,u,a;if(r<0){if(n<=this.ymin)return null;(o=(this.ymin-n)/r)<i&&(a=this.ymin,u=t+(i=o)*e)}else if(r>0){if(n>=this.ymax)return null;(o=(this.ymax-n)/r)<i&&(a=this.ymax,u=t+(i=o)*e)}if(e>0){if(t>=this.xmax)return null;(o=(this.xmax-t)/e)<i&&(u=this.xmax,a=n+(i=o)*r)}else if(e<0){if(t<=this.xmin)return null;(o=(this.xmin-t)/e)<i&&(u=this.xmin,a=n+(i=o)*r)}return[u,a]}_edgecode(t,n){return(t===this.xmin?1:t===this.xmax?2:0)|(n===this.ymin?4:n===this.ymax?8:0)}_regioncode(t,n){return(t<this.xmin?1:t>this.xmax?2:0)|(n<this.ymin?4:n>this.ymax?8:0)}_simplify(t){if(t&&t.length>4){for(let n=0;n<t.length;n+=2){let e=(n+2)%t.length,r=(n+4)%t.length;(t[n]===t[e]&&t[e]===t[r]||t[n+1]===t[e+1]&&t[e+1]===t[r+1])&&(t.splice(e,2),n-=2)}t.length||(t=null)}return t}}let id=2*Math.PI,ip=Math.pow;function iv(t){return t[0]}function ig(t){return t[1]}class iy{static from(t,n=iv,e=ig,r){return new iy("length"in t?function(t,n,e,r){let i=t.length,o=new Float64Array(2*i);for(let u=0;u<i;++u){let i=t[u];o[2*u]=n.call(r,i,u,t),o[2*u+1]=e.call(r,i,u,t)}return o}(t,n,e,r):Float64Array.from(function*(t,n,e,r){let i=0;for(let o of t)yield n.call(r,o,i,t),yield e.call(r,o,i,t),++i}(t,n,e,r)))}constructor(t){this._delaunator=new ir(t),this.inedges=new Int32Array(t.length/2),this._hullIndex=new Int32Array(t.length/2),this.points=this._delaunator.coords,this._init()}update(){return this._delaunator.update(),this._init(),this}_init(){let t=this._delaunator,n=this.points;if(t.hull&&t.hull.length>2&&function(t){let{triangles:n,coords:e}=t;for(let t=0;t<n.length;t+=3){let r=2*n[t],i=2*n[t+1],o=2*n[t+2];if((e[o]-e[r])*(e[i+1]-e[r+1])-(e[i]-e[r])*(e[o+1]-e[r+1])>1e-10)return!1}return!0}(t)){this.collinear=Int32Array.from({length:n.length/2},(t,n)=>n).sort((t,e)=>n[2*t]-n[2*e]||n[2*t+1]-n[2*e+1]);let t=this.collinear[0],i=this.collinear[this.collinear.length-1],o=[n[2*t],n[2*t+1],n[2*i],n[2*i+1]],u=1e-8*Math.hypot(o[3]-o[1],o[2]-o[0]);for(let t=0,i=n.length/2;t<i;++t){var e,r;let i=[(e=n[2*t])+Math.sin(e+(r=n[2*t+1]))*u,r+Math.cos(e-r)*u];n[2*t]=i[0],n[2*t+1]=i[1]}this._delaunator=new ir(n)}else delete this.collinear;let i=this.halfedges=this._delaunator.halfedges,o=this.hull=this._delaunator.hull,u=this.triangles=this._delaunator.triangles,a=this.inedges.fill(-1),c=this._hullIndex.fill(-1);for(let t=0,n=i.length;t<n;++t){let n=u[t%3==2?t-2:t+1];(-1===i[t]||-1===a[n])&&(a[n]=t)}for(let t=0,n=o.length;t<n;++t)c[o[t]]=t;o.length<=2&&o.length>0&&(this.triangles=new Int32Array(3).fill(-1),this.halfedges=new Int32Array(3).fill(-1),this.triangles[0]=o[0],a[o[0]]=1,2===o.length&&(a[o[1]]=0,this.triangles[1]=o[1],this.triangles[2]=o[1]))}voronoi(t){return new ih(this,t)}*neighbors(t){let{inedges:n,hull:e,_hullIndex:r,halfedges:i,triangles:o,collinear:u}=this;if(u){let n=u.indexOf(t);n>0&&(yield u[n-1]),n<u.length-1&&(yield u[n+1]);return}let a=n[t];if(-1===a)return;let c=a,f=-1;do{if(yield f=o[c],o[c=c%3==2?c-2:c+1]!==t)return;if(-1===(c=i[c])){let n=e[(r[t]+1)%e.length];n!==f&&(yield n);return}}while(c!==a)}find(t,n,e=0){let r;if((t=+t)!=t||(n=+n)!=n)return -1;let i=e;for(;(r=this._step(e,t,n))>=0&&r!==e&&r!==i;)e=r;return r}_step(t,n,e){let{inedges:r,hull:i,_hullIndex:o,halfedges:u,triangles:a,points:c}=this;if(-1===r[t]||!c.length)return(t+1)%(c.length>>1);let f=t,l=ip(n-c[2*t],2)+ip(e-c[2*t+1],2),s=r[t],h=s;do{let r=a[h],s=ip(n-c[2*r],2)+ip(e-c[2*r+1],2);if(s<l&&(l=s,f=r),a[h=h%3==2?h-2:h+1]!==t)break;if(-1===(h=u[h])){if((h=i[(o[t]+1)%i.length])!==r&&ip(n-c[2*h],2)+ip(e-c[2*h+1],2)<l)return h;break}}while(h!==s);return f}render(t){let n=null==t?t=new il:void 0,{points:e,halfedges:r,triangles:i}=this;for(let n=0,o=r.length;n<o;++n){let o=r[n];if(o<n)continue;let u=2*i[n],a=2*i[o];t.moveTo(e[u],e[u+1]),t.lineTo(e[a],e[a+1])}return this.renderHull(t),n&&n.value()}renderPoints(t,n){void 0!==n||t&&"function"==typeof t.moveTo||(n=t,t=null),n=void 0==n?2:+n;let e=null==t?t=new il:void 0,{points:r}=this;for(let e=0,i=r.length;e<i;e+=2){let i=r[e],o=r[e+1];t.moveTo(i+n,o),t.arc(i,o,n,0,id)}return e&&e.value()}renderHull(t){let n=null==t?t=new il:void 0,{hull:e,points:r}=this,i=2*e[0],o=e.length;t.moveTo(r[i],r[i+1]);for(let n=1;n<o;++n){let i=2*e[n];t.lineTo(r[i],r[i+1])}return t.closePath(),n&&n.value()}hullPolygon(){let t=new is;return this.renderHull(t),t.value()}renderTriangle(t,n){let e=null==n?n=new il:void 0,{points:r,triangles:i}=this,o=2*i[t*=3],u=2*i[t+1],a=2*i[t+2];return n.moveTo(r[o],r[o+1]),n.lineTo(r[u],r[u+1]),n.lineTo(r[a],r[a+1]),n.closePath(),e&&e.value()}*trianglePolygons(){let{triangles:t}=this;for(let n=0,e=t.length/3;n<e;++n)yield this.trianglePolygon(n)}trianglePolygon(t){let n=new is;return this.renderTriangle(t,n),n.value()}}var ib=t=>()=>t;function im(t,{sourceEvent:n,subject:e,target:r,identifier:i,active:o,x:u,y:a,dx:c,dy:f,dispatch:l}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},subject:{value:e,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:i,enumerable:!0,configurable:!0},active:{value:o,enumerable:!0,configurable:!0},x:{value:u,enumerable:!0,configurable:!0},y:{value:a,enumerable:!0,configurable:!0},dx:{value:c,enumerable:!0,configurable:!0},dy:{value:f,enumerable:!0,configurable:!0},_:{value:l}})}function i_(t){return!t.ctrlKey&&!t.button}function ix(){return this.parentNode}function iw(t,n){return null==n?{x:t.x,y:t.y}:n}function iM(){return navigator.maxTouchPoints||"ontouchstart"in this}function iA(){var t,n,e,r,i=i_,o=ix,u=iw,a=iM,c={},f=tQ("start","drag","end"),l=0,s=0;function h(t){t.on("mousedown.drag",d).filter(a).on("touchstart.drag",g).on("touchmove.drag",y,nZ).on("touchend.drag touchcancel.drag",b).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function d(u,a){if(!r&&i.call(this,u,a)){var c=m(this,o.call(this,u,a),u,a,"mouse");c&&(nO(u.view).on("mousemove.drag",p,nz).on("mouseup.drag",v,nz),nV(u.view),nB(u),e=!1,t=u.clientX,n=u.clientY,c("start",u))}}function p(r){if(nL(r),!e){var i=r.clientX-t,o=r.clientY-n;e=i*i+o*o>s}c.mouse("drag",r)}function v(t){nO(t.view).on("mousemove.drag mouseup.drag",null),nD(t.view,e),nL(t),c.mouse("end",t)}function g(t,n){if(i.call(this,t,n)){var e,r,u=t.changedTouches,a=o.call(this,t,n),c=u.length;for(e=0;e<c;++e)(r=m(this,a,t,n,u[e].identifier,u[e]))&&(nB(t),r("start",t,u[e]))}}function y(t){var n,e,r=t.changedTouches,i=r.length;for(n=0;n<i;++n)(e=c[r[n].identifier])&&(nL(t),e("drag",t,r[n]))}function b(t){var n,e,i=t.changedTouches,o=i.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),n=0;n<o;++n)(e=c[i[n].identifier])&&(nB(t),e("end",t,i[n]))}function m(t,n,e,r,i,o){var a,s,d,p=f.copy(),v=nI(o||e,n);if(null!=(d=u.call(t,new im("beforestart",{sourceEvent:e,target:h,identifier:i,active:l,x:v[0],y:v[1],dx:0,dy:0,dispatch:p}),r)))return a=d.x-v[0]||0,s=d.y-v[1]||0,function e(o,u,f){var g,y=v;switch(o){case"start":c[i]=e,g=l++;break;case"end":delete c[i],--l;case"drag":v=nI(f||u,n),g=l}p.call(o,t,new im(o,{sourceEvent:u,subject:d,target:h,identifier:i,active:g,x:v[0]+a,y:v[1]+s,dx:v[0]-y[0],dy:v[1]-y[1],dispatch:p}),r)}}return h.filter=function(t){return arguments.length?(i="function"==typeof t?t:ib(!!t),h):i},h.container=function(t){return arguments.length?(o="function"==typeof t?t:ib(t),h):o},h.subject=function(t){return arguments.length?(u="function"==typeof t?t:ib(t),h):u},h.touchable=function(t){return arguments.length?(a="function"==typeof t?t:ib(!!t),h):a},h.on=function(){var t=f.on.apply(f,arguments);return t===f?h:t},h.clickDistance=function(t){return arguments.length?(s=(t=+t)*t,h):Math.sqrt(s)},h}im.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};var iT={},iS={};function ik(t){return Function("d","return {"+t.map(function(t,n){return JSON.stringify(t)+": d["+n+'] || ""'}).join(",")+"}")}function iP(t){var n=Object.create(null),e=[];return t.forEach(function(t){for(var r in t)r in n||e.push(n[r]=r)}),e}function iE(t,n){var e=t+"",r=e.length;return r<n?Array(n-r+1).join(0)+e:e}function iC(t){var n=RegExp('["'+t+"\n\r]"),e=t.charCodeAt(0);function r(t,n){var r,i=[],o=t.length,u=0,a=0,c=o<=0,f=!1;function l(){if(c)return iS;if(f)return f=!1,iT;var n,r,i=u;if(34===t.charCodeAt(i)){for(;u++<o&&34!==t.charCodeAt(u)||34===t.charCodeAt(++u););return(n=u)>=o?c=!0:10===(r=t.charCodeAt(u++))?f=!0:13===r&&(f=!0,10===t.charCodeAt(u)&&++u),t.slice(i+1,n-1).replace(/""/g,'"')}for(;u<o;){if(10===(r=t.charCodeAt(n=u++)))f=!0;else if(13===r)f=!0,10===t.charCodeAt(u)&&++u;else if(r!==e)continue;return t.slice(i,n)}return c=!0,t.slice(i,o)}for(10===t.charCodeAt(o-1)&&--o,13===t.charCodeAt(o-1)&&--o;(r=l())!==iS;){for(var s=[];r!==iT&&r!==iS;)s.push(r),r=l();n&&null==(s=n(s,a++))||i.push(s)}return i}function i(n,e){return n.map(function(n){return e.map(function(t){return u(n[t])}).join(t)})}function o(n){return n.map(u).join(t)}function u(t){var e,r,i,o,u,a;return null==t?"":t instanceof Date?(r=(e=t).getUTCHours(),i=e.getUTCMinutes(),o=e.getUTCSeconds(),u=e.getUTCMilliseconds(),isNaN(e)?"Invalid Date":((a=e.getUTCFullYear())<0?"-"+iE(-a,6):a>9999?"+"+iE(a,6):iE(a,4))+"-"+iE(e.getUTCMonth()+1,2)+"-"+iE(e.getUTCDate(),2)+(u?"T"+iE(r,2)+":"+iE(i,2)+":"+iE(o,2)+"."+iE(u,3)+"Z":o?"T"+iE(r,2)+":"+iE(i,2)+":"+iE(o,2)+"Z":i||r?"T"+iE(r,2)+":"+iE(i,2)+"Z":"")):n.test(t+="")?'"'+t.replace(/"/g,'""')+'"':t}return{parse:function(t,n){var e,i,o=r(t,function(t,r){var o;if(e)return e(t,r-1);i=t,e=n?(o=ik(t),function(e,r){return n(o(e),r,t)}):ik(t)});return o.columns=i||[],o},parseRows:r,format:function(n,e){return null==e&&(e=iP(n)),[e.map(u).join(t)].concat(i(n,e)).join("\n")},formatBody:function(t,n){return null==n&&(n=iP(t)),i(t,n).join("\n")},formatRows:function(t){return t.map(o).join("\n")},formatRow:o,formatValue:u}}var iN=iC(","),iR=iN.parse,iO=iN.parseRows,iZ=iN.format,iz=iN.formatBody,iB=iN.formatRows,iL=iN.formatRow,iV=iN.formatValue,iD=iC("	"),iG=iD.parse,ij=iD.parseRows,iI=iD.format,iq=iD.formatBody,iF=iD.formatRows,iH=iD.formatRow,iY=iD.formatValue;function i$(t){for(var n in t){var e,r,i=t[n].trim();if(i){if("true"===i)i=!0;else if("false"===i)i=!1;else if("NaN"===i)i=NaN;else if(isNaN(e=+i)){if(!(r=i.match(/^([-+]\d{2})?\d{4}(-\d{2}(-\d{2})?)?(T\d{2}:\d{2}(:\d{2}(\.\d{3})?)?(Z|[-+]\d{2}:\d{2})?)?$/)))continue;iX&&r[4]&&!r[7]&&(i=i.replace(/-/g,"/").replace(/T/," ")),i=new Date(i)}else i=e}else i=null;t[n]=i}return t}let iX=new Date("2019-01-01T00:00").getHours()||new Date("2019-07-01T00:00").getHours(),iW=t=>+t;function iK(t){return t*t}function iU(t){return t*(2-t)}function iJ(t){return((t*=2)<=1?t*t:--t*(2-t)+1)/2}var iQ=function t(n){function e(t){return Math.pow(t,n)}return n=+n,e.exponent=t,e}(3),i0=function t(n){function e(t){return 1-Math.pow(1-t,n)}return n=+n,e.exponent=t,e}(3),i1=function t(n){function e(t){return((t*=2)<=1?Math.pow(t,n):2-Math.pow(2-t,n))/2}return n=+n,e.exponent=t,e}(3),i2=Math.PI,i6=i2/2;function i8(t){return 1==+t?1:1-Math.cos(t*i6)}function i3(t){return Math.sin(t*i6)}function i4(t){return(1-Math.cos(i2*t))/2}function i5(t){return(Math.pow(2,-10*t)-9765625e-10)*1.0009775171065494}function i7(t){return i5(1-+t)}function i9(t){return 1-i5(t)}function ot(t){return((t*=2)<=1?i5(1-t):2-i5(t-1))/2}function on(t){return 1-Math.sqrt(1-t*t)}function oe(t){return Math.sqrt(1- --t*t)}function or(t){return((t*=2)<=1?1-Math.sqrt(1-t*t):Math.sqrt(1-(t-=2)*t)+1)/2}var oi=4/11,oo=6/11,ou=8/11,oa=3/4,oc=9/11,of=10/11,ol=15/16,os=21/22,oh=63/64,od=1/(4/11)/(4/11);function op(t){return 1-ov(1-t)}function ov(t){return(t=+t)<oi?od*t*t:t<ou?od*(t-=oo)*t+oa:t<of?od*(t-=oc)*t+ol:od*(t-=os)*t+oh}function og(t){return((t*=2)<=1?1-ov(1-t):ov(t-1)+1)/2}var oy=function t(n){function e(t){return(t=+t)*t*(n*(t-1)+t)}return n=+n,e.overshoot=t,e}(1.70158),ob=function t(n){function e(t){return--t*t*((t+1)*n+t)+1}return n=+n,e.overshoot=t,e}(1.70158),om=function t(n){function e(t){return((t*=2)<1?t*t*((n+1)*t-n):(t-=2)*t*((n+1)*t+n)+2)/2}return n=+n,e.overshoot=t,e}(1.70158),o_=2*Math.PI,ox=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=o_);function i(t){return n*i5(- --t)*Math.sin((r-t)/e)}return i.amplitude=function(n){return t(n,e*o_)},i.period=function(e){return t(n,e)},i}(1,.3),ow=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=o_);function i(t){return 1-n*i5(t=+t)*Math.sin((t+r)/e)}return i.amplitude=function(n){return t(n,e*o_)},i.period=function(e){return t(n,e)},i}(1,.3),oM=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=o_);function i(t){return((t=2*t-1)<0?n*i5(-t)*Math.sin((r-t)/e):2-n*i5(t)*Math.sin((r+t)/e))/2}return i.amplitude=function(n){return t(n,e*o_)},i.period=function(e){return t(n,e)},i}(1,.3);function oA(t){if(!t.ok)throw Error(t.status+" "+t.statusText);return t.blob()}function oT(t,n){return fetch(t,n).then(oA)}function oS(t){if(!t.ok)throw Error(t.status+" "+t.statusText);return t.arrayBuffer()}function ok(t,n){return fetch(t,n).then(oS)}function oP(t){if(!t.ok)throw Error(t.status+" "+t.statusText);return t.text()}function oE(t,n){return fetch(t,n).then(oP)}function oC(t){return function(n,e,r){return 2==arguments.length&&"function"==typeof e&&(r=e,e=void 0),oE(n,e).then(function(n){return t(n,r)})}}function oN(t,n,e,r){3==arguments.length&&"function"==typeof e&&(r=e,e=void 0);var i=iC(t);return oE(n,e).then(function(t){return i.parse(t,r)})}var oR=oC(iR),oO=oC(iG);function oZ(t,n){return new Promise(function(e,r){var i=new Image;for(var o in n)i[o]=n[o];i.onerror=r,i.onload=function(){e(i)},i.src=t})}function oz(t){if(!t.ok)throw Error(t.status+" "+t.statusText);if(204!==t.status&&205!==t.status)return t.json()}function oB(t,n){return fetch(t,n).then(oz)}function oL(t){return(n,e)=>oE(n,e).then(n=>(new DOMParser).parseFromString(n,t))}var oV=oL("application/xml"),oD=oL("text/html"),oG=oL("image/svg+xml");function oj(t,n){var e,r=1;function i(){var i,o,u=e.length,a=0,c=0;for(i=0;i<u;++i)a+=(o=e[i]).x,c+=o.y;for(a=(a/u-t)*r,c=(c/u-n)*r,i=0;i<u;++i)o=e[i],o.x-=a,o.y-=c}return null==t&&(t=0),null==n&&(n=0),i.initialize=function(t){e=t},i.x=function(n){return arguments.length?(t=+n,i):t},i.y=function(t){return arguments.length?(n=+t,i):n},i.strength=function(t){return arguments.length?(r=+t,i):r},i}function oI(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,o,u,a,c,f,l,s,h,d=t._root,p={data:r},v=t._x0,g=t._y0,y=t._x1,b=t._y1;if(!d)return t._root=p,t;for(;d.length;)if((f=n>=(o=(v+y)/2))?v=o:y=o,(l=e>=(u=(g+b)/2))?g=u:b=u,i=d,!(d=d[s=l<<1|f]))return i[s]=p,t;if(a=+t._x.call(null,d.data),c=+t._y.call(null,d.data),n===a&&e===c)return p.next=d,i?i[s]=p:t._root=p,t;do i=i?i[s]=[,,,,]:t._root=[,,,,],(f=n>=(o=(v+y)/2))?v=o:y=o,(l=e>=(u=(g+b)/2))?g=u:b=u;while((s=l<<1|f)==(h=(c>=u)<<1|a>=o));return i[h]=d,i[s]=p,t}function oq(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i}function oF(t){return t[0]}function oH(t){return t[1]}function oY(t,n,e){var r=new o$(null==n?oF:n,null==e?oH:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function o$(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function oX(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var oW=oY.prototype=o$.prototype;function oK(t){return function(){return t}}function oU(t){return(t()-.5)*1e-6}function oJ(t){return t.x+t.vx}function oQ(t){return t.y+t.vy}function o0(t){var n,e,r,i=1,o=1;function u(){for(var t,u,c,f,l,s,h,d=n.length,p=0;p<o;++p)for(t=0,u=oY(n,oJ,oQ).visitAfter(a);t<d;++t)h=(s=e[(c=n[t]).index])*s,f=c.x+c.vx,l=c.y+c.vy,u.visit(v);function v(t,n,e,o,u){var a=t.data,d=t.r,p=s+d;if(a){if(a.index>c.index){var v=f-a.x-a.vx,g=l-a.y-a.vy,y=v*v+g*g;y<p*p&&(0===v&&(y+=(v=oU(r))*v),0===g&&(y+=(g=oU(r))*g),y=(p-(y=Math.sqrt(y)))/y*i,c.vx+=(v*=y)*(p=(d*=d)/(h+d)),c.vy+=(g*=y)*p,a.vx-=v*(p=1-p),a.vy-=g*p)}return}return n>f+p||o<f-p||e>l+p||u<l-p}}function a(t){if(t.data)return t.r=e[t.data.index];for(var n=t.r=0;n<4;++n)t[n]&&t[n].r>t.r&&(t.r=t[n].r)}function c(){if(n){var r,i,o=n.length;for(r=0,e=Array(o);r<o;++r)e[(i=n[r]).index]=+t(i,r,n)}}return"function"!=typeof t&&(t=oK(null==t?1:+t)),u.initialize=function(t,e){n=t,r=e,c()},u.iterations=function(t){return arguments.length?(o=+t,u):o},u.strength=function(t){return arguments.length?(i=+t,u):i},u.radius=function(n){return arguments.length?(t="function"==typeof n?n:oK(+n),c(),u):t},u}function o1(t){return t.index}function o2(t,n){var e=t.get(n);if(!e)throw Error("node not found: "+n);return e}function o6(t){var n,e,r,i,o,u,a=o1,c=function(t){return 1/Math.min(i[t.source.index],i[t.target.index])},f=oK(30),l=1;function s(r){for(var i=0,a=t.length;i<l;++i)for(var c,f,s,h,d,p,v,g=0;g<a;++g)f=(c=t[g]).source,p=((p=Math.sqrt((h=(s=c.target).x+s.vx-f.x-f.vx||oU(u))*h+(d=s.y+s.vy-f.y-f.vy||oU(u))*d))-e[g])/p*r*n[g],h*=p,d*=p,s.vx-=h*(v=o[g]),s.vy-=d*v,f.vx+=h*(v=1-v),f.vy+=d*v}function h(){if(r){var u,c,f=r.length,l=t.length,s=new Map(r.map((t,n)=>[a(t,n,r),t]));for(u=0,i=Array(f);u<l;++u)(c=t[u]).index=u,"object"!=typeof c.source&&(c.source=o2(s,c.source)),"object"!=typeof c.target&&(c.target=o2(s,c.target)),i[c.source.index]=(i[c.source.index]||0)+1,i[c.target.index]=(i[c.target.index]||0)+1;for(u=0,o=Array(l);u<l;++u)c=t[u],o[u]=i[c.source.index]/(i[c.source.index]+i[c.target.index]);n=Array(l),d(),e=Array(l),p()}}function d(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+c(t[e],e,t)}function p(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+f(t[n],n,t)}return null==t&&(t=[]),s.initialize=function(t,n){r=t,u=n,h()},s.links=function(n){return arguments.length?(t=n,h(),s):t},s.id=function(t){return arguments.length?(a=t,s):a},s.iterations=function(t){return arguments.length?(l=+t,s):l},s.strength=function(t){return arguments.length?(c="function"==typeof t?t:oK(+t),d(),s):c},s.distance=function(t){return arguments.length?(f="function"==typeof t?t:oK(+t),p(),s):f},s}function o8(t){return t.x}function o3(t){return t.y}oW.copy=function(){var t,n,e=new o$(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return e;if(!r.length)return e._root=oX(r),e;for(t=[{source:r,target:e._root=[,,,,]}];r=t.pop();)for(var i=0;i<4;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=[,,,,]}):r.target[i]=oX(n));return e},oW.add=function(t){let n=+this._x.call(null,t),e=+this._y.call(null,t);return oI(this.cover(n,e),n,e,t)},oW.addAll=function(t){var n,e,r,i,o=t.length,u=Array(o),a=Array(o),c=1/0,f=1/0,l=-1/0,s=-1/0;for(e=0;e<o;++e)!(isNaN(r=+this._x.call(null,n=t[e]))||isNaN(i=+this._y.call(null,n)))&&(u[e]=r,a[e]=i,r<c&&(c=r),r>l&&(l=r),i<f&&(f=i),i>s&&(s=i));if(c>l||f>s)return this;for(this.cover(c,f).cover(l,s),e=0;e<o;++e)oI(this,u[e],a[e],t[e]);return this},oW.cover=function(t,n){if(isNaN(t=+t)||isNaN(n=+n))return this;var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{for(var u,a,c=i-e||1,f=this._root;e>t||t>=i||r>n||n>=o;)switch(a=(n<r)<<1|t<e,(u=[,,,,])[a]=f,f=u,c*=2,a){case 0:i=e+c,o=r+c;break;case 1:e=i-c,o=r+c;break;case 2:i=e+c,r=o-c;break;case 3:e=i-c,r=o-c}this._root&&this._root.length&&(this._root=f)}return this._x0=e,this._y0=r,this._x1=i,this._y1=o,this},oW.data=function(){var t=[];return this.visit(function(n){if(!n.length)do t.push(n.data);while(n=n.next)}),t},oW.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},oW.find=function(t,n,e){var r,i,o,u,a,c,f,l=this._x0,s=this._y0,h=this._x1,d=this._y1,p=[],v=this._root;for(v&&p.push(new oq(v,l,s,h,d)),null==e?e=1/0:(l=t-e,s=n-e,h=t+e,d=n+e,e*=e);c=p.pop();)if((v=c.node)&&!((i=c.x0)>h)&&!((o=c.y0)>d)&&!((u=c.x1)<l)&&!((a=c.y1)<s)){if(v.length){var g=(i+u)/2,y=(o+a)/2;p.push(new oq(v[3],g,y,u,a),new oq(v[2],i,y,g,a),new oq(v[1],g,o,u,y),new oq(v[0],i,o,g,y)),(f=(n>=y)<<1|t>=g)&&(c=p[p.length-1],p[p.length-1]=p[p.length-1-f],p[p.length-1-f]=c)}else{var b=t-+this._x.call(null,v.data),m=n-+this._y.call(null,v.data),_=b*b+m*m;if(_<e){var x=Math.sqrt(e=_);l=t-x,s=n-x,h=t+x,d=n+x,r=v.data}}}return r},oW.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(u=+this._y.call(null,t)))return this;var n,e,r,i,o,u,a,c,f,l,s,h,d=this._root,p=this._x0,v=this._y0,g=this._x1,y=this._y1;if(!d)return this;if(d.length)for(;;){if((f=o>=(a=(p+g)/2))?p=a:g=a,(l=u>=(c=(v+y)/2))?v=c:y=c,n=d,!(d=d[s=l<<1|f]))return this;if(!d.length)break;(n[s+1&3]||n[s+2&3]||n[s+3&3])&&(e=n,h=s)}for(;d.data!==t;)if(r=d,!(d=d.next))return this;return((i=d.next)&&delete d.next,r)?i?r.next=i:delete r.next:n?(i?n[s]=i:delete n[s],(d=n[0]||n[1]||n[2]||n[3])&&d===(n[3]||n[2]||n[1]||n[0])&&!d.length&&(e?e[h]=d:this._root=d)):this._root=i,this},oW.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},oW.root=function(){return this._root},oW.size=function(){var t=0;return this.visit(function(n){if(!n.length)do++t;while(n=n.next)}),t},oW.visit=function(t){var n,e,r,i,o,u,a=[],c=this._root;for(c&&a.push(new oq(c,this._x0,this._y0,this._x1,this._y1));n=a.pop();)if(!t(c=n.node,r=n.x0,i=n.y0,o=n.x1,u=n.y1)&&c.length){var f=(r+o)/2,l=(i+u)/2;(e=c[3])&&a.push(new oq(e,f,l,o,u)),(e=c[2])&&a.push(new oq(e,r,l,f,u)),(e=c[1])&&a.push(new oq(e,f,i,o,l)),(e=c[0])&&a.push(new oq(e,r,i,f,l))}return this},oW.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new oq(this._root,this._x0,this._y0,this._x1,this._y1));n=e.pop();){var i=n.node;if(i.length){var o,u=n.x0,a=n.y0,c=n.x1,f=n.y1,l=(u+c)/2,s=(a+f)/2;(o=i[0])&&e.push(new oq(o,u,a,l,s)),(o=i[1])&&e.push(new oq(o,l,a,c,s)),(o=i[2])&&e.push(new oq(o,u,s,l,f)),(o=i[3])&&e.push(new oq(o,l,s,c,f))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.x1,n.y1);return this},oW.x=function(t){return arguments.length?(this._x=t,this):this._x},oW.y=function(t){return arguments.length?(this._y=t,this):this._y};var o4=Math.PI*(3-Math.sqrt(5));function o5(t){let n;var e,r=1,i=.001,o=1-Math.pow(.001,1/300),u=0,a=.6,c=new Map,f=n2(h),l=tQ("tick","end"),s=(n=1,()=>(n=(1664525*n+1013904223)%4294967296)/4294967296);function h(){d(),l.call("tick",e),r<i&&(f.stop(),l.call("end",e))}function d(n){var i,f,l=t.length;void 0===n&&(n=1);for(var s=0;s<n;++s)for(r+=(u-r)*o,c.forEach(function(t){t(r)}),i=0;i<l;++i)null==(f=t[i]).fx?f.x+=f.vx*=a:(f.x=f.fx,f.vx=0),null==f.fy?f.y+=f.vy*=a:(f.y=f.fy,f.vy=0);return e}function p(){for(var n,e=0,r=t.length;e<r;++e){if((n=t[e]).index=e,null!=n.fx&&(n.x=n.fx),null!=n.fy&&(n.y=n.fy),isNaN(n.x)||isNaN(n.y)){var i=10*Math.sqrt(.5+e),o=e*o4;n.x=i*Math.cos(o),n.y=i*Math.sin(o)}(isNaN(n.vx)||isNaN(n.vy))&&(n.vx=n.vy=0)}}function v(n){return n.initialize&&n.initialize(t,s),n}return null==t&&(t=[]),p(),e={tick:d,restart:function(){return f.restart(h),e},stop:function(){return f.stop(),e},nodes:function(n){return arguments.length?(t=n,p(),c.forEach(v),e):t},alpha:function(t){return arguments.length?(r=+t,e):r},alphaMin:function(t){return arguments.length?(i=+t,e):i},alphaDecay:function(t){return arguments.length?(o=+t,e):+o},alphaTarget:function(t){return arguments.length?(u=+t,e):u},velocityDecay:function(t){return arguments.length?(a=1-t,e):1-a},randomSource:function(t){return arguments.length?(s=t,c.forEach(v),e):s},force:function(t,n){return arguments.length>1?(null==n?c.delete(t):c.set(t,v(n)),e):c.get(t)},find:function(n,e,r){var i,o,u,a,c,f=0,l=t.length;for(null==r?r=1/0:r*=r,f=0;f<l;++f)(u=(i=n-(a=t[f]).x)*i+(o=e-a.y)*o)<r&&(c=a,r=u);return c},on:function(t,n){return arguments.length>1?(l.on(t,n),e):l.on(t)}}}function o7(){var t,n,e,r,i,o=oK(-30),u=1,a=1/0,c=.81;function f(e){var i,o=t.length,u=oY(t,o8,o3).visitAfter(s);for(r=e,i=0;i<o;++i)n=t[i],u.visit(h)}function l(){if(t){var n,e,r=t.length;for(n=0,i=Array(r);n<r;++n)i[(e=t[n]).index]=+o(e,n,t)}}function s(t){var n,e,r,o,u,a=0,c=0;if(t.length){for(r=o=u=0;u<4;++u)(n=t[u])&&(e=Math.abs(n.value))&&(a+=n.value,c+=e,r+=e*n.x,o+=e*n.y);t.x=r/c,t.y=o/c}else{(n=t).x=n.data.x,n.y=n.data.y;do a+=i[n.data.index];while(n=n.next)}t.value=a}function h(t,o,f,l){if(!t.value)return!0;var s=t.x-n.x,h=t.y-n.y,d=l-o,p=s*s+h*h;if(d*d/c<p)return p<a&&(0===s&&(p+=(s=oU(e))*s),0===h&&(p+=(h=oU(e))*h),p<u&&(p=Math.sqrt(u*p)),n.vx+=s*t.value*r/p,n.vy+=h*t.value*r/p),!0;if(!t.length&&!(p>=a)){(t.data!==n||t.next)&&(0===s&&(p+=(s=oU(e))*s),0===h&&(p+=(h=oU(e))*h),p<u&&(p=Math.sqrt(u*p)));do t.data!==n&&(d=i[t.data.index]*r/p,n.vx+=s*d,n.vy+=h*d);while(t=t.next)}}return f.initialize=function(n,r){t=n,e=r,l()},f.strength=function(t){return arguments.length?(o="function"==typeof t?t:oK(+t),l(),f):o},f.distanceMin=function(t){return arguments.length?(u=t*t,f):Math.sqrt(u)},f.distanceMax=function(t){return arguments.length?(a=t*t,f):Math.sqrt(a)},f.theta=function(t){return arguments.length?(c=t*t,f):Math.sqrt(c)},f}function o9(t,n,e){var r,i,o,u=oK(.1);function a(t){for(var u=0,a=r.length;u<a;++u){var c=r[u],f=c.x-n||1e-6,l=c.y-e||1e-6,s=Math.sqrt(f*f+l*l),h=(o[u]-s)*i[u]*t/s;c.vx+=f*h,c.vy+=l*h}}function c(){if(r){var n,e=r.length;for(n=0,i=Array(e),o=Array(e);n<e;++n)o[n]=+t(r[n],n,r),i[n]=isNaN(o[n])?0:+u(r[n],n,r)}}return"function"!=typeof t&&(t=oK(+t)),null==n&&(n=0),null==e&&(e=0),a.initialize=function(t){r=t,c()},a.strength=function(t){return arguments.length?(u="function"==typeof t?t:oK(+t),c(),a):u},a.radius=function(n){return arguments.length?(t="function"==typeof n?n:oK(+n),c(),a):t},a.x=function(t){return arguments.length?(n=+t,a):n},a.y=function(t){return arguments.length?(e=+t,a):e},a}function ut(t){var n,e,r,i=oK(.1);function o(t){for(var i,o=0,u=n.length;o<u;++o)i=n[o],i.vx+=(r[o]-i.x)*e[o]*t}function u(){if(n){var o,u=n.length;for(o=0,e=Array(u),r=Array(u);o<u;++o)e[o]=isNaN(r[o]=+t(n[o],o,n))?0:+i(n[o],o,n)}}return"function"!=typeof t&&(t=oK(null==t?0:+t)),o.initialize=function(t){n=t,u()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:oK(+t),u(),o):i},o.x=function(n){return arguments.length?(t="function"==typeof n?n:oK(+n),u(),o):t},o}function un(t){var n,e,r,i=oK(.1);function o(t){for(var i,o=0,u=n.length;o<u;++o)i=n[o],i.vy+=(r[o]-i.y)*e[o]*t}function u(){if(n){var o,u=n.length;for(o=0,e=Array(u),r=Array(u);o<u;++o)e[o]=isNaN(r[o]=+t(n[o],o,n))?0:+i(n[o],o,n)}}return"function"!=typeof t&&(t=oK(null==t?0:+t)),o.initialize=function(t){n=t,u()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:oK(+t),u(),o):i},o.y=function(n){return arguments.length?(t="function"==typeof n?n:oK(+n),u(),o):t},o}var ue=e(28599),ur=e(2842),ui=e(42413),uo=e(69616),uu=e(99942),ua=e(8066),uc=Math.PI,uf=uc/2,ul=uc/4,us=2*uc,uh=180/uc,ud=uc/180,up=Math.abs,uv=Math.atan,ug=Math.atan2,uy=Math.cos,ub=Math.ceil,um=Math.exp,u_=Math.hypot,ux=Math.log,uw=Math.pow,uM=Math.sin,uA=Math.sign||function(t){return t>0?1:t<0?-1:0},uT=Math.sqrt,uS=Math.tan;function uk(t){return t>1?0:t<-1?uc:Math.acos(t)}function uP(t){return t>1?uf:t<-1?-uf:Math.asin(t)}function uE(){}function uC(t,n){t&&uR.hasOwnProperty(t.type)&&uR[t.type](t,n)}var uN={Feature:function(t,n){uC(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)uC(e[r].geometry,n)}},uR={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)t=e[r],n.point(t[0],t[1],t[2])},LineString:function(t,n){uO(t.coordinates,n,0)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)uO(e[r],n,0)},Polygon:function(t,n){uZ(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)uZ(e[r],n)},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)uC(e[r],n)}};function uO(t,n,e){var r,i=-1,o=t.length-e;for(n.lineStart();++i<o;)r=t[i],n.point(r[0],r[1],r[2]);n.lineEnd()}function uZ(t,n){var e=-1,r=t.length;for(n.polygonStart();++e<r;)uO(t[e],n,1);n.polygonEnd()}function uz(t,n){t&&uN.hasOwnProperty(t.type)?uN[t.type](t,n):uC(t,n)}var uB,uL,uV,uD,uG,uj,uI=new k,uq=new k,uF={point:uE,lineStart:uE,lineEnd:uE,polygonStart:function(){uI=new k,uF.lineStart=uH,uF.lineEnd=uY},polygonEnd:function(){var t=+uI;uq.add(t<0?us+t:t),this.lineStart=this.lineEnd=this.point=uE},sphere:function(){uq.add(us)}};function uH(){uF.point=u$}function uY(){uX(uL,uV)}function u$(t,n){uF.point=uX,uL=t,uV=n,t*=ud,n*=ud,uD=t,uG=uy(n=n/2+ul),uj=uM(n)}function uX(t,n){t*=ud,n*=ud;var e=t-uD,r=e>=0?1:-1,i=r*e,o=uy(n=n/2+ul),u=uM(n),a=uj*u,c=uG*o+a*uy(i),f=a*r*uM(i);uI.add(ug(f,c)),uD=t,uG=o,uj=u}function uW(t){return uq=new k,uz(t,uF),2*uq}function uK(t){return[ug(t[1],t[0]),uP(t[2])]}function uU(t){var n=t[0],e=t[1],r=uy(e);return[r*uy(n),r*uM(n),uM(e)]}function uJ(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function uQ(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function u0(t,n){t[0]+=n[0],t[1]+=n[1],t[2]+=n[2]}function u1(t,n){return[t[0]*n,t[1]*n,t[2]*n]}function u2(t){var n=uT(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=n,t[1]/=n,t[2]/=n}var u6={point:u8,lineStart:u4,lineEnd:u5,polygonStart:function(){u6.point=u7,u6.lineStart=u9,u6.lineEnd=at,ca=new k,uF.polygonStart()},polygonEnd:function(){uF.polygonEnd(),u6.point=u8,u6.lineStart=u4,u6.lineEnd=u5,uI<0?(a9=-(cn=180),ct=-(ce=90)):ca>1e-6?ce=90:ca<-.000001&&(ct=-90),cf[0]=a9,cf[1]=cn},sphere:function(){a9=-(cn=180),ct=-(ce=90)}};function u8(t,n){cc.push(cf=[a9=t,cn=t]),n<ct&&(ct=n),n>ce&&(ce=n)}function u3(t,n){var e=uU([t*ud,n*ud]);if(cu){var r=uQ(cu,e),i=uQ([r[1],-r[0],0],r);u2(i),i=uK(i);var o,u=t-cr,a=u>0?1:-1,c=i[0]*uh*a,f=up(u)>180;f^(a*cr<c&&c<a*t)?(o=i[1]*uh)>ce&&(ce=o):f^(a*cr<(c=(c+360)%360-180)&&c<a*t)?(o=-i[1]*uh)<ct&&(ct=o):(n<ct&&(ct=n),n>ce&&(ce=n)),f?t<cr?an(a9,t)>an(a9,cn)&&(cn=t):an(t,cn)>an(a9,cn)&&(a9=t):cn>=a9?(t<a9&&(a9=t),t>cn&&(cn=t)):t>cr?an(a9,t)>an(a9,cn)&&(cn=t):an(t,cn)>an(a9,cn)&&(a9=t)}else cc.push(cf=[a9=t,cn=t]);n<ct&&(ct=n),n>ce&&(ce=n),cu=e,cr=t}function u4(){u6.point=u3}function u5(){cf[0]=a9,cf[1]=cn,u6.point=u8,cu=null}function u7(t,n){if(cu){var e=t-cr;ca.add(up(e)>180?e+(e>0?360:-360):e)}else ci=t,co=n;uF.point(t,n),u3(t,n)}function u9(){uF.lineStart()}function at(){u7(ci,co),uF.lineEnd(),up(ca)>1e-6&&(a9=-(cn=180)),cf[0]=a9,cf[1]=cn,cu=null}function an(t,n){return(n-=t)<0?n+360:n}function ae(t,n){return t[0]-n[0]}function ar(t,n){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}function ai(t){var n,e,r,i,o,u,a;if(ce=cn=-(a9=ct=1/0),cc=[],uz(t,u6),e=cc.length){for(cc.sort(ae),n=1,o=[r=cc[0]];n<e;++n)ar(r,(i=cc[n])[0])||ar(r,i[1])?(an(r[0],i[1])>an(r[0],r[1])&&(r[1]=i[1]),an(i[0],r[1])>an(r[0],r[1])&&(r[0]=i[0])):o.push(r=i);for(u=-1/0,e=o.length-1,n=0,r=o[e];n<=e;r=i,++n)i=o[n],(a=an(r[1],i[0]))>u&&(u=a,a9=i[0],cn=r[1])}return cc=cf=null,a9===1/0||ct===1/0?[[NaN,NaN],[NaN,NaN]]:[[a9,ct],[cn,ce]]}var ao={sphere:uE,point:au,lineStart:ac,lineEnd:as,polygonStart:function(){ao.lineStart=ah,ao.lineEnd=ad},polygonEnd:function(){ao.lineStart=ac,ao.lineEnd=as}};function au(t,n){t*=ud;var e=uy(n*=ud);aa(e*uy(t),e*uM(t),uM(n))}function aa(t,n,e){++cl,ch+=(t-ch)/cl,cd+=(n-cd)/cl,cp+=(e-cp)/cl}function ac(){ao.point=af}function af(t,n){t*=ud;var e=uy(n*=ud);cM=e*uy(t),cA=e*uM(t),cT=uM(n),ao.point=al,aa(cM,cA,cT)}function al(t,n){t*=ud;var e=uy(n*=ud),r=e*uy(t),i=e*uM(t),o=uM(n),u=ug(uT((u=cA*o-cT*i)*u+(u=cT*r-cM*o)*u+(u=cM*i-cA*r)*u),cM*r+cA*i+cT*o);cs+=u,cv+=u*(cM+(cM=r)),cg+=u*(cA+(cA=i)),cy+=u*(cT+(cT=o)),aa(cM,cA,cT)}function as(){ao.point=au}function ah(){ao.point=ap}function ad(){av(cx,cw),ao.point=au}function ap(t,n){cx=t,cw=n,t*=ud,n*=ud,ao.point=av;var e=uy(n);cM=e*uy(t),cA=e*uM(t),cT=uM(n),aa(cM,cA,cT)}function av(t,n){t*=ud;var e=uy(n*=ud),r=e*uy(t),i=e*uM(t),o=uM(n),u=cA*o-cT*i,a=cT*r-cM*o,c=cM*i-cA*r,f=u_(u,a,c),l=uP(f),s=f&&-l/f;cb.add(s*u),cm.add(s*a),c_.add(s*c),cs+=l,cv+=l*(cM+(cM=r)),cg+=l*(cA+(cA=i)),cy+=l*(cT+(cT=o)),aa(cM,cA,cT)}function ag(t){cl=cs=ch=cd=cp=cv=cg=cy=0,cb=new k,cm=new k,c_=new k,uz(t,ao);var n=+cb,e=+cm,r=+c_,i=u_(n,e,r);return i<1e-12&&(n=cv,e=cg,r=cy,cs<1e-6&&(n=ch,e=cd,r=cp),(i=u_(n,e,r))<1e-12)?[NaN,NaN]:[ug(e,n)*uh,uP(r/i)*uh]}function ay(t){return function(){return t}}function ab(t,n){function e(e,r){return n((e=t(e,r))[0],e[1])}return t.invert&&n.invert&&(e.invert=function(e,r){return(e=n.invert(e,r))&&t.invert(e[0],e[1])}),e}function am(t,n){return up(t)>uc&&(t-=Math.round(t/us)*us),[t,n]}function a_(t,n,e){return(t%=us)?n||e?ab(aw(t),aM(n,e)):aw(t):n||e?aM(n,e):am}function ax(t){return function(n,e){return up(n+=t)>uc&&(n-=Math.round(n/us)*us),[n,e]}}function aw(t){var n=ax(t);return n.invert=ax(-t),n}function aM(t,n){var e=uy(t),r=uM(t),i=uy(n),o=uM(n);function u(t,n){var u=uy(n),a=uy(t)*u,c=uM(t)*u,f=uM(n),l=f*e+a*r;return[ug(c*i-l*o,a*e-f*r),uP(l*i+c*o)]}return u.invert=function(t,n){var u=uy(n),a=uy(t)*u,c=uM(t)*u,f=uM(n),l=f*i-c*o;return[ug(c*i+f*o,a*e+l*r),uP(l*e-a*r)]},u}function aA(t){function n(n){return n=t(n[0]*ud,n[1]*ud),n[0]*=uh,n[1]*=uh,n}return t=a_(t[0]*ud,t[1]*ud,t.length>2?t[2]*ud:0),n.invert=function(n){return n=t.invert(n[0]*ud,n[1]*ud),n[0]*=uh,n[1]*=uh,n},n}function aT(t,n,e,r,i,o){if(e){var u=uy(n),a=uM(n),c=r*e;null==i?(i=n+r*us,o=n-c/2):(i=aS(u,i),o=aS(u,o),(r>0?i<o:i>o)&&(i+=r*us));for(var f,l=i;r>0?l>o:l<o;l-=c)f=uK([u,-a*uy(l),-a*uM(l)]),t.point(f[0],f[1])}}function aS(t,n){n=uU(n),n[0]-=t,u2(n);var e=uk(-n[1]);return((0>-n[2]?-e:e)+us-1e-6)%us}function ak(){var t,n,e=ay([0,0]),r=ay(90),i=ay(2),o={point:function(e,r){t.push(e=n(e,r)),e[0]*=uh,e[1]*=uh}};function u(){var u=e.apply(this,arguments),a=r.apply(this,arguments)*ud,c=i.apply(this,arguments)*ud;return t=[],n=a_(-u[0]*ud,-u[1]*ud,0).invert,aT(o,a,c,1),u={type:"Polygon",coordinates:[t]},t=n=null,u}return u.center=function(t){return arguments.length?(e="function"==typeof t?t:ay([+t[0],+t[1]]),u):e},u.radius=function(t){return arguments.length?(r="function"==typeof t?t:ay(+t),u):r},u.precision=function(t){return arguments.length?(i="function"==typeof t?t:ay(+t),u):i},u}function aP(){var t,n=[];return{point:function(n,e,r){t.push([n,e,r])},lineStart:function(){n.push(t=[])},lineEnd:uE,rejoin:function(){n.length>1&&n.push(n.pop().concat(n.shift()))},result:function(){var e=n;return n=[],t=null,e}}}function aE(t,n){return 1e-6>up(t[0]-n[0])&&1e-6>up(t[1]-n[1])}function aC(t,n,e,r){this.x=t,this.z=n,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}function aN(t,n,e,r,i){var o,u,a=[],c=[];if(t.forEach(function(t){if(!((n=t.length-1)<=0)){var n,e,r=t[0],u=t[n];if(aE(r,u)){if(!r[2]&&!u[2]){for(i.lineStart(),o=0;o<n;++o)i.point((r=t[o])[0],r[1]);i.lineEnd();return}u[0]+=2e-6}a.push(e=new aC(r,t,null,!0)),c.push(e.o=new aC(r,null,e,!1)),a.push(e=new aC(u,t,null,!1)),c.push(e.o=new aC(u,null,e,!0))}}),a.length){for(c.sort(n),aR(a),aR(c),o=0,u=c.length;o<u;++o)c[o].e=e=!e;for(var f,l,s=a[0];;){for(var h=s,d=!0;h.v;)if((h=h.n)===s)return;f=h.z,i.lineStart();do{if(h.v=h.o.v=!0,h.e){if(d)for(o=0,u=f.length;o<u;++o)i.point((l=f[o])[0],l[1]);else r(h.x,h.n.x,1,i);h=h.n}else{if(d)for(o=(f=h.p.z).length-1;o>=0;--o)i.point((l=f[o])[0],l[1]);else r(h.x,h.p.x,-1,i);h=h.p}f=(h=h.o).z,d=!d}while(!h.v);i.lineEnd()}}}function aR(t){if(n=t.length){for(var n,e,r=0,i=t[0];++r<n;)i.n=e=t[r],e.p=i,i=e;i.n=e=t[0],e.p=i}}function aO(t){return up(t[0])<=uc?t[0]:uA(t[0])*((up(t[0])+uc)%us-uc)}function aZ(t,n){var e=aO(n),r=n[1],i=uM(r),o=[uM(e),-uy(e),0],u=0,a=0,c=new k;1===i?r=uf+1e-6:-1===i&&(r=-uf-1e-6);for(var f=0,l=t.length;f<l;++f)if(h=(s=t[f]).length)for(var s,h,d=s[h-1],p=aO(d),v=d[1]/2+ul,g=uM(v),y=uy(v),b=0;b<h;++b,p=_,g=w,y=M,d=m){var m=s[b],_=aO(m),x=m[1]/2+ul,w=uM(x),M=uy(x),A=_-p,T=A>=0?1:-1,S=T*A,P=S>uc,E=g*w;if(c.add(ug(E*T*uM(S),y*M+E*uy(S))),u+=P?A+T*us:A,P^p>=e^_>=e){var C=uQ(uU(d),uU(m));u2(C);var N=uQ(o,C);u2(N);var R=(P^A>=0?-1:1)*uP(N[2]);(r>R||r===R&&(C[0]||C[1]))&&(a+=P^A>=0?1:-1)}}return(u<-.000001||u<1e-6&&c<-.000000000001)^1&a}function az(t,n,e,r){return function(i){var o,u,a,c=n(i),f=aP(),l=n(f),s=!1,h={point:d,lineStart:v,lineEnd:g,polygonStart:function(){h.point=y,h.lineStart=b,h.lineEnd=m,u=[],o=[]},polygonEnd:function(){h.point=d,h.lineStart=v,h.lineEnd=g,u=to(u);var t=aZ(o,r);u.length?(s||(i.polygonStart(),s=!0),aN(u,aL,t,e,i)):t&&(s||(i.polygonStart(),s=!0),i.lineStart(),e(null,null,1,i),i.lineEnd()),s&&(i.polygonEnd(),s=!1),u=o=null},sphere:function(){i.polygonStart(),i.lineStart(),e(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function d(n,e){t(n,e)&&i.point(n,e)}function p(t,n){c.point(t,n)}function v(){h.point=p,c.lineStart()}function g(){h.point=d,c.lineEnd()}function y(t,n){a.push([t,n]),l.point(t,n)}function b(){l.lineStart(),a=[]}function m(){y(a[0][0],a[0][1]),l.lineEnd();var t,n,e,r,c=l.clean(),h=f.result(),d=h.length;if(a.pop(),o.push(a),a=null,d){if(1&c){if((n=(e=h[0]).length-1)>0){for(s||(i.polygonStart(),s=!0),i.lineStart(),t=0;t<n;++t)i.point((r=e[t])[0],r[1]);i.lineEnd()}return}d>1&&2&c&&h.push(h.pop().concat(h.shift())),u.push(h.filter(aB))}}return h}}function aB(t){return t.length>1}function aL(t,n){return((t=t.x)[0]<0?t[1]-uf-1e-6:uf-t[1])-((n=n.x)[0]<0?n[1]-uf-1e-6:uf-n[1])}am.invert=am;var aV=az(function(){return!0},function(t){var n,e=NaN,r=NaN,i=NaN;return{lineStart:function(){t.lineStart(),n=1},point:function(o,u){var a,c,f,l,s,h,d=o>0?uc:-uc,p=up(o-e);1e-6>up(p-uc)?(t.point(e,r=(r+u)/2>0?uf:-uf),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(d,r),t.point(o,r),n=0):i!==d&&p>=uc&&(1e-6>up(e-i)&&(e-=1e-6*i),1e-6>up(o-d)&&(o-=1e-6*d),a=e,c=r,r=up(h=uM(a-(f=o)))>1e-6?uv((uM(c)*(s=uy(u))*uM(f)-uM(u)*(l=uy(c))*uM(a))/(l*s*h)):(c+u)/2,t.point(i,r),t.lineEnd(),t.lineStart(),t.point(d,r),n=0),t.point(e=o,r=u),i=d},lineEnd:function(){t.lineEnd(),e=r=NaN},clean:function(){return 2-n}}},function(t,n,e,r){var i;if(null==t)i=e*uf,r.point(-uc,i),r.point(0,i),r.point(uc,i),r.point(uc,0),r.point(uc,-i),r.point(0,-i),r.point(-uc,-i),r.point(-uc,0),r.point(-uc,i);else if(up(t[0]-n[0])>1e-6){var o=t[0]<n[0]?uc:-uc;i=e*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(n[0],n[1])},[-uc,-uf]);function aD(t){var n=uy(t),e=2*ud,r=n>0,i=up(n)>1e-6;function o(t,e){return uy(t)*uy(e)>n}function u(t,e,r){var i=uU(t),o=uU(e),u=[1,0,0],a=uQ(i,o),c=uJ(a,a),f=a[0],l=c-f*f;if(!l)return!r&&t;var s=uQ(u,a),h=u1(u,n*c/l);u0(h,u1(a,-n*f/l));var d=uJ(h,s),p=uJ(s,s),v=d*d-p*(uJ(h,h)-1);if(!(v<0)){var g=uT(v),y=u1(s,(-d-g)/p);if(u0(y,h),y=uK(y),!r)return y;var b,m=t[0],_=e[0],x=t[1],w=e[1];_<m&&(b=m,m=_,_=b);var M=_-m,A=1e-6>up(M-uc);if(!A&&w<x&&(b=x,x=w,w=b),A||M<1e-6?A?x+w>0^y[1]<(1e-6>up(y[0]-m)?x:w):x<=y[1]&&y[1]<=w:M>uc^(m<=y[0]&&y[0]<=_)){var T=u1(s,(-d+g)/p);return u0(T,h),[y,uK(T)]}}}function a(n,e){var i=r?t:uc-t,o=0;return n<-i?o|=1:n>i&&(o|=2),e<-i?o|=4:e>i&&(o|=8),o}return az(o,function(t){var n,e,c,f,l;return{lineStart:function(){f=c=!1,l=1},point:function(s,h){var d,p,v=[s,h],g=o(s,h),y=r?g?0:a(s,h):g?a(s+(s<0?uc:-uc),h):0;!n&&(f=c=g)&&t.lineStart(),g!==c&&(!(p=u(n,v))||aE(n,p)||aE(v,p))&&(v[2]=1),g!==c?(l=0,g?(t.lineStart(),p=u(v,n),t.point(p[0],p[1])):(p=u(n,v),t.point(p[0],p[1],2),t.lineEnd()),n=p):i&&n&&r^g&&!(y&e)&&(d=u(v,n,!0))&&(l=0,r?(t.lineStart(),t.point(d[0][0],d[0][1]),t.point(d[1][0],d[1][1]),t.lineEnd()):(t.point(d[1][0],d[1][1]),t.lineEnd(),t.lineStart(),t.point(d[0][0],d[0][1],3))),!g||n&&aE(n,v)||t.point(v[0],v[1]),n=v,c=g,e=y},lineEnd:function(){c&&t.lineEnd(),n=null},clean:function(){return l|(f&&c)<<1}}},function(n,r,i,o){aT(o,t,e,i,n,r)},r?[0,-t]:[-uc,t-uc])}function aG(t,n,e,r){function i(i,o){return t<=i&&i<=e&&n<=o&&o<=r}function o(i,o,a,f){var l=0,s=0;if(null==i||(l=u(i,a))!==(s=u(o,a))||0>c(i,o)^a>0)do f.point(0===l||3===l?t:e,l>1?r:n);while((l=(l+a+4)%4)!==s);else f.point(o[0],o[1])}function u(r,i){return 1e-6>up(r[0]-t)?i>0?0:3:1e-6>up(r[0]-e)?i>0?2:1:1e-6>up(r[1]-n)?i>0?1:0:i>0?3:2}function a(t,n){return c(t.x,n.x)}function c(t,n){var e=u(t,1),r=u(n,1);return e!==r?e-r:0===e?n[1]-t[1]:1===e?t[0]-n[0]:2===e?t[1]-n[1]:n[0]-t[0]}return function(u){var c,f,l,s,h,d,p,v,g,y,b,m=u,_=aP(),x={point:w,lineStart:function(){x.point=M,f&&f.push(l=[]),y=!0,g=!1,p=v=NaN},lineEnd:function(){c&&(M(s,h),d&&g&&_.rejoin(),c.push(_.result())),x.point=w,g&&m.lineEnd()},polygonStart:function(){m=_,c=[],f=[],b=!0},polygonEnd:function(){var n=function(){for(var n=0,e=0,i=f.length;e<i;++e)for(var o,u,a=f[e],c=1,l=a.length,s=a[0],h=s[0],d=s[1];c<l;++c)o=h,u=d,h=(s=a[c])[0],d=s[1],u<=r?d>r&&(h-o)*(r-u)>(d-u)*(t-o)&&++n:d<=r&&(h-o)*(r-u)<(d-u)*(t-o)&&--n;return n}(),e=b&&n,i=(c=to(c)).length;(e||i)&&(u.polygonStart(),e&&(u.lineStart(),o(null,null,1,u),u.lineEnd()),i&&aN(c,a,n,o,u),u.polygonEnd()),m=u,c=f=l=null}};function w(t,n){i(t,n)&&m.point(t,n)}function M(o,u){var a=i(o,u);if(f&&l.push([o,u]),y)s=o,h=u,d=a,y=!1,a&&(m.lineStart(),m.point(o,u));else if(a&&g)m.point(o,u);else{var c=[p=Math.max(-1e9,Math.min(1e9,p)),v=Math.max(-1e9,Math.min(1e9,v))],_=[o=Math.max(-1e9,Math.min(1e9,o)),u=Math.max(-1e9,Math.min(1e9,u))];!function(t,n,e,r,i,o){var u,a=t[0],c=t[1],f=n[0],l=n[1],s=0,h=1,d=f-a,p=l-c;if(u=e-a,d||!(u>0)){if(u/=d,d<0){if(u<s)return;u<h&&(h=u)}else if(d>0){if(u>h)return;u>s&&(s=u)}if(u=i-a,d||!(u<0)){if(u/=d,d<0){if(u>h)return;u>s&&(s=u)}else if(d>0){if(u<s)return;u<h&&(h=u)}if(u=r-c,p||!(u>0)){if(u/=p,p<0){if(u<s)return;u<h&&(h=u)}else if(p>0){if(u>h)return;u>s&&(s=u)}if(u=o-c,p||!(u<0)){if(u/=p,p<0){if(u>h)return;u>s&&(s=u)}else if(p>0){if(u<s)return;u<h&&(h=u)}return s>0&&(t[0]=a+s*d,t[1]=c+s*p),h<1&&(n[0]=a+h*d,n[1]=c+h*p),!0}}}}}(c,_,t,n,e,r)?a&&(m.lineStart(),m.point(o,u),b=!1):(g||(m.lineStart(),m.point(c[0],c[1])),m.point(_[0],_[1]),a||m.lineEnd(),b=!1)}p=o,v=u,g=a}return x}}function aj(){var t,n,e,r=0,i=0,o=960,u=500;return e={stream:function(e){return t&&n===e?t:t=aG(r,i,o,u)(n=e)},extent:function(a){return arguments.length?(r=+a[0][0],i=+a[0][1],o=+a[1][0],u=+a[1][1],t=n=null,e):[[r,i],[o,u]]}}}var aI={sphere:uE,point:uE,lineStart:function(){aI.point=aF,aI.lineEnd=aq},lineEnd:uE,polygonStart:uE,polygonEnd:uE};function aq(){aI.point=aI.lineEnd=uE}function aF(t,n){t*=ud,n*=ud,ck=t,cP=uM(n),cE=uy(n),aI.point=aH}function aH(t,n){t*=ud;var e=uM(n*=ud),r=uy(n),i=up(t-ck),o=uy(i),u=r*uM(i),a=cE*e-cP*r*o,c=cP*e+cE*r*o;cS.add(ug(uT(u*u+a*a),c)),ck=t,cP=e,cE=r}function aY(t){return cS=new k,uz(t,aI),+cS}var a$=[null,null],aX={type:"LineString",coordinates:a$};function aW(t,n){return a$[0]=t,a$[1]=n,aY(aX)}var aK={Feature:function(t,n){return aJ(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)if(aJ(e[r].geometry,n))return!0;return!1}},aU={Sphere:function(){return!0},Point:function(t,n){return 0===aW(t.coordinates,n)},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(0===aW(e[r],n))return!0;return!1},LineString:function(t,n){return aQ(t.coordinates,n)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(aQ(e[r],n))return!0;return!1},Polygon:function(t,n){return a0(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(a0(e[r],n))return!0;return!1},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)if(aJ(e[r],n))return!0;return!1}};function aJ(t,n){return!!(t&&aU.hasOwnProperty(t.type))&&aU[t.type](t,n)}function aQ(t,n){for(var e,r,i,o=0,u=t.length;o<u;o++){if(0===(r=aW(t[o],n))||o>0&&(i=aW(t[o],t[o-1]))>0&&e<=i&&r<=i&&(e+r-i)*(1-Math.pow((e-r)/i,2))<1e-12*i)return!0;e=r}return!1}function a0(t,n){return!!aZ(t.map(a1),a2(n))}function a1(t){return(t=t.map(a2)).pop(),t}function a2(t){return[t[0]*ud,t[1]*ud]}function a6(t,n){return(t&&aK.hasOwnProperty(t.type)?aK[t.type]:aJ)(t,n)}function a8(t,n,e){var r=(0,td.Z)(t,n-1e-6,e).concat(n);return function(t){return r.map(function(n){return[t,n]})}}function a3(t,n,e){var r=(0,td.Z)(t,n-1e-6,e).concat(n);return function(t){return r.map(function(n){return[n,t]})}}function a4(){var t,n,e,r,i,o,u,a,c,f,l,s,h=10,d=10,p=90,v=360,g=2.5;function y(){return{type:"MultiLineString",coordinates:b()}}function b(){return(0,td.Z)(ub(r/p)*p,e,p).map(l).concat((0,td.Z)(ub(a/v)*v,u,v).map(s)).concat((0,td.Z)(ub(n/h)*h,t,h).filter(function(t){return up(t%p)>1e-6}).map(c)).concat((0,td.Z)(ub(o/d)*d,i,d).filter(function(t){return up(t%v)>1e-6}).map(f))}return y.lines=function(){return b().map(function(t){return{type:"LineString",coordinates:t}})},y.outline=function(){return{type:"Polygon",coordinates:[l(r).concat(s(u).slice(1),l(e).reverse().slice(1),s(a).reverse().slice(1))]}},y.extent=function(t){return arguments.length?y.extentMajor(t).extentMinor(t):y.extentMinor()},y.extentMajor=function(t){return arguments.length?(r=+t[0][0],e=+t[1][0],a=+t[0][1],u=+t[1][1],r>e&&(t=r,r=e,e=t),a>u&&(t=a,a=u,u=t),y.precision(g)):[[r,a],[e,u]]},y.extentMinor=function(e){return arguments.length?(n=+e[0][0],t=+e[1][0],o=+e[0][1],i=+e[1][1],n>t&&(e=n,n=t,t=e),o>i&&(e=o,o=i,i=e),y.precision(g)):[[n,o],[t,i]]},y.step=function(t){return arguments.length?y.stepMajor(t).stepMinor(t):y.stepMinor()},y.stepMajor=function(t){return arguments.length?(p=+t[0],v=+t[1],y):[p,v]},y.stepMinor=function(t){return arguments.length?(h=+t[0],d=+t[1],y):[h,d]},y.precision=function(h){return arguments.length?(g=+h,c=a8(o,i,90),f=a3(n,t,g),l=a8(a,u,90),s=a3(r,e,g),y):g},y.extentMajor([[-180,-89.999999],[180,89.999999]]).extentMinor([[-180,-80.000001],[180,80.000001]])}function a5(){return a4()()}function a7(t,n){var e,r,i=t[0]*ud,o=t[1]*ud,u=n[0]*ud,a=n[1]*ud,c=uy(o),f=uM(o),l=uy(a),s=uM(a),h=c*uy(i),d=c*uM(i),p=l*uy(u),v=l*uM(u),g=2*uP(uT((e=uM((e=a-o)/2))*e+c*l*((r=uM((r=u-i)/2))*r))),y=uM(g),b=g?function(t){var n=uM(t*=g)/y,e=uM(g-t)/y,r=e*h+n*p,i=e*d+n*v;return[ug(i,r)*uh,ug(e*f+n*s,uT(r*r+i*i))*uh]}:function(){return[i*uh,o*uh]};return b.distance=g,b}var a9,ct,cn,ce,cr,ci,co,cu,ca,cc,cf,cl,cs,ch,cd,cp,cv,cg,cy,cb,cm,c_,cx,cw,cM,cA,cT,cS,ck,cP,cE,cC,cN,cR,cO,cZ=t=>t,cz=new k,cB=new k,cL={point:uE,lineStart:uE,lineEnd:uE,polygonStart:function(){cL.lineStart=cV,cL.lineEnd=cj},polygonEnd:function(){cL.lineStart=cL.lineEnd=cL.point=uE,cz.add(up(cB)),cB=new k},result:function(){var t=cz/2;return cz=new k,t}};function cV(){cL.point=cD}function cD(t,n){cL.point=cG,cC=cR=t,cN=cO=n}function cG(t,n){cB.add(cO*t-cR*n),cR=t,cO=n}function cj(){cG(cC,cN)}var cI,cq,cF,cH,cY=1/0,c$=1/0,cX=-1/0,cW=cX,cK={point:function(t,n){t<cY&&(cY=t),t>cX&&(cX=t),n<c$&&(c$=n),n>cW&&(cW=n)},lineStart:uE,lineEnd:uE,polygonStart:uE,polygonEnd:uE,result:function(){var t=[[cY,c$],[cX,cW]];return cX=cW=-(c$=cY=1/0),t}},cU=0,cJ=0,cQ=0,c0=0,c1=0,c2=0,c6=0,c8=0,c3=0,c4={point:c5,lineStart:c7,lineEnd:fn,polygonStart:function(){c4.lineStart=fe,c4.lineEnd=fr},polygonEnd:function(){c4.point=c5,c4.lineStart=c7,c4.lineEnd=fn},result:function(){var t=c3?[c6/c3,c8/c3]:c2?[c0/c2,c1/c2]:cQ?[cU/cQ,cJ/cQ]:[NaN,NaN];return cU=cJ=cQ=c0=c1=c2=c6=c8=c3=0,t}};function c5(t,n){cU+=t,cJ+=n,++cQ}function c7(){c4.point=c9}function c9(t,n){c4.point=ft,c5(cF=t,cH=n)}function ft(t,n){var e=t-cF,r=n-cH,i=uT(e*e+r*r);c0+=i*(cF+t)/2,c1+=i*(cH+n)/2,c2+=i,c5(cF=t,cH=n)}function fn(){c4.point=c5}function fe(){c4.point=fi}function fr(){fo(cI,cq)}function fi(t,n){c4.point=fo,c5(cI=cF=t,cq=cH=n)}function fo(t,n){var e=t-cF,r=n-cH,i=uT(e*e+r*r);c0+=i*(cF+t)/2,c1+=i*(cH+n)/2,c2+=i,c6+=(i=cH*t-cF*n)*(cF+t),c8+=i*(cH+n),c3+=3*i,c5(cF=t,cH=n)}function fu(t){this._context=t}fu.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,n){switch(this._point){case 0:this._context.moveTo(t,n),this._point=1;break;case 1:this._context.lineTo(t,n);break;default:this._context.moveTo(t+this._radius,n),this._context.arc(t,n,this._radius,0,us)}},result:uE};var fa,fc,ff,fl,fs,fh=new k,fd={point:uE,lineStart:function(){fd.point=fp},lineEnd:function(){fa&&fv(fc,ff),fd.point=uE},polygonStart:function(){fa=!0},polygonEnd:function(){fa=null},result:function(){var t=+fh;return fh=new k,t}};function fp(t,n){fd.point=fv,fc=fl=t,ff=fs=n}function fv(t,n){fl-=t,fs-=n,fh.add(uT(fl*fl+fs*fs)),fl=t,fs=n}class fg{constructor(t){this._append=null==t?fy:function(t){let n=Math.floor(t);if(!(n>=0))throw RangeError(`invalid digits: ${t}`);if(n>15)return fy;if(n!==r){let t=10**n;r=n,i=function(n){let e=1;this._+=n[0];for(let r=n.length;e<r;++e)this._+=Math.round(arguments[e]*t)/t+n[e]}}return i}(t),this._radius=4.5,this._=""}pointRadius(t){return this._radius=+t,this}polygonStart(){this._line=0}polygonEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){0===this._line&&(this._+="Z"),this._point=NaN}point(t,n){switch(this._point){case 0:this._append`M${t},${n}`,this._point=1;break;case 1:this._append`L${t},${n}`;break;default:if(this._append`M${t},${n}`,this._radius!==o||this._append!==i){let t=this._radius,n=this._;this._="",this._append`m0,${t}a${t},${t} 0 1,1 0,${-2*t}a${t},${t} 0 1,1 0,${2*t}z`,o=t,i=this._append,u=this._,this._=n}this._+=u}}result(){let t=this._;return this._="",t.length?t:null}}function fy(t){let n=1;this._+=t[0];for(let e=t.length;n<e;++n)this._+=arguments[n]+t[n]}function fb(t,n){let e=3,r=4.5,i,o;function u(t){return t&&("function"==typeof r&&o.pointRadius(+r.apply(this,arguments)),uz(t,i(o))),o.result()}return u.area=function(t){return uz(t,i(cL)),cL.result()},u.measure=function(t){return uz(t,i(fd)),fd.result()},u.bounds=function(t){return uz(t,i(cK)),cK.result()},u.centroid=function(t){return uz(t,i(c4)),c4.result()},u.projection=function(n){return arguments.length?(i=null==n?(t=null,cZ):(t=n).stream,u):t},u.context=function(t){return arguments.length?(o=null==t?(n=null,new fg(e)):new fu(n=t),"function"!=typeof r&&o.pointRadius(r),u):n},u.pointRadius=function(t){return arguments.length?(r="function"==typeof t?t:(o.pointRadius(+t),+t),u):r},u.digits=function(t){if(!arguments.length)return e;if(null==t)e=null;else{let n=Math.floor(t);if(!(n>=0))throw RangeError(`invalid digits: ${t}`);e=n}return null===n&&(o=new fg(e)),u},u.projection(t).digits(e).context(n)}function fm(t){return{stream:f_(t)}}function f_(t){return function(n){var e=new fx;for(var r in t)e[r]=t[r];return e.stream=n,e}}function fx(){}function fw(t,n,e){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=r&&t.clipExtent(null),uz(e,t.stream(cK)),n(cK.result()),null!=r&&t.clipExtent(r),t}function fM(t,n,e){return fw(t,function(e){var r=n[1][0]-n[0][0],i=n[1][1]-n[0][1],o=Math.min(r/(e[1][0]-e[0][0]),i/(e[1][1]-e[0][1])),u=+n[0][0]+(r-o*(e[1][0]+e[0][0]))/2,a=+n[0][1]+(i-o*(e[1][1]+e[0][1]))/2;t.scale(150*o).translate([u,a])},e)}function fA(t,n,e){return fM(t,[[0,0],n],e)}function fT(t,n,e){return fw(t,function(e){var r=+n,i=r/(e[1][0]-e[0][0]),o=(r-i*(e[1][0]+e[0][0]))/2,u=-i*e[0][1];t.scale(150*i).translate([o,u])},e)}function fS(t,n,e){return fw(t,function(e){var r=+n,i=r/(e[1][1]-e[0][1]),o=-i*e[0][0],u=(r-i*(e[1][1]+e[0][1]))/2;t.scale(150*i).translate([o,u])},e)}fx.prototype={constructor:fx,point:function(t,n){this.stream.point(t,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var fk=uy(30*ud);function fP(t,n){return+n?function(t,n){function e(r,i,o,u,a,c,f,l,s,h,d,p,v,g){var y=f-r,b=l-i,m=y*y+b*b;if(m>4*n&&v--){var _=u+h,x=a+d,w=c+p,M=uT(_*_+x*x+w*w),A=uP(w/=M),T=1e-6>up(up(w)-1)||1e-6>up(o-s)?(o+s)/2:ug(x,_),S=t(T,A),k=S[0],P=S[1],E=k-r,C=P-i,N=b*E-y*C;(N*N/m>n||up((y*E+b*C)/m-.5)>.3||u*h+a*d+c*p<fk)&&(e(r,i,o,u,a,c,k,P,T,_/=M,x/=M,w,v,g),g.point(k,P),e(k,P,T,_,x,w,f,l,s,h,d,p,v,g))}}return function(n){var r,i,o,u,a,c,f,l,s,h,d,p,v={point:g,lineStart:y,lineEnd:m,polygonStart:function(){n.polygonStart(),v.lineStart=_},polygonEnd:function(){n.polygonEnd(),v.lineStart=y}};function g(e,r){e=t(e,r),n.point(e[0],e[1])}function y(){l=NaN,v.point=b,n.lineStart()}function b(r,i){var o=uU([r,i]),u=t(r,i);e(l,s,f,h,d,p,l=u[0],s=u[1],f=r,h=o[0],d=o[1],p=o[2],16,n),n.point(l,s)}function m(){v.point=g,n.lineEnd()}function _(){y(),v.point=x,v.lineEnd=w}function x(t,n){b(r=t,n),i=l,o=s,u=h,a=d,c=p,v.point=b}function w(){e(l,s,f,h,d,p,i,o,r,u,a,c,16,n),v.lineEnd=m,m()}return v}}(t,n):f_({point:function(n,e){n=t(n,e),this.stream.point(n[0],n[1])}})}var fE=f_({point:function(t,n){this.stream.point(t*ud,n*ud)}});function fC(t,n,e,r,i,o){if(!o)return function(t,n,e,r,i){function o(o,u){return[n+t*(o*=r),e-t*(u*=i)]}return o.invert=function(o,u){return[(o-n)/t*r,(e-u)/t*i]},o}(t,n,e,r,i);var u=uy(o),a=uM(o),c=u*t,f=a*t,l=u/t,s=a/t,h=(a*e-u*n)/t,d=(a*n+u*e)/t;function p(t,o){return[c*(t*=r)-f*(o*=i)+n,e-f*t-c*o]}return p.invert=function(t,n){return[r*(l*t-s*n+h),i*(d-s*t-l*n)]},p}function fN(t){return fR(function(){return t})()}function fR(t){var n,e,r,i,o,u,a,c,f,l,s=150,h=480,d=250,p=0,v=0,g=0,y=0,b=0,m=0,_=1,x=1,w=null,M=aV,A=null,T=cZ,S=.5;function k(t){return c(t[0]*ud,t[1]*ud)}function P(t){return(t=c.invert(t[0],t[1]))&&[t[0]*uh,t[1]*uh]}function E(){var t=fC(s,0,0,_,x,m).apply(null,n(p,v)),r=fC(s,h-t[0],d-t[1],_,x,m);return e=a_(g,y,b),a=ab(n,r),c=ab(e,a),u=fP(a,S),C()}function C(){return f=l=null,k}return k.stream=function(t){var n;return f&&l===t?f:f=fE((n=e,f_({point:function(t,e){var r=n(t,e);return this.stream.point(r[0],r[1])}}))(M(u(T(l=t)))))},k.preclip=function(t){return arguments.length?(M=t,w=void 0,C()):M},k.postclip=function(t){return arguments.length?(T=t,A=r=i=o=null,C()):T},k.clipAngle=function(t){return arguments.length?(M=+t?aD(w=t*ud):(w=null,aV),C()):w*uh},k.clipExtent=function(t){return arguments.length?(T=null==t?(A=r=i=o=null,cZ):aG(A=+t[0][0],r=+t[0][1],i=+t[1][0],o=+t[1][1]),C()):null==A?null:[[A,r],[i,o]]},k.scale=function(t){return arguments.length?(s=+t,E()):s},k.translate=function(t){return arguments.length?(h=+t[0],d=+t[1],E()):[h,d]},k.center=function(t){return arguments.length?(p=t[0]%360*ud,v=t[1]%360*ud,E()):[p*uh,v*uh]},k.rotate=function(t){return arguments.length?(g=t[0]%360*ud,y=t[1]%360*ud,b=t.length>2?t[2]%360*ud:0,E()):[g*uh,y*uh,b*uh]},k.angle=function(t){return arguments.length?(m=t%360*ud,E()):m*uh},k.reflectX=function(t){return arguments.length?(_=t?-1:1,E()):_<0},k.reflectY=function(t){return arguments.length?(x=t?-1:1,E()):x<0},k.precision=function(t){return arguments.length?(u=fP(a,S=t*t),C()):uT(S)},k.fitExtent=function(t,n){return fM(k,t,n)},k.fitSize=function(t,n){return fA(k,t,n)},k.fitWidth=function(t,n){return fT(k,t,n)},k.fitHeight=function(t,n){return fS(k,t,n)},function(){return n=t.apply(this,arguments),k.invert=n.invert&&P,E()}}function fO(t){var n=0,e=uc/3,r=fR(t),i=r(n,e);return i.parallels=function(t){return arguments.length?r(n=t[0]*ud,e=t[1]*ud):[n*uh,e*uh]},i}function fZ(t,n){var e=uM(t),r=(e+uM(n))/2;if(1e-6>up(r))return function(t){var n=uy(t);function e(t,e){return[t*n,uM(e)/n]}return e.invert=function(t,e){return[t/n,uP(e*n)]},e}(t);var i=1+e*(2*r-e),o=uT(i)/r;function u(t,n){var e=uT(i-2*r*uM(n))/r;return[e*uM(t*=r),o-e*uy(t)]}return u.invert=function(t,n){var e=o-n,u=ug(t,up(e))*uA(e);return e*r<0&&(u-=uc*uA(t)*uA(e)),[u/r,uP((i-(t*t+e*e)*r*r)/(2*r))]},u}function fz(){return fO(fZ).scale(155.424).center([0,33.6442])}function fB(){return fz().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])}function fL(){var t,n,e,r,i,o,u=fB(),a=fz().rotate([154,0]).center([-2,58.5]).parallels([55,65]),c=fz().rotate([157,0]).center([-3,19.9]).parallels([8,18]),f={point:function(t,n){o=[t,n]}};function l(t){var n=t[0],u=t[1];return o=null,e.point(n,u),o||(r.point(n,u),o)||(i.point(n,u),o)}function s(){return t=n=null,l}return l.invert=function(t){var n=u.scale(),e=u.translate(),r=(t[0]-e[0])/n,i=(t[1]-e[1])/n;return(i>=.12&&i<.234&&r>=-.425&&r<-.214?a:i>=.166&&i<.234&&r>=-.214&&r<-.115?c:u).invert(t)},l.stream=function(e){var r,i;return t&&n===e?t:(i=(r=[u.stream(n=e),a.stream(e),c.stream(e)]).length,t={point:function(t,n){for(var e=-1;++e<i;)r[e].point(t,n)},sphere:function(){for(var t=-1;++t<i;)r[t].sphere()},lineStart:function(){for(var t=-1;++t<i;)r[t].lineStart()},lineEnd:function(){for(var t=-1;++t<i;)r[t].lineEnd()},polygonStart:function(){for(var t=-1;++t<i;)r[t].polygonStart()},polygonEnd:function(){for(var t=-1;++t<i;)r[t].polygonEnd()}})},l.precision=function(t){return arguments.length?(u.precision(t),a.precision(t),c.precision(t),s()):u.precision()},l.scale=function(t){return arguments.length?(u.scale(t),a.scale(.35*t),c.scale(t),l.translate(u.translate())):u.scale()},l.translate=function(t){if(!arguments.length)return u.translate();var n=u.scale(),o=+t[0],l=+t[1];return e=u.translate(t).clipExtent([[o-.455*n,l-.238*n],[o+.455*n,l+.238*n]]).stream(f),r=a.translate([o-.307*n,l+.201*n]).clipExtent([[o-.425*n+1e-6,l+.12*n+1e-6],[o-.214*n-1e-6,l+.234*n-1e-6]]).stream(f),i=c.translate([o-.205*n,l+.212*n]).clipExtent([[o-.214*n+1e-6,l+.166*n+1e-6],[o-.115*n-1e-6,l+.234*n-1e-6]]).stream(f),s()},l.fitExtent=function(t,n){return fM(l,t,n)},l.fitSize=function(t,n){return fA(l,t,n)},l.fitWidth=function(t,n){return fT(l,t,n)},l.fitHeight=function(t,n){return fS(l,t,n)},l.scale(1070)}function fV(t){return function(n,e){var r=uy(n),i=uy(e),o=t(r*i);return o===1/0?[2,0]:[o*i*uM(n),o*uM(e)]}}function fD(t){return function(n,e){var r=uT(n*n+e*e),i=t(r),o=uM(i);return[ug(n*o,r*uy(i)),uP(r&&e*o/r)]}}var fG=fV(function(t){return uT(2/(1+t))});function fj(){return fN(fG).scale(124.75).clipAngle(179.999)}fG.invert=fD(function(t){return 2*uP(t/2)});var fI=fV(function(t){return(t=uk(t))&&t/uM(t)});function fq(){return fN(fI).scale(79.4188).clipAngle(179.999)}function fF(t,n){return[t,ux(uS((uf+n)/2))]}function fH(){return fY(fF).scale(961/us)}function fY(t){var n,e,r,i=fN(t),o=i.center,u=i.scale,a=i.translate,c=i.clipExtent,f=null;function l(){var o=uc*u(),a=i(aA(i.rotate()).invert([0,0]));return c(null==f?[[a[0]-o,a[1]-o],[a[0]+o,a[1]+o]]:t===fF?[[Math.max(a[0]-o,f),n],[Math.min(a[0]+o,e),r]]:[[f,Math.max(a[1]-o,n)],[e,Math.min(a[1]+o,r)]])}return i.scale=function(t){return arguments.length?(u(t),l()):u()},i.translate=function(t){return arguments.length?(a(t),l()):a()},i.center=function(t){return arguments.length?(o(t),l()):o()},i.clipExtent=function(t){return arguments.length?(null==t?f=n=e=r=null:(f=+t[0][0],n=+t[0][1],e=+t[1][0],r=+t[1][1]),l()):null==f?null:[[f,n],[e,r]]},l()}function f$(t){return uS((uf+t)/2)}function fX(t,n){var e=uy(t),r=t===n?uM(t):ux(e/uy(n))/ux(f$(n)/f$(t)),i=e*uw(f$(t),r)/r;if(!r)return fF;function o(t,n){i>0?n<-uf+1e-6&&(n=-uf+1e-6):n>uf-1e-6&&(n=uf-1e-6);var e=i/uw(f$(n),r);return[e*uM(r*t),i-e*uy(r*t)]}return o.invert=function(t,n){var e=i-n,o=uA(r)*uT(t*t+e*e),u=ug(t,up(e))*uA(e);return e*r<0&&(u-=uc*uA(t)*uA(e)),[u/r,2*uv(uw(i/o,1/r))-uf]},o}function fW(){return fO(fX).scale(109.5).parallels([30,30])}function fK(t,n){return[t,n]}function fU(){return fN(fK).scale(152.63)}function fJ(t,n){var e=uy(t),r=t===n?uM(t):(e-uy(n))/(n-t),i=e/r+t;if(1e-6>up(r))return fK;function o(t,n){var e=i-n,o=r*t;return[e*uM(o),i-e*uy(o)]}return o.invert=function(t,n){var e=i-n,o=ug(t,up(e))*uA(e);return e*r<0&&(o-=uc*uA(t)*uA(e)),[o/r,i-uA(r)*uT(t*t+e*e)]},o}function fQ(){return fO(fJ).scale(131.154).center([0,13.9389])}fI.invert=fD(function(t){return t}),fF.invert=function(t,n){return[t,2*uv(um(n))-uf]},fK.invert=fK;var f0=uT(3)/2;function f1(t,n){var e=uP(f0*uM(n)),r=e*e,i=r*r*r;return[t*uy(e)/(f0*(1.340264+-.24331799999999998*r+i*(.0062510000000000005+.034164*r))),e*(1.340264+-.081106*r+i*(893e-6+.003796*r))]}function f2(){return fN(f1).scale(177.158)}function f6(t,n){var e=uy(n),r=uy(t)*e;return[e*uM(t)/r,uM(n)/r]}function f8(){return fN(f6).scale(144.049).clipAngle(60)}function f3(){var t,n,e,r,i,o,u,a=1,c=0,f=0,l=1,s=1,h=0,d=null,p=1,v=1,g=f_({point:function(t,n){var e=m([t,n]);this.stream.point(e[0],e[1])}}),y=cZ;function b(){return p=a*l,v=a*s,o=u=null,m}function m(e){var r=e[0]*p,i=e[1]*v;if(h){var o=i*t-r*n;r=r*t+i*n,i=o}return[r+c,i+f]}return m.invert=function(e){var r=e[0]-c,i=e[1]-f;if(h){var o=i*t+r*n;r=r*t-i*n,i=o}return[r/p,i/v]},m.stream=function(t){return o&&u===t?o:o=g(y(u=t))},m.postclip=function(t){return arguments.length?(y=t,d=e=r=i=null,b()):y},m.clipExtent=function(t){return arguments.length?(y=null==t?(d=e=r=i=null,cZ):aG(d=+t[0][0],e=+t[0][1],r=+t[1][0],i=+t[1][1]),b()):null==d?null:[[d,e],[r,i]]},m.scale=function(t){return arguments.length?(a=+t,b()):a},m.translate=function(t){return arguments.length?(c=+t[0],f=+t[1],b()):[c,f]},m.angle=function(e){return arguments.length?(n=uM(h=e%360*ud),t=uy(h),b()):h*uh},m.reflectX=function(t){return arguments.length?(l=t?-1:1,b()):l<0},m.reflectY=function(t){return arguments.length?(s=t?-1:1,b()):s<0},m.fitExtent=function(t,n){return fM(m,t,n)},m.fitSize=function(t,n){return fA(m,t,n)},m.fitWidth=function(t,n){return fT(m,t,n)},m.fitHeight=function(t,n){return fS(m,t,n)},m}function f4(t,n){var e=n*n,r=e*e;return[t*(.8707-.131979*e+r*(-.013791+r*(.003971*e-.001529*r))),n*(1.007226+e*(.015085+r*(-.044475+.028874*e-.005916*r)))]}function f5(){return fN(f4).scale(175.295)}function f7(t,n){return[uy(n)*uM(t),uM(n)]}function f9(){return fN(f7).scale(249.5).clipAngle(90.000001)}function lt(t,n){var e=uy(n),r=1+uy(t)*e;return[e*uM(t)/r,uM(n)/r]}function ln(){return fN(lt).scale(250).clipAngle(142)}function le(t,n){return[ux(uS((uf+n)/2)),-t]}function lr(){var t=fY(le),n=t.center,e=t.rotate;return t.center=function(t){return arguments.length?n([-t[1],t[0]]):[(t=n())[1],-t[0]]},t.rotate=function(t){return arguments.length?e([t[0],t[1],t.length>2?t[2]+90:90]):[(t=e())[0],t[1],t[2]-90]},e([0,0,90]).scale(159.155)}function li(t,n){return t.parent===n.parent?1:2}function lo(t,n){return t+n.x}function lu(t,n){return Math.max(t,n.y)}function la(){var t=li,n=1,e=1,r=!1;function i(i){var o,u=0;i.eachAfter(function(n){var e=n.children;e?(n.x=e.reduce(lo,0)/e.length,n.y=1+e.reduce(lu,0)):(n.x=o?u+=t(n,o):0,n.y=0,o=n)});var a=function(t){for(var n;n=t.children;)t=n[0];return t}(i),c=function(t){for(var n;n=t.children;)t=n[n.length-1];return t}(i),f=a.x-t(a,c)/2,l=c.x+t(c,a)/2;return i.eachAfter(r?function(t){t.x=(t.x-i.x)*n,t.y=(i.y-t.y)*e}:function(t){t.x=(t.x-f)/(l-f)*n,t.y=(1-(i.y?t.y/i.y:1))*e})}return i.separation=function(n){return arguments.length?(t=n,i):t},i.size=function(t){return arguments.length?(r=!1,n=+t[0],e=+t[1],i):r?null:[n,e]},i.nodeSize=function(t){return arguments.length?(r=!0,n=+t[0],e=+t[1],i):r?[n,e]:null},i}function lc(t){var n=0,e=t.children,r=e&&e.length;if(r)for(;--r>=0;)n+=e[r].value;else n=1;t.value=n}function lf(t,n){t instanceof Map?(t=[void 0,t],void 0===n&&(n=ls)):void 0===n&&(n=ll);for(var e,r,i,o,u,a=new lp(t),c=[a];e=c.pop();)if((i=n(e.data))&&(u=(i=Array.from(i)).length))for(e.children=i,o=u-1;o>=0;--o)c.push(r=i[o]=new lp(i[o])),r.parent=e,r.depth=e.depth+1;return a.eachBefore(ld)}function ll(t){return t.children}function ls(t){return Array.isArray(t)?t[1]:null}function lh(t){void 0!==t.data.value&&(t.value=t.data.value),t.data=t.data.data}function ld(t){var n=0;do t.height=n;while((t=t.parent)&&t.height<++n)}function lp(t){this.data=t,this.depth=this.height=0,this.parent=null}function lv(t){return null==t?null:lg(t)}function lg(t){if("function"!=typeof t)throw Error();return t}function ly(){return 0}function lb(t){return function(){return t}}function lm(){let t=1;return()=>(t=(1664525*t+1013904223)%4294967296)/4294967296}function l_(t){return lx(t,lm())}function lx(t,n){for(var e,r,i=0,o=(t=function(t,n){let e=t.length,r,i;for(;e;)i=n()*e--|0,r=t[e],t[e]=t[i],t[i]=r;return t}(Array.from(t),n)).length,u=[];i<o;)e=t[i],r&&lM(r,e)?++i:(r=function(t){switch(t.length){case 1:var n;return{x:(n=t[0]).x,y:n.y,r:n.r};case 2:return lT(t[0],t[1]);case 3:return lS(t[0],t[1],t[2])}}(u=function(t,n){var e,r;if(lA(n,t))return[n];for(e=0;e<t.length;++e)if(lw(n,t[e])&&lA(lT(t[e],n),t))return[t[e],n];for(e=0;e<t.length-1;++e)for(r=e+1;r<t.length;++r)if(lw(lT(t[e],t[r]),n)&&lw(lT(t[e],n),t[r])&&lw(lT(t[r],n),t[e])&&lA(lS(t[e],t[r],n),t))return[t[e],t[r],n];throw Error()}(u,e)),i=0);return r}function lw(t,n){var e=t.r-n.r,r=n.x-t.x,i=n.y-t.y;return e<0||e*e<r*r+i*i}function lM(t,n){var e=t.r-n.r+1e-9*Math.max(t.r,n.r,1),r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function lA(t,n){for(var e=0;e<n.length;++e)if(!lM(t,n[e]))return!1;return!0}function lT(t,n){var e=t.x,r=t.y,i=t.r,o=n.x,u=n.y,a=n.r,c=o-e,f=u-r,l=a-i,s=Math.sqrt(c*c+f*f);return{x:(e+o+c/s*l)/2,y:(r+u+f/s*l)/2,r:(s+i+a)/2}}function lS(t,n,e){var r=t.x,i=t.y,o=t.r,u=n.x,a=n.y,c=n.r,f=e.x,l=e.y,s=e.r,h=r-u,d=r-f,p=i-a,v=i-l,g=c-o,y=s-o,b=r*r+i*i-o*o,m=b-u*u-a*a+c*c,_=b-f*f-l*l+s*s,x=d*p-h*v,w=(p*_-v*m)/(2*x)-r,M=(v*g-p*y)/x,A=(d*m-h*_)/(2*x)-i,T=(h*y-d*g)/x,S=M*M+T*T-1,k=2*(o+w*M+A*T),P=w*w+A*A-o*o,E=-(Math.abs(S)>1e-6?(k+Math.sqrt(k*k-4*S*P))/(2*S):P/k);return{x:r+w+M*E,y:i+A+T*E,r:E}}function lk(t,n,e){var r,i,o,u,a=t.x-n.x,c=t.y-n.y,f=a*a+c*c;f?(i=n.r+e.r,i*=i,u=t.r+e.r,i>(u*=u)?(r=(f+u-i)/(2*f),o=Math.sqrt(Math.max(0,u/f-r*r)),e.x=t.x-r*a-o*c,e.y=t.y-r*c+o*a):(r=(f+i-u)/(2*f),o=Math.sqrt(Math.max(0,i/f-r*r)),e.x=n.x+r*a-o*c,e.y=n.y+r*c+o*a)):(e.x=n.x+e.r,e.y=n.y)}function lP(t,n){var e=t.r+n.r-1e-6,r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function lE(t){var n=t._,e=t.next._,r=n.r+e.r,i=(n.x*e.r+e.x*n.r)/r,o=(n.y*e.r+e.y*n.r)/r;return i*i+o*o}function lC(t){this._=t,this.next=null,this.previous=null}function lN(t,n){var e,r,i,o,u,a,c,f,l,s,h,d;if(!(o=(t="object"==typeof(d=t)&&"length"in d?d:Array.from(d)).length))return 0;if((e=t[0]).x=0,e.y=0,!(o>1))return e.r;if(r=t[1],e.x=-r.r,r.x=e.r,r.y=0,!(o>2))return e.r+r.r;lk(r,e,i=t[2]),e=new lC(e),r=new lC(r),i=new lC(i),e.next=i.previous=r,r.next=e.previous=i,i.next=r.previous=e;e:for(c=3;c<o;++c){lk(e._,r._,i=t[c]),i=new lC(i),f=r.next,l=e.previous,s=r._.r,h=e._.r;do if(s<=h){if(lP(f._,i._)){r=f,e.next=r,r.previous=e,--c;continue e}s+=f._.r,f=f.next}else{if(lP(l._,i._)){(e=l).next=r,r.previous=e,--c;continue e}h+=l._.r,l=l.previous}while(f!==l.next);for(i.previous=e,i.next=r,e.next=r.previous=r=i,u=lE(e);(i=i.next)!==r;)(a=lE(i))<u&&(e=i,u=a);r=e.next}for(e=[r._],i=r;(i=i.next)!==r;)e.push(i._);for(c=0,i=lx(e,n);c<o;++c)e=t[c],e.x-=i.x,e.y-=i.y;return i.r}function lR(t){return lN(t,lm()),t}function lO(t){return Math.sqrt(t.value)}function lZ(){var t=null,n=1,e=1,r=ly;function i(i){let o=lm();return i.x=n/2,i.y=e/2,t?i.eachBefore(lz(t)).eachAfter(lB(r,.5,o)).eachBefore(lL(1)):i.eachBefore(lz(lO)).eachAfter(lB(ly,1,o)).eachAfter(lB(r,i.r/Math.min(n,e),o)).eachBefore(lL(Math.min(n,e)/(2*i.r))),i}return i.radius=function(n){return arguments.length?(t=lv(n),i):t},i.size=function(t){return arguments.length?(n=+t[0],e=+t[1],i):[n,e]},i.padding=function(t){return arguments.length?(r="function"==typeof t?t:lb(+t),i):r},i}function lz(t){return function(n){n.children||(n.r=Math.max(0,+t(n)||0))}}function lB(t,n,e){return function(r){if(i=r.children){var i,o,u,a=i.length,c=t(r)*n||0;if(c)for(o=0;o<a;++o)i[o].r+=c;if(u=lN(i,e),c)for(o=0;o<a;++o)i[o].r-=c;r.r=u+c}}}function lL(t){return function(n){var e=n.parent;n.r*=t,e&&(n.x=e.x+t*n.x,n.y=e.y+t*n.y)}}function lV(t){t.x0=Math.round(t.x0),t.y0=Math.round(t.y0),t.x1=Math.round(t.x1),t.y1=Math.round(t.y1)}function lD(t,n,e,r,i){for(var o,u=t.children,a=-1,c=u.length,f=t.value&&(r-n)/t.value;++a<c;)(o=u[a]).y0=e,o.y1=i,o.x0=n,o.x1=n+=o.value*f}function lG(){var t=1,n=1,e=0,r=!1;function i(i){var o,u=i.height+1;return i.x0=i.y0=e,i.x1=t,i.y1=n/u,i.eachBefore((o=n,function(t){t.children&&lD(t,t.x0,o*(t.depth+1)/u,t.x1,o*(t.depth+2)/u);var n=t.x0,r=t.y0,i=t.x1-e,a=t.y1-e;i<n&&(n=i=(n+i)/2),a<r&&(r=a=(r+a)/2),t.x0=n,t.y0=r,t.x1=i,t.y1=a})),r&&i.eachBefore(lV),i}return i.round=function(t){return arguments.length?(r=!!t,i):r},i.size=function(e){return arguments.length?(t=+e[0],n=+e[1],i):[t,n]},i.padding=function(t){return arguments.length?(e=+t,i):e},i}f1.invert=function(t,n){for(var e,r,i=n,o=i*i,u=o*o*o,a=0;a<12&&(r=i*(1.340264+-.081106*o+u*(893e-6+.003796*o))-n,i-=e=r/(1.340264+-.24331799999999998*o+u*(.0062510000000000005+.034164*o)),u=(o=i*i)*o*o,!(1e-12>up(e)));++a);return[f0*t*(1.340264+-.24331799999999998*o+u*(.0062510000000000005+.034164*o))/uy(i),uP(uM(i)/f0)]},f6.invert=fD(uv),f4.invert=function(t,n){var e,r=n,i=25;do{var o=r*r,u=o*o;r-=e=(r*(1.007226+o*(.015085+u*(-.044475+.028874*o-.005916*u)))-n)/(1.007226+o*(.045255+u*(-.311325+.259866*o-.005916*11*u)))}while(up(e)>1e-6&&--i>0);return[t/(.8707+(o=r*r)*(-.131979+o*(-.013791+o*o*o*(.003971-.001529*o)))),r]},f7.invert=fD(uP),lt.invert=fD(function(t){return 2*uv(t)}),le.invert=function(t,n){return[-n,2*uv(um(t))-uf]},lp.prototype=lf.prototype={constructor:lp,count:function(){return this.eachAfter(lc)},each:function(t,n){let e=-1;for(let r of this)t.call(n,r,++e,this);return this},eachAfter:function(t,n){for(var e,r,i,o=this,u=[o],a=[],c=-1;o=u.pop();)if(a.push(o),e=o.children)for(r=0,i=e.length;r<i;++r)u.push(e[r]);for(;o=a.pop();)t.call(n,o,++c,this);return this},eachBefore:function(t,n){for(var e,r,i=this,o=[i],u=-1;i=o.pop();)if(t.call(n,i,++u,this),e=i.children)for(r=e.length-1;r>=0;--r)o.push(e[r]);return this},find:function(t,n){let e=-1;for(let r of this)if(t.call(n,r,++e,this))return r},sum:function(t){return this.eachAfter(function(n){for(var e=+t(n.data)||0,r=n.children,i=r&&r.length;--i>=0;)e+=r[i].value;n.value=e})},sort:function(t){return this.eachBefore(function(n){n.children&&n.children.sort(t)})},path:function(t){for(var n=this,e=function(t,n){if(t===n)return t;var e=t.ancestors(),r=n.ancestors(),i=null;for(t=e.pop(),n=r.pop();t===n;)i=t,t=e.pop(),n=r.pop();return i}(n,t),r=[n];n!==e;)r.push(n=n.parent);for(var i=r.length;t!==e;)r.splice(i,0,t),t=t.parent;return r},ancestors:function(){for(var t=this,n=[t];t=t.parent;)n.push(t);return n},descendants:function(){return Array.from(this)},leaves:function(){var t=[];return this.eachBefore(function(n){n.children||t.push(n)}),t},links:function(){var t=this,n=[];return t.each(function(e){e!==t&&n.push({source:e.parent,target:e})}),n},copy:function(){return lf(this).eachBefore(lh)},[Symbol.iterator]:function*(){var t,n,e,r,i=this,o=[i];do for(t=o.reverse(),o=[];i=t.pop();)if(yield i,n=i.children)for(e=0,r=n.length;e<r;++e)o.push(n[e]);while(o.length)}};var lj={depth:-1},lI={},lq={};function lF(t){return t.id}function lH(t){return t.parentId}function lY(){var t,n=lF,e=lH;function r(r){var i,o,u,a,c,f,l,s,h=Array.from(r),d=n,p=e,v=new Map;if(null!=t){let n=h.map((n,e)=>{var i;let o;return i=t(n,e,r),o=(i=`${i}`).length,lX(i,o-1)&&!lX(i,o-2)&&(i=i.slice(0,-1)),"/"===i[0]?i:`/${i}`}),e=n.map(l$),i=new Set(n).add("");for(let t of e)i.has(t)||(i.add(t),n.push(t),e.push(l$(t)),h.push(lq));d=(t,e)=>n[e],p=(t,n)=>e[n]}for(u=0,i=h.length;u<i;++u)o=h[u],f=h[u]=new lp(o),null!=(l=d(o,u,r))&&(l+="")&&(s=f.id=l,v.set(s,v.has(s)?lI:f)),null!=(l=p(o,u,r))&&(l+="")&&(f.parent=l);for(u=0;u<i;++u)if(l=(f=h[u]).parent){if(!(c=v.get(l)))throw Error("missing: "+l);if(c===lI)throw Error("ambiguous: "+l);c.children?c.children.push(f):c.children=[f],f.parent=c}else{if(a)throw Error("multiple roots");a=f}if(!a)throw Error("no root");if(null!=t){for(;a.data===lq&&1===a.children.length;)a=a.children[0],--i;for(let t=h.length-1;t>=0&&(f=h[t]).data===lq;--t)f.data=null}if(a.parent=lj,a.eachBefore(function(t){t.depth=t.parent.depth+1,--i}).eachBefore(ld),a.parent=null,i>0)throw Error("cycle");return a}return r.id=function(t){return arguments.length?(n=lv(t),r):n},r.parentId=function(t){return arguments.length?(e=lv(t),r):e},r.path=function(n){return arguments.length?(t=lv(n),r):t},r}function l$(t){let n=t.length;if(n<2)return"";for(;--n>1&&!lX(t,n););return t.slice(0,n)}function lX(t,n){if("/"===t[n]){let e=0;for(;n>0&&"\\"===t[--n];)++e;if((1&e)==0)return!0}return!1}function lW(t,n){return t.parent===n.parent?1:2}function lK(t){var n=t.children;return n?n[0]:t.t}function lU(t){var n=t.children;return n?n[n.length-1]:t.t}function lJ(t,n){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=n}function lQ(){var t=lW,n=1,e=1,r=null;function i(i){var c=function(t){for(var n,e,r,i,o,u=new lJ(t,0),a=[u];n=a.pop();)if(r=n._.children)for(n.children=Array(o=r.length),i=o-1;i>=0;--i)a.push(e=n.children[i]=new lJ(r[i],i)),e.parent=n;return(u.parent=new lJ(null,0)).children=[u],u}(i);if(c.eachAfter(o),c.parent.m=-c.z,c.eachBefore(u),r)i.eachBefore(a);else{var f=i,l=i,s=i;i.eachBefore(function(t){t.x<f.x&&(f=t),t.x>l.x&&(l=t),t.depth>s.depth&&(s=t)});var h=f===l?1:t(f,l)/2,d=h-f.x,p=n/(l.x+h+d),v=e/(s.depth||1);i.eachBefore(function(t){t.x=(t.x+d)*p,t.y=t.depth*v})}return i}function o(n){var e=n.children,r=n.parent.children,i=n.i?r[n.i-1]:null;if(e){!function(t){for(var n,e=0,r=0,i=t.children,o=i.length;--o>=0;)n=i[o],n.z+=e,n.m+=e,e+=n.s+(r+=n.c)}(n);var o=(e[0].z+e[e.length-1].z)/2;i?(n.z=i.z+t(n._,i._),n.m=n.z-o):n.z=o}else i&&(n.z=i.z+t(n._,i._));n.parent.A=function(n,e,r){if(e){for(var i,o,u,a=n,c=n,f=e,l=a.parent.children[0],s=a.m,h=c.m,d=f.m,p=l.m;f=lU(f),a=lK(a),f&&a;)l=lK(l),(c=lU(c)).a=n,(u=f.z+d-a.z-s+t(f._,a._))>0&&(function(t,n,e){var r=e/(n.i-t.i);n.c-=r,n.s+=e,t.c+=r,n.z+=e,n.m+=e}((i=f,o=r,i.a.parent===n.parent?i.a:o),n,u),s+=u,h+=u),d+=f.m,s+=a.m,p+=l.m,h+=c.m;f&&!lU(c)&&(c.t=f,c.m+=d-h),a&&!lK(l)&&(l.t=a,l.m+=s-p,r=n)}return r}(n,i,n.parent.A||r[0])}function u(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function a(t){t.x*=n,t.y=t.depth*e}return i.separation=function(n){return arguments.length?(t=n,i):t},i.size=function(t){return arguments.length?(r=!1,n=+t[0],e=+t[1],i):r?null:[n,e]},i.nodeSize=function(t){return arguments.length?(r=!0,n=+t[0],e=+t[1],i):r?[n,e]:null},i}function l0(t,n,e,r,i){for(var o,u=t.children,a=-1,c=u.length,f=t.value&&(i-e)/t.value;++a<c;)(o=u[a]).x0=n,o.x1=r,o.y0=e,o.y1=e+=o.value*f}lJ.prototype=Object.create(lp.prototype);var l1=(1+Math.sqrt(5))/2;function l2(t,n,e,r,i,o){for(var u,a,c,f,l,s,h,d,p,v,g,y=[],b=n.children,m=0,_=0,x=b.length,w=n.value;m<x;){c=i-e,f=o-r;do l=b[_++].value;while(!l&&_<x);for(s=h=l,p=Math.max(h/(g=l*l*(v=Math.max(f/c,c/f)/(w*t))),g/s);_<x;++_){if(l+=a=b[_].value,a<s&&(s=a),a>h&&(h=a),(d=Math.max(h/(g=l*l*v),g/s))>p){l-=a;break}p=d}y.push(u={value:l,dice:c<f,children:b.slice(m,_)}),u.dice?lD(u,e,r,i,w?r+=f*l/w:o):l0(u,e,r,w?e+=c*l/w:i,o),w-=l,m=_}return y}var l6=function t(n){function e(t,e,r,i,o){l2(n,t,e,r,i,o)}return e.ratio=function(n){return t((n=+n)>1?n:1)},e}(l1);function l8(){var t=l6,n=!1,e=1,r=1,i=[0],o=ly,u=ly,a=ly,c=ly,f=ly;function l(t){return t.x0=t.y0=0,t.x1=e,t.y1=r,t.eachBefore(s),i=[0],n&&t.eachBefore(lV),t}function s(n){var e=i[n.depth],r=n.x0+e,l=n.y0+e,s=n.x1-e,h=n.y1-e;s<r&&(r=s=(r+s)/2),h<l&&(l=h=(l+h)/2),n.x0=r,n.y0=l,n.x1=s,n.y1=h,n.children&&(e=i[n.depth+1]=o(n)/2,r+=f(n)-e,l+=u(n)-e,s-=a(n)-e,h-=c(n)-e,s<r&&(r=s=(r+s)/2),h<l&&(l=h=(l+h)/2),t(n,r,l,s,h))}return l.round=function(t){return arguments.length?(n=!!t,l):n},l.size=function(t){return arguments.length?(e=+t[0],r=+t[1],l):[e,r]},l.tile=function(n){return arguments.length?(t=lg(n),l):t},l.padding=function(t){return arguments.length?l.paddingInner(t).paddingOuter(t):l.paddingInner()},l.paddingInner=function(t){return arguments.length?(o="function"==typeof t?t:lb(+t),l):o},l.paddingOuter=function(t){return arguments.length?l.paddingTop(t).paddingRight(t).paddingBottom(t).paddingLeft(t):l.paddingTop()},l.paddingTop=function(t){return arguments.length?(u="function"==typeof t?t:lb(+t),l):u},l.paddingRight=function(t){return arguments.length?(a="function"==typeof t?t:lb(+t),l):a},l.paddingBottom=function(t){return arguments.length?(c="function"==typeof t?t:lb(+t),l):c},l.paddingLeft=function(t){return arguments.length?(f="function"==typeof t?t:lb(+t),l):f},l}function l3(t,n,e,r,i){var o,u,a=t.children,c=a.length,f=Array(c+1);for(f[0]=u=o=0;o<c;++o)f[o+1]=u+=a[o].value;!function t(n,e,r,i,o,u,c){if(n>=e-1){var l=a[n];l.x0=i,l.y0=o,l.x1=u,l.y1=c;return}for(var s=f[n],h=r/2+s,d=n+1,p=e-1;d<p;){var v=d+p>>>1;f[v]<h?d=v+1:p=v}h-f[d-1]<f[d]-h&&n+1<d&&--d;var g=f[d]-s,y=r-g;if(u-i>c-o){var b=r?(i*y+u*g)/r:u;t(n,d,g,i,o,b,c),t(d,e,y,b,o,u,c)}else{var m=r?(o*y+c*g)/r:c;t(n,d,g,i,o,u,m),t(d,e,y,i,m,u,c)}}(0,c,t.value,n,e,r,i)}function l4(t,n,e,r,i){(1&t.depth?l0:lD)(t,n,e,r,i)}var l5=function t(n){function e(t,e,r,i,o){if((u=t._squarify)&&u.ratio===n)for(var u,a,c,f,l,s=-1,h=u.length,d=t.value;++s<h;){for(c=(a=u[s]).children,f=a.value=0,l=c.length;f<l;++f)a.value+=c[f].value;a.dice?lD(a,e,r,i,d?r+=(o-r)*a.value/d:o):l0(a,e,r,d?e+=(i-e)*a.value/d:i,o),d-=a.value}else t._squarify=u=l2(n,t,e,r,i,o),u.ratio=n}return e.ratio=function(n){return t((n=+n)>1?n:1)},e}(l1),l7=e(9780),l9=e(19435),st=e(81641),sn=e(73564);function se(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}var sr=e(48443);function si(t,n){var e=(0,sr.wx)(+t,+n);return function(t){var n=e(t);return n-360*Math.floor(n/360)}}var so=e(48329),su=e(96803),sa=e(93785);function sc(t){return((t=Math.exp(t))+1/t)/2}var sf=function t(n,e,r){function i(t,i){var o,u,a=t[0],c=t[1],f=t[2],l=i[0],s=i[1],h=i[2],d=l-a,p=s-c,v=d*d+p*p;if(v<1e-12)u=Math.log(h/f)/n,o=function(t){return[a+t*d,c+t*p,f*Math.exp(n*t*u)]};else{var g=Math.sqrt(v),y=(h*h-f*f+r*v)/(2*f*e*g),b=(h*h-f*f-r*v)/(2*h*e*g),m=Math.log(Math.sqrt(y*y+1)-y);u=(Math.log(Math.sqrt(b*b+1)-b)-m)/n,o=function(t){var r,i,o=t*u,l=sc(m),s=f/(e*g)*(((r=Math.exp(2*(r=n*o+m)))-1)/(r+1)*l-((i=Math.exp(i=m))-1/i)/2);return[a+s*d,c+s*p,f*l/sc(n*o+m)]}}return o.duration=1e3*u*n/Math.SQRT2,o}return i.rho=function(n){var e=Math.max(.001,+n),r=e*e;return t(e,r,r*r)},i}(Math.SQRT2,2,4);function sl(t){return function(n,e){var r=t((n=(0,ed.Ym)(n)).h,(e=(0,ed.Ym)(e)).h),i=(0,sr.ZP)(n.s,e.s),o=(0,sr.ZP)(n.l,e.l),u=(0,sr.ZP)(n.opacity,e.opacity);return function(t){return n.h=r(t),n.s=i(t),n.l=o(t),n.opacity=u(t),n+""}}}var ss=sl(sr.wx),sh=sl(sr.ZP);function sd(t,n){var e=(0,sr.ZP)((t=rE(t)).l,(n=rE(n)).l),r=(0,sr.ZP)(t.a,n.a),i=(0,sr.ZP)(t.b,n.b),o=(0,sr.ZP)(t.opacity,n.opacity);return function(n){return t.l=e(n),t.a=r(n),t.b=i(n),t.opacity=o(n),t+""}}function sp(t){return function(n,e){var r=t((n=rL(n)).h,(e=rL(e)).h),i=(0,sr.ZP)(n.c,e.c),o=(0,sr.ZP)(n.l,e.l),u=(0,sr.ZP)(n.opacity,e.opacity);return function(t){return n.h=r(t),n.c=i(t),n.l=o(t),n.opacity=u(t),n+""}}}var sv=sp(sr.wx),sg=sp(sr.ZP);function sy(t){return function n(e){function r(n,r){var i=t((n=rj(n)).h,(r=rj(r)).h),o=(0,sr.ZP)(n.s,r.s),u=(0,sr.ZP)(n.l,r.l),a=(0,sr.ZP)(n.opacity,r.opacity);return function(t){return n.h=i(t),n.s=o(t),n.l=u(Math.pow(t,e)),n.opacity=a(t),n+""}}return e=+e,r.gamma=n,r}(1)}var sb=sy(sr.wx),sm=sy(sr.ZP),s_=e(42953);function sx(t,n){for(var e=Array(n),r=0;r<n;++r)e[r]=t(r/(n-1));return e}function sw(t){for(var n,e=-1,r=t.length,i=t[r-1],o=0;++e<r;)n=i,i=t[e],o+=n[1]*i[0]-n[0]*i[1];return o/2}function sM(t){for(var n,e,r=-1,i=t.length,o=0,u=0,a=t[i-1],c=0;++r<i;)n=a,a=t[r],c+=e=n[0]*a[1]-a[0]*n[1],o+=(n[0]+a[0])*e,u+=(n[1]+a[1])*e;return[o/(c*=3),u/c]}function sA(t,n){return t[0]-n[0]||t[1]-n[1]}function sT(t){let n=t.length,e=[0,1],r=2,i;for(i=2;i<n;++i){for(var o,u,a;r>1&&0>=(o=t[e[r-2]],u=t[e[r-1]],a=t[i],(u[0]-o[0])*(a[1]-o[1])-(u[1]-o[1])*(a[0]-o[0]));)--r;e[r++]=i}return e.slice(0,r)}function sS(t){if((e=t.length)<3)return null;var n,e,r=Array(e),i=Array(e);for(n=0;n<e;++n)r[n]=[+t[n][0],+t[n][1],n];for(r.sort(sA),n=0;n<e;++n)i[n]=[r[n][0],-r[n][1]];var o=sT(r),u=sT(i),a=u[0]===o[0],c=u[u.length-1]===o[o.length-1],f=[];for(n=o.length-1;n>=0;--n)f.push(t[r[o[n]][2]]);for(n=+a;n<u.length-c;++n)f.push(t[r[u[n]][2]]);return f}function sk(t,n){for(var e,r,i=t.length,o=t[i-1],u=n[0],a=n[1],c=o[0],f=o[1],l=!1,s=0;s<i;++s)e=(o=t[s])[0],(r=o[1])>a!=f>a&&u<(c-e)*(a-r)/(f-r)+e&&(l=!l),c=e,f=r;return l}function sP(t){for(var n,e,r=-1,i=t.length,o=t[i-1],u=o[0],a=o[1],c=0;++r<i;)n=u,e=a,u=(o=t[r])[0],a=o[1],n-=u,e-=a,c+=Math.hypot(n,e);return c}var sE=Math.random,sC=function t(n){function e(t,e){return t=null==t?0:+t,e=null==e?1:+e,1==arguments.length?(e=t,t=0):e-=t,function(){return n()*e+t}}return e.source=t,e}(sE),sN=function t(n){function e(t,e){return arguments.length<2&&(e=t,t=0),e=Math.floor(e)-(t=Math.floor(t)),function(){return Math.floor(n()*e+t)}}return e.source=t,e}(sE),sR=function t(n){function e(t,e){var r,i;return t=null==t?0:+t,e=null==e?1:+e,function(){var o;if(null!=r)o=r,r=null;else do r=2*n()-1,o=2*n()-1,i=r*r+o*o;while(!i||i>1);return t+e*o*Math.sqrt(-2*Math.log(i)/i)}}return e.source=t,e}(sE),sO=function t(n){var e=sR.source(n);function r(){var t=e.apply(this,arguments);return function(){return Math.exp(t())}}return r.source=t,r}(sE),sZ=function t(n){function e(t){return(t=+t)<=0?()=>0:function(){for(var e=0,r=t;r>1;--r)e+=n();return e+r*n()}}return e.source=t,e}(sE),sz=function t(n){var e=sZ.source(n);function r(t){if(0==(t=+t))return n;var r=e(t);return function(){return r()/t}}return r.source=t,r}(sE),sB=function t(n){function e(t){return function(){return-Math.log1p(-n())/t}}return e.source=t,e}(sE),sL=function t(n){function e(t){if((t=+t)<0)throw RangeError("invalid alpha");return t=-(1/t),function(){return Math.pow(1-n(),t)}}return e.source=t,e}(sE),sV=function t(n){function e(t){if((t=+t)<0||t>1)throw RangeError("invalid p");return function(){return Math.floor(n()+t)}}return e.source=t,e}(sE),sD=function t(n){function e(t){if((t=+t)<0||t>1)throw RangeError("invalid p");return 0===t?()=>1/0:1===t?()=>1:(t=Math.log1p(-t),function(){return 1+Math.floor(Math.log1p(-n())/t)})}return e.source=t,e}(sE),sG=function t(n){var e=sR.source(n)();function r(t,r){if((t=+t)<0)throw RangeError("invalid k");if(0===t)return()=>0;if(r=null==r?1:+r,1===t)return()=>-Math.log1p(-n())*r;var i=(t<1?t+1:t)-1/3,o=1/(3*Math.sqrt(i)),u=t<1?()=>Math.pow(n(),1/t):()=>1;return function(){do{do var t=e(),a=1+o*t;while(a<=0);a*=a*a;var c=1-n()}while(c>=1-.0331*t*t*t*t&&Math.log(c)>=.5*t*t+i*(1-a+Math.log(a)));return i*a*u()*r}}return r.source=t,r}(sE),sj=function t(n){var e=sG.source(n);function r(t,n){var r=e(t),i=e(n);return function(){var t=r();return 0===t?0:t/(t+i())}}return r.source=t,r}(sE),sI=function t(n){var e=sD.source(n),r=sj.source(n);function i(t,n){return(t=+t,(n=+n)>=1)?()=>t:n<=0?()=>0:function(){for(var i=0,o=t,u=n;o*u>16&&o*(1-u)>16;){var a=Math.floor((o+1)*u),c=r(a,o-a+1)();c<=u?(i+=a,o-=a,u=(u-c)/(1-c)):(o=a-1,u/=c)}for(var f=u<.5,l=e(f?u:1-u),s=l(),h=0;s<=o;++h)s+=l();return i+(f?h:o-h)}}return i.source=t,i}(sE),sq=function t(n){function e(t,e,r){var i;return 0==(t=+t)?i=t=>-Math.log(t):(t=1/t,i=n=>Math.pow(n,t)),e=null==e?0:+e,r=null==r?1:+r,function(){return e+r*i(-Math.log1p(-n()))}}return e.source=t,e}(sE),sF=function t(n){function e(t,e){return t=null==t?0:+t,e=null==e?1:+e,function(){return t+e*Math.tan(Math.PI*n())}}return e.source=t,e}(sE),sH=function t(n){function e(t,e){return t=null==t?0:+t,e=null==e?1:+e,function(){var r=n();return t+e*Math.log(r/(1-r))}}return e.source=t,e}(sE),sY=function t(n){var e=sG.source(n),r=sI.source(n);function i(t){return function(){for(var i=0,o=t;o>16;){var u=Math.floor(.875*o),a=e(u)();if(a>o)return i+r(u-1,o/a)();i+=u,o-=a}for(var c=-Math.log1p(-n()),f=0;c<=o;++f)c-=Math.log1p(-n());return i+f}}return i.source=t,i}(sE);let s$=1/4294967296;function sX(t=Math.random()){let n=(0<=t&&t<1?t/s$:Math.abs(t))|0;return()=>s$*((n=1664525*n+1013904223|0)>>>0)}var sW=e(63460);function sK(t){for(var n=t.length/6|0,e=Array(n),r=0;r<n;)e[r]="#"+t.slice(6*r,6*++r);return e}var sU=sK("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf"),sJ=sK("7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666"),sQ=sK("1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666"),s0=sK("4269d0efb118ff725c6cc5b03ca951ff8ab7a463f297bbf59c6b4e9498a0"),s1=sK("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928"),s2=sK("fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2"),s6=sK("b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc"),s8=sK("e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999"),s3=sK("66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3"),s4=sK("8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f"),s5=sK("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab"),s7=t=>(0,ep.hD)(t[t.length-1]),s9=[,,,].concat("d8b365f5f5f55ab4ac","a6611adfc27d80cdc1018571","a6611adfc27df5f5f580cdc1018571","8c510ad8b365f6e8c3c7eae55ab4ac01665e","8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e","8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e","8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e","5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30","5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30").map(sK),ht=s7(s9),hn=[,,,].concat("af8dc3f7f7f77fbf7b","7b3294c2a5cfa6dba0008837","7b3294c2a5cff7f7f7a6dba0008837","762a83af8dc3e7d4e8d9f0d37fbf7b1b7837","762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837","762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837","762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837","40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b","40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b").map(sK),he=s7(hn),hr=[,,,].concat("e9a3c9f7f7f7a1d76a","d01c8bf1b6dab8e1864dac26","d01c8bf1b6daf7f7f7b8e1864dac26","c51b7de9a3c9fde0efe6f5d0a1d76a4d9221","c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221","c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221","c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221","8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419","8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419").map(sK),hi=s7(hr),ho=[,,,].concat("998ec3f7f7f7f1a340","5e3c99b2abd2fdb863e66101","5e3c99b2abd2f7f7f7fdb863e66101","542788998ec3d8daebfee0b6f1a340b35806","542788998ec3d8daebf7f7f7fee0b6f1a340b35806","5427888073acb2abd2d8daebfee0b6fdb863e08214b35806","5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b35806","2d004b5427888073acb2abd2d8daebfee0b6fdb863e08214b358067f3b08","2d004b5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b358067f3b08").map(sK),hu=s7(ho),ha=[,,,].concat("ef8a62f7f7f767a9cf","ca0020f4a58292c5de0571b0","ca0020f4a582f7f7f792c5de0571b0","b2182bef8a62fddbc7d1e5f067a9cf2166ac","b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac","b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac","b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac","67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061","67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061").map(sK),hc=s7(ha),hf=[,,,].concat("ef8a62ffffff999999","ca0020f4a582bababa404040","ca0020f4a582ffffffbababa404040","b2182bef8a62fddbc7e0e0e09999994d4d4d","b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d","b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d","b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d","67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a","67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a").map(sK),hl=s7(hf),hs=[,,,].concat("fc8d59ffffbf91bfdb","d7191cfdae61abd9e92c7bb6","d7191cfdae61ffffbfabd9e92c7bb6","d73027fc8d59fee090e0f3f891bfdb4575b4","d73027fc8d59fee090ffffbfe0f3f891bfdb4575b4","d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4","d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4","a50026d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4313695","a50026d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4313695").map(sK),hh=s7(hs),hd=[,,,].concat("fc8d59ffffbf91cf60","d7191cfdae61a6d96a1a9641","d7191cfdae61ffffbfa6d96a1a9641","d73027fc8d59fee08bd9ef8b91cf601a9850","d73027fc8d59fee08bffffbfd9ef8b91cf601a9850","d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850","d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850","a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837","a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837").map(sK),hp=s7(hd),hv=[,,,].concat("fc8d59ffffbf99d594","d7191cfdae61abdda42b83ba","d7191cfdae61ffffbfabdda42b83ba","d53e4ffc8d59fee08be6f59899d5943288bd","d53e4ffc8d59fee08bffffbfe6f59899d5943288bd","d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd","d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd","9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2","9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2").map(sK),hg=s7(hv),hy=[,,,].concat("e5f5f999d8c92ca25f","edf8fbb2e2e266c2a4238b45","edf8fbb2e2e266c2a42ca25f006d2c","edf8fbccece699d8c966c2a42ca25f006d2c","edf8fbccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45006d2c00441b").map(sK),hb=s7(hy),hm=[,,,].concat("e0ecf49ebcda8856a7","edf8fbb3cde38c96c688419d","edf8fbb3cde38c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b").map(sK),h_=s7(hm),hx=[,,,].concat("e0f3dba8ddb543a2ca","f0f9e8bae4bc7bccc42b8cbe","f0f9e8bae4bc7bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe0868ac084081").map(sK),hw=s7(hx),hM=[,,,].concat("fee8c8fdbb84e34a33","fef0d9fdcc8afc8d59d7301f","fef0d9fdcc8afc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000").map(sK),hA=s7(hM),hT=[,,,].concat("ece2f0a6bddb1c9099","f6eff7bdc9e167a9cf02818a","f6eff7bdc9e167a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636").map(sK),hS=s7(hT),hk=[,,,].concat("ece7f2a6bddb2b8cbe","f1eef6bdc9e174a9cf0570b0","f1eef6bdc9e174a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858").map(sK),hP=s7(hk),hE=[,,,].concat("e7e1efc994c7dd1c77","f1eef6d7b5d8df65b0ce1256","f1eef6d7b5d8df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125698004367001f").map(sK),hC=s7(hE),hN=[,,,].concat("fde0ddfa9fb5c51b8a","feebe2fbb4b9f768a1ae017e","feebe2fbb4b9f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a017749006a").map(sK),hR=s7(hN),hO=[,,,].concat("edf8b17fcdbb2c7fb8","ffffcca1dab441b6c4225ea8","ffffcca1dab441b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58").map(sK),hZ=s7(hO),hz=[,,,].concat("f7fcb9addd8e31a354","ffffccc2e69978c679238443","ffffccc2e69978c67931a354006837","ffffccd9f0a3addd8e78c67931a354006837","ffffccd9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529").map(sK),hB=s7(hz),hL=[,,,].concat("fff7bcfec44fd95f0e","ffffd4fed98efe9929cc4c02","ffffd4fed98efe9929d95f0e993404","ffffd4fee391fec44ffe9929d95f0e993404","ffffd4fee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506").map(sK),hV=s7(hL),hD=[,,,].concat("ffeda0feb24cf03b20","ffffb2fecc5cfd8d3ce31a1c","ffffb2fecc5cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026").map(sK),hG=s7(hD),hj=[,,,].concat("deebf79ecae13182bd","eff3ffbdd7e76baed62171b5","eff3ffbdd7e76baed63182bd08519c","eff3ffc6dbef9ecae16baed63182bd08519c","eff3ffc6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b").map(sK),hI=s7(hj),hq=[,,,].concat("e5f5e0a1d99b31a354","edf8e9bae4b374c476238b45","edf8e9bae4b374c47631a354006d2c","edf8e9c7e9c0a1d99b74c47631a354006d2c","edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b").map(sK),hF=s7(hq),hH=[,,,].concat("f0f0f0bdbdbd636363","f7f7f7cccccc969696525252","f7f7f7cccccc969696636363252525","f7f7f7d9d9d9bdbdbd969696636363252525","f7f7f7d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000").map(sK),hY=s7(hH),h$=[,,,].concat("efedf5bcbddc756bb1","f2f0f7cbc9e29e9ac86a51a3","f2f0f7cbc9e29e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d").map(sK),hX=s7(h$),hW=[,,,].concat("fee0d2fc9272de2d26","fee5d9fcae91fb6a4acb181d","fee5d9fcae91fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d").map(sK),hK=s7(hW),hU=[,,,].concat("fee6cefdae6be6550d","feeddefdbe85fd8d3cd94701","feeddefdbe85fd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704").map(sK),hJ=s7(hU);function hQ(t){return"rgb("+Math.max(0,Math.min(255,Math.round(-4.54-(t=Math.max(0,Math.min(1,t)))*(35.34-t*(2381.73-t*(6402.7-t*(7024.72-2710.57*t)))))))+", "+Math.max(0,Math.min(255,Math.round(32.49+t*(170.73+t*(52.82-t*(131.46-t*(176.58-67.37*t)))))))+", "+Math.max(0,Math.min(255,Math.round(81.24+t*(442.36-t*(2482.43-t*(6167.24-t*(6614.94-2475.67*t)))))))+")"}var h0=sm(rj(300,.5,0),rj(-240,.5,1)),h1=sm(rj(-100,.75,.35),rj(80,1.5,.8)),h2=sm(rj(260,.75,.35),rj(80,1.5,.8)),h6=rj();function h8(t){(t<0||t>1)&&(t-=Math.floor(t));var n=Math.abs(t-.5);return h6.h=360*t-100,h6.s=1.5-1.5*n,h6.l=.8-.9*n,h6+""}var h3=(0,ed.B8)(),h4=Math.PI/3,h5=2*Math.PI/3;function h7(t){var n;return t=(.5-t)*Math.PI,h3.r=255*(n=Math.sin(t))*n,h3.g=255*(n=Math.sin(t+h4))*n,h3.b=255*(n=Math.sin(t+h5))*n,h3+""}function h9(t){return"rgb("+Math.max(0,Math.min(255,Math.round(34.61+(t=Math.max(0,Math.min(1,t)))*(1172.33-t*(10793.56-t*(33300.12-t*(38394.49-14825.05*t)))))))+", "+Math.max(0,Math.min(255,Math.round(23.31+t*(557.33+t*(1225.33-t*(3574.96-t*(1073.77+707.56*t)))))))+", "+Math.max(0,Math.min(255,Math.round(27.2+t*(3211.1-t*(15327.97-t*(27814-t*(22569.18-6838.66*t)))))))+")"}function dt(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}var dn=dt(sK("44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725")),de=dt(sK("00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf")),dr=dt(sK("00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4")),di=dt(sK("0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921"));function du(t){return nO(nx(t).call(document.documentElement))}var da=0;function dc(){return new df}function df(){this._="@"+(++da).toString(36)}function dl(t,n){return t.target&&(t=nj(t),void 0===n&&(n=t.currentTarget),t=t.touches||[t]),Array.from(t,t=>nI(t,n))}function ds(t){return"string"==typeof t?new nC([document.querySelectorAll(t)],[document.documentElement]):new nC([t2(t)],nE)}df.prototype=dc.prototype={constructor:df,get:function(t){for(var n=this._;!(n in t);)if(!(t=t.parentNode))return;return t[n]},set:function(t,n){return t[this._]=n},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}};var dh=e(98289),dd=e(84038),dp=e(83414);function dv(t){return t.innerRadius}function dg(t){return t.outerRadius}function dy(t){return t.startAngle}function db(t){return t.endAngle}function dm(t){return t&&t.padAngle}function d_(t,n,e,r,i,o,u){var a=t-e,c=n-r,f=(u?o:-o)/(0,dd._b)(a*a+c*c),l=f*c,s=-f*a,h=t+l,d=n+s,p=e+l,v=r+s,g=(h+p)/2,y=(d+v)/2,b=p-h,m=v-d,_=b*b+m*m,x=i-o,w=h*v-p*d,M=(m<0?-1:1)*(0,dd._b)((0,dd.Fp)(0,x*x*_-w*w)),A=(w*m-b*M)/_,T=(-w*b-m*M)/_,S=(w*m+b*M)/_,k=(-w*b+m*M)/_,P=A-g,E=T-y,C=S-g,N=k-y;return P*P+E*E>C*C+N*N&&(A=S,T=k),{cx:A,cy:T,x01:-l,y01:-s,x11:A*(i/x-1),y11:T*(i/x-1)}}function dx(){var t=dv,n=dg,e=(0,dh.Z)(0),r=null,i=dy,o=db,u=dm,a=null,c=(0,dp.d)(f);function f(){var f,l,s=+t.apply(this,arguments),h=+n.apply(this,arguments),d=i.apply(this,arguments)-dd.ou,p=o.apply(this,arguments)-dd.ou,v=(0,dd.Wn)(p-d),g=p>d;if(a||(a=f=c()),h<s&&(l=h,h=s,s=l),h>dd.Ho){if(v>dd.BZ-dd.Ho)a.moveTo(h*(0,dd.mC)(d),h*(0,dd.O$)(d)),a.arc(0,0,h,d,p,!g),s>dd.Ho&&(a.moveTo(s*(0,dd.mC)(p),s*(0,dd.O$)(p)),a.arc(0,0,s,p,d,g));else{var y,b,m=d,_=p,x=d,w=p,M=v,A=v,T=u.apply(this,arguments)/2,S=T>dd.Ho&&(r?+r.apply(this,arguments):(0,dd._b)(s*s+h*h)),k=(0,dd.VV)((0,dd.Wn)(h-s)/2,+e.apply(this,arguments)),P=k,E=k;if(S>dd.Ho){var C=(0,dd.ZR)(S/s*(0,dd.O$)(T)),N=(0,dd.ZR)(S/h*(0,dd.O$)(T));(M-=2*C)>dd.Ho?(C*=g?1:-1,x+=C,w-=C):(M=0,x=w=(d+p)/2),(A-=2*N)>dd.Ho?(N*=g?1:-1,m+=N,_-=N):(A=0,m=_=(d+p)/2)}var R=h*(0,dd.mC)(m),O=h*(0,dd.O$)(m),Z=s*(0,dd.mC)(w),z=s*(0,dd.O$)(w);if(k>dd.Ho){var B,L=h*(0,dd.mC)(_),V=h*(0,dd.O$)(_),D=s*(0,dd.mC)(x),G=s*(0,dd.O$)(x);if(v<dd.pi){if(B=function(t,n,e,r,i,o,u,a){var c=e-t,f=r-n,l=u-i,s=a-o,h=s*c-l*f;if(!(h*h<dd.Ho))return h=(l*(n-o)-s*(t-i))/h,[t+h*c,n+h*f]}(R,O,D,G,L,V,Z,z)){var j=R-B[0],I=O-B[1],q=L-B[0],F=V-B[1],H=1/(0,dd.O$)((0,dd.Kh)((j*q+I*F)/((0,dd._b)(j*j+I*I)*(0,dd._b)(q*q+F*F)))/2),Y=(0,dd._b)(B[0]*B[0]+B[1]*B[1]);P=(0,dd.VV)(k,(s-Y)/(H-1)),E=(0,dd.VV)(k,(h-Y)/(H+1))}else P=E=0}}A>dd.Ho?E>dd.Ho?(y=d_(D,G,R,O,h,E,g),b=d_(L,V,Z,z,h,E,g),a.moveTo(y.cx+y.x01,y.cy+y.y01),E<k?a.arc(y.cx,y.cy,E,(0,dd.fv)(y.y01,y.x01),(0,dd.fv)(b.y01,b.x01),!g):(a.arc(y.cx,y.cy,E,(0,dd.fv)(y.y01,y.x01),(0,dd.fv)(y.y11,y.x11),!g),a.arc(0,0,h,(0,dd.fv)(y.cy+y.y11,y.cx+y.x11),(0,dd.fv)(b.cy+b.y11,b.cx+b.x11),!g),a.arc(b.cx,b.cy,E,(0,dd.fv)(b.y11,b.x11),(0,dd.fv)(b.y01,b.x01),!g))):(a.moveTo(R,O),a.arc(0,0,h,m,_,!g)):a.moveTo(R,O),s>dd.Ho&&M>dd.Ho?P>dd.Ho?(y=d_(Z,z,L,V,s,-P,g),b=d_(R,O,D,G,s,-P,g),a.lineTo(y.cx+y.x01,y.cy+y.y01),P<k?a.arc(y.cx,y.cy,P,(0,dd.fv)(y.y01,y.x01),(0,dd.fv)(b.y01,b.x01),!g):(a.arc(y.cx,y.cy,P,(0,dd.fv)(y.y01,y.x01),(0,dd.fv)(y.y11,y.x11),!g),a.arc(0,0,s,(0,dd.fv)(y.cy+y.y11,y.cx+y.x11),(0,dd.fv)(b.cy+b.y11,b.cx+b.x11),g),a.arc(b.cx,b.cy,P,(0,dd.fv)(b.y11,b.x11),(0,dd.fv)(b.y01,b.x01),!g))):a.arc(0,0,s,w,x,g):a.lineTo(Z,z)}}else a.moveTo(0,0);if(a.closePath(),f)return a=null,f+""||null}return f.centroid=function(){var e=(+t.apply(this,arguments)+ +n.apply(this,arguments))/2,r=(+i.apply(this,arguments)+ +o.apply(this,arguments))/2-dd.pi/2;return[(0,dd.mC)(r)*e,(0,dd.O$)(r)*e]},f.innerRadius=function(n){return arguments.length?(t="function"==typeof n?n:(0,dh.Z)(+n),f):t},f.outerRadius=function(t){return arguments.length?(n="function"==typeof t?t:(0,dh.Z)(+t),f):n},f.cornerRadius=function(t){return arguments.length?(e="function"==typeof t?t:(0,dh.Z)(+t),f):e},f.padRadius=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,dh.Z)(+t),f):r},f.startAngle=function(t){return arguments.length?(i="function"==typeof t?t:(0,dh.Z)(+t),f):i},f.endAngle=function(t){return arguments.length?(o="function"==typeof t?t:(0,dh.Z)(+t),f):o},f.padAngle=function(t){return arguments.length?(u="function"==typeof t?t:(0,dh.Z)(+t),f):u},f.context=function(t){return arguments.length?(a=null==t?null:t,f):a},f}var dw=e(30841),dM=e(20858),dA=e(62825);function dT(t,n){return n<t?-1:n>t?1:n>=t?0:NaN}function dS(t){return t}function dk(){var t=dS,n=dT,e=null,r=(0,dh.Z)(0),i=(0,dh.Z)(dd.BZ),o=(0,dh.Z)(0);function u(u){var a,c,f,l,s,h=(u=(0,dA.Z)(u)).length,d=0,p=Array(h),v=Array(h),g=+r.apply(this,arguments),y=Math.min(dd.BZ,Math.max(-dd.BZ,i.apply(this,arguments)-g)),b=Math.min(Math.abs(y)/h,o.apply(this,arguments)),m=b*(y<0?-1:1);for(a=0;a<h;++a)(s=v[p[a]=a]=+t(u[a],a,u))>0&&(d+=s);for(null!=n?p.sort(function(t,e){return n(v[t],v[e])}):null!=e&&p.sort(function(t,n){return e(u[t],u[n])}),a=0,f=d?(y-h*m)/d:0;a<h;++a,g=l)l=g+((s=v[c=p[a]])>0?s*f:0)+m,v[c]={data:u[c],index:a,value:s,startAngle:g,endAngle:l,padAngle:b};return v}return u.value=function(n){return arguments.length?(t="function"==typeof n?n:(0,dh.Z)(+n),u):t},u.sortValues=function(t){return arguments.length?(n=t,e=null,u):n},u.sort=function(t){return arguments.length?(e=t,n=null,u):e},u.startAngle=function(t){return arguments.length?(r="function"==typeof t?t:(0,dh.Z)(+t),u):r},u.endAngle=function(t){return arguments.length?(i="function"==typeof t?t:(0,dh.Z)(+t),u):i},u.padAngle=function(t){return arguments.length?(o="function"==typeof t?t:(0,dh.Z)(+t),u):o},u}var dP=e(82496),dE=dN(dP.Z);function dC(t){this._curve=t}function dN(t){function n(n){return new dC(t(n))}return n._curve=t,n}function dR(t){var n=t.curve;return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t.curve=function(t){return arguments.length?n(dN(t)):n()._curve},t}function dO(){return dR((0,dM.Z)().curve(dE))}function dZ(){var t=(0,dw.Z)().curve(dE),n=t.curve,e=t.lineX0,r=t.lineX1,i=t.lineY0,o=t.lineY1;return t.angle=t.x,delete t.x,t.startAngle=t.x0,delete t.x0,t.endAngle=t.x1,delete t.x1,t.radius=t.y,delete t.y,t.innerRadius=t.y0,delete t.y0,t.outerRadius=t.y1,delete t.y1,t.lineStartAngle=function(){return dR(e())},delete t.lineX0,t.lineEndAngle=function(){return dR(r())},delete t.lineX1,t.lineInnerRadius=function(){return dR(i())},delete t.lineY0,t.lineOuterRadius=function(){return dR(o())},delete t.lineY1,t.curve=function(t){return arguments.length?n(dN(t)):n()._curve},t}dC.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(t,n){this._curve.point(n*Math.sin(t),-(n*Math.cos(t)))}};var dz=e(95043),dB=e(58326),dL=e(48540);function dV(t){return t.source}function dD(t){return t.target}function dG(t){let n=dV,e=dD,r=dL.x,i=dL.y,o=null,u=null,a=(0,dp.d)(c);function c(){let c;let f=dA.t.call(arguments),l=n.apply(this,f),s=e.apply(this,f);if(null==o&&(u=t(c=a())),u.lineStart(),f[0]=l,u.point(+r.apply(this,f),+i.apply(this,f)),f[0]=s,u.point(+r.apply(this,f),+i.apply(this,f)),u.lineEnd(),c)return u=null,c+""||null}return c.source=function(t){return arguments.length?(n=t,c):n},c.target=function(t){return arguments.length?(e=t,c):e},c.x=function(t){return arguments.length?(r="function"==typeof t?t:(0,dh.Z)(+t),c):r},c.y=function(t){return arguments.length?(i="function"==typeof t?t:(0,dh.Z)(+t),c):i},c.context=function(n){return arguments.length?(null==n?o=u=null:u=t(o=n),c):o},c}function dj(){return dG(dB.sj)}function dI(){return dG(dB.BW)}function dq(){let t=dG(dB.hR);return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t}var dF=e(11563),dH=e(32098),dY=e(80876),d$=e(69263),dX=e(97130),dW=e(73125),dK=e(42891),dU=e(72737),dJ=e(99392),dQ=e(62049),d0=e(78734),d1=e(20367),d2=e(10550),d6=e(1087),d8=e(54266),d3=e(48739),d4=e(80085);function d5(t,n){this._basis=new d4.fE(t),this._beta=n}d5.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,e=t.length-1;if(e>0)for(var r,i=t[0],o=n[0],u=t[e]-i,a=n[e]-o,c=-1;++c<=e;)r=c/e,this._basis.point(this._beta*t[c]+(1-this._beta)*(i+r*u),this._beta*n[c]+(1-this._beta)*(o+r*a));this._x=this._y=null,this._basis.lineEnd()},point:function(t,n){this._x.push(+t),this._y.push(+n)}};var d7=function t(n){function e(t){return 1===n?new d4.fE(t):new d5(t,n)}return e.beta=function(n){return t(+n)},e}(.85),d9=e(87931);function pt(t,n,e){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-e),t._x2,t._y2)}function pn(t,n){this._context=t,this._k=(1-n)/6}pn.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:pt(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2,this._x1=t,this._y1=n;break;case 2:this._point=3;default:pt(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var pe=function t(n){function e(t){return new pn(t,n)}return e.tension=function(n){return t(+n)},e}(0);function pr(t,n){this._context=t,this._k=(1-n)/6}pr.prototype={areaStart:d9.Z,areaEnd:d9.Z,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:pt(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var pi=function t(n){function e(t){return new pr(t,n)}return e.tension=function(n){return t(+n)},e}(0);function po(t,n){this._context=t,this._k=(1-n)/6}po.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:pt(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var pu=function t(n){function e(t){return new po(t,n)}return e.tension=function(n){return t(+n)},e}(0);function pa(t,n,e){var r=t._x1,i=t._y1,o=t._x2,u=t._y2;if(t._l01_a>dd.Ho){var a=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,c=3*t._l01_a*(t._l01_a+t._l12_a);r=(r*a-t._x0*t._l12_2a+t._x2*t._l01_2a)/c,i=(i*a-t._y0*t._l12_2a+t._y2*t._l01_2a)/c}if(t._l23_a>dd.Ho){var f=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,l=3*t._l23_a*(t._l23_a+t._l12_a);o=(o*f+t._x1*t._l23_2a-n*t._l12_2a)/l,u=(u*f+t._y1*t._l23_2a-e*t._l12_2a)/l}t._context.bezierCurveTo(r,i,o,u,t._x2,t._y2)}function pc(t,n){this._context=t,this._alpha=n}pc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:pa(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var pf=function t(n){function e(t){return n?new pc(t,n):new pn(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function pl(t,n){this._context=t,this._alpha=n}pl.prototype={areaStart:d9.Z,areaEnd:d9.Z,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:pa(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var ps=function t(n){function e(t){return n?new pl(t,n):new pr(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function ph(t,n){this._context=t,this._alpha=n}ph.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:pa(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var pd=function t(n){function e(t){return n?new ph(t,n):new po(t,0)}return e.alpha=function(n){return t(+n)},e}(.5),pp=e(31617),pv=e(44393),pg=e(73846),py=e(17732),pb=e(34397),pm=e(92796);function p_(t,n){if((a=t.length)>0)for(var e,r,i,o,u,a,c=0,f=t[n[0]].length;c<f;++c)for(o=u=0,e=0;e<a;++e)(i=(r=t[n[e]][c])[1]-r[0])>0?(r[0]=o,r[1]=o+=i):i<0?(r[1]=u,r[0]=u+=i):(r[0]=0,r[1]=i)}var px=e(60848),pw=e(63478),pM=e(69838),pA=e(91049);function pT(t){var n=t.map(pS);return(0,pA.Z)(t).sort(function(t,e){return n[t]-n[e]})}function pS(t){for(var n,e=-1,r=0,i=t.length,o=-1/0;++e<i;)(n=+t[e][1])>o&&(o=n,r=e);return r}function pk(t){var n=t.map(pP);return(0,pA.Z)(t).sort(function(t,e){return n[t]-n[e]})}function pP(t){for(var n,e=0,r=-1,i=t.length;++r<i;)(n=+t[r][1])&&(e+=n);return e}function pE(t){return pk(t).reverse()}function pC(t){var n,e,r=t.length,i=t.map(pP),o=pT(t),u=0,a=0,c=[],f=[];for(n=0;n<r;++n)e=o[n],u<a?(u+=i[e],c.push(e)):(a+=i[e],f.push(e));return f.reverse().concat(c)}function pN(t){return(0,pA.Z)(t).reverse()}var pR=e(21356),pO=e(56937),pZ=e(2280),pz=e(20727),pB=e(88698),pL=e(54842),pV=e(55764),pD=e(16860),pG=e(1148),pj=e(15313),pI=e(82385),pq=e(76199),pF="%Y-%m-%dT%H:%M:%S.%LZ",pH=Date.prototype.toISOString?function(t){return t.toISOString()}:(0,pI.g0)(pF),pY=+new Date("2000-01-01T00:00:00.000Z")?function(t){var n=new Date(t);return isNaN(n)?null:n}:(0,pI.wp)(pF);function p$(t,n,e){var r=new n1,i=n;return null==n||(r._restart=r.restart,r.restart=function(t,n,e){n=+n,e=null==e?nQ():+e,r._restart(function o(u){u+=i,r._restart(o,i+=n,e),t(u)},n,e)}),r.restart(t,n,e),r}var pX=t=>()=>t;function pW(t,{sourceEvent:n,target:e,transform:r,dispatch:i}){Object.defineProperties(this,{type:{value:t,enumerable:!0,configurable:!0},sourceEvent:{value:n,enumerable:!0,configurable:!0},target:{value:e,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:i}})}function pK(t,n,e){this.k=t,this.x=n,this.y=e}pK.prototype={constructor:pK,scale:function(t){return 1===t?this:new pK(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new pK(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var pU=new pK(1,0,0);function pJ(t){for(;!t.__zoom;)if(!(t=t.parentNode))return pU;return t.__zoom}function pQ(t){t.stopImmediatePropagation()}function p0(t){t.preventDefault(),t.stopImmediatePropagation()}function p1(t){return(!t.ctrlKey||"wheel"===t.type)&&!t.button}function p2(){var t=this;return t instanceof SVGElement?(t=t.ownerSVGElement||t).hasAttribute("viewBox")?[[(t=t.viewBox.baseVal).x,t.y],[t.x+t.width,t.y+t.height]]:[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]:[[0,0],[t.clientWidth,t.clientHeight]]}function p6(){return this.__zoom||pU}function p8(t){return-t.deltaY*(1===t.deltaMode?.05:t.deltaMode?1:.002)*(t.ctrlKey?10:1)}function p3(){return navigator.maxTouchPoints||"ontouchstart"in this}function p4(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],u=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),u>o?(o+u)/2:Math.min(0,o)||Math.max(0,u))}function p5(){var t,n,e,r=p1,i=p2,o=p4,u=p8,a=p3,c=[0,1/0],f=[[-1/0,-1/0],[1/0,1/0]],l=250,s=sf,h=tQ("start","zoom","end"),d=0,p=10;function v(t){t.property("__zoom",p6).on("wheel.zoom",w,{passive:!1}).on("mousedown.zoom",M).on("dblclick.zoom",A).filter(a).on("touchstart.zoom",T).on("touchmove.zoom",S).on("touchend.zoom touchcancel.zoom",k).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(t,n){return(n=Math.max(c[0],Math.min(c[1],n)))===t.k?t:new pK(n,t.x,t.y)}function y(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new pK(t.k,r,i)}function b(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function m(t,n,e,r){t.on("start.zoom",function(){_(this,arguments).event(r).start()}).on("interrupt.zoom end.zoom",function(){_(this,arguments).event(r).end()}).tween("zoom",function(){var t=arguments,o=_(this,t).event(r),u=i.apply(this,t),a=null==e?b(u):"function"==typeof e?e.apply(this,t):e,c=Math.max(u[1][0]-u[0][0],u[1][1]-u[0][1]),f=this.__zoom,l="function"==typeof n?n.apply(this,t):n,h=s(f.invert(a).concat(c/f.k),l.invert(a).concat(c/l.k));return function(t){if(1===t)t=l;else{var n=h(t),e=c/n[2];t=new pK(e,a[0]-n[0]*e,a[1]-n[1]*e)}o.zoom(null,t)}})}function _(t,n,e){return!e&&t.__zooming||new x(t,n)}function x(t,n){this.that=t,this.args=n,this.active=0,this.sourceEvent=null,this.extent=i.apply(t,n),this.taps=0}function w(t,...n){if(r.apply(this,arguments)){var e=_(this,n).event(t),i=this.__zoom,a=Math.max(c[0],Math.min(c[1],i.k*Math.pow(2,u.apply(this,arguments)))),l=nI(t);if(e.wheel)(e.mouse[0][0]!==l[0]||e.mouse[0][1]!==l[1])&&(e.mouse[1]=i.invert(e.mouse[0]=l)),clearTimeout(e.wheel);else{if(i.k===a)return;e.mouse=[l,i.invert(l)],ei(this),e.start()}p0(t),e.wheel=setTimeout(function(){e.wheel=null,e.end()},150),e.zoom("mouse",o(y(g(i,a),e.mouse[0],e.mouse[1]),e.extent,f))}}function M(t,...n){if(!e&&r.apply(this,arguments)){var i=t.currentTarget,u=_(this,n,!0).event(t),a=nO(t.view).on("mousemove.zoom",function(t){if(p0(t),!u.moved){var n=t.clientX-l,e=t.clientY-s;u.moved=n*n+e*e>d}u.event(t).zoom("mouse",o(y(u.that.__zoom,u.mouse[0]=nI(t,i),u.mouse[1]),u.extent,f))},!0).on("mouseup.zoom",function(t){a.on("mousemove.zoom mouseup.zoom",null),nD(t.view,u.moved),p0(t),u.event(t).end()},!0),c=nI(t,i),l=t.clientX,s=t.clientY;nV(t.view),pQ(t),u.mouse=[c,this.__zoom.invert(c)],ei(this),u.start()}}function A(t,...n){if(r.apply(this,arguments)){var e=this.__zoom,u=nI(t.changedTouches?t.changedTouches[0]:t,this),a=e.invert(u),c=e.k*(t.shiftKey?.5:2),s=o(y(g(e,c),u,a),i.apply(this,n),f);p0(t),l>0?nO(this).transition().duration(l).call(m,s,u,t):nO(this).call(v.transform,s,u,t)}}function T(e,...i){if(r.apply(this,arguments)){var o,u,a,c,f=e.touches,l=f.length,s=_(this,i,e.changedTouches.length===l).event(e);for(pQ(e),u=0;u<l;++u)c=[c=nI(a=f[u],this),this.__zoom.invert(c),a.identifier],s.touch0?s.touch1||s.touch0[2]===c[2]||(s.touch1=c,s.taps=0):(s.touch0=c,o=!0,s.taps=1+!!t);t&&(t=clearTimeout(t)),o&&(s.taps<2&&(n=c[0],t=setTimeout(function(){t=null},500)),ei(this),s.start())}}function S(t,...n){if(this.__zooming){var e,r,i,u,a=_(this,n).event(t),c=t.changedTouches,l=c.length;for(p0(t),e=0;e<l;++e)i=nI(r=c[e],this),a.touch0&&a.touch0[2]===r.identifier?a.touch0[0]=i:a.touch1&&a.touch1[2]===r.identifier&&(a.touch1[0]=i);if(r=a.that.__zoom,a.touch1){var s=a.touch0[0],h=a.touch0[1],d=a.touch1[0],p=a.touch1[1],v=(v=d[0]-s[0])*v+(v=d[1]-s[1])*v,b=(b=p[0]-h[0])*b+(b=p[1]-h[1])*b;r=g(r,Math.sqrt(v/b)),i=[(s[0]+d[0])/2,(s[1]+d[1])/2],u=[(h[0]+p[0])/2,(h[1]+p[1])/2]}else{if(!a.touch0)return;i=a.touch0[0],u=a.touch0[1]}a.zoom("touch",o(y(r,i,u),a.extent,f))}}function k(t,...r){if(this.__zooming){var i,o,u=_(this,r).event(t),a=t.changedTouches,c=a.length;for(pQ(t),e&&clearTimeout(e),e=setTimeout(function(){e=null},500),i=0;i<c;++i)o=a[i],u.touch0&&u.touch0[2]===o.identifier?delete u.touch0:u.touch1&&u.touch1[2]===o.identifier&&delete u.touch1;if(u.touch1&&!u.touch0&&(u.touch0=u.touch1,delete u.touch1),u.touch0)u.touch0[1]=this.__zoom.invert(u.touch0[0]);else if(u.end(),2===u.taps&&(o=nI(o,this),Math.hypot(n[0]-o[0],n[1]-o[1])<p)){var f=nO(this).on("dblclick.zoom");f&&f.apply(this,arguments)}}}return v.transform=function(t,n,e,r){var i=t.selection?t.selection():t;i.property("__zoom",p6),t!==i?m(t,n,e,r):i.interrupt().each(function(){_(this,arguments).event(r).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},v.scaleBy=function(t,n,e,r){v.scaleTo(t,function(){var t=this.__zoom.k,e="function"==typeof n?n.apply(this,arguments):n;return t*e},e,r)},v.scaleTo=function(t,n,e,r){v.transform(t,function(){var t=i.apply(this,arguments),r=this.__zoom,u=null==e?b(t):"function"==typeof e?e.apply(this,arguments):e,a=r.invert(u),c="function"==typeof n?n.apply(this,arguments):n;return o(y(g(r,c),u,a),t,f)},e,r)},v.translateBy=function(t,n,e,r){v.transform(t,function(){return o(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),i.apply(this,arguments),f)},null,r)},v.translateTo=function(t,n,e,r,u){v.transform(t,function(){var t=i.apply(this,arguments),u=this.__zoom,a=null==r?b(t):"function"==typeof r?r.apply(this,arguments):r;return o(pU.translate(a[0],a[1]).scale(u.k).translate("function"==typeof n?-n.apply(this,arguments):-n,"function"==typeof e?-e.apply(this,arguments):-e),t,f)},r,u)},x.prototype={event:function(t){return t&&(this.sourceEvent=t),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(t){var n=nO(this.that).datum();h.call(t,this.that,new pW(t,{sourceEvent:this.sourceEvent,target:v,type:t,transform:this.that.__zoom,dispatch:h}),n)}},v.wheelDelta=function(t){return arguments.length?(u="function"==typeof t?t:pX(+t),v):u},v.filter=function(t){return arguments.length?(r="function"==typeof t?t:pX(!!t),v):r},v.touchable=function(t){return arguments.length?(a="function"==typeof t?t:pX(!!t),v):a},v.extent=function(t){return arguments.length?(i="function"==typeof t?t:pX([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),v):i},v.scaleExtent=function(t){return arguments.length?(c[0]=+t[0],c[1]=+t[1],v):[c[0],c[1]]},v.translateExtent=function(t){return arguments.length?(f[0][0]=+t[0][0],f[1][0]=+t[1][0],f[0][1]=+t[0][1],f[1][1]=+t[1][1],v):[[f[0][0],f[0][1]],[f[1][0],f[1][1]]]},v.constrain=function(t){return arguments.length?(o=t,v):o},v.duration=function(t){return arguments.length?(l=+t,v):l},v.interpolate=function(t){return arguments.length?(s=t,v):s},v.on=function(){var t=h.on.apply(h,arguments);return t===h?v:t},v.clickDistance=function(t){return arguments.length?(d=(t=+t)*t,v):Math.sqrt(d)},v.tapDistance=function(t){return arguments.length?(p=+t,v):p},v}pJ.prototype=pK.prototype}}]);