(()=>{var e={};e.id=105,e.ids=[105],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},88505:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(75440),r(78398),r(57757),r(48045);var s=r(40060),a=r(33581),o=r(57567),l=r.n(o),n=r(51650),i={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);r.d(t,i);let c=["",{children:["redirect",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75440)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\redirect\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\redirect\\page.tsx"],m="/redirect/page",u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/redirect/page",pathname:"/redirect",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},69037:(e,t,r)=>{Promise.resolve().then(r.bind(r,74794))},74794:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>S});var s=r(98768),a=r(69424),o=r(26100),l=r(86418),n=r(72548),i=r(79418),c=r(76342),d=r(26660),m=r(60343),u=r(7678),p=r.n(u),g=r(94060),f=r(66263);let S=()=>{let e=(0,a.useSearchParams)().get("t"),t=(0,a.useRouter)(),r=(0,d.x)(),[u,S]=(0,m.useState)(""),[h]=(0,n.D)(c.xUA,{onCompleted:e=>{let t=e.createToken;if(t.token){let e=t.token;localStorage.setItem("sl-jwt",e),localStorage.setItem("firstName",t.member.firstName??""),localStorage.setItem("surname",t.member.surname??""),localStorage.setItem("userId",t.member.id);let s=t.member.superAdmin;localStorage.setItem("superAdmin",s),localStorage.setItem("clientId","0"),localStorage.setItem("useDepartment","false"),localStorage.setItem("usePilotTransfer","false"),localStorage.setItem("useTripSchedule","false");let a=t.member.availableClients??[];localStorage.setItem("availableClients",JSON.stringify(a)),localStorage.setItem("admin","false"),r.defaultContext.token=localStorage?.getItem("sl-jwt")?.toString(),P()}else S("Your username or password is incorrect.")},onError:e=>{console.error("loginUser error",e)}}),x=async e=>{await h({variables:e})},[I]=(0,i.t)(g.jl,{fetchPolicy:"no-cache",onCompleted:e=>{let r=e.readOneSeaLogsMember;if(r){localStorage.setItem("clientId",r.clientID),localStorage.setItem("clientTitle",r.client.title),localStorage.setItem("departmentId",r.currentDepartmentID),localStorage.setItem("useDepartment",r.client.useDepartment),localStorage.setItem("usePilotTransfer",r.client.usePilotTransfer),localStorage.setItem("useTripSchedule",r.client.useTripSchedule),localStorage.setItem("departmentTitle",r.currentDepartment.title);let e=r.groups.nodes.flatMap(e=>e.permissions.nodes).map(e=>e.code);r.superAdmin&&e.push("ADMIN"),localStorage.setItem("permissions",JSON.stringify(e));let s=r.groups.nodes.some(e=>"admin"===e.code)||e.includes("ADMIN");localStorage.setItem("admin",s?"true":"false");let a=r.groups.nodes.some(e=>"crew"===e.code);localStorage.setItem("crew",a?"true":"false"),"true"===localStorage.getItem("superAdmin")?t.push("/select-client?from=login"):t.push("/")}else localStorage.setItem("admin","false"),t.push("/")},onError:e=>{console.error("readOneSeaLogsMember error",e)}}),P=async()=>{await I({variables:{filter:{id:{eq:+(localStorage.getItem("userId")??0)}}}})};return(0,m.useEffect)(()=>{if(e){let{exp:t,username:r,pw:s}=(0,l.o)(e);!t||Date.now()>=1e3*t?S("Your login session has expired. Please login again."):x({email:r,password:s})}},[e]),(0,s.jsxs)("div",{children:[p()(u)&&s.jsx(o.Z,{message:"Redirecting..."}),!p()(u)&&(0,s.jsxs)("div",{children:[s.jsx("div",{children:u}),s.jsx(f.default,{href:"/login",children:"Click here to login again."})]})]})}},75440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\redirect\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,6451,4234,6342],()=>r(88505));module.exports=s})();