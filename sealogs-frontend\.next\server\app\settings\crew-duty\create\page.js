(()=>{var e={};e.id=5287,e.ids=[5287],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},43594:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>d,routeModule:()=>m,tree:()=>u}),r(58953),r(50681),r(78398),r(57757),r(48045);var i=r(40060),s=r(33581),n=r(57567),o=r.n(n),a=r(51650),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u=["",{children:["settings",{children:["crew-duty",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58953)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\crew-duty\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50681)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\crew-duty\\create\\page.tsx"],c="/settings/crew-duty/create/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/settings/crew-duty/create/page",pathname:"/settings/crew-duty/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},34952:(e,t,r)=>{Promise.resolve().then(r.bind(r,8971)),Promise.resolve().then(r.bind(r,20412)),Promise.resolve().then(r.bind(r,93500)),Promise.resolve().then(r.bind(r,97514)),Promise.resolve().then(r.bind(r,45305)),Promise.resolve().then(r.bind(r,44106)),Promise.resolve().then(r.bind(r,58301)),Promise.resolve().then(r.bind(r,67468)),Promise.resolve().then(r.bind(r,40533)),Promise.resolve().then(r.bind(r,57103)),Promise.resolve().then(r.bind(r,69748)),Promise.resolve().then(r.bind(r,39544)),Promise.resolve().then(r.bind(r,8750)),Promise.resolve().then(r.bind(r,81524)),Promise.resolve().then(r.bind(r,48651)),Promise.resolve().then(r.bind(r,24794)),Promise.resolve().then(r.bind(r,27514)),Promise.resolve().then(r.bind(r,83676)),Promise.resolve().then(r.bind(r,57906)),Promise.resolve().then(r.bind(r,60797)),Promise.resolve().then(r.bind(r,39650)),Promise.resolve().then(r.bind(r,15580)),Promise.resolve().then(r.bind(r,24224)),Promise.resolve().then(r.bind(r,26509)),Promise.resolve().then(r.bind(r,69852)),Promise.resolve().then(r.bind(r,36895)),Promise.resolve().then(r.bind(r,52269)),Promise.resolve().then(r.bind(r,70684))},31941:(e,t,r)=>{Promise.resolve().then(r.bind(r,84313))},71241:(e,t,r)=>{var i=r(4171),s=r(96817),n=r(24436),o=Math.max,a=Math.min;e.exports=function(e,t,r){var l,u,d,c,p,m,v=0,x=!1,h=!1,b=!0;if("function"!=typeof e)throw TypeError("Expected a function");function g(t){var r=l,i=u;return l=u=void 0,v=t,c=e.apply(i,r)}function f(e){var r=e-m,i=e-v;return void 0===m||r>=t||r<0||h&&i>=d}function y(){var e,r,i,n=s();if(f(n))return P(n);p=setTimeout(y,(e=n-m,r=n-v,i=t-e,h?a(i,d-r):i))}function P(e){return(p=void 0,b&&l)?g(e):(l=u=void 0,c)}function w(){var e,r=s(),i=f(r);if(l=arguments,u=this,m=r,i){if(void 0===p)return v=e=m,p=setTimeout(y,t),x?g(e):c;if(h)return clearTimeout(p),p=setTimeout(y,t),g(m)}return void 0===p&&(p=setTimeout(y,t)),c}return t=n(t)||0,i(r)&&(x=!!r.leading,d=(h="maxWait"in r)?o(n(r.maxWait)||0,t):d,b="trailing"in r?!!r.trailing:b),w.cancel=function(){void 0!==p&&clearTimeout(p),v=0,l=m=u=p=void 0},w.flush=function(){return void 0===p?c:P(s())},w}},96817:(e,t,r)=>{var i=r(65584);e.exports=function(){return i.Date.now()}},24436:(e,t,r)=>{var i=r(49513),s=r(4171),n=r(15903),o=0/0,a=/^[-+]0x[0-9a-f]+$/i,l=/^0b[01]+$/i,u=/^0o[0-7]+$/i,d=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(n(e))return o;if(s(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=s(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=i(e);var r=l.test(e);return r||u.test(e)?d(e.slice(2),r?2:8):a.test(e)?o:+e}},84313:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var i=r(98768);r(60343);var s=r(64837);function n({children:e}){return i.jsx(s.Z,{children:e})}},67468:(e,t,r)=>{"use strict";r.d(t,{default:()=>j});var i=r(98768),s=r(72548),n=r(60343),o=r(71241),a=r.n(o),l=r(7678),u=r.n(l),d=r(14826),c=r.n(d),p=r(69424),m=r(76342),v=r(66263),x=r(78965),h=r(13842),b=r(46776),g=r(71890),f=r(57103),y=r(17203),P=r(81311),w=r(74602),C=r(39544);let j=({crewDutyId:e,onCancel:t,onCreate:r,isPopup:o=!1})=>{let[l,d]=(0,n.useState)({}),j=(0,p.useRouter)(),[D,q]=(0,n.useState)(!1),[T,N]=(0,n.useState)({title:"",abbreviation:""}),[_,S]=(0,n.useState)(!1),[U,B]=(0,n.useState)(!1);(0,h.UL)(e,d);let k=a()((t,r)=>{d({...l,[t]:r,id:+e})},300),E=e=>{let{name:t,value:r}=e.target;k(t,r)},M=async()=>{let t=!1,r={title:"",abbreviation:""};if(u()(c()(l.title))&&(t=!0,r.title="Title is required"),u()(c()(l.abbreviation))&&(t=!0,r.abbreviation="Abbreviation is required"),t){q(!0),N(r);return}let i={input:{id:+l.id,title:l.title,abbreviation:l.abbreviation}};0===e?await L({variables:i}):await V({variables:i})},[L,{loading:A}]=(0,s.D)(m.fJx,{onCompleted:e=>{let t=e.createCrewDuty;t.id>0?r?r(t):j.back():console.error("mutationCreateCrewDuty error",e)},onError:e=>{console.error("mutationCreateCrewDuty error",e)}}),[V,{loading:R}]=(0,s.D)(m.Qem,{onCompleted:e=>{e.updateCrewDuty.id>0?j.back():console.error("mutationUpdateCrewDuty error",e)},onError:e=>{console.error("mutationUpdateCrewDuty error",e)}}),$=async e=>{await G({variables:{ids:[e.id]}})},[G]=(0,s.D)(m.T5T,{onCompleted:()=>{j.push("/settings/crew-duty/list")},onError:e=>{console.error("mutationDeleteCrewDuty error",e)}});return(0,n.useEffect)(()=>{(0,b.UU)()},[]),(0,i.jsxs)(i.Fragment,{children:[i.jsx("div",{className:" flex justify-between pb-4 pt-3 items-center",children:(0,i.jsxs)(w.H3,{children:[0===e?"Create":"Edit"," Crew Duty"]})}),(0,i.jsxs)("div",{className:`${o?"grid-cols-2":"grid-cols-3"} grid gap-6`,children:[(0,i.jsxs)("div",{className:`${o?"hidden":""} my-4 `,children:["Crew duty details",i.jsx("p",{className:" mt-4 max-w-[25rem] leading-loose",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Esse minima maxime enim, consectetur hic est perferendis explicabo suscipit rem reprehenderit vitae ex sunt corrupti obcaecati aliquid natus et inventore tenetur?"})]}),(0,i.jsxs)("div",{className:"w-full grid col-span-2 gap-4",children:[(0,i.jsxs)("div",{children:[i.jsx(g.I,{name:"title",type:"text",required:!0,defaultValue:l?.title||"",onChange:E,placeholder:"Duty Title"}),i.jsx("small",{className:"text-destructive",children:D&&T.title})]}),(0,i.jsxs)("div",{children:[i.jsx(g.I,{name:"abbreviation",type:"text",placeholder:"Abbreviation",required:!0,defaultValue:l?.abbreviation||"",onChange:E}),i.jsx("small",{className:"text-destructive",children:D&&T.abbreviation})]})]})]}),o?(0,i.jsxs)("div",{className:"flex justify-end pt-8 gap-2.5",children:[i.jsx(C.Button,{variant:"back",onClick:t,children:"Cancel"}),0!==e&&(0,i.jsxs)(i.Fragment,{children:[i.jsx(C.Button,{variant:"destructive",onClick:()=>{B(!0)},children:"Delete"}),i.jsx(f.AlertDialogNew,{openDialog:U,setOpenDialog:B,title:"Delete Crew Duty",description:`Are you sure you want to delete "${l.title}"?`,variant:"danger",actionText:"Delete",handleCreate:()=>$(l)})]}),i.jsx(C.Button,{onClick:M,disabled:A||R,children:`${0===e?"Create":"Update"} Duty`})]}):(0,i.jsxs)(x.V,{children:[i.jsx(v.default,{href:"/settings/crew-duty/list",children:i.jsx(C.Button,{variant:"back",iconLeft:y.Z,className:"mr-2",children:"Cancel"})}),0!==e&&(0,i.jsxs)(i.Fragment,{children:[i.jsx(C.Button,{variant:"destructive",onClick:()=>{S(!0)},children:"Delete"}),i.jsx(f.AlertDialogNew,{openDialog:_,setOpenDialog:S,title:"Delete Crew Duty",description:`Are you sure you want to delete "${l.title}"?`,variant:"danger",actionText:"Delete",handleCreate:()=>$(l)})]}),i.jsx(C.Button,{iconLeft:P.Z,onClick:M,disabled:A||R,children:`${0===e?"Create":"Update"} Duty`})]})]})}},58953:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var i=r(96141);let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\crew-duty\form.tsx#default`);var n=r(73501);let o=()=>i.jsx(n.Zb,{children:i.jsx(n.aY,{children:i.jsx(s,{crewDutyId:0})})})},50681:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\layout.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,1484,5206,6451,4234,2925,5394,4837,6342,3842,3501],()=>r(43594));module.exports=i})();