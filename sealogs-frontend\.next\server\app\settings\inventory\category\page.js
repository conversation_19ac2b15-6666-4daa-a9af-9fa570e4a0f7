(()=>{var e={};e.id=3757,e.ids=[3757],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},79204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>g,tree:()=>c}),r(97604),r(50681),r(78398),r(57757),r(48045);var s=r(40060),a=r(33581),n=r(57567),i=r.n(n),o=r(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["settings",{children:["inventory",{children:["category",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,97604)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\inventory\\category\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50681)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\inventory\\category\\page.tsx"],u="/settings/inventory/category/page",p={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/settings/inventory/category/page",pathname:"/settings/inventory/category",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},78849:(e,t,r)=>{Promise.resolve().then(r.bind(r,27843))},31941:(e,t,r)=>{Promise.resolve().then(r.bind(r,84313))},27843:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var s=r(98768);r(46776);var a=r(60343),n=r(72548),i=r(76342),o=r(69424),l=r(13842),c=r(71890),d=r(39544),u=r(57103),p=r(78965),g=r(34376),x=r(81311),m=r(25394);function h({categoryID:e}){let t=(0,o.useRouter)(),[r,h]=(0,a.useState)(),[y,v]=(0,a.useState)(!1),{toast:f}=(0,g.pm)(),[j,b]=(0,a.useState)(!1),[C,w]=(0,a.useState)(!1);(0,l.p9)(e,h);let N=async()=>{let t=document.getElementById("category-name")?.value,r=document.getElementById("category-abbr")?.value;if(""!==t)return b(!0),await q({variables:{input:{id:+e,name:t,abbreviation:r}}});f({variant:"destructive",title:"Error",description:"Category name cannot be empty"})},[q,{loading:P}]=(0,n.D)(i.TB5,{onCompleted:e=>{b(!1),e.updateInventoryCategory.id>0?t.back():(f({variant:"destructive",title:"Error",description:"Failed to update category"}),console.error("mutationupdateInventoryCategory error",e))},onError:e=>{b(!1),f({variant:"destructive",title:"Error",description:"Failed to update category"}),console.error("mutationupdateInventoryCategory error",e)}}),k=async e=>{w(!0),await I({variables:{ids:[+e]}})},[I,{loading:M}]=(0,n.D)(i.pFH,{onCompleted:e=>{e.deleteInventoryCategories?t.push("/settings/inventory/category"):(f({variant:"destructive",title:"Error",description:"Failed to delete category"}),console.error("mutationDeleteInventoryCategory error",e))},onError:e=>{w(!1),f({variant:"destructive",title:"Error",description:"Failed to delete category"}),console.error("mutationDeleteInventoryCategory error",e)}});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(m.Zb,{className:"mb-4",children:[s.jsx(m.H3,{className:"text-xl",children:"Edit Inventory Category"}),s.jsx("div",{className:"grid grid-cols-2",children:(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[s.jsx(m.__,{label:"Category name",children:s.jsx(c.I,{id:"category-name",type:"text",defaultValue:r?.name,placeholder:"Category name"})}),s.jsx(m.__,{label:"Abbreviation",children:s.jsx(c.I,{id:"category-abbr",type:"text",defaultValue:r?.abbreviation,placeholder:"Abbreviation"})})]})})]}),(0,s.jsxs)(p.V,{children:[s.jsx(d.Button,{variant:"back",onClick:()=>t.push("/settings/inventory/category"),children:"Cancel"}),s.jsx(d.Button,{variant:"destructive",onClick:()=>v(!0),children:"Delete"}),s.jsx(d.Button,{iconLeft:x.Z,onClick:N,isLoading:j,children:"Update Category"})]}),s.jsx(u.AlertDialogNew,{openDialog:y,setOpenDialog:v,title:"Delete Inventory Category",description:`Are you sure you want to delete "${r?.name}"?`,handleCreate:()=>k(r?.id),actionText:"Delete",cancelText:"Cancel",variant:"danger",loading:C})]})}var y=r(15580),v=r(97428);let f=(0,v.Z)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]),j=(0,v.Z)("Tags",[["path",{d:"m15 5 6.3 6.3a2.4 2.4 0 0 1 0 3.4L17 19",key:"1cbfv1"}],["path",{d:"M9.586 5.586A2 2 0 0 0 8.172 5H3a1 1 0 0 0-1 1v5.172a2 2 0 0 0 .586 1.414L8.29 18.29a2.426 2.426 0 0 0 3.42 0l3.58-3.58a2.426 2.426 0 0 0 0-3.42z",key:"135mg7"}],["circle",{cx:"6.5",cy:"9.5",r:".5",fill:"currentColor",key:"5pm5xn"}]]);var b=r(66263),C=r(79418),w=r(94060),N=r(51742),q=r(30905),P=r(39650),k=r(26100),I=r(27514),M=r(22995),_=r(9210);let D=()=>{let{isMobile:e}=(0,M.Ap)();return(0,s.jsxs)(I.DropdownMenu,{children:[s.jsx(I.DropdownMenuTrigger,{asChild:!0,children:s.jsx(_.HV,{size:36})}),s.jsx(I.DropdownMenuContent,{side:e?"bottom":"right",align:e?"end":"start",children:s.jsx(b.default,{href:"/settings/inventory/category/new",children:s.jsx(I.DropdownMenuItem,{children:"New Category"})})})]})};function S(){let[e,t]=(0,a.useState)([]),[r,n]=(0,a.useState)(!0),[i]=(0,C.t)(w.I,{fetchPolicy:"cache-and-network",onCompleted:e=>{let r=e.readInventoryCategories.nodes;r&&t(r),n(!1)},onError:e=>{console.error("queryCategories error",e),n(!1)}}),o=(0,N.wu)([{accessorKey:"name",header:({column:e})=>s.jsx(q.u,{column:e,title:"Name"}),cellAlignment:"left",cell:({row:e})=>{let t=e.original;return(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(b.default,{href:`/settings/inventory/category?categoryID=${t.id}`,className:`hover:underline ${t.archived?"line-through text-muted-foreground":""}`,children:t.name}),t.archived&&s.jsx(m.Ct,{variant:"secondary",className:"ml-2 text-xs",children:"Archived"})]})},sortingFn:(e,t)=>{let r=e?.original?.name||"",s=t?.original?.name||"";return r.localeCompare(s)}},{accessorKey:"abbreviation",header:"Abbreviation",cell:({row:e})=>{let t=e.original;return s.jsx("div",{className:"text-sm",children:t.abbreviation||"-"})}},{accessorKey:"inventories",header:"Recent Inventories",cellAlignment:"right",cell:({row:e})=>{let t=e.original,r=t.inventories?.nodes?.length||0;return 0===r?s.jsx("span",{className:"text-sm text-muted-foreground",children:"-"}):(0,s.jsxs)(y.Popover,{children:[s.jsx(y.PopoverTrigger,{asChild:!0,children:s.jsx(d.Button,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:s.jsx(f,{className:"h-4 w-4"})})}),s.jsx(y.PopoverContent,{className:"w-80",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)(m.P,{className:"font-medium text-sm",children:["Inventories (",r,")"]}),s.jsx("div",{className:"space-y-1 max-h-48 overflow-y-auto",children:t.inventories.nodes.map(e=>s.jsx(b.default,{href:`/inventory/view?id=${e.id}`,className:"block py-1 px-2 text-sm hover:bg-muted rounded",children:e.title},e.id))})]})})]})}}]);return(0,s.jsxs)(s.Fragment,{children:[s.jsx(P.ListHeader,{icon:s.jsx(j,{className:"h-12 w-12 ring-1 p-1 text-primary bg-[#fff] rounded-full"}),title:"Inventory categories",actions:s.jsx(D,{})}),s.jsx("div",{className:"mt-16",children:r?s.jsx(k.Z,{}):s.jsx(N.wQ,{columns:o,data:e,showToolbar:!1,pageSize:20})})]})}function E(){let e=(0,o.useSearchParams)().get("categoryID")??0;return s.jsx(s.Fragment,{children:0==+e?s.jsx(S,{}):s.jsx(h,{categoryID:+e})})}},84313:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(98768);r(60343);var a=r(64837);function n({children:e}){return s.jsx(a.Z,{children:e})}},97604:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\inventory\category\page.tsx#default`)},50681:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\layout.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7944],()=>r(79204));module.exports=s})();