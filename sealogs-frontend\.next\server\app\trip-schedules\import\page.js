(()=>{var e={};e.id=3449,e.ids=[3449],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},4375:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),r(49549),r(17790),r(78398),r(57757),r(48045);var s=r(40060),i=r(33581),a=r(57567),o=r.n(a),l=r(51650),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let c=["",{children:["trip-schedules",{children:["import",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49549)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-schedules\\import\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,17790)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-schedules\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-schedules\\import\\page.tsx"],u="/trip-schedules/import/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/trip-schedules/import/page",pathname:"/trip-schedules/import",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},84126:(e,t,r)=>{Promise.resolve().then(r.bind(r,77211))},60850:(e,t,r)=>{Promise.resolve().then(r.bind(r,99027))},60826:e=>{e.exports=function(e){return e.split("")}},829:e=>{e.exports=function(e,t,r,s){for(var i=e.length,a=r+(s?1:-1);s?a--:++a<i;)if(t(e[a],a,e))return a;return -1}},65337:(e,t,r)=>{var s=r(829),i=r(35447),a=r(28026);e.exports=function(e,t,r){return t==t?a(e,t,r):s(e,i,r)}},35447:e=>{e.exports=function(e){return e!=e}},77420:e=>{e.exports=function(e,t,r){var s=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(r=r>i?i:r)<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++s<i;)a[s]=e[s+t];return a}},49513:(e,t,r)=>{var s=r(70458),i=/^\s+/;e.exports=function(e){return e?e.slice(0,s(e)+1).replace(i,""):e}},30482:(e,t,r)=>{var s=r(77420);e.exports=function(e,t,r){var i=e.length;return r=void 0===r?i:r,!t&&r>=i?e:s(e,t,r)}},74783:(e,t,r)=>{var s=r(65337);e.exports=function(e,t){for(var r=e.length;r--&&s(t,e[r],0)>-1;);return r}},41200:(e,t,r)=>{var s=r(65337);e.exports=function(e,t){for(var r=-1,i=e.length;++r<i&&s(t,e[r],0)>-1;);return r}},73211:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},28026:e=>{e.exports=function(e,t,r){for(var s=r-1,i=e.length;++s<i;)if(e[s]===t)return s;return -1}},66095:(e,t,r)=>{var s=r(60826),i=r(73211),a=r(92115);e.exports=function(e){return i(e)?a(e):s(e)}},70458:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},92115:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",s="\ud83c[\udffb-\udfff]",i="[^"+t+"]",a="(?:\ud83c[\udde6-\uddff]){2}",o="[\ud800-\udbff][\udc00-\udfff]",l="(?:"+r+"|"+s+")?",n="[\\ufe0e\\ufe0f]?",c="(?:\\u200d(?:"+[i,a,o].join("|")+")"+n+l+")*",d=RegExp(s+"(?="+s+")|(?:"+[i+r+"?",r,a,o,"["+t+"]"].join("|")+")"+(n+l+c),"g");e.exports=function(e){return e.match(d)||[]}},14826:(e,t,r)=>{var s=r(22060),i=r(49513),a=r(30482),o=r(74783),l=r(41200),n=r(66095),c=r(16266);e.exports=function(e,t,r){if((e=c(e))&&(r||void 0===t))return i(e);if(!e||!(t=s(t)))return e;var d=n(e),u=n(t),p=l(d,u),m=o(d,u)+1;return a(d,p,m).join("")}},77211:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P});var s=r(98768),i=r(46776),a=r(26100),o=r(13842),l=r(60343),n=r(46347),c=r(7678),d=r.n(c),u=r(14826),p=r.n(u),m=r(69424),f=r(72548),h=r(79418),x=r(76342),v=r(94060),g=r(74602),b=r(71890),j=r(60797),y=r(34376),S=r(13609),N=r(3510),T=r(81524),D=r(26509),w=r(25394);let E=()=>{let e=(0,m.useRouter)(),t=(0,m.useSearchParams)().get("id")??0,{toast:r}=(0,y.pm)(),i=[{value:"WeekDays",label:"Days of the Week"}],[a,c]=(0,l.useState)({id:t,title:"",scheduleType:i[0].value}),[u,E]=(0,l.useState)(i[0]),[P,q]=(0,l.useState)([]),[C,R]=(0,l.useState)([]),[k,I]=(0,l.useState)([]),[L,U]=(0,l.useState)(!1),[A,F]=(0,l.useState)(!1),[_,M]=(0,l.useState)(!1),[B,O]=(0,l.useState)(!1),[V,{loading:Y}]=(0,f.D)(x.Vq7,{onCompleted:t=>{t.deleteTripScheduleImports?(O(!1),r({title:"Success",description:"Trip Schedule Import deleted successfully"}),e.push("/trip-schedules")):(O(!1),r({variant:"destructive",title:"Error",description:"Error deleting Trip Schedule Import"}))},onError:e=>{O(!1),r({variant:"destructive",title:"Error",description:`Error: ${e.message}`}),console.error("deleteTripScheduleImport onError",e.message)}}),G=()=>{let e="";return d()(p()(a.title))&&(e+="\nThe title is required."),d()(C)&&(e+="\nAt least one vessel is required."),d()(p()(a.R2FileID))&&(e+="\nPlease select a CSV file to import."),!!d()(p()(e))||(r({variant:"destructive",title:"Validation Error",description:p()(e)}),!1)},[Z,{loading:W}]=(0,f.D)(x.UB7,{onCompleted:()=>{e.push("/trip-schedules")},onError:e=>{r({variant:"destructive",title:"Error",description:e.message}),console.error("createTripScheduleImport onError",e.message)}}),[H,{loading:K}]=(0,f.D)(x.cNX,{onCompleted:()=>{e.push("/trip-schedules")},onError:e=>{r({variant:"destructive",title:"Error",description:e.message}),console.error("updateTripScheduleImport onError",e.message)}}),$=async()=>{G()&&(0==+t?await Z({variables:{input:a}}):(L&&(a.archiveTripReportSchedule=L),A&&(a.archiveTripScheduleService=A),_&&(a.archiveLocation=_),await H({variables:{input:a}})))},[z]=(0,h.t)(v.dH,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneTripScheduleImport;if(t){let e=t.vehicles.nodes.map(e=>e.id);c({id:t.id,title:t.title,scheduleType:t.scheduleType,R2FileID:t.R2FileID,vehicles:e.join(",")}),I([t.R2File])}},onError:e=>{console.error("readOneTripScheduleImport error",e)}});(0,o.sy)(e=>{q(e.map(e=>({label:e.title,value:e.id})))});let X=async()=>{await z({variables:{id:t}})},[J,{loading:Q}]=(0,f.D)(x.q$t,{onCompleted:e=>{let t=e.createR2File,r=k.map(e=>e.title===t.title?{...e,id:t.id}:e);I(r),c({...a,R2FileID:r[0].id})},onError:e=>{console.error("Error creating R2 file",e)}});return(0,l.useEffect)(()=>{k.length>0&&k.map(e=>{e.id||J({variables:{input:{title:e.title}}})})},[k]),(0,l.useEffect)(()=>{0!=+t&&X()},[t]),(0,l.useEffect)(()=>{d()(P)||d()(a.vehicles)||R(a.vehicles.split(",").map(e=>P.find(t=>+t.value==+e)))},[P,a]),s.jsx("div",{className:"w-full",children:(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx("div",{className:"flex justify-between items-center",children:(0,s.jsxs)(g.H3,{children:[+t>0?"Edit":"New"," Trip Schedule Import"]})}),s.jsx(D.Separator,{}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(j.Label,{htmlFor:"title",children:"Title"}),s.jsx(b.I,{id:"title",defaultValue:a.title,onChange:e=>{c({...a,title:e.target.value})},type:"text",placeholder:"Import Title"})]}),s.jsx("div",{className:"space-y-2",children:s.jsx(T.Combobox,{id:"schedule-type",options:i.map(e=>({...e,value:String(e.value)})),value:u,onChange:e=>{e&&!Array.isArray(e)&&(E(e),c({...a,scheduleType:e.value}))},placeholder:"Schedule Type",label:"Schedule Type"})}),s.jsx("div",{className:"space-y-2",children:s.jsx(T.Combobox,{id:"vessel-list",options:P.map(e=>({...e,value:String(e.value)})),value:C,onChange:e=>{e&&Array.isArray(e)&&(R(e),c({...a,vehicles:e.map(e=>e.value).join(",")}))},placeholder:"Select Vessels",label:"Vessels",multi:!0})}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(j.Label,{htmlFor:"import-file",children:"Import File"}),s.jsx(n.Z,{files:k,setFiles:I,multipleUpload:!1})]}),s.jsx(D.Separator,{className:"my-6"}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(g.H4,{children:"Import File Guidelines"}),(0,s.jsxs)("ul",{className:"list-disc pl-6 space-y-3",children:[s.jsx("li",{children:"Must be a comma separated (.csv) file."}),(0,s.jsxs)("li",{children:[s.jsx(g.P,{className:"mb-2",children:"Depending on Schedule Type selected above the column format should be:"}),(0,s.jsxs)("ul",{className:"list-disc pl-8 space-y-4",children:[(0,s.jsxs)("li",{children:[s.jsx("p",{className:"font-medium mb-1",children:"Every Day:"}),s.jsx("div",{className:"bg-muted p-2 rounded",children:s.jsx("code",{className:"text-xs",children:'"Service Name","Transit Route ID","Transit Trip ID","Depart Location","Depart Time","Destination","Arrival Time","Start Date","End Date","Stop1 Name","Stop1 ArriveTime","Stop1 DepartTime","Stop1 PickUp","Stop1 DropOff","Stop2 Name","Stop2 ArriveTime","Stop2 DepartTime","Stop2 PickUp","Stop2 DropOff"'})})]}),(0,s.jsxs)("li",{children:[s.jsx("p",{className:"font-medium mb-1",children:"Days of the Week:"}),s.jsx("div",{className:"bg-muted p-2 rounded",children:s.jsx("code",{className:"text-xs",children:'"Service Name","Transit Route ID","Transit Trip ID","Depart Location","Depart Time","Destination","Arrival Time","Start Date","End Date","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday","Public Holidays","Stop1 Name","Stop1 ArriveTime","Stop1 DepartTime","Stop1 PickUp","Stop1 DropOff","Stop2 Name","Stop2 ArriveTime","Stop2 DepartTime","Stop2 PickUp","Stop2 DropOff"'})})]})]})]}),s.jsx("li",{children:"Service Name must be included but if there is no Service Name set this to 0."}),s.jsx("li",{children:"Every column up to the Stops columns is required, the Days of Week/Month should be set to 1 for on and 0 for off."}),s.jsx("li",{children:'The "Start Date" and "End Date" should be in the format YYYY-MM-DD or in the format DD/MM/YYYY.'}),s.jsx("li",{children:"Stops are optional. If including Stops, they must come in column sets of five as demonstrated above even if some of the columns are empty but the Stop Name is required to create a Stop."}),s.jsx("li",{children:"All rows must have the same number of columns (this will happen automatically when exporting from a spreadsheet)."}),s.jsx("li",{children:"Column values do not need to be enclosed in quotes."}),s.jsx("li",{children:"Remove any empty rows from the bottom of the spreadsheet before exporting."})]})]}),s.jsx(D.Separator,{className:"my-6"}),(0,s.jsxs)("div",{className:"flex justify-end gap-2",children:[s.jsx(w.zx,{variant:"text",iconLeft:S.Z,onClick:()=>e.push("/trip-schedules"),children:"Cancel"}),(0,s.jsxs)(w.zx,{iconLeft:N.Z,isLoading:W||K||Y||Q,onClick:$,children:[+t>0?"Update":"Save"," Import"]})]})]})})},P=()=>{let[e,t]=(0,l.useState)(!0);return(0,l.useEffect)(()=>{t((0,i.E7)())},[]),s.jsx("div",{children:e?s.jsx(E,{}):s.jsx(a.Z,{message:"Trip schedules are not enabled. Please enable them in the settings to use this feature."})})}},99027:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(98768);r(60343);var i=r(64837);function a({children:e}){return s.jsx(i.Z,{children:e})}},46347:(e,t,r)=>{"use strict";r.d(t,{Z:()=>h});var s=r(98768),i=r(60343),a=r(28147),o=r(83048),l=r.n(o),n=r(25394),c=r(67537),d=r(71890),u=r(56937);function p({files:e,onFilesSelected:t,text:r="Documents and Images",subText:o,bgClass:l="",multipleUpload:n=!0,acceptedFileTypes:p=".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf,.csv",isLoading:m=!1,renderFileItem:f,onFileClick:h,children:x,displayFiles:v=!0}){let[g,b]=(0,i.useState)(!1),j=(0,i.useRef)(null),y=(0,u.cn)("relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none",g?"bg-accent border-primary":"bg-accent/50 border-border","text-foreground hover:bg-accent hover:border-primary","min-h-[10rem] cursor-pointer select-none",l),S=e=>{t(e)},N=e=>t=>{t.preventDefault(),b(e)},T=(e,t)=>(0,s.jsxs)("div",{onClick:()=>h?.(e),className:"flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden",children:[s.jsx(a.default,{src:"/sealogs-document_upload.svg",alt:"Document",width:48,height:48,className:"mb-2"}),s.jsx("div",{className:"text-xs text-center break-all text-muted-foreground",children:e.title})]},t);return(0,s.jsxs)("div",{className:"w-full pt-4 lg:pt-0",children:[e.length>0&&v&&s.jsx("div",{className:"flex flex-wrap gap-4 mb-4",children:e.filter(e=>e.title?.length>0).map((e,t)=>f?f(e,t):T(e,t))}),(0,s.jsxs)("form",{className:y,onSubmit:e=>e.preventDefault(),onDragEnter:N(!0),onDragOver:N(!0),onDragLeave:N(!1),onDrop:e=>{e.preventDefault(),b(!1),e.dataTransfer.files&&S(e.dataTransfer.files)},onClick:()=>{j.current&&(j.current.value="",j.current.click())},"aria-label":"File uploader drop zone",children:[s.jsx("span",{className:"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider",children:r}),s.jsx(d.I,{ref:j,type:"file",className:"hidden",multiple:n,accept:p,onChange:e=>{e.target.files&&S(e.target.files)}}),(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2 pointer-events-none",children:[s.jsx(a.default,{src:"/sealogs-document_upload.svg",alt:"Upload illustration",width:96,height:96,className:"relative -translate-x-2.5",priority:!0}),o&&s.jsx("span",{className:"text-sm font-medium text-neutral-400",children:o})]})]}),m&&(0,s.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2",children:[s.jsx(c.Z,{className:"h-5 w-5 animate-spin text-primary"}),s.jsx("span",{className:"text-sm text-muted-foreground",children:"Uploading..."})]}),x]})}var m=r(34376);let f=new(l()).S3({endpoint:"https://ddde1c1cd1aa25641691808dcbafdeb7.r2.cloudflarestorage.com",accessKeyId:"06c3e13a539f24e6fdf7075bf381bf5e",secretAccessKey:"****************************************************************",signatureVersion:"v4",region:"auto"});function h({files:e=[],setFiles:t,multipleUpload:r=!0,text:o="Documents and Images",subText:l,bgClass:c="",accept:d=".xlsx,.xls,image/*,.doc, .docx,.ppt, .pptx,.txt,.pdf,.csv",bucketName:u="sealogs",prefix:h="",displayFiles:x=!0}){let[v,g]=(0,i.useState)(!1),[b,j]=(0,i.useState)(""),[y,S]=(0,i.useState)(!1),[N,T]=(0,i.useState)(0),D=async s=>{let i=N+"-"+h+s.name;if(e?.some(e=>e.title===i)){(0,m.Am)({description:"File with same name already exists!",variant:"destructive"});return}g(!0),f.putObject({Bucket:u,Key:i,Body:s},(e,s)=>{if(g(!1),e)console.error(e),(0,m.Am)({description:"Failed to upload file",variant:"destructive"});else{let e={title:i};r?t(t=>[...t,e]):t([e])}})},w=e=>{f.getObject({Bucket:u,Key:e.title},async(t,r)=>{if(t)console.error(t),(0,m.Am)({description:"Failed to download file",variant:"destructive"});else{let t=e.title.split(".").pop()||"",s=new Blob([r?.Body]),i=URL.createObjectURL(s);if(t.match(/^(jpg|jpeg|png|gif|bmp)$/i))j(i),S(!0);else if(t.match(/^(pdf)$/i)){let e=new Blob([r?.Body],{type:"application/pdf"}),t=URL.createObjectURL(e);window.open(t,"_blank"),URL.revokeObjectURL(t)}else{(0,m.Am)({description:"File type not supported to view. Please save the file to view.",variant:"destructive"});let t=document.createElement("a");t.target="_blank",t.href=i,t.download=e.title,t.click(),URL.revokeObjectURL(i)}}})};return s.jsx(p,{files:e,onFilesSelected:e=>{Array.from(e).forEach(D)},text:o,subText:l,bgClass:c,multipleUpload:r,acceptedFileTypes:d,isLoading:v,renderFileItem:(e,t)=>(0,s.jsxs)("div",{onClick:()=>w(e),className:"flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden",children:[s.jsx(a.default,{src:"/sealogs-document_upload.svg",alt:"Document",width:48,height:48,className:"mb-2"}),s.jsx("div",{className:"text-xs text-center break-all text-muted-foreground",children:e.title.replace(N+"-","")})]},t),displayFiles:x,onFileClick:w,children:s.jsx(n.h9,{openDialog:y,setOpenDialog:S,noButton:!0,actionText:"Close",title:"Image Preview",children:s.jsx("div",{className:"flex items-center justify-center",children:s.jsx("img",{src:b,alt:"Preview",className:"max-w-full max-h-96 object-contain"})})})})}},49549:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\trip-schedules\import\page.tsx#default`)},17790:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\trip-schedules\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,6451,4234,2925,5394,4837,6342,3842],()=>r(4375));module.exports=s})();