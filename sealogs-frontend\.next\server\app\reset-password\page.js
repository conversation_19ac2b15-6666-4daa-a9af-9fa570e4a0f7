(()=>{var e={};e.id=2363,e.ids=[2363],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},81273:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>d}),r(63711),r(78398),r(57757),r(48045);var a=r(40060),t=r(33581),o=r(57567),n=r.n(o),i=r(51650),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(s,l);let d=["",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,63711)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reset-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reset-password\\page.tsx"],x="/reset-password/page",p={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},17884:(e,s,r)=>{Promise.resolve().then(r.bind(r,44826))},44826:(e,s,r)=>{"use strict";r.d(s,{default:()=>f});var a=r(98768),t=r(98771),o=r(66263),n=r(60343),i=r(72548),l=r(76342),d=r(69424),c=r(7678),x=r.n(c),p=r(71496),h=r(66314),u=r(42106),m=r(25394);let g=p.z.object({password:p.z.string().min(6,{message:"Password must be at least 6 characters"}),confirmPassword:p.z.string()}).refine(e=>e.password===e.confirmPassword,{message:"Passwords must match",path:["confirmPassword"]}),f=()=>{let e=(0,d.useSearchParams)().get("t"),[s,r]=(0,n.useState)(""),[c,p]=(0,n.useState)(!1),f=(0,h.cI)({resolver:(0,u.F)(g),defaultValues:{password:"",confirmPassword:""}}),[j,{loading:w}]=(0,i.D)(l.bky,{onCompleted:e=>{let{message:s,result:a}=e.resetPassword;"OK"===a?p(!0):x()(s)?r(""):r(s[0])},onError:e=>{console.error("resetPassword error",e),r(e.message)}});async function P(s){r(""),await j({variables:{token:e,password:s.password,passwordConfirm:s.confirmPassword}})}return(0,a.jsxs)("div",{className:"flex h-screen flex-col items-center justify-center gap-4 ",children:[a.jsx(o.default,{href:"/",children:a.jsx(t.Z,{})}),!x()(e)&&!c&&(0,a.jsxs)(m.Zb,{children:[(0,a.jsxs)(m.Ol,{children:[a.jsx(m.ll,{className:"text-xl",children:"Reset Password"}),a.jsx(m.SZ,{children:"Enter your new password below."})]}),(0,a.jsxs)(m.aY,{children:[s&&a.jsx(m.bZ,{variant:"destructive",children:a.jsx(m.X,{children:s})}),a.jsx(m.l0,{...f,children:(0,a.jsxs)("form",{onSubmit:f.handleSubmit(P),className:"flex flex-col gap-4",children:[a.jsx(m.Wi,{control:f.control,name:"password",render:({field:e})=>(0,a.jsxs)(m.xJ,{children:[a.jsx(m.lX,{children:"Password"}),a.jsx(m.NI,{children:a.jsx(m.II,{...e})})]})}),a.jsx(m.Wi,{control:f.control,name:"confirmPassword",render:({field:e})=>(0,a.jsxs)(m.xJ,{children:[a.jsx(m.lX,{children:"Confirm Password"}),a.jsx(m.NI,{children:a.jsx(m.II,{...e})})]})}),a.jsx("div",{className:"flex py-4",children:a.jsx("div",{children:a.jsx(m.zx,{type:"submit",disabled:w,children:w?"Saving password...":"Save Password"})})})]})})]})]}),x()(e)&&(0,a.jsxs)(m.Zb,{children:[(0,a.jsxs)(m.Ol,{children:[a.jsx(m.ll,{className:"text-xl",children:"Change Password"}),a.jsx(m.SZ,{children:"The password reset link is inivalid or expired."})]}),a.jsx(m.aY,{children:(0,a.jsxs)("div",{children:["You can request a new one"," ",a.jsx(o.default,{href:"/lost-password",className:" ",children:"here"})," ","or change your password after you"," ",a.jsx(o.default,{href:"/login",className:" ",children:"login"}),"."]})})]}),c&&(0,a.jsxs)(m.Zb,{children:[(0,a.jsxs)(m.Ol,{children:[a.jsx(m.ll,{children:"Password Changed"}),a.jsx(m.SZ,{})]}),(0,a.jsxs)(m.aY,{children:[a.jsx(m.P,{children:"You have successfully reset your password."}),(0,a.jsxs)(m.P,{children:["Click"," ",a.jsx(o.default,{href:"/login",className:" ",children:"here"})," ","to login."]})]})]})]})}},98771:(e,s,r)=>{"use strict";r.d(s,{Z:()=>o});var a=r(98768),t=r(28147);let o=function({color:e="default"}){return a.jsx("div",{children:"white"===e?a.jsx(t.default,{src:"/sealogs-horizontal-logo-white.png",alt:"Sealogs Logo",width:300,height:92,className:""}):a.jsx(t.default,{src:"/sealogs-horizontal-logo.png",alt:"Sealogs Logo",width:300,height:92,className:""})})}},63711:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var a=r(96141);let t=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reset-password\form.tsx#default`),o=()=>a.jsx(t,{})}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[864,8865,3563,6263,8189,7602,1264,6451,4234,2925,5394,6342],()=>r(81273));module.exports=a})();