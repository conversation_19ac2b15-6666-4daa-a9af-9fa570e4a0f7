exports.id=5959,exports.ids=[5959],exports.modules={73304:()=>{},18937:()=>{},8087:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});let r=(0,n(97428).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},47634:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});let r=(0,n(97428).Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},46877:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});let r=(0,n(97428).Z)("FileImage",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]])},12513:(e,t,n)=>{"use strict";n.d(t,{$0:()=>eu,Eq:()=>v,Fy:()=>m,KL:()=>y,NM:()=>p,Nc:()=>s,OL:()=>el,P0:()=>em,QB:()=>g,RD:()=>_,TX:()=>Q,U2:()=>j,VI:()=>E,Wy:()=>c,ZP:()=>e_,bQ:()=>eg,bc:()=>ew,bt:()=>er,gJ:()=>d,hU:()=>K,l6:()=>S,oc:()=>Z,tS:()=>F,wQ:()=>C});var r,l,i=n(60343),o=n(77257),a=n(61222);let u="yarl__";function c(...e){return[...e].filter(Boolean).join(" ")}function s(e){return`${u}${e}`}function d(e){return`--${u}${e}`}function h(e,t){return`${e}${t?`_${t}`:""}`}function f(e){return t=>h(e,t)}function v(...e){return()=>{e.forEach(e=>{e()})}}function m(e,t,n){return()=>{let r=i.useContext(n);if(!r)throw Error(`${e} must be used within a ${t}.Provider`);return r}}function p(e,t=0){let n=10**t;return Math.round((e+Number.EPSILON)*n)/n}function g(e){return void 0===e.type||"image"===e.type}function E(e,t){return e.imageFit===o.rO||e.imageFit!==o.j3&&t===o.rO}function b(e){return"string"==typeof e?Number.parseInt(e,10):e}function w(e){if("number"==typeof e)return{pixel:e};if("string"==typeof e){let t=b(e);return e.endsWith("%")?{percent:t}:{pixel:t}}return{pixel:0}}function y(){return 1}function x(e,t){var n;return e[(n=e.length)>0?(t%n+n)%n:0]}function M(e,t){return e.length>0?x(e,t):void 0}function C(e,t,n){if(!n)return e;let{buttons:r,...l}=e,o=r.findIndex(e=>e===t),a=i.isValidElement(n)?i.cloneElement(n,{key:t},null):n;if(o>=0){let e=[...r];return e.splice(o,1,a),{buttons:e,...l}}return{buttons:[a,...r],...l}}let k=Number(i.version.split(".")[0])>=19,N={open:!1,close:()=>{},index:0,slides:[],render:{},plugins:[],toolbar:{buttons:[o.t9]},labels:{},animation:{fade:250,swipe:500,easing:{fade:"ease",swipe:"ease-out",navigation:"ease-in-out"}},carousel:{finite:!1,preload:2,padding:"16px",spacing:"30%",imageFit:o.j3,imageProps:{}},controller:{ref:null,focus:!0,aria:!1,touchAction:"none",closeOnPullUp:!1,closeOnPullDown:!1,closeOnBackdropClick:!1,preventDefaultWheelX:!0,preventDefaultWheelY:!1,disableSwipeNavigation:!1},portal:{},noScroll:{disabled:!1},on:{},styles:{},className:""};function S(e,t){return{name:e,component:t}}function R(e,t){return{module:e,children:t}}function L(e,t,n){return e.flatMap(e=>{var r;return null!==(r=function e(t,n,r){return t.module.name===n?r(t):t.children?[R(t.module,t.children.flatMap(t=>{var l;return null!==(l=e(t,n,r))&&void 0!==l?l:[]}))]:[t]}(e,t,n))&&void 0!==r?r:[]})}let P=i.createContext(null),F=m("useDocument","DocumentContext",P);function I({nodeRef:e,children:t}){let n=i.useMemo(()=>{let t=t=>{var n;return(null===(n=t||e.current)||void 0===n?void 0:n.ownerDocument)||document};return{getOwnerDocument:t,getOwnerWindow:e=>{var n;return(null===(n=t(e))||void 0===n?void 0:n.defaultView)||window}}},[e]);return i.createElement(P.Provider,{value:n},t)}let D=i.createContext(null),$=m("useEvents","EventsContext",D);function z({children:e}){let[t]=i.useState({});i.useEffect(()=>()=>{Object.keys(t).forEach(e=>delete t[e])},[t]);let n=i.useMemo(()=>{let e=(e,n)=>{var r;null===(r=t[e])||void 0===r||r.splice(0,t[e].length,...t[e].filter(e=>e!==n))};return{publish:(...[e,n])=>{var r;null===(r=t[e])||void 0===r||r.forEach(e=>e(n))},subscribe:(n,r)=>(t[n]||(t[n]=[]),t[n].push(r),()=>e(n,r)),unsubscribe:e}},[t]);return i.createElement(D.Provider,{value:n},e)}let T=i.createContext(null),_=m("useLightboxProps","LightboxPropsContext",T);function H({children:e,...t}){return i.createElement(T.Provider,{value:t},e)}let O=i.createContext(null),Z=m("useLightboxState","LightboxStateContext",O),A=i.createContext(null),W=m("useLightboxDispatch","LightboxDispatchContext",A);function U(e,t){switch(t.type){case"swipe":{var n;let{slides:r}=e,l=(null==t?void 0:t.increment)||0,i=e.globalIndex+l,o=(n=r.length)>0?(i%n+n)%n:0,a=M(r,o);return{slides:r,currentIndex:o,globalIndex:i,currentSlide:a,animation:l||void 0!==t.duration?{increment:l,duration:t.duration,easing:t.easing}:void 0}}case"update":if(t.slides!==e.slides||t.index!==e.currentIndex)return{slides:t.slides,currentIndex:t.index,globalIndex:t.index,currentSlide:M(t.slides,t.index)};return e;default:throw Error(o.c3)}}function X({slides:e,index:t,children:n}){let[r,l]=i.useReducer(U,{slides:e,currentIndex:t,globalIndex:t,currentSlide:M(e,t)});i.useEffect(()=>{l({type:"update",slides:e,index:t})},[e,t]);let o=i.useMemo(()=>({...r,state:r,dispatch:l}),[r,l]);return i.createElement(A.Provider,{value:l},i.createElement(O.Provider,{value:o},n))}let V=i.createContext(null),q=m("useTimeouts","TimeoutsContext",V);function B({children:e}){let[t]=i.useState([]);i.useEffect(()=>()=>{t.forEach(e=>window.clearTimeout(e)),t.splice(0,t.length)},[t]);let n=i.useMemo(()=>{let e=e=>{t.splice(0,t.length,...t.filter(t=>t!==e))};return{setTimeout:(n,r)=>{let l=window.setTimeout(()=>{e(l),n()},r);return t.push(l),l},clearTimeout:t=>{void 0!==t&&(e(t),window.clearTimeout(t))}}},[t]);return i.createElement(V.Provider,{value:n},e)}let K=i.forwardRef(function({label:e,className:t,icon:n,renderIcon:r,onClick:l,style:a,...u},d){var h;let{styles:f,labels:v}=_(),m=null!==(h=null==v?void 0:v[e])&&void 0!==h?h:e;return i.createElement("button",{ref:d,type:"button",title:m,"aria-label":m,className:c(s(o.bg),t),onClick:l,style:{...a,...f.button},...u},r?r():i.createElement(n,{className:s(o.vg),style:f.icon}))});function Y(e,t){let n=e=>i.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24","aria-hidden":"true",focusable:"false",...e},t);return n.displayName=e,n}function j(e,t){return Y(e,i.createElement("g",{fill:"currentColor"},i.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),t))}function Q(e,t){return Y(e,i.createElement(i.Fragment,null,i.createElement("defs",null,i.createElement("mask",{id:"strike"},i.createElement("path",{d:"M0 0h24v24H0z",fill:"white"}),i.createElement("path",{d:"M0 0L24 24",stroke:"black",strokeWidth:4}))),i.createElement("path",{d:"M0.70707 2.121320L21.878680 23.292883",stroke:"currentColor",strokeWidth:2}),i.createElement("g",{fill:"currentColor",mask:"url(#strike)"},i.createElement("path",{d:"M0 0h24v24H0z",fill:"none"}),t)))}let J=j("Close",i.createElement("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),G=j("Previous",i.createElement("path",{d:"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"})),ee=j("Next",i.createElement("path",{d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"})),et=j("Loading",i.createElement(i.Fragment,null,Array.from({length:8}).map((e,t,n)=>i.createElement("line",{key:t,x1:"12",y1:"6.5",x2:"12",y2:"1.8",strokeLinecap:"round",strokeWidth:"2.6",stroke:"currentColor",strokeOpacity:1/n.length*(t+1),transform:`rotate(${360/n.length*t}, 12, 12)`})))),en=j("Error",i.createElement("path",{d:"M21.9,21.9l-8.49-8.49l0,0L3.59,3.59l0,0L2.1,2.1L0.69,3.51L3,5.83V19c0,1.1,0.9,2,2,2h13.17l2.31,2.31L21.9,21.9z M5,18 l3.5-4.5l2.5,3.01L12.17,15l3,3H5z M21,18.17L5.83,3H19c1.1,0,2,0.9,2,2V18.17z"})),er=i.useEffect;function el(){let[e,t]=i.useState(!1);return i.useEffect(()=>{var e,n;let r=null===(e=window.matchMedia)||void 0===e?void 0:e.call(window,"(prefers-reduced-motion: reduce)");t(null==r?void 0:r.matches);let l=e=>t(e.matches);return null===(n=null==r?void 0:r.addEventListener)||void 0===n||n.call(r,"change",l),()=>{var e;return null===(e=null==r?void 0:r.removeEventListener)||void 0===e?void 0:e.call(r,"change",l)}},[]),e}function ei(e,t){let n=i.useRef(void 0),r=i.useRef(void 0),l=el();return er(()=>{var i,o,a;if(e.current&&void 0!==n.current&&!l){let{keyframes:l,duration:u,easing:c,onfinish:s}=t(n.current,e.current.getBoundingClientRect(),function(e){let t=0,n=0,r=0,l=window.getComputedStyle(e).transform.match(/matrix.*\((.+)\)/);if(l){let e=l[1].split(",").map(b);6===e.length?(t=e[4],n=e[5]):16===e.length&&(t=e[12],n=e[13],r=e[14])}return{x:t,y:n,z:r}}(e.current))||{};if(l&&u){null===(i=r.current)||void 0===i||i.cancel(),r.current=void 0;try{r.current=null===(a=(o=e.current).animate)||void 0===a?void 0:a.call(o,l,{duration:u,easing:c})}catch(e){console.error(e)}r.current&&(r.current.onfinish=()=>{r.current=void 0,null==s||s()})}}n.current=void 0}),{prepareAnimation:e=>{n.current=e},isAnimationPlaying:()=>{var e;return(null===(e=r.current)||void 0===e?void 0:e.playState)==="running"}}}function eo(){let e=i.useRef(null),t=i.useRef(void 0),[n,r]=i.useState();return{setContainerRef:i.useCallback(n=>{e.current=n,t.current&&(t.current.disconnect(),t.current=void 0);let l=()=>{if(n){let e=window.getComputedStyle(n),t=e=>parseFloat(e)||0;r({width:Math.round(n.clientWidth-t(e.paddingLeft)-t(e.paddingRight)),height:Math.round(n.clientHeight-t(e.paddingTop)-t(e.paddingBottom))})}else r(void 0)};l(),n&&"undefined"!=typeof ResizeObserver&&(t.current=new ResizeObserver(l),t.current.observe(n))},[]),containerRef:e,containerRect:n}}function ea(){let e=i.useRef(void 0),{setTimeout:t,clearTimeout:n}=q();return i.useCallback((r,l)=>{n(e.current),e.current=t(r,l>0?l:0)},[t,n])}function eu(e){let t=i.useRef(e);return er(()=>{t.current=e}),i.useCallback((...e)=>{var n;return null===(n=t.current)||void 0===n?void 0:n.call(t,...e)},[])}function ec(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function es(e,t){return i.useMemo(()=>null==e&&null==t?null:n=>{ec(e,n),ec(t,n)},[e,t])}function ed(){let[e,t]=i.useState(!1);return er(()=>{t("rtl"===window.getComputedStyle(window.document.documentElement).direction)},[]),e}function eh(e,t){let n=i.useRef(0),r=ea(),l=eu((...t)=>{n.current=Date.now(),e(t)});return i.useCallback((...e)=>{r(()=>{l(e)},t-(Date.now()-n.current))},[t,l,r])}let ef=f("slide"),ev=f("slide_image");function em({slide:e,offset:t,render:n,rect:r,imageFit:l,imageProps:a,onClick:u,onLoad:d,onError:h,style:f}){var v,m,p,g,b,w,y;let[x,M]=i.useState(o.Xe),{publish:C}=$(),{setTimeout:k}=q(),N=i.useRef(null);i.useEffect(()=>{0===t&&C((0,o.J1)(x))},[t,x,C]);let S=eu(e=>{("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{e.parentNode&&(M(o.Zv),k(()=>{null==d||d(e)},0))})}),R=i.useCallback(e=>{N.current=e,(null==e?void 0:e.complete)&&S(e)},[S]),L=i.useCallback(e=>{S(e.currentTarget)},[S]),P=eu(()=>{M(o.fS),null==h||h()}),F=E(e,l),I=(e,t)=>Number.isFinite(e)?e:t,D=I(Math.max(...(null!==(m=null===(v=e.srcSet)||void 0===v?void 0:v.map(e=>e.width))&&void 0!==m?m:[]).concat(e.width?[e.width]:[]).filter(Boolean)),(null===(p=N.current)||void 0===p?void 0:p.naturalWidth)||0),z=I(Math.max(...(null!==(b=null===(g=e.srcSet)||void 0===g?void 0:g.map(e=>e.height))&&void 0!==b?b:[]).concat(e.height?[e.height]:[]).filter(Boolean)),(null===(w=N.current)||void 0===w?void 0:w.naturalHeight)||0),T=D&&z?{maxWidth:`min(${D}px, 100%)`,maxHeight:`min(${z}px, 100%)`}:{maxWidth:"100%",maxHeight:"100%"},_=null===(y=e.srcSet)||void 0===y?void 0:y.sort((e,t)=>e.width-t.width).map(e=>`${e.src} ${e.width}w`).join(", "),{style:H,className:O,...Z}=a||{};return i.createElement(i.Fragment,null,i.createElement("img",{ref:R,onLoad:L,onError:P,onClick:u,draggable:!1,className:c(s(ev()),F&&s(ev("cover")),x!==o.Zv&&s(ev("loading")),O),style:{...T,...f,...H},...Z,alt:e.alt,sizes:void 0,srcSet:_,src:e.src}),x!==o.Zv&&i.createElement("div",{className:s(ef(o.$L))},x===o.Xe&&((null==n?void 0:n.iconLoading)?n.iconLoading():i.createElement(et,{className:c(s(o.vg),s(ef(o.Xe)))})),x===o.fS&&((null==n?void 0:n.iconError)?n.iconError():i.createElement(en,{className:c(s(o.vg),s(ef(o.fS)))}))))}let ep=i.forwardRef(function({className:e,children:t,...n},r){let l=i.useRef(null);return i.createElement(I,{nodeRef:l},i.createElement("div",{ref:es(r,l),className:c(s("root"),e),...n},t))});function eg(e,t,n,r,l){i.useEffect(()=>l?()=>{}:v(e(o.NZ,t),e(o.N4,n),e(o.S2,r),e(o.pE,r),e(o.Vt,r)),[e,t,n,r,l])}(function(e){e[e.NONE=0]="NONE",e[e.SWIPE=1]="SWIPE",e[e.PULL=2]="PULL",e[e.ANIMATION=3]="ANIMATION"})(r||(r={})),function(e){e[e.NONE=0]="NONE",e[e.SWIPE=1]="SWIPE",e[e.PULL=2]="PULL"}(l||(l={}));let eE=f("container"),eb=i.createContext(null),ew=m("useController","ControllerContext",eb),ey=S(o.l4,function({children:e,...t}){var n;let{carousel:a,animation:u,controller:h,on:f,styles:m,render:g}=t,{closeOnPullUp:E,closeOnPullDown:b,preventDefaultWheelX:y,preventDefaultWheelY:x}=h,[M,C]=i.useState(),k=Z(),N=W(),[S,R]=i.useState(r.NONE),L=i.useRef(0),P=i.useRef(0),I=i.useRef(1),{registerSensors:D,subscribeSensors:z}=function(){let[e]=i.useState({}),t=i.useCallback((t,n)=>{var r;null===(r=e[t])||void 0===r||r.forEach(e=>{n.isPropagationStopped()||e(n)})},[e]);return{registerSensors:i.useMemo(()=>({onPointerDown:e=>t(o.NZ,e),onPointerMove:e=>t(o.N4,e),onPointerUp:e=>t(o.S2,e),onPointerLeave:e=>t(o.pE,e),onPointerCancel:e=>t(o.Vt,e),onKeyDown:e=>t(o.ds,e),onKeyUp:e=>t(o.Bm,e),onWheel:e=>t(o.yq,e)}),[t]),subscribeSensors:i.useCallback((t,n)=>(e[t]||(e[t]=[]),e[t].unshift(n),()=>{let r=e[t];r&&r.splice(0,r.length,...r.filter(e=>e!==n))}),[e])}}(),{subscribe:T,publish:_}=$(),H=ea(),O=ea(),A=ea(),{containerRef:U,setContainerRef:X,containerRect:V}=eo(),B=es(function({preventDefaultWheelX:e,preventDefaultWheelY:t}){let n=i.useRef(null),r=eu(n=>{let r=Math.abs(n.deltaX)>Math.abs(n.deltaY);(r&&e||!r&&t||n.ctrlKey)&&n.preventDefault()});return i.useCallback(e=>{var t;e?e.addEventListener("wheel",r,{passive:!1}):null===(t=n.current)||void 0===t||t.removeEventListener("wheel",r),n.current=e},[r])}({preventDefaultWheelX:y,preventDefaultWheelY:x}),X),K=i.useRef(null),Y=es(K,void 0),{getOwnerDocument:j}=F(),Q=ed(),J=e=>(Q?-1:1)*("number"==typeof e?e:1),G=eu(()=>{var e;return null===(e=U.current)||void 0===e?void 0:e.focus()}),ee=eu(()=>t),et=eu(()=>k),en=i.useCallback(e=>_(o.KN,e),[_]),er=i.useCallback(e=>_(o.Eb,e),[_]),el=i.useCallback(()=>_(o.t9),[_]),ec=e=>!(a.finite&&(J(e)>0&&0===k.currentIndex||0>J(e)&&k.currentIndex===k.slides.length-1)),eh=e=>{var t;L.current=e,null===(t=U.current)||void 0===t||t.style.setProperty(d("swipe_offset"),`${Math.round(e)}px`)},ef=e=>{var t,n;P.current=e,I.current=Math.min(Math.max(p(1-(b&&e>0?e:E&&e<0?-e:0)/60*.5,2),.5),1),null===(t=U.current)||void 0===t||t.style.setProperty(d("pull_offset"),`${Math.round(e)}px`),null===(n=U.current)||void 0===n||n.style.setProperty(d("pull_opacity"),`${I.current}`)},{prepareAnimation:ev}=ei(K,(e,t,n)=>{if(K.current&&V)return{keyframes:[{transform:`translate(0, ${e.rect.y-t.y+n.y}px)`,opacity:e.opacity},{transform:"translate(0, 0)",opacity:1}],duration:e.duration,easing:u.easing.fade}}),em=(e,t)=>{if(E||b){ef(e);let n=0;K.current&&(n=u.fade*(t?2:1),ev({rect:K.current.getBoundingClientRect(),opacity:I.current,duration:n})),A(()=>{ef(0),R(r.NONE)},n),R(r.ANIMATION),t||el()}},{prepareAnimation:ep,isAnimationPlaying:ew}=ei(K,(e,t,n)=>{var r;if(K.current&&V&&(null===(r=k.animation)||void 0===r?void 0:r.duration)){let r=w(a.spacing),l=(r.percent?r.percent*V.width/100:r.pixel)||0;return{keyframes:[{transform:`translate(${J(k.globalIndex-e.index)*(V.width+l)+e.rect.x-t.x+n.x}px, 0)`},{transform:"translate(0, 0)"}],duration:k.animation.duration,easing:k.animation.easing}}}),ey=eu(e=>{var t,n;let l=e.offset||0,i=l?u.swipe:null!==(t=u.navigation)&&void 0!==t?t:u.swipe,a=l||ew()?u.easing.swipe:u.easing.navigation,{direction:c}=e,s=null!==(n=e.count)&&void 0!==n?n:1,d=r.ANIMATION,h=i*s;if(!c){let t=null==V?void 0:V.width,n=e.duration||0,r=t?i/t*Math.abs(l):i;0!==s?(n<r?h=h/r*Math.max(n,r/5):t&&(h=i/t*(t-Math.abs(l))),c=J(l)>0?o.KN:o.Eb):h=i/2}let f=0;c===o.KN?ec(J(1))?f=-s:(d=r.NONE,h=i):c===o.Eb&&(ec(J(-1))?f=s:(d=r.NONE,h=i)),O(()=>{eh(0),R(r.NONE)},h=Math.round(h)),K.current&&ep({rect:K.current.getBoundingClientRect(),index:k.globalIndex}),R(d),_(o.Tn,{type:"swipe",increment:f,duration:h,easing:a})});i.useEffect(()=>{var e,t;(null===(e=k.animation)||void 0===e?void 0:e.increment)&&(null===(t=k.animation)||void 0===t?void 0:t.duration)&&H(()=>N({type:"swipe",increment:0}),k.animation.duration)},[k.animation,N,H]);let ex=[z,ec,(null==V?void 0:V.width)||0,u.swipe,()=>R(r.SWIPE),e=>eh(e),(e,t)=>ey({offset:e,duration:t,count:1}),e=>ey({offset:e,count:0})],eM=[()=>{b&&R(r.PULL)},e=>ef(e),e=>em(e),e=>em(e,!0)];(function({disableSwipeNavigation:e,closeOnBackdropClick:t},n,r,a,u,c,d,h,f,v,m,p,g,E,b,w){let y=i.useRef(0),x=i.useRef([]),M=i.useRef(void 0),C=i.useRef(0),k=i.useRef(l.NONE),N=i.useCallback(e=>{M.current===e.pointerId&&(M.current=void 0,k.current=l.NONE);let t=x.current;t.splice(0,t.length,...t.filter(t=>t.pointerId!==e.pointerId))},[]),S=i.useCallback(e=>{N(e),e.persist(),x.current.push(e)},[N]),R=i.useCallback(e=>x.current.find(({pointerId:t})=>e.pointerId===t),[]),L=eu(e=>{S(e)}),P=(e,t)=>m&&e>t||v&&e<-t,F=eu(e=>{let n=R(e);if(n){if(M.current===e.pointerId){let e=Date.now()-C.current,t=y.current;k.current===l.SWIPE?Math.abs(t)>.3*a||Math.abs(t)>5&&e<u?h(t,e):f(t):k.current===l.PULL&&(P(t,60)?E(t,e):b(t)),y.current=0,k.current=l.NONE}else{let{target:r}=e;t&&r instanceof HTMLElement&&r===n.target&&(r.classList.contains(s(o.hD))||r.classList.contains(s(o.af)))&&w()}}N(e)});eg(n,L,eu(t=>{let n=R(t);if(n){let i=M.current===t.pointerId;if(0===t.buttons){i&&0!==y.current?F(t):N(n);return}let o=t.clientX-n.clientX,a=t.clientY-n.clientY;if(void 0===M.current){let n=e=>{S(t),M.current=t.pointerId,C.current=Date.now(),k.current=e};Math.abs(o)>Math.abs(a)&&Math.abs(o)>30&&r(o)?e||(n(l.SWIPE),c()):Math.abs(a)>Math.abs(o)&&P(a,30)&&(n(l.PULL),p())}else i&&(k.current===l.SWIPE?(y.current=o,d(o)):k.current===l.PULL&&(y.current=a,g(a)))}}),F)})(h,...ex,E,b,...eM,el),function(e,t,n,l,a,u,c,s,d){let h=i.useRef(0),f=i.useRef(0),v=i.useRef(void 0),m=i.useRef(void 0),p=i.useRef(0),g=i.useRef(void 0),E=i.useRef(0),{setTimeout:b,clearTimeout:w}=q(),y=i.useCallback(()=>{v.current&&(w(v.current),v.current=void 0)},[w]),x=i.useCallback(()=>{m.current&&(w(m.current),m.current=void 0)},[w]),M=eu(()=>{e!==r.SWIPE&&(h.current=0,E.current=0,y(),x())});i.useEffect(M,[e,M]);let C=eu(e=>{m.current=void 0,h.current===e&&d(h.current)}),k=eu(t=>{if(t.ctrlKey||Math.abs(t.deltaY)>Math.abs(t.deltaX))return;let i=e=>{p.current=e,w(g.current),g.current=e>0?b(()=>{p.current=0,g.current=void 0},300):void 0};if(e===r.NONE){if(Math.abs(t.deltaX)<=1.2*Math.abs(p.current)){i(t.deltaX);return}if(!n(-t.deltaX))return;if(f.current+=t.deltaX,y(),Math.abs(f.current)>30)f.current=0,i(0),E.current=Date.now(),u();else{let e=f.current;v.current=b(()=>{v.current=void 0,e===f.current&&(f.current=0)},a)}}else if(e===r.SWIPE){let e=h.current-t.deltaX;if(e=Math.min(Math.abs(e),l)*Math.sign(e),h.current=e,c(e),x(),Math.abs(e)>.2*l){i(t.deltaX),s(e,Date.now()-E.current);return}m.current=b(()=>C(e),2*a)}else i(t.deltaX)});i.useEffect(()=>t(o.yq,k),[t,k])}(S,...ex);let eC=eu(()=>{h.focus&&j().querySelector(`.${s(o.SA)} .${s(eE())}`)&&G()});i.useEffect(eC,[eC]);let ek=eu(()=>{var e;null===(e=f.view)||void 0===e||e.call(f,{index:k.currentIndex})});i.useEffect(ek,[k.globalIndex,ek]),i.useEffect(()=>v(T(o.KN,e=>ey({direction:o.KN,...e})),T(o.Eb,e=>ey({direction:o.Eb,...e})),T(o.Tn,e=>N(e))),[T,ey,N]);let eN=i.useMemo(()=>({prev:en,next:er,close:el,focus:G,slideRect:V?function(e,t){let n=w(t),r=void 0!==n.percent?e.width/100*n.percent:n.pixel;return{width:Math.max(e.width-2*r,0),height:Math.max(e.height-2*r,0)}}(V,a.padding):{width:0,height:0},containerRect:V||{width:0,height:0},subscribeSensors:z,containerRef:U,setCarouselRef:Y,toolbarWidth:M,setToolbarWidth:C}),[en,er,el,G,z,V,U,Y,M,C,a.padding]);return i.useImperativeHandle(h.ref,()=>({prev:en,next:er,close:el,focus:G,getLightboxProps:ee,getLightboxState:et}),[en,er,el,G,ee,et]),i.createElement("div",{ref:B,className:c(s(eE()),s(o.aN)),style:{...S===r.SWIPE?{[d("swipe_offset")]:`${Math.round(L.current)}px`}:null,...S===r.PULL?{[d("pull_offset")]:`${Math.round(P.current)}px`,[d("pull_opacity")]:`${I.current}`}:null,..."none"!==h.touchAction?{[d("controller_touch_action")]:h.touchAction}:null,...m.container},...h.aria?{role:"region","aria-live":"polite","aria-roledescription":"carousel"}:null,tabIndex:-1,...D},V&&i.createElement(eb.Provider,{value:eN},e,null===(n=g.controls)||void 0===n?void 0:n.call(g)))});function ex(e){return h(o.k0,e)}function eM({slide:e,offset:t}){var n,r,l,a,u;let d;let f=i.useRef(null),{currentIndex:v}=Z(),{slideRect:m,focus:p}=ew(),{render:E,carousel:{imageFit:b,imageProps:w},on:{click:y},styles:{slide:x}}=_(),{getOwnerDocument:M}=F(),C=0!==t;return i.useEffect(()=>{var e;C&&(null===(e=f.current)||void 0===e?void 0:e.contains(M().activeElement))&&p()},[C,p,M]),i.createElement("div",{ref:f,className:c(s(h(o.hD,void 0)),!C&&s((n="current",h(o.hD,n))),s(o.aN)),inert:k?C:C?"":void 0,style:x,role:"region","aria-roledescription":"slide"},(!(d=null===(r=E.slide)||void 0===r?void 0:r.call(E,{slide:e,offset:t,rect:m}))&&g(e)&&(d=i.createElement(em,{slide:e,offset:t,render:E,rect:m,imageFit:b,imageProps:w,onClick:C?void 0:()=>null==y?void 0:y({index:v})})),d?i.createElement(i.Fragment,null,null===(l=E.slideHeader)||void 0===l?void 0:l.call(E,{slide:e}),(null!==(a=E.slideContainer)&&void 0!==a?a:({children:e})=>e)({slide:e,children:d}),null===(u=E.slideFooter)||void 0===u?void 0:u.call(E,{slide:e})):null))}function eC(){let e=_().styles.slide;return i.createElement("div",{className:s(o.hD),style:e})}let ek=S(o.k0,function({carousel:e}){let{slides:t,currentIndex:n,globalIndex:r}=Z(),{setCarouselRef:l}=ew(),o=w(e.spacing),a=w(e.padding),u=function(e,t,n=0){return Math.min(e.preload,Math.max(e.finite?t.length-1:Math.floor(t.length/2),n))}(e,t,1),h=[];if(t.length>0)for(let l=n-u;l<=n+u;l+=1){let i=x(t,l),o=r-n+l,a=e.finite&&(l<0||l>t.length-1);h.push(a?{key:o}:{key:[`${o}`,g(i)?i.src:void 0].filter(Boolean).join("|"),offset:l-n,slide:i})}return i.createElement("div",{ref:l,className:c(s(ex()),h.length>0&&s(ex("with_slides"))),style:{[`${d(ex("slides_count"))}`]:h.length,[`${d(ex("spacing_px"))}`]:o.pixel||0,[`${d(ex("spacing_percent"))}`]:o.percent||0,[`${d(ex("padding_px"))}`]:a.pixel||0,[`${d(ex("padding_percent"))}`]:a.percent||0}},h.map(({key:e,slide:t,offset:n})=>t?i.createElement(eM,{key:e,slide:t,offset:n}):i.createElement(eC,{key:e})))});function eN(){let{carousel:e}=_(),{slides:t,currentIndex:n}=Z();return{prevDisabled:0===t.length||e.finite&&0===n,nextDisabled:0===t.length||e.finite&&n===t.length-1}}function eS({label:e,icon:t,renderIcon:n,action:r,onClick:l,disabled:o,style:a}){return i.createElement(K,{label:e,icon:t,renderIcon:n,className:s(`navigation_${r}`),disabled:o,onClick:l,style:a,...function(e,t=!1){let n=i.useRef(!1);return er(()=>{t&&n.current&&(n.current=!1,e())},[t,e]),{onFocus:i.useCallback(()=>{n.current=!0},[]),onBlur:i.useCallback(()=>{n.current=!1},[])}}(ew().focus,o)})}let eR=S(o.Op,function({render:{buttonPrev:e,buttonNext:t,iconPrev:n,iconNext:r},styles:l}){let{prev:a,next:u,subscribeSensors:c}=ew(),{prevDisabled:s,nextDisabled:d}=eN();return function(e){var t;let n=ed(),{publish:r}=$(),{animation:l}=_(),{prevDisabled:a,nextDisabled:u}=eN(),c=(null!==(t=l.navigation)&&void 0!==t?t:l.swipe)/2,s=eh(()=>r(o.KN),c),d=eh(()=>r(o.Eb),c),h=eu(e=>{switch(e.key){case o.PU:r(o.t9);break;case o.Sl:(n?u:a)||(n?d:s)();break;case o.NH:(n?a:u)||(n?s:d)()}});i.useEffect(()=>e(o.ds,h),[e,h])}(c),i.createElement(i.Fragment,null,e?e():i.createElement(eS,{label:"Previous",action:o.KN,icon:G,renderIcon:n,style:l.navigationPrev,disabled:s,onClick:a}),t?t():i.createElement(eS,{label:"Next",action:o.Eb,icon:ee,renderIcon:r,style:l.navigationNext,disabled:d,onClick:u}))}),eL=s(o.Tf),eP=s(o.M9);function eF(e,t,n){let r=window.getComputedStyle(e),l=n?"padding-left":"padding-right",i=n?r.paddingLeft:r.paddingRight,o=e.style.getPropertyValue(l);return e.style.setProperty(l,`${(b(i)||0)+t}px`),()=>{o?e.style.setProperty(l,o):e.style.removeProperty(l)}}let eI=S(o.HE,function({noScroll:{disabled:e},children:t}){let n=ed(),{getOwnerDocument:r,getOwnerWindow:l}=F();return i.useEffect(()=>{if(e)return()=>{};let t=[],i=l(),{body:o,documentElement:a}=r(),u=Math.round(i.innerWidth-a.clientWidth);if(u>0){t.push(eF(o,u,n));let e=o.getElementsByTagName("*");for(let r=0;r<e.length;r+=1){let l=e[r];"style"in l&&"fixed"===i.getComputedStyle(l).getPropertyValue("position")&&!l.classList.contains(eP)&&t.push(eF(l,u,n))}}return o.classList.add(eL),()=>{o.classList.remove(eL),t.forEach(e=>e())}},[n,e,r,l]),i.createElement(i.Fragment,null,t)});function eD(e,t,n){let r=e.getAttribute(t);return e.setAttribute(t,n),()=>{r?e.setAttribute(t,r):e.removeAttribute(t)}}let e$=S(o.SA,function({children:e,animation:t,styles:n,className:r,on:l,portal:u,close:f}){var v;let[m,p]=i.useState(!1),[g,E]=i.useState(!1),b=i.useRef([]),w=i.useRef(null),{setTimeout:y}=q(),{subscribe:x}=$(),M=el()?0:t.fade;i.useEffect(()=>(p(!0),()=>{p(!1),E(!1)}),[]);let C=eu(()=>{b.current.forEach(e=>e()),b.current=[]}),k=eu(()=>{var e;E(!1),C(),null===(e=l.exiting)||void 0===e||e.call(l),y(()=>{var e;null===(e=l.exited)||void 0===e||e.call(l),f()},M)});i.useEffect(()=>x(o.t9,k),[x,k]);let S=eu(e=>{var t,n,r;e.scrollTop,E(!0),null===(t=l.entering)||void 0===t||t.call(l);let i=null!==(r=null===(n=e.parentNode)||void 0===n?void 0:n.children)&&void 0!==r?r:[];for(let t=0;t<i.length;t+=1){let n=i[t];-1===["TEMPLATE","SCRIPT","STYLE"].indexOf(n.tagName)&&n!==e&&(b.current.push(eD(n,"inert","")),b.current.push(eD(n,"aria-hidden","true")))}b.current.push(()=>{var e,t;null===(t=null===(e=w.current)||void 0===e?void 0:e.focus)||void 0===t||t.call(e)}),y(()=>{var e;null===(e=l.entered)||void 0===e||e.call(l)},M)}),R=i.useCallback(e=>{e?S(e):C()},[S,C]);return m?(0,a.createPortal)(i.createElement(ep,{ref:R,className:c(r,s(h(o.SA,void 0)),s(o.M9),g&&s((v="open",h(o.SA,v)))),"aria-modal":!0,role:"dialog","aria-live":"polite","aria-roledescription":"lightbox",style:{...t.fade!==N.animation.fade?{[d("fade_animation_duration")]:`${M}ms`}:null,...t.easing.fade!==N.animation.easing.fade?{[d("fade_animation_timing_function")]:t.easing.fade}:null,...n.root},onFocus:e=>{w.current||(w.current=e.relatedTarget)}},e),u.root||document.body):null}),ez=S(o.lT,function({children:e}){return i.createElement(i.Fragment,null,e)}),eT=S(o.hb,function({toolbar:{buttons:e},render:{buttonClose:t,iconClose:n},styles:r}){let{close:l,setToolbarWidth:a}=ew(),{setContainerRef:u,containerRect:c}=eo();er(()=>{a(null==c?void 0:c.width)},[a,null==c?void 0:c.width]);let d=()=>t?t():i.createElement(K,{key:o.t9,label:"Close",icon:J,renderIcon:n,onClick:l});return i.createElement("div",{ref:u,style:r.toolbar,className:s(h(o.hb,void 0))},null==e?void 0:e.map(e=>e===o.t9?d():e))});function e_({carousel:e,animation:t,render:n,toolbar:r,controller:l,noScroll:a,on:u,plugins:c,slides:s,index:d,...h}){let{animation:f,carousel:v,render:m,toolbar:p,controller:g,noScroll:E,on:w,slides:y,index:x,plugins:M,...C}=N,{config:k,augmentation:S}=function(e,t=[],n=[]){let r=e,l=e=>{let t=[...r];for(;t.length>0;){let n=t.pop();if((null==n?void 0:n.module.name)===e)return!0;(null==n?void 0:n.children)&&t.push(...n.children)}return!1},i=(e,t)=>{if(""===e){r=[R(t,r)];return}r=L(r,e,e=>[R(t,[e])])},a=(e,t)=>{r=L(r,e,e=>[R(e.module,[R(t,e.children)])])},u=(e,t,n)=>{r=L(r,e,e=>{var r;return[R(e.module,[...n?[R(t)]:[],...null!==(r=e.children)&&void 0!==r?r:[],...n?[]:[R(t)]])]})},c=(e,t,n)=>{r=L(r,e,e=>[...n?[R(t)]:[],e,...n?[]:[R(t)]])},s=e=>{a(o.l4,e)},d=(e,t)=>{r=L(r,e,e=>[R(t,e.children)])},h=e=>{r=L(r,e,e=>e.children)},f=e=>{n.push(e)};return t.forEach(e=>{e({contains:l,addParent:i,append:a,addChild:u,addSibling:c,addModule:s,replace:d,remove:h,augment:f})}),{config:r,augmentation:e=>n.reduce((e,t)=>t(e),e)}}([R(e$,[R(eI,[R(ey,[R(ek),R(eT),R(eR)])])])],c||M),P=S({animation:function(e,t={}){let{easing:n,...r}=e,{easing:l,...i}=t;return{easing:{...n,...l},...r,...i}}(f,t),carousel:{...v,...e},render:{...m,...n},toolbar:{...p,...r},controller:{...g,...l},noScroll:{...E,...a},on:{...w,...u},...C,...h});return P.open?i.createElement(H,{...P},i.createElement(X,{slides:s||y,index:b(d||x)},i.createElement(B,null,i.createElement(z,null,function e(t,n){var r;return i.createElement(t.module.component,{key:t.module.name,...n},null===(r=t.children)||void 0===r?void 0:r.map(t=>e(t,n)))}(R(ez,k),P))))):null}},88557:(e,t,n)=>{"use strict";n.d(t,{Z:()=>E});var r=n(60343),l=n(12513),i=n(77257);let o=e=>(0,l.Nc)(`slide_${e}`),a={descriptionTextAlign:"start",descriptionMaxLines:3,showToggle:!1,hidden:!1},u=e=>({...a,...e}),c=r.createContext(null),s=(0,l.Fy)("useCaptions","CaptionsContext",c);function d({captions:e,children:t}){let{ref:n,hidden:l}=u(e),[i,o]=r.useState(!l),a=r.useMemo(()=>({visible:i,show:()=>o(!0),hide:()=>o(!1)}),[i]);return r.useImperativeHandle(n,()=>a,[a]),r.createElement(c.Provider,{value:a},t)}function h({title:e}){let{toolbarWidth:t}=(0,l.bc)(),{styles:n}=(0,l.RD)(),{visible:i}=s();return i?r.createElement("div",{style:n.captionsTitleContainer,className:(0,l.Wy)(o("captions_container"),o("title_container"))},r.createElement("div",{className:o("title"),style:{...t?{[(0,l.gJ)("toolbar_width")]:`${t}px`}:null,...n.captionsTitle}},e)):null}function f({description:e}){let{descriptionTextAlign:t,descriptionMaxLines:n}=function(){let{captions:e}=(0,l.RD)();return u(e)}(),{styles:i}=(0,l.RD)(),{visible:c}=s();return c?r.createElement("div",{style:i.captionsDescriptionContainer,className:(0,l.Wy)(o("captions_container"),o("description_container"))},r.createElement("div",{className:o("description"),style:{...t!==a.descriptionTextAlign||n!==a.descriptionMaxLines?{[(0,l.gJ)("slide_description_text_align")]:t,[(0,l.gJ)("slide_description_max_lines")]:n}:null,...i.captionsDescription}},"string"==typeof e?e.split("\n").flatMap((e,t)=>[...t>0?[r.createElement("br",{key:t})]:[],e]):e)):null}let v=()=>r.createElement(r.Fragment,null,r.createElement("path",{strokeWidth:2,stroke:"currentColor",strokeLinejoin:"round",fill:"none",d:"M3 5l18 0l0 14l-18 0l0-14z"}),r.createElement("path",{d:"M7 15h3c.55 0 1-.45 1-1v-1H9.5v.5h-2v-3h2v.5H11v-1c0-.55-.45-1-1-1H7c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1zm7 0h3c.55 0 1-.45 1-1v-1h-1.5v.5h-2v-3h2v.5H18v-1c0-.55-.45-1-1-1h-3c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1z"})),m=(0,l.U2)("CaptionsVisible",v()),p=(0,l.TX)("CaptionsVisible",v());function g(){let{visible:e,show:t,hide:n}=s(),{render:i}=(0,l.RD)();return i.buttonCaptions?r.createElement(r.Fragment,null,i.buttonCaptions({visible:e,show:t,hide:n})):r.createElement(l.hU,{label:e?"Hide captions":"Show captions",icon:e?m:p,renderIcon:e?i.iconCaptionsVisible:i.iconCaptionsHidden,onClick:e?n:t})}function E({augment:e,addModule:t}){e(({captions:e,render:{slideFooter:t,...n},toolbar:o,...a})=>{let c=u(e);return{render:{slideFooter:({slide:e})=>r.createElement(r.Fragment,null,null==t?void 0:t({slide:e}),e.title&&r.createElement(h,{title:e.title}),e.description&&r.createElement(f,{description:e.description})),...n},toolbar:(0,l.wQ)(o,i.JT,c.showToggle?r.createElement(g,null):null),captions:c,...a}}),t((0,l.l6)(i.JT,d))}},50526:(e,t,n)=>{"use strict";n.d(t,{Z:()=>h});var r=n(60343),l=n(12513),i=n(77257);let o={download:void 0},a=e=>({...o,...e});function u(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(n){let t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(t)}}function c(e,t){let n=document.createElement("a");n.rel="noopener",n.download=t||"",n.download||(n.target="_blank"),"string"==typeof e?(n.href=e,n.origin!==window.location.origin?function(e){let t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(e){}return t.status>=200&&t.status<=299}(n.href)?function(e,t){let n=new XMLHttpRequest;n.open("GET",e),n.responseType="blob",n.onload=()=>{c(n.response,t)},n.onerror=()=>{console.error("Failed to download file")},n.send()}(e,t):(n.target="_blank",u(n)):u(n)):(n.href=URL.createObjectURL(e),setTimeout(()=>URL.revokeObjectURL(n.href),3e4),setTimeout(()=>u(n),0))}let s=(0,l.U2)("DownloadIcon",r.createElement("path",{d:"M18 15v3H6v-3H4v3c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-3h-2zm-1-4-1.41-1.41L13 12.17V4h-2v8.17L8.41 9.59 7 11l5 5 5-5z"}));function d(){let{render:e,on:t,download:n}=(0,l.RD)(),{download:i}=a(n),{currentSlide:o,currentIndex:u}=(0,l.oc)();if(e.buttonDownload)return r.createElement(r.Fragment,null,e.buttonDownload());let d=o&&(o.downloadUrl||"string"==typeof o.download&&o.download||"object"==typeof o.download&&o.download.url||(0,l.QB)(o)&&o.src)||void 0,h=i?(null==o?void 0:o.download)!==!1:!!d,f=()=>{o&&d&&c(d,o.downloadFilename||"object"==typeof o.download&&o.download.filename||void 0)};return r.createElement(l.hU,{label:"Download",icon:s,renderIcon:e.iconDownload,disabled:!h,onClick:()=>{var e;o&&((i||f)({slide:o,saveAs:c}),null===(e=t.download)||void 0===e||e.call(t,{index:u}))}})}function h({augment:e}){e(({toolbar:e,download:t,...n})=>({toolbar:(0,l.wQ)(e,i.J3,r.createElement(d,null)),download:a(t),...n}))}},7179:(e,t,n)=>{"use strict";n.d(t,{Z:()=>v});var r=n(60343),l=n(12513),i=n(77257);let o={auto:!1,ref:null},a=e=>({...o,...e}),u=r.createContext(null),c=(0,l.Fy)("useFullscreen","FullscreenContext",u);function s({fullscreen:e,on:t,children:n}){let{auto:o,ref:c}=a(e),s=r.useRef(null),[d,h]=r.useState(),[f,v]=r.useState(!1),m=r.useRef(!1),{getOwnerDocument:p}=(0,l.tS)();(0,l.bt)(()=>{var e,t,n,r;let l=p();h(!(null!==(r=null!==(n=null!==(t=null!==(e=l.fullscreenEnabled)&&void 0!==e?e:l.webkitFullscreenEnabled)&&void 0!==t?t:l.mozFullScreenEnabled)&&void 0!==n?n:l.msFullscreenEnabled)&&void 0!==r&&r))},[p]);let g=r.useCallback(()=>{var e;let t=p(),n=t.fullscreenElement||t.webkitFullscreenElement||t.mozFullScreenElement||t.msFullscreenElement;return(null===(e=null==n?void 0:n.shadowRoot)||void 0===e?void 0:e.fullscreenElement)||n},[p]),E=r.useCallback(()=>{let e=s.current;try{e.requestFullscreen?e.requestFullscreen().catch(()=>{}):e.webkitRequestFullscreen?e.webkitRequestFullscreen():e.mozRequestFullScreen?e.mozRequestFullScreen():e.msRequestFullscreen&&e.msRequestFullscreen()}catch(e){}},[]),b=r.useCallback(()=>{if(!g())return;let e=p();try{e.exitFullscreen?e.exitFullscreen().catch(()=>{}):e.webkitExitFullscreen?e.webkitExitFullscreen():e.mozCancelFullScreen?e.mozCancelFullScreen():e.msExitFullscreen&&e.msExitFullscreen()}catch(e){}},[g,p]);r.useEffect(()=>{let e=p(),t=()=>{v(g()===s.current)};return(0,l.Eq)(...["fullscreenchange","webkitfullscreenchange","mozfullscreenchange","MSFullscreenChange"].map(n=>(e.addEventListener(n,t),()=>e.removeEventListener(n,t))))},[g,p]);let w=(0,l.$0)(()=>{var e;return null===(e=t.enterFullscreen)||void 0===e?void 0:e.call(t)}),y=(0,l.$0)(()=>{var e;return null===(e=t.exitFullscreen)||void 0===e?void 0:e.call(t)});r.useEffect(()=>{f&&(m.current=!0),m.current&&(f?w:y)()},[f,w,y]);let x=(0,l.$0)(()=>{var e;return null===(e=o?E:null)||void 0===e||e(),b});r.useEffect(x,[x]);let M=r.useMemo(()=>({fullscreen:f,disabled:d,enter:E,exit:b}),[f,d,E,b]);return r.useImperativeHandle(c,()=>M,[M]),r.createElement("div",{ref:s,className:(0,l.Wy)((0,l.Nc)(i.zr),(0,l.Nc)(i.yS))},r.createElement(u.Provider,{value:M},n))}let d=(0,l.U2)("EnterFullscreen",r.createElement("path",{d:"M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"})),h=(0,l.U2)("ExitFullscreen",r.createElement("path",{d:"M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"}));function f(){var e;let{fullscreen:t,disabled:n,enter:i,exit:o}=c(),{render:a}=(0,l.RD)();return n?null:a.buttonFullscreen?r.createElement(r.Fragment,null,null===(e=a.buttonFullscreen)||void 0===e?void 0:e.call(a,{fullscreen:t,disabled:n,enter:i,exit:o})):r.createElement(l.hU,{disabled:n,label:t?"Exit Fullscreen":"Enter Fullscreen",icon:t?h:d,renderIcon:t?a.iconExitFullscreen:a.iconEnterFullscreen,onClick:t?o:i})}function v({augment:e,contains:t,addParent:n}){e(({fullscreen:e,toolbar:t,...n})=>({toolbar:(0,l.wQ)(t,i.zr,r.createElement(f,null)),fullscreen:a(e),...n})),n(t(i.dA)?i.dA:i.l4,(0,l.l6)(i.zr,s))}},27780:(e,t,n)=>{"use strict";n.d(t,{Z:()=>x});var r=n(60343),l=n(12513),i=n(77257);let o={maxZoomPixelRatio:1,zoomInMultiplier:2,doubleTapDelay:300,doubleClickDelay:500,doubleClickMaxStops:2,keyboardMoveDistance:50,wheelZoomDistanceFactor:100,pinchZoomDistanceFactor:100,scrollToZoom:!1},a=e=>({...o,...e});function u(){let{zoom:e}=(0,l.RD)();return a(e)}function c(e,t){return((e.clientX-t.clientX)**2+(e.clientY-t.clientY)**2)**.5}function s(e,t,n=100,r=2){return e*Math.min(1+Math.abs(t/n),r)**Math.sign(t)}let d=r.createContext(null),h=(0,l.Fy)("useZoom","ZoomControllerContext",d);function f({children:e}){let[t,n]=r.useState(),{slideRect:o}=(0,l.bc)(),{imageRect:a,maxZoom:h}=function(e,t){var n,r;let i={width:0,height:0},o={width:0,height:0},{currentSlide:a}=(0,l.oc)(),{imageFit:c}=(0,l.RD)().carousel,{maxZoomPixelRatio:s}=u();if(e&&a){let u={...a,...t};if((0,l.QB)(u)){let t=(0,l.VI)(u,c),a=Math.max(...((null===(n=u.srcSet)||void 0===n?void 0:n.map(e=>e.width))||[]).concat(u.width?[u.width]:[])),d=Math.max(...((null===(r=u.srcSet)||void 0===r?void 0:r.map(e=>e.height))||[]).concat(u.height?[u.height]:[]));a>0&&d>0&&e.width>0&&e.height>0&&(o={width:(o=t?{width:Math.round(Math.min(a,e.width/e.height*d)),height:Math.round(Math.min(d,e.height/e.width*a))}:{width:a,height:d}).width*s,height:o.height*s},i=t?{width:Math.min(e.width,o.width,a),height:Math.min(e.height,o.height,d)}:{width:Math.round(Math.min(e.width,e.height/d*a,a)),height:Math.round(Math.min(e.height,e.width/a*d,d))})}}let d=i.width?Math.max((0,l.NM)(o.width/i.width,5),1):1;return{imageRect:i,maxZoom:d}}(o,null==t?void 0:t.imageDimensions),{zoom:f,offsetX:v,offsetY:m,disabled:p,changeZoom:g,changeOffsets:E,zoomIn:b,zoomOut:w}=function(e,t,n){let[i,o]=r.useState(1),[a,c]=r.useState(0),[s,d]=r.useState(0),h=function(e,t,n,i){let o=r.useRef(void 0),a=r.useRef(void 0),{zoom:u}=(0,l.RD)().animation,c=(0,l.OL)(),s=(0,l.$0)(()=>{var r,l,s;if(null===(r=o.current)||void 0===r||r.cancel(),o.current=void 0,a.current&&(null==i?void 0:i.current)){try{o.current=null===(s=(l=i.current).animate)||void 0===s?void 0:s.call(l,[{transform:a.current},{transform:`scale(${e}) translateX(${t}px) translateY(${n}px)`}],{duration:c?0:null!=u?u:500,easing:o.current?"ease-out":"ease-in-out"})}catch(e){console.error(e)}a.current=void 0,o.current&&(o.current.onfinish=()=>{o.current=void 0})}});return(0,l.bt)(s,[e,t,n,s]),r.useCallback(()=>{a.current=(null==i?void 0:i.current)?window.getComputedStyle(i.current).transform:void 0},[i])}(i,a,s,n),{currentSlide:f,globalIndex:v}=(0,l.oc)(),{containerRect:m,slideRect:p}=(0,l.bc)(),{zoomInMultiplier:g}=u(),E=f&&(0,l.QB)(f)?f.src:void 0,b=!E||!(null==n?void 0:n.current);(0,l.bt)(()=>{o(1),c(0),d(0)},[v,E]);let w=r.useCallback((t,n,r)=>{let l=r||i,o=a-(t||0),u=s-(n||0),h=(e.width*l-p.width)/2/l,f=(e.height*l-p.height)/2/l;c(Math.min(Math.abs(o),Math.max(h,0))*Math.sign(o)),d(Math.min(Math.abs(u),Math.max(f,0))*Math.sign(u))},[i,a,s,p,e.width,e.height]),y=r.useCallback((e,n,r,a)=>{let u=(0,l.NM)(Math.min(Math.max(e+.001<t?e:t,1),t),5);u!==i&&(n||h(),w(r?r*(1/i-1/u):0,a?a*(1/i-1/u):0,u),o(u))},[i,t,w,h]),x=(0,l.$0)(()=>{i>1&&(i>t&&y(t,!0),w())});(0,l.bt)(x,[m.width,m.height,x]);let M=r.useCallback(()=>y(i*g),[i,g,y]),C=r.useCallback(()=>y(i/g),[i,g,y]);return{zoom:i,offsetX:a,offsetY:s,disabled:b,changeOffsets:w,changeZoom:y,zoomIn:M,zoomOut:C}}(a,h,null==t?void 0:t.zoomWrapperRef);(function(e,t){let{on:n}=(0,l.RD)(),i=(0,l.$0)(()=>{var r;t||null===(r=n.zoom)||void 0===r||r.call(n,{zoom:e})});r.useEffect(i,[e,i])})(f,p),function(e,t,n,o,a,d){let h=r.useRef([]),f=r.useRef(0),v=r.useRef(void 0),{globalIndex:m}=(0,l.oc)(),{getOwnerWindow:p}=(0,l.tS)(),{containerRef:g,subscribeSensors:E}=(0,l.bc)(),{keyboardMoveDistance:b,zoomInMultiplier:w,wheelZoomDistanceFactor:y,scrollToZoom:x,doubleTapDelay:M,doubleClickDelay:C,doubleClickMaxStops:k,pinchZoomDistanceFactor:N}=u(),S=r.useCallback(e=>{if(g.current){let{pageX:t,pageY:n}=e,{scrollX:r,scrollY:l}=p(),{left:i,top:o,width:a,height:u}=g.current.getBoundingClientRect();return[t-i-r-a/2,n-o-l-u/2]}return[]},[g,p]),R=(0,l.$0)(t=>{let{key:n,metaKey:r,ctrlKey:l}=t,i=r||l,u=()=>{t.preventDefault(),t.stopPropagation()};if(e>1){let e=(e,t)=>{u(),a(e,t)};"ArrowDown"===n?e(0,b):"ArrowUp"===n?e(0,-b):"ArrowLeft"===n?e(-b,0):"ArrowRight"===n&&e(b,0)}let c=e=>{u(),o(e)};"+"===n||i&&"="===n?c(e*w):"-"===n||i&&"_"===n?c(e/w):i&&"0"===n&&c(1)}),L=(0,l.$0)(t=>{if((t.ctrlKey||x)&&Math.abs(t.deltaY)>Math.abs(t.deltaX)){t.stopPropagation(),o(s(e,-t.deltaY,y),!0,...S(t));return}e>1&&(t.stopPropagation(),x||a(t.deltaX,t.deltaY))}),P=r.useCallback(e=>{let t=h.current;t.splice(0,t.length,...t.filter(t=>t.pointerId!==e.pointerId))},[]),F=r.useCallback(e=>{P(e),e.persist(),h.current.push(e)},[P]),I=(0,l.$0)(n=>{var r;let l=h.current;if("mouse"===n.pointerType&&n.buttons>1||!(null===(r=null==d?void 0:d.current)||void 0===r?void 0:r.contains(n.target)))return;e>1&&n.stopPropagation();let{timeStamp:i}=n;0===l.length&&i-f.current<("touch"===n.pointerType?M:C)?(f.current=0,o(e!==t?e*Math.max(t**(1/k),w):1,!1,...S(n))):f.current=i,F(n),2===l.length&&(v.current=c(l[0],l[1]))}),D=(0,l.$0)(t=>{let n=h.current,r=n.find(e=>e.pointerId===t.pointerId);if(2===n.length&&v.current){t.stopPropagation(),F(t);let r=c(n[0],n[1]),l=r-v.current;Math.abs(l)>0&&(o(s(e,l,N),!0,...n.map(e=>S(e)).reduce((e,t)=>t.map((t,n)=>e[n]+t/2))),v.current=r);return}e>1&&(t.stopPropagation(),r&&(1===n.length&&a((r.clientX-t.clientX)/e,(r.clientY-t.clientY)/e),F(t)))}),$=r.useCallback(e=>{let t=h.current;2===t.length&&t.find(t=>t.pointerId===e.pointerId)&&(v.current=void 0),P(e)},[P]),z=r.useCallback(()=>{let e=h.current;e.splice(0,e.length),f.current=0,v.current=void 0},[]);(0,l.bQ)(E,I,D,$,n),r.useEffect(z,[m,z]),r.useEffect(()=>n?()=>{}:(0,l.Eq)(z,E(i.ds,R),E(i.yq,L)),[n,E,z,R,L])}(f,h,p,g,E,null==t?void 0:t.zoomWrapperRef);let y=r.useMemo(()=>({zoom:f,maxZoom:h,offsetX:v,offsetY:m,disabled:p,zoomIn:b,zoomOut:w,changeZoom:g}),[f,h,v,m,p,b,w,g]);r.useImperativeHandle(u().ref,()=>y,[y]);let x=r.useMemo(()=>({...y,setZoomWrapper:n}),[y,n]);return r.createElement(d.Provider,{value:x},e)}let v=(0,l.U2)("ZoomIn",r.createElement(r.Fragment,null,r.createElement("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}),r.createElement("path",{d:"M12 10h-2v2H9v-2H7V9h2V7h1v2h2v1z"}))),m=(0,l.U2)("ZoomOut",r.createElement("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h5v1H7z"})),p=r.forwardRef(function({zoomIn:e,onLoseFocus:t},n){let i=r.useRef(!1),o=r.useRef(!1),{zoom:a,maxZoom:u,zoomIn:c,zoomOut:s,disabled:d}=h(),{render:f}=(0,l.RD)(),p=d||(e?a>=u:a<=1);return r.useEffect(()=>{p&&i.current&&o.current&&t(),p||(i.current=!0)},[p,t]),r.createElement(l.hU,{ref:n,disabled:p,label:e?"Zoom in":"Zoom out",icon:e?v:m,renderIcon:e?f.iconZoomIn:f.iconZoomOut,onClick:e?c:s,onFocus:()=>{o.current=!0},onBlur:()=>{o.current=!1}})});function g(){let e=r.useRef(null),t=r.useRef(null),{focus:n}=(0,l.bc)(),i=r.useCallback(e=>{var t,r;(null===(t=e.current)||void 0===t?void 0:t.disabled)?n():null===(r=e.current)||void 0===r||r.focus()},[n]),o=r.useCallback(()=>i(e),[i]),a=r.useCallback(()=>i(t),[i]);return r.createElement(r.Fragment,null,r.createElement(p,{zoomIn:!0,ref:e,onLoseFocus:a}),r.createElement(p,{ref:t,onLoseFocus:o}))}function E(){let{render:e}=(0,l.RD)(),t=h();return e.buttonZoom?r.createElement(r.Fragment,null,e.buttonZoom(t)):r.createElement(g,null)}function b({current:e,preload:t},{type:n,source:r}){switch(n){case"fetch":if(!e)return{current:r};return{current:e,preload:r};case"done":if(r===t)return{current:r};return{current:e,preload:t};default:throw Error(i.c3)}}function w(e){var t,n;let[{current:i,preload:o},a]=r.useReducer(b,{}),{slide:u,rect:c,imageFit:s,render:d,interactive:h}=e,f=u.srcSet.sort((e,t)=>e.width-t.width),v=null!==(t=u.width)&&void 0!==t?t:f[f.length-1].width,m=null!==(n=u.height)&&void 0!==n?n:f[f.length-1].height,p=(0,l.VI)(u,s),g=Math.max(...f.map(e=>e.width)),E=Math.min((p?Math.max:Math.min)(c.width,c.height/m*v),g),w=(0,l.KL)(),y=(0,l.$0)(()=>{var e;let t=null!==(e=f.find(e=>e.width>=E*w))&&void 0!==e?e:f[f.length-1];(!i||f.findIndex(e=>e.src===i)<f.findIndex(e=>e===t))&&a({type:"fetch",source:t.src})});(0,l.bt)(y,[c.width,c.height,w,y]);let x=(0,l.$0)(e=>a({type:"done",source:e})),M={WebkitTransform:h?"initial":"translateZ(0)"};return p||Object.assign(M,c.width/c.height<v/m?{width:"100%",height:"auto"}:{width:"auto",height:"100%"}),r.createElement(r.Fragment,null,o&&o!==i&&r.createElement(l.P0,{key:"preload",...e,offset:void 0,slide:{...u,src:o,srcSet:void 0},style:{position:"absolute",visibility:"hidden",...M},onLoad:()=>x(o),render:{...d,iconLoading:()=>null,iconError:()=>null}}),i&&r.createElement(l.P0,{key:"current",...e,slide:{...u,src:i,srcSet:void 0},style:M}))}function y({render:e,slide:t,offset:n,rect:o}){var a,u;let[c,s]=r.useState(),d=r.useRef(null),{zoom:f,maxZoom:v,offsetX:m,offsetY:p,setZoomWrapper:g}=h(),E=f>1,{carousel:b,on:y}=(0,l.RD)(),{currentIndex:x}=(0,l.oc)();(0,l.bt)(()=>0===n?(g({zoomWrapperRef:d,imageDimensions:c}),()=>g(void 0)):()=>{},[n,c,g]);let M=null===(a=e.slide)||void 0===a?void 0:a.call(e,{slide:t,offset:n,rect:o,zoom:f,maxZoom:v});if(!M&&(0,l.QB)(t)){let i={slide:t,offset:n,rect:o,render:e,imageFit:b.imageFit,imageProps:b.imageProps,onClick:0===n?()=>{var e;return null===(e=y.click)||void 0===e?void 0:e.call(y,{index:x})}:void 0};M=((null===(u=t.srcSet)||void 0===u?void 0:u.length)||0)>0?r.createElement(w,{...i,slide:t,interactive:E,rect:0===n?{width:o.width*f,height:o.height*f}:o}):r.createElement(l.P0,{onLoad:e=>s({width:e.naturalWidth,height:e.naturalHeight}),...i})}return M?r.createElement("div",{ref:d,className:(0,l.Wy)((0,l.Nc)(i.yS),(0,l.Nc)(i.aN),(0,l.Nc)(i.af),E&&(0,l.Nc)(i.ZZ)),style:0===n?{transform:`scale(${f}) translateX(${m}px) translateY(${p}px)`}:void 0},M):null}let x=({augment:e,addModule:t})=>{e(({zoom:e,toolbar:t,render:n,controller:o,...u})=>{let c=a(e);return{zoom:c,toolbar:(0,l.wQ)(t,i.xc,r.createElement(E,null)),render:{...n,slide:e=>{var t;return(0,l.QB)(e.slide)?r.createElement(y,{render:n,...e}):null===(t=n.slide)||void 0===t?void 0:t.call(n,e)}},controller:{...o,preventDefaultWheelY:c.scrollToZoom},...u}}),t((0,l.l6)(i.xc,f))}},77257:(e,t,n)=>{"use strict";n.d(t,{$L:()=>E,Bm:()=>_,Eb:()=>R,HE:()=>o,J1:()=>b,J3:()=>d,JT:()=>s,KN:()=>S,M9:()=>M,N4:()=>I,NH:()=>A,NZ:()=>F,Op:()=>i,PU:()=>O,S2:()=>D,SA:()=>a,Sl:()=>Z,Tf:()=>x,Tn:()=>L,Vt:()=>z,Xe:()=>m,ZZ:()=>N,Zv:()=>g,aN:()=>y,af:()=>k,bg:()=>W,c3:()=>q,dA:()=>f,ds:()=>T,fS:()=>p,hD:()=>C,hb:()=>c,j3:()=>X,k0:()=>r,l4:()=>l,lT:()=>u,pE:()=>$,rO:()=>V,t9:()=>P,vg:()=>U,xc:()=>v,yS:()=>w,yq:()=>H,zr:()=>h});let r="carousel",l="controller",i="navigation",o="no-scroll",a="portal",u="root",c="toolbar",s="captions",d="download",h="fullscreen",f="thumbnails",v="zoom",m="loading",p="error",g="complete",E="placeholder",b=e=>`active-slide-${e}`;b(m),b("playing"),b(p),b(g);let w="fullsize",y="flex_center",x="no_scroll",M="no_scroll_padding",C="slide",k="slide_wrapper",N="slide_wrapper_interactive",S="prev",R="next",L="swipe",P="close",F="onPointerDown",I="onPointerMove",D="onPointerUp",$="onPointerLeave",z="onPointerCancel",T="onKeyDown",_="onKeyUp",H="onWheel",O="Escape",Z="ArrowLeft",A="ArrowRight",W="button",U="icon",X="contain",V="cover",q="Unknown action type"}};