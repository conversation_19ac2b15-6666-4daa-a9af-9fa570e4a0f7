(()=>{var e={};e.id=6500,e.ids=[6500,9414],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},3174:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>f,originalPathname:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>l}),r(99835),r(9276),r(78398),r(57757),r(48045);var n=r(40060),s=r(33581),i=r(57567),a=r.n(i),u=r(51650),o={};for(let e in u)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>u[e]);r.d(t,o);let l=["",{children:["trip-report-schedules",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,99835)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-report-schedules\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,9276)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-report-schedules\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-report-schedules\\page.tsx"],d="/trip-report-schedules/page",f={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/trip-report-schedules/page",pathname:"/trip-report-schedules",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},8287:(e,t,r)=>{Promise.resolve().then(r.bind(r,85075))},21812:(e,t,r)=>{Promise.resolve().then(r.bind(r,30308))},83179:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",r="minute",n="hour",s="week",i="month",a="quarter",u="year",o="date",l="Invalid Date",c=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,d=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,f=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},h="en",p={};p[h]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||"th")+"]"}};var m="$isDayjsObject",g=function(e){return e instanceof y||!(!e||!e[m])},x=function e(t,r,n){var s;if(!t)return h;if("string"==typeof t){var i=t.toLowerCase();p[i]&&(s=i),r&&(p[i]=r,s=i);var a=t.split("-");if(!s&&a.length>1)return e(a[0])}else{var u=t.name;p[u]=t,s=u}return!n&&s&&(h=s),s||!n&&h},$=function(e,t){if(g(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new y(r)},v={s:f,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+f(Math.floor(r/60),2,"0")+":"+f(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),s=t.clone().add(n,i),a=r-s<0,u=t.clone().add(n+(a?-1:1),i);return+(-(n+(r-s)/(a?s-u:u-s))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(l){return({M:i,y:u,w:s,d:"day",D:o,h:n,m:r,s:t,ms:e,Q:a})[l]||String(l||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};v.l=x,v.i=g,v.w=function(e,t){return $(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var y=function(){function f(e){this.$L=x(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[m]=!0}var h=f.prototype;return h.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(v.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(c);if(n){var s=n[2]-1||0,i=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,i)):new Date(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,i)}}return new Date(t)}(e),this.init()},h.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},h.$utils=function(){return v},h.isValid=function(){return this.$d.toString()!==l},h.isSame=function(e,t){var r=$(e);return this.startOf(t)<=r&&r<=this.endOf(t)},h.isAfter=function(e,t){return $(e)<this.startOf(t)},h.isBefore=function(e,t){return this.endOf(t)<$(e)},h.$g=function(e,t,r){return v.u(e)?this[t]:this.set(r,e)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(e,a){var l=this,c=!!v.u(a)||a,d=v.p(e),f=function(e,t){var r=v.w(l.$u?Date.UTC(l.$y,t,e):new Date(l.$y,t,e),l);return c?r:r.endOf("day")},h=function(e,t){return v.w(l.toDate()[e].apply(l.toDate("s"),(c?[0,0,0,0]:[23,59,59,999]).slice(t)),l)},p=this.$W,m=this.$M,g=this.$D,x="set"+(this.$u?"UTC":"");switch(d){case u:return c?f(1,0):f(31,11);case i:return c?f(1,m):f(0,m+1);case s:var $=this.$locale().weekStart||0,y=(p<$?p+7:p)-$;return f(c?g-y:g+(6-y),m);case"day":case o:return h(x+"Hours",0);case n:return h(x+"Minutes",1);case r:return h(x+"Seconds",2);case t:return h(x+"Milliseconds",3);default:return this.clone()}},h.endOf=function(e){return this.startOf(e,!1)},h.$set=function(s,a){var l,c=v.p(s),d="set"+(this.$u?"UTC":""),f=((l={}).day=d+"Date",l[o]=d+"Date",l[i]=d+"Month",l[u]=d+"FullYear",l[n]=d+"Hours",l[r]=d+"Minutes",l[t]=d+"Seconds",l[e]=d+"Milliseconds",l)[c],h="day"===c?this.$D+(a-this.$W):a;if(c===i||c===u){var p=this.clone().set(o,1);p.$d[f](h),p.init(),this.$d=p.set(o,Math.min(this.$D,p.daysInMonth())).$d}else f&&this.$d[f](h);return this.init(),this},h.set=function(e,t){return this.clone().$set(e,t)},h.get=function(e){return this[v.p(e)]()},h.add=function(e,a){var o,l=this;e=Number(e);var c=v.p(a),d=function(t){var r=$(l);return v.w(r.date(r.date()+Math.round(t*e)),l)};if(c===i)return this.set(i,this.$M+e);if(c===u)return this.set(u,this.$y+e);if("day"===c)return d(1);if(c===s)return d(7);var f=((o={})[r]=6e4,o[n]=36e5,o[t]=1e3,o)[c]||1,h=this.$d.getTime()+e*f;return v.w(h,this)},h.subtract=function(e,t){return this.add(-1*e,t)},h.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||l;var n=e||"YYYY-MM-DDTHH:mm:ssZ",s=v.z(this),i=this.$H,a=this.$m,u=this.$M,o=r.weekdays,c=r.months,f=r.meridiem,h=function(e,r,s,i){return e&&(e[r]||e(t,n))||s[r].slice(0,i)},p=function(e){return v.s(i%12||12,e,"0")},m=f||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n};return n.replace(d,function(e,n){return n||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return v.s(t.$y,4,"0");case"M":return u+1;case"MM":return v.s(u+1,2,"0");case"MMM":return h(r.monthsShort,u,c,3);case"MMMM":return h(c,u);case"D":return t.$D;case"DD":return v.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return h(r.weekdaysMin,t.$W,o,2);case"ddd":return h(r.weekdaysShort,t.$W,o,3);case"dddd":return o[t.$W];case"H":return String(i);case"HH":return v.s(i,2,"0");case"h":return p(1);case"hh":return p(2);case"a":return m(i,a,!0);case"A":return m(i,a,!1);case"m":return String(a);case"mm":return v.s(a,2,"0");case"s":return String(t.$s);case"ss":return v.s(t.$s,2,"0");case"SSS":return v.s(t.$ms,3,"0");case"Z":return s}return null}(e)||s.replace(":","")})},h.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},h.diff=function(e,o,l){var c,d=this,f=v.p(o),h=$(e),p=(h.utcOffset()-this.utcOffset())*6e4,m=this-h,g=function(){return v.m(d,h)};switch(f){case u:c=g()/12;break;case i:c=g();break;case a:c=g()/3;break;case s:c=(m-p)/6048e5;break;case"day":c=(m-p)/864e5;break;case n:c=m/36e5;break;case r:c=m/6e4;break;case t:c=m/1e3;break;default:c=m}return l?c:v.a(c)},h.daysInMonth=function(){return this.endOf(i).$D},h.$locale=function(){return p[this.$L]},h.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=x(e,t,!0);return n&&(r.$L=n),r},h.clone=function(){return v.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},f}(),b=y.prototype;return $.prototype=b,[["$ms",e],["$s",t],["$m",r],["$H",n],["$W","day"],["$M",i],["$y",u],["$D",o]].forEach(function(e){b[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),$.extend=function(e,t){return e.$i||(e(t,y,$),e.$i=!0),$},$.locale=x,$.isDayjs=g,$.unix=function(e){return $(1e3*e)},$.en=p[h],$.Ls=p,$.p={},$},e.exports=t()},18479:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,s=Array(n);++r<n;)s[r]=t(e[r],r,e);return s}},60826:e=>{e.exports=function(e){return e.split("")}},829:e=>{e.exports=function(e,t,r,n){for(var s=e.length,i=r+(n?1:-1);n?i--:++i<s;)if(t(e[i],i,e))return i;return -1}},65337:(e,t,r)=>{var n=r(829),s=r(35447),i=r(28026);e.exports=function(e,t,r){return t==t?i(e,t,r):n(e,s,r)}},35447:e=>{e.exports=function(e){return e!=e}},77420:e=>{e.exports=function(e,t,r){var n=-1,s=e.length;t<0&&(t=-t>s?0:s+t),(r=r>s?s:r)<0&&(r+=s),s=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(s);++n<s;)i[n]=e[n+t];return i}},22060:(e,t,r)=>{var n=r(51858),s=r(18479),i=r(55813),a=r(15903),u=1/0,o=n?n.prototype:void 0,l=o?o.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return s(t,e)+"";if(a(t))return l?l.call(t):"";var r=t+"";return"0"==r&&1/t==-u?"-0":r}},49513:(e,t,r)=>{var n=r(70458),s=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(s,""):e}},30482:(e,t,r)=>{var n=r(77420);e.exports=function(e,t,r){var s=e.length;return r=void 0===r?s:r,!t&&r>=s?e:n(e,t,r)}},74783:(e,t,r)=>{var n=r(65337);e.exports=function(e,t){for(var r=e.length;r--&&n(t,e[r],0)>-1;);return r}},41200:(e,t,r)=>{var n=r(65337);e.exports=function(e,t){for(var r=-1,s=e.length;++r<s&&n(t,e[r],0)>-1;);return r}},73211:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},28026:e=>{e.exports=function(e,t,r){for(var n=r-1,s=e.length;++n<s;)if(e[n]===t)return n;return -1}},66095:(e,t,r)=>{var n=r(60826),s=r(73211),i=r(92115);e.exports=function(e){return s(e)?i(e):n(e)}},70458:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},92115:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",s="[^"+t+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",u="(?:"+r+"|"+n+")?",o="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[s,i,a].join("|")+")"+o+u+")*",c=RegExp(n+"(?="+n+")|(?:"+[s+r+"?",r,i,a,"["+t+"]"].join("|")+")"+(o+u+l),"g");e.exports=function(e){return e.match(c)||[]}},15903:(e,t,r)=>{var n=r(55296),s=r(48377);e.exports=function(e){return"symbol"==typeof e||s(e)&&"[object Symbol]"==n(e)}},16266:(e,t,r)=>{var n=r(22060);e.exports=function(e){return null==e?"":n(e)}},14826:(e,t,r)=>{var n=r(22060),s=r(49513),i=r(30482),a=r(74783),u=r(41200),o=r(66095),l=r(16266);e.exports=function(e,t,r){if((e=l(e))&&(r||void 0===t))return s(e);if(!e||!(t=n(t)))return e;var c=o(e),d=o(t),f=u(c,d),h=a(c,d)+1;return i(c,f,h).join("")}},3233:(e,t,r)=>{var n=r(16266),s=0;e.exports=function(e){var t=++s;return n(e)+t}},75546:(e,t,r)=>{"use strict";r.d(t,{Br:()=>f,fU:()=>h,o0:()=>c,p6:()=>l,vq:()=>d});var n=r(83179),s=r.n(n),i=r(7678),a=r.n(i),u=r(14826),o=r.n(u);let l=(e="",t=!0)=>{let r;if(a()(o()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,n,s]=e.split("-"),i=t?r.slice(-2):r,a=parseInt(s,10).toString().padStart(2,"0"),u=parseInt(n,10).toString().padStart(2,"0");return`${a}/${u}/${i}`}if(!(r=e&&"object"==typeof e?s()(e.toString()):s()(e)).isValid())return"";let n=r.format("DD"),i=r.format("MM"),u=t?r.format("YY"):r.format("YYYY");return`${n}/${i}/${u}`},c=(e="",t=!0)=>{let r;if(a()(o()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,n,s]=e.split("-"),i=t?r.slice(-2):r,a=parseInt(s,10).toString().padStart(2,"0"),u=parseInt(n,10).toString().padStart(2,"0");return`${a}/${u}/${i} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[r,n]=e.split(" "),[s,i,a]=r.split("-"),u=t?s.slice(-2):s,o=n.split(":"),l=o[0].padStart(2,"0"),c=o[1].padStart(2,"0"),d=parseInt(a,10).toString().padStart(2,"0"),f=parseInt(i,10).toString().padStart(2,"0");return`${d}/${f}/${u} ${l}:${c}`}if(!(r=e&&"object"==typeof e?s()(e.toString()):s()(e)).isValid())return"";let n=r.format("DD"),i=r.format("MM"),u=t?r.format("YY"):r.format("YYYY"),l=r.format("HH:mm");return`${n}/${i}/${u} ${l}`},d=(e="")=>a()(o()(e))?"":s()(e).format("YYYY-MM-DD HH:mm:ss"),f=(e="")=>a()(o()(e))?new Date:new Date(`${e}T10:00:00Z`),h=(e,t)=>{let r=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),n=e=>e.includes(" ")?e.replace(" ","T"):e,s=e=>{if(!e||"string"!=typeof e)return null;if(r(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(n(e))},i=s(e),a=s(t);return!i||!a||isNaN(i.getTime())||isNaN(a.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):a>i}},85075:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(98768);r(60343);var s=r(64837);function i({children:e}){return n.jsx(s.Z,{children:e})}},30308:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>$});var n=r(98768),s=r(26100),i=r(46776),a=r(60343),u=r(43692),o=r(75546),l=r(94060),c=r(60797),d=r(79418),f=r(83179),h=r.n(f),p=r(69424),m=r(89546),g=r(66263);let x=()=>{(0,p.useRouter)();let[e,t]=(0,a.useState)(!0),[r,s]=(0,a.useState)([]),[i,f]=(0,a.useState)({totalCount:0,hasNextPage:!1,hasPreviousPage:!1}),[x,$]=(0,a.useState)(0),[v,y]=(0,a.useState)({}),[b]=(0,d.t)(l.Iq,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTripReportSchedules;t&&(s(t.nodes),f(t.pageInfo))},onError:e=>{console.error("ReadTripReportSchedules error",e)}}),S=async(e=0,t={...v})=>{let r={...t};await b({variables:{limit:100,offset:100*e,filter:r}})},j=e=>{e<0||e===x||($(e),S(e))};return(0,a.useEffect)(()=>{e&&(S(),t(!1))},[e]),(0,n.jsxs)("div",{className:"w-full py-0",children:[n.jsx("div",{className:"flex w-full justify-start flex-col items-start",children:(0,n.jsxs)(m.Z,{headings:[],children:[(0,n.jsxs)("tr",{className:"font-medium border-b border-slblue-200 hidden md:table-row",children:[n.jsx("td",{className:"hidden md:table-cell",children:n.jsx(c.Label,{children:"Title"})}),n.jsx("td",{className:"hidden md:table-cell",children:n.jsx(c.Label,{children:"Depart"})}),n.jsx("td",{className:"hidden md:table-cell",children:n.jsx(c.Label,{children:"Date Range"})}),n.jsx("td",{className:"hidden md:table-cell",children:n.jsx(c.Label,{children:"Vessels"})})]}),r.map(e=>(0,n.jsxs)("tr",{className:"border-b border-sldarkblue-50 even:bg-sllightblue-50/50 hover:bg-sllightblue-50 ",children:[n.jsx("td",{className:"p-2 min-w-1/2",children:n.jsx(g.default,{href:`/trip-report-schedules/edit/?id=${e.id}`,className:"group-hover:text-sllightblue-1000",children:e.title})}),n.jsx("td",{className:"p-2 min-w-1/2",children:`${e.fromLocation.title||""}  @ ${h()(e.start+" "+e.departTime).format("HH:mma")}`}),n.jsx("td",{className:"p-2 min-w-1/2",children:`From ${(0,o.p6)(e.start)} until ${(0,o.p6)(e.end)}`}),n.jsx("td",{className:"p-2 min-w-1/2",children:`${e.vehicles.nodes.map(e=>e.title).join(", ")}`})]},e.id))]})}),n.jsx("div",{className:"flex justify-center",children:n.jsx(u.Z,{page:x,limit:100,onClick:e=>j(e),...i,visiblePageCount:5})})]})},$=()=>{let[e,t]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{(0,i.ay)(),t((0,i.E7)())},[]),n.jsx("div",{children:e?n.jsx(x,{}):n.jsx(s.Z,{message:"Trip schedules are not enabled. Please enable them in the settings to use this feature."})})}},43692:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var n=r(98768);let s=({page:e=0,limit:t=0,onClick:r,totalCount:s=0,hasNextPage:i=!1,hasPreviousPage:a=!1,visiblePageCount:u=0})=>{let o=t>0?Math.ceil(s/t):0,l=t>0?u:0,c=e-l,d=e;c<0&&(c=0,d=l-1);let f=o-l,h=d+1!==f;d>=f&&(c=0,d=l-1),o<l&&(c=0,d=o-1);let p=Array.from({length:d-c+1},(e,t)=>c+t).slice(-l),m=Array.from({length:(t>0?Math.floor(s/t):0)-f+1},(e,t)=>f+t).slice(0,l);return m=(m=m.filter(e=>!p.includes(e))).filter(e=>e>=0),(p[p.length-1]+1===m[0]||p[p.length-1]-1===m[0]||m.length<=0)&&(h=!1),n.jsx("div",{className:"flex items-center justify-end p-4",children:n.jsx("nav",{"aria-label":"Log Entries pagination",children:(0,n.jsxs)("ul",{className:"inline-flex -space-x-px  h-10",children:[n.jsx("li",{children:a&&e>0&&n.jsx("button",{onClick:()=>r(0),className:" rounded-s-lg",children:"First"})}),n.jsx("li",{children:a&&n.jsx("button",{onClick:()=>r(e-1),className:"",children:"Previous"})}),Array.from({length:p.length},(e,t)=>n.jsx("li",{children:n.jsx("button",{onClick:()=>r(p[t]),className:"",children:p[t]+1})},t)),h&&n.jsx("li",{children:n.jsx("button",{onClick:()=>r(d+1),className:"flex items-center justify-center px-4 h-10 leading-tight  border   ",children:"..."})}),Array.from({length:m.length},(e,t)=>n.jsx("li",{children:n.jsx("button",{onClick:()=>r(m[t]),className:"",children:m[t]+1})},t)),n.jsx("li",{children:i&&n.jsx("button",{onClick:()=>r(e+1),className:"",children:"Next"})}),n.jsx("li",{children:i&&e*t<s&&n.jsx("button",{onClick:()=>r(o-1),className:" rounded-e-lg",children:"Last"})})]})})})}},89546:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var n=r(98768);let s=e=>(0,n.jsxs)("table",{className:" table-auto w-full ",cellPadding:"10",children:[n.jsx("thead",{children:n.jsx("tr",{className:e.showHeader?"":"hidden",children:e.headings.map((t,r)=>n.jsx("th",{scope:"col",className:`pb-3 pt-6 px-2 ${0===r?"rounded-tl-lg":" "}   ${e.headings.length===r+1?"rounded-tr-lg":" "}
                                ${t.includes(":")&&"last"===t.split(":")[1]?"rounded-tr-lg":""}
                                ${t.includes(":")&&"smhidden"===t.split(":")[1]?"hidden sm:block":""}
                                ${t.includes(":")&&"left"===t.split(":")[1]?"text-left":""}
                                ${t.includes(":")&&"firstHead"===t.split(":")[1]?"text-left text-nowrap font-thin  md: lg:text-2xl pl-6 rounded-tl-lg":""}  `,children:t.includes(":")?t.split(":")[0]:t},r))})}),n.jsx("tbody",{className:`  text-foreground ${e?.bodyClass}`,children:e.children})]})},9276:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\trip-report-schedules\layout.tsx#default`)},99835:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\trip-report-schedules\page.tsx#default`)},84961:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},46020:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},23379:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},7671:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[864,8865,3563,6263,8189,9507,7602,6451,4234,2925,5394,4837],()=>r(3174));module.exports=n})();