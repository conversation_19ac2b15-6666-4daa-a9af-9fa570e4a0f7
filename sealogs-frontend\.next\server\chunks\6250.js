exports.id=6250,exports.ids=[6250],exports.modules={58774:function(t,e,n){var o;"undefined"!=typeof globalThis?globalThis:void 0!==this||("undefined"!=typeof window?window:"undefined"!=typeof self?self:global),o=function(t){return function(){"use strict";var e={172:function(t,e){var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.CellHookData=e.HookData=void 0;var i=function(t,e,n){this.table=e,this.pageNumber=e.pageNumber,this.pageCount=this.pageNumber,this.settings=e.settings,this.cursor=n,this.doc=t.getDocument()};e.HookData=i;var r=function(t){function e(e,n,o,i,r,l){var a=t.call(this,e,n,l)||this;return a.cell=o,a.row=i,a.column=r,a.section=i.section,a}return o(e,t),e}(i);e.CellHookData=r},340:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0});var o=n(4),i=n(136),r=n(744),l=n(776),a=n(664),s=n(972);e.default=function(t){t.API.autoTable=function(){for(var t,e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];1===e.length?t=e[0]:(console.error("Use of deprecated autoTable initiation"),(t=e[2]||{}).columns=e[0],t.body=e[1]);var o=(0,l.parseInput)(this,t),i=(0,s.createTable)(this,o);return(0,a.drawTable)(this,i),this},t.API.lastAutoTable=!1,t.API.previousAutoTable=!1,t.API.autoTable.previous=!1,t.API.autoTableText=function(t,e,n,o){(0,i.default)(t,e,n,o,this)},t.API.autoTableSetDefaults=function(t){return r.DocHandler.setDefaults(t,this),this},t.autoTableSetDefaults=function(t,e){r.DocHandler.setDefaults(t,e)},t.API.autoTableHtmlToJson=function(t,e){if(void 0===e&&(e=!1),"undefined"==typeof window)return console.error("Cannot run autoTableHtmlToJson in non browser environment"),null;var n,i=new r.DocHandler(this),l=(0,o.parseHtml)(i,t,window,e,!1),a=l.head,s=l.body;return{columns:(null===(n=a[0])||void 0===n?void 0:n.map(function(t){return t.content}))||[],rows:s,data:s}},t.API.autoTableEndPosY=function(){console.error("Use of deprecated function: autoTableEndPosY. Use doc.lastAutoTable.finalY instead.");var t=this.lastAutoTable;return t&&t.finalY?t.finalY:0},t.API.autoTableAddPageContent=function(e){return console.error("Use of deprecated function: autoTableAddPageContent. Use jsPDF.autoTableSetDefaults({didDrawPage: () => {}}) instead."),t.API.autoTable.globalDefaults||(t.API.autoTable.globalDefaults={}),t.API.autoTable.globalDefaults.addPageContent=e,this},t.API.autoTableAddPage=function(){return console.error("Use of deprecated function: autoTableAddPage. Use doc.addPage()"),this.addPage(),this}}},136:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e,n,o,i){o=o||{};var r=i.internal.scaleFactor,l=i.internal.getFontSize()/r,a=l*(i.getLineHeightFactor?i.getLineHeightFactor():1.15),s="",u=1;if(("middle"===o.valign||"bottom"===o.valign||"center"===o.halign||"right"===o.halign)&&(u=(s="string"==typeof t?t.split(/\r\n|\r|\n/g):t).length||1),n+=.8500000000000001*l,"middle"===o.valign?n-=u/2*a:"bottom"===o.valign&&(n-=u*a),"center"===o.halign||"right"===o.halign){var d=l;if("center"===o.halign&&(d*=.5),s&&u>=1){for(var h=0;h<s.length;h++)i.text(s[h],e-i.getStringUnitWidth(s[h])*d,n),n+=a;return i}e-=i.getStringUnitWidth(t)*d}return"justify"===o.halign?i.text(t,e,n,{maxWidth:o.maxWidth||100,align:"justify"}):i.text(t,e,n),i}},420:function(t,e){function n(t,e){var n=t>0,o=e||0===e;return n&&o?"DF":n?"S":o?"F":null}function o(t,e){var n,o,i,r;if(Array.isArray(t=t||e)){if(t.length>=4)return{top:t[0],right:t[1],bottom:t[2],left:t[3]};if(3===t.length)return{top:t[0],right:t[1],bottom:t[2],left:t[1]};if(2===t.length)return{top:t[0],right:t[1],bottom:t[0],left:t[1]};t=1===t.length?t[0]:e}return"object"==typeof t?("number"==typeof t.vertical&&(t.top=t.vertical,t.bottom=t.vertical),"number"==typeof t.horizontal&&(t.right=t.horizontal,t.left=t.horizontal),{left:null!==(n=t.left)&&void 0!==n?n:e,top:null!==(o=t.top)&&void 0!==o?o:e,right:null!==(i=t.right)&&void 0!==i?i:e,bottom:null!==(r=t.bottom)&&void 0!==r?r:e}):("number"!=typeof t&&(t=e),{top:t,right:t,bottom:t,left:t})}Object.defineProperty(e,"__esModule",{value:!0}),e.getPageAvailableWidth=e.parseSpacing=e.getFillStyle=e.addTableBorder=e.getStringWidth=void 0,e.getStringWidth=function(t,e,n){return n.applyStyles(e,!0),(Array.isArray(t)?t:[t]).map(function(t){return n.getTextWidth(t)}).reduce(function(t,e){return Math.max(t,e)},0)},e.addTableBorder=function(t,e,o,i){var r=e.settings.tableLineWidth,l=e.settings.tableLineColor;t.applyStyles({lineWidth:r,lineColor:l});var a=n(r,!1);a&&t.rect(o.x,o.y,e.getWidth(t.pageSize().width),i.y-o.y,a)},e.getFillStyle=n,e.parseSpacing=o,e.getPageAvailableWidth=function(t,e){var n=o(e.settings.margin,0);return t.pageSize().width-(n.left+n.right)}},796:function(t,e){var n,o=this&&this.__extends||(n=function(t,e){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)});Object.defineProperty(e,"__esModule",{value:!0}),e.getTheme=e.defaultStyles=e.HtmlRowInput=void 0;var i=function(t){function e(e){var n=t.call(this)||this;return n._element=e,n}return o(e,t),e}(Array);e.HtmlRowInput=i,e.defaultStyles=function(t){return{font:"helvetica",fontStyle:"normal",overflow:"linebreak",fillColor:!1,textColor:20,halign:"left",valign:"top",fontSize:10,cellPadding:5/t,lineColor:200,lineWidth:0,cellWidth:"auto",minCellHeight:0,minCellWidth:0}},e.getTheme=function(t){return({striped:{table:{fillColor:255,textColor:80,fontStyle:"normal"},head:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},body:{},foot:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1},head:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},body:{},foot:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},alternateRow:{}},plain:{head:{fontStyle:"bold"},foot:{fontStyle:"bold"}}})[t]}},903:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.parseCss=void 0;var o=n(420);function i(t,e){var n=function t(e,n){var o=n(e);return"rgba(0, 0, 0, 0)"!==o&&"transparent"!==o&&"initial"!==o&&"inherit"!==o?o:null==e.parentElement?null:t(e.parentElement,n)}(t,e);if(!n)return null;var o=n.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)$/);if(!o||!Array.isArray(o))return null;var i=[parseInt(o[1]),parseInt(o[2]),parseInt(o[3])];return 0===parseInt(o[4])||isNaN(i[0])||isNaN(i[1])||isNaN(i[2])?null:i}e.parseCss=function(t,e,n,r,l){var a,s,u,d,h,c,f={},g=96/72,p=i(e,function(t){return l.getComputedStyle(t).backgroundColor});null!=p&&(f.fillColor=p);var y=i(e,function(t){return l.getComputedStyle(t).color});null!=y&&(f.textColor=y);var v=(a=[r.paddingTop,r.paddingRight,r.paddingBottom,r.paddingLeft],s=96/(72/n),u=(parseInt(r.lineHeight)-parseInt(r.fontSize))/n/2,d=a.map(function(t){return parseInt(t||"0")/s}),u>(h=(0,o.parseSpacing)(d,0)).top&&(h.top=u),u>h.bottom&&(h.bottom=u),h);v&&(f.cellPadding=v);var m="borderTopColor",b=g*n,w=r.borderTopWidth;if(r.borderBottomWidth===w&&r.borderRightWidth===w&&r.borderLeftWidth===w){var P=(parseFloat(w)||0)/b;P&&(f.lineWidth=P)}else f.lineWidth={top:(parseFloat(r.borderTopWidth)||0)/b,right:(parseFloat(r.borderRightWidth)||0)/b,bottom:(parseFloat(r.borderBottomWidth)||0)/b,left:(parseFloat(r.borderLeftWidth)||0)/b},!f.lineWidth.top&&(f.lineWidth.right?m="borderRightColor":f.lineWidth.bottom?m="borderBottomColor":f.lineWidth.left&&(m="borderLeftColor"));var S=i(e,function(t){return l.getComputedStyle(t)[m]});null!=S&&(f.lineColor=S);var x=["left","right","center","justify"];-1!==x.indexOf(r.textAlign)&&(f.halign=r.textAlign),-1!==(x=["middle","bottom","top"]).indexOf(r.verticalAlign)&&(f.valign=r.verticalAlign);var C=parseInt(r.fontSize||"");isNaN(C)||(f.fontSize=C/g);var W=(c="",("bold"===r.fontWeight||"bolder"===r.fontWeight||parseInt(r.fontWeight)>=700)&&(c="bold"),("italic"===r.fontStyle||"oblique"===r.fontStyle)&&(c+="italic"),c);W&&(f.fontStyle=W);var D=(r.fontFamily||"").toLowerCase();return -1!==t.indexOf(D)&&(f.font=D),f}},744:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.DocHandler=void 0;var n={},o=function(){function t(t){this.jsPDFDocument=t,this.userStyles={textColor:t.getTextColor?this.jsPDFDocument.getTextColor():0,fontSize:t.internal.getFontSize(),fontStyle:t.internal.getFont().fontStyle,font:t.internal.getFont().fontName,lineWidth:t.getLineWidth?this.jsPDFDocument.getLineWidth():0,lineColor:t.getDrawColor?this.jsPDFDocument.getDrawColor():0}}return t.setDefaults=function(t,e){void 0===e&&(e=null),e?e.__autoTableDocumentDefaults=t:n=t},t.unifyColor=function(t){return Array.isArray(t)?t:"number"==typeof t?[t,t,t]:"string"==typeof t?[t]:null},t.prototype.applyStyles=function(e,n){void 0===n&&(n=!1),e.fontStyle&&this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(e.fontStyle);var o,i,r,l=this.jsPDFDocument.internal.getFont(),a=l.fontStyle,s=l.fontName;if(e.font&&(s=e.font),e.fontStyle){a=e.fontStyle;var u=this.getFontList()[s];u&&-1===u.indexOf(a)&&(this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(u[0]),a=u[0])}if(this.jsPDFDocument.setFont(s,a),e.fontSize&&this.jsPDFDocument.setFontSize(e.fontSize),!n){var d=t.unifyColor(e.fillColor);d&&(o=this.jsPDFDocument).setFillColor.apply(o,d),(d=t.unifyColor(e.textColor))&&(i=this.jsPDFDocument).setTextColor.apply(i,d),(d=t.unifyColor(e.lineColor))&&(r=this.jsPDFDocument).setDrawColor.apply(r,d),"number"==typeof e.lineWidth&&this.jsPDFDocument.setLineWidth(e.lineWidth)}},t.prototype.splitTextToSize=function(t,e,n){return this.jsPDFDocument.splitTextToSize(t,e,n)},t.prototype.rect=function(t,e,n,o,i){return this.jsPDFDocument.rect(t,e,n,o,i)},t.prototype.getLastAutoTable=function(){return this.jsPDFDocument.lastAutoTable||null},t.prototype.getTextWidth=function(t){return this.jsPDFDocument.getTextWidth(t)},t.prototype.getDocument=function(){return this.jsPDFDocument},t.prototype.setPage=function(t){this.jsPDFDocument.setPage(t)},t.prototype.addPage=function(){return this.jsPDFDocument.addPage()},t.prototype.getFontList=function(){return this.jsPDFDocument.getFontList()},t.prototype.getGlobalOptions=function(){return n||{}},t.prototype.getDocumentOptions=function(){return this.jsPDFDocument.__autoTableDocumentDefaults||{}},t.prototype.pageSize=function(){var t=this.jsPDFDocument.internal.pageSize;return null==t.width&&(t={width:t.getWidth(),height:t.getHeight()}),t},t.prototype.scaleFactor=function(){return this.jsPDFDocument.internal.scaleFactor},t.prototype.getLineHeightFactor=function(){var t=this.jsPDFDocument;return t.getLineHeightFactor?t.getLineHeightFactor():1.15},t.prototype.getLineHeight=function(t){return t/this.scaleFactor()*this.getLineHeightFactor()},t.prototype.pageNumber=function(){var t=this.jsPDFDocument.internal.getCurrentPageInfo();return t?t.pageNumber:this.jsPDFDocument.internal.getNumberOfPages()},t}();e.DocHandler=o},4:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.parseHtml=void 0;var o=n(903),i=n(796);e.parseHtml=function(t,e,n,r,l){void 0===r&&(r=!1),void 0===l&&(l=!1),u="string"==typeof e?n.document.querySelector(e):e;var a,s,u,d=Object.keys(t.getFontList()),h=t.scaleFactor(),c=[],f=[],g=[];if(!u)return console.error("Html table could not be found with input: ",e),{head:c,body:f,foot:g};for(var p=0;p<u.rows.length;p++){var y=u.rows[p],v=null===(s=null===(a=null==y?void 0:y.parentElement)||void 0===a?void 0:a.tagName)||void 0===s?void 0:s.toLowerCase(),m=function(t,e,n,r,l,a){for(var s=new i.HtmlRowInput(r),u=0;u<r.cells.length;u++){var d=r.cells[u],h=n.getComputedStyle(d);if(l||"none"!==h.display){var c=void 0;a&&(c=(0,o.parseCss)(t,d,e,h,n)),s.push({rowSpan:d.rowSpan,colSpan:d.colSpan,styles:c,_element:d,content:function(t){var e=t.cloneNode(!0);return e.innerHTML=e.innerHTML.replace(/\n/g,"").replace(/ +/g," "),e.innerHTML=e.innerHTML.split(/<br.*?>/).map(function(t){return t.trim()}).join("\n"),e.innerText||e.textContent||""}(d)})}}var f=n.getComputedStyle(r);if(s.length>0&&(l||"none"!==f.display))return s}(d,h,n,y,r,l);m&&("thead"===v?c.push(m):"tfoot"===v?g.push(m):f.push(m))}return{head:c,body:f,foot:g}}},776:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.parseInput=void 0;var o=n(4),i=n(356),r=n(420),l=n(744),a=n(792);e.parseInput=function(t,e){var n,s,u,d,h,c,f,g,p,y,v,m,b,w,P,S,x,C,W,D,H,F,T,j,_,A,k=new l.DocHandler(t),z=k.getDocumentOptions(),O=k.getGlobalOptions();(0,a.default)(k,O,z,e);var R=(0,i.assign)({},O,z,e);"undefined"!=typeof window&&(A=window);var M=function(t,e,n){for(var o={styles:{},headStyles:{},bodyStyles:{},footStyles:{},alternateRowStyles:{},columnStyles:{}},r=function(r){if("columnStyles"===r){var l=t[r],a=e[r],s=n[r];o.columnStyles=(0,i.assign)({},l,a,s)}else{var u=[t,e,n].map(function(t){return t[r]||{}});o[r]=(0,i.assign)({},u[0],u[1],u[2])}},l=0,a=Object.keys(o);l<a.length;l++)r(a[l]);return o}(O,z,e),I=function(t,e,n){for(var o={didParseCell:[],willDrawCell:[],didDrawCell:[],willDrawPage:[],didDrawPage:[]},i=0,r=[t,e,n];i<r.length;i++){var l=r[i];l.didParseCell&&o.didParseCell.push(l.didParseCell),l.willDrawCell&&o.willDrawCell.push(l.willDrawCell),l.didDrawCell&&o.didDrawCell.push(l.didDrawCell),l.willDrawPage&&o.willDrawPage.push(l.willDrawPage),l.didDrawPage&&o.didDrawPage.push(l.didDrawPage)}return o}(O,z,e),L=(D=(0,r.parseSpacing)(R.margin,40/k.scaleFactor()),H=null!==(n=R.startY,s=k.getLastAutoTable(),u=k.scaleFactor(),d=k.pageNumber(),h=!1,s&&s.startPageNumber&&(h=s.startPageNumber+s.pageNumber-1===d),c="number"==typeof n?n:(null==n||!1===n)&&h&&(null==s?void 0:s.finalY)!=null?s.finalY+20/u:null)&&void 0!==c?c:D.top,C=!0===R.showFoot?"everyPage":!1===R.showFoot?"never":null!==(f=R.showFoot)&&void 0!==f?f:"everyPage",W=!0===R.showHead?"everyPage":!1===R.showHead?"never":null!==(g=R.showHead)&&void 0!==g?g:"everyPage",F=null!==(p=R.useCss)&&void 0!==p&&p,T=R.theme||(F?"plain":"striped"),j=!!R.horizontalPageBreak,_=null!==(y=R.horizontalPageBreakRepeat)&&void 0!==y?y:null,{includeHiddenHtml:null!==(v=R.includeHiddenHtml)&&void 0!==v&&v,useCss:F,theme:T,startY:H,margin:D,pageBreak:null!==(m=R.pageBreak)&&void 0!==m?m:"auto",rowPageBreak:null!==(b=R.rowPageBreak)&&void 0!==b?b:"auto",tableWidth:null!==(w=R.tableWidth)&&void 0!==w?w:"auto",showHead:W,showFoot:C,tableLineWidth:null!==(P=R.tableLineWidth)&&void 0!==P?P:0,tableLineColor:null!==(S=R.tableLineColor)&&void 0!==S?S:200,horizontalPageBreak:j,horizontalPageBreakRepeat:_,horizontalPageBreakBehaviour:null!==(x=R.horizontalPageBreakBehaviour)&&void 0!==x?x:"afterAllRows"}),E=function(t,e,n){var i,r,l,a,s,u=e.head||[],d=e.body||[],h=e.foot||[];if(e.html){var c=e.includeHiddenHtml;if(n){var f=(0,o.parseHtml)(t,e.html,n,c,e.useCss)||{};u=f.head||u,d=f.body||u,h=f.foot||u}else console.error("Cannot parse html in non browser environment")}return{columns:e.columns||(i=u,r=d,l=h,a=i[0]||r[0]||l[0]||[],s=[],Object.keys(a).filter(function(t){return"_element"!==t}).forEach(function(t){var e,n=1;"object"!=typeof(e=Array.isArray(a)?a[parseInt(t)]:a[t])||Array.isArray(e)||(n=(null==e?void 0:e.colSpan)||1);for(var o=0;o<n;o++){var i={dataKey:Array.isArray(a)?s.length:t+(o>0?"_".concat(o):"")};s.push(i)}}),s),head:u,body:d,foot:h}}(k,R,A);return{id:e.tableId,content:E,hooks:I,styles:M,settings:L}}},792:function(t,e){function n(t){t.rowHeight?(console.error("Use of deprecated style rowHeight. It is renamed to minCellHeight."),t.minCellHeight||(t.minCellHeight=t.rowHeight)):t.columnWidth&&(console.error("Use of deprecated style columnWidth. It is renamed to cellWidth."),t.cellWidth||(t.cellWidth=t.columnWidth))}Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e,o,i){for(var r=function(e){e&&"object"!=typeof e&&console.error("The options parameter should be of type object, is: "+typeof e),void 0!==e.extendWidth&&(e.tableWidth=e.extendWidth?"auto":"wrap",console.error("Use of deprecated option: extendWidth, use tableWidth instead.")),void 0!==e.margins&&(void 0===e.margin&&(e.margin=e.margins),console.error("Use of deprecated option: margins, use margin instead.")),e.startY&&"number"!=typeof e.startY&&(console.error("Invalid value for startY option",e.startY),delete e.startY),!e.didDrawPage&&(e.afterPageContent||e.beforePageContent||e.afterPageAdd)&&(console.error("The afterPageContent, beforePageContent and afterPageAdd hooks are deprecated. Use didDrawPage instead"),e.didDrawPage=function(n){t.applyStyles(t.userStyles),e.beforePageContent&&e.beforePageContent(n),t.applyStyles(t.userStyles),e.afterPageContent&&e.afterPageContent(n),t.applyStyles(t.userStyles),e.afterPageAdd&&n.pageNumber>1&&n.afterPageAdd(n),t.applyStyles(t.userStyles)}),["createdHeaderCell","drawHeaderRow","drawRow","drawHeaderCell"].forEach(function(t){e[t]&&console.error('The "'.concat(t,'" hook has changed in version 3.0, check the changelog for how to migrate.'))}),[["showFoot","showFooter"],["showHead","showHeader"],["didDrawPage","addPageContent"],["didParseCell","createdCell"],["headStyles","headerStyles"]].forEach(function(t){var n=t[0],o=t[1];e[o]&&(console.error("Use of deprecated option ".concat(o,". Use ").concat(n," instead")),e[n]=e[o])}),[["padding","cellPadding"],["lineHeight","rowHeight"],"fontSize","overflow"].forEach(function(t){var n="string"==typeof t?t:t[0],o="string"==typeof t?t:t[1];void 0!==e[n]&&(void 0===e.styles[o]&&(e.styles[o]=e[n]),console.error("Use of deprecated option: "+n+", use the style "+o+" instead."))});for(var o=0,i=["styles","bodyStyles","headStyles","footStyles"];o<i.length;o++)n(e[i[o]]||{});for(var r=e.columnStyles||{},l=0,a=Object.keys(r);l<a.length;l++)n(r[a[l]]||{})},l=0,a=[e,o,i];l<a.length;l++)r(a[l])}},260:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.Column=e.Cell=e.Row=e.Table=void 0;var o=n(796),i=n(172),r=n(420),l=function(){function t(t,e){this.pageNumber=1,this.pageCount=1,this.id=t.id,this.settings=t.settings,this.styles=t.styles,this.hooks=t.hooks,this.columns=e.columns,this.head=e.head,this.body=e.body,this.foot=e.foot}return t.prototype.getHeadHeight=function(t){return this.head.reduce(function(e,n){return e+n.getMaxCellHeight(t)},0)},t.prototype.getFootHeight=function(t){return this.foot.reduce(function(e,n){return e+n.getMaxCellHeight(t)},0)},t.prototype.allRows=function(){return this.head.concat(this.body).concat(this.foot)},t.prototype.callCellHooks=function(t,e,n,o,r,l){for(var a=0;a<e.length;a++){var s=!1===(0,e[a])(new i.CellHookData(t,this,n,o,r,l));if(n.text=Array.isArray(n.text)?n.text:[n.text],s)return!1}return!0},t.prototype.callEndPageHooks=function(t,e){t.applyStyles(t.userStyles);for(var n=0,o=this.hooks.didDrawPage;n<o.length;n++)(0,o[n])(new i.HookData(t,this,e))},t.prototype.callWillDrawPageHooks=function(t,e){for(var n=0,o=this.hooks.willDrawPage;n<o.length;n++)(0,o[n])(new i.HookData(t,this,e))},t.prototype.getWidth=function(t){if("number"==typeof this.settings.tableWidth)return this.settings.tableWidth;if("wrap"===this.settings.tableWidth)return this.columns.reduce(function(t,e){return t+e.wrappedWidth},0);var e=this.settings.margin;return t-e.left-e.right},t}();e.Table=l;var a=function(){function t(t,e,n,i,r){void 0===r&&(r=!1),this.height=0,this.raw=t,t instanceof o.HtmlRowInput&&(this.raw=t._element,this.element=t._element),this.index=e,this.section=n,this.cells=i,this.spansMultiplePages=r}return t.prototype.getMaxCellHeight=function(t){var e=this;return t.reduce(function(t,n){var o;return Math.max(t,(null===(o=e.cells[n.index])||void 0===o?void 0:o.height)||0)},0)},t.prototype.hasRowSpan=function(t){var e=this;return t.filter(function(t){var n=e.cells[t.index];return!!n&&n.rowSpan>1}).length>0},t.prototype.canEntireRowFit=function(t,e){return this.getMaxCellHeight(e)<=t},t.prototype.getMinimumRowHeight=function(t,e){var n=this;return t.reduce(function(t,o){var i=n.cells[o.index];if(!i)return 0;var r=e.getLineHeight(i.styles.fontSize),l=i.padding("vertical")+r;return l>t?l:t},0)},t}();e.Row=a;var s=function(){function t(t,e,n){this.contentHeight=0,this.contentWidth=0,this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.height=0,this.x=0,this.y=0,this.styles=e,this.section=n,this.raw=t;var o,i,r=t;null==t||"object"!=typeof t||Array.isArray(t)?(this.rowSpan=1,this.colSpan=1):(this.rowSpan=t.rowSpan||1,this.colSpan=t.colSpan||1,r=null!==(i=null!==(o=t.content)&&void 0!==o?o:t.title)&&void 0!==i?i:t,t._element&&(this.raw=t._element));var l=null!=r?""+r:"";this.text=l.split(/\r\n|\r|\n/g)}return t.prototype.getTextPos=function(){if("top"===this.styles.valign)t=this.y+this.padding("top");else if("bottom"===this.styles.valign)t=this.y+this.height-this.padding("bottom");else{var t,e,n=this.height-this.padding("vertical");t=this.y+n/2+this.padding("top")}if("right"===this.styles.halign)e=this.x+this.width-this.padding("right");else if("center"===this.styles.halign){var o=this.width-this.padding("horizontal");e=this.x+o/2+this.padding("left")}else e=this.x+this.padding("left");return{x:e,y:t}},t.prototype.getContentHeight=function(t,e){return void 0===e&&(e=1.15),Math.max((Array.isArray(this.text)?this.text.length:1)*(this.styles.fontSize/t*e)+this.padding("vertical"),this.styles.minCellHeight)},t.prototype.padding=function(t){var e=(0,r.parseSpacing)(this.styles.cellPadding,0);return"vertical"===t?e.top+e.bottom:"horizontal"===t?e.left+e.right:e[t]},t}();e.Cell=s;var u=function(){function t(t,e,n){this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.dataKey=t,this.raw=e,this.index=n}return t.prototype.getMaxCustomCellWidth=function(t){for(var e=0,n=0,o=t.allRows();n<o.length;n++){var i=o[n].cells[this.index];i&&"number"==typeof i.styles.cellWidth&&(e=Math.max(e,i.styles.cellWidth))}return e},t}();e.Column=u},356:function(t,e){Object.defineProperty(e,"__esModule",{value:!0}),e.assign=void 0,e.assign=function(t,e,n,o,i){if(null==t)throw TypeError("Cannot convert undefined or null to object");for(var r=Object(t),l=1;l<arguments.length;l++){var a=arguments[l];if(null!=a)for(var s in a)Object.prototype.hasOwnProperty.call(a,s)&&(r[s]=a[s])}return r}},972:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.createTable=void 0;var o=n(744),i=n(260),r=n(324),l=n(796),a=n(356);function s(t,e,n,o,r,s){var u={};return e.map(function(e,d){for(var h=0,c={},f=0,g=0,p=0;p<n.length;p++){var y=n[p];if(null==u[y.index]||0===u[y.index].left){if(0===g){var v=void 0;v=Array.isArray(e)?e[y.index-f-h]:e[y.dataKey];var m={};"object"!=typeof v||Array.isArray(v)||(m=(null==v?void 0:v.styles)||{});var b=function(t,e,n,o,i,r,s){var u,d=(0,l.getTheme)(o);"head"===t?u=i.headStyles:"body"===t?u=i.bodyStyles:"foot"===t&&(u=i.footStyles);var h=(0,a.assign)({},d.table,d[t],i.styles,u),c=i.columnStyles[e.dataKey]||i.columnStyles[e.index]||{},f="body"===t&&n%2==0?(0,a.assign)({},d.alternateRow,i.alternateRowStyles):{},g=(0,l.defaultStyles)(r),p=(0,a.assign)({},g,h,f,"body"===t?c:{});return(0,a.assign)(p,s)}(t,y,d,r,o,s,m),w=new i.Cell(v,b,t);c[y.dataKey]=w,c[y.index]=w,g=w.colSpan-1,u[y.index]={left:w.rowSpan-1,times:g}}else g--,f++}else u[y.index].left--,g=u[y.index].times,h++}return new i.Row(e,d,t,c)})}function u(t,e){var n={};return t.forEach(function(t){if(null!=t.raw){var o=function(t,e){if("head"===t){if("object"==typeof e)return e.header||e.title||null;if("string"==typeof e||"number"==typeof e)return e}else if("foot"===t&&"object"==typeof e)return e.footer;return null}(e,t.raw);null!=o&&(n[t.dataKey]=o)}}),Object.keys(n).length>0?n:null}e.createTable=function(t,e){var n=new o.DocHandler(t),l=function(t,e){var n=t.content,o=n.columns.map(function(t,e){var n,o,r;return r="object"==typeof t&&null!==(o=null!==(n=t.dataKey)&&void 0!==n?n:t.key)&&void 0!==o?o:e,new i.Column(r,t,e)});if(0===n.head.length){var r=u(o,"head");r&&n.head.push(r)}if(0===n.foot.length){var r=u(o,"foot");r&&n.foot.push(r)}var l=t.settings.theme,a=t.styles;return{columns:o,head:s("head",n.head,o,a,l,e),body:s("body",n.body,o,a,l,e),foot:s("foot",n.foot,o,a,l,e)}}(e,n.scaleFactor()),a=new i.Table(e,l);return(0,r.calculateWidths)(n,a),n.applyStyles(n.userStyles),a}},664:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.addPage=e.drawTable=void 0;var o=n(420),i=n(260),r=n(744),l=n(356),a=n(136),s=n(224);function u(t,e,n,o){var i=e.settings;t.applyStyles(t.userStyles),("firstPage"===i.showHead||"everyPage"===i.showHead)&&e.head.forEach(function(i){return f(t,e,i,n,o)})}function d(t,e,n,o,i,r){t.applyStyles(t.userStyles);var l=Math.min(n+(r=null!=r?r:e.body.length),e.body.length),a=-1;return e.body.slice(n,l).forEach(function(r,l){var s=n+l===e.body.length-1,u=g(t,e,s,o);r.canEntireRowFit(u,i)&&(f(t,e,r,o,i),a=n+l)}),a}function h(t,e,n,o){var i=e.settings;t.applyStyles(t.userStyles),("lastPage"===i.showFoot||"everyPage"===i.showFoot)&&e.foot.forEach(function(i){return f(t,e,i,n,o)})}function c(t,e,n,o,r,a,s){var u=g(t,e,o,a);if(n.canEntireRowFit(u,s))f(t,e,n,a,s);else if(function(t,e,n,o){var i=t.pageSize().height,r=o.settings.margin,l=i-(r.top+r.bottom);"body"===e.section&&(l-=o.getHeadHeight(o.columns)+o.getFootHeight(o.columns));var a=e.getMinimumRowHeight(o.columns,t);if(a>l)return console.error("Will not be able to print row ".concat(e.index," correctly since it's minimum height is larger than page height")),!0;if(!(a<n))return!1;var s=e.hasRowSpan(o.columns);return e.getMaxCellHeight(o.columns)>l?(s&&console.error("The content of row ".concat(e.index," will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.")),!0):!s&&"avoid"!==o.settings.rowPageBreak}(t,n,u,e)){var d=function(t,e,n,o){var r={};t.spansMultiplePages=!0,t.height=0;for(var a=0,s=0,u=n.columns;s<u.length;s++){var d=u[s],h=t.cells[d.index];if(h){Array.isArray(h.text)||(h.text=[h.text]);var c=new i.Cell(h.raw,h.styles,h.section);(c=(0,l.assign)(c,h)).text=[];var f=function(t,e,n){var o=n.getLineHeight(t.styles.fontSize);return Math.max(0,Math.floor((e-t.padding("vertical"))/o))}(h,e,o);h.text.length>f&&(c.text=h.text.splice(f,h.text.length));var g=o.scaleFactor(),p=o.getLineHeightFactor();h.contentHeight=h.getContentHeight(g,p),h.contentHeight>=e&&(h.contentHeight=e,c.styles.minCellHeight-=e),h.contentHeight>t.height&&(t.height=h.contentHeight),c.contentHeight=c.getContentHeight(g,p),c.contentHeight>a&&(a=c.contentHeight),r[d.index]=c}}var y=new i.Row(t.raw,-1,t.section,r,!0);y.height=a;for(var v=0,m=n.columns;v<m.length;v++){var d=m[v],c=y.cells[d.index];c&&(c.height=y.height);var h=t.cells[d.index];h&&(h.height=t.height)}return y}(n,u,e,t);f(t,e,n,a,s),p(t,e,r,a,s),c(t,e,d,o,r,a,s)}else p(t,e,r,a,s),c(t,e,n,o,r,a,s)}function f(t,e,n,i,r){i.x=e.settings.margin.left;for(var l=0;l<r.length;l++){var s=r[l],u=n.cells[s.index];if(!u||(t.applyStyles(u.styles),u.x=i.x,u.y=i.y,!1===e.callCellHooks(t,e.hooks.willDrawCell,u,n,s,i))){i.x+=s.width;continue}(function(t,e,n){var i=e.styles;if(t.getDocument().setFillColor(t.getDocument().getFillColor()),"number"==typeof i.lineWidth){var r=(0,o.getFillStyle)(i.lineWidth,i.fillColor);r&&t.rect(e.x,n.y,e.width,e.height,r)}else"object"==typeof i.lineWidth&&(i.fillColor&&t.rect(e.x,n.y,e.width,e.height,"F"),function(t,e,n,o){var i,r,l,a;function s(e,n,o,i,r){t.getDocument().setLineWidth(e),t.getDocument().line(n,o,i,r,"S")}o.top&&(i=n.x,r=n.y,l=n.x+e.width,a=n.y,o.right&&(l+=.5*o.right),o.left&&(i-=.5*o.left),s(o.top,i,r,l,a)),o.bottom&&(i=n.x,r=n.y+e.height,l=n.x+e.width,a=n.y+e.height,o.right&&(l+=.5*o.right),o.left&&(i-=.5*o.left),s(o.bottom,i,r,l,a)),o.left&&(i=n.x,r=n.y,l=n.x,a=n.y+e.height,o.top&&(r-=.5*o.top),o.bottom&&(a+=.5*o.bottom),s(o.left,i,r,l,a)),o.right&&(i=n.x+e.width,r=n.y,l=n.x+e.width,a=n.y+e.height,o.top&&(r-=.5*o.top),o.bottom&&(a+=.5*o.bottom),s(o.right,i,r,l,a))}(t,e,n,i.lineWidth))})(t,u,i);var d=u.getTextPos();(0,a.default)(u.text,d.x,d.y,{halign:u.styles.halign,valign:u.styles.valign,maxWidth:Math.ceil(u.width-u.padding("left")-u.padding("right"))},t.getDocument()),e.callCellHooks(t,e.hooks.didDrawCell,u,n,s,i),i.x+=s.width}i.y+=n.height}function g(t,e,n,o){var i=e.settings.margin.bottom,r=e.settings.showFoot;return("everyPage"===r||"lastPage"===r&&n)&&(i+=e.getFootHeight(e.columns)),t.pageSize().height-o.y-i}function p(t,e,n,i,r,l){void 0===r&&(r=[]),void 0===l&&(l=!1),t.applyStyles(t.userStyles),"everyPage"!==e.settings.showFoot||l||e.foot.forEach(function(n){return f(t,e,n,i,r)}),e.callEndPageHooks(t,i);var a=e.settings.margin;(0,o.addTableBorder)(t,e,n,i),y(t),e.pageNumber++,e.pageCount++,i.x=a.left,i.y=a.top,n.y=a.top,e.callWillDrawPageHooks(t,i),"everyPage"===e.settings.showHead&&(e.head.forEach(function(n){return f(t,e,n,i,r)}),t.applyStyles(t.userStyles))}function y(t){var e=t.pageNumber();return t.setPage(e+1),t.pageNumber()===e&&(t.addPage(),!0)}e.drawTable=function(t,e){var n=e.settings,i=n.startY,a=n.margin,g={x:a.left,y:i},v=e.getHeadHeight(e.columns)+e.getFootHeight(e.columns),m=i+a.bottom+v;"avoid"===n.pageBreak&&(m+=e.body.reduce(function(t,e){return t+e.height},0));var b=new r.DocHandler(t);("always"===n.pageBreak||null!=n.startY&&m>b.pageSize().height)&&(y(b),g.y=a.top),e.callWillDrawPageHooks(b,g);var w=(0,l.assign)({},g);e.startPageNumber=b.pageNumber(),n.horizontalPageBreak?function(t,e,n,o){var i=(0,s.calculateAllColumnsCanFitInPage)(t,e);if("afterAllRows"===e.settings.horizontalPageBreakBehaviour)i.forEach(function(i,r){var l;t.applyStyles(t.userStyles),r>0?p(t,e,n,o,i.columns,!0):u(t,e,o,i.columns),l=i.columns,t.applyStyles(t.userStyles),e.body.forEach(function(i,r){var a=r===e.body.length-1;c(t,e,i,a,n,o,l)}),h(t,e,o,i.columns)});else for(var r=-1,l=i[0];r<e.body.length-1;)!function(){var a=r;if(l){t.applyStyles(t.userStyles);var s=l.columns;r>=0?p(t,e,n,o,s,!0):u(t,e,o,s),a=d(t,e,r+1,o,s),h(t,e,o,s)}var c=a-r;i.slice(1).forEach(function(i){t.applyStyles(t.userStyles),p(t,e,n,o,i.columns,!0),d(t,e,r+1,o,i.columns,c),h(t,e,o,i.columns)}),r=a}()}(b,e,w,g):(b.applyStyles(b.userStyles),("firstPage"===n.showHead||"everyPage"===n.showHead)&&e.head.forEach(function(t){return f(b,e,t,g,e.columns)}),b.applyStyles(b.userStyles),e.body.forEach(function(t,n){var o=n===e.body.length-1;c(b,e,t,o,w,g,e.columns)}),b.applyStyles(b.userStyles),("lastPage"===n.showFoot||"everyPage"===n.showFoot)&&e.foot.forEach(function(t){return f(b,e,t,g,e.columns)})),(0,o.addTableBorder)(b,e,w,g),e.callEndPageHooks(b,g),e.finalY=g.y,t.lastAutoTable=e,t.previousAutoTable=e,t.autoTable&&(t.autoTable.previous=e),b.applyStyles(b.userStyles)},e.addPage=p},224:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.calculateAllColumnsCanFitInPage=void 0;var o=n(420);e.calculateAllColumnsCanFitInPage=function(t,e){for(var n=[],i=0;i<e.columns.length;i++){var r=function(t,e,n){void 0===n&&(n={});var i,r=(0,o.getPageAvailableWidth)(t,e),l=new Map,a=[],s=[],u=[];e.settings.horizontalPageBreakRepeat,Array.isArray(e.settings.horizontalPageBreakRepeat)?u=e.settings.horizontalPageBreakRepeat:("string"==typeof e.settings.horizontalPageBreakRepeat||"number"==typeof e.settings.horizontalPageBreakRepeat)&&(u=[e.settings.horizontalPageBreakRepeat]),u.forEach(function(t){var n=e.columns.find(function(e){return e.dataKey===t||e.index===t});n&&!l.has(n.index)&&(l.set(n.index,!0),a.push(n.index),s.push(e.columns[n.index]),r-=n.wrappedWidth)});for(var d=!0,h=null!==(i=null==n?void 0:n.start)&&void 0!==i?i:0;h<e.columns.length;){if(l.has(h)){h++;continue}var c=e.columns[h].wrappedWidth;if(d||r>=c)d=!1,a.push(h),s.push(e.columns[h]),r-=c;else break;h++}return{colIndexes:a,columns:s,lastIndex:h-1}}(t,e,{start:i});r.columns.length&&(n.push(r),i=r.lastIndex)}return n}},324:function(t,e,n){Object.defineProperty(e,"__esModule",{value:!0}),e.ellipsize=e.resizeColumns=e.calculateWidths=void 0;var o=n(420);function i(t,e,n){for(var o=e,r=t.reduce(function(t,e){return t+e.wrappedWidth},0),l=0;l<t.length;l++){var a=t[l],s=a.wrappedWidth/r*o,u=a.width+s,d=n(a),h=u<d?d:u;e-=h-a.width,a.width=h}if(e=Math.round(1e10*e)/1e10){var c=t.filter(function(t){return!(e<0)||t.width>n(t)});c.length&&(e=i(c,e,n))}return e}function r(t,e,n,i,r){return t.map(function(t){return function(t,e,n,i,r){var l=1e4*i.scaleFactor();if((e=Math.ceil(e*l)/l)>=(0,o.getStringWidth)(t,n,i))return t;for(;e<(0,o.getStringWidth)(t+r,n,i)&&!(t.length<=1);)t=t.substring(0,t.length-1);return t.trim()+r}(t,e,n,i,r)})}e.calculateWidths=function(t,e){n=t.scaleFactor(),l=e.settings.horizontalPageBreak,a=(0,o.getPageAvailableWidth)(t,e),e.allRows().forEach(function(i){for(var r=0,s=e.columns;r<s.length;r++){var u=s[r],d=i.cells[u.index];if(d){var h=e.hooks.didParseCell;e.callCellHooks(t,h,d,i,u,null);var c=d.padding("horizontal");d.contentWidth=(0,o.getStringWidth)(d.text,d.styles,t)+c;var f=(0,o.getStringWidth)(d.text.join(" ").split(/[^\S\u00A0]+/),d.styles,t);if(d.minReadableWidth=f+d.padding("horizontal"),"number"==typeof d.styles.cellWidth)d.minWidth=d.styles.cellWidth,d.wrappedWidth=d.styles.cellWidth;else if("wrap"===d.styles.cellWidth||!0===l)d.contentWidth>a?(d.minWidth=a,d.wrappedWidth=a):(d.minWidth=d.contentWidth,d.wrappedWidth=d.contentWidth);else{var g=10/n;d.minWidth=d.styles.minCellWidth||g,d.wrappedWidth=d.contentWidth,d.minWidth>d.wrappedWidth&&(d.wrappedWidth=d.minWidth)}}}}),e.allRows().forEach(function(t){for(var n=0,o=e.columns;n<o.length;n++){var i=o[n],r=t.cells[i.index];if(r&&1===r.colSpan)i.wrappedWidth=Math.max(i.wrappedWidth,r.wrappedWidth),i.minWidth=Math.max(i.minWidth,r.minWidth),i.minReadableWidth=Math.max(i.minReadableWidth,r.minReadableWidth);else{var l=e.styles.columnStyles[i.dataKey]||e.styles.columnStyles[i.index]||{},a=l.cellWidth||l.minCellWidth;a&&"number"==typeof a&&(i.minWidth=a,i.wrappedWidth=a)}r&&(r.colSpan>1&&!i.minWidth&&(i.minWidth=r.minWidth),r.colSpan>1&&!i.wrappedWidth&&(i.wrappedWidth=r.minWidth))}});var n,l,a,s=[],u=0;e.columns.forEach(function(t){var n=t.getMaxCustomCellWidth(e);n?t.width=n:(t.width=t.wrappedWidth,s.push(t)),u+=t.width});var d=e.getWidth(t.pageSize().width)-u;d&&(d=i(s,d,function(t){return Math.max(t.minReadableWidth,t.minWidth)})),d&&(d=i(s,d,function(t){return t.minWidth})),d=Math.abs(d),!e.settings.horizontalPageBreak&&d>.1/t.scaleFactor()&&(d=d<1?d:Math.round(d),console.warn("Of the table content, ".concat(d," units width could not fit page"))),function(t){for(var e=t.allRows(),n=0;n<e.length;n++)for(var o=e[n],i=null,r=0,l=0,a=0;a<t.columns.length;a++){var s=t.columns[a];if((l-=1)>1&&t.columns[a+1])r+=s.width,delete o.cells[s.index];else if(i){var u=i;delete o.cells[s.index],i=null,u.width=s.width+r}else{var u=o.cells[s.index];if(!u)continue;if(l=u.colSpan,r=0,u.colSpan>1){i=u,r+=s.width;continue}u.width=s.width+r}}}(e),function(t,e){for(var n={count:0,height:0},o=0,i=t.allRows();o<i.length;o++){for(var l=i[o],a=0,s=t.columns;a<s.length;a++){var u=s[a],d=l.cells[u.index];if(d){e.applyStyles(d.styles,!0);var h=d.width-d.padding("horizontal");if("linebreak"===d.styles.overflow)d.text=e.splitTextToSize(d.text,h+1/e.scaleFactor(),{fontSize:d.styles.fontSize});else if("ellipsize"===d.styles.overflow)d.text=r(d.text,h,d.styles,e,"...");else if("hidden"===d.styles.overflow)d.text=r(d.text,h,d.styles,e,"");else if("function"==typeof d.styles.overflow){var c=d.styles.overflow(d.text,h);"string"==typeof c?d.text=[c]:d.text=c}d.contentHeight=d.getContentHeight(e.scaleFactor(),e.getLineHeightFactor());var f=d.contentHeight/d.rowSpan;d.rowSpan>1&&n.count*n.height<f*d.rowSpan?n={height:f,count:d.rowSpan}:n&&n.count>0&&n.height>f&&(f=n.height),f>l.height&&(l.height=f)}}n.count--}}(e,t),function(t){for(var e={},n=1,o=t.allRows(),i=0;i<o.length;i++)for(var r=o[i],l=0,a=t.columns;l<a.length;l++){var s=a[l],u=e[s.index];if(n>1)n--,delete r.cells[s.index];else if(u)u.cell.height+=r.height,n=u.cell.colSpan,delete r.cells[s.index],u.left--,u.left<=1&&delete e[s.index];else{var d=r.cells[s.index];if(!d)continue;if(d.height=r.height,d.rowSpan>1){var h=o.length-i,c=d.rowSpan>h?h:d.rowSpan;e[s.index]={cell:d,left:c,row:r}}}}}(e)},e.resizeColumns=i,e.ellipsize=r},964:function(e){if(void 0===t){var n=Error("Cannot find module 'undefined'");throw n.code="MODULE_NOT_FOUND",n}e.exports=t}},n={};function o(t){var i=n[t];if(void 0!==i)return i.exports;var r=n[t]={exports:{}};return e[t].call(r.exports,r,r.exports,o),r.exports}var i={};return function(){Object.defineProperty(i,"__esModule",{value:!0}),i.Cell=i.Column=i.Row=i.Table=i.CellHookData=i.__drawTable=i.__createTable=i.applyPlugin=void 0;var t=o(340),e=o(776),n=o(664),r=o(972),l=o(260);Object.defineProperty(i,"Table",{enumerable:!0,get:function(){return l.Table}});var a=o(172);Object.defineProperty(i,"CellHookData",{enumerable:!0,get:function(){return a.CellHookData}});var s=o(260);function u(e){(0,t.default)(e)}Object.defineProperty(i,"Cell",{enumerable:!0,get:function(){return s.Cell}}),Object.defineProperty(i,"Column",{enumerable:!0,get:function(){return s.Column}}),Object.defineProperty(i,"Row",{enumerable:!0,get:function(){return s.Row}}),i.applyPlugin=u,i.__createTable=function(t,n){var o=(0,e.parseInput)(t,n);return(0,r.createTable)(t,o)},i.__drawTable=function(t,e){(0,n.drawTable)(t,e)};try{var d=o(964);d.jsPDF&&(d=d.jsPDF),u(d)}catch(t){}i.default=function(t,o){var i=(0,e.parseInput)(t,o),l=(0,r.createTable)(t,i);(0,n.drawTable)(t,l)}}(),i}()},t.exports=o(function(){try{return n(9707)}catch(t){}}())},76915:(t,e,n)=>{"use strict";n.d(e,{Z:()=>o});let o=(0,n(97428).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])}};