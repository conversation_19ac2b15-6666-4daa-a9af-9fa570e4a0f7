(()=>{var e={};e.id=9482,e.ids=[9482],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},91766:(e,o,t)=>{"use strict";t.r(o),t.d(o,{GlobalError:()=>r.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(42496),t(42832),t(78398),t(57757),t(48045);var s=t(40060),l=t(33581),i=t(57567),r=t.n(i),a=t(51650),n={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>a[e]);t.d(o,n);let d=["",{children:["vessel",{children:["logbook-configuration",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,42496)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\vessel\\logbook-configuration\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,42832)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\vessel\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\vessel\\logbook-configuration\\page.tsx"],u="/vessel/logbook-configuration/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/vessel/logbook-configuration/page",pathname:"/vessel/logbook-configuration",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},897:(e,o,t)=>{Promise.resolve().then(t.bind(t,57710))},84773:(e,o,t)=>{Promise.resolve().then(t.bind(t,64379))},28288:e=>{e.exports=function(e){return null==e}},47520:(e,o,t)=>{"use strict";t.d(o,{default:()=>l.a});var s=t(19821),l=t.n(s)},19821:(e,o,t)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return i}});let s=t(41034);t(98768),t(60343);let l=s._(t(40907));function i(e,o){var t;let s={loading:e=>{let{error:o,isLoading:t,pastDelay:s}=e;return null}};"function"==typeof e&&(s.loader=e);let i={...s,...o};return(0,l.default)({...i,modules:null==(t=i.loadableGenerated)?void 0:t.modules})}("function"==typeof o.default||"object"==typeof o.default&&null!==o.default)&&void 0===o.default.__esModule&&(Object.defineProperty(o.default,"__esModule",{value:!0}),Object.assign(o.default,o),e.exports=o.default)},96359:(e,o,t)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"BailoutToCSR",{enumerable:!0,get:function(){return l}});let s=t(90408);function l(e){let{reason:o,children:t}=e;throw new s.BailoutToCSRError(o)}},40907:(e,o,t)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"default",{enumerable:!0,get:function(){return d}});let s=t(98768),l=t(60343),i=t(96359),r=t(58902);function a(e){return{default:e&&"default"in e?e.default:e}}let n={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},d=function(e){let o={...n,...e},t=(0,l.lazy)(()=>o.loader().then(a)),d=o.loading;function c(e){let a=d?(0,s.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,n=o.ssr?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(r.PreloadCss,{moduleIds:o.modules}),(0,s.jsx)(t,{...e})]}):(0,s.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,s.jsx)(t,{...e})});return(0,s.jsx)(l.Suspense,{fallback:a,children:n})}return c.displayName="LoadableComponent",c}},58902:(e,o,t)=>{"use strict";Object.defineProperty(o,"__esModule",{value:!0}),Object.defineProperty(o,"PreloadCss",{enumerable:!0,get:function(){return i}});let s=t(98768),l=t(54580);function i(e){let{moduleIds:o}=e,t=(0,l.getExpectedRequestStore)("next/dynamic css"),i=[];if(t.reactLoadableManifest&&o){let e=t.reactLoadableManifest;for(let t of o){if(!e[t])continue;let o=e[t].files.filter(e=>e.endsWith(".css"));i.push(...o)}}return 0===i.length?null:(0,s.jsx)(s.Fragment,{children:i.map(e=>(0,s.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:t.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},65451:(e,o,t)=>{"use strict";t.d(o,{D:()=>l});var s=t(83962);let l=e=>(e?.customisedLogBookComponents?.nodes??[]).reduce((e,o)=>{let t=e.find(e=>e.componentClass===o.componentClass);return t?Number(o.id)>Number(t.id)?e.map(e=>e.componentClass===o.componentClass?o:e):e:[...e,o]},[]).map(e=>{let o=s.FV.find(o=>o.componentClass===e.componentClass);return o?{...e,title:o.label}:e})},33096:(e,o,t)=>{"use strict";t.d(o,{Z:()=>s});let s=["SLALL","Rescue_Vessel","Tug_Boat","Pilot_Vessel","Recreation","Passenger_Ferry","Water_Taxi","Sailing_Vessel","Large_Motor_Yacht","JetBoat"]},62466:(e,o,t)=>{"use strict";t.d(o,{Z:()=>a});var s=t(83179),l=t.n(s),i=t(86708);class r{async save(e){try{return await i.Z.CustomisedLogBookConfig.put({...e,idbCRUD:"Update",idbCRUDDate:l()().format("YYYY-MM-DD HH:mm:ss")}),console.log("CustomisedLogBookConfigModel save",e),e}catch(o){console.error("CustomisedLogBookConfigModel save",e,o)}}async getAll(){try{let e=await i.Z.CustomisedLogBookConfig.toArray();return console.log("CustomisedLogBookConfigModel getAll",e),e}catch(e){console.error("CustomisedLogBookConfigModel getAll",e)}}async getById(e){try{let o=await i.Z.CustomisedLogBookConfig.get(`${e}`);return console.log("CustomisedLogBookConfigModel getById",e,o),o}catch(o){console.error("CustomisedLogBookConfigModel getById",e,o)}}async getByCustomisedLogBookId(e){try{let o=await i.Z.CustomisedLogBookConfig.where("customisedLogBookID").equals(`${e}`).toArray(),t=o&&o.length>0?o[0]:null;return console.log("CustomisedLogBookConfigModel getByCustomisedLogBookId",e,t),t}catch(o){console.error("CustomisedLogBookConfigModel getByCustomisedLogBookId",e,o)}}async bulkAdd(e){try{return await i.Z.CustomisedLogBookConfig.bulkAdd(e),console.log("CustomisedLogBookConfigModel bulkAdd",e),e}catch(o){if("BulkError"===o.name){let t=o.failuresByPos.map(e=>e.key),s=e.filter(e=>!t.includes(e.id));return await i.Z.CustomisedLogBookConfig.bulkAdd(s),console.log("CustomisedLogBookConfigModel bulkAdd::BulkError",e,o),e}console.error("CustomisedLogBookConfigModel bulkAdd",e,o)}}async setProperty(e){try{if(e){let o=await i.Z.CustomisedLogBookConfig.get(`${e}`);return o.idbCRUD="Download",o.idbCRUDDate=l()().format("YYYY-MM-DD HH:mm:ss"),await i.Z.CustomisedLogBookConfig.update(e,o),console.log("CustomisedLogBookConfigModel setProperty",e,o),o}}catch(o){console.error("CustomisedLogBookConfigModel setProperty",e,o)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await i.Z.CustomisedLogBookConfig.update(e.id,e)})),console.log("CustomisedLogBookConfigModel multiUpdate",e)}catch(o){console.error("CustomisedLogBookConfigModel multiUpdate",e,o)}}async delete(e){try{return await i.Z.CustomisedLogBookConfig.delete(`${e}`),!0}catch(o){console.error("CustomisedLogBookConfigModel delete",e,o)}}}let a=r},33849:(e,o,t)=>{"use strict";t.d(o,{Z:()=>a});var s=t(98768);t(60343);var l=t(47520);t(30854);var i=t(56937);let r=(0,l.default)(async()=>{},{loadableGenerated:{modules:["app\\ui\\editor.tsx -> react-quill"]},ssr:!1});function a(e,o){return s.jsx(r,{value:e.content,placeholder:e.placeholder,onChange:e.handleEditorChange,onBlur:e.handleEditorBlur,modules:{toolbar:[[{header:[1,2,3,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"}],["link","image"],[{align:[]},{color:[]}],["clean"]]},formats:["header","bold","italic","underline","strike","blockquote","list","bullet","link","image","align","color","code-block"],className:(0,i.cn)("w-full min-h-60 mb-2 bg-card border overflow-auto text-input border-border rounded-lg",o)})}},93956:(e,o,t)=>{"use strict";t.d(o,{Ar:()=>y,Bo:()=>k,EJ:()=>f,Gy:()=>L,Lj:()=>x,N0:()=>h,No:()=>g,Ts:()=>C,Up:()=>n,V6:()=>d,V9:()=>b,Vu:()=>a,ZU:()=>j,Z_:()=>c,dO:()=>v,n3:()=>m,qH:()=>p,ql:()=>N,sU:()=>u,td:()=>i});var s=t(28288),l=t.n(s);let i=(e,o,t,s)=>{if(e&&s.vesselType){let l=o.map(e=>e);return{...e,customisedLogBookComponents:{...e.customisedLogBookComponents,nodes:e.customisedLogBookComponents.nodes.filter(e=>{var o=!1;return l.forEach(l=>{e.componentClass===l.componentClass&&l.vesselType.includes(t.indexOf(s.vesselType))&&(o=!0)}),o})}}}return e},r=(e,o)=>{let t=o.map(e=>e);var s=0;return t.forEach(o=>{e.componentClass===o.componentClass&&(s=o?.sortOrder?o?.sortOrder:99)}),s},a=(e,o)=>e.sort((e,t)=>r(e,o)>r(t,o)?1:r(e,o)<r(t,o)?-1:0),n=(e,o,t)=>{if(e?.__typename&&"CustomisedComponentField"===e.__typename){let t=o.filter(o=>o.fieldID===e.id).map(e=>e.status);return t.length>0?"Off"!==t[0]:"Off"!==e.status}{let o=t.filter(o=>o.localID===e.localID).map(e=>e.status);if(o.length>0)return"Off"!==o[0]}},d=(e,o,t)=>{if(e?.__typename&&"CustomisedComponentField"===e.__typename){let t=o.filter(o=>o.fieldID===e.id).map(e=>e.status);return t.length>0?"Off"===t[0]:"Off"===e.status}{let o=t.filter(o=>o.localID===e.localID).map(e=>e.status);if(o.length>0)return"Off"===o[0]}return!0},c=(e,o)=>{let t=e.customisedFieldTitle||e.fieldName,s=o.customisedFieldTitle||o.fieldName;return e.sortOrder-o.sortOrder||(l()(t)?1:l()(s)?-1:t.localeCompare(s))},u=(e,o,t,s)=>{let l=o.map(e=>e);var i=[];return l.forEach(o=>{t.toLowerCase()===o.label.toLowerCase()&&o.items.forEach(t=>{if(t.groupTo===e){let e=s.customisedLogBookComponents.nodes.find(e=>e.componentClass===o.componentClass);if(e){let o=e.customisedComponentFields.nodes.find(e=>e.fieldName===t.value);if(o)i.push(o);else{let o=e.id;i.push({...t,customisedLogBookComponentID:o,localID:o+t.value})}}}})}),i.sort(c)},m=(e,o,t)=>{if(!e?.__typename||"CustomisedComponentField"!==e.__typename)return e.fieldSet?e.fieldSet:"Other";{let l=o.map(e=>e);var s="Other";return l.forEach(o=>{t===o.label&&o.items.forEach((o,t)=>{e.fieldName===o.value&&(s=o?.fieldSet?o.fieldSet:"Other")})}),s}},p=(e,o,t)=>{if(!e?.__typename||"CustomisedComponentField"!==e.__typename)return!!e.groupTo&&e.groupTo;{let l=o.map(e=>e);var s=!1;return l.forEach(o=>{t===o.label&&o.items.forEach((o,t)=>{e.fieldName===o.value&&(s=!!o?.groupTo&&o.groupTo)})}),s}},f=(e,o,t)=>{let s=t.customisedLogBookComponents.nodes.filter(o=>o.id==e.id).map(e=>e),l=o.map(e=>e);var i=[];return s.forEach(e=>{let o=l.find(o=>o.componentClass===e.componentClass);o&&o.items.forEach(o=>{e.customisedComponentFields.nodes.some(e=>e.fieldName===o.value)||i.push(o)})}),i},g=(e,o,t,s,l)=>{if("Daily Checks"===o)return null==e.subFields?void 0:""==e.subFields||"Enabled"==s.filter(e=>{if(e.label===l)return e}).status&&"Engine Checks"==t||e.subFields&&e.subFields.split("||").includes(t)?"":"opacity-60 pointer-events-none"},h=(e,o,t)=>{let s=t.customisedLogBookComponents.nodes.filter(o=>o.id==e.id).map(e=>e),l=o.map(e=>e);var i=!1;return s.forEach(e=>{l.forEach(o=>{e.componentClass===o.componentClass&&o?.subCategory&&(i=!0)})}),i},x=(e,o)=>{let t=o.customisedLogBookComponents.nodes,s=e.map(e=>e);var l=[];return s.forEach(e=>{t.filter(o=>o.componentClass===e.componentClass).length>0||l.push(e)}),l},v=(e,o,t)=>{if(e?.__typename&&"CustomisedComponentField"===e.__typename){let l=o.map(e=>e);var s=!1;return l.forEach(o=>{t===o.label&&o.items.forEach(o=>{e.fieldName===o.value&&"files"===o.fieldType&&(s=!0)})}),s}{let l=o.map(e=>e);var s=!1;return l.forEach(o=>{t===o.label&&o.items.forEach(o=>{e.value===o.value&&"files"===o.fieldType&&(s=!0)})}),s}},b=(e,o)=>{let t=o.map(e=>e);var s=e.title;return t.forEach(o=>{e.componentClass===o.componentClass&&(s=o?.title?o?.title:e.title)}),s},C=(e,o)=>{if(!e?.__typename||"CustomisedComponentField"!==e.__typename)return e?.label?e.label:e?.value;{let s=o.map(e=>e);var t=e.fieldName;return s.forEach(o=>{o.items.forEach(o=>{e.fieldName===o.value&&(t=o?.label?o?.label:e.fieldName)})}),t}},y=(e,o,t)=>{var s=!1;return o.customisedLogBookComponents?.nodes.filter(e=>e.title===t).map(o=>{o.customisedComponentFields.nodes.filter((e,o,t)=>t.findIndex(o=>o.fieldName===e.fieldName)===o).forEach(o=>{o.fieldName===e&&(s=!0)})}),s},j=(e,o,t)=>{let s=o.map(e=>e);var l="";return e?.__typename&&"CustomisedComponentField"===e.__typename?s.forEach(o=>{t===o.label&&o.items.forEach(o=>{e.fieldName===o.value&&(l+=o?.classes)})}):s.forEach(o=>{t===o.label&&o.items.forEach(o=>{e.value===o.value&&(l+=o?.classes)})}),l},k=(e,o,t)=>t.filter(e=>e.subCategory)[0].items.filter(o=>3===o.level&&o.fieldSet===e).length>0,N=(e,o,t)=>{if(!e?.__typename||"CustomisedComponentField"!==e.__typename)return!!e.tab&&e.tab;{let l=o.map(e=>e);var s=!1;return l.forEach(o=>{t===o.label&&o.items.forEach((o,t)=>{e.fieldName===o.value&&(s=o.tab)})}),s}},L=(e,o,t)=>{let s=o.map(e=>e);var l=!1;return s.forEach(o=>{t===o.label&&o.items.forEach(o=>{e.fieldName===o?.groupTo&&(l=!0)})}),l}},57710:(e,o,t)=>{"use strict";t.r(o),t.d(o,{default:()=>i});var s=t(98768);t(60343);var l=t(64837);function i({children:e}){return s.jsx(l.Z,{children:e})}},64379:(e,o,t)=>{"use strict";t.r(o),t.d(o,{default:()=>J});var s=t(98768),l=t(60343),i=t(33096),r=t(76342),a=t(79418),n=t(72548),d=t(94060),c=t(13842),u=t(69424),m=t(34376);t(46776);var p=t(93956),f=t(83179),g=t.n(f),h=t(86708);require("assert");class x{async save(e){try{return await h.Z.CustomisedComponentField.put({...e,idbCRUD:"Update",idbCRUDDate:g()().format("YYYY-MM-DD HH:mm:ss")}),console.log("CustomisedComponentFieldModel save",e),e}catch(o){console.error("CustomisedComponentFieldModel save",e,o)}}async getAll(){try{let e=await h.Z.CustomisedComponentField.toArray();return console.log("CustomisedComponentFieldModel getAll",e),e}catch(e){console.error("CustomisedComponentFieldModel getAll",e)}}async getById(e){try{let o=await h.Z.CustomisedComponentField.get(`${e}`);return console.log("CustomisedComponentFieldModel getById",e,o),o}catch(o){console.error("CustomisedComponentFieldModel getById",e,o)}}async getByIds(e){try{let o=await h.Z.CustomisedComponentField.where("id").anyOf(e).toArray();return console.log("CustomisedComponentFieldModel getByIds",e,o),o}catch(o){console.error("CustomisedComponentFieldModel getByIds",e,o)}}async bulkAdd(e){try{return await h.Z.CustomisedComponentField.bulkAdd(e),console.log("CustomisedComponentFieldModel bulkAdd",e),e}catch(o){if("BulkError"===o.name){let t=Object.keys(o.failuresByPos),s=e.filter((e,o)=>!t.includes(o));return await h.Z.CustomisedComponentField.bulkAdd(s),console.log("CustomisedComponentFieldModel bulkAdd::BulkError",e,o),e}console.error("CustomisedComponentFieldModel bulkAdd",e,o)}}async setProperty(e){try{if(e){let o=await h.Z.CustomisedComponentField.get(`${e}`);return o.idbCRUD="Download",o.idbCRUDDate=g()().format("YYYY-MM-DD HH:mm:ss"),await h.Z.CustomisedComponentField.update(e,o),console.log("CustomisedComponentFieldModel setProperty",e,o),o}}catch(o){console.error("CustomisedComponentFieldModel setProperty",e,o)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await h.Z.CustomisedComponentField.update(e.id,e)})),console.log("CustomisedComponentFieldModel multiUpdate",e)}catch(o){console.error("CustomisedComponentFieldModel multiUpdate",e,o)}}async delete(e){try{return await h.Z.CustomisedComponentField.delete(`${e}`),!0}catch(o){console.error("CustomisedComponentFieldModel delete",e,o)}}}var v=t(62466),b=t(65451),C=t(83962),y=t(25394),j=t(78965),k=t(97428);let N=(0,k.Z)("Ban",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m4.9 4.9 14.2 14.2",key:"1m5liu"}]]),L=(0,k.Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var B=t(94460),F=t(17203);let w=(0,k.Z)("FilePlus",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M9 15h6",key:"cctwl0"}],["path",{d:"M12 18v-6",key:"17g6i2"}]]);var D=t(3510),_=t(68648),E=t(39544),T=t(56937);function S({tab:e,logBookConfig:o,slallFields:t,imCrew:l,createCustomisedComponent:i,fields:r}){return s.jsx("div",{className:"flex flex-col gap-4 mt-4",children:r.map(r=>(0,s.jsxs)("div",{className:(0,T.cn)("grid grid-cols-3 pb-2 border border-border rounded-lg",r.label,!(e?.replace("Logbook","LogBook")===r.label.replace("Logbook","LogBook")||"Crew Welfare"===r.label&&"Crew Members"===e||"Engine Reports"===e&&("Engineer_LogBookComponent"===r.componentClass||"Fuel_LogBookComponent"===r.componentClass))&&"hidden"),children:[(0,s.jsxs)("div",{className:"col-span-3 md:col-span-1 flex flex-col justify-between bg-background",children:[(0,s.jsxs)("div",{className:"p-4 mt-2 font-medium text-xl",children:[s.jsx("p",{children:r.label}),s.jsx("p",{className:"mt-2 text-destructive text-lg",children:"Not Exist"})]}),!(0,p.N0)(r,t,o)&&s.jsx("div",{className:"mt-2 p-4",children:!l&&s.jsx(E.Button,{variant:"primaryOutline",iconLeft:w,color:"sky",onClick:()=>{document.body.style.cursor="wait",i({variables:{input:{title:r.label,sortOrder:r.sortOrder||0,category:r.category,customisedLogBookConfigID:o.id,componentClass:r.componentClass,active:!0}}})},children:`Add ${r.label}`})})]}),(0,s.jsxs)("div",{className:"col-span-3 md:col-span-2",children:[s.jsx("p",{className:"font-medium px-4 py-3 border-b border-border text-lg",children:"Available Fields:"}),s.jsx("div",{className:"max-h-80 overflow-y-auto",children:r.items.map((e,o)=>s.jsx("div",{className:"border-b border-border py-3 px-4",children:e.label},e.value+"_"+o))})]})]},r.label))})}let M=(0,k.Z)("Asterisk",[["path",{d:"M12 6v12",key:"1vza4d"}],["path",{d:"M17.196 9 6.804 15",key:"1ah31z"}],["path",{d:"m6.804 9 10.392 6",key:"1b6pxd"}]]);function O({message:e}){return(0,s.jsxs)(y.u,{children:[s.jsx(y.aJ,{children:s.jsx(M,{className:"text-destructive",size:18})}),s.jsx(y._v,{children:s.jsx("p",{children:e})})]})}var I=t(58830),Z=t(18415);function P({title:e,isRequired:o=!1,className:t,isDisabled:l=!1,value:i,onUpdateField:r,field:a,onDescriptionClick:n,onCustomizeClick:d}){return(0,s.jsxs)("div",{className:(0,T.cn)("flex flex-nowrap border-b border-border gap-4",t),children:[s.jsx("div",{className:"flex",children:(0,s.jsxs)(y.Ee,{variant:"horizontal",className:(0,T.cn)({"opacity-60":l}),gap:"none",value:i,onValueChange:e=>{r(a,"yes"===e?"Required":"Off")},disabled:l,children:[s.jsx("div",{className:(0,T.cn)("flex w-[48px] bg-cinnabar-100 justify-center py-3 standard:p-0 standard:items-center"),children:s.jsx(y.mJ,{value:"no",id:`${a.id}-no_radio`,variant:"destructive",size:"lg"})}),s.jsx("div",{className:(0,T.cn)("flex w-[48px] bg-bright-turquoise-100 justify-center pt-3 standard:p-0 standard:items-center"),children:s.jsx(y.mJ,{value:"yes",id:`${a.id}-yes_radio`,variant:"success",size:"lg"})})]})}),(0,s.jsxs)("div",{className:"flex flex-grow justify-between items-center gap-3 py-1 pr-2",children:[(0,s.jsxs)("div",{className:"flex gap-2 items-center",children:[e,o&&s.jsx(O,{message:"This is a must have field!"}),a?.description&&s.jsx(y.zx,{variant:"text",size:"icon",onClick:()=>n&&n(e,a?.description),children:s.jsx(I.Z,{})})]}),s.jsx(y.zx,{size:"icon",variant:"text",onClick:()=>d&&d(a),children:s.jsx(Z.Z,{})})]})]})}var z=t(24894),q=t(8087);function R({fileUploadText:e,documents:o,setDocuments:t,showFileUpload:l=!0}){return(0,s.jsxs)(s.Fragment,{children:[o.length>0&&o.map((e,l)=>s.jsx($,{title:e.title,onClick:()=>{t(o.filter(o=>o.id!==e.id))}},e.id+"_"+l)),l&&s.jsx(z.Z,{setDocuments:e=>t(e),text:e,documents:o})]})}function $({title:e,onClick:o}){return s.jsx("div",{children:(0,s.jsxs)("div",{className:"flex items-center w-full justify-between mb-2 uppercase",children:[s.jsx("span",{children:e}),s.jsx(y.zx,{onClick:()=>o(),size:"icon",variant:"destructive",children:s.jsx(q.Z,{})})]})})}var V=t(33849);function A({title:e,field:o,isOpen:t,onOpenChange:i,defaultValues:r,isFieldGroup:a,onSave:n}){let[d,c]=(0,l.useState)(r);return s.jsx(y.yo,{open:t,onOpenChange:i,children:(0,s.jsxs)(y.ue,{children:[s.jsx(y.Tu,{children:(0,s.jsxs)(y.bC,{children:["Field - ",s.jsx("span",{className:"font-thin",children:e})]})}),(0,s.jsxs)(y.Fm,{className:"flex flex-col gap-4",children:[!a&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(y.__,{label:"Customised Field Name",children:s.jsx(y.II,{type:"text",id:"field-name",placeholder:"(Optional)",value:d.fieldName,onChange:e=>c(o=>({...o,fieldName:e.target.value}))})}),s.jsx(y.__,{label:"Sort Order",children:s.jsx(y.II,{type:"number",min:1,id:"field-sort-order",value:d.sortOrder,onChange:e=>c(o=>({...o,sortOrder:+e.target.value}))})})]}),s.jsx(y.__,{label:"Description",children:s.jsx(V.Z,{id:"field-description",placeholder:"Description (Optional)",content:d.description,handleEditorChange:e=>c(o=>({...o,description:e}))})})]}),s.jsx(y.FF,{children:s.jsx(y.zx,{variant:"primary",onClick:()=>n(o,d),children:"Save"})})]})})}function U({title:e,field:o,className:t,value:l,onUpdateField:i,onDescriptionClick:r,onCustomizeClick:a,isDisabled:n}){return(0,s.jsxs)("div",{className:(0,T.cn)("flex justify-between items-start",t),children:[(0,s.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,s.jsxs)("div",{className:"flex gap-2 items-center font-medium text-xl",children:[e," ",o?.description&&s.jsx(y.zx,{variant:"text",size:"icon",onClick:()=>r&&r(e,o?.description),children:s.jsx(I.Z,{})})]}),(0,s.jsxs)(y.Ee,{variant:"horizontal",className:(0,T.cn)({"opacity-60":n}),gap:"large",value:l,onValueChange:e=>{i(o,"yes"===e?"Required":"Off")},disabled:n,children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[s.jsx(y.mJ,{value:"no",id:`${e}-group-no_radio`,variant:"destructive",size:"lg"}),s.jsx("div",{className:"font-medium text-destructive",children:"No"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[s.jsx(y.mJ,{value:"yes",id:`${e}-group-yes_radio`,variant:"success",size:"lg"}),s.jsx("div",{className:"font-medium text-bright-turquoise-600",children:"Yes"})]})]})]}),s.jsx(y.zx,{size:"icon",variant:"text",onClick:()=>a&&a(o),children:s.jsx(Z.Z,{})})]})}let Y=()=>{let[e,o]=(0,l.useState)(""),[t,s]=(0,l.useState)(!0),[i,r]=(0,l.useState)(!1),[a,n]=(0,l.useState)(null),[d,c]=(0,l.useState)({sortOrder:0,description:"",fieldName:""});return{title:e,isFieldGroup:t,selectedField:a,form:d,isOpen:i,onOpenChange(e){r(e)},openDialog:({field:e,title:t,isFieldGroup:l})=>{n(e),c({fieldName:e?.customisedFieldTitle??"",sortOrder:e?.sortOrder??0,description:e?.description??""}),s(l),o(t),r(!0)},closeDialog(){r(!1)}}},H=()=>{let[e,o]=(0,l.useState)(!1),[t,s]=(0,l.useState)(""),[i,r]=(0,l.useState)("");return{title:t,content:i,open:e,onOpenChange(e){o(e)},openDialog(e,t){s(e),r(t??""),o(!0)},closeDialog(){o(!1)}}},G=({open:e,onOpenChange:o,title:t,content:l})=>s.jsx(y.yo,{open:e,onOpenChange:o,children:(0,s.jsxs)(y.ue,{children:[s.jsx(y.Tu,{children:(0,s.jsxs)(y.bC,{children:["Field - ",s.jsx("span",{className:"font-thin",children:t})]})}),s.jsx(y.Fm,{children:s.jsx("div",{className:"leading-loose",dangerouslySetInnerHTML:{__html:l}})})]})});function W({logBookID:e,vesselID:o}){let{toast:t}=(0,m.pm)(),f=(0,u.useRouter)(),[g,h]=(0,l.useState)(!0),[k,E]=(0,l.useState)(!1),[M,O]=(0,l.useState)([]),[I,Z]=(0,l.useState)([]),[z,q]=(0,l.useState)(!1),[$,V]=(0,l.useState)(""),[W,J]=(0,l.useState)([]),[Q,X]=(0,l.useState)(),[K,ee]=(0,l.useState)(),[eo,et]=(0,l.useState)(!1),[es,el]=(0,l.useState)(!1),[ei,er]=(0,l.useState)(!1),[ea,en]=(0,l.useState)(!1),[ed,ec]=(0,l.useState)(-1),[eu,em]=(0,l.useState)([]),[ep,ef]=(0,l.useState)(!1),[eg,eh]=(0,l.useState)(!0),[ex,ev]=(0,l.useState)(!1),[eb,eC]=(0,l.useState)(null),ey=Y(),ej=H();(0,c.BJ)(o,e=>{if(e?.vesselType){let t=C.FV.filter(o=>o?.items?.length>0&&o.vesselType.includes(i.Z.indexOf(e?.vesselType)));var o=[];t.map(t=>{var s=[];t.items.map(o=>{o.vesselType.includes(i.Z.indexOf(e?.vesselType))&&(e?.vesselSpecifics?.carriesDangerousGoods==!1?"dangerous-goods-sailing"!==o.classes&&s.push(o):s.push(o))}),t.items=s,o.push(t)}),eC(o)}q(e),eB()});let ek=(0,l.useMemo)(()=>eb||C.FV,[eb]),[eN]=(0,a.t)(d.Yo,{fetchPolicy:"no-cache",onCompleted:o=>{let t=o.readOneCustomisedLogBookConfig,s=(0,b.D)(t);s=s.map(e=>(e.customisedComponentFields.nodes&&e.customisedComponentFields.nodes.sort(p.Z_),e)),t.customisedLogBookComponents.nodes=s;let l=(0,p.td)(t,ek,i.Z,z);if(l){if(Z([]),O([]),E(l),l.policies.nodes.length>0&&em(l.policies.nodes),!$){let e=l.customisedLogBookComponents?.nodes.map(e=>({title:e.title,category:e.category,componentClass:e.componentClass})).sort(),o=l.customisedLogBookComponents.nodes,t=ek.map(e=>e);var r=[];t.forEach(e=>{var t=!1;o.forEach(o=>{o.componentClass===e.componentClass&&(t=!0)}),t||r.push(e)});let s=r.map(e=>({title:e.label,category:e.category,componentClass:e.componentClass})),i=(0,p.Vu)([...e,...s],ek);J(i),V(i[0].title)}}else document.body.style.cursor="wait",eL({variables:{input:{customisedLogBookID:e}}})},onError:e=>{console.error("queryLogBookConfig error",e)}}),[eL]=(0,n.D)(r.tE3,{onCompleted:e=>{document.body.style.cursor="auto",e.createCustomisedLogBookConfig},onError:e=>{document.body.style.cursor="auto",console.error("updateVessel error",e)}}),eB=async()=>{await eN({variables:{id:+e}})},eF=(e,o)=>{e?.__typename&&"CustomisedComponentField"===e.__typename?(O([...M.filter(o=>o.fieldID!==e.id),{fieldID:e.id,status:o}]),document.body.style.cursor="wait",e_({variables:{input:{id:e.id,status:o}}})):(Z([...I.filter(o=>o.localID!==e.localID),{...e,status:o}]),document.body.style.cursor="wait",ew({variables:{input:{customisedFieldTitle:e?.title?e.title:e.label,customisedLogBookComponentID:e.customisedLogBookComponentID,fieldName:e.value,status:o,sortOrder:e.sortOrder||0}}}))},[ew]=(0,n.D)(r.LFI,{onCompleted:e=>{document.body.style.cursor="auto",e.createCustomisedComponentField,eB()},onError:e=>{document.body.style.cursor="auto",console.error("updateVessel error",e)}}),eD=async e=>{if(+e>0){let o=new x;await o.delete(e)}},[e_]=(0,n.D)(r.G7d,{onCompleted:e=>{document.body.style.cursor="auto",eD(e.updateCustomisedComponentField.id),ed>0&&ec(ed-1),0==ed&&ec(ed-1),eB()},onError:e=>{document.body.style.cursor="auto",console.error("updateVessel error",e)}}),eE=async(e,o)=>{document.body.style.cursor="wait",await e_({variables:{input:{id:e.id,customisedFieldTitle:o.fieldName,sortOrder:o.sortOrder,description:"<p><br></p>"===o.description?"":o.description}}}),ey.closeDialog()},eT=e=>{ec(ed+1);let o=k.customisedLogBookComponents.nodes.filter(o=>o.id==e.id).map(e=>e),t=ek.map(e=>e);o.forEach(e=>{t.forEach(o=>{e.componentClass===o.componentClass&&e.customisedComponentFields.nodes.filter((e,o,t)=>t.findIndex(o=>o.fieldName===e.fieldName)===o).forEach(e=>{o.items.forEach(o=>{if(e.fieldName===o.value){let t=M.find(o=>o.fieldID===e.id);o.status!=e.status&&(document.body.style.cursor="wait",e_({variables:{input:{id:e.id,status:o.status}}}),ec(ed+1)),t?.fieldID&&t?.status!=o.status&&(document.body.style.cursor="wait",e_({variables:{input:{id:e.id,status:o.status}}}),ec(ed+1))}})})})})},[eS]=(0,n.D)(r.KMF,{onCompleted:e=>{document.body.style.cursor="auto",e.updateCustomisedLogBookComponent,eB()},onError:e=>{document.body.style.cursor="auto",console.error("updateVessel error",e)}}),eM=e=>()=>{document.body.style.cursor="wait",eS({variables:{input:{id:e,active:!0}}})},eO=e=>()=>{document.body.style.cursor="wait",eS({variables:{input:{id:e,active:!1}}})},eI=(e,o)=>()=>{e.subFields?(document.body.style.cursor="wait",eS({variables:{input:{id:e.id,subFields:e.subFields+"||"+o}}})):(document.body.style.cursor="wait",eS({variables:{input:{id:e.id,subFields:es.concat(o).join("||")}}}))},eZ=(e,o)=>()=>{e.subFields?(document.body.style.cursor="wait",eS({variables:{input:{id:e.id,subFields:e.subFields.split("||").filter(e=>e!==o).join("||")}}})):(document.body.style.cursor="wait",eS({variables:{input:{id:e.id,subFields:es.filter(e=>e!=o).join("||")}}}))},eP=(e,o)=>()=>{e.subFields?(document.body.style.cursor="wait",eS({variables:{input:{id:e.id,subFields:e.subFields+"||"+o.label}}})):(document.body.style.cursor="wait",eS({variables:{input:{id:e.id,subFields:es.concat(o.label).join("||")}}})),ei.map(e=>(e.label===o.label&&(e.status="Enabled"),e))},ez=(e,o)=>()=>{e.subFields?(document.body.style.cursor="wait",eS({variables:{input:{id:e.id,subFields:e.subFields.split("||").filter(e=>e!==o.label).join("||")}}})):(document.body.style.cursor="wait",eS({variables:{input:{id:e.id,subFields:es.filter(e=>e!=o.label).join("||")}}})),ei.map(e=>(e.label===o.label&&(e.status="Disabled"),e))},[eq]=(0,n.D)(r.Lss,{onCompleted:e=>{document.body.style.cursor="auto",e.createCustomisedComponent,eB()},onError:e=>{document.body.style.cursor="auto",console.error("updateVessel error",e)}}),eR=e=>{if(e?.__typename&&"CustomisedComponentField"===e.__typename){let o=!!C.HN.includes(e.fieldName);return o&&!(0,p.Up)(e,M,I)&&eF(e,"Required"),o}{let o=!!C.HN.includes(e.value);return o&&!(0,p.Up)(e,M,I)&&(0===I.filter(o=>o.localID!==e.localID).length&&(document.body.style.cursor="wait",ew({variables:{input:{customisedFieldTitle:e?.title?e.title:e.label,customisedLogBookComponentID:e.customisedLogBookComponentID,fieldName:e.value,status:"Required",sortOrder:e.sortOrder||0}}})),Z([...I.filter(o=>o.localID!==e.localID),{...e,status:"Required"}])),o}},e$=(e=!0)=>{e?(ef(!0),t({title:"Saving",description:"Logbook configuration saving..."})):eh(!1);let o=eu.length>0?eu?.map(e=>+e.id).join(","):"";document.body.style.cursor="wait",eA({variables:{input:{id:k.id,policies:o}}}),h(!0)},eV=async e=>{if(+e>0){let o=new v.Z;await o.delete(e)}},[eA]=(0,n.D)(r.nvD,{onCompleted:e=>{document.body.style.cursor="auto";let o=e.updateCustomisedLogBookConfig;eV(o.id),o.id>0&&(eg&&(t({title:"Info!",description:"Logbook configuration saved"}),ef(!1)),eh(!0))},onError:e=>{document.body.style.cursor="auto",console.error("updateVessel error",e)}}),eU=(e,o)=>{let t=ek.map(e=>e);var s=[],l=[];if(t.forEach(e=>{$?.replace("Logbook","LogBook")===e.label.replace("Logbook","LogBook")&&e.componentClass===o&&e.items.forEach(e=>{e.groupTo&&3!==e.level&&!l.includes(e.groupTo)&&l.push(e.groupTo)})}),t.forEach(e=>{$?.replace("Logbook","LogBook")===e.label.replace("Logbook","LogBook")&&e.items.forEach((e,o)=>{l.forEach(t=>{e.value!==t||(0,p.Ar)(e.value,k,$)||3===e.level||s.push({...e,description:null,fieldName:e.value,id:o+"0",sortOrder:o,status:e.status})})})}),l.length>0){let o=s.flatMap(e=>e.value);return[...s.map(o=>{let t=o;return e.map(e=>{o.value===e.fieldName&&(t={...o,...e})}),t}),...e.filter(e=>!o.includes(e.fieldName))].filter(e=>eY(e)).sort(p.Z_)}return e.filter(e=>eY(e)).sort(p.Z_)},eY=e=>{if(e?.__typename&&"CustomisedComponentField"===e.__typename){let l=ek.map(e=>e);var o=!1,t=!1,s=!1;return l.forEach(l=>{($?.replace("Logbook","LogBook")===l.label.replace("Logbook","LogBook")||"Crew Welfare"===l.label&&"Crew Members"===$)&&(void 0!==l.items.find(o=>o.groupTo===e.fieldName)&&(s=!0),l.items.forEach(s=>{e.fieldName===s.value&&(o=!!s?.groupTo&&s.groupTo),e.fieldName===s.value&&s.vesselType.includes(i.Z.indexOf(z?.vesselType))&&3!==s.level&&(t=e)}))}),!1===o&&!1!==t&&!1!==s}{let l=ek.map(e=>e);var o=!1,t=!1,s=!1;return l.forEach(l=>{($?.replace("Logbook","LogBook")===l.label.replace("Logbook","LogBook")||"Crew Welfare"===l.label&&"Crew Members"===$)&&(void 0!==l.items.find(o=>o.groupTo===e.value)&&(s=!0),l.items.forEach(s=>{e.value===s.value&&(o=!!s?.groupTo&&s.groupTo),e.value===s.value&&s.vesselType.includes(i.Z.indexOf(z?.vesselType))&&3!==s.level&&(t=e)}))}),!1===o&&!1!==t&&!1!==s}},eH=e=>{if(e?.__typename&&"CustomisedComponentField"===e.__typename){let l=ek.map(e=>e);var o=!1,t=!1,s=!1;return l.forEach(l=>{($?.replace("Logbook","LogBook")===l.label.replace("Logbook","LogBook")||"Crew Welfare"===l.label&&"Crew Members"===$)&&(void 0!==l.items.find(o=>o.groupTo===e.fieldName)&&(s=!0),l.items.forEach(s=>{e.fieldName===s.value&&(o=!!s?.groupTo&&s.groupTo),e.fieldName===s.value&&s.vesselType.includes(i.Z.indexOf(z?.vesselType))&&3!==s.level&&(t=e)}))}),!1===o&&!1!==t&&!1==s}{let l=ek.map(e=>e);var o=!1,t=!1,s=!1;return l.forEach(l=>{($?.replace("Logbook","LogBook")===l.label.replace("Logbook","LogBook")||"Crew Welfare"===l.label&&"Crew Members"===$)&&(void 0!==l.items.find(o=>o.groupTo===e.value)&&(s=!0),l.items.forEach(s=>{e.value===s.value&&(o=!!s?.groupTo&&s.groupTo),e.value===s.value&&s.vesselType.includes(i.Z.indexOf(z?.vesselType))&&3!==s.level&&(t=e)}))}),!1===o&&!1!==t&&!1==s}},eG=e=>!(0,p.qH)(e,ek,$?.replace("Logbook","LogBook"))||(0,p.qH)(e,ek,$?.replace("Logbook","LogBook"))===ea,eW=e=>{let o=(0,p.ql)(e,ek,$?.replace("Logbook","LogBook"));return!o||o===ea},eJ=(0,l.useMemo)(()=>W.filter(e=>"Crew Welfare"!==e.title&&"Crew Training"!==e.title&&"Engine_LogBookComponent"!==e.componentClass&&"Engineer_LogBookComponent"!==e.componentClass&&"Fuel_LogBookComponent"!==e.componentClass&&"Supernumerary_LogBookComponent"!==e.componentClass),[W]),eQ=(0,l.useMemo)(()=>k?(0,p.Lj)(ek,k)??[]:[],[k,ek]),eX=e=>$?.replace("Logbook","LogBook")===e.title.replace("Logbook","LogBook")||"Crew Members"===$&&"Crew Welfare"===e.title||"Engine Reports"===$&&("Engineer_LogBookComponent"===e.componentClass||"Fuel_LogBookComponent"===e.componentClass),eK=e=>e.customisedComponentFields.nodes.filter((e,o,t)=>t.findIndex(o=>o.fieldName===e.fieldName)===o).filter(e=>eH(e)).sort(p.Z_),e0=e=>{ey.openDialog({field:e,title:e?.customisedFieldTitle?e?.customisedFieldTitle:(0,p.Ts)(e,ek),isFieldGroup:(0,p.Gy)(e,ek,$?.replace("Logbook","LogBook"))})};return console.info("Tab",$,W),console.info("Daily Check Category",eo,es),console.info("Level Three Category",ea,ei),(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx(y.Bu,{title:`Logbook Configuration: ${z?.title}`}),(0,s.jsxs)(y.Zb,{children:[s.jsx(y.Ol,{children:(0,s.jsxs)(y.ll,{className:"text-2xl font-semibold",children:["Vessel Type: ",z?.vesselType?.replaceAll("_"," ")]})}),s.jsx(y.aY,{children:!g&&eJ.length>0&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(y.mQ,{value:$,onValueChange:e=>{V(e)},children:s.jsx(y.dr,{children:eJ.map((e,o)=>s.jsx(y.SP,{value:e.title,children:(0,p.V9)(e,C.FV)},e.title))})}),s.jsx("div",{className:"mt-4 flex flex-col gap-4",children:k.customisedLogBookComponents?.nodes.sort((e,o)=>"CrewMembers_LogBookComponent"===e.componentClass?-1:"CrewMembers_LogBookComponent"===o.componentClass?1:0).map(e=>s.jsxs("div",{className:`border rounded-lg grid grid-cols-3 border-border ${eX(e)?"":"hidden"} ${e.label}`,children:[s.jsxs("div",{className:"flex bg-background border-b border-border flex-row md:flex-col items-center md:items-start justify-between gap-4 col-span-3 md:col-span-1 p-4",children:[s.jsxs("div",{className:"text-xl font-medium flex items-center gap-2",children:[p.V9(e,C.FV)," ",e.active?"":s.jsx(N,{className:"text-destructive"})]}),!p.N0(e,ek,k)&&!ex&&s.jsx("div",{className:"flex gap-2",children:e.active?s.jsxs(s.Fragment,{children:[s.jsx(y.zx,{onClick:eO(e.id),iconLeft:N,variant:"destructive",children:"Disable"}),s.jsx(y.zx,{iconLeft:L,variant:"outline",onClick:()=>eT(e),children:"Reset Default"})]}):s.jsx(y.zx,{variant:"primary",iconLeft:B.Z,onClick:eM(e.id),children:"Enable"})})]}),p.N0(e,ek,k)&&s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"md:col-span-2 hidden md:block bg-background border-b border-border"}),s.jsx("div",{className:"col-span-3 p-4 overflow-x-auto",children:es&&s.jsx(y.mQ,{value:eo,onValueChange:e=>{et(e)},children:s.jsx(y.dr,{children:es.map(e=>s.jsx(y.SP,{value:e,children:"Engine Checks"!==e?e:"Engine, steering, electrical & alt power"},e))})})}),ei&&s.jsx("div",{className:T.cn("col-span-3 p-4",!p.Bo(eo,3,ek)&&"hidden"),children:s.jsx(y.mQ,{value:ea,onValueChange:e=>en(e),children:s.jsx(y.dr,{children:ei.map(e=>s.jsx(y.SP,{value:e.label,children:e.label},e.label))})})}),es&&es.map((o,t)=>s.jsxs("div",{className:T.cn("flex border-b border-border border-r border-t justify-between bg-background",p.Bo(eo,3,ek)?"col-span-3 flex-row items-center":"col-span-3 md:col-span-1 flex-row md:flex-col items-center md:items-start",eo!==o&&"hidden"),children:[s.jsxs("div",{className:"p-4 font-medium text-xl",children:["Engine Checks"!==o?o:"Engine, steering, electrical & alt power"," ",e.active?"":s.jsx(N,{className:"text-destructive"})]}),"Engine Checks"!==o?s.jsx("div",{className:"mt-2 p-4",children:(null==e.subFields||e?.subFields&&e.subFields.split("||").includes(o))&&!ex?s.jsx(s.Fragment,{children:s.jsx(y.zx,{onClick:eZ(e,o),iconLeft:N,variant:"destructive",children:"Disable"})}):s.jsx(s.Fragment,{children:!ex&&s.jsx(s.Fragment,{children:s.jsx(y.zx,{onClick:eI(e,o),iconLeft:B.Z,variant:"primary",children:"Enable"})})})}):s.jsx("div",{className:"flex items-center pr-4",children:ei&&ei.map(o=>o.label===ea&&s.jsx("div",{children:(null==e.subFields||e?.subFields&&e.subFields.split("||").includes(o.label))&&!ex?s.jsx(s.Fragment,{children:s.jsx(y.zx,{onClick:ez(e,o),iconLeft:N,variant:"destructive",children:"Disable"})}):s.jsx(s.Fragment,{children:!ex&&s.jsx(s.Fragment,{children:s.jsx(y.zx,{onClick:eP(e,o),iconLeft:B.Z,variant:"primary",children:"Enable"})})})},o.label))})]},t))]}),s.jsxs("div",{className:T.cn("col-span-3 md:col-span-2 border-b border-t border-border","Pre-Departure Checks"===$&&p.Bo(eo,3,ek)&&"hidden"),children:[s.jsx(_.xc,{className:"justify-start"}),eK(e).map((o,t)=>s.jsx("div",{className:`${p.N0(e,ek,k)?eo===p.n3(o,ek,$)&&eG(o)?"":"hidden":""}`,children:s.jsxs(_.yE,{children:[s.jsx(P,{title:o?.customisedFieldTitle?o.customisedFieldTitle:p.Ts(o,ek),field:o,isRequired:eR(o),className:T.cn(p.No(e,$?.replace("Logbook","LogBook"),eo,ei,ea),!e.active&&"pointer-events-none opacity-50",`field-${p.n3(o,ek,$)}`),isDisabled:ex||eR(o),value:p.V6(o,M,I)?"no":"yes",onUpdateField:(e,o)=>eF(e,o),onDescriptionClick:(e,o)=>ej.openDialog(e,o),onCustomizeClick:e=>e0(e)}),o?.fieldType==="files"||p.dO(o,ek,$?.replace("Logbook","LogBook"))&&s.jsxs("div",{className:T.cn("px-4",!e.active&&"pointer-events-none opacity-50",p.n3(o,ek,$?.replace("Logbook","LogBook")),p.N0(e,ek,k)&&eo!==o.fieldSet&&"hidden"),children:[s.jsx("div",{}),s.jsx("div",{className:"colspan-3",children:s.jsx(R,{documents:eu,setDocuments:em,fileUploadText:"Policies"})})]})]})},o.id)),p.EJ(e,ek,k).filter(e=>eH(e)).map((o,l)=>s.jsxs("div",{className:`${p.ZU(o,ek,$?.replace("Logbook","LogBook"))} ${p.N0(e,ek,k)?eo===(o?.fieldSet?o.fieldSet:"Other")&&eG(o)?"":"hidden":""}`,children:[s.jsx(P,{title:p.Ts(o,ek),field:o,isRequired:eR({...o,localID:l,customisedLogBookComponentID:e.id,status:"Required"}),isDisabled:ex||eR({...o,localID:l,customisedLogBookComponentID:e.id,status:"Required"}),value:void 0,onUpdateField:(o,t)=>eF({...o,customisedLogBookComponentID:e.id},t),className:`${p.No(e,$?.replace("Logbook","LogBook"),eo,ei,ea)} ${e.active?"":"pointer-events-none opacity-50"} ${p.n3(o,ek,$?.replace("Logbook","LogBook"))}`,onCustomizeClick:()=>{t({title:"Warning!",description:'You can rename this field after setting its value to either "Yes" or "No."',variant:"destructive"})}}),o?.fieldType==="files"&&s.jsxs("div",{className:` px-4 ${e.active?"":"pointer-events-none opacity-50"} ${p.n3(o,ek,$?.replace("Logbook","LogBook"))} ${p.N0(e,ek,k)?eo===o.fieldSet?"":"hidden":""}`,children:[s.jsx("div",{}),s.jsx("div",{className:"colspan-3",children:s.jsx(R,{documents:eu,setDocuments:em,fileUploadText:"Policies",showFileUpload:!ex})})]})]},o.value+"_"+l))]}),s.jsx("div",{className:"col-span-3",children:eU(e.customisedComponentFields.nodes.filter((e,o,t)=>t.findIndex(o=>o.fieldName===e.fieldName)===o),e.componentClass).map((o,l)=>s.jsxs("div",{className:`${p.No(e,$?.replace("Logbook","LogBook"),eo,ei,ea)} ${p.N0(e,ek,k)?eo===p.n3(o,ek,$?.replace("Logbook","LogBook"))&&eW(o)?"":"hidden":""} border rounded-lg m-4 border-border`,children:[s.jsxs("div",{className:`${e.active?"":"pointer-events-none opacity-50"} field-${p.n3(o,ek,$?.replace("Logbook","LogBook"))}`,children:[s.jsx(U,{className:"border-b border-border p-3 bg-background",title:o?.customisedFieldTitle?o.customisedFieldTitle:p.Ts(o,ek),value:o?.id>0?p.V6({...o,localID:l,customisedLogBookComponentID:e.id},M,I)?"no":"yes":void 0,field:o,isDisabled:ex,onUpdateField:(o,t)=>{eF({...o,customisedLogBookComponentID:e.id},t)},onCustomizeClick:e=>{e?.id>0?e0(e):t({title:"Warning!",description:'You can rename this group after setting its value to either "Yes" or "No."',variant:"destructive"})},onDescriptionClick:(e,o)=>ej.openDialog(e,o)}),s.jsxs("div",{className:"",children:[s.jsx(_.xc,{className:"justify-start"}),s.jsx("div",{className:`md:col-span-2 ${p.V6({...o,localID:l,customisedLogBookComponentID:e.id},M,I)?"pointer-events-none opacity-50":""}`,children:p.sU(o.fieldName,ek,$?.replace("Logbook","LogBook"),k).map((o,l)=>s.jsx(P,{title:o?.customisedFieldTitle?o.customisedFieldTitle:p.Ts(o,ek),isRequired:eR(o),field:o,className:`${o?.id} ${e.active?"":"pointer-events-none opacity-50 77"} ${p.n3(o,ek,$?.replace("Logbook","LogBook"))}`,isDisabled:eR(o)||ex,value:o?.id>0?p.V6(o,M,I)?"no":"yes":void 0,onUpdateField:(e,o)=>eF(e,o),onDescriptionClick:(e,o)=>ej.openDialog(e,o),onCustomizeClick:e=>{e?.id>0?e0(e):t({description:'You can rename this group after setting its value to either "Yes" or "No."',title:"Warning",variant:"destructive"})}},o.value+"_"+l))})]})]}),o?.fieldType==="files"||p.dO(o,ek,$)&&s.jsxs("div",{className:` px-4 ${e.active?"":"pointer-events-none opacity-50"} ${p.n3(o,ek,$?.replace("Logbook","LogBook"))} ${p.N0(e,ek,k)?eo===o.fieldSet?"":"hidden":""}`,children:[s.jsx("div",{}),s.jsx("div",{className:"colspan-3",children:s.jsx(R,{documents:eu,setDocuments:em,fileUploadText:"Policies"})})]})]},o.id))})]},e.id))}),k&&s.jsx(S,{tab:$,logBookConfig:k,slallFields:ek,fields:eQ,imCrew:ex,createCustomisedComponent:eq})]})})]}),(0,s.jsxs)(j.V,{children:[s.jsx(y.zx,{iconLeft:F.Z,variant:"back",onClick:()=>{f.back()},children:"Cancel"}),!g&&W&&k.customisedLogBookComponents?.nodes?.length>0&&k.customisedLogBookComponents?.nodes?.map(e=>s.jsx("div",{className:`${$?.replace("Logbook","LogBook")===e.title.replace("Logbook","LogBook")?"":"hidden"}`,children:p.N0(e,ek,k)&&s.jsx(s.Fragment,{children:e.active?s.jsx(s.Fragment,{children:!ex&&s.jsxs(s.Fragment,{children:[s.jsx(y.zx,{className:"mr-2",variant:"destructive",iconLeft:N,onClick:eO(e.id),children:"Disable"}),s.jsx(y.zx,{variant:"outline",iconLeft:L,onClick:()=>eT(e),children:"Reset to default"})]})}):s.jsx(s.Fragment,{children:!ex&&s.jsx(y.zx,{variant:"primary",onClick:eM(e.id),children:"Enable"})})})},e.id)),!g&&W&&k&&eQ.map(e=>s.jsx("div",{className:`${$?.replace("Logbook","LogBook")===e.label.replace("Logbook","LogBook")?"":"hidden"}`,children:!ex&&s.jsx(y.zx,{variant:"primaryOutline",iconLeft:w,onClick:()=>{document.body.style.cursor="wait",eq({variables:{input:{title:e.label,sortOrder:e.sortOrder||0,category:e.category,customisedLogBookConfigID:k.id,componentClass:e.componentClass,active:!0}}})},children:`Add ${e.label}`})},e.label)),!ex&&s.jsx(y.zx,{variant:"primary",onClick:()=>e$(),iconLeft:D.Z,disabled:ep,children:ep?"Saving":"Save"})]}),s.jsx(A,{title:ey.title,field:ey.selectedField,isFieldGroup:ey.isFieldGroup,defaultValues:ey.form,isOpen:ey.isOpen,onOpenChange:ey.onOpenChange,onSave:(e,o)=>eE(e,o)}),s.jsx(G,{open:ej.open,onOpenChange:ej.onOpenChange,title:ej.title,content:ej.content})]})}let J=()=>{let e=(0,u.useSearchParams)(),o=e.get("logBookID")??0,t=e.get("vesselID")??0;return s.jsx(W,{logBookID:+o,vesselID:+t})}},68648:(e,o,t)=>{"use strict";t.d(o,{Cz:()=>v,Mw:()=>k,a3:()=>C,eN:()=>b,xc:()=>y,yE:()=>j});var s=t(98768),l=t(60343),i=t.n(l),r=t(39544),a=t(81515),n=t(49581),d=t(58830),c=t(67537),u=t(52269),m=t(57103),p=t(70906),f=t(56937),g=t(74602),h=t(69852),x=t(75776);let v=i().forwardRef(({className:e,...o},t)=>s.jsx("div",{ref:t,className:(0,f.cn)("h-fit bg-card",e),...o}));v.displayName="CheckField";let b=i().forwardRef(({className:e,...o},t)=>s.jsx("div",{ref:t,className:(0,f.cn)("flex flex-col space-y-1.5 pb-2.5",e),...o}));b.displayName="CheckFieldHeader";let C=i().forwardRef(({className:e,children:o,...t},l)=>s.jsx(g.H4,{ref:l,className:(0,f.cn)("",e),...t,children:o}));C.displayName="CheckFieldTitle";let y=i().forwardRef(({className:e,...o},t)=>(0,s.jsxs)("div",{ref:t,className:(0,f.cn)("text-input font-medium border-b border-border flex justify-end",e),...o,children:[s.jsx("div",{className:"flex w-12 h-10 rounded-t-md text-destructive justify-center items-center",children:"No"}),s.jsx("div",{className:"flex w-12 h-10 rounded-t-md text-bright-turquoise-600 text justify-center items-center",children:"Yes"})]}));y.displayName="CheckFieldTopContent";let j=i().forwardRef(({className:e,...o},t)=>s.jsx("div",{ref:t,className:(0,f.cn)("pt-0",e),...o}));function k({locked:e=!1,displayField:o=!0,displayLabel:t="",displayDescription:i="",descriptionType:v,inputId:b,handleNoChange:C,handleYesChange:y,defaultNoChecked:j=!1,defaultYesChecked:k=!1,comment:N="",setDescriptionPanelContent:L,setOpenDescriptionPanel:B,setDescriptionPanelHeading:F,className:w="",innerWrapperClassName:D="",onCommentSave:_,onCommentDelete:E,commentAction:T,offline:S=!1,fieldId:M,onError:O,disabled:I=!1,required:Z=!1,hideCommentButton:P=!1,displayImage:z=!1,fieldImages:q=!1,onImageUpload:R,sectionData:$={id:0,sectionName:"logBookEntryID"},...V}){let[A,U]=(0,l.useState)(k),[Y,H]=(0,l.useState)(j),[G,W]=(0,l.useState)(!1),[J,Q]=(0,l.useState)(N),[X,K]=(0,l.useState)(N),[ee,eo]=(0,l.useState)(!1),[et,es]=(0,l.useState)(!1);(0,l.useRef)(!0);let el=e||I,[ei,er]=(0,l.useState)(!1),[ea,en]=(0,l.useState)(""),[ed,ec]=(0,l.useState)(""),eu=async()=>{if(!el){eo(!0),es(!1);try{if(Q(X),_){let e=_(X);e instanceof Promise&&await e}W(!1)}catch(e){console.error("Error saving comment:",e),es(!0),O&&e instanceof Error&&O(e)}finally{eo(!1)}}},em=async()=>{if(!el){eo(!0),es(!1);try{if(E)try{let e=E();e instanceof Promise&&await e}catch(e){console.error("Error in onCommentDelete callback:",e)}Q(""),K(""),W(!1)}catch(e){console.error("Error deleting comment:",e),es(!0),O&&e instanceof Error&&O(e)}finally{eo(!1)}}};return o?(console.log($,z),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:(0,f.cn)("flex gap-2.5 min-h-16 !mt-0 border-b border-border ",D),children:[(0,s.jsxs)("div",{className:"flex-1 flex flex-col phablet:flex-row pt-2.5 phablet:pt-0 phablet:gap-2.5 items-end phablet:items-center",children:[(0,s.jsxs)(g.P,{className:"text-card-foreground text-base w-full",children:[t,Z&&s.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-5 py-2.5",children:[z&&s.jsx(x.Z,{file:!!(q&&Array.isArray(q))&&q.filter(e=>e.fieldName===b).sort((e,o)=>o.id-e.id),setFile:R,inputId:b,sectionData:$}),i&&s.jsx(r.Button,{variant:"ghost",size:"icon",iconOnly:!0,title:"View description",iconLeft:s.jsx(a.Z,{className:"text-light-blue-vivid-900 fill-light-blue-vivid-50",size:24}),onClick:()=>{L&&B&&F&&(L(i),B(!0),F(t)),en(i),er(!0),ec(t)}}),!P&&s.jsx(r.Button,{variant:"ghost",size:"icon",iconOnly:!0,title:"Add comment",className:"group",iconLeft:J?s.jsx(n.Z,{className:(0,f.cn)("text-curious-blue-400"),size:24}):s.jsx(d.Z,{className:(0,f.cn)("text-neutral-400 group-hover:text-neutral-400/50","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300"),size:24}),onClick:()=>{T?T():W(!0)}})]})]}),(0,s.jsxs)(p.Ee,{...V,variant:"horizontal",className:(0,f.cn)({"opacity-60":el}),gap:"none",value:A?"yes":Y?"no":"",onValueChange:e=>{el||("yes"===e?function(){if(!el)try{y(M),U(!0),H(!1)}catch(e){console.error("Error handling Yes change:",e),O&&e instanceof Error&&O(e)}}():"no"!==e||function(){if(!el)try{C(M),H(!0),U(!1)}catch(e){console.error("Error handling No change:",e),O&&e instanceof Error&&O(e)}}())},disabled:el,children:[s.jsx("div",{className:(0,f.cn)("flex w-12 bg-destructive-foreground justify-center phablet:p-0 items-center"),children:s.jsx(p.mJ,{value:"no",id:`${b}-no_radio`,variant:"destructive",size:"lg"})}),s.jsx("div",{className:(0,f.cn)("flex w-12 bg-bright-turquoise-100 justify-center items-center "),children:s.jsx(p.mJ,{value:"yes",id:`${b}-yes_radio`,variant:"success",size:"lg"})})]})]}),s.jsx(h.Sheet,{open:ei,onOpenChange:er,children:(0,s.jsxs)(h.SheetContent,{side:"left",className:"w-[90%] sm:w-[540px] max-w-2xl",onInteractOutside:()=>{er(!1),en(""),ec("")},children:[s.jsx(h.SheetHeader,{children:s.jsx(h.SheetTitle,{children:ed})}),s.jsx(h.SheetBody,{children:"string"==typeof ea?s.jsx("div",{className:"prose prose-sm max-w-none leading-7",dangerouslySetInnerHTML:{__html:ea}}):s.jsx("div",{className:"prose prose-sm max-w-none leading-7",children:ea})})]})}),s.jsx(m.AlertDialogNew,{openDialog:G,setOpenDialog:W,title:J?"Edit comment":"Add comment",handleCreate:el?void 0:eu,handleDestructiveAction:!el&&J?em:void 0,showDestructiveAction:!el&&!!J,actionText:ee?"Saving...":"Save",destructiveActionText:"Delete",destructiveLoading:ee,noButton:el,cancelText:el?"Close":"Cancel",loading:ee,children:(0,s.jsxs)("div",{className:"flex flex-col",children:[et&&(0,s.jsxs)("div",{className:"text-destructive mb-2 text-sm",children:["Error ",J?"updating":"saving"," ","comment. Please try again."]}),s.jsx(u.Textarea,{id:`${b}-comment`,disabled:el||ee,rows:4,autoResize:!0,placeholder:"Comment",value:X,onChange:e=>K(e.target.value),className:(0,f.cn)("max-h-[60svh]",{"border-destructive":et})}),ee&&(0,s.jsxs)("div",{className:"flex items-center justify-center mt-2",children:[s.jsx(c.Z,{className:"h-4 w-4 animate-spin mr-2"}),s.jsx("span",{className:"text-sm",children:"Saving..."})]})]})})]})):null}j.displayName="CheckFieldContent",i().forwardRef(({className:e,...o},t)=>s.jsx("div",{ref:t,className:(0,f.cn)("flex items-center p-4 pt-0",e),...o})).displayName="CheckFieldFooter"},24894:(e,o,t)=>{"use strict";t.d(o,{Z:()=>g});var s=t(98768),l=t(60343),i=t(28147),r=t(79418),a=t(72548),n=t(67537),d=t(94060),c=t(76342),u=t(56937),m=t(71890),p=t(60797),f=t(25394);function g({setDocuments:e,text:o="Documents and Images",subText:t,bgClass:g="",documents:h,multipleUpload:x=!0}){let[v,b]=(0,l.useState)(!1),[C,y]=(0,l.useState)([]),[j,k]=(0,l.useState)(!1),[N,L]=(0,l.useState)(!1),B=(0,l.useRef)(null),F=(0,u.cn)("relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none",v?"bg-accent border-primary":"bg-accent/50 border-border","text-foreground hover:bg-accent hover:border-primary","min-h-[10rem] cursor-pointer select-none",g),w=async e=>{let o=new FormData;o.append("FileData",e,e.name.replace(/\s/g,""));try{let e=await fetch("https://api.sealogs.com/api/v2/upload",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("sl-jwt")}`},body:o}),t=await e.json();await D({variables:{id:[t[0].id]}}),L(!1)}catch(e){console.error(e)}},[D]=(0,r.t)(d.ZX,{fetchPolicy:"cache-and-network",onCompleted:e=>{y(o=>[...o,e.readFiles.nodes[0]]),k(!0)},onError:e=>console.error(e)}),[_]=(0,a.D)(c.RgS,{onCompleted:o=>{let t=o.updateFile;e(e=>x?[...e,t]:[t])},onError:e=>console.error(e)}),E=e=>{let o=Array.from(e);L(!0),o.forEach(w)},T=e=>o=>{o.preventDefault(),b(e)};return(0,s.jsxs)("div",{className:"w-full pt-4 lg:pt-0",children:[(0,s.jsxs)("form",{className:F,onSubmit:e=>e.preventDefault(),onDragEnter:T(!0),onDragOver:T(!0),onDragLeave:T(!1),onDrop:e=>{e.preventDefault(),b(!1),e.dataTransfer.files&&E(e.dataTransfer.files)},onClick:()=>B.current?.click(),"aria-label":"File uploader drop zone",children:[s.jsx("span",{className:"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider",children:o}),s.jsx(m.I,{ref:B,type:"file",className:"hidden",multiple:x,accept:".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf",onChange:e=>{e.target.files&&E(e.target.files)}}),(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2 pointer-events-none",children:[s.jsx(i.default,{src:"/sealogs-document_upload.svg",alt:"Upload illustration",width:96,height:96,className:"relative -translate-x-2.5",priority:!0}),t&&s.jsx("span",{className:"text-sm font-medium text-neutral-400",children:t})]})]}),N?(0,s.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2",children:[s.jsx(n.Z,{className:"h-5 w-5 animate-spin text-primary"}),s.jsx("span",{className:"text-sm text-muted-foreground",children:"Uploading..."})]}):s.jsx(f.h9,{openDialog:j,setOpenDialog:k,handleCreate:()=>{C.forEach((e,o)=>{let t=document.getElementById(`file-name-${o}`).value;_({variables:{input:{id:e.id,title:t}}})}),k(!1)},actionText:"Save",title:"File Name",children:s.jsx("div",{className:"space-y-4",children:C.map((e,o)=>s.jsx(p.Label,{label:`File ${o+1} Name`,htmlFor:`file-name-${o}`,children:s.jsx(m.I,{id:`file-name-${o}`,defaultValue:e.title,placeholder:"Enter file name"})},e.id))})})]})}},50058:(e,o,t)=>{"use strict";t.d(o,{k:()=>i});var s=t(9999);let l={tiny:"320px",small:"375px",standard:"430px",phablet:"480px","tablet-sm":"600px","tablet-md":"768px","tablet-lg":"834px",landscape:"1024px",laptop:"1280px",desktop:"1536px"};function i(e){let o={...l,...e},t={};return Object.keys(o).forEach(e=>{let l=o[e];t[e]=(0,s.a)(`(min-width: ${l})`)}),t}},42832:(e,o,t)=>{"use strict";t.r(o),t.d(o,{default:()=>s});let s=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\vessel\layout.tsx#default`)},42496:(e,o,t)=>{"use strict";t.r(o),t.d(o,{default:()=>s});let s=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\vessel\logbook-configuration\page.tsx#default`)},30854:()=>{},9999:(e,o,t)=>{"use strict";t.d(o,{a:()=>l});var s=t(60343);function l(e,o,{getInitialValueInEffect:t}={getInitialValueInEffect:!0}){let[l,i]=(0,s.useState)(!!t&&o);return(0,s.useRef)(null),l||!1}},8087:(e,o,t)=>{"use strict";t.d(o,{Z:()=>s});let s=(0,t(97428).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},49581:(e,o,t)=>{"use strict";t.d(o,{Z:()=>s});let s=(0,t(97428).Z)("MessageSquareText",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}],["path",{d:"M13 8H7",key:"14i4kc"}],["path",{d:"M17 12H7",key:"16if0g"}]])},58830:(e,o,t)=>{"use strict";t.d(o,{Z:()=>s});let s=(0,t(97428).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},18415:(e,o,t)=>{"use strict";t.d(o,{Z:()=>s});let s=(0,t(97428).Z)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])}};var o=require("../../../webpack-runtime.js");o.C(e);var t=e=>o(o.s=e),s=o.X(0,[864,8865,3563,6263,8189,9507,7602,2935,6451,4234,2925,5394,4837,6342,3842,5776],()=>t(91766));module.exports=s})();