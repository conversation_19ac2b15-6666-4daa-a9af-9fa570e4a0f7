exports.id=6129,exports.ids=[6129],exports.modules={897:(e,t,l)=>{Promise.resolve().then(l.bind(l,57710))},72017:(e,t,l)=>{"use strict";l.d(t,{Z:()=>i});var a=l(83179),n=l.n(a),s=l(86708);class r{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),l=t.id,a={...t,__typename:"FuelTanks",idbCRUD:"Update",idbCRUDDate:n()().format("YYYY-MM-DD HH:mm:ss")},r=await this.getById(l);return r?await s.Z.FuelTank.update(l,a):await s.Z.FuelTank.add(a),r=await this.getById(l),console.log("FuelTank save",e,r),r}catch(t){console.error("FuelTank save",e,t)}}async update(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t]));return await s.Z.FuelTank.update(t.id,{...t,idbCRUD:"Update",idbCRUDDate:n()().format("YYYY-MM-DD HH:mm:ss")}),console.log("FuelTank update",e,t),t}catch(t){console.error("FuelTank update",e,t)}}async getAll(){try{let e=await s.Z.FuelTank.toArray();return console.log("FuelTank getAll",e),e}catch(e){console.error("FuelTank getAll",e)}}async getById(e){try{let t=await s.Z.FuelTank.get(`${e}`);return console.log("FuelTank getById",e,t),t}catch(t){console.error("FuelTank getById",e,t)}}async getByIds(e){try{let t=await s.Z.FuelTank.where("id").anyOf(e).toArray();return console.log("FuelTank getByIds",e,t),t}catch(t){console.error("FuelTank getByIds",e,t)}}async bulkAdd(e){try{return await s.Z.FuelTank.bulkAdd(e),console.log("FuelTank bulkAdd",e),e}catch(t){if("BulkError"===t.name){let l=t.failuresByPos.map(e=>e.key),a=e.filter(e=>!l.includes(e.id));return await s.Z.FuelTank.bulkAdd(a),console.log("FuelTank bulkAdd::BulkError",e,t),e}console.error("FuelTank bulkAdd",e,t)}}async delete(e){try{let t=await s.Z.FuelTank.delete(e);return console.log("FuelTank delete",e,t),t}catch(t){console.error("FuelTank delete",e,t)}}async setProperty(e){try{if(e){let t=await s.Z.FuelTank.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=n()().format("YYYY-MM-DD HH:mm:ss"),await s.Z.FuelTank.update(e,t),console.log("FuelTank setProperty",e,t),t}}catch(t){console.error("FuelTank setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await s.Z.FuelTank.update(e.id,e)})),console.log("FuelTank multiUpdate",e)}catch(t){console.error("FuelTank multiUpdate",e,t)}}}let i=r},67468:(e,t,l)=>{"use strict";l.d(t,{default:()=>k});var a=l(98768),n=l(72548),s=l(60343),r=l(71241),i=l.n(r),o=l(7678),d=l.n(o),c=l(14826),u=l.n(c),m=l(69424),p=l(76342),h=l(66263),v=l(78965),g=l(13842),y=l(46776),b=l(71890),f=l(57103),x=l(17203),C=l(81311),j=l(74602),w=l(39544);let k=({crewDutyId:e,onCancel:t,onCreate:l,isPopup:r=!1})=>{let[o,c]=(0,s.useState)({}),k=(0,m.useRouter)(),[I,S]=(0,s.useState)(!1),[T,D]=(0,s.useState)({title:"",abbreviation:""}),[E,N]=(0,s.useState)(!1),[F,B]=(0,s.useState)(!1);(0,g.UL)(e,c);let O=i()((t,l)=>{c({...o,[t]:l,id:+e})},300),L=e=>{let{name:t,value:l}=e.target;O(t,l)},V=async()=>{let t=!1,l={title:"",abbreviation:""};if(d()(u()(o.title))&&(t=!0,l.title="Title is required"),d()(u()(o.abbreviation))&&(t=!0,l.abbreviation="Abbreviation is required"),t){S(!0),D(l);return}let a={input:{id:+o.id,title:o.title,abbreviation:o.abbreviation}};0===e?await P({variables:a}):await _({variables:a})},[P,{loading:A}]=(0,n.D)(p.fJx,{onCompleted:e=>{let t=e.createCrewDuty;t.id>0?l?l(t):k.back():console.error("mutationCreateCrewDuty error",e)},onError:e=>{console.error("mutationCreateCrewDuty error",e)}}),[_,{loading:M}]=(0,n.D)(p.Qem,{onCompleted:e=>{e.updateCrewDuty.id>0?k.back():console.error("mutationUpdateCrewDuty error",e)},onError:e=>{console.error("mutationUpdateCrewDuty error",e)}}),Z=async e=>{await U({variables:{ids:[e.id]}})},[U]=(0,n.D)(p.T5T,{onCompleted:()=>{k.push("/settings/crew-duty/list")},onError:e=>{console.error("mutationDeleteCrewDuty error",e)}});return(0,s.useEffect)(()=>{(0,y.UU)()},[]),(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:" flex justify-between pb-4 pt-3 items-center",children:(0,a.jsxs)(j.H3,{children:[0===e?"Create":"Edit"," Crew Duty"]})}),(0,a.jsxs)("div",{className:`${r?"grid-cols-2":"grid-cols-3"} grid gap-6`,children:[(0,a.jsxs)("div",{className:`${r?"hidden":""} my-4 `,children:["Crew duty details",a.jsx("p",{className:" mt-4 max-w-[25rem] leading-loose",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Esse minima maxime enim, consectetur hic est perferendis explicabo suscipit rem reprehenderit vitae ex sunt corrupti obcaecati aliquid natus et inventore tenetur?"})]}),(0,a.jsxs)("div",{className:"w-full grid col-span-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(b.I,{name:"title",type:"text",required:!0,defaultValue:o?.title||"",onChange:L,placeholder:"Duty Title"}),a.jsx("small",{className:"text-destructive",children:I&&T.title})]}),(0,a.jsxs)("div",{children:[a.jsx(b.I,{name:"abbreviation",type:"text",placeholder:"Abbreviation",required:!0,defaultValue:o?.abbreviation||"",onChange:L}),a.jsx("small",{className:"text-destructive",children:I&&T.abbreviation})]})]})]}),r?(0,a.jsxs)("div",{className:"flex justify-end pt-8 gap-2.5",children:[a.jsx(w.Button,{variant:"back",onClick:t,children:"Cancel"}),0!==e&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(w.Button,{variant:"destructive",onClick:()=>{B(!0)},children:"Delete"}),a.jsx(f.AlertDialogNew,{openDialog:F,setOpenDialog:B,title:"Delete Crew Duty",description:`Are you sure you want to delete "${o.title}"?`,variant:"danger",actionText:"Delete",handleCreate:()=>Z(o)})]}),a.jsx(w.Button,{onClick:V,disabled:A||M,children:`${0===e?"Create":"Update"} Duty`})]}):(0,a.jsxs)(v.V,{children:[a.jsx(h.default,{href:"/settings/crew-duty/list",children:a.jsx(w.Button,{variant:"back",iconLeft:x.Z,className:"mr-2",children:"Cancel"})}),0!==e&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(w.Button,{variant:"destructive",onClick:()=>{N(!0)},children:"Delete"}),a.jsx(f.AlertDialogNew,{openDialog:E,setOpenDialog:N,title:"Delete Crew Duty",description:`Are you sure you want to delete "${o.title}"?`,variant:"danger",actionText:"Delete",handleCreate:()=>Z(o)})]}),a.jsx(w.Button,{iconLeft:C.Z,onClick:V,disabled:A||M,children:`${0===e?"Create":"Update"} Duty`})]})]})}},64939:(e,t,l)=>{"use strict";l.d(t,{Z:()=>u});var a=l(98768),n=l(60343),s=l(7678),r=l.n(s),i=l(79418),o=l(94060),d=l(46776),c=l(81524);let u=({value:e=[],onChange:t,allDepartments:l})=>{let[s,u]=(0,n.useState)([]),[m,p]=(0,n.useState)([]),[h,v]=(0,n.useState)(!1),[g,y]=(0,n.useState)(!1),[b]=(0,i.t)(o.jl,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneSeaLogsMember;t&&y(t.departments.nodes)},onError:e=>{console.error("querySeaLogsMembers error",e)}});(0,n.useEffect)(()=>{b({variables:{filter:{id:{eq:+(localStorage.getItem("userId")??0)}}}})},[]);let f=e=>{e&&u(e?.map(e=>({value:e.id,label:e.title})))};return(0,n.useEffect)(()=>{r()(s)||r()(e)||p(e)},[e,s]),(0,n.useEffect)(()=>{f(l)},[]),a.jsx(c.Combobox,{value:m?m.map(e=>s.find(t=>t.value===e)):[],multi:!0,options:(0,d.GJ)()?s:g?s.filter(e=>g.some(t=>t.id===e.value)):s,onChange:e=>{t(e),p(e)},placeholder:"Select Department",isLoading:!s})}},10773:(e,t,l)=>{"use strict";l.d(t,{Z:()=>U});var a=l(98768),n=l(60343),s=l(69424),r=l(13842),i=l(44010),o=l(72548),d=l(79418),c=l(76342),u=l(94060),m=l(99161),p=l(64939),h=l(24894),v=l(46776),g=l(26100),y=l(7678),b=l.n(y),f=l(14826),x=l.n(f),C=l(70413),j=l(72017),w=l(71890),k=l(60797),I=l(81524),S=l(39544),T=l(8750),D=l(8416),E=l(78965),N=l(35024),F=l(74602),B=l(52269),O=l(29342),L=l(25394),V=l(52016),P=l(17203),A=l(36895),_=l(75546),M=l(82102),Z=l(93488);function U({vesselId:e}){let t=(0,s.useRouter)(),[l,y]=(0,n.useState)(!0),[f,U]=(0,n.useState)(e),[W,$]=(0,n.useState)({}),[H,q]=(0,n.useState)(),[Y,z]=(0,n.useState)([]),[R,G]=(0,n.useState)(),[J,K]=(0,n.useState)(),[Q,X]=(0,n.useState)(0),[ee,et]=(0,n.useState)(),[el,ea]=(0,n.useState)(0),[en,es]=(0,n.useState)(),[er,ei]=(0,n.useState)(0),[eo,ed]=(0,n.useState)(),[ec,eu]=(0,n.useState)(0),[em,ep]=(0,n.useState)(),[eh,ev]=(0,n.useState)(),[eg,ey]=(0,n.useState)(!1),[eb,ef]=(0,n.useState)(!1),[ex,eC]=(0,n.useState)(!1),[ej,ew]=(0,n.useState)(!1),[ek,eI]=(0,n.useState)(!1),[eS,eT]=(0,n.useState)(!1),[eD,eE]=(0,n.useState)({}),[eN,eF]=(0,n.useState)(!1),[eB,eO]=(0,n.useState)(!1),[eL,eV]=(0,n.useState)(),[eP,eA]=(0,n.useState)(!1),[e_,eM]=(0,n.useState)(),[eZ,eU]=(0,n.useState)(),[eW,e$]=(0,n.useState)(""),[eH,eq]=(0,n.useState)(null),[eY,ez]=(0,n.useState)([]),[eR,eG]=(0,n.useState)(!1),[eJ,eK]=(0,n.useState)(!1),[eQ,eX]=(0,n.useState)(!1),[e0,e1]=(0,n.useState)("Icon"),[e5,e2]=(0,n.useState)(!1),[e9,e6]=(0,n.useState)(),[e4,e8]=(0,n.useState)(),[e7,e3]=(0,n.useState)(),[te,tt]=(0,n.useState)([]),[tl,ta]=(0,n.useState)([]),[tn,ts]=(0,n.useState)(!1),tr=new C.Z,[ti,to]=(0,n.useState)(!1),[td,tc]=(0,n.useState)(!1),[tu,tm]=(0,n.useState)([]),[tp,th]=(0,n.useState)(),[tv,tg]=(0,n.useState)(),[ty,tb]=(0,n.useState)(0),tf=(0,Z.e)(),tx=new j.Z;(0,r.oA)(e=>{G([{label:" ---- Create Category ---- ",value:"newCategory"},...e?.filter(e=>null!==e.name).map(e=>({label:e.name,value:e.id}))])}),(0,r.E$)(e2);let[tC,{data:tj}]=(0,o.D)(c.YMB,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.createVessel;t.id>0&&t6({variables:{input:{id:t.id,icon:eQ?eQ.replaceAll(" ","-"):null,iconMode:e0,photoID:te.length>0?te[0].id:null}}})},onError:e=>{console.error("createVessel error",e)}}),tw=async()=>{let e=f;if(0===f){let t=document.getElementById("vessel-title").value||"New Vessel",l=document.getElementById("vessel-authNo").value,a=document.getElementById("vessel-mmsi").value,n=document.getElementById("vessel-transitId").value,s=eD.countryofoperation?eD.countryofoperation.value:W?.countryOfOperation,r=eD.vesseltype?eD.vesseltype.value||lc.value:W?.vesselType||lc.value;r=r.replaceAll("_"," ");let i=document.getElementById("vessel-description").value,o=document.getElementById("vessel-minCrew").value,d=document.getElementById("vessel-maxPax").value,c=document.getElementById("vessel-maxPOB").value,u=document.getElementById("vessel-callSign").value,{data:m}=await tC({variables:{input:{mmsi:a,registration:l,title:t,countryOfOperation:s,transitID:n,showOnDashboard:eN,displayLogbookComments:eB,vesselType:r,vesselTypeDescription:i,callSign:u,minCrew:+o,maxPax:+d,maxPOB:+c,vesselSpecificsID:0,seaLogsMembers:eh?.map(e=>e.value).join(",")}}});U(e=+m.createVessel.id)}return e},[tk]=(0,d.t)(u.l6,{fetchPolicy:"no-cache",onCompleted:e=>{K(e.readEngines.nodes)},onError:e=>{console.error("getEngines error",e)}}),tI=async e=>{await tk({variables:{id:e}})},[tS]=(0,d.t)(u.u2,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.readFuelTanks.nodes;t&&et(t=t.map(e=>({...e,fuelType:""===x()(e.fuelType?.replaceAll("_"," "))?null:e.fuelType?.replaceAll("_"," ")})))},onError:e=>{console.error("getFuelTanks error",e)}}),tT=async e=>{await tS({variables:{id:e}})},[tD]=(0,d.t)(u.QK,{fetchPolicy:"no-cache",onCompleted:e=>{es(e.readWaterTanks.nodes)},onError:e=>{console.error("getWaterTanks error",e)}}),tE=async e=>{await tD({variables:{id:e}})},[tN]=(0,d.t)(u.rO,{fetchPolicy:"no-cache",onCompleted:e=>{ed(e.readSewageSystems.nodes)},onError:e=>{console.error("getSewageSystems error",e)}}),tF=async e=>{await tN({variables:{id:e}})},tB=t=>{let l=0===e,a=t?.parentComponent_Components?.nodes.filter(e=>"Engine"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id),n=t?.parentComponent_Components?.nodes.filter(e=>"FuelTank"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id),s=t?.parentComponent_Components?.nodes.filter(e=>"WaterTank"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id),r=t?.parentComponent_Components?.nodes.filter(e=>"SewageSystem"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id);a.length>0&&tI(a),n.length>0&&tT(n),s.length>0&&tE(s),r.length>0&&tF(r),ev(t.seaLogsMembers.nodes.map(e=>({label:e?.firstName+" "+e.surname,value:e.id}))),e6(i.$Y.find(e=>e.value===t.countryOfOperation)),l&&e4?(e8(e4),eE({countryofoperation:t.countryOfOperation,vesseltype:e4,engine:t?.parentComponent_Components?.nodes.find(e=>"Engine"===e.basicComponent.componentCategory)?.basicComponent,fuelTank:t?.parentComponent_Components?.nodes.find(e=>"FuelTank"===e.basicComponent.componentCategory)?.basicComponent,waterTank:t?.parentComponent_Components?.nodes.find(e=>"WaterTank"===e.basicComponent.componentCategory)?.basicComponent,sewageSystem:t?.parentComponent_Components?.nodes.find(e=>"SewageSystem"===e.basicComponent.componentCategory)?.basicComponent})):(e8(lu.find(e=>e.value===t.vesselType)),eE({countryofoperation:t.countryOfOperation,vesseltype:t.vesselType||lc.value,engine:t?.parentComponent_Components?.nodes.find(e=>"Engine"===e.basicComponent.componentCategory)?.basicComponent,fuelTank:t?.parentComponent_Components?.nodes.find(e=>"FuelTank"===e.basicComponent.componentCategory)?.basicComponent,waterTank:t?.parentComponent_Components?.nodes.find(e=>"WaterTank"===e.basicComponent.componentCategory)?.basicComponent,sewageSystem:t?.parentComponent_Components?.nodes.find(e=>"SewageSystem"===e.basicComponent.componentCategory)?.basicComponent})),l&&!t?.vesselSpecifics?.dateOfBuild&&e_?eM(e_):eM(t?.vesselSpecifics?.dateOfBuild?new Date(t?.vesselSpecifics?.dateOfBuild):null),l&&W?.vesselSpecifics?$({...t,vesselSpecifics:{...t.vesselSpecifics,carriesDangerousGoods:W.vesselSpecifics.carriesDangerousGoods}}):$(t),eF(t.showOnDashboard),eO(t.displayLogbookComments),tm(t.departments?.nodes.map(e=>e.id)),t.bannerImageID&&lv({variables:{id:[t.bannerImageID]}}),t?.icon&&eX(t.icon.replaceAll("-"," ")),t?.iconMode&&e1(t.iconMode),t?.photoID>0&&tO(t.photoID),t?.bannerImageID>0&&eq(t.bannerImageID)},tO=async e=>{await tL({variables:{id:[e]}})},[tL]=(0,d.t)(u.ZX,{fetchPolicy:"cache-and-network",onCompleted:e=>{tt([e.readFiles.nodes[0]])},onError:e=>{console.error("queryFilesEntry error",e)}}),[tV]=(0,d.t)(u.rk,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneVessel;t&&tB(t)},onError:e=>{console.error("queryVesselInfo error",e)}}),tP=async()=>{await tV({variables:{id:+f}})},[tA]=(0,o.D)(c.MfI,{fetchPolicy:"no-cache",onCompleted:e=>{e.createParentComponent_Component.id>0&&tP()},onError:e=>{console.error("createParentComponent error",e)}}),t_=async e=>{let t=await tw();await tA({variables:{input:{parentComponentID:t,basicComponentID:e}}})};(0,r.td)(e=>{q([{label:"--- Create Inventory ---",value:"newInventory"},...e.filter(e=>e.vesselID<=0).filter(e=>!1==e.archived&&null!=e.title).map(e=>({label:e.title,value:e.id}))])});let tM=e=>{let t=W.departments?.nodes.flatMap(e=>e.id)??[];ep([{label:"--- Create Crew Member ---",value:"newCrewMember"},...e.filter(e=>!1==e.archived&&null!==e.firstName&&(0==e.departments.nodes.length||e.departments.nodes.some(e=>t.includes(e.id)))).map(e=>({label:e.firstName+" "+e?.surname,value:e.id}))])};(0,r.Ox)(e=>{ta(e),tM(e)}),(0,r.mJ)(e=>{tg(e.filter(e=>(0,v.GJ)()||"admin"!==e.code).map(e=>({label:e.title,value:e.id})))}),(0,r.GL)(f,t=>{e>0&&z(t.map(e=>({label:e.title,value:e.id})))});let[tZ]=(0,o.D)(c.UtL,{fetchPolicy:"no-cache",onCompleted:e=>{if(e.updateEngine.id>0){X(0),ey(!1);let e=W?.parentComponent_Components?.nodes?W?.parentComponent_Components?.nodes.filter(e=>"Engine"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id):[];e.length>0&&tI(e)}},onError:e=>{console.error("updateEngine error",e)}}),[tU]=(0,o.D)(c.apX,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.createEngine;if(t.id>0){ey(!1);let e=[t.id,...W?.parentComponent_Components?.nodes?W.parentComponent_Components.nodes.filter(e=>"Engine"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id):[]];e.length>0&&tI(e),t_(t.id)}},onError:e=>{console.error("createEngine error",e)}}),tW=async()=>{let t=document.getElementById("engine-title").value,l=eD.engine?eD.engine.type?.replaceAll("_"," "):"",a=document.getElementById("engine-power").value,n=eD.engine?eD.engine.driveType?.replaceAll("_"," "):"",s=eD.engine?eD.engine.positionOnVessel:"",r=document.getElementById("engine-hours").value,i=document.getElementById("engine-identified").value,o=document.getElementById("engine-make").value,d=document.getElementById("engine-model").value,c=document.getElementById("engine-kW").value,u=document.getElementById("engine-kVA").value;tr.delete(e),Q>0?await tZ({variables:{input:{id:Q,title:t,type:l,maxPower:a,driveType:n,positionOnVessel:s,currentHours:+r,identifier:i,componentCategory:"Engine",make:o,model:d,kW:c,kVA:u}}}):await tU({variables:{input:{title:t,type:l,maxPower:a,driveType:n,positionOnVessel:s,currentHours:+r,identifier:i,componentCategory:"Engine",make:o,model:d,kW:c,kVA:u}}})},[t$]=(0,o.D)(c.UtL,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.updateEngine;t.id>0&&(K(J.filter(e=>e.id!=t.id)),ts(!1),X(0),ey(!1))},onError:e=>{console.error("updateEngine error",e)}}),tH=async()=>{let t=document.getElementById("fuel-tank-title").value,l={title:t,safeFuelCapacity:+document.getElementById("fuel-tank-safeCapacity").value,capacity:+document.getElementById("fuel-tank-capacity").value,identifier:document.getElementById("fuel-tank-identified").value,fuelType:""===x()(eD.fuelTank?.fuelType)?null:eD.fuelTank?.fuelType,dipType:eD.fuelTank?eD.fuelTank.dipType:null,componentCategory:"FuelTank",currentLevel:+eD.fuelTank.currentLevel};el>0?(tx.update({id:el,...l}),tr.delete(e),await t0({variables:{input:{id:el,...l}}})):await tX({variables:{input:{...l}}})},[tq]=(0,o.D)(c.xrA,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.deleteFuelTanks;et(ee.filter(e=>!t.includes(e.id))),ea(0),ef(!1)},onError:e=>{console.error("deleteFuelTank error",e)}}),tY=async e=>{await tq({variables:{ids:[e]}})},[tz]=(0,o.D)(c.VyK,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.createWaterTank;if(t.id>0){eI(!1);let e=[t.id,...W?.parentComponent_Components?.nodes?W?.parentComponent_Components?.nodes?.filter(e=>"WaterTank"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id):[]];e.length>0&&tE(e),t_(t.id)}},onError:e=>{console.error("createWaterTank error",e)}}),tR=async()=>{let e=document.getElementById("water-tank-title").value,t=document.getElementById("water-tank-capacity").value,l=document.getElementById("water-tank-identified").value;er>0?await tJ({variables:{input:{id:er,title:e,capacity:+t,identifier:l,componentCategory:"WaterTank"}}}):await tz({variables:{input:{title:e,capacity:+t,identifier:l,componentCategory:"WaterTank"}}})},tG=async()=>{let e=document.getElementById("sewage-system-title").value,t=document.getElementById("sewage-system-capacity").value,l=document.getElementById("sewage-system-identified").value,a=document.getElementById("sewage-system-numberOfTanks").value;ec>0?await tQ({variables:{input:{id:ec,title:e,capacity:+t,identifier:l,numberOfTanks:+a,componentCategory:"SewageSystem"}}}):await tK({variables:{input:{title:e,capacity:+t,identifier:l,numberOfTanks:+a,componentCategory:"SewageSystem"}}})},[tJ]=(0,o.D)(c.HBr,{fetchPolicy:"no-cache",onCompleted:e=>{if(e.updateWaterTank.id>0){ei(0),eI(!1);let e=W?.parentComponent_Components?.nodes?W?.parentComponent_Components?.nodes.filter(e=>"WaterTank"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id):[];e.length>0&&tE(e)}},onError:e=>{console.error("updateWaterTank error",e)}}),[tK]=(0,o.D)(c.UO3,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.createSewageSystem;if(t.id>0){eT(!1);let e=[t.id,...W?.parentComponent_Components?.nodes?W?.parentComponent_Components?.nodes.filter(e=>"SewageSystem"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id):[]];e.length>0&&tF(e),t_(t.id)}},onError:e=>{console.error("createSewageSystem error",e)}}),[tQ]=(0,o.D)(c.rRz,{fetchPolicy:"no-cache",onCompleted:e=>{if(e.updateSewageSystem.id>0){eu(0),eT(!1);let e=W?.parentComponent_Components?.nodes?W?.parentComponent_Components?.nodes.filter(e=>"SewageSystem"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id):[];e.length>0&&tF(e)}},onError:e=>{console.error("updateSewageSystem error",e)}}),[tX]=(0,o.D)(c.ooB,{fetchPolicy:"no-cache",onCompleted:t=>{let l=t.createFuelTank,a=document.getElementById("fuel-tank-title").value,n=document.getElementById("fuel-tank-safeCapacity").value,s=document.getElementById("fuel-tank-capacity").value,r=document.getElementById("fuel-tank-identified").value,i={id:l.id,title:a,safeFuelCapacity:+n,capacity:+s,identifier:r,fuelType:""===x()(eD.fuelTank?.fuelType)?null:eD.fuelTank?.fuelType,dipType:eD.fuelTank?eD.fuelTank.dipType:null,componentCategory:"FuelTank",currentLevel:+eD.fuelTank.currentLevel};if(tx.save(i),tr.delete(e),l.id>0){ef(!1);let e=[l.id,...W?.parentComponent_Components?.nodes?W?.parentComponent_Components?.nodes.filter(e=>"FuelTank"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id):[]];e.length>0&&tT(e),t_(l.id)}},onError:e=>{console.error("createFuelTank error",e)}}),[t0]=(0,o.D)(c.wMh,{fetchPolicy:"no-cache",onCompleted:e=>{if(e.updateFuelTank.id>0){ea(0),ef(!1);let e=W?.parentComponent_Components?.nodes?W?.parentComponent_Components?.nodes.filter(e=>"FuelTank"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id):[];e.length>0&&tT(e)}},onError:e=>{console.error("updateFuelTank error",e)}}),t1=async(e=0)=>{let t=document.getElementById("vessel-title").value,l=document.getElementById("vessel-authNo").value,a=document.getElementById("vessel-mmsi").value,n=document.getElementById("vessel-transitId").value,s=eD.countryofoperation?eD.countryofoperation.value:W?.countryOfOperation,r=eD.vesseltype?eD.vesseltype.value||lc.value:W?.vesselType||lc.value;r=r.replaceAll("_"," ");let i=document.getElementById("vessel-description").value,o=document.getElementById("vessel-minCrew").value,d=document.getElementById("vessel-maxPax").value,c=document.getElementById("vessel-maxPOB").value,u=document.getElementById("vessel-callSign").value,m=document.getElementById("vessel-beam").value,p=document.getElementById("vessel-overallLength").value,h=document.getElementById("vessel-draft").value,v=document.getElementById("vessel-hullConstruction").value,g=document.getElementById("vessel-hullColor").value,y=document.getElementById("vessel-primaryHarbour").value,b=await tw();e>0&&await t9({variables:{input:{id:b,mmsi:a,registration:l,title:t,countryOfOperation:s,transitID:n,showOnDashboard:eN,displayLogbookComments:eB,vesselType:r,vesselTypeDescription:i,callSign:u,minCrew:+o,maxPax:+d,maxPOB:+c,vesselSpecificsID:e,seaLogsMembers:eh?.map(e=>e.value).join(","),departments:tu.join(","),bannerImageID:eH}}}),0==e&&(W?.vesselSpecifics?.id>0?await t2({variables:{input:{id:W?.vesselSpecifics.id,beam:m,overallLength:p,dateOfBuild:(0,_.vq)(e_),draft:h,carriesDangerousGoods:W?.vesselSpecifics?.carriesDangerousGoods||!1,carriesVehicles:W?.vesselSpecifics?.carriesVehicles||!1,hullConstruction:v,hullColor:g,primaryHarbour:y}}}):await t5({variables:{input:{beam:m,overallLength:p,dateOfBuild:(0,_.vq)(e_),draft:h,hullConstruction:v,hullColor:g,carriesDangerousGoods:W?.vesselSpecifics?.carriesDangerousGoods||!1,carriesVehicles:W?.vesselSpecifics?.carriesVehicles||!1,primaryHarbour:y,vesselID:b}}}))},[t5]=(0,o.D)(c.xoj,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.createVesselSpecifics;t.id>0&&t1(t.id)},onError:e=>{console.error("createVesselSpecifics error",e)}}),[t2]=(0,o.D)(c.H3E,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.updateVesselSpecifics;t.id>0&&(tr.delete(t.id),t1(t.id))},onError:e=>{console.error("updateVesselSpecifics error",e)}}),[t9]=(0,o.D)(c.FxW,{onCompleted:l=>{let a=l.updateVessel;a.id>0&&(tr.delete(a.id),t.push(`/vessel/info?id=${e>0?e:a.id}`))},onError:e=>{console.error("updateVessel error",e)}}),[t6]=(0,o.D)(c.FxW,{onCompleted:e=>{let t=e.updateVessel;t.id>0&&tr.delete(t.id)},onError:e=>{console.error("updateVessel error",e)}}),t4=async()=>{if((0,v.GJ)()){let e=await tw();await t8({variables:{input:{id:e,archived:!0}}})}else alert("You do not have permission to archive vessel")},[t8]=(0,o.D)(c.FxW,{fetchPolicy:"no-cache",onCompleted:e=>{let l=e.updateVessel;l.id>0&&(tr.delete(l.id),t.push("/vessel"))},onError:e=>{console.error("archiveVessel error",e)}}),t7=e=>{X(e),eE({...eD,engine:J.find(t=>t.id==e)})},t3=e=>{ea(e),eE({...eD,fuelTank:ee.find(t=>t.id==e)})},le=e=>{ei(e),eE({...eD,waterTank:en.find(t=>t.id==e)})},lt=e=>{eu(e),eE({...eD,sewageSystem:eo.find(t=>t.id==e)})},ll=async e=>{let t=await tw();await ln({variables:{input:{id:e,vesselID:t}}})},la=async e=>{await ln({variables:{input:{id:e,vesselID:null}}})},[ln]=(0,o.D)(c.HcK,{fetchPolicy:"no-cache",onError:e=>{console.error("updateInventory error",e)}}),ls=async()=>{let e=await tw(),t={input:{item:document.getElementById("inventory-item").value?document.getElementById("inventory-item").value:null,description:document.getElementById("inventory-short-description").value?document.getElementById("inventory-short-description").value:null,quantity:document.getElementById("inventory-qty").value?parseInt(document.getElementById("inventory-qty").value):null,productCode:document.getElementById("inventory-code").value?document.getElementById("inventory-code").value:null,costingDetails:document.getElementById("inventory-cost").value?document.getElementById("inventory-cost").value:null,location:document.getElementById("inventory-location").value?document.getElementById("inventory-location").value:null,categories:eD?.inventory?.category?eD.inventory.category:null,vesselID:e}};await lr({variables:t})},[lr]=(0,o.D)(c._Pq,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.createInventory;t.id>0&&(eC(!1),q([...H,{label:t.item,value:t.id}]),z([...Y,{label:t.item,value:t.id}]))},onError:e=>{console.error("createInventory error",e)}}),li=()=>{th(null),tb(0),["crew-firstName","crew-surname","crew-username","crew-password","crew-email","crew-phoneNumber"].forEach(e=>{let t=document.getElementById(e);t&&(t.value="")})},lo=async()=>{let e=await tw(),t={input:{firstName:document.getElementById("crew-firstName").value?document.getElementById("crew-firstName").value:null,surname:document.getElementById("crew-surname").value?document.getElementById("crew-surname").value:null,email:document.getElementById("crew-email").value?document.getElementById("crew-email").value:null,phoneNumber:document.getElementById("crew-phoneNumber").value?document.getElementById("crew-phoneNumber").value:null,username:document.getElementById("crew-username").value?document.getElementById("crew-username").value:null,password:document.getElementById("crew-password").value?document.getElementById("crew-password").value:null,primaryDutyID:ty>0?ty:null,groups:tp?[tp.value].join(","):null,vehicles:[e].join(",")}};await ld({variables:t})},[ld]=(0,o.D)(c.qK0,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.createSeaLogsMember;t.id>0&&(ew(!1),li(),ep([...em,{label:t.firstName+" "+t.surname,value:t.id}]),ev([...eh,{label:t.firstName+" "+t.surname,value:t.id}]),eA(!1))},onError:e=>{console.error("createUser error",e.message),eA(e)}}),lc={value:"Recreation"},lu=[{label:"SLALL",value:"SLALL"},{label:"Rescue Vessel",value:"Rescue_Vessel"},{label:"Tug Boat",value:"Tug_Boat"},{label:"Pilot Vessel",value:"Pilot_Vessel"},{label:"Recreation",value:"Recreation"},{label:"Passenger Ferry",value:"Passenger_Ferry"},{label:"Water Taxi",value:"Water_Taxi"},{label:"Sailing Vessel",value:"Sailing_Vessel"},{label:"Large Motor Yacht",value:"Large_Motor_Yacht"},{label:"JetBoat",value:"JetBoat"}].filter(t=>0!==e||"SLALL"!==t.value),lm=async()=>{await lp({variables:{input:{name:document.getElementById("inventory-category-title").value,abbreviation:document.getElementById("inventory-category-abbreviation").value}}})},[lp]=(0,o.D)(c.CQz,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.createInventoryCategory;t.id>0&&(to(!1),G([...R,{label:t.name,value:t.id}]),eE({...eD,inventory:{...eD.inventory,category:t.id}}),eV({label:t.name,value:t.id}))},onError:e=>{console.error("createInventoryCategory error",e)}});async function lh(e){let t=new FormData;t.append("FileData",e,e.name.replace(/\s/g,""));try{let e=await fetch("https://api.sealogs.com/api/v2/upload",{method:"POST",headers:{Authorization:"Bearer "+localStorage.getItem("sl-jwt")},body:t}),l=await e.json();e$(l[0].location),eq(l[0].id),eG(!1)}catch(e){console.error(e),eG(!1)}}let[lv,{data:lg,loading:ly}]=(0,d.t)(u.ZX,{fetchPolicy:"cache-and-network",onCompleted:e=>{e$("https://api.sealogs.com/assets/"+e.readFiles.nodes[0].fileFilename)},onError:e=>{console.error(e)}}),[lb,lf]=(0,n.useState)(!1);if(!lb||!(0,v.Fs)("VIEW_VESSEL",lb))return lb?a.jsx(g.Z,{errorMessage:"Oops You do not have the permission to view this section."}):a.jsx(g.Z,{});let lx=!lb||!(0,v.Fs)("EDIT_VESSEL",lb);return(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx(L.Bu,{title:e>0?`Updating Vessel: ${W?.title}`:"Creating new vessel"}),(0,a.jsxs)(N.Zb,{className:"mx-2.5",children:[(0,a.jsxs)(N.Ol,{children:[a.jsx(F.H4,{children:"Vessel Details"}),a.jsx(F.P,{children:"Record details such as the vessel's name, size, registration, etc. Make sure everything listed is correct and kept up-to-date. These fields can be exported with reports for the likes of survey reports and certification documents."})]}),a.jsx(N.aY,{className:"space-y-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-5",children:[a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx(k.Label,{htmlFor:"display-dashboard",label:"Display on dashboard",size:"lg",leftContent:a.jsx(T.Checkbox,{id:"display-dashboard",checked:eN,onCheckedChange:e=>{eF(e)},isRadioStyle:!0,disabled:lx,size:"lg"})})}),a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx(k.Label,{htmlFor:"display-logbook-comments",label:"Display previous logbook comments",size:"lg",leftContent:a.jsx(T.Checkbox,{id:"display-logbook-comments",checked:eB,onCheckedChange:e=>{eO(e)},isRadioStyle:!0,disabled:lx,size:"lg"})})})]}),(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Vessel Title",htmlFor:"vessel-title",children:a.jsx(w.I,{readOnly:lx,id:"vessel-title",type:"text",placeholder:"Vessel Title",defaultValue:W?.title})}),a.jsx(k.Label,{label:"Country of Operation",htmlFor:"country-operation",children:a.jsx(I.Combobox,{className:"w-full",options:i.$Y,value:e9,onChange:e=>{eE({...eD,countryofoperation:e}),e6(e)},title:"Country of Operation",placeholder:"Country of Operation",multi:!1})})]}),a.jsx(k.Label,{label:"Primary Harbour",htmlFor:"vessel-primaryHarbour",children:a.jsx(w.I,{id:"vessel-primaryHarbour",type:"text",readOnly:lx,placeholder:"Home port",defaultValue:W?.vesselSpecifics?.primaryHarbour})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5",children:[a.jsx(k.Label,{label:"Authority No.",htmlFor:"vessel-authNo",children:a.jsx(w.I,{id:"vessel-authNo",readOnly:lx,type:"text",placeholder:"(MNZ, AMSA)",defaultValue:W?.registration})}),a.jsx(k.Label,{label:"Transit identifier",htmlFor:"vessel-transitId",children:a.jsx(w.I,{id:"vessel-transitId",readOnly:lx,type:"text",placeholder:"(AIS)",defaultValue:W?.transitID})}),a.jsx(k.Label,{label:"MMSI",htmlFor:"vessel-mmsi",children:a.jsx(w.I,{id:"vessel-mmsi",readOnly:lx,type:"text",placeholder:"For marine traffic maps",defaultValue:W?.mmsi})}),a.jsx(k.Label,{label:"Call sign",htmlFor:"vessel-callSign",children:a.jsx(w.I,{id:"vessel-callSign",type:"text",readOnly:lx,placeholder:"Call sign",defaultValue:W?.callSign})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5",children:[a.jsx(k.Label,{label:"Vessel beam",htmlFor:"vessel-beam",children:a.jsx(w.I,{id:"vessel-beam",type:"text",readOnly:lx,placeholder:"Beam",defaultValue:W?.vesselSpecifics?.beam})}),a.jsx(k.Label,{label:"Length overall",htmlFor:"vessel-overallLength",children:a.jsx(w.I,{id:"vessel-overallLength",type:"text",readOnly:lx,placeholder:"L.O.A",defaultValue:W?.vesselSpecifics?.overallLength})}),a.jsx(k.Label,{label:"Date of build",htmlFor:"vessel-dateOfBuild",children:a.jsx(O.Z,{mode:"single",onChange:e=>{eM(new Date(e))},className:"w-full",placeholder:"D.O.B",disabled:lx,value:e_})}),a.jsx(k.Label,{label:"Draft",htmlFor:"vessel-draft",children:a.jsx(w.I,{id:"vessel-draft",readOnly:lx,type:"text",placeholder:"Draft",defaultValue:W?.vesselSpecifics?.draft})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Hull construction",htmlFor:"vessel-hullConstruction",children:a.jsx(w.I,{id:"vessel-hullConstruction",readOnly:lx,type:"text",placeholder:"(Steel, fibreglass, carbon etc)",defaultValue:W?.vesselSpecifics?.hullConstruction})}),a.jsx(k.Label,{label:"Hull color",htmlFor:"vessel-hullColor",children:a.jsx(w.I,{id:"vessel-hullColor",readOnly:lx,type:"text",placeholder:"Color",defaultValue:W?.vesselSpecifics?.hullColor})})]})]})]})})]}),(0,a.jsxs)(N.Zb,{className:"mx-2.5",children:[(0,a.jsxs)(N.Ol,{children:[a.jsx(F.H4,{children:"Vessel Configuration"}),a.jsx(F.P,{children:"Select a vessel type from the dropdown. We use this to help with constructing your logbook configuration. If a vessel type does not adequately describe your vessel, contact support and we can add this for you."}),a.jsx(F.P,{children:"Add engines, fuel and water tanks, and sullage configuration for this vessel."})]}),(0,a.jsxs)(N.aY,{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Vessel Type",required:!0,htmlFor:"vessel-type",children:a.jsx(I.Combobox,{className:"w-full",options:lu,value:e4,onChange:e=>{eE({...eD,vesseltype:e}),$({...W,vesselType:e}),e8(e)},title:"Vessel Type",placeholder:"Vessel Type",required:!0,multi:!1,disabled:!lb||!(0,v.Fs)("EDIT_VESSEL",lb)})}),a.jsx(k.Label,{label:"Vessel type description",htmlFor:"vessel-description",children:a.jsx(w.I,{id:"vessel-description",readOnly:lx,type:"text",placeholder:"Vessel type description",defaultValue:W?.vesselTypeDescription})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5",children:[(0,a.jsxs)("div",{children:[a.jsx(k.Label,{htmlFor:"vesselIcon",children:"Vessel Icon / Photo"}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:["Icon"!==e0||b()(W?.icon)?"Photo"===e0&&te.length>0?a.jsx("img",{className:"w-16 h-16 object-cover rounded-full border",src:"https://api.sealogs.com/assets/"+te[0].fileFilename,alt:""}):a.jsx(m.b,{className:"w-16 h-16 rounded-full border"}):a.jsx("img",{className:"w-16 h-16 object-cover rounded-full border",src:`/vessel-icons/${W.icon}.svg`,alt:""}),a.jsx(S.Button,{onClick:()=>lb&&(0,v.Fs)("EDIT_VESSEL",lb)&&eK(!0),children:"Change"})]})]}),(0,a.jsxs)("div",{children:[a.jsx(k.Label,{htmlFor:"vesselBanner",children:"Banner Image"}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"w-32 h-16",children:eR?a.jsx("div",{className:"flex justify-center items-center h-full",children:(0,a.jsxs)("svg",{className:"w-6 h-6 animate-spin",viewBox:"0 0 100 101",fill:"none",children:[a.jsx("path",{d:"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C0 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z",fill:"currentColor"}),a.jsx("path",{d:"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z",fill:"currentFill"})]})}):a.jsx("img",{className:"w-full h-full object-cover border rounded",src:eW||"/sealogs-SeaLogs_hero.png",alt:""})}),a.jsx(k.Label,{htmlFor:"fileUpload",label:"Upload",className:"inline-flex items-center justify-center gap-[8.5px] whitespace-nowrap font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-5 [&_svg]:shrink-0 border rounded-[6px] bg-primary border-primary text-background shadow-[0_4px_6px_hsla(0,0%,0%,0.2)] hover:bg-background hover:text-accent-foreground hover:border-border h-11 px-5 py-3 will-change-transform will-change-width will-change-padding transform-gpu hover:transition-colors hover:ease-out hover:duration-300 cursor-pointer"}),a.jsx(w.I,{type:"file",id:"fileUpload",readOnly:lx,className:"hidden",onChange:function(e){if(eG(!0),e.preventDefault(),e.target.files&&e.target.files[0])for(let t=0;t<e.target.files.length;t++)ez(l=>[...l,e.target.files[t]]),lh(e.target.files[t])}})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-5",children:[(0,a.jsxs)(k.Label,{label:"Minimum required crew",htmlFor:"vessel-minCrew",children:[a.jsx(w.I,{id:"vessel-minCrew",type:"text",placeholder:"Minimum crew",defaultValue:W?.minCrew,readOnly:lx}),a.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"Please include the skipper/master in this number. If a vessel can operate with 1 crew + the master then this number should be 2"})]}),a.jsx(k.Label,{label:"Maximum passengers allowed",htmlFor:"vessel-maxPax",children:a.jsx(w.I,{id:"vessel-maxPax",type:"text",readOnly:lx,placeholder:"Maximum passengers",defaultValue:W?.maxPax})}),(0,a.jsxs)(k.Label,{label:"Maximum people on board",htmlFor:"vessel-maxPOB",children:[a.jsx(w.I,{id:"vessel-maxPOB",type:"text",readOnly:lx,placeholder:"Max P.O.B",defaultValue:W?.maxPOB}),a.jsx("p",{className:"text-sm text-muted-foreground mt-1",children:"This is the maximum people on board including the skipper/master."})]})]}),a.jsx(D.tz,{type:"checkbox",id:"carries-dangerous-goods",checked:!!W?.vesselSpecifics?.carriesDangerousGoods,onCheckedChange:e=>{$({...W,vesselSpecifics:{...W.vesselSpecifics,carriesDangerousGoods:e}})},variant:"warning",disabled:lx,label:"This vessel is able to carry legally classified dangerous goods",className:"w-fit"}),a.jsx(D.tz,{type:"checkbox",id:"carries-vehicles",checked:!!W?.vesselSpecifics?.carriesVehicles,onCheckedChange:e=>{$({...W,vesselSpecifics:{...W.vesselSpecifics,carriesVehicles:e}})},variant:"warning",disabled:lx,label:"This vessel is able to carry vehicles",className:"w-fit"})]})]}),(0,a.jsxs)(N.Zb,{className:"mx-2.5",children:[(0,a.jsxs)(N.Ol,{children:[a.jsx(F.H4,{children:"Systems Configuration"}),a.jsx(F.P,{children:"Configure engines, fuel tanks, water tanks, and sewage systems for this vessel."})]}),(0,a.jsxs)(N.aY,{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[a.jsx(k.Label,{className:"text-base font-medium",children:"Engines / motors"}),(0,a.jsxs)("div",{className:"mt-2 space-y-2",children:[W?.parentComponent_Components?.nodes.length>0&&J&&a.jsx("div",{className:"flex flex-wrap gap-2",children:W?.parentComponent_Components?.nodes.filter(e=>"Engine"===e.basicComponent.componentCategory).map((e,t)=>a.jsx(S.Button,{variant:"outline",onClick:()=>{ey(!0),t7(e.basicComponent.id)},children:J?.find(t=>t.id==e.basicComponent.id)?.title},t))}),a.jsx(S.Button,{iconLeft:V.Z,variant:"outline",onClick:()=>{ey(!0),X(0)},disabled:lx,children:"Add engine"})]})]}),(0,a.jsxs)("div",{children:[a.jsx(k.Label,{className:"text-base font-medium",children:"Fuel tanks"}),(0,a.jsxs)("div",{className:"space-y-2",children:[W?.parentComponent_Components?.nodes.length>0&&ee&&a.jsx("div",{className:"flex flex-wrap gap-2",children:W?.parentComponent_Components?.nodes.filter(e=>"FuelTank"===e.basicComponent.componentCategory&&ee?.find(t=>t.id==e.basicComponent.id)?.id>0).map((e,t)=>a.jsx(S.Button,{iconLeft:V.Z,variant:"outline",onClick:()=>{ef(!0),t3(e.basicComponent.id)},children:ee?.find(t=>t.id==e.basicComponent.id)?.title},t))}),a.jsx(S.Button,{iconLeft:V.Z,variant:"outline",onClick:()=>{ef(!0),ea(0)},disabled:lx,children:"Add Fuel Tank"})]})]}),(0,a.jsxs)("div",{children:[a.jsx(k.Label,{className:"text-base font-medium",children:"Water tanks"}),(0,a.jsxs)("div",{className:"space-y-2",children:[W?.parentComponent_Components?.nodes.length>0&&en&&a.jsx("div",{className:"flex flex-wrap gap-2",children:W?.parentComponent_Components?.nodes.filter(e=>"WaterTank"===e.basicComponent.componentCategory).map((e,t)=>a.jsx(S.Button,{iconLeft:V.Z,variant:"outline",onClick:()=>{eI(!0),le(e.basicComponent.id)},children:en?.find(t=>t.id==e.basicComponent.id)?.title},t))}),a.jsx(S.Button,{iconLeft:V.Z,variant:"outline",onClick:()=>{eI(!0),ei(0)},disabled:lx,children:"Add Water Tank"})]})]}),(0,a.jsxs)("div",{children:[a.jsx(k.Label,{className:"text-base font-medium",children:"Sewage system"}),(0,a.jsxs)("div",{className:"space-y-2",children:[W?.parentComponent_Components?.nodes.filter(e=>"SewageSystem"===e.basicComponent.componentCategory).length>0&&eo&&a.jsx("div",{className:"flex flex-wrap gap-2",children:W?.parentComponent_Components?.nodes.filter(e=>"SewageSystem"===e.basicComponent.componentCategory).map((e,t)=>a.jsx(S.Button,{variant:"outline",onClick:()=>{eT(!0),lt(e.basicComponent.id)},children:eo?.find(t=>t.id==e.basicComponent.id)?.title},t))}),a.jsx(S.Button,{variant:"outline",onClick:()=>{eT(!0),eu(0)},disabled:lx,children:"Add Sewage System"})]})]})]})]}),(0,a.jsxs)(N.Zb,{className:"mx-2.5",children:[(0,a.jsxs)(N.Ol,{children:[a.jsx(F.H4,{children:"Inventory"}),a.jsx(F.P,{children:"Attach inventory items to this vessel. Record uses, create maintenance tasks (one-off and recurring), add manuals and documentation."})]}),a.jsx(N.aY,{children:H&&a.jsx(I.Combobox,{className:"w-full",options:H,value:Y,onChange:e=>{if(e.find(e=>"newInventory"===e.value))eC(!0);else{z(e);let t=Y?.filter(t=>e.every(e=>e.value!==t.value));t&&t.length>0&&t.map(e=>{la(e.value)});let l=e.filter(e=>Y?.every(t=>t.value!==e.value));l&&l.length>0&&l.map(e=>{ll(e.value)})}},title:"Select Inventory",placeholder:"Select Inventory",multi:!0,responsiveBadges:!0})})]}),(0,a.jsxs)(N.Zb,{className:"mx-2.5",children:[(0,a.jsxs)(N.Ol,{children:[a.jsx(F.H4,{children:"Crew"}),a.jsx(F.P,{children:"Add or update crew members to this vessel crew list. Linking your crew here will make it faster to add crew to your logbook entries and will help you track the status of your crew's training and qualifications."})]}),a.jsx(N.aY,{children:em&&a.jsx(I.Combobox,{className:"w-full",options:em,value:eh,onChange:e=>{e.find(e=>"newCrewMember"===e.value)?ew(!0):ev(e)},title:"Select Crew Members",placeholder:"Select Crew Members",multi:!0,responsiveBadges:!0})})]}),"true"===localStorage.getItem("useDepartment")&&(0,a.jsxs)(N.Zb,{className:"mx-2.5",children:[(0,a.jsxs)(N.Ol,{children:[a.jsx(F.H4,{children:"Department"}),a.jsx(F.P,{children:"Add or update departments to this vessel."})]}),a.jsx(N.aY,{children:e5&&a.jsx(p.Z,{value:tu,onChange:e=>{tm(e.map(e=>e.value))},allDepartments:e5})})]}),(0,a.jsxs)(E.V,{children:[a.jsx(S.Button,{iconLeft:P.Z,variant:"back",onClick:()=>e>0?t.push(`/vessel/info?id=${e}`):t.push("/vessel"),children:"Cancel"}),+e>0&&a.jsx(S.Button,{variant:"destructive",onClick:()=>tc(!0),children:tf("Archive","Archive vessel")}),a.jsx(S.Button,{onClick:()=>t1(0),children:`${e>0?"Update":"Create"} ${tf("","vessel")}`})]}),a.jsx(L.h9,{openDialog:eg,setOpenDialog:ey,handleCreate:tW,title:`${Q>0?"Update Engine":"New Engine"}`,size:"xl",actionText:`${Q>0?"Update Engine":"Add Engine"}`,children:(0,a.jsxs)("div",{className:"space-y-5",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Title",htmlFor:"engine-title",children:a.jsx(w.I,{id:"engine-title",type:"text",readOnly:lx,placeholder:"Title",defaultValue:Q>0?J.filter(e=>e.id==Q).map(e=>e.title):""})}),a.jsx(k.Label,{label:"Identifier (abbreviation)",htmlFor:"engine-identified",children:a.jsx(w.I,{id:"engine-identified",type:"text",readOnly:lx,placeholder:"Identifier (abbreviation)",defaultValue:Q>0?J.filter(e=>e.id==Q).map(e=>e?.identifier):""})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(I.Combobox,{className:"w-full",label:"Type",buttonClassName:"w-auto min-w-auto",options:[{value:"Main",label:"Main"},{value:"Auxiliary",label:"Auxiliary"},{value:"Generator",label:"Generator"}],value:eD.engine?.type?{value:eD.engine.type,label:eD.engine.type}:null,onChange:e=>{eE({...eD,engine:{...eD.engine,type:e?.value||null}})},title:"Type",placeholder:"Type",multi:!1}),a.jsx(I.Combobox,{className:"w-full",label:"Drive type",buttonClassName:"w-auto min-w-auto",options:[{value:"Stern drive",label:"Stern drive"},{value:"Jet",label:"Jet"},{value:"Outboard",label:"Outboard"},{value:"Inboard",label:"Inboard"}],value:eD.engine?.driveType?{value:eD.engine.driveType,label:eD.engine.driveType}:null,onChange:e=>eE({...eD,engine:{...eD.engine,driveType:e?.value||null}}),title:"Drive type",placeholder:"Drive type",multi:!1})]}),a.jsx(I.Combobox,{label:"Position on vessel",className:"w-full",buttonClassName:"w-auto min-w-auto",options:[{value:"Port",label:"Port"},{value:"Starboard",label:"Starboard"},{value:"Centre",label:"Centre"}],value:eD.engine?.positionOnVessel?{value:eD.engine.positionOnVessel,label:eD.engine.positionOnVessel}:null,onChange:e=>eE({...eD,engine:{...eD.engine,positionOnVessel:e?.value||null}}),title:"Position on vessel",placeholder:"Position on vessel",multi:!1}),(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Engine make",htmlFor:"engine-make",children:a.jsx(w.I,{id:"engine-make",readOnly:lx,type:"text",placeholder:"Make",defaultValue:Q>0?J.find(e=>e.id==Q)?.make:""})}),a.jsx(k.Label,{label:"and model",htmlFor:"engine-model",children:a.jsx(w.I,{id:"engine-model",readOnly:lx,type:"text",placeholder:"Model",defaultValue:Q>0?J.find(e=>e.id==Q)?.model:""})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Engine kilowatts",htmlFor:"engine-kW",children:a.jsx(w.I,{id:"engine-kW",type:"number",readOnly:lx,placeholder:"kW",defaultValue:Q>0&&J.filter(e=>e.id==Q).map(e=>e.kW)})}),a.jsx(k.Label,{label:"Genset kilovolt-amperes",htmlFor:"engine-kVA",children:a.jsx(w.I,{id:"engine-kVA",type:"number",placeholder:"kVA",readOnly:lx,defaultValue:Q>0&&J.filter(e=>e.id==Q).map(e=>e.kVA)})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Current engine hours",htmlFor:"engine-hours",children:a.jsx(w.I,{id:"engine-hours",type:"number",readOnly:lx,placeholder:"Current hours",defaultValue:Q>0&&J.filter(e=>e.id==Q).map(e=>e?.currentHours)})}),a.jsx(k.Label,{label:"Max engine powers",htmlFor:"engine-power",children:a.jsx(w.I,{id:"engine-power",type:"number",placeholder:"Max power",readOnly:lx,defaultValue:Q>0&&J.filter(e=>e.id==Q).map(e=>e?.maxPower)})})]})]})}),a.jsx(L.h9,{openDialog:eb,setOpenDialog:ef,handleCreate:tH,title:`${el>0?"Update Fuel Tank":"New Fuel Tank"}`,handleDestructiveAction:()=>tY(el),destructiveActionText:el>0?"Delete Fuel Tank":"",size:"xl",actionText:`${el>0?"Update Fuel Tank":"Add Fuel Tank"}`,children:(0,a.jsxs)("div",{className:"space-y-5",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Title",htmlFor:"fuel-tank-title",children:a.jsx(w.I,{id:"fuel-tank-title",type:"text",placeholder:"Main Fuel Tank",readOnly:lx,defaultValue:el>0?ee.filter(e=>e.id==el).map(e=>e.title):""})}),a.jsx(k.Label,{label:"Identifier",htmlFor:"fuel-tank-identified",children:a.jsx(w.I,{id:"fuel-tank-identified",type:"text",placeholder:"FT-001",readOnly:lx,defaultValue:el>0?ee.filter(e=>e.id==el).map(e=>e.identifier):""})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Safe fuel capacity",children:a.jsx(w.I,{id:"fuel-tank-safeCapacity",type:"number",readOnly:lx,className:" grow",placeholder:"Safe fuel level",defaultValue:el>0?ee.filter(e=>e.id==el&&e.safeFuelCapacity>0).map(e=>e.safeFuelCapacity):""})}),a.jsx(k.Label,{label:"Maximum fuel capacity",children:a.jsx(w.I,{id:"fuel-tank-capacity",type:"number",className:" grow",readOnly:lx,placeholder:"Max fuel level",defaultValue:el>0?ee.filter(e=>e.id==el&&e.capacity>0).map(e=>e.capacity):""})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(I.Combobox,{className:"w-full",label:"Fuel type",options:[{value:"Unleaded 91",label:"Unleaded 91"},{value:"Unleaded 95",label:"Unleaded 95"},{value:"Diesel",label:"Diesel"},{value:"Other",label:"Other"}],value:eD.fuelTank?.fuelType?{value:eD.fuelTank.fuelType,label:eD.fuelTank.fuelType}:null,onChange:e=>eE({...eD,fuelTank:{...eD.fuelTank,fuelType:""===x()(e?.value)?null:e?.value||null}}),title:"Select",placeholder:"Select"}),a.jsx(k.Label,{label:"Current fuel level",htmlFor:"fuel-tank-currentLevel",children:a.jsx(w.I,{type:"number",className:" grow",readOnly:lx,value:eD.fuelTank?.currentLevel,onChange:e=>eE({...eD,fuelTank:{...eD.fuelTank,currentLevel:e.target.value}})})})]})]})}),a.jsx(L.h9,{openDialog:ek,setOpenDialog:eI,handleCreate:tR,title:`${er>0?"Update Water Tank":"New Water Tank"}`,size:"xl",actionText:`${er>0?"Update Water Tank":"Add Water Tank"}`,children:(0,a.jsxs)("div",{className:"space-y-5",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Title",htmlFor:"water-tank-title",children:a.jsx(w.I,{id:"water-tank-title",type:"text",placeholder:"Fresh Water Tank",readOnly:lx,defaultValue:er>0?en.filter(e=>e.id==er).map(e=>e.title):null})}),a.jsx(k.Label,{label:"Identifier",htmlFor:"water-tank-identified",children:a.jsx(w.I,{id:"water-tank-identified",type:"text",placeholder:"WT-001",readOnly:lx,defaultValue:er>0?en.filter(e=>e.id==er).map(e=>e.identifier):null})})]}),a.jsx(k.Label,{label:"Fresh Water Capacity",htmlFor:"water-tank-capacity",children:a.jsx(w.I,{id:"water-tank-capacity",type:"number",readOnly:lx,placeholder:"500",defaultValue:er>0?en.filter(e=>e.id==er&&e.capacity>0).map(e=>e.capacity):null})})]})}),a.jsx(L.h9,{openDialog:eS,setOpenDialog:eT,handleCreate:tG,size:"xl",title:`${ec>0?"Update Sewage System":"New Sewage System"}`,actionText:`${ec>0?"Update Sewage System":"Add Sewage System"}`,children:(0,a.jsxs)("div",{className:"space-y-5",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Title",htmlFor:"sewage-system-title",children:a.jsx(w.I,{id:"sewage-system-title",type:"text",readOnly:lx,placeholder:"Main Sewage System",defaultValue:ec>0?eo.filter(e=>e.id==ec).map(e=>e.title):null})}),a.jsx(k.Label,{label:"Identifier",htmlFor:"sewage-system-identified",children:a.jsx(w.I,{id:"sewage-system-identified",type:"text",readOnly:lx,placeholder:"SS-001",defaultValue:ec>0?eo.filter(e=>e.id==ec).map(e=>e.identifier):null})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Number of Tanks",htmlFor:"sewage-system-numberOfTanks",children:a.jsx(w.I,{id:"sewage-system-numberOfTanks",type:"number",placeholder:"2",readOnly:lx,defaultValue:ec>0?eo.filter(e=>e.id==ec&&e.numberOfTanks>0).map(e=>e.numberOfTanks):null})}),a.jsx(k.Label,{label:"Capacity",htmlFor:"sewage-system-capacity",children:a.jsx(w.I,{id:"sewage-system-capacity",type:"number",placeholder:"100",readOnly:lx,defaultValue:ec>0?eo.filter(e=>e.id==ec&&e.capacity>0).map(e=>e.capacity):null})})]})]})}),a.jsx(L.h9,{openDialog:ex,setOpenDialog:eC,handleCreate:ls,title:"Add Inventory",size:"xl",actionText:"Add Inventory",children:(0,a.jsxs)("div",{className:"space-y-5",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Title",htmlFor:"inventory-item",children:a.jsx(w.I,{id:"inventory-item",type:"text",placeholder:"Life Jacket",readOnly:lx})}),a.jsx(k.Label,{label:"Product Code",htmlFor:"inventory-code",children:a.jsx(w.I,{id:"inventory-code",type:"text",placeholder:"LJ-001",readOnly:lx})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Quantity",htmlFor:"inventory-qty",children:a.jsx(w.I,{id:"inventory-qty",type:"number",readOnly:lx,placeholder:"10"})}),a.jsx(k.Label,{label:"Costing details",htmlFor:"inventory-cost",children:a.jsx(w.I,{id:"inventory-cost",readOnly:lx,type:"text",placeholder:"$50.00 each"})})]}),a.jsx(I.Combobox,{options:R,isDisabled:lx,label:"Category",placeholder:"Select Category",className:"w-full",value:eL,onChange:e=>{"newCategory"===e.value&&to(!0),eE({...eD,inventory:{...eD.inventory,category:e.value}})}}),a.jsx(k.Label,{label:"Location",htmlFor:"inventory-location",children:a.jsx(w.I,{id:"inventory-location",type:"text",placeholder:"Port Storage Locker",readOnly:lx})}),a.jsx(B.Textarea,{id:"inventory-short-description",placeholder:"Description",readOnly:lx})]})}),(0,a.jsxs)(L.h9,{openDialog:ej,setOpenDialog:e=>{ew(e),e||li()},handleCreate:lo,title:"Add Crew Member",size:"xl",actionText:"Add Crew Member",children:[(0,a.jsxs)("div",{className:"space-y-5",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"First Name",htmlFor:"crew-firstName",children:a.jsx(w.I,{id:"crew-firstName",type:"text",readOnly:lx,placeholder:"John"})}),a.jsx(k.Label,{label:"Surname",htmlFor:"crew-surname",children:a.jsx(w.I,{id:"crew-surname",type:"text",readOnly:lx,placeholder:"Smith"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Username",htmlFor:"crew-username",children:a.jsx(w.I,{id:"crew-username",type:"text",readOnly:lx,placeholder:"jsmith"})}),a.jsx(k.Label,{label:"Password",htmlFor:"crew-password",children:a.jsx(w.I,{id:"crew-password",type:"password",readOnly:lx,placeholder:"••••••••"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Email",htmlFor:"crew-email",children:a.jsx(w.I,{id:"crew-email",type:"email",readOnly:lx,placeholder:"<EMAIL>"})}),a.jsx(k.Label,{label:"Phone Number",htmlFor:"crew-phoneNumber",children:a.jsx(w.I,{id:"crew-phoneNumber",type:"text",readOnly:lx,placeholder:"+****************"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 tablet-sm:grid-cols-2 gap-5",children:[a.jsx(k.Label,{label:"Primary Duty",htmlFor:"primaryDuty",children:a.jsx(M.Z,{onChange:e=>{tb(+e.value)},crewDutyID:ty})}),a.jsx(k.Label,{label:"User Role",htmlFor:"userRole",children:a.jsx(I.Combobox,{placeholder:"Select roles",onChange:e=>{th(e)},value:tp,options:tv})})]})]}),eP&&a.jsx("div",{className:"",children:eP.message})]}),a.jsx(L.h9,{openDialog:ti,setOpenDialog:to,title:"Create Inventory Category",actionText:"Create Category",handleCreate:lm,children:(0,a.jsxs)("div",{className:"space-y-8",children:[a.jsx(k.Label,{label:"Title",htmlFor:"inventory-category-title",children:a.jsx(w.I,{id:"inventory-category-title",type:"text",readOnly:lx,placeholder:"Safety Equipment"})}),a.jsx(k.Label,{label:"Abbreviation",htmlFor:"inventory-category-abbreviation",children:a.jsx(w.I,{id:"inventory-category-abbreviation",type:"text",readOnly:lx,placeholder:"SAFE"})})]})}),a.jsx(L.h9,{openDialog:td,setOpenDialog:tc,handleCreate:t4,title:"Archive Vessel",actionText:"Archive Vessel",children:a.jsx("div",{className:"my-4 flex items-center",children:"Are you sure you want to archive this vessel?"})}),a.jsx(L.h9,{openDialog:eJ,setOpenDialog:eK,handleCreate:()=>{W?.id&&t6({variables:{input:{id:W?.id,icon:eQ?eQ.replaceAll(" ","-"):null,iconMode:e0,photoID:te.length>0?te[0].id:null}}}),$({...W,icon:eQ?eQ.replaceAll(" ","-"):null,iconMode:e0,photoID:te.length>0?te[0].id:null}),eK(!1)},title:"Select Vessel Icon / Upload a Photo",actionText:"Save",children:(0,a.jsxs)(A.Tabs,{value:e0||"Icon",onValueChange:e1,className:"w-full",children:[(0,a.jsxs)(A.TabsList,{children:[a.jsx(A.TabsTrigger,{value:"Icon",children:"Icon"}),a.jsx(A.TabsTrigger,{value:"Photo",children:"Photo"})]}),a.jsx(A.TabsContent,{value:"Icon",className:"mt-4 space-y-2.5",children:i.uq.map((e,t)=>{let l=eQ===e.title;return a.jsx(S.Button,{variant:l?"primary":"outline",className:`w-full h-fit py-2.5 justify-start ${l?"bg-primary-foreground text-primary border-primary":""}`,iconLeft:a.jsx("img",{src:`/vessel-icons/${e.icon}`,alt:"icon",className:"size-12"}),onClick:()=>eX(e.title),children:a.jsx("span",{className:"uppercase",children:e.title})},t)})}),a.jsx(A.TabsContent,{value:"Photo",className:"mt-4",children:(0,a.jsxs)("div",{className:"flex w-full gap-5",children:[te.length>0&&te.map((e,t)=>a.jsx("div",{className:"flex items-center justify-between w-1/2",children:a.jsx("div",{className:"flex items-center gap-5 w-full",children:a.jsx("img",{src:`${"https://api.sealogs.com/assets/"+e.fileFilename}`,alt:"icon",className:"h-56 object-cover w-full"})})},t)),a.jsx("div",{className:`${te.length>0?"w-1/2":"w-full"}`,children:a.jsx(h.Z,{setDocuments:tt,text:"Vessel Photo",documents:te,multipleUpload:!1})})]})})]})})]})}},57710:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>s});var a=l(98768);l(60343);var n=l(64837);function s({children:e}){return a.jsx(n.Z,{children:e})}},82102:(e,t,l)=>{"use strict";l.d(t,{Z:()=>u});var a=l(98768),n=l(94060),s=l(79418),r=l(60343),i=l(59689),o=l(67468),d=l(57103),c=l(81524);let u=({crewDutyID:e=0,onChange:t,label:l,offline:u=!1,placeholder:m="Duty",multi:p=!1,modal:h,hideCreateOption:v=!1})=>{let g=new i.Z,[y,b]=(0,r.useState)(!0),[f,x]=(0,r.useState)([]),[C,j]=(0,r.useState)([]),[w,k]=(0,r.useState)(!1),[I]=(0,s.t)(n.HW,{fetchPolicy:"cache-and-network",onCompleted:t=>{let l=t.readCrewDuties.nodes;if(l){let t=l.filter(e=>!e.archived),a=t;if(v||(a=[{id:0,title:"-- Create New Duty --",archived:!1,abbreviation:"NEW"},...t]),x(a),e>0){let l=t.find(t=>t.id===e);j({label:l.title,value:l.id})}}},onError:e=>{console.error("queryDutiesEntry error",e)}}),S=async()=>{if(u){let t=await g.getAll();if(t){let l=t.filter(e=>!e.archived),a=l;if(v||(a=[{id:0,title:"-- Create New Duty --",archived:!1,abbreviation:"NEW"},...l]),x(a),e>0){let t=l.find(t=>t.id===e);j({label:t.title,value:t.id})}}}else await I()};return(0,r.useEffect)(()=>{y&&(S(),b(!1))},[y]),(0,r.useEffect)(()=>{if(e>0&&f.length>0){let t=f.find(t=>t.id==e);t&&j({label:t.title,value:t.id})}else 0===e&&j(null)},[e,f]),(0,a.jsxs)(a.Fragment,{children:[a.jsx(c.Combobox,{modal:h,options:f.map(e=>({label:`${e.title}`,value:e.id})),label:l,multi:p,value:C,onChange:e=>{if(e&&0===e.value){k(!0);return}j(e),t(e)},placeholder:m}),a.jsx(d.AlertDialogNew,{openDialog:w,setOpenDialog:k,size:"md",noButton:!0,noFooter:!0,className:"space-y-0",children:a.jsx(o.default,{crewDutyId:0,onCancel:()=>k(!1),onCreate:e=>{let l={label:e.title,value:e.id};j(l),t(l),S(),k(!1)},isPopup:!0})})]})}},8416:(e,t,l)=>{"use strict";l.d(t,{tz:()=>m});var a=l(98768),n=l(60343),s=l(85745),r=l(8750),i=l(70906),o=l(56937),d=l(74602);let c=(0,s.j)("cursor-pointer w-full px-4 rounded-e-lg will-change-transform will-change-color will-change-padding transform-gpu hover:transition-all hover:ease-out hover:duration-300",{variants:{variant:{default:"hover:bg-fire-bush-100 hover:border-yellow-vivid-600",primary:"hover:bg-light-blue-vivid-50 hover:border-light-blue-vivid-600",secondary:"hover:bg-background hover:border-neutral-400",success:"hover:bg-bright-turquoise-100 hover:border-teal-600",destructive:"hover:bg-red-vivid-50 hover:border-red-vivid-600",warning:"hover:bg-fire-bush-100 hover:border-yellow-vivid-600",pink:"hover:bg-pink-vivid-50 hover:border-pink-vivid-600",outline:"hover:bg-background hover:border-neutral-400","light-blue":"hover:bg-light-blue-vivid-50 hover:border-light-blue-vivid-600"},size:{default:"py-[10.5px]",sm:"py-2",lg:"py-6"},disabled:{true:"hover:bg-transparent hover:border-border",false:""}},defaultVariants:{variant:"default",size:"default",disabled:!1}}),u=(0,s.j)("relative inset-x-0 w-12 rounded-s-lg justify-center flex items-center",{variants:{variant:{default:"bg-light-blue-vivid-50 border-light-blue-vivid-600",primary:"bg-light-blue-vivid-50 border-light-blue-vivid-600",secondary:"bg-background border-neutral-400",success:"bg-bright-turquoise-100 border-teal-600",destructive:"bg-red-vivid-50 border-red-vivid-600",warning:"bg-fire-bush-100 border-yellow-vivid-600",pink:"bg-pink-vivid-50 border-pink-vivid-600",outline:"bg-background border-neutral-400","light-blue":"bg-light-blue-vivid-50 border-light-blue-vivid-600"}},defaultVariants:{variant:"default"}}),m=n.forwardRef(({type:e="checkbox",id:t,checked:l,onCheckedChange:s,disabled:m,value:p,name:h,label:v,children:g,className:y,variant:b,size:f,radioGroupValue:x,isRadioStyle:C=!0,rightContent:j,leftContent:w,onClick:k,...I},S)=>{let T=n.useId(),D=t||`${e}-${T}`,E="radio"===e?x===p:l;return(0,a.jsxs)("div",{ref:S,className:(0,o.cn)("flex rounded-lg relative group justify-evenly border border-border shadow-sm cursor-pointer",m&&"opacity-50 cursor-not-allowed",y),onClick:t=>{!m&&("checkbox"===e&&s?s(!l):"radio"===e&&s&&!E&&s(!0),k&&k(t))},...I,children:[a.jsx("div",{className:(0,o.cn)(u({variant:b})),children:"checkbox"===e?a.jsx(r.Checkbox,{id:D,isRadioStyle:C,checked:l,onCheckedChange:e=>{"boolean"==typeof e&&s&&s(e)},disabled:m,name:h,variant:b,size:"lg",className:"pointer-events-none"}):a.jsx(i.mJ,{id:D,value:p||"",disabled:m,variant:b,size:"md",checked:E,className:"pointer-events-none"})}),a.jsx("div",{className:(0,o.cn)("flex items-center",c({variant:"secondary",size:f,disabled:m})),children:(0,a.jsxs)("div",{className:(0,o.cn)("flex flex-1 items-center",{"gap-2":w||j}),children:[w&&a.jsx("div",{className:"inline-flex items-center",children:w}),g||v&&a.jsx(d.P,{className:(0,o.cn)("text-wrap text-foreground text-base"),children:v}),j&&a.jsx("div",{className:"inline-flex items-center",children:j})]})})]})});m.displayName="CheckFieldLabel"},93488:(e,t,l)=>{"use strict";l.d(t,{e:()=>n,p:()=>s});var a=l(50058);function n(e="phablet"){let t=(0,a.k)();return(l,a)=>t[e]?a:l}function s(e,t,l){return e?l:t}},42832:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>a});let a=(0,l(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\vessel\layout.tsx#default`)}};