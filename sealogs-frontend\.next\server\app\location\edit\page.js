(()=>{var e={};e.id=5747,e.ids=[5747],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},39470:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>p,routeModule:()=>g,tree:()=>d}),s(61520),s(10133),s(78398),s(57757),s(48045);var r=s(40060),a=s(33581),o=s(57567),n=s.n(o),i=s(51650),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d=["",{children:["location",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,61520)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\location\\edit\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,10133)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\location\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\location\\edit\\page.tsx"],c="/location/edit/page",u={require:s,loadChunk:()=>Promise.resolve()},g=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/location/edit/page",pathname:"/location/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},20835:(e,t,s)=>{Promise.resolve().then(s.bind(s,66882))},66882:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(98768),a=s(95272),o=s(69424);let n=()=>{let e=(0,o.useSearchParams)().get("id")??0;return r.jsx(a.default,{id:+e})}},61520:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\location\edit\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[864,8865,3563,6263,8189,9507,7602,1656,6451,4234,2925,5394,4837,6342,7380,1206],()=>s(39470));module.exports=r})();