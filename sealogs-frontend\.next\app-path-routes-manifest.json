{"/_not-found/page": "/_not-found", "/favicon.ico/route": "/favicon.ico", "/login/page": "/login", "/logout/page": "/logout", "/lost-password/page": "/lost-password", "/page": "/", "/redirect/page": "/redirect", "/reset-password/page": "/reset-password", "/select-client/page": "/select-client", "/select-department/page": "/select-department", "/crew-training/page": "/crew-training", "/crew-training/edit/page": "/crew-training/edit", "/calendar/page": "/calendar", "/crew-training/create/page": "/crew-training/create", "/crew/info/page": "/crew/info", "/company-details/page": "/company-details", "/crew-training/info/page": "/crew-training/info", "/department/edit/page": "/department/edit", "/crew/page": "/crew", "/department/info/page": "/department/info", "/dashboard/page": "/dashboard", "/department/page": "/department", "/dashboard/log-entries/page": "/dashboard/log-entries", "/incident-records/edit/page": "/incident-records/edit", "/document-locker/page": "/document-locker", "/department/create/page": "/department/create", "/incident-records/page": "/incident-records", "/incident-records/create/page": "/incident-records/create", "/inventory/new/page": "/inventory/new", "/inventory/suppliers/page": "/inventory/suppliers", "/inventory/view/page": "/inventory/view", "/inventory/suppliers/view/page": "/inventory/suppliers/view", "/key-contacts/page": "/key-contacts", "/inventory/suppliers/new/page": "/inventory/suppliers/new", "/key-contacts/create/page": "/key-contacts/create", "/key-contacts/edit/page": "/key-contacts/edit", "/location-overview/page": "/location-overview", "/location/create/page": "/location/create", "/location/page": "/location", "/log-entries/customise-log-books/edit/page": "/log-entries/customise-log-books/edit", "/location/info/page": "/location/info", "/location/edit/page": "/location/edit", "/maintenance/new/page": "/maintenance/new", "/reporting/activity-reports/page": "/reporting/activity-reports", "/reporting/crew-seatime-report/page": "/reporting/crew-seatime-report", "/log-entries/page": "/log-entries", "/maintenance/complete-recurring-task/page": "/maintenance/complete-recurring-task", "/reporting/detailed-fuel-report/page": "/reporting/detailed-fuel-report", "/log-entries/oldEntry/page": "/log-entries/oldEntry", "/reporting/crew-training-completed-report/page": "/reporting/crew-training-completed-report", "/maintenance/page": "/maintenance", "/inventory/page": "/inventory", "/reporting/crew-training-upcoming-overdue-report/page": "/reporting/crew-training-upcoming-overdue-report", "/reporting/fuel-summary-report/page": "/reporting/fuel-summary-report", "/reporting/fuel-analysis/page": "/reporting/fuel-analysis", "/log-entries/pdf/page": "/log-entries/pdf", "/reporting/engine-hours-report/page": "/reporting/engine-hours-report", "/reporting/maintenance-cost-track/page": "/reporting/maintenance-cost-track", "/reporting/page": "/reporting", "/reporting/maintenance-status-activity/page": "/reporting/maintenance-status-activity", "/risk-evaluations/page": "/risk-evaluations", "/reporting/fuel-tasking-analysis/page": "/reporting/fuel-tasking-analysis", "/risk-strategies/page": "/risk-strategies", "/reporting/simple-fuel-report/page": "/reporting/simple-fuel-report", "/settings/crew-duty/edit/page": "/settings/crew-duty/edit", "/reporting/service-report/page": "/reporting/service-report", "/reporting/trip-report/page": "/reporting/trip-report", "/settings/crew-duty/list/page": "/settings/crew-duty/list", "/settings/crew-duty/create/page": "/settings/crew-duty/create", "/settings/maintenance/category/page": "/settings/maintenance/category", "/settings/inventory/category/new/page": "/settings/inventory/category/new", "/settings/maintenance/category/new/page": "/settings/maintenance/category/new", "/settings/inventory/category/page": "/settings/inventory/category", "/training-type/create/page": "/training-type/create", "/settings/user-role/edit/page": "/settings/user-role/edit", "/training-matrix/page": "/training-matrix", "/settings/user-role/create/page": "/settings/user-role/create", "/settings/user-role/page": "/settings/user-role", "/training-type/info/page": "/training-type/info", "/training-type/edit/page": "/training-type/edit", "/trip-report-schedule-stop/edit/page": "/trip-report-schedule-stop/edit", "/trip-report-schedules/edit/page": "/trip-report-schedules/edit", "/trip-report-schedule-stop/create/page": "/trip-report-schedule-stop/create", "/training-type/page": "/training-type", "/trip-schedules/import/page": "/trip-schedules/import", "/trip-schedule-services/page": "/trip-schedule-services", "/trip-report-schedules/page": "/trip-report-schedules", "/user/create/page": "/user/create", "/vessel/create/page": "/vessel/create", "/trip-schedules/page": "/trip-schedules", "/vessel/info/page": "/vessel/info", "/user/edit/page": "/user/edit", "/trip-schedule-services/edit/page": "/trip-schedule-services/edit", "/vessel/edit/page": "/vessel/edit", "/vessel/logbook-configuration/page": "/vessel/logbook-configuration", "/vessel/page": "/vessel"}