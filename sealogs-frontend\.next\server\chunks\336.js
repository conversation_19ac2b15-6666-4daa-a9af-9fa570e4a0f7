exports.id=336,exports.ids=[336],exports.modules={4454:(e,s,t)=>{"use strict";t.d(s,{n:()=>x});var l=t(98768),n=t(17203),a=t(8087),r=t(27514),i=t(26509),o=t(13006),c=t(9210),d=t(56937),u=t(25394),v=t(60343),h=t(69424);function x({items:e,onBack:s,onDistructAction:t,ShowDistructive:x=!1,setOpen:m,logBookConfig:p,backLabel:g="Back to vessel",disTructLabel:f="Delete logbook entry",onSelect:j,triggerIcon:b}){let[w,N]=(0,o.v1)("tab",{defaultValue:"crew"}),[y,D]=(0,v.useState)(),C=(0,h.useRouter)(),O=async e=>{e.url?C.push(e.url):j?j(e.value):e.value!==w&&await N(e.value)};return(0,l.jsxs)(r.DropdownMenu,{children:[l.jsx(r.DropdownMenuTrigger,{className:"h-10 flex items-center",asChild:!!b,children:b?l.jsx(u.zx,{variant:"ghost",onClick:e=>e.stopPropagation(),size:"sm",className:"h-8 w-8 p-0",children:b}):l.jsx(c.HV,{size:36})}),(0,l.jsxs)(r.DropdownMenuContent,{align:"end",className:"w-[256px] p-0",children:[(0,l.jsxs)("div",{className:"text-input flex flex-col items-center justify-center py-[9px]",children:[(0,l.jsxs)(r.DropdownMenuItem,{hoverEffect:!1,onClick:s,className:(0,d.cn)("flex items-center gap-[8px] px-[26px] w-full h-11 rounded-md cursor-pointer focus:bg-accent","hover:bg-accent hover:px-[6px] hover:w-[233px] hover:border hover:border-border"),children:[l.jsx(n.Z,{size:16}),l.jsx("span",{children:g})]}),e.map((e,s)=>(0,l.jsxs)(r.DropdownMenuItem,{onClick:()=>O(e),children:[e.icon?e.icon:(0,l.jsxs)("div",{className:"h-full w-fit flex z-10 relative flex-col items-center justify-center",children:[l.jsx("div",{className:(0,d.cn)("border-l -translate-y-[17.5px] absolute inset-y-0 border-wedgewood-200 border-dashed",0===s&&"invisible")}),l.jsx("div",{className:(0,d.cn)("size-[11px] z-10 rounded-full","group-hover:border-primary group-hover:bg-curious-blue-200",w===e.value?"border border-primary bg-curious-blue-200":"border border-cool-wedgewood-200 bg-outer-space-50")})]}),l.jsx("div",{className:(0,d.cn)("relative z-20",w!==e.value||e.url?"":"font-medium text-accent-foreground"),children:e.label})]},s))]}),p&&y&&l.jsx(l.Fragment,{children:"Off"!==y.status&&(0,l.jsxs)(l.Fragment,{children:[l.jsx(i.Separator,{className:"border-border"}),(0,l.jsxs)(r.DropdownMenuItem,{hoverEffect:!1,className:(0,d.cn)("px-[47px] h-[61px] text-text hover:text-foreground flex gap-2.5 group py-[21px] relative focus:bg-outer-space-50 rounded-none"),onClick:()=>{m(!0)},children:[l.jsx("span",{className:"relative z-20 text-text uppercase hover:text-primary text-sm",children:"Radio logs"}),l.jsx("div",{className:(0,d.cn)("absolute w-20 left-12 inset-y-0","group-hover:bg-outer-space-50 group-hover:w-full group-hover:left-0","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-[width,left] group-hover:ease-out group-hover:duration-300","outline-none focus:outline-none active:outline-none")})]})]})}),x&&(0,l.jsxs)(l.Fragment,{children:[l.jsx(i.Separator,{className:"border-border"}),(0,l.jsxs)(r.DropdownMenuItem,{hoverEffect:!1,onClick:t,className:(0,d.cn)("group relative h-[61px] px-[26px] py-[21px] cursor-pointer focus:bg-destructive/5 rounded-none text-destructive focus:text-destructive"),children:[(0,l.jsxs)("div",{className:"relative gap-2.5 flex items-center z-20",children:[l.jsx(a.Z,{size:24}),l.jsx("span",{children:f})]}),l.jsx("div",{className:(0,d.cn)("absolute w-full h-11 bottom-0 inset-x-0","group-hover:bg-destructive/focus:bg-destructive/5 group-hover:h-full","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-[height,color] group-hover:ease-out group-hover:duration-300","outline-none focus:outline-none active:outline-none")})]})]})]})]})}},65128:(e,s,t)=>{"use strict";t.d(s,{z:()=>j});var l=t(98768),n=t(9210),a=t(34376),r=t(60343),i=t(72548),o=t(76342),c=t(33849),d=t(25394),u=t(29342),v=t(56937);let h=[{label:"On Voyage",value:"OnVoyage"},{label:"Ready For Voyage",value:"AvailableForVoyage"},{label:"Out Of Service",value:"OutOfService"}],x=[{label:"Crew Unavailable",value:"CrewUnavailable"},{label:"Skipper/Master Unavailable",value:"MasterUnavailable"},{label:"Planned Maintenance",value:"PlannedMaintenance"},{label:"Breakdown",value:"Breakdown"},{label:"Other",value:"Other"}];function m({vessel:e,display:s,setDisplay:t,onChangeStatusSuccess:n}){let{toast:m}=(0,a.pm)(),[p,g]=(0,r.useState)(null),[f]=(0,i.D)(o.qb9,{onCompleted:s=>{s.createLogBookEntry,g({...p,vesselID:e?.id,date:p?.date,status:p?.status,comment:p?.comment,reason:p?.reason,otherReason:p?.otherReason,expectedReturn:p?.expectedReturn}),t(!1),n&&n(p)},onError:e=>{m({variant:"destructive",description:e.message})}});return l.jsx(d.h9,{openDialog:s,size:"xl",setOpenDialog:t,handleCreate:()=>{p?.status==="OutOfService"?f({variables:{input:{vesselID:e?.id,date:p?.date,status:p?.status,comment:p?.comment,reason:p?.reason,otherReason:p?.otherReason,expectedReturn:p?.expectedReturn}}}):f({variables:{input:{vesselID:e?.id,date:p?.date,status:p?.status}}})},title:"Update Vessel Status",actionText:"Update",children:l.jsx("div",{className:"",children:(0,l.jsxs)("div",{className:"mb-4 md:mb-0",children:[l.jsx("div",{className:"my-4",children:l.jsx(u.Z,{mode:"single",onChange:e=>{g({...p,date:e})},className:"w-full",placeholder:"Select date",value:p?.date})}),(0,l.jsxs)("div",{className:(0,v.cn)("grid gap-2.5 my-4",p?.status==="OutOfService"?"grid-cols-2":"grid-cols-1"),children:[l.jsx(d.__,{label:"Status",children:l.jsx(d.hQ,{id:"vessel-status",options:h,placeholder:"Status",value:h.find(e=>p?.status===e.value),onChange:s=>{0===e.logentryID?"OnVoyage"===s.value?m({variant:"destructive",description:"There is no Open LogBook entry, Please create a Logbook entry to set the vessel on voyage"}):g({...p,status:s?.value}):m({variant:"destructive",description:"There is an Open LogBook entry, Please complete the entry in order to update the vessel status"})}})}),p?.status==="OutOfService"&&l.jsx(d.__,{label:"Reason for out of service status",children:l.jsx(d.hQ,{id:"vessel-status-reason",options:x,placeholder:"Reason",value:x.find(e=>p?.reason===e.value),onChange:e=>{g({...p,reason:e?.value})}})})]}),p?.status==="OutOfService"&&p?.reason==="Other"&&l.jsx("div",{className:"flex items-center my-4",children:l.jsx(d.gx,{id:"vessel-status-other",placeholder:"Other description",value:p?.otherReason,onChange:e=>g({...p,otherReason:e.target.value})})}),p?.status==="OutOfService"&&l.jsx(d.__,{label:"Comments",className:"my-4",children:l.jsx(c.Z,{id:"comment",placeholder:"Comment",content:p?.comment,handleEditorChange:e=>g({...p,comment:e})})}),p?.status==="OutOfService"&&(0,l.jsxs)("div",{className:"my-4",children:[l.jsx(d.__,{label:"Expected date of return"}),l.jsx(u.Z,{mode:"single",onChange:e=>{g({...p,expectedReturn:e})},className:"w-full",placeholder:"Select date",value:p?.expectedReturn})]})]})})})}var p=t(4454),g=t(69424),f=t(46020);let j=({vessel:e,onChangeStatusSuccess:s})=>{let[t,i]=(0,r.useState)(!1),o=(0,g.useRouter)(),{toast:c}=(0,a.pm)(),d=()=>{if(e.logentryID>0){c({variant:"destructive",description:"There is an Open LogBook entry, Please complete the entry in order to update the vessel status"});return}i(!0)},u=[{label:"Maintenance",value:"maintenance",icon:l.jsx(n.y5,{className:"icons h-6 w-6 bg-accent/0 ring-0"}),url:`/vessel/info?id=${e.id}&tab=maintenance`},{label:"Crew",value:"crew",icon:l.jsx(n.Ko,{className:"icons h-6 w-6"}),url:`/vessel/info?id=${e.id}&tab=crew`},{label:"Training & drills",value:"crew_training",icon:l.jsx(n._z,{className:"icons h-6 w-6 bg-accent/0 ring-0"}),url:`/vessel/info?id=${e.id}&tab=crew_training`},{label:0!==e.logentryID?"Open Logbook entry":"Logbook entry",value:0!==e.logentryID?"logbook-open":"logbook",icon:l.jsx(n.V1,{className:"icons h-6 w-6"}),url:0!==e.logentryID?`/log-entries?vesselID=${e.id}&logentryID=${e.logentryID}`:`/vessel/info?id=${e.id}`},{label:"Documents",value:"documents",icon:l.jsx(n.EU,{className:"icons h-6 w-6"}),url:`/vessel/info?id=${e.id}&tab=documents`}];return(0,l.jsxs)(l.Fragment,{children:[l.jsx(p.n,{backLabel:"View vessel",triggerIcon:l.jsx(f.Z,{className:"h-4 w-4"}),onBack:()=>o.push(`/vessel/info?id=${e.id}`),items:u,onDistructAction:()=>d(),ShowDistructive:!0,disTructLabel:e.status&&"OutOfService"!==e.status.status?"Take vessel out of service":"Change vessel status"}),l.jsx(m,{vessel:e,display:t,setDisplay:i,onChangeStatusSuccess:s})]})}},60336:(e,s,t)=>{"use strict";t.d(s,{F:()=>z,default:()=>P});var l=t(98768),n=t(60343),a=t(66263),r=t(51742),i=t(30905),o=t(87175);function c({vessel:e}){return e?l.jsx(l.Fragment,{children:e.pob>0?l.jsx(o.C,{children:e.pob}):l.jsx("span",{children:"\xa0"})}):null}var d=t(99891),u=t(34469),v=t(49008),h=t(24794),x=t(70684),m=t(13842),p=t(7678),g=t.n(p),f=t(14826),j=t.n(f),b=t(69424),w=t(9210),N=t(27514),y=t(22995);let D=({onChange:e})=>{let{isMobile:s}=(0,y.Ap)(),[t,r]=(0,n.useState)(!0),i=()=>{r(s=>{let t=!s;return e(t),t})};return(0,l.jsxs)(N.DropdownMenu,{children:[l.jsx(N.DropdownMenuTrigger,{asChild:!0,children:l.jsx(w.HV,{size:36})}),(0,l.jsxs)(N.DropdownMenuContent,{side:s?"bottom":"right",align:s?"end":"start",children:[l.jsx(N.DropdownMenuItem,{onClick:()=>i(),children:t?"View archived vessels":"View active vessels"}),l.jsx(N.DropdownMenuSeparator,{}),l.jsx(a.default,{href:"/vessel/create",children:l.jsx(N.DropdownMenuItem,{children:"Add vessel"})})]})]})};var C=t(39650),O=t(65128),S=t(56937),k=t(17380);let z=({vessel:e,className:s,iconClassName:t})=>e.vesselPosition&&(e.vesselPosition.geoLocation?.id>0||e.vesselPosition.lat&&e.vesselPosition.lat)?(0,l.jsxs)(h.Dialog,{children:[l.jsx(h.DialogTrigger,{onClick:e=>e.stopPropagation(),className:(0,S.cn)(s),children:l.jsx(w.r4,{className:(0,S.cn)("size-9",t)})}),(0,l.jsxs)(h.DialogContent,{className:"max-w-6xl w-[95vw] p-0 overflow-hidden",children:[l.jsx(h.DialogHeader,{className:"p-4 pb-0",children:(0,l.jsxs)(h.DialogTitle,{className:"flex items-center gap-2",children:[l.jsx(v.Z,{size:18}),e.vesselPosition.geoLocation?.title]})}),l.jsx("div",{className:"h-[80vh] pt-0 grid",children:l.jsx(u.Z,{position:[e.vesselPosition.lat||e.vesselPosition.geoLocation?.lat,e.vesselPosition.long||e.vesselPosition.geoLocation?.long],zoom:7,vessel:e})})]})]}):null;function P(e){(0,b.useRouter)();let[s,t]=(0,n.useState)([]),[u,v]=(0,n.useState)([]),[h,p]=(0,n.useState)([]),[f,N]=(0,n.useState)(!0),[y,S]=(0,n.useState)({}),[P,R]=(0,n.useState)([]);(0,m.vu)(e=>{let s=e.filter(e=>!e.archived),l=e.filter(e=>e.archived);p(s),v(l),t(s),N(!1)},2);let M=(e,s)=>{t(t=>{let l=t.findIndex(s=>s.id==e.id);if(-1===l)return t;let n=[...t];return e.status=s,n[l]=e,n})},T=(0,r.wu)([{accessorKey:"title",header:({column:e})=>l.jsx(i.u,{column:e,title:"Vessels"}),cell:({row:e})=>{let s=e.original,t=s.status;return(0,l.jsxs)("div",{className:"relative w-fit",children:[l.jsx(a.default,{href:`/vessel/info?id=${s.id}&name=${s.title}`,children:t&&"OutOfService"!==t.status?(0,l.jsxs)("div",{className:"flex flex-col tablet-md:flex-row gap-2 whitespace-nowrap items-center",children:[l.jsx(x.TooltipProvider,{children:(0,l.jsxs)(x.Tooltip,{children:[l.jsx(x.TooltipTrigger,{asChild:!0,children:l.jsx("div",{className:"border-2 relative border-bright-turquoise-600 rounded-full",children:l.jsx(d.Z,{vessel:s})})}),l.jsx(x.TooltipContent,{className:"tablet-md:hidden",children:l.jsx("p",{children:s.title})})]})}),(0,l.jsxs)("div",{className:"flex-col hidden tablet-md:flex",children:[l.jsx("div",{className:"flex items-center",children:l.jsx("span",{className:"font-medium",children:s.title})}),0!==s.logentryID?l.jsx("div",{className:"text-curious-blue-400 text-[10px]",children:"ON VOYAGE"}):l.jsx("div",{className:"text-curious-blue-400 text-[10px]",children:"READY FOR VOYAGE"})]})]}):(0,l.jsxs)("div",{className:"flex flex-col tablet-md:flex-row gap-2 whitespace-nowrap items-center ",children:[l.jsx(x.TooltipProvider,{children:(0,l.jsxs)(x.Tooltip,{children:[l.jsx(x.TooltipTrigger,{children:l.jsx("div",{className:"relative size-fit",children:(0,l.jsxs)("div",{className:"relative inline-block border-2 min-w-fit border-destructive opacity-50 rounded-full",children:[l.jsx(d.Z,{vessel:s}),l.jsx("div",{className:"absolute inset-0 bg-destructive opacity-50 rounded-full",children:"        "})]})})}),l.jsx(x.TooltipContent,{className:"tablet-md:hidden",children:l.jsx("p",{children:s.title})})]})}),(0,l.jsxs)("div",{className:"flex-col hidden tablet-md:flex",children:[l.jsx("div",{className:"flex items-center",children:l.jsx("span",{className:"font-medium opacity-50",children:s.title})}),l.jsx("div",{className:"inline-block text-[10px] text-destructive",children:"OUT OF SERVICE"})]})]})}),l.jsx(z,{className:"absolute tablet-md:hidden -top-2.5 -right-4",iconClassName:"size-8",vessel:s})]})}},{accessorKey:"pob",header:({column:e})=>l.jsx(i.u,{column:e,title:"P.O.B"}),breakpoint:"tablet-sm",cell:({row:e})=>{let s=e.original;return l.jsx(c,{vessel:s})}},{accessorKey:"trainingsDue",header:({column:e})=>l.jsx(i.u,{column:e,title:"Training"}),cell:({row:e})=>{let s=e.original;return l.jsx(l.Fragment,{children:s.trainingsDue>0?l.jsx(o.C,{variant:"destructive",children:s.trainingsDue}):l.jsx(o.C,{variant:"success",children:l.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"#27AB83","aria-hidden":"true",children:l.jsx("path",{fillRule:"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z",clipRule:"evenodd"})})})})}},{accessorKey:"tasksDue",header:({column:e})=>l.jsx(i.u,{column:e,title:"Tasks"}),cell:({row:e})=>{let s=e.original;return l.jsx(l.Fragment,{children:s.tasksDue>0?l.jsx(o.C,{variant:"destructive",children:s.tasksDue}):l.jsx(o.C,{variant:"success",children:l.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"#27AB83","aria-hidden":"true",children:l.jsx("path",{fillRule:"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z",clipRule:"evenodd"})})})})}},{accessorKey:"postition",header:"Location",breakpoint:"tablet-md",cell:({row:e})=>{let s=e.original;return l.jsx(z,{vessel:s})}},{id:"actions",enableHiding:!1,cellAlignment:"right",cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"w-full flex justify-end",children:l.jsx(O.z,{vessel:s,onChangeStatusSuccess:e=>M(s,e)})})}}]),I=({type:e,data:s})=>{let l={...y};"vessel"===e&&(s?l.basicComponentID={eq:+s.value}:delete l.basicComponentID);let n=P;"keyword"===e&&(n=g()(j()(s.value))?[]:[{name:{contains:s.value}},{comments:{contains:s.value}},{workOrderNumber:{contains:s.value}}]),"isArchived"===e&&t(s?h:u),S(l),R(n)},V=(e,s)=>{I({type:e,data:s})};return(0,l.jsxs)(l.Fragment,{children:[l.jsx(C.ListHeader,{icon:l.jsx(w.bF,{className:"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]"}),title:"All vessels",actions:l.jsx(D,{onChange:e=>{V("isArchived",e)}})}),l.jsx("div",{className:"mt-16",children:f?l.jsx(k.hM,{}):l.jsx(r.wQ,{columns:T,data:s,onChange:I})})]})}},34469:(e,s,t)=>{"use strict";t.d(s,{Z:()=>x});var l=t(98768),n=t(47520);t(47011),t(7385);var a=t(35753),r=t(60343);t(94060);var i=t(96268),o=t(71241),c=t.n(o);let d=(0,n.default)(async()=>{},{loadableGenerated:{modules:["components\\location-map.tsx -> react-leaflet"]},ssr:!1}),u=(0,n.default)(async()=>{},{loadableGenerated:{modules:["components\\location-map.tsx -> react-leaflet"]},ssr:!1}),v=(0,n.default)(async()=>{},{loadableGenerated:{modules:["components\\location-map.tsx -> react-leaflet"]},ssr:!1}),h=({debounceMs:e=150})=>{let s=(0,a.Sx)(),{run:t}=(0,i.DI)(()=>{s&&s.invalidateSize()},e);return(0,r.useEffect)(()=>{let e=s.getContainer();if(!e)return;let l=new ResizeObserver(e=>{e.some(e=>{let{width:s,height:t}=e.contentRect;return s>0&&t>0})&&t()});l.observe(e);let n=e.parentElement;n&&l.observe(n);let a=setTimeout(()=>{s&&s.invalidateSize()},100);return()=>{l.disconnect(),clearTimeout(a)}},[s,t]),null};function x({position:e,vessel:s,vessels:t,zoom:n=13,onPositionChange:r,className:i="h-full",enableResize:o=!0,scrollWheelZoom:x=!1,style:m,resizeDebounceMs:p=150,enableClickToSetPosition:g=!1}){let f=[isNaN(e[0])?0:e[0],isNaN(e[1])?0:e[1]],j=c()((e,s)=>{"number"!=typeof e||"number"!=typeof s||isNaN(e)||isNaN(s)||r?.([e,s])},100),b=e=>{if(e?.latlng?.lat!==void 0&&e?.latlng?.lng!==void 0){let{lat:s,lng:t}=e.latlng;j(s,t)}},w=e=>{let{position:s,vessel:t}=e;return(0,a.zV)({dblclick(e){g&&b(e)}}),l.jsx(u,{position:s})},N=(()=>{if(t&&t.length>0){let e=t.find(e=>e.vesselPosition?.lat&&e.vesselPosition?.long);if(e)return[e.vesselPosition.lat||0,e.vesselPosition.long||0]}return f})(),y=`map-${Math.round(1e3*N[0])}-${Math.round(1e3*N[1])}`;return(0,l.jsxs)(d,{center:N,zoom:n,scrollWheelZoom:x,className:i,style:{minHeight:"200px",height:"100%",...m},children:[l.jsx(v,{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),t&&t.length>0?t.filter(e=>e.vesselPosition?.lat!=0||e.vesselPosition?.geoLocation?.id>0).map((e,s)=>e?.vesselPosition?.id>0&&l.jsx(w,{position:[(e.vesselPosition.lat||0)+.001+.004*Math.random(),e.vesselPosition.long||0],vessel:e},s)):s&&l.jsx(w,{position:f,vessel:s}),o&&l.jsx(h,{debounceMs:p})]},y)}},7385:()=>{},47011:()=>{},35753:(e,s,t)=>{"use strict";t.d(s,{Sx:()=>a,zV:()=>r});var l=t(60343);let n=(0,l.createContext)(null);function a(){return function(){let e=(0,l.useContext)(n);if(null==e)throw Error("No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>");return e}().map}function r(e){let s=a();return(0,l.useEffect)(function(){return s.on(e),function(){s.off(e)}},[s,e]),s}n.Provider}};