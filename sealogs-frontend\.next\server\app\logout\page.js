(()=>{var e={};e.id=5805,e.ids=[5805],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},89655:(e,i,o)=>{"use strict";o.r(i),o.d(i,{GlobalError:()=>s.a,__next_app__:()=>g,originalPathname:()=>D,pages:()=>c,routeModule:()=>u,tree:()=>l}),o(56887),o(78398),o(57757),o(48045);var t=o(40060),n=o(33581),r=o(57567),s=o.n(r),a=o(51650),d={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);o.d(i,d);let l=["",{children:["logout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,56887)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\logout\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(o.bind(o,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(o.bind(o,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\logout\\page.tsx"],D="/logout/page",g={require:o,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/logout/page",pathname:"/logout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},66840:(e,i,o)=>{Promise.resolve().then(o.bind(o,93148))},46776:(e,i,o)=>{"use strict";o.d(i,{E7:()=>g,Fs:()=>l,GJ:()=>r,PE:()=>n,UU:()=>a,Zu:()=>c,ay:()=>d,io:()=>D,j5:()=>s});var t=o(86708);let n=()=>{},r=()=>{let e="true"===localStorage.getItem("superAdmin"),i="true"===localStorage.getItem("admin");return e||i},s=()=>"true"===localStorage.getItem("superAdmin"),a=()=>{n()&&(window.location.href="/dashboard")},d=()=>{r()||(window.location.href="/dashboard")},l=(e,i)=>i.includes("ADMIN")||i.includes(e),c=e=>!1,D=async()=>{try{await t.Z.delete()}catch(e){console.error("[clearDB] deleting seaLogsDB failed: ",e)}try{await t.Z.open()}catch(e){console.error("[clearDB] opening seaLogsDB failed: ",e)}},g=()=>!1},93148:(e,i,o)=>{"use strict";o.r(i),o.d(i,{default:()=>d});var t=o(98768),n=o(60343),r=o(26100),s=o(69424),a=o(46776);let d=()=>{let e=(0,s.useRouter)(),i=async()=>{await (0,a.io)()};return(0,n.useEffect)(()=>{(0,a.j5)()&&i(),localStorage.removeItem("sl-jwt"),localStorage.removeItem("firstName"),localStorage.removeItem("surname"),localStorage.removeItem("userId"),localStorage.removeItem("superAdmin"),localStorage.removeItem("admin"),localStorage.removeItem("clientId"),localStorage.removeItem("useDepartment"),localStorage.removeItem("usePilotTransfer"),localStorage.removeItem("useTripSchedule"),localStorage.removeItem("clientTitle"),localStorage.removeItem("departmentId"),localStorage.removeItem("departmentTitle"),localStorage.removeItem("availableClients"),localStorage.removeItem("permissions"),localStorage.removeItem("crew"),e.push("/login")},[e]),t.jsx(t.Fragment,{children:t.jsx(r.Z,{message:"Logging you out ..."})})}},86708:(e,i,o)=>{"use strict";o.d(i,{Z:()=>n});let t=new(o(69507)).ZP("seaLogsDB");t.version(2).stores({AssetReporting_LogBookEntrySection:"id, idbCRUD",BarCrossingChecklist:"id, idbCRUD, vesselID",CGEventMission:"id, idbCRUD, vesselID",Client:"id, idbCRUD",ComponentMaintenanceCheck:"id, idbCRUD",ComponentMaintenanceSchedule:"id, idbCRUD",Consequence:"id, idbCRUD",CrewDuty:"id, idbCRUD",CrewMembers_LogBookEntrySection:"id, idbCRUD",CrewTraining_LogBookEntrySection:"id, idbCRUD",CrewWelfare_LogBookEntrySection:"id, idbCRUD",CustomisedComponentField:"id, idbCRUD, customisedLogBookComponentID",CustomisedLogBookConfig:"id, idbCRUD, customisedLogBookID",DangerousGood:"id, idbCRUD",DangerousGoodsChecklist:"id, idbCRUD, vesselID",DangerousGoodsRecord:"id, idbCRUD, tripReport_LogBookEntrySectionID",Engine:"id, idbCRUD",Engine_LogBookEntrySection:"id, idbCRUD",Engine_Usage:"id, idbCRUD, maintenanceScheduleID, engineID",Engineer_LogBookEntrySection:"id, idbCRUD",EventType:"id, idbCRUD",EventType_BarCrossing:"id, idbCRUD, tripEventID, geoLocationID, geoLocationCompletedID, barCrossingChecklistID",EventType_PassengerDropFacility:"id, idbCRUD, tripEventID, geoLocationID",EventType_PersonRescue:"id, idbCRUD, missionID, tripEventID",EventType_RestrictedVisibility:"id, idbCRUD, tripEventID, startLocationID, endLocationID",EventType_Supernumerary:"id, idbCRUD",EventType_Tasking:"id, idbCRUD, currentEntryID, groupID, pausedTaskID, openTaskID, completedTaskID, tripEventID, geoLocationID, vesselRescueID, personRescueID, towingChecklistID, parentTaskingID",EventType_VesselRescue:"id, idbCRUD, missionID, vesselLocationID, tripEventID",FavoriteLocation:"id, idbCRUD, memberID",FuelLog:"id, idbCRUD, refuellingBunkeringID, eventType_TaskingID, eventType_PassengerDropFacilityID, logBookEntryID, fuelTankID",FuelTank:"id, idbCRUD",Fuel_LogBookEntrySection:"id, idbCRUD",GeoLocation:"id, idbCRUD",Inventory:"id, idbCRUD, vesselID",InfringementNotice:"id, idbCRUD",InfringementNotice_Signature:"id, idbCRUD, infringementNoticeID",Likelihood:"id, idbCRUD",LogBookEntry:"id, idbCRUD, vehicleID",LogBookEntryOldConfigs:"id, idbCRUD, logBookEntryID",LogBookEntrySection_Signature:"id, idbCRUD, logBookEntrySectionID",LogBookSignOff_LogBookEntrySection:"id, idbCRUD",MaintenanceCheck:"id, idbCRUD, maintenanceScheduleID",MaintenanceCheck_Signature:"id, idbCRUD, maintenanceCheckID",MaintenanceSchedule:"id, idbCRUD",MaintenanceScheduleSubTask:"id, idbCRUD, componentMaintenanceScheduleID",MemberTraining_Signature:"id, idbCRUD, memberID, trainingSessionID",MissionTimeline:"id, idbCRUD, missionID, vesselRescueID, personRescueID, maintenanceCheckID, subTaskID",MitigationStrategy:"id, idbCRUD",Ports_LogBookEntrySection:"id, idbCRUD",RefuellingBunkering:"id, idbCRUD, tripEventID, geoLocationID",TripUpdate:"id, idbCRUD, tripEventID, geoLocationID",RiskFactor:"id, idbCRUD, vesselID, riskRatingID, consequenceID, likelihoodID, towingChecklistID, dangerousGoodsChecklistID, barCrossingChecklistID, type",RiskRating:"id, idbCRUD",SeaLogsMember:"id, idbCRUD",SectionMemberComment:"id, idbCRUD, logBookEntrySectionID, commentType, comment, hideComment",Supernumerary_LogBookEntrySection:"id, idbCRUD, supernumeraryID",TowingChecklist:"id, idbCRUD, vesselID",TrainingLocation:"id, idbCRUD",TrainingSession:"id, idbCRUD, trainerID, logBookEntrySectionID, logBookEntryID, vesselID, trainingLocationID, geoLocationID",TrainingSessionDue:"id, idbCRUD, memberID, trainingTypeID, vesselID, trainingSessionID",TrainingType:"id, idbCRUD",TripEvent:"id, idbCRUD, logBookEntrySectionID",TripReport_LogBookEntrySection:"id, idbCRUD",TripReport_Stop:"id, idbCRUD, stopLocationID, logBookEntrySectionID, tripReportScheduleStopID, dangerousGoodsChecklistID",VehiclePosition:"id, idbCRUD, vehicleID",Vessel:"id, idbCRUD",VesselDailyCheck_LogBookEntrySection:"id, idbCRUD",VoyageSummary_LogBookEntrySection:"id, idbCRUD",WeatherForecast:"id, idbCRUD, logBookEntryID",WeatherObservation:"id, idbCRUD, logBookEntryID, forecastID",WeatherTide:"id, idbCRUD, logBookEntryID"}),t.open().catch(function(e){console.error("[seaLogsDB] Open failed: "+e)});let n=t},56887:(e,i,o)=>{"use strict";o.r(i),o.d(i,{default:()=>t});let t=(0,o(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\logout\page.tsx#default`)},88570:(e,i,o)=>{"use strict";o.r(i),o.d(i,{default:()=>n});var t=o(3563);let n=e=>[{type:"image/x-icon",sizes:"152x152",url:(0,t.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var i=require("../../webpack-runtime.js");i.C(e);var o=e=>i(i.s=e),t=i.X(0,[864,8865,3563,9507,6451],()=>o(89655));module.exports=t})();