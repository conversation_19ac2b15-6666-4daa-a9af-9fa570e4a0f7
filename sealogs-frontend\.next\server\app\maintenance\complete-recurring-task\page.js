(()=>{var e={};e.id=3779,e.ids=[3779],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},95848:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>f,originalPathname:()=>d,pages:()=>l,routeModule:()=>h,tree:()=>c}),r(99274),r(86439),r(78398),r(57757),r(48045);var n=r(40060),s=r(33581),a=r(57567),i=r.n(a),u=r(51650),o={};for(let e in u)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>u[e]);r.d(t,o);let c=["",{children:["maintenance",{children:["complete-recurring-task",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,99274)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\maintenance\\complete-recurring-task\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,86439)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\maintenance\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\maintenance\\complete-recurring-task\\page.tsx"],d="/maintenance/complete-recurring-task/page",f={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/maintenance/complete-recurring-task/page",pathname:"/maintenance/complete-recurring-task",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},65953:(e,t,r)=>{Promise.resolve().then(r.bind(r,16480))},27536:(e,t,r)=>{Promise.resolve().then(r.bind(r,24012))},83179:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",r="minute",n="hour",s="week",a="month",i="quarter",u="year",o="date",c="Invalid Date",l=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,d=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,f=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},h="en",p={};p[h]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||"th")+"]"}};var m="$isDayjsObject",g=function(e){return e instanceof M||!(!e||!e[m])},y=function e(t,r,n){var s;if(!t)return h;if("string"==typeof t){var a=t.toLowerCase();p[a]&&(s=a),r&&(p[a]=r,s=a);var i=t.split("-");if(!s&&i.length>1)return e(i[0])}else{var u=t.name;p[u]=t,s=u}return!n&&s&&(h=s),s||!n&&h},$=function(e,t){if(g(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new M(r)},v={s:f,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+f(Math.floor(r/60),2,"0")+":"+f(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),s=t.clone().add(n,a),i=r-s<0,u=t.clone().add(n+(i?-1:1),a);return+(-(n+(r-s)/(i?s-u:u-s))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(c){return({M:a,y:u,w:s,d:"day",D:o,h:n,m:r,s:t,ms:e,Q:i})[c]||String(c||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};v.l=y,v.i=g,v.w=function(e,t){return $(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var M=function(){function f(e){this.$L=y(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[m]=!0}var h=f.prototype;return h.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(v.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(l);if(n){var s=n[2]-1||0,a=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,a)):new Date(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,a)}}return new Date(t)}(e),this.init()},h.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},h.$utils=function(){return v},h.isValid=function(){return this.$d.toString()!==c},h.isSame=function(e,t){var r=$(e);return this.startOf(t)<=r&&r<=this.endOf(t)},h.isAfter=function(e,t){return $(e)<this.startOf(t)},h.isBefore=function(e,t){return this.endOf(t)<$(e)},h.$g=function(e,t,r){return v.u(e)?this[t]:this.set(r,e)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(e,i){var c=this,l=!!v.u(i)||i,d=v.p(e),f=function(e,t){var r=v.w(c.$u?Date.UTC(c.$y,t,e):new Date(c.$y,t,e),c);return l?r:r.endOf("day")},h=function(e,t){return v.w(c.toDate()[e].apply(c.toDate("s"),(l?[0,0,0,0]:[23,59,59,999]).slice(t)),c)},p=this.$W,m=this.$M,g=this.$D,y="set"+(this.$u?"UTC":"");switch(d){case u:return l?f(1,0):f(31,11);case a:return l?f(1,m):f(0,m+1);case s:var $=this.$locale().weekStart||0,M=(p<$?p+7:p)-$;return f(l?g-M:g+(6-M),m);case"day":case o:return h(y+"Hours",0);case n:return h(y+"Minutes",1);case r:return h(y+"Seconds",2);case t:return h(y+"Milliseconds",3);default:return this.clone()}},h.endOf=function(e){return this.startOf(e,!1)},h.$set=function(s,i){var c,l=v.p(s),d="set"+(this.$u?"UTC":""),f=((c={}).day=d+"Date",c[o]=d+"Date",c[a]=d+"Month",c[u]=d+"FullYear",c[n]=d+"Hours",c[r]=d+"Minutes",c[t]=d+"Seconds",c[e]=d+"Milliseconds",c)[l],h="day"===l?this.$D+(i-this.$W):i;if(l===a||l===u){var p=this.clone().set(o,1);p.$d[f](h),p.init(),this.$d=p.set(o,Math.min(this.$D,p.daysInMonth())).$d}else f&&this.$d[f](h);return this.init(),this},h.set=function(e,t){return this.clone().$set(e,t)},h.get=function(e){return this[v.p(e)]()},h.add=function(e,i){var o,c=this;e=Number(e);var l=v.p(i),d=function(t){var r=$(c);return v.w(r.date(r.date()+Math.round(t*e)),c)};if(l===a)return this.set(a,this.$M+e);if(l===u)return this.set(u,this.$y+e);if("day"===l)return d(1);if(l===s)return d(7);var f=((o={})[r]=6e4,o[n]=36e5,o[t]=1e3,o)[l]||1,h=this.$d.getTime()+e*f;return v.w(h,this)},h.subtract=function(e,t){return this.add(-1*e,t)},h.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||c;var n=e||"YYYY-MM-DDTHH:mm:ssZ",s=v.z(this),a=this.$H,i=this.$m,u=this.$M,o=r.weekdays,l=r.months,f=r.meridiem,h=function(e,r,s,a){return e&&(e[r]||e(t,n))||s[r].slice(0,a)},p=function(e){return v.s(a%12||12,e,"0")},m=f||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n};return n.replace(d,function(e,n){return n||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return v.s(t.$y,4,"0");case"M":return u+1;case"MM":return v.s(u+1,2,"0");case"MMM":return h(r.monthsShort,u,l,3);case"MMMM":return h(l,u);case"D":return t.$D;case"DD":return v.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return h(r.weekdaysMin,t.$W,o,2);case"ddd":return h(r.weekdaysShort,t.$W,o,3);case"dddd":return o[t.$W];case"H":return String(a);case"HH":return v.s(a,2,"0");case"h":return p(1);case"hh":return p(2);case"a":return m(a,i,!0);case"A":return m(a,i,!1);case"m":return String(i);case"mm":return v.s(i,2,"0");case"s":return String(t.$s);case"ss":return v.s(t.$s,2,"0");case"SSS":return v.s(t.$ms,3,"0");case"Z":return s}return null}(e)||s.replace(":","")})},h.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},h.diff=function(e,o,c){var l,d=this,f=v.p(o),h=$(e),p=(h.utcOffset()-this.utcOffset())*6e4,m=this-h,g=function(){return v.m(d,h)};switch(f){case u:l=g()/12;break;case a:l=g();break;case i:l=g()/3;break;case s:l=(m-p)/6048e5;break;case"day":l=(m-p)/864e5;break;case n:l=m/36e5;break;case r:l=m/6e4;break;case t:l=m/1e3;break;default:l=m}return c?l:v.a(l)},h.daysInMonth=function(){return this.endOf(a).$D},h.$locale=function(){return p[this.$L]},h.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=y(e,t,!0);return n&&(r.$L=n),r},h.clone=function(){return v.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},f}(),x=M.prototype;return $.prototype=x,[["$ms",e],["$s",t],["$m",r],["$H",n],["$W","day"],["$M",a],["$y",u],["$D",o]].forEach(function(e){x[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),$.extend=function(e,t){return e.$i||(e(t,M,$),e.$i=!0),$},$.locale=y,$.isDayjs=g,$.unix=function(e){return $(1e3*e)},$.en=p[h],$.Ls=p,$.p={},$},e.exports=t()},18479:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,s=Array(n);++r<n;)s[r]=t(e[r],r,e);return s}},22060:(e,t,r)=>{var n=r(51858),s=r(18479),a=r(55813),i=r(15903),u=1/0,o=n?n.prototype:void 0,c=o?o.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(a(t))return s(t,e)+"";if(i(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-u?"-0":r}},15903:(e,t,r)=>{var n=r(55296),s=r(48377);e.exports=function(e){return"symbol"==typeof e||s(e)&&"[object Symbol]"==n(e)}},16266:(e,t,r)=>{var n=r(22060);e.exports=function(e){return null==e?"":n(e)}},3233:(e,t,r)=>{var n=r(16266),s=0;e.exports=function(e){var t=++s;return n(e)+t}},16480:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(98768),s=r(69424);r(60343);var a=r(76342),i=r(72548),u=r(26100);function o({taskID:e,lastCompletedDate:t=!1}){let r=(0,s.useRouter)(),[o]=(0,i.D)(a.VgQ,{onCompleted:e=>{r.push("/maintenance")},onError:e=>{console.error("updateMaintenanceChecksEntry error",e)}});return n.jsx("div",{children:n.jsx(u.Z,{message:"Completing Task"})})}function c(){let e=(0,s.useSearchParams)(),t=e.get("taskID")??0,r=e.get("lastCompletedDate")??"";return n.jsx(o,{taskID:t,lastCompletedDate:r})}},24012:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(98768),s=r(64837);function a({children:e}){return n.jsx(s.Z,{children:e})}r(60343)},99274:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\maintenance\complete-recurring-task\page.tsx#default`)},86439:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\maintenance\layout.tsx#default`)},84961:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},46020:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},23379:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},7671:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[864,8865,3563,6263,8189,9507,7602,6451,4234,2925,5394,4837,6342],()=>r(95848));module.exports=n})();