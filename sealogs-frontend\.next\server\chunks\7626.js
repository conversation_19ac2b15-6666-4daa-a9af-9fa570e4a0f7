"use strict";exports.id=7626,exports.ids=[7626],exports.modules={75546:(e,t,n)=>{n.d(t,{Br:()=>p,fU:()=>c,o0:()=>f,p6:()=>l,vq:()=>m});var r=n(83179),s=n.n(r),i=n(7678),o=n.n(i),a=n(14826),d=n.n(a);let l=(e="",t=!0)=>{let n;if(o()(d()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[n,r,s]=e.split("-"),i=t?n.slice(-2):n,o=parseInt(s,10).toString().padStart(2,"0"),a=parseInt(r,10).toString().padStart(2,"0");return`${o}/${a}/${i}`}if(!(n=e&&"object"==typeof e?s()(e.toString()):s()(e)).isValid())return"";let r=n.format("DD"),i=n.format("MM"),a=t?n.format("YY"):n.format("YYYY");return`${r}/${i}/${a}`},f=(e="",t=!0)=>{let n;if(o()(d()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[n,r,s]=e.split("-"),i=t?n.slice(-2):n,o=parseInt(s,10).toString().padStart(2,"0"),a=parseInt(r,10).toString().padStart(2,"0");return`${o}/${a}/${i} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[n,r]=e.split(" "),[s,i,o]=n.split("-"),a=t?s.slice(-2):s,d=r.split(":"),l=d[0].padStart(2,"0"),f=d[1].padStart(2,"0"),m=parseInt(o,10).toString().padStart(2,"0"),p=parseInt(i,10).toString().padStart(2,"0");return`${m}/${p}/${a} ${l}:${f}`}if(!(n=e&&"object"==typeof e?s()(e.toString()):s()(e)).isValid())return"";let r=n.format("DD"),i=n.format("MM"),a=t?n.format("YY"):n.format("YYYY"),l=n.format("HH:mm");return`${r}/${i}/${a} ${l}`},m=(e="")=>o()(d()(e))?"":s()(e).format("YYYY-MM-DD HH:mm:ss"),p=(e="")=>o()(d()(e))?new Date:new Date(`${e}T10:00:00Z`),c=(e,t)=>{let n=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),r=e=>e.includes(" ")?e.replace(" ","T"):e,s=e=>{if(!e||"string"!=typeof e)return null;if(n(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(r(e))},i=s(e),o=s(t);return!i||!o||isNaN(i.getTime())||isNaN(o.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):o>i}},7626:(e,t,n)=>{n.d(t,{Z:()=>C});var r=n(98768),s=n(60343),i=n(79418),o=n(13842),a=n(37042),d=n(7678),l=n.n(d),f=n(14826),m=n.n(f),p=n(43777),c=n.n(p),u=n(48967),g=n(58688),h=n(97892),x=n(75546),y=n(25394),v=n(60797),D=n(56937),b=n(50058),_=n(45519);let T=(0,_.ZP)`
    query {
        readLogBookEntries {
            nodes {
                id
                startDate
                archived
                masterID
            }
        }
    }
`,I=(0,_.ZP)`
    query ReadCrewMembers_LogBookEntrySections(
        $filter: CrewMembers_LogBookEntrySectionFilterFields = {}
    ) {
        readCrewMembers_LogBookEntrySections(filter: $filter) {
            nodes {
                id
                crewMember {
                    id
                    firstName
                    surname
                }
            }
        }
    }
`,$=(0,_.ZP)`
    query ReadTrainingSessionDues(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingSessionDueFilterFields = {}
    ) {
        readTrainingSessionDues(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: {
                dueDate: ASC
                trainingTypeID: ASC
                vesselID: ASC
                memberID: ASC
            }
        ) {
            nodes {
                id
                dueDate
                memberID
                member {
                    id
                    firstName
                    surname
                }
                vesselID
                vessel {
                    id
                    title
                    seaLogsMembers {
                        nodes {
                            id
                            firstName
                            surname
                        }
                    }
                }
                trainingTypeID
                trainingType {
                    id
                    title
                }
            }
        }
    }
`,S=(0,_.ZP)`
    query ReadComponentMaintenanceCheckList(
        $inventoryID: Int!
        $vesselID: Int!
    ) {
        readComponentMaintenanceCheckList(
            inventoryID: $inventoryID
            vesselID: $vesselID
        ) {
            list
        }
    }
`,P=(0,_.ZP)`
    query ReadInventories(
        $limit: Int = 100
        $offset: Int = 0
        $filter: InventoryFilterFields = {}
    ) {
        readInventories(limit: $limit, offset: $offset, filter: $filter) {
            nodes {
                id
                item
            }
        }
    }
`,j=(0,_.ZP)`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                firstName
                surname
                trainingSessionsDue {
                    nodes {
                        id
                        dueDate
                        vesselID
                    }
                }
            }
        }
    }
`,L=(0,_.ZP)`
    query ReadOneLogBookEntry($logbookEntryId: ID!) {
        readOneLogBookEntry(filter: { id: { eq: $logbookEntryId } }) {
            id
            master {
                id
                firstName
                surname
            }
            vehicle {
                id
                seaLogsMembers {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
            }
        }
    }
`,N=(0,_.ZP)`
    query ReadTrainingSessions(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingSessionFilterFields = {}
    ) {
        readTrainingSessions(limit: $limit, offset: $offset, filter: $filter) {
            nodes {
                id
                date
                trainingSummary
                vessel {
                    id
                    title
                }
            }
        }
    }
`,k=(0,_.ZP)`
    query ReadVessels(
        $limit: Int
        $offset: Int
        $filter: VesselFilterFields = {}
    ) {
        readVessels(filter: $filter, limit: $limit, offset: $offset) {
            nodes {
                id
                archived
                title
            }
        }
    }
`;function C({initialView:e="dayGridMonth",isDashboard:t=!1,showWeekends:n=!0}){let[d,f]=(0,s.useState)([]),[p,_]=(0,s.useState)([]),[C,w]=(0,s.useState)(),[E,M]=(0,s.useState)([]),[q,B]=(0,s.useState)(),[Z,A]=(0,s.useState)(!0),[V,Y]=(0,s.useState)([]),[F,O]=(0,s.useState)({}),[R,z]=(0,s.useState)([]),[H,U]=(0,s.useState)(0),[W,G]=(0,s.useState)([]),[J,K]=(0,s.useState)([]),[Q,X]=(0,s.useState)([]),[ee,et]=(0,s.useState)([]),[en,er]=(0,s.useState)([]),[es,ei]=(0,s.useState)([]),[eo,ea]=(0,s.useState)([]),[ed,el]=(0,s.useState)({}),[ef,em]=(0,s.useState)(!1),[ep,ec]=(0,s.useState)([]),[eu,eg]=(0,s.useState)([]);(0,b.k)();let[eh,ex]=(0,s.useState)({}),[ey,ev]=(0,s.useState)({totalCount:0,hasNextPage:!1,hasPreviousPage:!1}),eD=async e=>{el(e),e?.event?._def?.extendedProps?.type==="Log Book Entry"&&eL(e?.event?._def?.extendedProps?.other_info?.id),em(!0)};function eb(e){return c()(m()(e)).replace(/<.*?>/g,"")}let[e_]=(0,i.t)(S,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=[],n=e.readComponentMaintenanceCheckList?e.readComponentMaintenanceCheckList[0].list.filter(e=>"Open"===e.status):[];n.forEach(e=>{t.push({title:"Task :"+eb(e.name),start:new Date(e.expires),type:"Task",other_info:e,extendedProps:{type:"Task",other_info:e},classNames:["fc-event-task"]})}),n&&(f([...d,...t]),_([...p,...t]))},onError:e=>{console.error("queryMaintenanceChecks error",e)}}),[eT]=(0,i.t)(k,{fetchPolicy:"cache-and-network",onCompleted:e=>{e.readVessels.nodes&&eE(e.readVessels.nodes)},onError:e=>{console.error("queryVessels error",e)}}),[eI,{loading:e$}]=(0,i.t)(N,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=[],n=e.readTrainingSessions.nodes;n&&(n.forEach(e=>{e.date&&t.push({title:"Training Completed :"+eb(e.trainingSummary),start:new Date(e.date),type:"Completed Training",other_info:e,extendedProps:{type:"Completed Training",other_info:e},classNames:["fc-event-training-completed"]})}),f([...d,...t]),_([...p,...t]))},onError:e=>{console.error("queryTrainingList error",e)}}),[eS,{loading:eP}]=(0,i.t)($,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=[],n=e.readTrainingSessionDues.nodes;n&&(Object.values(n.filter(e=>e.vessel.seaLogsMembers.nodes.some(t=>t.id===e.memberID)).map(e=>({...e,status:(0,o.nu)(e)})).filter(e=>e.status.isOverdue||!1===e.status.isOverdue&&!0===e.status.dueWithinSevenDays).reduce((e,t)=>{let n=`${t.vesselID}-${t.trainingTypeID}-${t.dueDate}`;return e[n]||(e[n]={id:t.id,vesselID:t.vesselID,vessel:t.vessel,trainingTypeID:t.trainingTypeID,trainingType:t.trainingType,dueDate:t.dueDate,status:t.status,members:[]}),e[n].members.push(t.member),e},{})).map(e=>{let t=e.members.reduce((e,t)=>{let n=e.find(e=>e.id===t.id);return n?(n.firstName=t.firstName,n.surname=t.surname):e.push(t),e},[]);return{id:e.id,vesselID:e.vesselID,vessel:e.vessel,trainingTypeID:e.trainingTypeID,trainingType:e.trainingType,status:e.status,dueDate:e.dueDate,members:t}}).forEach(e=>{e.dueDate&&t.push({title:"Training Due :"+eb(e.trainingType.title),start:new Date(e.dueDate),type:"Training Due",other_info:e,extendedProps:{type:"Training Due",other_info:e},classNames:["fc-event-training-due"]})}),n&&(f([...d,...t]),_([...p,...t])))},onError:e=>{console.error("readTrainingSessionDues error",e)}}),[ej]=(0,i.t)(T,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=[],n=e.readLogBookEntries.nodes;n.forEach(e=>{e.startDate&&t.push({title:"Log Book Entry",start:new Date(e.startDate),type:"Log Book Entry",other_info:e,extendedProps:{type:"Log Book Entry",other_info:e},classNames:["fc-event-logbook"]})}),n&&(f([...d,...t]),_([...p,...t]))},onError:e=>{console.error("queryLogBookEntries error",e)}}),eL=async e=>{await eN({variables:{logbookEntryId:+e}}),await ek({variables:{filter:{logBookEntryID:{eq:+e}}}})},[eN]=(0,i.t)(L,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneLogBookEntry;t&&O(t)},onError:e=>{console.error("queryLogBookEntry error",e)}}),[ek]=(0,i.t)(I,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readCrewMembers_LogBookEntrySections;t.nodes.length>0&&z(t.nodes.map(e=>e.crewMember))},onError:e=>{console.error("queryLogBookEntry error",e)}});function eC(e){return r.jsx(r.Fragment,{children:r.jsx("div",{onClick:()=>eD(e),className:(0,D.cn)("cursor-pointer overflow-hidden text-wrap fc-event-title",{"line-clamp-3":t}),children:e.event.title})})}let ew=(e,t)=>{if("vessel"===e&&t&&t.vesselID&&t.vesselID.eq&&!isNaN(t.vesselID.eq)&&f(p.filter(e=>{let n=t.vesselID.eq.toString(),r=null;return e?.other_info?.vessel?.id?r=e.other_info.vessel.id:e?.other_info?.vehicle?.id?r=e.other_info.vehicle.id:e?.other_info?.basicComponent?.vesselID&&(r=e.other_info.basicComponent.vesselID.toString()),r===n})),"Module"===e&&null!==t&&t&&t.moduleName&&f(p.filter(e=>e.type===t.moduleName)),"member"===e&&null!==t&&t&&t.assignedToID){let e=t.assignedToID.eq.toString();f(p.filter((t,n)=>{if(!t||!t.other_info)return!1;let r=!1;return"Training Due"===t.type?(t.other_info.members&&Array.isArray(t.other_info.members)&&(r=t.other_info.members.some(t=>t.id===e)),!r&&t.other_info.member&&(r=t.other_info.member.id===e)):"Task"===t.type?t.other_info.assignedTo&&(r=t.other_info.assignedTo.id===e):"Log Book Entry"===t.type?t.other_info.masterID&&(r=t.other_info.masterID.toString()===e):(t.type,r=!1),r}))}},eE=e=>{X(e.filter(e=>!e.archived))},[eM,{loading:eq}]=(0,i.t)(j,{fetchPolicy:"cache-and-network",onCompleted:e=>(eB(e.readSeaLogsMembers.nodes),ev(e.readSeaLogsMembers.pageInfo),e.readSeaLogsMembers.nodes),onError:e=>{console.error("\uD83D\uDD0D [CALENDAR CREW] queryCrewMembers error",e)}}),eB=e=>{let t=e.filter((e,t,n)=>n.findIndex(t=>t.id===e.id)===t);K((0,o.Vp)(t,Q).filter((e,t,n)=>n.findIndex(t=>t.id===e.id)===t))},[eZ]=(0,i.t)(P,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readInventories.nodes;t&&et(t)},onError:e=>{console.error("queryInventoriesEntry error",e)}});return(0,r.jsxs)(r.Fragment,{children:[!1===t?r.jsx(a.Z,{onChange:({type:e,data:t})=>{let n={...eh};if("Module"===e&&(t&&null!==t&&!l()(t?.value)?n.moduleName=t?.value:(delete n.item,n={},f(p))),"vessel"===e){if(t&&Array.isArray(t)&&t.length>0){let e=t[0];n.vesselID={eq:+e?.value}}else t&&!Array.isArray(t)&&t.value?n.vesselID={eq:+t.value}:(delete n.vesselID,f(p))}"member"===e&&(t?n.assignedToID={eq:+t.value}:(delete n.assignedToID,f(p))),ex(n),ew(e,n)}}):null,!1===t?r.jsx(y.Zb,{className:"mt-4",children:r.jsx(u.Z,{plugins:[g.Z,h.Z],initialView:e,weekends:n,events:d,eventContent:eC,height:"auto",contentHeight:500})}):r.jsx(u.Z,{plugins:[g.Z,h.Z],initialView:e,weekends:n,events:d,eventContent:eC,height:"auto",contentHeight:500}),r.jsx(y.h9,{openDialog:ef,setOpenDialog:em,size:"lg",title:ed?.event?._def?.extendedProps?.type==="Task"?"Task Details":ed?.event?._def?.extendedProps?.type==="Completed Training"?"Training Completed":ed?.event?._def?.extendedProps?.type==="Training Due"?"Training Due":ed?.event?._def?.extendedProps?.type==="Log Book Entry"?"Log Book Entry":"Event Details",handleCreate:ed?.event?._def?.extendedProps?.type==="Task"?()=>window.open(`/maintenance?taskID=${ed?.event?._def?.extendedProps?.other_info?.id}`,"_self"):ed?.event?._def?.extendedProps?.type==="Completed Training"||ed?.event?._def?.extendedProps?.type==="Training Due"?()=>window.open(`/crew-training/info?id=${ed?.event?._def?.extendedProps?.other_info?.id}`,"_self"):ed?.event?._def?.extendedProps?.type==="Log Book Entry"&&C?()=>window.open(`/log-entries?vesselID=${F?.vehicle?.id}&logentryID=${F?.id}`,"_self"):void 0,actionText:ed?.event?._def?.extendedProps?.type==="Task"?"Open Task":ed?.event?._def?.extendedProps?.type==="Completed Training"||ed?.event?._def?.extendedProps?.type==="Training Due"?"Open Training":ed?.event?._def?.extendedProps?.type==="Log Book Entry"&&C?"Open Log Book":void 0,cancelText:"Close",children:(0,r.jsxs)("div",{className:"space-y-6",children:[ed?.event?._def?.extendedProps?.type==="Task"&&(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(v.Label,{label:"Task",children:r.jsx("p",{className:"text-foreground",children:eb(ed?.event?._def?.extendedProps?.other_info?.name)})}),r.jsx(v.Label,{label:"Due Date",children:r.jsx("p",{className:"text-foreground",children:(0,x.p6)(ed?.event?._def?.extendedProps?.other_info?.expires)})}),r.jsx(v.Label,{label:"Assigned By",children:r.jsx("p",{className:"text-foreground",children:J.find(e=>e.id===ed?.event?._def?.extendedProps?.other_info?.assignedByID)?.firstName||"Unknown"})}),r.jsx(v.Label,{label:"Assigned To",children:r.jsx("p",{className:"text-foreground",children:J.find(e=>e.id===ed?.event?._def?.extendedProps?.other_info?.assignedTo?.id)?.firstName||"Unknown"})}),r.jsx(v.Label,{label:"Inventory Item",children:r.jsx("p",{className:"text-foreground",children:E?E.item:"Item"})}),r.jsx(v.Label,{label:"Vessel",children:r.jsx("p",{className:"text-foreground",children:ed?.event?._def?.extendedProps?.other_info?.basicComponent?.title})}),ed?.event?._def?.extendedProps?.other_info?.comments!=="<p>This is the description of the task</p>"&&r.jsx(v.Label,{label:"Comment",children:r.jsx("p",{className:"text-foreground",children:eb(ed?.event?._def?.extendedProps?.other_info?.comments)})}),r.jsx(v.Label,{label:"Severity",children:r.jsx("p",{className:"text-foreground",children:ed?.event?._def?.extendedProps?.other_info?.severity})})]}),ed?.event?._def?.extendedProps?.type==="Completed Training"&&(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(v.Label,{label:"Training Summary",children:r.jsx("div",{dangerouslySetInnerHTML:{__html:ed?.event?._def?.extendedProps?.other_info?.trainingSummary}})}),r.jsx(v.Label,{label:"Trainer",children:(0,r.jsxs)("p",{className:"text-foreground",children:[ed?.event?._def?.extendedProps?.other_info?.trainer?.firstName," ",ed?.event?._def?.extendedProps?.other_info?.trainer?.surname]})}),r.jsx(v.Label,{label:"Members",children:r.jsx("p",{className:"text-foreground",children:F?.vehicle?.seaLogsMembers?.nodes?.map((e,t)=>r.jsxs("span",{children:[e.firstName," ",e.surname,t<F.vehicle.seaLogsMembers.nodes.length-1?", ":""]},`completed-training-member-${e.id}-${t}`))})}),r.jsx(v.Label,{label:"Training Types",children:r.jsx("p",{className:"text-foreground",children:ed?.event?._def?.extendedProps?.other_info?.trainingTypes?.nodes?.map((e,t)=>r.jsxs("span",{children:[e.title,t<ed.event._def.extendedProps.other_info.trainingTypes.nodes.length-1?", ":""]},`completed-training-type-${e.id}-${t}`))})}),r.jsx(v.Label,{label:"Completed Date",children:r.jsx("p",{className:"text-foreground",children:(0,x.p6)(ed?.event?._def?.extendedProps?.other_info?.date)})}),r.jsx(v.Label,{label:"Vessel",children:r.jsx("p",{className:"text-foreground",children:ed?.event?._def?.extendedProps?.other_info?.vessel?.title})})]}),ed?.event?._def?.extendedProps?.type==="Training Due"&&(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(v.Label,{label:"Training Summary",children:r.jsx("p",{className:"text-foreground",children:ed?.event?._def?.extendedProps?.other_info?.trainingType?.title})}),r.jsx(v.Label,{label:"Trainer",children:(0,r.jsxs)("p",{className:"text-foreground",children:[ed?.event?._def?.extendedProps?.other_info?.trainer?.firstName," ",ed?.event?._def?.extendedProps?.other_info?.trainer?.surname]})}),r.jsx(v.Label,{label:"Members",children:r.jsx("p",{className:"text-foreground",children:F?.vehicle?.seaLogsMembers?.nodes?.map((e,t)=>r.jsxs("span",{children:[e.firstName," ",e.surname,t<F.vehicle.seaLogsMembers.nodes.length-1?", ":""]},`training-due-member-${e.id}-${t}`))})}),r.jsx(v.Label,{size:"sm",label:"Training Types",children:r.jsx("p",{className:"text-foreground",children:ed?.event?._def?.extendedProps?.other_info?.trainingTypes?.nodes?.map((e,t)=>r.jsxs("span",{children:[e.title,t<ed.event._def.extendedProps.other_info.trainingTypes.nodes.length-1?", ":""]},`training-due-type-${e.id}-${t}`))})}),r.jsx(v.Label,{size:"sm",label:"Due Date",children:r.jsx("p",{className:"text-foreground",children:(0,x.p6)(ed?.event?._def?.extendedProps?.other_info?.dueDate)})}),r.jsx(v.Label,{size:"sm",label:"Vessel",children:r.jsx("p",{className:"text-foreground",children:ed?.event?._def?.extendedProps?.other_info?.vessel?.title})})]}),ed?.event?._def?.extendedProps?.type==="Log Book Entry"&&(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(v.Label,{size:"sm",label:"Vessel",children:r.jsx("p",{className:"text-foreground",children:C?.title||"No Vessel"})}),r.jsx(v.Label,{size:"sm",label:"Date",children:r.jsx("p",{className:"text-foreground",children:(0,x.p6)(ed?.event?._def?.extendedProps?.other_info?.startDate)})}),r.jsx(v.Label,{size:"sm",label:"Master",children:(0,r.jsxs)("p",{className:"text-foreground",children:[F?.master?.firstName," ",F?.master?.surname]})}),r.jsx(v.Label,{size:"sm",label:"Crew",children:r.jsx("p",{className:"text-foreground",children:R.map((e,t)=>(0,r.jsxs)("span",{children:[e.firstName," ",e.surname,t<R.length-1?", ":""]},`logbook-crew-${e.id}-${t}`))})})]})]})})]})}}};