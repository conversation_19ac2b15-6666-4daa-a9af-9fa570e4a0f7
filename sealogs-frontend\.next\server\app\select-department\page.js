(()=>{var e={};e.id=5102,e.ids=[5102],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},23120:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>p,routeModule:()=>m,tree:()=>d}),s(11042),s(78398),s(57757),s(48045);var r=s(40060),a=s(33581),n=s(57567),l=s.n(n),i=s(51650),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d=["",{children:["select-department",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,11042)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\select-department\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\select-department\\page.tsx"],c="/select-department/page",u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/select-department/page",pathname:"/select-department",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81495:(e,t,s)=>{Promise.resolve().then(s.bind(s,89660))},89660:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g});var r=s(98768),a=s(60343),n=s(69424),l=s(72548),i=s(76342),o=s(49296),d=s(26100),p=s(60797),c=s(64837),u=s(74602);function m(){let e=(0,n.useSearchParams)().get("from"),t=(0,n.useRouter)(),[s,m]=(0,a.useState)(null),[x,g]=(0,a.useState)(0),[h,f]=(0,a.useState)(""),[v,{loading:b}]=(0,l.D)(i.AXh,{onCompleted:e=>{t.push("/")},onError:e=>{console.error("mutationUpdateUser error",e),f(e.message)}});async function j(){f("");let e=localStorage.getItem("userId")??0;if(0==+e){f("Opps, there was an error with your login. Please try and login again.");return}await v({variables:{input:{id:+e,currentDepartmentID:x}}})}let y=r.jsx("div",{className:"flex h-screen flex-col items-center justify-center gap-4 ",children:(0,r.jsxs)("div",{className:"    shadow rounded py-8 px-4 w-96",children:[r.jsx(u.H1,{children:"Select A Department"}),r.jsx("div",{className:"text-red-500 text-center py-4",children:r.jsx("small",{children:h})}),(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[r.jsx(p.Label,{htmlFor:"client ",children:"Department"}),r.jsx(o.Z,{value:x,onChange:e=>{g(e.value)}})]}),r.jsx("div",{className:"md:flex md:justify-between py-4 gap-2",children:r.jsx("div",{children:r.jsx("button",{onClick:j,type:"button",className:"0  px-4 py-2 rounded w-full md:w-auto my-2",disabled:b,children:"Select Department"})})})]})});return"login"!==e&&(y=r.jsx(c.Z,{children:y})),s?y:r.jsx(r.Fragment,{children:null===s?r.jsx(d.Z,{}):"login"!==e?r.jsx(c.Z,{children:r.jsx(d.Z,{message:"Departments are not enabled, please enable the departments from settings to use departments."})}):r.jsx(d.Z,{message:"Departments are not enabled, please enable the departments from settings to use departments."})})}var x=s(46776);let g=()=>((0,a.useEffect)(()=>{(0,x.UU)()},[]),r.jsx("div",{children:r.jsx(m,{})}))},49296:(e,t,s)=>{"use strict";s.d(t,{Z:()=>p});var r=s(98768),a=s(60343),n=s(79418),l=s(94060),i=s(12166),o=s.n(i),d=s(81524);let p=({value:e=0,onChange:t,excludeId:s=0,parentsOnly:i=!1,disabled:p=!1})=>{let[c,u]=(0,a.useState)([]),[m,x]=(0,a.useState)(!0),[g,h]=(0,a.useState)({}),[f,v]=(0,a.useState)([]),b={value:"0",label:"All Departments",level:0},j=(e,t=0,s=0)=>e.filter(e=>+e.parentID===t).flatMap(t=>{let r=j(e,+t.id,s+1);return[{...t,level:s},...r]}),[y,{loading:P}]=(0,n.t)(l.d5,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readDepartments.nodes;i&&(t=t.filter(e=>0==+e.parentID)),u(t),v([b,...j(t).map(e=>({value:e.id,label:o()(` `,e.level)+e.title,level:e.level}))])},onError:e=>{console.error("readDepartments",e)}}),S=async()=>{await y({variables:{filter:{id:{ne:s}}}})};return(0,a.useEffect)(()=>{if(m&&(S(),x(!1)),e>0){let t=c.find(t=>t.id===e);h({value:t?.id,label:t?.title})}else h(b)},[m,e,c]),r.jsx("div",{className:"flex items-center",children:r.jsx(d.Combobox,{options:f.map(e=>({...e,value:String(e.value)})),value:g,onChange:t,placeholder:"Select department",isDisabled:p,isLoading:P})})}},11042:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\select-department\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[864,8865,3563,6263,8189,9507,7602,4316,6451,4234,2925,5394,4837,6342],()=>s(23120));module.exports=r})();