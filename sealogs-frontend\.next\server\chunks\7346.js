"use strict";exports.id=7346,exports.ids=[7346],exports.modules={52241:(e,t,l)=>{l.d(t,{P:()=>n});var a=l(60343),o=l(79418),i=l(94060);let n=()=>{let[e,t]=(0,a.useState)({}),[l,n]=(0,a.useState)(!0),[r]=(0,o.t)(i.N5,{fetchPolicy:"cache-and-network",onCompleted:e=>{if(e.readVessels.nodes){let l={};e.readVessels.nodes.forEach(e=>{l[e.id]={...e,vesselPosition:e.vehiclePositions?.nodes?.[0]||null}}),t(l),n(!1)}},onError:e=>{console.error("queryVesselsWithIcons error",e),n(!1)}});return(0,a.useEffect)(()=>{r({variables:{filter:{archived:{eq:!1}}}})},[]),{vesselIconData:e,loading:l,getVesselWithIcon:(t,l)=>e[t]||l||{id:t,title:"Unknown Vessel"}}}},14692:(e,t,l)=>{l.d(t,{Z:()=>E});var a=l(98768),o=l(60343),i=l(81524),n=l(76342),r=l(72548),s=l(79418),d=l(34376),c=l(7678),u=l.n(c),m=l(14826),g=l.n(m),h=l(94060),f=l(83179),v=l.n(f),b=l(86708);class x{async save(e){try{let l=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t]));var t=[];+l.geoLocationID>0&&+l.memberID>0&&(t=await b.Z.FavoriteLocation.where("geoLocationID").equals(`${l.geoLocationID}`).and(e=>e.where("memberID").equals(`${l.memberID}`))),t.length>0?await b.Z.FavoriteLocation.update(t[0].id,{...l,usage:t[0].usage+1,__typename:"FavoriteLocation",idbCRUD:"Update",idbCRUDDate:v()().format("YYYY-MM-DD HH:mm:ss")}):await b.Z.FavoriteLocation.put({...l,__typename:"FavoriteLocation",idbCRUD:"Update",idbCRUDDate:v()().format("YYYY-MM-DD HH:mm:ss")});let a=await this.getById(l.id);return console.log("FavoriteLocation save",e,a),a}catch(t){console.error("FavoriteLocation save",e,t)}}async getAll(){try{let e=await b.Z.FavoriteLocation.toArray();return console.log("FavoriteLocation getAll",e),e}catch(e){console.error("FavoriteLocation getAll",e)}}async getById(e){try{let t=await b.Z.FavoriteLocation.get(`${e}`);return console.log("FavoriteLocation getById",e,t),t}catch(t){console.error("FavoriteLocation getById",e,t)}}async getByIds(e){try{let t=await b.Z.FavoriteLocation.where("id").anyOf(e).toArray();return console.log("FavoriteLocation getByIds",e,t),t}catch(t){console.error("FavoriteLocation getByIds",e,t)}}async getByMemberID(e){try{let t=await b.Z.FavoriteLocation.where("memberID").equals(`${e}`).toArray();return console.log("FavoriteLocation getByMemberID",e,t),t}catch(t){console.error("FavoriteLocation getByMemberID",e,t)}}async bulkAdd(e){try{return await b.Z.FavoriteLocation.bulkAdd(e),console.log("FavoriteLocation bulkAdd",e),e}catch(t){if("BulkError"===t.name){let l=t.failuresByPos.map(e=>e.key),a=e.filter(e=>!l.includes(e.id));return await b.Z.FavoriteLocation.bulkAdd(a),console.log("FavoriteLocation bulkAdd::BulkError",e,t),e}console.error("FavoriteLocation bulkAdd",e,t)}}async setProperty(e){try{if(e){let t=await b.Z.FavoriteLocation.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=v()().format("YYYY-MM-DD HH:mm:ss"),await b.Z.FavoriteLocation.update(e,t),console.log("FavoriteLocation setProperty",e,t),t}}catch(t){console.error("FavoriteLocation setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await b.Z.FavoriteLocation.update(e.id,e)})),console.log("FavoriteLocation multiUpdate",e)}catch(t){console.error("FavoriteLocation multiUpdate",e,t)}}}var p=l(1971),w=l(10090),L=l(71890),N=l(60797),j=l(39544),y=l(74932),I=l(49008),F=l(26509),S=l(70906),D=l(25394);function E({setCurrentLocation:e,handleLocationChange:t,currentEvent:l,offline:c=!1,showAddNewLocation:m=!0,showUseCoordinates:f=!0,showCurrentLocation:v=!0}){let b;let[E,C]=(0,o.useState)([]),[P,A]=(0,o.useState)(!0),[q,M]=(0,o.useState)(!1),[B,Z]=(0,o.useState)(!1),[k,U]=(0,o.useState)(),[Y,$]=(0,o.useState)([]),{toast:V}=(0,d.pm)(),[T,O]=(0,o.useState)(!1),[R,G]=(0,o.useState)(),[H,_]=(0,o.useState)(!1),[z,J]=(0,o.useState)(!1),[W,K]=(0,o.useState)({latitude:0,longitude:0}),Q=new x,X=new p.Z,ee=()=>{M(!1)},et=async e=>{if(!e){U(null),t(null);return}if("newLocation"===e.value)O(!1),ex("new");else{U(e),t(e);let l=localStorage.getItem("userId");if(null!==l&&+l>0){if(c){let t=Y.find(t=>t.geoLocationID===e.value);if(t){let e={...t,usage:(t.usage||0)+1};await Q.save(e),$(Y.map(l=>l.id===t.id?e:l))}else{let t={id:(0,w.lY)(),memberID:+localStorage.getItem("userId"),geoLocationID:+e.value,usage:999};await Q.save(t),$([t,...Y])}}else{let t=Y.find(t=>t.geoLocationID===e.value);el({variables:{input:{memberID:+localStorage.getItem("userId"),geoLocationID:+e.value}},onCompleted:l=>{t?ei({variables:{userID:+localStorage.getItem("userId")}}):$([{id:l.createFavoriteLocation.id,geoLocationID:e.value,usage:999},...Y])}})}}}},[el]=(0,r.D)(n._AY,{onError:e=>{console.error("Error creating favorite location:",e)}}),ea=async()=>{let e=document.getElementById("new-location-title"),l=document.getElementById("new-location-latitude"),a=document.getElementById("new-location-longitude"),o=e.value?.trim()||"New location",i=T?T.value:null,n=l.value?.trim()||"0",r=a.value?.trim()||"0",s=parseFloat(n),d=parseFloat(r),u={input:{title:o,lat:isNaN(s)?0:s,long:isNaN(d)?0:d,parentLocationID:i}};if(c){let e=(0,w.lY)(),l=await X.save({...u.input,id:e});R?.length>0?G([...R,l]):G([l]);let a={label:l.title,value:l.id,latitude:l.lat,longitude:l.long};U(a),t(a);let o=localStorage.getItem("userId");if(null!==o&&+o>0){let e={id:(0,w.lY)(),memberID:+o,geoLocationID:l.id,usage:999};await Q.save(e),$([e,...Y])}_(!1),J(!1),ev(!1)}else eo({variables:u})},[eo]=(0,r.D)(n.T,{onCompleted:e=>{let l=e.createGeoLocation;R?.length>0?G([...R,l]):G([l]);let a={label:l.title,value:l.id,latitude:l.lat,longitude:l.long};U(a),t(a);let o=localStorage.getItem("userId");null!==o&&+o>0&&el({variables:{input:{memberID:+o,geoLocationID:+l.id}},onCompleted:e=>{$([{id:e.createFavoriteLocation.id,geoLocationID:l.id,usage:999},...Y])}}),_(!1),J(!1),ev(!1)},onError:e=>{_(!1),J(!1),ev(!1),console.error("Error creating GeoLocation",e),V({variant:"destructive",title:"Error",description:"Error creating GeoLocation"})}}),[ei]=(0,s.t)(h.To,{fetchPolicy:"cache-and-network",onCompleted:e=>{$(e.readFavoriteLocations.nodes)},onError:e=>{console.error("onError",e)}}),en=(e,t,l)=>Math.sqrt(Math.pow(e.lat-t,2)+Math.pow(e.long-l,2)),er=(e,t)=>{if(0===E.length){Z(null),es(e,t);return}let l=E.reduce((l,a)=>Math.sqrt(Math.pow(l.lat-e,2)+Math.pow(l.long-t,2))<Math.sqrt(Math.pow(a.lat-e,2)+Math.pow(a.long-t,2))?l:a);.15>=en(l,e,t)&&0!==l.lat&&0!==l.long?(Z({label:l.title,value:l.id,latitude:l.lat,longitude:l.long}),J(!0)):(Z(null),es(e,t))},es=(e,l)=>{U(null),t({latitude:+e,longitude:+l})},ed=()=>{"geolocation"in navigator?navigator.geolocation.getCurrentPosition(({coords:l})=>{let{latitude:a,longitude:o}=l;K({latitude:a,longitude:o}),e({latitude:+a,longitude:+o}),t({latitude:+a,longitude:+o}),er(a,o)},e=>{let t="Failed to get current location";e.code===e.PERMISSION_DENIED?t="Location access denied. Please enable location permissions.":e.code===e.POSITION_UNAVAILABLE?t="Location information unavailable.":e.code===e.TIMEOUT&&(t="Location request timed out."),V({variant:"destructive",title:"Error",description:t})},{timeout:3e4}):V({variant:"destructive",title:"Error",description:"Geolocation is not supported by your browser"})},ec=()=>{U(B),t(B),ev(!1),J(!1)},eu=()=>{let t=document.getElementById("location-latitude").value,l=document.getElementById("location-longitude").value;K({latitude:+t,longitude:+l}),e({latitude:+t,longitude:+l}),U(null)},[em]=(0,s.t)(h.IR,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readGeoLocations.nodes;t&&C(t)},onError:e=>{console.error("queryGeoLocations error",e)}}),eg=(0,o.useRef)(null),eh=(0,o.useRef)(null),[ef,ev]=(0,o.useState)(!1),[eb,ex]=(0,o.useState)("select"),[ep,ew]=(0,o.useState)({title:"",latitude:"",longitude:""}),eL=()=>{"coordinates"===eb&&(ee(),U(null),t({latitude:W.latitude,longitude:W.longitude})),ev(!1)},eN=()=>{let{title:e,latitude:t,longitude:l}=ep;return""!==e.trim()&&""!==t.trim()&&""!==l.trim()&&!isNaN(parseFloat(t))&&!isNaN(parseFloat(l))},ej=e=>{let{name:t,value:l}=e.target;ew(e=>({...e,[t]:l})),setTimeout(eN,100)},ey=()=>{if(eN()){let e=document.getElementById("new-location-title"),t=document.getElementById("new-location-latitude"),l=document.getElementById("new-location-longitude");e&&t&&l&&(e.value=ep.title,t.value=ep.latitude,l.value=ep.longitude),ea(),ev(!1)}else V({variant:"destructive",title:"Validation Error",description:"Please fill in all required fields with valid values"})};return(0,a.jsxs)(a.Fragment,{children:[a.jsx(j.Button,{iconRight:a.jsx(y.Z,{className:"text-neutral-400"}),className:"w-full justify-between max-h-11 text-base",variant:"outline",onClick:()=>{ex("select"),ev(!0)},children:k?.label?k.label:void 0!==W.latitude&&null!==W.latitude&&0!==W.latitude||void 0!==W.longitude&&null!==W.longitude&&0!==W.longitude?`${Number(W.latitude).toFixed(6)}, ${Number(W.longitude).toFixed(6)}`:"Select location"}),a.jsx(D.h9,{openDialog:ef,setOpenDialog:ev,handleCreate:()=>{if("coordinates"===eb)eL();else if("current"===eb)B?.label||0===W.latitude&&W.longitude,ev(!1);else if("new"===eb){if(eN())ey();else{V({variant:"destructive",title:"Validation Error",description:"Please fill in all required fields with valid values"});return}}else ev(!1)},actionText:"coordinates"===eb?"Use Location":"current"===eb?B?.label||0===W.latitude&&0===W.longitude?"Get Location":"Use GPS Coordinates":"new"===eb?"Add Location":"OK",title:"Select location",size:"md",children:a.jsx("div",{className:"flex flex-col gap-5",children:(0,a.jsxs)(S.Ee,{value:eb,onValueChange:e=>{ex(e),"coordinates"===e?(M(!1),U(null)):"current"===e?ed():"new"===e&&M(!0)},className:"space-y-8",children:[(0,a.jsxs)("div",{className:"w-full flex gap-2.5",children:[a.jsx(S.mJ,{size:"md",value:"select",id:"select-location"}),a.jsx(N.Label,{className:"text-base w-full font-medium",label:"Select from locations",htmlFor:"select-location",children:"select"===eb&&a.jsx("div",{className:"flex flex-row w-full",children:a.jsx(i.Combobox,{options:E?[{label:" Add New Location",value:"newLocation"},...(b=E?.map(e=>({label:e.title,value:e.id,latitude:e.lat,longitude:e.long}))||[],(e=>{if(!e||0===e.length)return[];let t=[...e];return Y.length>0?t.sort((e,t)=>{let l=Y.find(t=>t.geoLocationID===e.value),a=Y.find(e=>e.geoLocationID===t.value);return l&&a?a.usage-l.usage:l?-1:a?1:0}):t})((E&&R?.length>0?[...R,...b]:[...b]).filter(e=>!u()(g()(e.label)))))]:[{label:" Add New Location",value:"newLocation"}],value:k,onChange:e=>{et(e),e&&"newLocation"!==e.value&&ev(!1)},placeholder:"Select location"})})})]}),f&&(0,a.jsxs)("div",{className:"flex gap-2.5",children:[a.jsx(S.mJ,{size:"md",value:"coordinates",id:"enter-coordinates"}),a.jsx(N.Label,{label:"Enter coordinates",htmlFor:"enter-coordinates",className:"text-base font-medium",children:"coordinates"===eb&&a.jsx("div",{className:"flex flex-col gap-4 w-full p-4 border border-neutral-400 border-dashed rounded-lg bg-card",children:(0,a.jsxs)("div",{className:"flex flex-col gap-4 w-full",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsx(N.Label,{label:"Latitude",htmlFor:"location-latitude",children:a.jsx(L.I,{id:"location-latitude",name:"latitude",type:"number",ref:eg,value:W.latitude,onChange:eu,placeholder:"Enter latitude",className:"w-full",required:!0})}),a.jsx(N.Label,{label:"Longitude",htmlFor:"location-longitude",children:a.jsx(L.I,{id:"location-longitude",name:"longitude",type:"number",ref:eh,value:W.longitude,onChange:eu,placeholder:"Enter longitude",className:"w-full",required:!0})})]}),a.jsx("div",{className:"mt-2",children:a.jsx("p",{className:"text-sm text-muted-foreground italic",children:"Enter coordinates in decimal degrees format (e.g., 37.80255, -122.41463)"})}),(0!==W.latitude||0!==W.longitude)&&a.jsx("div",{className:"mt-2 p-3 bg-muted rounded-md",children:(0,a.jsxs)("p",{children:["Current coordinates:"," ",Number(W.latitude).toFixed(6),","," ",Number(W.longitude).toFixed(6)]})})]})})})]}),v&&(0,a.jsxs)("div",{className:"flex gap-2.5",children:[a.jsx(S.mJ,{size:"md",value:"current",id:"current-location"}),a.jsx(N.Label,{label:"Use current location",htmlFor:"current-location",className:"text-base font-medium",children:(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-sm text-muted-foreground",children:"Automatically detects your location and uses the nearest available location or GPS coordinates."}),"current"===eb&&(0,a.jsxs)("div",{className:"mt-2",children:[B?.label&&(0,a.jsxs)("div",{className:"p-4 border rounded-lg border-neutral-400 border-dashed bg-card",children:[(0,a.jsxs)(D.P,{className:"mb-2",children:["Are you in"," ",B?.label,"?"]}),(0,a.jsxs)(j.Button,{iconLeft:I.Z,onClick:ec,className:"w-full",children:["Use"," ",B?.label]})]}),!B?.label&&(0!==W.latitude||0!==W.longitude)&&(0,a.jsxs)("div",{className:"p-4 border rounded-lg border-neutral-400 border-dashed bg-card",children:[a.jsx(D.P,{className:"mb-2 font-medium",children:"Using GPS coordinates:"}),(0,a.jsxs)(D.P,{className:"text-sm font-mono",children:[Number(W.latitude).toFixed(6),","," ",Number(W.longitude).toFixed(6)]}),a.jsx(D.P,{className:"text-xs text-muted-foreground mt-2",children:"No nearby locations found within range."})]})]})]})})]}),m&&(0,a.jsxs)("div",{className:"w-full flex gap-2.5",children:[a.jsx(S.mJ,{size:"md",value:"new",id:"new-location"}),a.jsx(N.Label,{label:"Add new location",htmlFor:"new-location",className:"text-base w-full font-medium",children:"new"===eb&&(0,a.jsxs)("div",{className:"flex-grow flex flex-col gap-5 p-4 bg-background rounded-lg border-neutral-400 border border-dashed",children:[a.jsx("div",{className:"grid grid-cols-1 gap-5",children:a.jsx(N.Label,{label:"Location Name",htmlFor:"new-location-title",children:a.jsx(L.I,{id:"new-location-title",name:"title",type:"text",placeholder:"Enter location name",value:ep.title,onChange:ej,required:!0})})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsx(N.Label,{label:"Latitude",htmlFor:"new-location-latitude",children:a.jsx(L.I,{id:"new-location-latitude",name:"latitude",type:"number",value:ep.latitude||W.latitude,onChange:ej,placeholder:"Enter latitude",required:!0})}),a.jsx(N.Label,{label:"Longitude",htmlFor:"new-location-longitude",children:a.jsx(L.I,{id:"new-location-longitude",name:"longitude",type:"number",value:ep.longitude||W.longitude,onChange:ej,placeholder:"Enter longitude",required:!0})})]})]})})]})]})})}),a.jsx(D.h9,{openDialog:H,setOpenDialog:_,handleCreate:ea,actionText:"Add Location",title:"Add New Location",showIcon:!0,children:a.jsx("div",{className:"flex flex-col gap-5",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-5",children:[a.jsx(N.Label,{label:"Location Name",htmlFor:"new-location-title",children:a.jsx(L.I,{id:"new-location-title",name:"title",type:"text",placeholder:"Enter location name",value:ep.title,onChange:ej,required:!0})}),a.jsx(N.Label,{htmlFor:"new-location-latitude",label:"Latitude",children:a.jsx(L.I,{id:"new-location-latitude",name:"latitude",type:"number",value:ep.latitude||W.latitude,onChange:ej,placeholder:"Enter latitude",required:!0})}),a.jsx(N.Label,{label:"Longitude",htmlFor:"new-location-longitude",children:a.jsx(L.I,{id:"new-location-longitude",name:"longitude",type:"number",value:ep.longitude||W.longitude,onChange:ej,placeholder:"Enter longitude",required:!0})})]})})}),a.jsx(D.h9,{openDialog:z,setOpenDialog:J,handleCreate:ea,actionText:"Create Location",title:"Current Location",children:(0,a.jsxs)("div",{className:"flex flex-col gap-5",children:[B?.label?a.jsx("div",{className:"flex flex-col gap-4",children:B?.label!=void 0?(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)(D.P,{className:"font-medium",children:["Are you in ",B?.label,"?"]}),(0,a.jsxs)(j.Button,{iconLeft:I.Z,onClick:ec,className:"w-full",children:["Use ",B?.label]}),a.jsx(F.Separator,{className:"my-4"}),a.jsx(D.P,{className:"uppercase",children:"Or create new location"})]}):a.jsx("p",{className:"text-lg",children:"Fetching current location took long, do you want to create a new location instead?"})}):a.jsx("p",{className:"text-lg",children:"Failed to fetch current location. Do you want to create a new location instead?"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-5",children:[a.jsx(N.Label,{label:"Location Name",htmlFor:"new-location-title",children:a.jsx(L.I,{id:"new-location-title",name:"title",type:"text",placeholder:"Enter location name",value:ep.title,onChange:ej,required:!0})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsx(N.Label,{label:"Latitude",htmlFor:"new-location-latitude",children:a.jsx(L.I,{id:"new-location-latitude",name:"latitude",type:"number",value:ep.latitude||W.latitude,onChange:ej,placeholder:"Enter latitude",required:!0})}),a.jsx(N.Label,{label:"Longitude",htmlFor:"new-location-longitude",children:a.jsx(L.I,{id:"new-location-longitude",name:"longitude",type:"number",value:ep.longitude||W.longitude,onChange:ej,placeholder:"Enter longitude",required:!0})})]})]})]})})]})}},33810:(e,t,l)=>{l.d(t,{Z:()=>r});var a=l(98768),o=l(83179),i=l.n(o),n=l(77866);function r({time:e=i()().format("HH:mm"),handleTimeChange:t,timeID:l,fieldName:o="Time",buttonLabel:r="Set To Now",hideButton:s=!1,disabled:d}){return a.jsx(n.j,{disabled:d,value:(()=>{if(!e||!1===e)return new Date;let t=`${i()().format("YYYY-MM-DD")} ${e}`,l=i()(t);return l.isValid()?l.toDate():new Date})(),onChange:t,use24Hour:!0,className:"tablet-sm:w-[300px] w-full",nowButton:!s,nowButtonLabel:r})}},99303:(e,t,l)=>{l.d(t,{Z:()=>m});var a=l(98768),o=l(60343),i=l(79418),n=l(69424),r=l(46776),s=l(73366),d=l(81524),c=l(45519);let u=(0,c.ZP)`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                isArchived
                firstName
                surname
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
            }
        }
    }
`,m=({label:e="Trainer",value:t,onChange:l,controlClasses:c="default",placeholder:m="Trainer",isClearable:g=!1,filterByTrainingSessionMemberId:h=0,trainerIdOptions:f=[],memberIdOptions:v=[],multi:b=!1,offline:x=!1,vesselID:p=0,disabled:w=!1})=>{let[L,N]=(0,o.useState)(),[j,y]=(0,o.useState)(b?[]:null),[I,F]=(0,o.useState)(!0),[S,D]=(0,o.useState)([]),E=(0,n.usePathname)(),[C,P]=(0,o.useState)(!1),A=new s.Z,[q,M]=(0,o.useState)([]),[B,Z]=(0,o.useState)({}),[k,U]=(0,o.useState)(0),Y=e=>{let t=p>0?e.filter(e=>e.vehicles.nodes.some(e=>+e.id===p)):e;if(t){if(C&&"/reporting"===E){let e=localStorage.getItem("userId");N(t.filter(t=>t.id===e))}else N(t);D(t)}},[$]=(0,i.t)(u,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readSeaLogsMembers.nodes,l=e.readSeaLogsMembers.pageInfo,a=[...q,...t];if(M(a),l.hasNextPage){let e=k+t.length;U(e),$({variables:{filter:B,offset:e,limit:100}});return}Y(a)},onError:e=>{console.error("querySeaLogsMembersList error",e)}}),V=async()=>{let e={isArchived:{eq:!1}};if(h>0&&(e={...e,trainingSessions:{members:{id:{contains:h}}}}),x){let e=(await A.getAll()).filter(e=>h>0?!1===e.isArchived&&e.trainingSessions.nodes.some(e=>e.members.nodes.some(e=>e.id===h)):!1===e.isArchived);if(e){if(C&&"/reporting"===E){let t=localStorage.getItem("userId");N(e.filter(e=>e.id===t))}else N(e);D(e)}}else M([]),U(0),Z(e),await $({variables:{filter:e,offset:0,limit:100}})};return(0,o.useEffect)(()=>{I&&(P((0,r.PE)()||!1),V(),F(!1))},[I]),(0,o.useEffect)(()=>{if(t&&L){let e=L.find(e=>e.id===t);e&&y({value:e.id,label:`${e.firstName||""} ${e.surname||""}`,profile:{firstName:e.firstName,surname:e.surname,avatar:null}})}else t||y(null)},[t,L,m]),(0,o.useEffect)(()=>{f.length>0&&S.length>0&&N(S.filter(e=>f.includes(e.id)))},[f,S]),(0,o.useEffect)(()=>{v.length>0&&S.length>0?N(S.filter(e=>v.includes(e.id))):S.length>0&&N(S)},[v,S,m]),a.jsx(d.Combobox,{options:L?.map(e=>({value:e.id,label:`${e.firstName||""} ${e.surname||""}`,profile:{firstName:e.firstName,surname:e.surname,avatar:null}})),value:j,onChange:e=>{y(e),l(b?e:e?.value||null)},multi:b,isLoading:!L,placeholder:m,disabled:w})}},11232:(e,t,l)=>{l.d(t,{Z:()=>d});var a=l(98768),o=l(94060),i=l(79418),n=l(60343),r=l(81524),s=l(52241);let d=({value:e,onChange:t,isClearable:l=!1,className:d="",vesselIdOptions:c=[],filterByTrainingSessionMemberId:u=0})=>{let[m,g]=(0,n.useState)(!0),[h,f]=(0,n.useState)([]),[v,b]=(0,n.useState)([]),[x,p]=(0,n.useState)([]),[w,L]=(0,n.useState)([]),{getVesselWithIcon:N,loading:j}=(0,s.P)(),[y,{loading:I}]=(0,i.t)(o.NS,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readVessels.nodes;t&&L(t.filter(e=>!e.archived&&e.title))},onError:e=>{console.error("queryVesselList error",e)}}),F=async()=>{let e={};u>0&&(e={trainingSessions:{members:{id:{contains:u}}}}),y({variables:{filter:e={...e,archived:{eq:!1}}}})};return(0,n.useEffect)(()=>{if(w.length>0&&!j){let e=w.map(e=>{let t=N(e.id,e);return{value:e.id,label:e.title,vessel:t}});e.sort((e,t)=>e.label.localeCompare(t.label)),b(e),f(e)}},[w,j]),(0,n.useEffect)(()=>{m&&(F(),g(!1))},[m]),(0,n.useEffect)(()=>{h.length>0&&p(h.find(t=>t.value===e))},[e,h]),(0,n.useEffect)(()=>{c.length>0?f(v.filter(e=>c.includes(e.value))):f(v)},[c,v]),a.jsx(r.Combobox,{options:h,defaultValues:x,onChange:e=>{p(e),t(e)},isLoading:I&&h&&!m,title:"Vessel",buttonClassName:d,labelClassName:d,placeholder:"Vessel",multi:!1})}}};