(()=>{var e={};e.id=1838,e.ids=[1838],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71645:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),t(39006),t(32693),t(78398),t(57757),t(48045);var s=t(40060),i=t(33581),a=t(57567),l=t.n(a),n=t(51650),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(r,o);let d=["",{children:["crew",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,39006)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,32693)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew\\page.tsx"],u="/crew/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/crew/page",pathname:"/crew",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},37608:(e,r,t)=>{Promise.resolve().then(t.bind(t,68888))},3676:(e,r,t)=>{Promise.resolve().then(t.bind(t,13386))},68888:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(98768);t(60343);var i=t(64837);function a({children:e}){return s.jsx(i.Z,{children:e})}},13386:(e,r,t)=>{"use strict";t.d(r,{x:()=>M,default:()=>A});var s=t(98768),i=t(60343),a=t(94060),l=t(79418),n=t(72548),o=t(66263),d=t(76342),c=t(13842),u=t(7678),m=t.n(u),p=t(69424),h=t(22995),x=t(39544),g=t(51742),v=t(30905),f=t(25394),j=t(15580),y=t(81311),w=t(53294),b=t(81257),S=t(27514),N=t(9210);let P=({onChange:e})=>{let{isMobile:r}=(0,h.Ap)(),[t,a]=(0,i.useState)(!0),l=()=>{a(r=>{let t=!r;return e(t),t})};return(0,s.jsxs)(S.DropdownMenu,{children:[s.jsx(S.DropdownMenuTrigger,{asChild:!0,children:s.jsx(N.HV,{size:36})}),(0,s.jsxs)(S.DropdownMenuContent,{side:r?"bottom":"right",align:r?"end":"start",children:[s.jsx(S.DropdownMenuItem,{onClick:()=>l(),children:t?"Archived Crews":"Active Crews"}),s.jsx(S.DropdownMenuSeparator,{}),s.jsx(o.default,{href:"/user/create",children:s.jsx(S.DropdownMenuItem,{children:"Add Crew"})})]})]})};var C=t(39650),q=t(99891),D=t(52241);function A(e){(0,p.useRouter)();let[r,t]=(0,i.useState)([]),[u,x]=(0,i.useState)([]),{vesselIconData:j,getVesselWithIcon:w}=(0,D.P)(),[S,N]=(0,i.useState)(!0),{isMobile:A}=(0,h.Ap)(),[M,T]=(0,i.useState)(!0),[_,k]=(0,i.useState)([]),[$,E]=(0,i.useState)([]),[V,G]=(0,i.useState)({totalCount:0,hasNextPage:!1,hasPreviousPage:!1}),[L,I]=(0,i.useState)(0),[B,K]=(0,i.useState)({isArchived:{eq:!1}}),[R,U]=(0,i.useState)(null);(0,c.Fo)(e=>{E(e.filter(e=>S?!e.archived:e.archived).map(e=>({label:e.title,value:e.id})))});let F=(e,r=0,t=0)=>e.filter(e=>+e.parentID===r).flatMap(r=>{let s=F(e,+r.id,t+1);return[{...r,level:t},...s]}),[O,{loading:z}]=(0,l.t)(a.d5,{fetchPolicy:"cache-and-network",onCompleted:e=>{let r=e.readDepartments.nodes;r&&k(F(r))},onError:e=>{console.error("queryCrewMembers error",e)}});(0,c.lY)(e=>{x(e.map(e=>({label:e.title,value:e.id})))});let[Z]=(0,l.t)(a.qJ,{fetchPolicy:"cache-and-network",onCompleted:e=>(Q(e.readSeaLogsMembers.nodes),G(e.readSeaLogsMembers.pageInfo),e.readSeaLogsMembers.nodes),onError:e=>{console.error("queryCrewMembers error",e)}}),Q=e=>{t((0,c.Vp)(e,u))},H=((e,r)=>{let t=[...e].map(e=>{let r=e.trainingStatus.dues;return 0===r.length?{...e,trainingStatus:{label:"Good",dues:[]}}:r.some(e=>e.status.isOverdue)?{...e,trainingStatus:{label:"Overdue",dues:r.filter(e=>e.status.isOverdue)}}:{...e,trainingStatus:{label:" ",dues:r.filter(e=>!e.status.isOverdue)}}});return r?t.filter(e=>{let t=e.trainingStatus?.label,s=e.trainingStatus?.dues||[];return"Good"===r?"Good"===t:"Overdue"===r?"Overdue"===t:"Due Soon"!==r||" "===t&&s.length>0}):t})(r,R),J=async(e=0,r={...B})=>{await Z({variables:{limit:100,offset:100*e,filter:r}})},[X]=(0,n.D)(d.AXh,{onCompleted:()=>{},onError:e=>{console.error("mutationUpdateUser error",e)}}),W=({type:e,data:r})=>{let t={...B};if("trainingStatus"===e){r&&r.value?U(r.value):U(null);return}"vessel"===e&&(Array.isArray(r)&&r.length>0?t.vehicles={id:{in:r.map(e=>+e.value)}}:r&&!Array.isArray(r)?t.vehicles={id:{contains:+r.value}}:delete t.vehicles),"crewDuty"===e&&(Array.isArray(r)&&r.length>0?t.primaryDutyID={in:r.map(e=>+e.value)}:r&&!Array.isArray(r)?t.primaryDutyID={eq:+r.value}:delete t.primaryDutyID),"keyword"===e&&(m()(r.value)?delete t.q:t.q={contains:r.value}),"isArchived"===e&&(void 0!==r?t.isArchived={eq:!r}:delete t.isArchived),K(t),J(0,t)},Y=[{accessorKey:"title",header:({column:e})=>s.jsx(v.u,{column:e,title:"Name"}),cell:({row:e})=>{let r=e.original,t=`${r.firstName} ${r.surname}`;return(0,s.jsxs)("div",{className:"flex-1 flex justify-start items-center gap-2",children:[s.jsx(f.qE,{size:"sm",variant:r.trainingStatus?.label==="Overdue"?"destructive":"success",children:s.jsx(f.Q5,{children:(0,f.xE)(r.firstName,r.surname)})}),s.jsx("div",{className:"grid min-w-32",children:s.jsx(o.default,{href:`/crew/info?id=${r.id}`,className:"items-center truncate pl-2 text-nowrap",children:t||"--"})})]})},sortingFn:(e,r)=>{let t=`${e?.original?.firstName} ${e?.original?.surname}`||"",s=`${r?.original?.firstName} ${r?.original?.surname}`||"";return t.localeCompare(s)}},{accessorKey:"vehicles",cellAlignment:"left",header:({column:e})=>s.jsx(v.u,{column:e,title:"Vessel"}),cell:({row:e})=>{let r=e.original;return s.jsx("div",{className:"flex flex-row gap-2 py-2.5",children:r.vehicles.nodes.map(e=>{let r=w(e.id,e);return s.jsx("div",{className:"flex items-center text-start gap-2",children:(0,s.jsxs)(f.u,{children:[s.jsx(f.aJ,{children:s.jsx("div",{className:"min-w-fit",children:s.jsx(q.Z,{vessel:r})})}),s.jsx(f._v,{children:e.title})]},e.id)},String(e.id))})})},sortingFn:(e,r)=>{let t=e?.original?.vehicles?.nodes?.[0]?.title||"",s=r?.original?.vehicles?.nodes?.[0]?.title||"";return t.localeCompare(s)}},{accessorKey:"primaryDuty",cellAlignment:"right",header:({column:e})=>s.jsx(v.u,{column:e,title:"Primary Duty"}),cell:({row:e})=>{let r=e.original;return s.jsx("div",{className:"whitespace-normal px-5",children:r.primaryDuty.title})},sortingFn:(e,r)=>{let t=e?.original?.primaryDuty?.title||"",s=r?.original?.primaryDuty?.title||"";return t.localeCompare(s)}},{accessorKey:"trainingStatus",header:({column:e})=>s.jsx("div",{className:"flex justify-center w-full",children:s.jsx(v.u,{column:e,title:"Training"})}),cell:({row:e})=>{let r=e.original;return s.jsx("div",{className:"flex justify-center w-full",children:"Overdue"===r.trainingStatus.label?s.jsx(f.Ct,{variant:"destructive",type:"circle",children:r.trainingStatus.dues.length}):s.jsx(f.Ct,{variant:"success",type:"circle",children:s.jsx(y.Z,{className:"h-5 w-5"})})})}}],ee=(e,r)=>{W({type:e,data:r})};return(0,s.jsxs)(s.Fragment,{children:[s.jsx(C.ListHeader,{icon:s.jsx(b.K,{className:"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]"}),title:"All crew",actions:s.jsx(P,{onChange:e=>{ee("isArchived",e)}})}),s.jsx("div",{className:"mt-16",children:s.jsx(g.wQ,{columns:Y,data:H,pageSize:20,onChange:W})})]})}let M=({crewList:e,vessels:r,handleCrewDuty:t=!1,showSurname:n})=>{let[d,u]=(0,i.useState)(!1),[m,p]=(0,i.useState)([]),[h,v]=(0,i.useState)(!0),b=(e,r=0,t=0)=>e.filter(e=>+e.parentID===r).flatMap(r=>{let s=b(e,+r.id,t+1);return[{...r,level:t},...s]}),[S,{loading:N}]=(0,l.t)(a.d5,{fetchPolicy:"cache-and-network",onCompleted:e=>{let r=e.readDepartments.nodes;r&&p(b(r))},onError:e=>{console.error("queryCrewMembers error",e)}}),P=async()=>{await S()};(0,i.useEffect)(()=>{h&&(P(),v(!1))},[h]);let C=(0,c.Vp)(e,r).map(e=>{let r=e.trainingStatus.dues.filter(r=>e.vehicles.nodes.some(e=>e.id===r.vesselID)),t={...e.trainingStatus,dues:r};return 0===r.length&&(t.label="Good"),{...e,trainingStatus:t}});(0,i.useEffect)(()=>{},[]);let q=(0,g.wu)([{accessorKey:"title",header:"",cell:({row:e})=>{let r=e.original;return(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex gap-2.5 items-center",children:[s.jsx(f.qE,{size:"sm",variant:r.trainingStatus?.label!=="Good"?"destructive":"success",children:s.jsx(f.Q5,{children:(0,f.xE)(r.firstName,r.surname)})}),(0,s.jsxs)(o.default,{href:`/crew/info?id=${r.id}`,className:"flex items-center pl-2 text-nowrap",children:[r.firstName||"--",!0==n?(0,s.jsxs)("span",{children:["\xa0",r.surname||"--"]}):(0,s.jsxs)("span",{className:"hidden md:flex",children:["\xa0",r.surname||"--"]})]})]}),t&&s.jsx("div",{className:"flex md:hidden flex-col",children:r.vehicles.nodes&&r.vehicles.nodes.map((e,t)=>t<2?s.jsx("div",{className:"bg-muted font-light rounded-lg p-2 border m-1 border-border text-nowrap",children:s.jsx(o.default,{className:"max-w-32 overflow-hidden block",href:`/vessel/info?id=${e.id}`,children:e.title})},e.id):2===t?(0,s.jsxs)(j.Popover,{children:[s.jsx(j.PopoverTrigger,{asChild:!0,children:(0,s.jsxs)(x.Button,{variant:"outline",size:"sm",className:"text-orange-500",children:["+"," ",r.vehicles.nodes.length-2," ","more"]})}),s.jsx(j.PopoverContent,{className:"p-0 w-64",children:s.jsx("div",{className:"max-h-full bg-background rounded",children:r.vehicles.nodes.slice(2).map(e=>s.jsx("div",{className:"flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2",children:s.jsx("div",{className:"text-sm",children:s.jsx(o.default,{href:`/vessel/info?id=${e.id}`,children:e.title})})},e.id))})})]},e.id):null)})]})}},{accessorKey:"vessel",header:()=>s.jsx(s.Fragment,{children:t&&"Vessel"}),cellAlignment:"left",cell:({row:e})=>{let r=e.original;return s.jsx(s.Fragment,{children:t&&s.jsx("div",{children:r.vehicles.nodes&&r.vehicles.nodes.map((e,t)=>t<2?s.jsx("div",{className:"bg-muted inline-block font-light rounded-lg p-2 border border-border m-1",children:s.jsx(o.default,{href:`/vessel/info?id=${e.id}`,children:e.title})},e.id):2===t?(0,s.jsxs)(j.Popover,{children:[s.jsx(j.PopoverTrigger,{asChild:!0,children:(0,s.jsxs)(x.Button,{variant:"outline",size:"sm",className:"text-orange-500",children:["+"," ",r.vehicles.nodes.length-2," ","more"]})}),s.jsx(j.PopoverContent,{className:"p-0 w-64",children:s.jsx("div",{className:"max-h-full bg-background rounded",children:r.vehicles.nodes.slice(2).map(e=>s.jsx("div",{className:"flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2",children:s.jsx("div",{className:"text-sm",children:s.jsx(o.default,{href:`/vessel/info?id=${e.id}`,children:e.title})})},e.id))})})]},e.id):null)})})}},{accessorKey:"primaryDuty",header:"Primary Duty",cellAlignment:"left",cell:({row:e})=>{let r=e.original;return s.jsx("div",{className:"text-wrap text-right whitespace-normal",children:r.primaryDuty.title})}},{accessorKey:"trainingStatus",header:"Training Status",cell:({row:e})=>{let r=e.original;return s.jsx("div",{className:"flex justify-center",children:"Good"!==r.trainingStatus.label?(0,s.jsxs)(j.Popover,{triggerType:"hover",children:[s.jsx(j.PopoverTrigger,{asChild:!0,children:s.jsx(w.Z,{strokeWidth:1,className:"h-9 w-9 text-destructive cursor-pointer"})}),s.jsx(j.PopoverContent,{children:s.jsx("div",{className:"bg-background rounded p-2",children:s.jsx("div",{className:"text-xs whitespace-nowrap font-medium focus:outline-none inline-block rounded",children:r.trainingStatus.dues.map((e,r)=>s.jsx("div",{children:`${e.trainingType.title} - ${e.status.label}`},r))})})})]}):s.jsx(f.Ct,{variant:"success",type:"circle",children:s.jsx(y.Z,{className:"h-5 w-5"})})})}},{accessorKey:"departments",header:()=>s.jsx(s.Fragment,{children:d&&"true"===localStorage.getItem("useDepartment")&&"Departments"}),cell:({row:e})=>{let r=e.original;return s.jsx("div",{children:d&&"true"===localStorage.getItem("useDepartment")&&s.jsx(s.Fragment,{children:r.departments&&r.departments.nodes.length>0?r.departments.nodes.map(e=>s.jsx(o.default,{href:`/department/info?id=${e.id}`,className:"flex flex-col text-nowrap",children:m.find(r=>r.id===e.id)?.title},e.id)):s.jsx("span",{children:"No departments found"})})})}}]);return s.jsx(g.wQ,{columns:q,showToolbar:!1,data:C,pageSize:20})}},32693:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\crew\layout.tsx#default`)},39006:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(96141),i=t(38851);let a=(0,i.createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\crew\list.tsx#default`);function l(){return s.jsx(a,{})}(0,i.createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\crew\list.tsx#CrewTable`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7944],()=>t(71645));module.exports=s})();