exports.id=209,exports.ids=[209],exports.modules={93854:(e,t,r)=>{Promise.resolve().then(r.bind(r,30261))},47520:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var a=r(19821),s=r.n(a)},19821:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let a=r(41034);r(98768),r(60343);let s=a._(r(40907));function i(e,t){var r;let a={loading:e=>{let{error:t,isLoading:r,pastDelay:a}=e;return null}};"function"==typeof e&&(a.loader=e);let i={...a,...t};return(0,s.default)({...i,modules:null==(r=i.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96359:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return s}});let a=r(90408);function s(e){let{reason:t,children:r}=e;throw new a.BailoutToCSRError(t)}},40907:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let a=r(98768),s=r(60343),i=r(96359),n=r(58902);function l(e){return{default:e&&"default"in e?e.default:e}}let o={loader:()=>Promise.resolve(l(()=>null)),loading:null,ssr:!0},d=function(e){let t={...o,...e},r=(0,s.lazy)(()=>t.loader().then(l)),d=t.loading;function c(e){let l=d?(0,a.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,o=t.ssr?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.PreloadCss,{moduleIds:t.modules}),(0,a.jsx)(r,{...e})]}):(0,a.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(r,{...e})});return(0,a.jsx)(s.Suspense,{fallback:l,children:o})}return c.displayName="LoadableComponent",c}},58902:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return i}});let a=r(98768),s=r(54580);function i(e){let{moduleIds:t}=e,r=(0,s.getExpectedRequestStore)("next/dynamic css"),i=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files.filter(e=>e.endsWith(".css"));i.push(...t)}}return 0===i.length?null:(0,a.jsx)(a.Fragment,{children:i.map(e=>(0,a.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:r.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},30261:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(98768);r(60343);var s=r(64837);function i({children:e}){return a.jsx(s.Z,{children:e})}},85787:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var a=r(83179),s=r.n(a),i=r(86708);class n{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),r=t.id,a={...t,idbCRUD:"Update",idbCRUDDate:s()().format("YYYY-MM-DD HH:mm:ss")},n=await this.getById(r);return n?await i.Z.TrainingSessionDue.update(r,a):await i.Z.TrainingSessionDue.add(a),n=await this.getById(r),console.log("TrainingSessionDue save",e,n),n}catch(t){console.error("TrainingSessionDue save",e,t)}}async getAll(){try{let e=await i.Z.TrainingSessionDue.toArray();return console.log("TrainingSessionDue getAll",e),e}catch(e){console.error("TrainingSessionDue getAll",e)}}async getById(e){try{let t=await i.Z.TrainingSessionDue.get(`${e}`);return console.log("TrainingSessionDue getById",e,t),t}catch(t){console.error("TrainingSessionDue getById",e,t)}}async getByIds(e){try{let t=await i.Z.TrainingSessionDue.where("id").anyOf(e).toArray();return console.log("TrainingSessionDue getByIds",e,t),t}catch(t){console.error("TrainingSessionDue getByIds",e,t)}}async getByFieldID(e,t){try{let r=await i.Z.TrainingSessionDue.where(`${e}`).equals(`${t}`).toArray();return console.log("TrainingSessionDue getByFieldID",e,t,r),r}catch(r){console.error("TrainingSessionDue getByFieldID",e,t,r)}}async bulkAdd(e){try{return await i.Z.TrainingSessionDue.bulkAdd(e),console.log("TrainingSessionDue bulkAdd",e),e}catch(t){if("BulkError"===t.name){let r=t.failuresByPos.map(e=>e.key),a=e.filter(e=>!r.includes(e.id));return await i.Z.TrainingSessionDue.bulkAdd(a),console.log("TrainingSessionDue bulkAdd::BulkError",e,t),e}console.error("TrainingSessionDue bulkAdd",e,t)}}async setProperty(e){try{if(e){let t=await i.Z.TrainingSessionDue.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=s()().format("YYYY-MM-DD HH:mm:ss"),await i.Z.TrainingSessionDue.update(e,t),console.log("TrainingSessionDue setProperty",e,t),t}}catch(t){console.error("TrainingSessionDue setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await i.Z.TrainingSessionDue.update(e.id,e)})),console.log("TrainingSessionDue multiUpdate",e)}catch(t){console.error("TrainingSessionDue multiUpdate",e,t)}}}let l=n},31321:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var a=r(83179),s=r.n(a),i=r(86708),n=r(85787);class l{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),r=t.id,a={...t,idbCRUD:"Update",idbCRUDDate:s()().format("YYYY-MM-DD HH:mm:ss")},n=await this.getById(r);return n?await i.Z.TrainingType.update(r,a):await i.Z.TrainingType.add(a),n=await this.getById(r),console.log("TrainingType save",e,n),n}catch(t){console.error("TrainingType save",e,t)}}async getAll(){try{let e=await i.Z.TrainingType.toArray();return console.log("TrainingType getAll",e),e}catch(e){console.error("TrainingType getAll",e)}}async getById(e){try{let t=await i.Z.TrainingType.get(`${e}`),r=await this.addRelationships(t);return console.log("TrainingType getById",e,r),r}catch(t){console.error("TrainingType getById",e,t)}}async getByIds(e){try{let t=await i.Z.TrainingType.where("id").anyOf(e).toArray(),r=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("TrainingType getByIds",e,r),r}catch(t){console.error("TrainingType getByIds",e,t)}}async getByVesselID(e){try{let t=await i.Z.TrainingType.where("vesselID").equals(`${e}`).toArray();return console.log("TrainingType getByVesselID",e,t),t}catch(t){console.error("TrainingType getByVesselID",e,t)}}async getByFieldID(e,t){try{let r=await i.Z.TrainingType.where(`${e}`).equals(`${t}`).toArray();return console.log("TrainingType getByFieldID",e,t,r),r}catch(r){console.error("TrainingType getByFieldID",e,t,r)}}async bulkAdd(e){try{return await i.Z.TrainingType.bulkAdd(e),console.log("TrainingType bulkAdd",e),e}catch(t){if("BulkError"===t.name){let r=t.failuresByPos.map(e=>e.key),a=e.filter(e=>!r.includes(e.id));return await i.Z.TrainingType.bulkAdd(a),console.log("TrainingType bulkAdd::BulkError",e,t),e}console.error("TrainingType bulkAdd",e,t)}}async addRelationships(e){if(!e)return console.log("TrainingType addRelationships",e),e;{let t=+e.id>0?await this.trainingSessionDueModel.getByFieldID("trainingTypeID",e.id):[];return console.log("TrainingType addRelationships",e),{...e,trainingSessionsDue:{nodes:t}}}}async setProperty(e){try{if(e){let t=await i.Z.TrainingType.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=s()().format("YYYY-MM-DD HH:mm:ss"),await i.Z.TrainingType.update(e,t),console.log("TrainingType setProperty",e,t),t}}catch(t){console.error("TrainingType setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await i.Z.TrainingType.update(e.id,e)})),console.log("TrainingType multiUpdate",e)}catch(t){console.error("TrainingType multiUpdate",e,t)}}constructor(){this.trainingSessionDueModel=new n.Z}}let o=l},4687:(e,t,r)=>{"use strict";r.d(t,{Z:()=>$});var a=r(98768),s=r(83179),i=r.n(s),n=r(7678),l=r.n(n),o=r(60343),d=r(99303),c=r(17380),u=r(70030),m=r(55361),g=r(76342),f=r(94060),p=r(79418),h=r(72548),y=r(69424),x=r(13842),v=r(33849),b=r(46776),D=r(26100),T=r(60797),S=r(81524),w=r(35024),I=r(26509),j=r(69852),N=r(39544),C=r(26477),M=r(78965),E=r(68648),k=r(29342),F=r(34376),Z=r(17203),A=r(25394),B=r(6834),P=r(52269),L=r(45519);let O=(0,L.ZP)`
    query ReadVessels(
        $limit: Int = 100
        $offset: Int = 0
        $filter: VesselFilterFields = {}
    ) {
        readVessels(filter: $filter, limit: $limit, offset: $offset) {
            nodes {
                id
                title
            }
        }
    }
`,$=({trainingID:e=0,memberId:t=0,trainingTypeId:r=0,vesselId:s=0})=>{let n=(0,y.useRouter)(),{toast:L}=(0,F.pm)(),[$,Y]=(0,o.useState)(!0),[R,V]=(0,o.useState)({}),[_,q]=(0,o.useState)(),[U,H]=(0,o.useState)(new Date),[z,G]=(0,o.useState)(!1),[J,K]=(0,o.useState)([]),[X,W]=(0,o.useState)([]),[Q,ee]=(0,o.useState)(),[et,er]=(0,o.useState)([]),[ea,es]=(0,o.useState)(""),[ei,en]=(0,o.useState)(!1),[el,eo]=(0,o.useState)(""),[ed,ec]=(0,o.useState)(""),[eu,em]=(0,o.useState)([]),[eg,ef]=(0,o.useState)([]),[ep,eh]=(0,o.useState)(!1),[ey,ex]=(0,o.useState)({TrainingTypes:"",TrainerID:"",VesselID:"",Date:""}),[ev,eb]=(0,o.useState)(s),[eD,eT]=(0,o.useState)(!1);(0,x.nV)(er);let[eS]=(0,p.t)(f.HC,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readCaptureImages.nodes;t&&eT(t)},onError:e=>{console.error("getFieldImages error",e)}});(0,o.useEffect)(()=>{eS({variables:{filter:{trainingSessionID:{eq:e}}}})},[]);let ew=async()=>{await eS({variables:{filter:{trainingSessionID:{eq:e}}}})},eI=e=>{ee([{label:"Other",value:"Other"},{label:"Desktop/shore",value:"Onshore"},...(e?.filter(e=>!e.archived)).map(e=>({value:e.id,label:e.title}))])},[ej]=(0,p.t)(O,{fetchPolicy:"cache-and-network",onCompleted:e=>{e.readVessels.nodes&&eI(e.readVessels.nodes)},onError:e=>{console.error("queryVessels error",e)}}),eN=async()=>{await ej({variables:{limit:200,offset:0}})};(0,o.useEffect)(()=>{$&&(eN(),Y(!1))},[$]),(0,x.MX)(e,t=>{H(new Date(t.date));let r={ID:e,Date:i()(t.date).format("YYYY-MM-DD"),Members:t.members.nodes.map(e=>e.id),TrainerID:t.trainer.id,TrainingSummary:t.trainingSummary,TrainingTypes:t.trainingTypes.nodes.map(e=>e.id),VesselID:t.vesselID};q(t),V(r),es(t.trainingSummary);let a=t.members.nodes.map(e=>({label:`${e.firstName??""} ${e.surname??""}`,value:e.id}))||[],s=t.vessel.seaLogsMembers.nodes.map(e=>+e.id);K(a.filter(e=>s.includes(+e.value))),W(t.signatures.nodes.map(e=>({MemberID:e.member.id,SignatureData:e.signatureData,ID:e.id}))),t.procedureFields?.nodes&&(em(t.procedureFields.nodes.map(e=>({fieldId:e.customisedComponentFieldID,status:"Ok"===e.status}))),ef(t.procedureFields.nodes.filter(e=>e.comment).map(e=>({fieldId:e.customisedComponentFieldID,comment:e.comment}))))});let[eC,{loading:eM}]=(0,h.D)(g.dd,{onCompleted:e=>{let t=e.createTrainingSession;t.id>0?(eu.length>0&&eu.map(e=>({status:e.status?"Ok":"Not_Ok",trainingSessionID:t.id,customisedComponentFieldID:e.fieldId,comment:eg.find(t=>t.fieldId==e.fieldId)?.comment})).forEach(e=>{eY({variables:{input:e}})}),e$(),e_(t.id),eX(t.trainingSummary),n.push("/crew-training")):console.error("mutationCreateUser error",e)},onError:e=>{console.error("mutationCreateTrainingSession error",e)}}),[eE,{loading:ek}]=(0,h.D)(g.TV,{onCompleted:r=>{let a=r.updateTrainingSession;a.id>0?(eu.length>0&&eu.map(e=>{let t=_?.procedureFields?.nodes?.find(t=>t.customisedComponentFieldID===e.fieldId);return{id:t?.id,status:e.status?"Ok":"Not_Ok",trainingSessionID:a.id,customisedComponentFieldID:e.fieldId,comment:eg.find(t=>t.fieldId==e.fieldId)?.comment}}).forEach(e=>{if(e.id)eR({variables:{input:e}});else{let{id:t,...r}=e;eY({variables:{input:r}})}}),e$(),e_(e),eX(a.trainingSummary),+t>0?n.push(`/crew/info?id=${t}`):+s>0?n.push(`/vessel/info?id=${s}`):n.push("/crew-training")):console.error("mutationUpdateTrainingSession error",r)},onError:e=>{console.error("mutationUpdateTrainingSession error",e)}}),[eF,{loading:eZ}]=(0,p.t)(f.KX,{fetchPolicy:"cache-and-network",onCompleted:e=>e.readOneTrainingSessionDue.data,onError:e=>(console.error("readOneTrainingSessionDueLoading error:",e),null)}),eA=async(e={},t)=>{let{data:r}=await eF({variables:e});t(r.readOneTrainingSessionDue)},[eB,{loading:eP}]=(0,h.D)(g.ZKO,{onCompleted:e=>{},onError:e=>{console.error("createTrainingSessionDue error",e)}}),[eL,{loading:eO}]=(0,h.D)(g._Bk,{onCompleted:e=>{},onError:e=>{console.error("updateTrainingSessionDue error",e)}}),e$=async()=>{let e=[],t=R.VesselID;R.TrainingTypes.forEach(r=>{let a=et.find(e=>e.id===r);if(!l()(a)&&a.occursEvery>0){let s=i()(R.Date).add(a.occursEvery,"day");R.Members.forEach(a=>{e.push({dueDate:s.format("YYYY-MM-DD"),memberID:a,vesselID:t,trainingTypeID:r})})}});let r=[];l()(e)||await Promise.all(e.map(async e=>{let t={filter:{memberID:{eq:e.memberID},vesselID:{eq:e.vesselID},trainingTypeID:{eq:e.trainingTypeID}}};await eA(t,t=>{r.push({...e,id:t?.id??0})})})),l()(r)||await Promise.all(Array.from(r).map(async e=>{let t={variables:{input:e}};0===e.id?await eB(t):await eL(t)}))},[eY]=(0,h.D)(g.U2,{onCompleted:e=>{let t=e.createCustomisedComponentFieldData;t.id>0&&_?.procedureFields?.nodes?q({..._,procedureFields:{..._.procedureFields,nodes:[..._.procedureFields.nodes,t]}}):console.error("createCustomisedComponentFieldData error",e)},onError:e=>{console.error("createCustomisedComponentFieldData error",e)}}),[eR]=(0,h.D)(g.zv_,{onCompleted:e=>{let t=e.updateCustomisedComponentFieldData;t.id>0?q({..._,procedureFields:{..._.procedureFields,nodes:[..._?.procedureFields?.nodes.filter(e=>e.customisedComponentFieldID!==t.customisedComponentFieldID),{...t}]}}):console.error("updateCustomisedComponentFieldData error",e)},onError:e=>{console.error("updateCustomisedComponentFieldData error",e)}}),eV=async()=>{let t=!1,r={TrainingTypes:"",TrainerID:"",VesselID:"",Date:""};if(ex(r),l()(R.TrainingTypes)&&(t=!0,r.TrainingTypes="Nature of training is required"),R.TrainerID&&R.TrainerID>0||(t=!0,r.TrainerID="Trainer is required"),R.VesselID||R.TrainingLocationID&&R.TrainingLocationID>=0||(t=!0,r.VesselID="Location is required"),void 0===R.Date&&(R.Date=i()().format("YYYY-MM-DD")),null!==R.Date&&i()(R.Date).isValid()||(t=!0,r.Date="The date is invalid"),t){G(!0),ex(r),L({title:"Error",description:r.TrainingTypes||r.TrainerID||r.VesselID||r.Date,variant:"destructive"});return}let a={id:e,date:R.Date?i()(R.Date).format("YYYY-MM-DD"):"",members:R.Members?.join(","),trainerID:R.TrainerID,trainingSummary:ea,trainingTypes:R.TrainingTypes?.join(","),vesselID:R?.VesselID,trainingLocationType:R?.VesselID?"Other"===R.VesselID||"Onshore"===R.VesselID?R.VesselID:"Vessel":"Location"};0===e?await eC({variables:{input:a}}):await eE({variables:{input:a}})},e_=e=>{X.length>0&&X?.forEach(t=>{eq(t,e)})},eq=async(e,t)=>{await eU({variables:{filter:{memberID:{eq:e.MemberID},trainingSessionID:{in:t}}}}).then(r=>{let a=r.data.readMemberTraining_Signatures.nodes;a.length>0?eH({variables:{input:{id:a[0].id,memberID:e.MemberID,signatureData:e.SignatureData,trainingSessionID:t}}}):e.SignatureData&&eG({variables:{input:{memberID:e.MemberID,signatureData:e.SignatureData,trainingSessionID:t}}})}).catch(e=>{console.error("mutationGetMemberTrainingSignatures error",e)})},[eU]=(0,p.t)(f.Se),[eH,{loading:ez}]=(0,h.D)(g.S3v,{onCompleted:e=>{e.updateMemberTraining_Signature.id>0||console.error("mutationUpdateMemberTrainingSignature error",e)},onError:e=>{console.error("mutationUpdateMemberTrainingSignature error",e)}}),[eG,{loading:eJ}]=(0,h.D)(g.Xfh,{onCompleted:e=>{e.createMemberTraining_Signature.id>0||console.error("mutationCreateMemberTrainingSignature error",e)},onError:e=>{console.error("mutationCreateMemberTrainingSignature error",e)}}),eK=(e,t,r)=>{let a=X.findIndex(e=>e.MemberID===r),s=[...X];e?-1!==a?""===e.trim()?s.splice(a,1):s[a].SignatureData=e:s.push({MemberID:r,SignatureData:e}):s.splice(a,1),W(s)};(0,x.$J)(r,V);let eX=e=>{es(e)};(0,o.useEffect)(()=>{l()(R)||eb(s>0||isNaN(parseInt(R?.VesselID,10))?s:parseInt(R?.VesselID,10))},[s,R]);let[eW,eQ]=(0,o.useState)(!1);if((0,o.useEffect)(()=>{eQ(b.Zu)},[]),!eW||!(0,b.Fs)("EDIT_TRAINING",eW)&&!(0,b.Fs)("VIEW_TRAINING",eW)&&!(0,b.Fs)("RECORD_TRAINING",eW))return eW?a.jsx(D.Z,{errorMessage:"Oops You do not have the permission to view this section."}):a.jsx(D.Z,{});let e0=()=>et.filter(e=>R?.TrainingTypes?.includes(e.id)).map(e=>e.customisedComponentField.nodes.length>0?{id:e.id,title:e.title,fields:[...e.customisedComponentField.nodes].sort((e,t)=>e.sortOrder-t.sortOrder)}:null).filter(e=>null!=e),e1=(e,t,r)=>{em([...eu.filter(t=>t.fieldId!==e.id),{fieldId:e.id,status:r}])},e4=e=>{if(eu.length>0){let t=eu.find(t=>t.fieldId==e.id);if(t)return t.status?"Ok":"Not_Ok"}let t=_?.procedureFields?.nodes?.find(t=>t.customisedComponentFieldID==e.id);return t?.status||""},e2=e=>{let t=_?.procedureFields?.nodes?.find(t=>t.customisedComponentFieldID==e.id);if(eg.length>0){let t=eg.find(t=>t.fieldId==e.id);eo(t?.comment||"")}else eo(t?.comment||"");ec(e),eh(!0)},e3=e=>{if(eg.length>0){let t=eg.find(t=>t.fieldId==e.id);if(t)return t.comment}let t=_?.procedureFields?.nodes?.find(t=>t.customisedComponentFieldID==e.id);return t?.comment||e.comment};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(w.Zb,{className:"mb-2.5 mx-4",children:[a.jsx(w.Ol,{children:(0,a.jsxs)(w.ll,{children:[0===e?"New":"Edit"," Training Session"]})}),a.jsx(I.Separator,{className:"my-5"}),!R&&e>0?a.jsx(c.$S,{}):(0,a.jsxs)(w.aY,{className:"p-0",children:[(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"col-span-3 md:col-span-1 my-4 ",children:["Training Details",a.jsx("p",{className:" mt-4 max-w-[25rem] leading-loose mb-4"}),R&&et.filter(e=>R?.TrainingTypes?.includes(e.id)&&e.procedure).length>0&&a.jsx(N.Button,{onClick:()=>en(!0),children:"View Procedures"})]}),(0,a.jsxs)("div",{className:"col-span-3 md:col-span-2 pt-8 pb-5 space-y-6 px-7 border border-border border-dashed rounded-lg",children:[(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("div",{className:"w-full my-4 flex flex-col",children:[a.jsx(T.Label,{label:"Trainer",children:a.jsx(d.Z,{value:R?.TrainerID,vesselID:ev,onChange:e=>{if(!e)return;let t="object"==typeof e&&e.value?e.value:e,r="object"==typeof e&&e.value?e:{value:e,label:`Trainer ${e}`},a=new Set(R?.Members||[]);a.add(t);let s=Array.from(a);V({...R,TrainerID:t,Members:s}),K([...J,r]),W([...X,{MemberID:+t,SignatureData:null}])}})}),a.jsx("small",{className:"text-destructive",children:z&&ey.TrainerID})]}),(0,a.jsxs)("div",{className:"w-full md:mt-4 flex flex-col",children:[a.jsx(u.Z,{value:R?.TrainingTypes,onChange:e=>{V({...R,TrainingTypes:e.map(e=>e.value)})}}),a.jsx("small",{className:"text-red-vivid-500",children:z&&ey.TrainingTypes})]})]}),(0,a.jsxs)("div",{className:"w-full mt-4 flex flex-col",children:[a.jsx(T.Label,{children:"Crew"}),a.jsx(m.Z,{value:t>0?[t.toString()]:(R?.Members||[]).filter(e=>null!=e),vesselID:ev,onChange:e=>{let t=X.filter(t=>e.some(e=>+e.value===t.MemberID));V({...R,Members:e.map(e=>e.value)}),K(e),W(t)}})]}),a.jsx(I.Separator,{className:"my-5"}),(0,a.jsxs)("div",{className:"flex w-full gap-4 mt-4",children:[(0,a.jsxs)("div",{className:"w-full ",children:[a.jsx(k.Z,{mode:"single",onChange:e=>{H(e&&new Date(e.toString())),V({...R,Date:i()(e).format("YYYY-MM-DD")})},value:new Date(U)}),a.jsx("small",{className:"text-destructive",children:z&&ey.Date})]}),(0,a.jsxs)("div",{className:"w-full",children:[Q&&a.jsx(S.Combobox,{options:Q.map(e=>({label:e.label,value:e.value})),defaultValues:_?.trainingLocationType==="Vessel"?_?.VesselID?.toString():_?.trainingLocationType==="Onshore"?"Desktop/shore":_?.trainingLocationType==="Other"?"Other":_?.trainingLocationType==="Location"&&_?.trainingLocation?.id>0?_?.trainingLocation?.id.toString():s?{label:Q.find(e=>e.value===s)?.label,value:s.toString()}:null,isLoading:_,onChange:e=>{V({...R,VesselID:e?"object"!=typeof e||Array.isArray(e)?0:e.value:0})},placeholder:"Select location"}),a.jsx("small",{className:"text-destructive",children:z&&ey.VesselID})]})]}),(0,a.jsxs)("div",{className:"col-span-3 md:col-span-2",children:[e0().length>0&&a.jsx("div",{className:"space-y-8",children:e0().map(t=>(0,a.jsxs)(E.Cz,{children:[a.jsx(T.Label,{label:t.title}),a.jsx(E.yE,{children:t.fields.map(r=>a.jsx(E.Mw,{displayField:"Required"===r.status,displayDescription:r.description,displayLabel:r.fieldName,inputId:r.id,handleNoChange:()=>e1(r,t,!1),defaultNoChecked:"Not_Ok"===e4(r),handleYesChange:()=>e1(r,t,!0),defaultYesChecked:"Ok"===e4(r),commentAction:()=>e2(r),comment:e3(r),displayImage:e>0,fieldImages:eD,onImageUpload:ew,sectionData:{id:e,sectionName:"trainingSessionID"}},r.id))})]},t.id))}),a.jsx("div",{className:"my-4 flex items-center w-full",children:a.jsx(v.Z,{id:"TrainingSummary",placeholder:"Summary of training, identify any outcomes, further training required or other observations.",className:"!w-full  ring-1 ring-inset ",handleEditorChange:eX,content:ea})})]})]})]}),a.jsx(I.Separator,{className:"my-5"}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-3 gap-6",children:[a.jsx("div",{className:"col-span-3 md:col-span-1 md:my-4 ",children:"Signatures"}),a.jsx("div",{className:"col-span-3 sm:col-span-2 md:my-4 flex justify-between flex-wrap gap-4",children:J&&J.map((e,t)=>a.jsx("div",{className:"w-full md:w-96",children:a.jsx(C.Z,{className:"w-full",member:e.label,memberId:e.value,onSignatureChanged:(e,t,r)=>eK(e,t??"",r||0),signature:{id:X.find(t=>t.MemberID===e.value)?.ID}})},t))})]})]})]}),(0,a.jsxs)(M.V,{children:[a.jsx(N.Button,{variant:"back",onClick:()=>n.push("/crew-training"),iconLeft:Z.Z,children:"Cancel"}),a.jsx(N.Button,{onClick:eV,disabled:eM||ek,children:0===e?"Create session":"Update session"})]}),a.jsx(j.Sheet,{open:ei,onOpenChange:en,children:(0,a.jsxs)(j.SheetContent,{side:"right",className:"w-[400px] sm:w-[540px]",children:[a.jsx(j.SheetHeader,{children:a.jsx(j.SheetTitle,{children:"Procedures"})}),R&&et.filter(e=>R?.TrainingTypes?.includes(e.id)&&e.procedure).map(e=>(0,a.jsxs)("div",{className:"mb-4 px-2.5 sm:px-5",children:[a.jsx(A.H4,{children:e.title}),a.jsx("div",{dangerouslySetInnerHTML:{__html:e.procedure}})]},e.id))]})}),a.jsx(B.aR,{open:ep,onOpenChange:eh,children:(0,a.jsxs)(B._T,{children:[(0,a.jsxs)(B.fY,{children:[a.jsx(B.f$,{children:"Add Comment"}),a.jsx(B.yT,{children:"Add a comment for this procedure check."})]}),a.jsx(P.Textarea,{value:el,onChange:e=>eo(e.target.value),placeholder:"Enter your comment here...",rows:4}),(0,a.jsxs)(B.xo,{children:[a.jsx(B.le,{children:"Cancel"}),a.jsx(B.OL,{onClick:()=>{ef([...eg.filter(e=>e.fieldId!==ed.id),{fieldId:ed.id,comment:el}]),eh(!1)},children:"Save Comment"})]})]})})]})}},70030:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var a=r(98768),s=r(60343),i=r(7678),n=r.n(i),l=r(31321),o=r(81524),d=r(79418),c=r(94060);let u=({value:e=[],onChange:t,locked:r,offline:i=!1})=>{let[u,m]=(0,s.useState)([]),[g,f]=(0,s.useState)([]),[p,h]=(0,s.useState)(0),[y,x]=(0,s.useState)(!0),[v,b]=(0,s.useState)(!1),D=new l.Z,T=t=>{let r=t.map(e=>({value:e.id,label:e.title}));r.sort((e,t)=>e.label.localeCompare(t.label)),m(r),f(e.map(e=>r.find(t=>t.value===e)))},[S]=(0,d.t)(c.$0,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTrainingTypes.nodes,r=e.readTrainingTypes.pageInfo;if(t){if(m([...u,...t.map(e=>({value:e.id,label:e.title}))]),r.hasNextPage){let e=p+100;h(e),b(!0),S({variables:{limit:100,offset:e}})}else x(!1),b(!1)}},onError:e=>{console.error("queryTrainingTypes error",e),b(!1)}}),w=async()=>{m([]),h(0),x(!0),b(!0),await S({variables:{limit:100,offset:0}})};return(0,s.useEffect)(()=>{if(!v){let t=[...u];t.sort((e,t)=>e.label.localeCompare(t.label)),m(t),f(e.map(e=>t.find(t=>t.value===e)))}},[v]),(0,s.useEffect)(()=>{i&&D.getAll().then(e=>{T(e)})},[i]),(0,s.useEffect)(()=>{n()(e)||n()(u)||f(e.map(e=>u.find(t=>t.value===e)))},[e,u]),(0,s.useEffect)(()=>{i||w()},[]),a.jsx(o.Combobox,{disabled:r,value:g,options:u,onChange:e=>{if(!e||Array.isArray(e)&&0===e.length){f([]),t([]);return}let r=Array.isArray(e)?e:[e];f(r),t(r)},isLoading:!u,buttonClassName:"w-full",responsiveBadges:!0,labelClassName:"w-full",placeholder:"Select training type",multi:!0})}},55361:(e,t,r)=>{"use strict";r.d(t,{Z:()=>v});var a=r(98768),s=r(60343),i=r(7678),n=r.n(i),l=r(76342),o=r(79418),d=r(72548),c=r(73366),u=r(10090),m=r(81524),g=r(24794),f=r(39544),p=r(71890);function h({openDialog:e,setOpenDialog:t,handleCreate:r,actionText:s,error:i}){return a.jsx(g.Dialog,{open:e,onOpenChange:t,children:(0,a.jsxs)(g.DialogContent,{children:[a.jsx(g.DialogHeader,{children:a.jsx(g.DialogTitle,{className:"font-medium",children:"Add Crew Member"})}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),r(new FormData(e.currentTarget))},children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 border-t pt-6",children:[(0,a.jsxs)("div",{className:"flex gap-4",children:[a.jsx(p.I,{id:"crew-firstName",name:"firstName",type:"text",placeholder:"First Name"}),a.jsx(p.I,{id:"crew-surname",name:"surname",type:"text",placeholder:"Surname"})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[a.jsx(p.I,{id:"crew-username",name:"username",type:"text",placeholder:"Username"}),a.jsx(p.I,{id:"crew-password",name:"password",type:"password",placeholder:"Password"})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[a.jsx(p.I,{id:"crew-email",name:"email",type:"email",placeholder:"Email"}),a.jsx(p.I,{id:"crew-phoneNumber",name:"phoneNumber",type:"text",placeholder:"Phone Number"})]}),i&&a.jsx("div",{className:"text-rose-600",children:i.message})]}),a.jsx(g.DialogFooter,{className:"mt-6",children:a.jsx(f.Button,{type:"submit",children:s})})]})]})})}var y=r(45519);let x=(0,y.ZP)`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                isArchived
                firstName
                surname
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
                departments {
                    nodes {
                        id
                    }
                }
                groups {
                    nodes {
                        id
                        title
                        code
                    }
                }
            }
        }
    }
`,v=({value:e=[],onChange:t,memberIdOptions:r=[],departments:i=[],filterByAdmin:g=!1,offline:f=!1,vesselID:p=0})=>{let[y,v]=(0,s.useState)(!0),[b,D]=(0,s.useState)([]),[T,S]=(0,s.useState)(!1),[w,I]=(0,s.useState)([]),[j,N]=(0,s.useState)(!1),C=new c.Z,M=e=>{let t=p>0?e.filter(e=>e.vehicles.nodes.some(e=>+e.id===p)):e,a={value:"newCrewMember",label:"--- Create Crew Member ---"},s=t.filter(e=>!g||!F(e));if(i.length>0){let e=i.flatMap(e=>e.id),t=s.filter(t=>t.departments.nodes.some(t=>e.includes(t.id))).map(e=>({value:e.id,label:`${e.firstName??""} ${e.surname??""}`}));0===r.length?D([a,...t]):D(t.filter(e=>r.includes(e.value)))}else{let e=s.map(e=>({value:e.id,label:`${e.firstName??""} ${e.surname??""}`}));0===r.length?D([a,...e]):D(e.filter(e=>r.includes(e.value)))}},[E]=(0,o.t)(x,{fetchPolicy:"cache-and-network",onError:e=>{console.error("querySeaLogsMembersList error",e)}}),k=async()=>{let e=[],t=0,r=!0;try{for(;r;){let a=await E({variables:{filter:{isArchived:{eq:!1}},limit:100,offset:t}});if(a.data?.readSeaLogsMembers){let s=a.data.readSeaLogsMembers.nodes,i=a.data.readSeaLogsMembers.pageInfo;s&&s.length>0&&(e=[...e,...s]),r=i?.hasNextPage||!1,t+=100}else r=!1}e.length>0&&M(e)}catch(e){console.error("Error loading all crew members:",e)}};(0,s.useEffect)(()=>{y&&!f&&(k(),v(!1))},[y,f]),(0,s.useEffect)(()=>{f&&C.getAll().then(e=>{M(e)})},[f]);let F=e=>e.groups.nodes?.filter(e=>"admin"===e.code).length>0,[Z]=(0,d.D)(l.qK0,{fetchPolicy:"no-cache",onCompleted:r=>{let a=r.createSeaLogsMember;if(a.id>0){S(!1);let r={value:a.id,label:a.firstName+" "+a.surname};D([...b,r]),I([...w,a.id]),t([r,...e.map(e=>b.find(t=>t.value===e))]),N(!1)}},onError:e=>{console.error("createUser error",e.message),N(e)}}),A=async()=>{let r={input:{firstName:document.getElementById("crew-firstName").value?document.getElementById("crew-firstName").value:null,surname:document.getElementById("crew-surname").value?document.getElementById("crew-surname").value:null,email:document.getElementById("crew-email").value?document.getElementById("crew-email").value:null,phoneNumber:document.getElementById("crew-phoneNumber").value?document.getElementById("crew-phoneNumber").value:null,username:document.getElementById("crew-username").value?document.getElementById("crew-username").value:null,password:document.getElementById("crew-password").value?document.getElementById("crew-password").value:null}};if(f){let a=await C.save({...r.input,id:(0,u.lY)()});S(!1);let s={value:a.id,label:a.firstName+" "+a.surname};D([...b,s]),I([...w,a.id]),t([s,...e.map(e=>b.find(t=>t.value===e))]),N(!1)}else await Z({variables:r})};return(0,s.useEffect)(()=>{if(n()(e)||0===e.length){I([]);return}n()(b)||I(e.map(e=>{let t=String(e);return b.find(e=>String(e.value)===t)||(console.warn("CrewMultiSelectDropdown - Could not find crew with ID:",e),{value:t,label:`Unknown (${e})`})}).filter(Boolean))},[e,b]),(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.Combobox,{options:b,value:w,onChange:e=>{if(!e){I([]),t([]);return}let r=Array.isArray(e)?e:[e];if(r.find(e=>"newCrewMember"===e.value)){S(!0);return}if(0===r.length){I([]),t([]);return}let a=r.filter(e=>e&&"object"==typeof e);I(a),t(a)},placeholder:"Select Crew",multi:!0,responsiveBadges:!0,isLoading:!b}),a.jsx(h,{openDialog:T,setOpenDialog:S,handleCreate:A,actionText:"Add Crew Member",error:j})]})}},33849:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var a=r(98768);r(60343);var s=r(47520);r(30854);var i=r(56937);let n=(0,s.default)(async()=>{},{loadableGenerated:{modules:["app\\ui\\editor.tsx -> react-quill"]},ssr:!1});function l(e,t){return a.jsx(n,{value:e.content,placeholder:e.placeholder,onChange:e.handleEditorChange,onBlur:e.handleEditorBlur,modules:{toolbar:[[{header:[1,2,3,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"}],["link","image"],[{align:[]},{color:[]}],["clean"]]},formats:["header","bold","italic","underline","strike","blockquote","list","bullet","link","image","align","color","code-block"],className:(0,i.cn)("w-full min-h-60 mb-2 bg-card border overflow-auto text-input border-border rounded-lg",t)})}},68648:(e,t,r)=>{"use strict";r.d(t,{Cz:()=>x,Mw:()=>S,a3:()=>b,eN:()=>v,xc:()=>D,yE:()=>T});var a=r(98768),s=r(60343),i=r.n(s),n=r(39544),l=r(81515),o=r(49581),d=r(58830),c=r(67537),u=r(52269),m=r(57103),g=r(70906),f=r(56937),p=r(74602),h=r(69852),y=r(75776);let x=i().forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,f.cn)("h-fit bg-card",e),...t}));x.displayName="CheckField";let v=i().forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,f.cn)("flex flex-col space-y-1.5 pb-2.5",e),...t}));v.displayName="CheckFieldHeader";let b=i().forwardRef(({className:e,children:t,...r},s)=>a.jsx(p.H4,{ref:s,className:(0,f.cn)("",e),...r,children:t}));b.displayName="CheckFieldTitle";let D=i().forwardRef(({className:e,...t},r)=>(0,a.jsxs)("div",{ref:r,className:(0,f.cn)("text-input font-medium border-b border-border flex justify-end",e),...t,children:[a.jsx("div",{className:"flex w-12 h-10 rounded-t-md text-destructive justify-center items-center",children:"No"}),a.jsx("div",{className:"flex w-12 h-10 rounded-t-md text-bright-turquoise-600 text justify-center items-center",children:"Yes"})]}));D.displayName="CheckFieldTopContent";let T=i().forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,f.cn)("pt-0",e),...t}));function S({locked:e=!1,displayField:t=!0,displayLabel:r="",displayDescription:i="",descriptionType:x,inputId:v,handleNoChange:b,handleYesChange:D,defaultNoChecked:T=!1,defaultYesChecked:S=!1,comment:w="",setDescriptionPanelContent:I,setOpenDescriptionPanel:j,setDescriptionPanelHeading:N,className:C="",innerWrapperClassName:M="",onCommentSave:E,onCommentDelete:k,commentAction:F,offline:Z=!1,fieldId:A,onError:B,disabled:P=!1,required:L=!1,hideCommentButton:O=!1,displayImage:$=!1,fieldImages:Y=!1,onImageUpload:R,sectionData:V={id:0,sectionName:"logBookEntryID"},..._}){let[q,U]=(0,s.useState)(S),[H,z]=(0,s.useState)(T),[G,J]=(0,s.useState)(!1),[K,X]=(0,s.useState)(w),[W,Q]=(0,s.useState)(w),[ee,et]=(0,s.useState)(!1),[er,ea]=(0,s.useState)(!1);(0,s.useRef)(!0);let es=e||P,[ei,en]=(0,s.useState)(!1),[el,eo]=(0,s.useState)(""),[ed,ec]=(0,s.useState)(""),eu=async()=>{if(!es){et(!0),ea(!1);try{if(X(W),E){let e=E(W);e instanceof Promise&&await e}J(!1)}catch(e){console.error("Error saving comment:",e),ea(!0),B&&e instanceof Error&&B(e)}finally{et(!1)}}},em=async()=>{if(!es){et(!0),ea(!1);try{if(k)try{let e=k();e instanceof Promise&&await e}catch(e){console.error("Error in onCommentDelete callback:",e)}X(""),Q(""),J(!1)}catch(e){console.error("Error deleting comment:",e),ea(!0),B&&e instanceof Error&&B(e)}finally{et(!1)}}};return t?(console.log(V,$),(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:(0,f.cn)("flex gap-2.5 min-h-16 !mt-0 border-b border-border ",M),children:[(0,a.jsxs)("div",{className:"flex-1 flex flex-col phablet:flex-row pt-2.5 phablet:pt-0 phablet:gap-2.5 items-end phablet:items-center",children:[(0,a.jsxs)(p.P,{className:"text-card-foreground text-base w-full",children:[r,L&&a.jsx("span",{className:"text-destructive ml-1",children:"*"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-5 py-2.5",children:[$&&a.jsx(y.Z,{file:!!(Y&&Array.isArray(Y))&&Y.filter(e=>e.fieldName===v).sort((e,t)=>t.id-e.id),setFile:R,inputId:v,sectionData:V}),i&&a.jsx(n.Button,{variant:"ghost",size:"icon",iconOnly:!0,title:"View description",iconLeft:a.jsx(l.Z,{className:"text-light-blue-vivid-900 fill-light-blue-vivid-50",size:24}),onClick:()=>{I&&j&&N&&(I(i),j(!0),N(r)),eo(i),en(!0),ec(r)}}),!O&&a.jsx(n.Button,{variant:"ghost",size:"icon",iconOnly:!0,title:"Add comment",className:"group",iconLeft:K?a.jsx(o.Z,{className:(0,f.cn)("text-curious-blue-400"),size:24}):a.jsx(d.Z,{className:(0,f.cn)("text-neutral-400 group-hover:text-neutral-400/50","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300"),size:24}),onClick:()=>{F?F():J(!0)}})]})]}),(0,a.jsxs)(g.Ee,{..._,variant:"horizontal",className:(0,f.cn)({"opacity-60":es}),gap:"none",value:q?"yes":H?"no":"",onValueChange:e=>{es||("yes"===e?function(){if(!es)try{D(A),U(!0),z(!1)}catch(e){console.error("Error handling Yes change:",e),B&&e instanceof Error&&B(e)}}():"no"!==e||function(){if(!es)try{b(A),z(!0),U(!1)}catch(e){console.error("Error handling No change:",e),B&&e instanceof Error&&B(e)}}())},disabled:es,children:[a.jsx("div",{className:(0,f.cn)("flex w-12 bg-destructive-foreground justify-center phablet:p-0 items-center"),children:a.jsx(g.mJ,{value:"no",id:`${v}-no_radio`,variant:"destructive",size:"lg"})}),a.jsx("div",{className:(0,f.cn)("flex w-12 bg-bright-turquoise-100 justify-center items-center "),children:a.jsx(g.mJ,{value:"yes",id:`${v}-yes_radio`,variant:"success",size:"lg"})})]})]}),a.jsx(h.Sheet,{open:ei,onOpenChange:en,children:(0,a.jsxs)(h.SheetContent,{side:"left",className:"w-[90%] sm:w-[540px] max-w-2xl",onInteractOutside:()=>{en(!1),eo(""),ec("")},children:[a.jsx(h.SheetHeader,{children:a.jsx(h.SheetTitle,{children:ed})}),a.jsx(h.SheetBody,{children:"string"==typeof el?a.jsx("div",{className:"prose prose-sm max-w-none leading-7",dangerouslySetInnerHTML:{__html:el}}):a.jsx("div",{className:"prose prose-sm max-w-none leading-7",children:el})})]})}),a.jsx(m.AlertDialogNew,{openDialog:G,setOpenDialog:J,title:K?"Edit comment":"Add comment",handleCreate:es?void 0:eu,handleDestructiveAction:!es&&K?em:void 0,showDestructiveAction:!es&&!!K,actionText:ee?"Saving...":"Save",destructiveActionText:"Delete",destructiveLoading:ee,noButton:es,cancelText:es?"Close":"Cancel",loading:ee,children:(0,a.jsxs)("div",{className:"flex flex-col",children:[er&&(0,a.jsxs)("div",{className:"text-destructive mb-2 text-sm",children:["Error ",K?"updating":"saving"," ","comment. Please try again."]}),a.jsx(u.Textarea,{id:`${v}-comment`,disabled:es||ee,rows:4,autoResize:!0,placeholder:"Comment",value:W,onChange:e=>Q(e.target.value),className:(0,f.cn)("max-h-[60svh]",{"border-destructive":er})}),ee&&(0,a.jsxs)("div",{className:"flex items-center justify-center mt-2",children:[a.jsx(c.Z,{className:"h-4 w-4 animate-spin mr-2"}),a.jsx("span",{className:"text-sm",children:"Saving..."})]})]})})]})):null}T.displayName="CheckFieldContent",i().forwardRef(({className:e,...t},r)=>a.jsx("div",{ref:r,className:(0,f.cn)("flex items-center p-4 pt-0",e),...t})).displayName="CheckFieldFooter"},99303:(e,t,r)=>{"use strict";r.d(t,{Z:()=>m});var a=r(98768),s=r(60343),i=r(79418),n=r(69424),l=r(46776),o=r(73366),d=r(81524),c=r(45519);let u=(0,c.ZP)`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                isArchived
                firstName
                surname
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
            }
        }
    }
`,m=({label:e="Trainer",value:t,onChange:r,controlClasses:c="default",placeholder:m="Trainer",isClearable:g=!1,filterByTrainingSessionMemberId:f=0,trainerIdOptions:p=[],memberIdOptions:h=[],multi:y=!1,offline:x=!1,vesselID:v=0,disabled:b=!1})=>{let[D,T]=(0,s.useState)(),[S,w]=(0,s.useState)(y?[]:null),[I,j]=(0,s.useState)(!0),[N,C]=(0,s.useState)([]),M=(0,n.usePathname)(),[E,k]=(0,s.useState)(!1),F=new o.Z,[Z,A]=(0,s.useState)([]),[B,P]=(0,s.useState)({}),[L,O]=(0,s.useState)(0),$=e=>{let t=v>0?e.filter(e=>e.vehicles.nodes.some(e=>+e.id===v)):e;if(t){if(E&&"/reporting"===M){let e=localStorage.getItem("userId");T(t.filter(t=>t.id===e))}else T(t);C(t)}},[Y]=(0,i.t)(u,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readSeaLogsMembers.nodes,r=e.readSeaLogsMembers.pageInfo,a=[...Z,...t];if(A(a),r.hasNextPage){let e=L+t.length;O(e),Y({variables:{filter:B,offset:e,limit:100}});return}$(a)},onError:e=>{console.error("querySeaLogsMembersList error",e)}}),R=async()=>{let e={isArchived:{eq:!1}};if(f>0&&(e={...e,trainingSessions:{members:{id:{contains:f}}}}),x){let e=(await F.getAll()).filter(e=>f>0?!1===e.isArchived&&e.trainingSessions.nodes.some(e=>e.members.nodes.some(e=>e.id===f)):!1===e.isArchived);if(e){if(E&&"/reporting"===M){let t=localStorage.getItem("userId");T(e.filter(e=>e.id===t))}else T(e);C(e)}}else A([]),O(0),P(e),await Y({variables:{filter:e,offset:0,limit:100}})};return(0,s.useEffect)(()=>{I&&(k((0,l.PE)()||!1),R(),j(!1))},[I]),(0,s.useEffect)(()=>{if(t&&D){let e=D.find(e=>e.id===t);e&&w({value:e.id,label:`${e.firstName||""} ${e.surname||""}`,profile:{firstName:e.firstName,surname:e.surname,avatar:null}})}else t||w(null)},[t,D,m]),(0,s.useEffect)(()=>{p.length>0&&N.length>0&&T(N.filter(e=>p.includes(e.id)))},[p,N]),(0,s.useEffect)(()=>{h.length>0&&N.length>0?T(N.filter(e=>h.includes(e.id))):N.length>0&&T(N)},[h,N,m]),a.jsx(d.Combobox,{options:D?.map(e=>({value:e.id,label:`${e.firstName||""} ${e.surname||""}`,profile:{firstName:e.firstName,surname:e.surname,avatar:null}})),value:S,onChange:e=>{w(e),r(y?e:e?.value||null)},multi:y,isLoading:!D,placeholder:m,disabled:b})}},26477:(e,t,r)=>{"use strict";r.d(t,{Z:()=>h});var a=r(98768),s=r(60343),i=r.n(s),n=r(72184),l=r.n(n),o=r(39544),d=r(87175),c=r(4289),u=r(20502),m=r(99562),g=r(25394),f=r(56937),p=r(13842);let h=i().memo(({signature:e,member:t,memberId:r,title:i,onSignatureChanged:n,penColor:h="blue",className:y,description:x,locked:v=!1,...b})=>{let D=(0,s.useRef)(null),[T,S]=(0,s.useState)(e?.signatureData??null);(0,s.useEffect)(()=>{e?.id&&e.id>0&&!e.signatureData&&(0,p.iA)(e.id).then(S).catch(e=>console.error("Fetch sig URL failed:",e))},[e?.id,e?.signatureData]),(0,s.useEffect)(()=>{let e=D.current;e&&T&&(e.clear(),e.fromDataURL(T))},[T]);let w=(0,s.useCallback)(()=>{let e=D.current;e&&!e.isEmpty()&&n(e.toDataURL(),t,r)},[n,t,r]),I=(0,s.useCallback)(()=>{let e=D.current;e?.clear(),n("",t,r),S(null)},[n,t,r]);return(0,s.useMemo)(()=>i??t??"Signature",[i,t]),(0,a.jsxs)("div",{className:(0,f.cn)("relative w-full space-y-2",y),children:[v&&(0,a.jsxs)(d.C,{variant:"destructive",className:"absolute top-2 right-2 text-xs gap-1",children:[a.jsx(c.Z,{className:"h-3 w-3"}),"Locked"]}),a.jsx("div",{children:x&&a.jsx(g.P,{className:"text-sm text-muted-foreground",children:x})}),a.jsx(g.__,{htmlFor:"sig-canvas",children:a.jsx(l(),{...b,ref:D,penColor:h,canvasProps:{id:"sig-canvas",className:(0,f.cn)("border-2 border-dashed border-neutral-400 rounded-lg h-48",v?"bg-muted/50":"bg-white"),style:{width:"100%",touchAction:v?"none":"auto",cursor:v?"not-allowed":"crosshair"}},onEnd:w})}),v&&a.jsx("div",{className:"absolute inset-0 bg-white/60 rounded-lg pointer-events-none"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[a.jsx(u.Z,{className:"h-4 w-4"}),"Draw your signature"]}),a.jsx(o.Button,{size:"sm",iconLeft:m.Z,className:"w-fit",onClick:I,disabled:v,"aria-label":"Clear signature",children:"Clear"})]})]})})},74399:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\crew-training\layout.tsx#default`)},30854:()=>{},9999:(e,t,r)=>{"use strict";r.d(t,{a:()=>s});var a=r(60343);function s(e,t,{getInitialValueInEffect:r}={getInitialValueInEffect:!0}){let[s,i]=(0,a.useState)(!!r&&t);return(0,a.useRef)(null),s||!1}},79320:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});var a=r(93140),s=r(69359),i=r(79824);function n(e,t){let r=(0,i.Q)(e);return isNaN(+r)?(0,a.L)(e,NaN):(null!=t.year&&r.setFullYear(t.year),null!=t.month&&(r=(0,s.q)(r,t.month)),null!=t.date&&r.setDate(t.date),null!=t.hours&&r.setHours(t.hours),null!=t.minutes&&r.setMinutes(t.minutes),null!=t.seconds&&r.setSeconds(t.seconds),null!=t.milliseconds&&r.setMilliseconds(t.milliseconds),r)}},99491:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(97428).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},72997:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(97428).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49581:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(97428).Z)("MessageSquareText",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}],["path",{d:"M13 8H7",key:"14i4kc"}],["path",{d:"M17 12H7",key:"16if0g"}]])},58830:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(97428).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])}};