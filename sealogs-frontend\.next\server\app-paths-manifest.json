{"/_not-found/page": "app/_not-found/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/login/page": "app/login/page.js", "/logout/page": "app/logout/page.js", "/lost-password/page": "app/lost-password/page.js", "/page": "app/page.js", "/redirect/page": "app/redirect/page.js", "/reset-password/page": "app/reset-password/page.js", "/select-client/page": "app/select-client/page.js", "/select-department/page": "app/select-department/page.js", "/crew-training/page": "app/crew-training/page.js", "/crew-training/edit/page": "app/crew-training/edit/page.js", "/calendar/page": "app/calendar/page.js", "/crew-training/create/page": "app/crew-training/create/page.js", "/crew/info/page": "app/crew/info/page.js", "/company-details/page": "app/company-details/page.js", "/crew-training/info/page": "app/crew-training/info/page.js", "/department/edit/page": "app/department/edit/page.js", "/crew/page": "app/crew/page.js", "/department/info/page": "app/department/info/page.js", "/dashboard/page": "app/dashboard/page.js", "/department/page": "app/department/page.js", "/dashboard/log-entries/page": "app/dashboard/log-entries/page.js", "/incident-records/edit/page": "app/incident-records/edit/page.js", "/document-locker/page": "app/document-locker/page.js", "/department/create/page": "app/department/create/page.js", "/incident-records/page": "app/incident-records/page.js", "/incident-records/create/page": "app/incident-records/create/page.js", "/inventory/new/page": "app/inventory/new/page.js", "/inventory/suppliers/page": "app/inventory/suppliers/page.js", "/inventory/view/page": "app/inventory/view/page.js", "/inventory/suppliers/view/page": "app/inventory/suppliers/view/page.js", "/key-contacts/page": "app/key-contacts/page.js", "/inventory/suppliers/new/page": "app/inventory/suppliers/new/page.js", "/key-contacts/create/page": "app/key-contacts/create/page.js", "/key-contacts/edit/page": "app/key-contacts/edit/page.js", "/location-overview/page": "app/location-overview/page.js", "/location/create/page": "app/location/create/page.js", "/location/page": "app/location/page.js", "/log-entries/customise-log-books/edit/page": "app/log-entries/customise-log-books/edit/page.js", "/location/info/page": "app/location/info/page.js", "/location/edit/page": "app/location/edit/page.js", "/maintenance/new/page": "app/maintenance/new/page.js", "/reporting/activity-reports/page": "app/reporting/activity-reports/page.js", "/reporting/crew-seatime-report/page": "app/reporting/crew-seatime-report/page.js", "/log-entries/page": "app/log-entries/page.js", "/maintenance/complete-recurring-task/page": "app/maintenance/complete-recurring-task/page.js", "/reporting/detailed-fuel-report/page": "app/reporting/detailed-fuel-report/page.js", "/log-entries/oldEntry/page": "app/log-entries/oldEntry/page.js", "/reporting/crew-training-completed-report/page": "app/reporting/crew-training-completed-report/page.js", "/maintenance/page": "app/maintenance/page.js", "/inventory/page": "app/inventory/page.js", "/reporting/crew-training-upcoming-overdue-report/page": "app/reporting/crew-training-upcoming-overdue-report/page.js", "/reporting/fuel-summary-report/page": "app/reporting/fuel-summary-report/page.js", "/reporting/fuel-analysis/page": "app/reporting/fuel-analysis/page.js", "/log-entries/pdf/page": "app/log-entries/pdf/page.js", "/reporting/engine-hours-report/page": "app/reporting/engine-hours-report/page.js", "/reporting/maintenance-cost-track/page": "app/reporting/maintenance-cost-track/page.js", "/reporting/page": "app/reporting/page.js", "/reporting/maintenance-status-activity/page": "app/reporting/maintenance-status-activity/page.js", "/risk-evaluations/page": "app/risk-evaluations/page.js", "/reporting/fuel-tasking-analysis/page": "app/reporting/fuel-tasking-analysis/page.js", "/risk-strategies/page": "app/risk-strategies/page.js", "/reporting/simple-fuel-report/page": "app/reporting/simple-fuel-report/page.js", "/settings/crew-duty/edit/page": "app/settings/crew-duty/edit/page.js", "/reporting/service-report/page": "app/reporting/service-report/page.js", "/reporting/trip-report/page": "app/reporting/trip-report/page.js", "/settings/crew-duty/list/page": "app/settings/crew-duty/list/page.js", "/settings/crew-duty/create/page": "app/settings/crew-duty/create/page.js", "/settings/maintenance/category/page": "app/settings/maintenance/category/page.js", "/settings/inventory/category/new/page": "app/settings/inventory/category/new/page.js", "/settings/maintenance/category/new/page": "app/settings/maintenance/category/new/page.js", "/settings/inventory/category/page": "app/settings/inventory/category/page.js", "/training-type/create/page": "app/training-type/create/page.js", "/settings/user-role/edit/page": "app/settings/user-role/edit/page.js", "/training-matrix/page": "app/training-matrix/page.js", "/settings/user-role/create/page": "app/settings/user-role/create/page.js", "/settings/user-role/page": "app/settings/user-role/page.js", "/training-type/info/page": "app/training-type/info/page.js", "/training-type/edit/page": "app/training-type/edit/page.js", "/trip-report-schedule-stop/edit/page": "app/trip-report-schedule-stop/edit/page.js", "/trip-report-schedules/edit/page": "app/trip-report-schedules/edit/page.js", "/trip-report-schedule-stop/create/page": "app/trip-report-schedule-stop/create/page.js", "/training-type/page": "app/training-type/page.js", "/trip-schedules/import/page": "app/trip-schedules/import/page.js", "/trip-schedule-services/page": "app/trip-schedule-services/page.js", "/trip-report-schedules/page": "app/trip-report-schedules/page.js", "/user/create/page": "app/user/create/page.js", "/vessel/create/page": "app/vessel/create/page.js", "/trip-schedules/page": "app/trip-schedules/page.js", "/vessel/info/page": "app/vessel/info/page.js", "/user/edit/page": "app/user/edit/page.js", "/trip-schedule-services/edit/page": "app/trip-schedule-services/edit/page.js", "/vessel/edit/page": "app/vessel/edit/page.js", "/vessel/logbook-configuration/page": "app/vessel/logbook-configuration/page.js", "/vessel/page": "app/vessel/page.js"}