(()=>{var e={};e.id=7194,e.ids=[7194],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},44231:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),s(4335),s(32693),s(78398),s(57757),s(48045);var r=s(40060),i=s(33581),a=s(57567),l=s.n(a),n=s(51650),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d=["",{children:["crew",{children:["info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4335)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew\\info\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32693)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew\\info\\page.tsx"],u="/crew/info/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/crew/info/page",pathname:"/crew/info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6657:(e,t,s)=>{Promise.resolve().then(s.bind(s,15996))},37608:(e,t,s)=>{Promise.resolve().then(s.bind(s,68888))},46890:function(e){var t;t=function(){return function(e,t,s){t.prototype.isBetween=function(e,t,r,i){var a=s(e),l=s(t),n="("===(i=i||"()")[0],o=")"===i[1];return(n?this.isAfter(a,r):!this.isBefore(a,r))&&(o?this.isBefore(l,r):!this.isAfter(l,r))||(n?this.isBefore(a,r):!this.isAfter(a,r))&&(o?this.isAfter(l,r):!this.isBefore(l,r))}}},e.exports=t()},15996:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>F});var r=s(98768),i=s(60343),a=s(69424),l=s(66263),n=s(72548),o=s(13006),d=s(76342),c=s(59445),u=s(7678),m=s.n(u),p=s(51742),h=s(39544),f=s(25394),v=s(52241),g=s(99891),x=s(79418),y=s(45519);let j=(0,y.ZP)`
    query ReadInventories($filter: InventoryFilterFields = {}) {
        readInventories(filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                item
                title
            }
        }
    }
`;var D=s(49581),b=s(17380);let N=({taskList:e})=>{let t=(0,a.usePathname)(),s=(0,a.useSearchParams)(),[n,o]=(0,i.useState)(e),{getVesselWithIcon:d}=(0,v.P)(),[c,u]=(0,i.useState)(),[y,N]=(0,i.useState)(!0),[w,E]=(0,i.useState)({vessel:null,status:null,keyword:null}),C=Array.from(new Set((n||[]).map(e=>+e.inventoryID).filter(Boolean))),[S]=(0,x.t)(j,{onCompleted:e=>{let t=e.readInventories.nodes;t&&u(t)}});(0,i.useEffect)(()=>{y&&(O(),N(!1))},[y]);let O=async()=>{await S({variables:{id:C}})};return(0,i.useEffect)(()=>{o(e)},[e]),(0,i.useEffect)(()=>{if(!e)return;let t=[...e];if(w.vessel){let e=[];Array.isArray(w.vessel)&&w.vessel.length>0?e=w.vessel.map(e=>String(e.value)):w.vessel&&!Array.isArray(w.vessel)&&(e=[String(w.vessel.value)]),e.length>0&&(t=t.filter(t=>{let s=String(t?.basicComponent?.id);return e.includes(s)}))}if(w.status&&(t=t.filter(e=>e?.status===w.status.value)),w.keyword&&w.keyword.value&&w.keyword.value.trim()){let e=w.keyword.value.toLowerCase().trim();t=t.filter(t=>{let s=(t?.name||"").toLowerCase().includes(e),r=(t?.description||"").toLowerCase().includes(e),i=(t?.comments?t.comments.replace(/<[^>]*>/g,"").toLowerCase():"").includes(e);return s||r||i})}o(t)},[e,w]),r.jsx(r.Fragment,{children:e?r.jsx(p.wQ,{columns:[{accessorKey:"name",header:"Task",cellAlignment:"left",cell:({row:e})=>{let i=e.original;return(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"text-foreground flex items-center",children:r.jsx(l.default,{href:`/maintenance?taskID=${i.id}&redirect_to=${t}?${s.toString()}`,className:"focus:outline-none",children:i.name})}),r.jsx("div",{className:"w-14 flex items-center pl-1",children:void 0!==i.comments&&null!==i.comments&&(0,r.jsxs)(f.J2,{children:[r.jsx(f.CM,{asChild:!0,children:r.jsx(h.Button,{variant:"ghost",size:"icon",className:"outline-none px-1",children:r.jsx(D.Z,{className:"w-5 h-5"})})}),r.jsx(f.yk,{className:"w-64",children:r.jsx("div",{className:"leading-loose",children:i.comments})})]})})]}),i.description&&r.jsx("div",{className:"mt-1 text-sm text-muted-foreground",children:i.description})]})}},{accessorKey:"basicComponent",header:"Vessel",cellAlignment:"center",cell:({row:e})=>{let t=e.original,s=d(t.basicComponent?.id,t.basicComponent);return r.jsx(r.Fragment,{children:t.basicComponent?.id!=="0"?(0,r.jsxs)(f.u,{children:[r.jsx(f.aJ,{children:r.jsx("div",{className:"min-w-fit",children:r.jsx(g.Z,{vessel:s})})}),r.jsx(f._v,{children:t.basicComponent.title})]},t.basicComponent.id):null})}},{accessorKey:"inventoryID",header:"Inventory Item",cellAlignment:"center",cell:({row:e})=>{let t=e.original,s=c?.find(e=>+e.id==+t.inventoryID);return r.jsx("div",{className:"text-center",children:s?.title||""})}},{accessorKey:"isOverDue",header:"Status",cellAlignment:"right",cell:({row:e})=>{let t=e.original;return r.jsx("div",{className:"text-right",children:(0,r.jsxs)("div",{className:`ml-3 ${t.isOverDue?.status==="High"?"inline-block rounded px-3 py-1 alert":"inline-block"}`,children:[t.isOverDue?.status&&["High","Medium","Low"].includes(t.isOverDue.status)&&t.isOverDue.days,t.isOverDue?.status==="Completed"&&"Save As Draft"===t.isOverDue.days&&t.isOverDue.days,t.isOverDue?.status==="Upcoming"&&t.isOverDue.days,t.isOverDue?.status==="Completed"&&m()(t.isOverDue.days)&&t.isOverDue.status,t.isOverDue?.status==="Completed"&&!m()(t.isOverDue.days)&&"Save As Draft"!==t.isOverDue.days&&t.isOverDue.days]})})}}],data:n||[],showToolbar:!0,pageSize:20,onChange:({type:e,data:t})=>{E(s=>({...s,[e]:t}))}}):r.jsx(b.hM,{})})};var w=s(83179),E=s.n(w),C=s(46890),S=s.n(C),O=s(75546),I=s(29342),M=s(81524),T=s(30905);E().extend(S());let A=({voyages:e})=>{let[t,s]=(0,o.v1)("dateRange",{defaultValue:"",serialize:e=>e||"",parse:e=>e||""}),[a,n]=(0,o.v1)("vessel",{defaultValue:"",serialize:e=>e||"",parse:e=>e||""}),[d,c]=(0,o.v1)("duty",{defaultValue:"",serialize:e=>e||"",parse:e=>e||""}),[u,m]=(0,i.useState)(null),[h,x]=(0,i.useState)(null),[y,j]=(0,i.useState)(null),{vesselIconData:D,getVesselWithIcon:N}=(0,v.P)(),w=e=>{if(e){let[t,s]=e.split(" "),[r,i,a]=t.split("-");return`${a}/${i}/${r.slice(-2)} at ${s}`}},C=(0,i.useMemo)(()=>{if(!e||!Array.isArray(e))return[];let t=new Map;return e.forEach(e=>{let s=e?.logBookEntry?.vehicle;s&&s.id&&s.title&&t.set(s.id,{value:s.id,label:s.title})}),Array.from(t.values()).sort((e,t)=>e.label.localeCompare(t.label))},[e]),S=(0,i.useMemo)(()=>{if(!e||!Array.isArray(e))return[];let t=new Map;return e.forEach(e=>{let s=e?.dutyPerformed;s&&s.id&&s.title&&t.set(s.id,{value:s.id,label:s.title})}),Array.from(t.values()).sort((e,t)=>e.label.localeCompare(t.label))},[e]),A=(0,i.useMemo)(()=>{if(!e||!Array.isArray(e))return[];let t=[...e];if(u&&(u.from||u.to)&&(t=t.filter(e=>{let t=e.punchIn?E()(e.punchIn):E()(e.logBookEntry.startDate);return u.from&&u.to?t.isBetween(E()(u.from).startOf("day"),E()(u.to).endOf("day"),null,"[]"):u.from?t.isAfter(E()(u.from).startOf("day"))||t.isSame(E()(u.from),"day"):!u.to||t.isBefore(E()(u.to).endOf("day"))||t.isSame(E()(u.to),"day")})),h){let e=String(h.value);t=t.filter(t=>String(t?.logBookEntry?.vehicle?.id)===e)}if(y){let e=String(y.value);t=t.filter(t=>String(t?.dutyPerformed?.id)===e)}return t},[e,u,h,y]),k=(e,t)=>{if(!e||!t)return"0";let s=Math.floor((E()(t).valueOf()-E()(e).valueOf())/36e5);return isNaN(s)?"0":s.toString()};return(0,i.useEffect)(()=>{try{if(t){let e=JSON.parse(t);m(e)}if(a){let e=JSON.parse(a);x(e)}if(d){let e=JSON.parse(d);j(e)}}catch(e){console.warn("Error parsing filter values from URL:",e)}},[t,a,d]),r.jsx("div",{className:"w-full p-0",children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(f.Zb,{className:"mb-4 space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[r.jsx("div",{className:"flex-1 min-w-0",children:r.jsx(I.Z,{mode:"range",type:"date",placeholder:"Select date range",value:u,onChange:e=>{m(e),s(e?JSON.stringify(e):"")},clearable:!0,className:"w-full"})}),r.jsx("div",{className:"flex-1 min-w-0",children:r.jsx(M.Combobox,{options:C,value:h,onChange:e=>{x(e),n(e?JSON.stringify(e):"")},placeholder:"Select vessel",title:"Vessel",className:"w-full",multi:!1})}),r.jsx("div",{className:"flex-1 min-w-0",children:r.jsx(M.Combobox,{options:S,value:y,onChange:e=>{j(e),c(e?JSON.stringify(e):"")},placeholder:"Select duty",title:"Duty",className:"w-full",multi:!1})})]}),(u||h||y)&&(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",A.length," of"," ",e.length," voyages",u&&" • Date filtered",h&&` • Vessel: ${h.label}`,y&&` • Duty: ${y.label}`]})]}),r.jsx(p.wQ,{columns:[{accessorKey:"date",header:({column:e})=>r.jsx(T.u,{column:e,title:"Date"}),cellAlignment:"left",cell:({row:e})=>{let t=e.original;return r.jsx(l.default,{href:`/log-entries?vesselID=${t.logBookEntry.vehicleID}&logentryID=${t.logBookEntry.id}`,children:t.punchIn?(0,O.p6)(t.punchIn):(0,O.p6)(t.logBookEntry.startDate)})},sortingFn:(e,t)=>{let s=e?.original?.punchIn?new Date(e.original.punchIn).getTime():new Date(e?.original?.logBookEntry?.startDate||0).getTime();return(t?.original?.punchIn?new Date(t.original.punchIn).getTime():new Date(t?.original?.logBookEntry?.startDate||0).getTime())-s}},{accessorKey:"vessel",header:({column:e})=>r.jsx(T.u,{column:e,title:"Vessel"}),cellAlignment:"left",cell:({row:e})=>{let t=e.original,s=N(t.logBookEntry.vehicle.id,t.logBookEntry.vehicle);return r.jsx("div",{className:"flex flex-col md:flex-row gap-2 py-2.5",children:"0"!==t.logBookEntry.vehicle.id?r.jsx("div",{className:"flex items-center text-start gap-2",children:(0,r.jsxs)(f.u,{children:[r.jsx(f.aJ,{children:r.jsx("div",{className:"min-w-fit",children:r.jsx(g.Z,{vessel:s})})}),r.jsx(f._v,{children:t.logBookEntry.vehicle.title})]},t.logBookEntry.vehicle.id)}):r.jsx("div",{})})},sortingFn:(e,t)=>{let s=e?.original?.logBookEntry?.vehicle?.title||"",r=t?.original?.logBookEntry?.vehicle?.title||"";return s.localeCompare(r)}},{accessorKey:"dutyPerformed",header:"Duty performed",cellAlignment:"center",cell:({row:e})=>e.original.dutyPerformed.title},{accessorKey:"signIn",header:"Sign in",cellAlignment:"left",cell:({row:e})=>w(e.original.punchIn)},{accessorKey:"signOut",header:"Sign out",cellAlignment:"left",cell:({row:e})=>w(e.original.punchOut)},{accessorKey:"totalSeaTime",header:({column:e})=>r.jsx(T.u,{column:e,title:"Total sea time"}),cellAlignment:"right",cell:({row:e})=>{let t=e.original,s=k(t.punchIn,t.punchOut);return`${s} Hours`},sortingFn:(e,t)=>{let s=parseInt(k(e?.original?.punchIn,e?.original?.punchOut))||0;return(parseInt(k(t?.original?.punchIn,t?.original?.punchOut))||0)-s}}],data:A,showToolbar:!1,pageSize:20})]}):r.jsx(b.hM,{})})};var k=s(13842),B=s(46776);let $=e=>(e.sort((e,t)=>{if("High"===e.isOverDue.status&&"High"!==t.isOverDue.status)return -1;if("High"!==e.isOverDue.status&&"High"===t.isOverDue.status)return 1;if("Medium"===e.isOverDue.status&&"Medium"!==t.isOverDue.status)return -1;if("Medium"!==e.isOverDue.status&&"Medium"===t.isOverDue.status)return 1;if("Medium"===e.isOverDue.status&&"Medium"===t.isOverDue.status)return E()(t.startDate).diff(e.startDate);if("High"===e.isOverDue.status&&"High"===t.isOverDue.status){if("Completed"===e.isOverDue.days)return 1;if("Completed"===t.isOverDue.days)return -1;let s=parseInt(e.isOverDue.days.match(/(\d+)/)[0]);return parseInt(t.isOverDue.days.match(/(\d+)/)[0])-s}if("Upcoming"===e.isOverDue.status&&"Upcoming"===t.isOverDue.status)return parseInt(e.isOverDue?.days?.match(/(\d+)/)?.[0])-parseInt(t.isOverDue?.days?.match(/(\d+)/)?.[0]);if("1"===e.isCompleted&&"1"===t.isCompleted){if("Completed"===e.isOverDue.days||void 0===e.isOverDue.days||null===e.isOverDue.days||""===e.isOverDue.days)return 1;if("Completed"===t.isOverDue.days||void 0===t.isOverDue.days||null===t.isOverDue.days||""===t.isOverDue.days)return -1;let s=E()(e.isOverDue.days.replace("Completed on ",""),"DD/MM/YYYY");return E()(t.isOverDue.days.replace("Completed on ",""),"DD/MM/YYYY").diff(s)}return"1"===e.isCompleted?1:"1"===t.isCompleted?-1:E()(e.expires).diff(t.expires)}),e);var _=s(26100),P=s(62861),R=s(70684),q=s(36895),V=s(17203);function Y({crewId:e}){let t=(0,a.useRouter)(),[s,u]=(0,o.v1)("tab",{defaultValue:"training"}),[m,p]=(0,i.useState)(0),[h,v]=(0,i.useState)(0),[g,x]=(0,i.useState)([]),[y,j]=(0,i.useState)([]),[D,b]=(0,i.useState)([]),[w,E]=(0,i.useState)(!1),[C,S]=(0,i.useState)(!1),[O,I]=(0,i.useState)(!1),[M,T]=(0,i.useState)({});(0,k.Gr)(e,b),(0,k.sy)(e=>{x(e.filter(e=>!e.archived).map(e=>({label:e.title,value:e.id})))}),(0,k.Q0)(e,e=>{let t=e.filter(e=>!e.archived).map(e=>({...e,isOverDue:(0,k.AT)(e)}));j($(t.map(e=>({id:e.id,name:e.name,basicComponentID:e.basicComponentID,comments:e.comments,description:e.description,assignedToID:e.assignedToID,expires:(0,k.tt)(e),status:e.status,startDate:e.startDate,isOverDue:e.isOverDue,basicComponent:e.basicComponent,isCompleted:"Completed"===e.status?"1":"2",inventoryID:e.inventoryID})))),p(t.filter(e=>!["Completed","Save_As_Draft"].includes(e.status)&&!["Completed","Upcoming"].includes(e.isOverDue.status)).length)}),(0,k.yh)(e,e=>{T(e);let[t]=(0,k.Vp)([e],g);v((t?.trainingSessionsDue?.nodes.filter(e=>e.status.isOverdue||e.status.dueWithinSevenDays)??[]).length),localStorage.getItem("userId")===e.id&&S(!0)});let[Y]=(0,n.D)(d.AXh,{onCompleted:()=>t.back(),onError:e=>console.error("mutationUpdateUser error",e)}),F=async e=>{e&&e.id>0&&await Y({variables:{input:{id:e.id,isArchived:!e.isArchived}}})},Z=e=>!w||!(0,B.Fs)(e,w),L=({count:e})=>e?r.jsx("span",{className:"ml-2 flex h-5 w-5 items-center justify-center text-xs font-medium alert !rounded-full",children:e}):null;if(!w||!(0,B.Fs)("VIEW_MEMBER",w)&&!(0,B.Fs)("VIEW_MEMBER_CONTACT",w))return w?r.jsx(_.Z,{errorMessage:"Oops! You do not have the permission to view this section."}):r.jsx(_.Z,{});let H=D&&D.length>0&&!D[0].punchOut?D[0]:null;return(0,r.jsxs)("div",{className:"w-full p-0",children:[(0,r.jsxs)("div",{className:"flex flex-col justify-between md:flex-row",children:[(0,r.jsxs)(f.H2,{className:"flex items-center gap-2",children:[r.jsx("span",{className:"mr-2 font-medium",children:"Crew:"}),r.jsx("span",{className:"flex",children:M?(0,r.jsxs)("span",{className:"flex whitespace-nowrap",children:[M?.firstName||""," ",M?.surname||""]}):r.jsx(P.O,{})}),r.jsx(f.Ct,{variant:M.isArchived?"warning":H?"warning":"primary",className:"hidden min-w-fit rounded h-fit py-0.5 px-1.5 text-sm font-normal lg:inline ms-2",children:M.isArchived?"Archived":H?(0,r.jsxs)(l.default,{href:`/log-entries?vesselID=${H.logBookEntry.vehicle.id}&logentryID=${H.logBookEntry.id}`,className:"text-fire-bush-700 hover:text-fire-bush-800",children:["Active log book at"," ",H.logBookEntry.vehicle.title]}):"No active log books"}),r.jsx(f.Ct,{variant:M.isArchived?"warning":H?"warning":"primary",className:"block w-max rounded py-0.5 px-1.5 text-sm font-normal lg:hidden mb-2 ms-2",children:M.isArchived?"Archived":H?(0,r.jsxs)(l.default,{href:`/log-entries?vesselID=${H.logBookEntry.vehicle.id}&logentryID=${H.logBookEntry.id}`,className:"text-fire-bush-700 hover:text-fire-bush-800",children:["Active log book at"," ",H.logBookEntry.vehicle.title]}):"No active log books"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap items-center justify-end gap-2",children:[w&&(0,B.Fs)(process.env.EDIT_MEMBER||"EDIT_MEMBER",w)&&r.jsx(f.zx,{variant:"destructive",onClick:()=>{I(!0)},children:M?.isArchived?"Retrieve":"Archive"}),(w&&(0,B.Fs)(process.env.EDIT_MEMBER||"EDIT_MEMBER",w)||C)&&r.jsx(f.zx,{variant:"ghost",onClick:()=>t.push(`/user/edit?id=${e}`),className:"border border-base hover:border-input",children:"Edit"}),(w&&(0,B.Fs)(process.env.EDIT_MEMBER||"EDIT_MEMBER",w)||C)&&r.jsx(f.zx,{onClick:()=>t.push("/user/create"),className:`${"training"===s?"hidden":""} ${"qualification"===s?"!mr-0":""}`,children:"Add Qualification"}),w&&"qualification"!==s&&(0,B.Fs)("RECORD_TRAINING",w)&&r.jsx(f.zx,{onClick:()=>t.push(`/crew-training/create?memberId=${e}`),children:"Record training"})]})]}),(M?.email||M?.vehicles||M?.phoneNumber)&&(0,r.jsxs)("div",{className:"ml-[1px] mt-2 mb-3 border-t border-b border-border px-4 pb-4 pt-4",children:[M?.primaryDuty&&(0,r.jsxs)("div",{className:"mt-2 flex items-center",children:[r.jsx("span",{className:"mr-4 w-32",children:"Primary Duty:"}),r.jsx("span",{className:"ms-2",children:M.primaryDuty.title})]}),["email","phoneNumber"].map(e=>(w&&(0,B.Fs)(process.env.VIEW_MEMBER_CONTACT||"VIEW_MEMBER_CONTACT",w)||C)&&M?.[e]&&(0,r.jsxs)("div",{className:"mt-4 flex items-center",children:[r.jsx("span",{className:"mr-4 w-32",children:"email"===e?"Email:":"Phone:"}),r.jsx("span",{className:"ms-2",children:M[e]})]},e)),M.vehicles?.nodes&&(0,r.jsxs)("div",{className:"mb-2 mt-4 flex items-center",children:[r.jsx("span",{className:"mr-4 w-32",children:"Vessels:"}),r.jsx("div",{className:"flex gap-2 flex-wrap md:flex-nowrap",children:M.vehicles.nodes.map(e=>r.jsx(l.default,{href:`/vessel/info?id=${e.id}`,children:r.jsx(f.Ct,{variant:"primary",className:"w-fit h-fit py-2 rounded-lg",children:e.title})},e.id))})]}),M.departments?.nodes&&"true"===localStorage.getItem("useDepartment")&&(0,r.jsxs)("div",{className:"mb-2 mt-4 flex items-center",children:[r.jsx("span",{className:"mr-4 w-32",children:"Departments:"}),r.jsx("div",{className:"flex flex-wrap md:flex-nowrap",children:M.departments.nodes.map(e=>r.jsx(l.default,{href:`/department/info?id=${e.id}`,children:r.jsx("div",{className:"ms-2 my-1 rounded border py-1 px-2 md:my-0",children:e.title})},e.id))})]})]}),r.jsx(R.TooltipProvider,{children:(0,r.jsxs)(q.Tabs,{value:s,onValueChange:u,className:"pt-2 pb-5",children:[(0,r.jsxs)(q.TabsList,{className:"gap-2",children:[(0,B.Fs)("VIEW_TRAINING",w)&&(0,r.jsxs)(q.TabsTrigger,{value:"training",children:["Training",r.jsx(L,{count:h})]}),(0,r.jsxs)(R.Tooltip,{children:[r.jsx(R.TooltipTrigger,{asChild:!0,children:r.jsx("span",{className:"inline-block",children:r.jsx(q.TabsTrigger,{value:"qualification",disabled:!0,children:"Qualifications"})})}),r.jsx(R.TooltipContent,{side:"bottom",children:"Coming soon"})]}),(0,r.jsxs)(q.TabsTrigger,{value:"allocatedTasks",children:["Allocated Tasks",r.jsx(L,{count:m})]}),r.jsx(q.TabsTrigger,{value:"voyages",children:"Voyages"})]}),r.jsx(q.TabsContent,{value:"training",children:Z("VIEW_MEMBER_TRAINING")?r.jsx(_.Z,{errorMessage:"Oops! You do not have permission to view this section."}):r.jsx(c.Z,{memberId:e,excludeFilters:["crew","overdueToggle"]})}),r.jsx(q.TabsContent,{value:"qualification"}),r.jsx(q.TabsContent,{value:"allocatedTasks",children:Z("VIEW_MEMBER_TASKS")?r.jsx(_.Z,{errorMessage:"Oops! You do not have permission to view this section."}):r.jsx(N,{taskList:y})}),r.jsx(q.TabsContent,{value:"voyages",children:Z("VIEW_MEMBER_VOYAGES")?r.jsx(_.Z,{errorMessage:"Oops! You do not have permission to view this section."}):r.jsx(A,{voyages:D})})]})}),r.jsx(f.h9,{openDialog:O,setOpenDialog:I,actionText:M?.isArchived?"Retrieve":"Archive",cancelText:"Cancel",handleCreate:()=>F(M),title:`${M?.isArchived?"Retrieve":"Archive"} User`,children:r.jsx("div",{className:"flex justify-center flex-col px-6 py-6",children:w&&(0,B.Fs)(process.env.DELETE_MEMBER||"DELETE_MEMBER",w)?(0,r.jsxs)("div",{children:["Are you sure you want to"," ",M?.isArchived?"retrieve":"archive"," ",`${M?.firstName||"this user"} ${M?.surname||""}`,"?"]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(f.H4,{slot:"title",className:"text-2xl  leading-6 my-2 ",children:"Warning"}),r.jsx("p",{className:"mt-3 text-slate-500",children:"You do not have permission to archive user."}),r.jsx("hr",{className:"my-6"}),r.jsx("div",{className:"flex justify-end",children:r.jsx(f.zx,{iconLeft:V.Z,variant:"back",onClick:()=>I(!1),children:"Cancel"})})]})})})]})}let F=()=>{let e=(0,a.useSearchParams)().get("id")??0;return r.jsx(Y,{crewId:+e})}},68888:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(98768);s(60343);var i=s(64837);function a({children:e}){return r.jsx(i.Z,{children:e})}},75546:(e,t,s)=>{"use strict";s.d(t,{Br:()=>m,fU:()=>p,o0:()=>c,p6:()=>d,vq:()=>u});var r=s(83179),i=s.n(r),a=s(7678),l=s.n(a),n=s(14826),o=s.n(n);let d=(e="",t=!0)=>{let s;if(l()(o()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[s,r,i]=e.split("-"),a=t?s.slice(-2):s,l=parseInt(i,10).toString().padStart(2,"0"),n=parseInt(r,10).toString().padStart(2,"0");return`${l}/${n}/${a}`}if(!(s=e&&"object"==typeof e?i()(e.toString()):i()(e)).isValid())return"";let r=s.format("DD"),a=s.format("MM"),n=t?s.format("YY"):s.format("YYYY");return`${r}/${a}/${n}`},c=(e="",t=!0)=>{let s;if(l()(o()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[s,r,i]=e.split("-"),a=t?s.slice(-2):s,l=parseInt(i,10).toString().padStart(2,"0"),n=parseInt(r,10).toString().padStart(2,"0");return`${l}/${n}/${a} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[s,r]=e.split(" "),[i,a,l]=s.split("-"),n=t?i.slice(-2):i,o=r.split(":"),d=o[0].padStart(2,"0"),c=o[1].padStart(2,"0"),u=parseInt(l,10).toString().padStart(2,"0"),m=parseInt(a,10).toString().padStart(2,"0");return`${u}/${m}/${n} ${d}:${c}`}if(!(s=e&&"object"==typeof e?i()(e.toString()):i()(e)).isValid())return"";let r=s.format("DD"),a=s.format("MM"),n=t?s.format("YY"):s.format("YYYY"),d=s.format("HH:mm");return`${r}/${a}/${n} ${d}`},u=(e="")=>l()(o()(e))?"":i()(e).format("YYYY-MM-DD HH:mm:ss"),m=(e="")=>l()(o()(e))?new Date:new Date(`${e}T10:00:00Z`),p=(e,t)=>{let s=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),r=e=>e.includes(" ")?e.replace(" ","T"):e,i=e=>{if(!e||"string"!=typeof e)return null;if(s(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(r(e))},a=i(e),l=i(t);return!a||!l||isNaN(a.getTime())||isNaN(l.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):l>a}},33849:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n});var r=s(98768);s(60343);var i=s(47520);s(30854);var a=s(56937);let l=(0,i.default)(async()=>{},{loadableGenerated:{modules:["app\\ui\\editor.tsx -> react-quill"]},ssr:!1});function n(e,t){return r.jsx(l,{value:e.content,placeholder:e.placeholder,onChange:e.handleEditorChange,onBlur:e.handleEditorBlur,modules:{toolbar:[[{header:[1,2,3,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"}],["link","image"],[{align:[]},{color:[]}],["clean"]]},formats:["header","bold","italic","underline","strike","blockquote","list","bullet","link","image","align","color","code-block"],className:(0,a.cn)("w-full min-h-60 mb-2 bg-card border overflow-auto text-input border-border rounded-lg",t)})}},4335:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\crew\info\page.tsx#default`)},32693:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\crew\layout.tsx#default`)},8087:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(97428).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},49581:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(97428).Z)("MessageSquareText",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}],["path",{d:"M13 8H7",key:"14i4kc"}],["path",{d:"M17 12H7",key:"16if0g"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,8822,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7380,7944,336,9445],()=>s(44231));module.exports=r})();