"use strict";exports.id=6889,exports.ids=[6889,5776],exports.modules={77664:(e,t,a)=>{a.d(t,{j:()=>s});let s=e=>{let t=document.createElement("div");return t.innerHTML=e,t.textContent||t.innerText||""}},46347:(e,t,a)=>{a.d(t,{Z:()=>v});var s=a(98768),r=a(60343),n=a(28147),l=a(83048),i=a.n(l),o=a(25394),c=a(67537),d=a(71890),u=a(56937);function m({files:e,onFilesSelected:t,text:a="Documents and Images",subText:l,bgClass:i="",multipleUpload:o=!0,acceptedFileTypes:m=".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf,.csv",isLoading:p=!1,renderFileItem:h,onFileClick:v,children:g,displayFiles:x=!0}){let[f,b]=(0,r.useState)(!1),y=(0,r.useRef)(null),j=(0,u.cn)("relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none",f?"bg-accent border-primary":"bg-accent/50 border-border","text-foreground hover:bg-accent hover:border-primary","min-h-[10rem] cursor-pointer select-none",i),k=e=>{t(e)},C=e=>t=>{t.preventDefault(),b(e)},D=(e,t)=>(0,s.jsxs)("div",{onClick:()=>v?.(e),className:"flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden",children:[s.jsx(n.default,{src:"/sealogs-document_upload.svg",alt:"Document",width:48,height:48,className:"mb-2"}),s.jsx("div",{className:"text-xs text-center break-all text-muted-foreground",children:e.title})]},t);return(0,s.jsxs)("div",{className:"w-full pt-4 lg:pt-0",children:[e.length>0&&x&&s.jsx("div",{className:"flex flex-wrap gap-4 mb-4",children:e.filter(e=>e.title?.length>0).map((e,t)=>h?h(e,t):D(e,t))}),(0,s.jsxs)("form",{className:j,onSubmit:e=>e.preventDefault(),onDragEnter:C(!0),onDragOver:C(!0),onDragLeave:C(!1),onDrop:e=>{e.preventDefault(),b(!1),e.dataTransfer.files&&k(e.dataTransfer.files)},onClick:()=>{y.current&&(y.current.value="",y.current.click())},"aria-label":"File uploader drop zone",children:[s.jsx("span",{className:"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider",children:a}),s.jsx(d.I,{ref:y,type:"file",className:"hidden",multiple:o,accept:m,onChange:e=>{e.target.files&&k(e.target.files)}}),(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2 pointer-events-none",children:[s.jsx(n.default,{src:"/sealogs-document_upload.svg",alt:"Upload illustration",width:96,height:96,className:"relative -translate-x-2.5",priority:!0}),l&&s.jsx("span",{className:"text-sm font-medium text-neutral-400",children:l})]})]}),p&&(0,s.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2",children:[s.jsx(c.Z,{className:"h-5 w-5 animate-spin text-primary"}),s.jsx("span",{className:"text-sm text-muted-foreground",children:"Uploading..."})]}),g]})}var p=a(34376);let h=new(i()).S3({endpoint:"https://ddde1c1cd1aa25641691808dcbafdeb7.r2.cloudflarestorage.com",accessKeyId:"06c3e13a539f24e6fdf7075bf381bf5e",secretAccessKey:"****************************************************************",signatureVersion:"v4",region:"auto"});function v({files:e=[],setFiles:t,multipleUpload:a=!0,text:l="Documents and Images",subText:i,bgClass:c="",accept:d=".xlsx,.xls,image/*,.doc, .docx,.ppt, .pptx,.txt,.pdf,.csv",bucketName:u="sealogs",prefix:v="",displayFiles:g=!0}){let[x,f]=(0,r.useState)(!1),[b,y]=(0,r.useState)(""),[j,k]=(0,r.useState)(!1),[C,D]=(0,r.useState)(0),w=async s=>{let r=C+"-"+v+s.name;if(e?.some(e=>e.title===r)){(0,p.Am)({description:"File with same name already exists!",variant:"destructive"});return}f(!0),h.putObject({Bucket:u,Key:r,Body:s},(e,s)=>{if(f(!1),e)console.error(e),(0,p.Am)({description:"Failed to upload file",variant:"destructive"});else{let e={title:r};a?t(t=>[...t,e]):t([e])}})},N=e=>{h.getObject({Bucket:u,Key:e.title},async(t,a)=>{if(t)console.error(t),(0,p.Am)({description:"Failed to download file",variant:"destructive"});else{let t=e.title.split(".").pop()||"",s=new Blob([a?.Body]),r=URL.createObjectURL(s);if(t.match(/^(jpg|jpeg|png|gif|bmp)$/i))y(r),k(!0);else if(t.match(/^(pdf)$/i)){let e=new Blob([a?.Body],{type:"application/pdf"}),t=URL.createObjectURL(e);window.open(t,"_blank"),URL.revokeObjectURL(t)}else{(0,p.Am)({description:"File type not supported to view. Please save the file to view.",variant:"destructive"});let t=document.createElement("a");t.target="_blank",t.href=r,t.download=e.title,t.click(),URL.revokeObjectURL(r)}}})};return s.jsx(m,{files:e,onFilesSelected:e=>{Array.from(e).forEach(w)},text:l,subText:i,bgClass:c,multipleUpload:a,acceptedFileTypes:d,isLoading:x,renderFileItem:(e,t)=>(0,s.jsxs)("div",{onClick:()=>N(e),className:"flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden",children:[s.jsx(n.default,{src:"/sealogs-document_upload.svg",alt:"Document",width:48,height:48,className:"mb-2"}),s.jsx("div",{className:"text-xs text-center break-all text-muted-foreground",children:e.title.replace(C+"-","")})]},t),displayFiles:g,onFileClick:N,children:s.jsx(o.h9,{openDialog:j,setOpenDialog:k,noButton:!0,actionText:"Close",title:"Image Preview",children:s.jsx("div",{className:"flex items-center justify-center",children:s.jsx("img",{src:b,alt:"Preview",className:"max-w-full max-h-96 object-contain"})})})})}},75776:(e,t,a)=>{a.d(t,{Z:()=>f});var s=a(98768),r=a(60343),n=a(83048),l=a.n(n),i=a(25394),o=a(34376),c=a(78853),d=a(69422),u=a(69424),m=a(72548),p=a(76342),h=a(46347),v=a(93488),g=a(50058);let x=new(l()).S3({endpoint:"https://ddde1c1cd1aa25641691808dcbafdeb7.r2.cloudflarestorage.com",accessKeyId:"06c3e13a539f24e6fdf7075bf381bf5e",secretAccessKey:"****************************************************************",signatureVersion:"v4",region:"auto"});function f({file:e=!1,setFile:t,inputId:a,buttonType:n="icon",sectionData:l={id:0,sectionName:"logBookEntryID"}}){let f=(0,u.useSearchParams)().get("logentryID")??0,[b,y]=(0,r.useState)(!1),[j,k]=(0,r.useState)(!1),[C,D]=(0,r.useState)(!1),[w,N]=(0,r.useState)(0),[S,E]=(0,r.useState)([]),[I,T]=(0,r.useState)(!1),[O,B]=(0,r.useState)(!1),[F,M]=(0,r.useState)([]),[A,L]=(0,r.useState)([]),_=(0,g.k)(),R=e=>{if(!e||!e.name){console.error("No file name provided");return}x.getObject({Bucket:"captures",Key:e.name},async(t,a)=>{if(t)console.error(t),e.id&&V({variables:{ids:[+e.id]}});else{if(!e||!e.name){console.error("No file name provided");return}let t=e.name.split(".").pop()||"",r=new Blob([a?.Body]),n=URL.createObjectURL(r);if(t.match(/^(jpg|jpeg|png|gif|bmp)$/i)){k(n),D(!0);let r=Buffer.from(a?.Body).toString("base64"),l=new TextDecoder().decode(a?.Body);var s=`data:image/${t};base64,${r}`;l.startsWith("�PNG")||(s=l),k(s),void 0===F.find(t=>t.name===e.name)?M(t=>[...t,{...e,imageData:s}]):M(t=>t.map(t=>t.name===e.name?{...t,imageData:s}:t))}else{let t=new TextDecoder().decode(a?.Body);k(t),void 0===F.find(t=>t.name===e.name)?M(a=>[...a,{...e,imageData:t}]):M(a=>a.map(a=>a.name===e.name?{...a,imageData:t}:a)),D(!0)}}})},U=async(t=!1)=>{if(y(!0),B(!1),T(!1),k(null),D(!1),e&&e.length>0&&!t){e.forEach(e=>{R(e)});return}let a=await navigator.mediaDevices.enumerateDevices();if(a.some(e=>"videoinput"===e.kind))E(a.filter(e=>"videoinput"===e.kind));else{(0,o.Am)({description:"No camera found. Please connect a camera.",variant:"destructive"});return}navigator.mediaDevices.getUserMedia({video:{facingMode:"environment"},audio:!1}).then(e=>{let t=document.getElementById("camera-video");t.srcObject=e,t.play()}).catch(e=>{console.error("Error accessing camera:",e)})},P=()=>{let e=document.getElementById("camera-video");e&&e.srcObject&&(e.srcObject.getTracks().forEach(e=>e.stop()),e.srcObject=null)},[$]=(0,m.D)(p.fQS,{onCompleted:e=>{e.createSectionMemberImage,t()},onError:e=>{console.error("Error updating comment",e)}});async function Y(e){var s;e.imageData&&(s=e.name||w+"-capture-"+Date.now()),s||(s=w+"-capture-"+Date.now()),$({variables:{input:{name:s,fieldName:a,imageType:"FieldImage",[l.sectionName]:"logBookEntryID"===l.sectionName?f:l.id}}}),e.imageData&&x.putObject({Bucket:"captures",Key:s,Body:e.imageData},(e,a)=>{e?console.error(e):t()})}let Z=()=>{let e=document.getElementById("camera-video");if(!e){console.error("Video element not found");return}let t=e.srcObject?e.srcObject.getVideoTracks()[0].getSettings().deviceId:null,a=S.find(e=>"videoinput"===e.kind&&e.deviceId!==t);a?navigator.mediaDevices.getUserMedia({video:{deviceId:a.deviceId},audio:!1}).then(t=>{e.srcObject=t,e.play()}).catch(e=>{console.error("Error switching camera:",e)}):(0,o.Am)({description:"No other camera found to switch.",variant:"destructive"})},H=()=>{if(0===F.length){(0,o.Am)({description:"Please capture or upload an image first.",variant:"destructive"});return}(0,o.Am)({description:"Please capture or upload an image first.",variant:"destructive"}),F.forEach(e=>{Y(e)}),M([]),L([]),k(null),D(!1),y(!1),P(),(0,o.Am)({description:"Images uploaded successfully."})},[V]=(0,m.D)(p.l9V,{onCompleted:e=>{e.deleteCaptureImage&&(t(),(0,o.Am)({description:"Image deleted successfully."}))},onError:e=>{console.error("Error deleting image",e),(0,o.Am)({description:"Failed to delete image.",variant:"destructive"})}}),z=e=>{M(t=>t.filter(t=>t.name!==e.name)),e.imageData&&(x.deleteObject({Bucket:"captures",Key:e.name||""},(e,t)=>{e?console.error("Error deleting image:",e):(0,o.Am)({description:"Image deleted successfully."})}),e.id&&V({variables:{ids:[+e.id]}}))};return(0,s.jsxs)(s.Fragment,{children:[s.jsx(i.zx,{variant:"icon"===n?"ghost":"outline",iconOnly:"icon"===n,size:"icon"===n?"icon":"default",title:"Add comment",className:"icon"===n?"group":"",iconLeft:s.jsx(c.Z,{className:"icon"===n?(0,d.cn)(e&&e.length>0?"text-curious-blue-400 group-hover:text-curious-blue-400/50":"text-neutral-400 group-hover:text-neutral-400/50","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300"):"",size:24}),onClick:()=>U(!1),children:"button"===n&&(0,v.p)(_.phablet,"Capture / Upload","Capture / Upload Image")}),(0,s.jsxs)(i.h9,{openDialog:b,setOpenDialog:y,title:I?"Files":"Camera",handleCreate:()=>{j?H():(0,o.Am)({description:"Please capture an image first.",variant:"destructive"})},handleCancel:()=>{y(!1),k(null),D(!1),M([]),P(),L([]),t()},actionText:"Save",cancelText:"Close",loading:!1,children:[(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[F.length>0&&s.jsx("div",{className:"flex flex-wrap mb-4",children:F.map((e,t)=>(0,s.jsxs)("div",{className:"w-1/4 p-1 rounded-md relative",children:[s.jsx("img",{src:e.imageData,alt:`Captured ${t}`,className:"object-cover",onClick:()=>{k(e.imageData),D(!0),P()}},t),s.jsx(i.zx,{variant:"destructive",size:"icon",className:"absolute top-1 right-1 p-0 size-5",onClick:()=>{z(e)},children:"\xd7"})]},t))}),I?s.jsx(h.Z,{files:A,setFiles:L,accept:"image/*",bucketName:"captures",multipleUpload:!0,prefix:f+"-",displayFiles:!1}):(0,s.jsxs)(s.Fragment,{children:[s.jsx("video",{id:"camera-video",style:{display:C?"none":"block"}}),s.jsx("img",{src:j,alt:"Captured",style:{display:C?"block":"none"}})]})]}),(0,s.jsxs)("div",{className:"flex items-center mt-4 gap-2 justify-between",children:[!C&&!I&&s.jsx(i.zx,{onClick:()=>{let e=document.getElementById("camera-video");if(!e){console.error("Video element not found");return}let t=document.createElement("canvas");t.width=e.videoWidth,t.height=e.videoHeight;let a=t.getContext("2d");if(!a){console.error("Failed to get canvas context");return}a.drawImage(e,0,0,t.width,t.height);let s=t.toDataURL("image/png");e.srcObject&&(e.srcObject.getTracks().forEach(e=>e.stop()),e.srcObject=null),s&&(k(s),D(!0),M(e=>[...e,{name:w+"-capture-"+Date.now(),imageData:s}]))},className:"mt-2",children:"Capture"}),C&&!I&&s.jsx(s.Fragment,{children:s.jsx(i.zx,{onClick:()=>{k(null),D(!1),U(!0)},className:"mt-2",children:"Recapture"})}),S.length>1&&s.jsx(i.zx,{onClick:()=>{Z()},className:"mt-2",children:"Switch Camera"}),I?s.jsx(i.zx,{onClick:()=>{T(!1),U()},className:"mt-2",children:"Capture Image"}):s.jsx(i.zx,{onClick:()=>{P(),T(!0)},className:"mt-2",children:"Upload Image"})]})]})]})}},76889:(e,t,a)=>{a.d(t,{Z:()=>ey});var s=a(98768),r=a(60343),n=a(79418),l=a(72548),i=a(94060),o=a(76342),c=a(24894),d=a(35024),u=a(83179),m=a.n(u),p=a(69424),h=a(13006),v=a(66263),g=a(57103),x=a(39544),f=a(18415),b=a(13609),y=a(8087),j=a(52016),k=a(49581),C=a(17203),D=a(81311),w=a(8750),N=a(8416),S=a(13842),E=a(7678),I=a.n(E),T=a(43926),O=a(34376),B=a(75546);a(46776);var F=a(26100),M=a(46347),A=a(71890),L=a(52269),_=a(40533),R=a(81524),U=a(29342),P=a(25394),$=a(70684),Y=a(36895),Z=a(96268),H=a(29052),V=a(33849),z=a(13295),q=a(54928),W="Progress",[K,Q]=(0,z.b)(W),[G,X]=K(W),J=r.forwardRef((e,t)=>{var a,r;let{__scopeProgress:n,value:l=null,max:i,getValueLabel:o=ea,...c}=e;(i||0===i)&&!en(i)&&console.error((a=`${i}`,`Invalid prop \`max\` of value \`${a}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let d=en(i)?i:100;null===l||el(l,d)||console.error((r=`${l}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let u=el(l,d)?l:null,m=er(u)?o(u,d):void 0;return(0,s.jsx)(G,{scope:n,value:u,max:d,children:(0,s.jsx)(q.WV.div,{"aria-valuemax":d,"aria-valuemin":0,"aria-valuenow":er(u)?u:void 0,"aria-valuetext":m,role:"progressbar","data-state":es(u,d),"data-value":u??void 0,"data-max":d,...c,ref:t})})});J.displayName=W;var ee="ProgressIndicator",et=r.forwardRef((e,t)=>{let{__scopeProgress:a,...r}=e,n=X(ee,a);return(0,s.jsx)(q.WV.div,{"data-state":es(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...r,ref:t})});function ea(e,t){return`${Math.round(e/t*100)}%`}function es(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function er(e){return"number"==typeof e}function en(e){return er(e)&&!isNaN(e)&&e>0}function el(e,t){return er(e)&&!isNaN(e)&&e<=t&&e>=0}et.displayName=ee;var ei=a(56937);let eo=r.forwardRef(({className:e,value:t,...a},r)=>s.jsx(J,{ref:r,className:(0,ei.cn)("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",e),...a,children:s.jsx(et,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));eo.displayName=J.displayName;var ec=a(51742),ed=a(77664),eu=a(69748),em=a(52241),ep=a(45519);let eh=(0,ep.ZP)`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                firstName
                surname
            }
        }
    }
`;var ev=a(50058),eg=a(93488),ex=a(75776),ef=a(93778);let eb=({record:e,onEdit:t,onDelete:a,className:r=""})=>(0,s.jsxs)("div",{className:(0,ei.cn)("border border-border bg-card rounded-md relative flex justify-between items-center gap-2 px-2.5",r),children:[(0,s.jsxs)("div",{className:"py-2 pe-14 space-y-1",children:[e.description&&s.jsx("div",{className:" normal-case line-clamp-2",dangerouslySetInnerHTML:{__html:e.description}}),e.author&&s.jsx("div",{className:"text-[10px] uppercase text-curious-blue-400",children:(0,s.jsxs)("span",{children:[e.author.firstName&&(0,s.jsxs)("span",{children:["By ",e.author.firstName," ",e.author.surname]}),e.time&&(0,s.jsxs)("span",{children:[" • ",m()(e.time).format("MMM DD, YYYY HH:mm")]})]})})]}),(0,s.jsxs)("div",{className:"flex absolute top-0 right-0",children:[s.jsx(x.Button,{iconLeft:f.Z,variant:"warning",iconOnly:!0,size:"sm",className:"text-sm rounded-none rounded-bl-md p-1 h-fit aspect-square",onClick:()=>t(e)}),s.jsx(x.Button,{iconLeft:b.Z,variant:"destructive",iconOnly:!0,size:"sm",className:"h-fit p-1 rounded-none rounded-tr-md aspect-square",onClick:()=>a(e.id)})]})]},`${e.id}-record-${e.time||""}`);function ey({taskId:e,redirectTo:t,inSidebar:a=!1,onSidebarClose:u,vesselID:f=0}){let[b,E]=(0,r.useState)(!0),z=(0,p.useRouter)(),q=(0,p.useSearchParams)(),W=(0,p.usePathname)(),{toast:K}=(0,O.pm)(),[Q,G]=(0,r.useState)(),[X,J]=(0,r.useState)(),[ee,et]=(0,r.useState)(),[ea,es]=(0,r.useState)(),[er,en]=(0,r.useState)(),[el,ei]=(0,r.useState)([]),[ep,ey]=(0,r.useState)([]),[ej,ek]=(0,r.useState)(),[eC,eD]=(0,r.useState)(""),[ew,eN]=(0,r.useState)(""),[eS,eE]=(0,r.useState)(0),[eI,eT]=(0,r.useState)(""),[eO,eB]=(0,r.useState)(!1),[eF,eM]=(0,r.useState)(!1),[eA,eL]=(0,r.useState)(!1),[e_,eR]=(0,r.useState)(null),[eU,eP]=(0,r.useState)(!1),[e$,eY]=(0,r.useState)(!1),[eZ,eH]=(0,r.useState)([]),[eV,ez]=(0,r.useState)(),[eq,eW]=(0,r.useState)(),[eK,eQ]=(0,r.useState)(!1),[eG,eX]=(0,r.useState)([]),[eJ,e0]=(0,r.useState)(!1),[e1,e2]=(0,r.useState)(!1),[e4,e3]=(0,r.useState)(!1),[e6,e5]=(0,r.useState)(0),[e7,e8]=(0,r.useState)(!1),[e9,te]=(0,r.useState)(!1),[tt,ta]=(0,r.useState)(),[ts,tr]=(0,r.useState)(!1),[tn,tl]=(0,r.useState)(0),[ti,to]=(0,r.useState)(!1),[tc,td]=(0,r.useState)(),[tu,tm]=(0,r.useState)([]),[tp,th]=(0,r.useState)(),[tv,tg]=(0,r.useState)("task"),[tx,tf]=(0,r.useState)(null),[tb,ty]=(0,r.useState)(""),[tj,tk]=(0,r.useState)(""),[tC,tD]=(0,r.useState)(""),[tw,tN]=(0,r.useState)(0),[tS,tE]=(0,r.useState)([]),[tI,tT]=(0,r.useState)([]),[tO,tB]=(0,r.useState)([]),[tF,tM]=(0,r.useState)([]),[tA,tL]=(0,r.useState)([]),[t_,tR]=(0,r.useState)(!1),[tU,tP]=(0,h.v1)("taskTab",{defaultValue:"Details"}),[t$,tY]=(0,r.useState)(0),[tZ,tH]=(0,r.useState)(!1),tV=(0,Z.ac)("(min-width: 768px)"),[tz,tq]=(0,r.useState)(!1),[tW,tK]=(0,r.useState)(!1),[tQ,tG]=(0,r.useState)(!1),[tX,tJ]=(0,r.useState)(!1),[t0,t1]=(0,r.useState)(!1),[t2,t4]=(0,r.useState)(0),[t3,t6]=(0,r.useState)(!1),[t5,t7]=(0,r.useState)(!1),[t8,t9]=(0,r.useState)(!1),[ae,at]=(0,r.useState)([]),[aa,as]=(0,r.useState)([]),[ar,an]=(0,r.useState)({value:"expiry",label:"Due by date"}),al=(0,ev.k)(),[ai,ao]=(0,r.useState)([]),[ac,ad]=(0,r.useState)([]),au=(0,eg.e)(),{getVesselWithIcon:am}=(0,em.P)(),ap=q.get("taskID")??0,ah=[{label:"On Voyage",value:"OnVoyage"},{label:"Available For Voyage",value:"AvailableForVoyage"},{label:"Out Of Service",value:"OutOfService"}],av=[{label:"Crew Unavailable",value:"CrewUnavailable"},{label:"Skipper/Master Unavailable",value:"MasterUnavailable"},{label:"Planned Maintenance",value:"PlannedMaintenance"},{label:"Breakdown",value:"Breakdown"},{label:"Other",value:"Other"}],[ag]=(0,n.t)(i.Hb,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readVesselStatuss.nodes;t&&t7(t[0]),0===t.length&&sf({variables:{input:{date:m()().format("YYYY-MM-DD"),vesselID:ej?.basicComponentID}}})},onError:e=>{console.error("Error getting vessel status",e)}}),ax=async()=>{await sy({variables:{filter:{componentMaintenanceCheckID:{eq:ap}}}})},af=e=>{tk(e)},ab=e=>{tD(e)};(0,S.UU)(e,e=>{eH(e)});let[ay]=(0,n.t)(eh,{fetchPolicy:"cache-and-network",onError:e=>{console.error("querySeaLogsMembersList error",e)}}),[aj]=(0,n.t)(i.$e,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readInventoryList[0].list;t&&es(t)},onError:e=>{console.error("queryInventoriesEntry error",e)}}),[ak]=(0,n.t)(i.$e,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readInventoryList[0].list;t&&(es(t.filter(e=>e.vessel.id==t$)),es(t.filter(e=>e.vessel.id==t$)))},onError:e=>{console.error("queryInventoriesEntry error",e)}});(0,S.sy)(e=>{let t=e.filter(e=>!e.archived).map(e=>({...e}));t.push({title:"Other",id:0}),G(t)});let aC=e=>{let t=document.getElementById("task-projected"),a=document.getElementById("task-actual");e5((t.value?+t.value:0)-(a.value?+a.value:0))};(0,S.P)(e,e=>{tH(e),e?.startDate&&eB(m()(e.startDate)),e?.expires&&e?.maintenanceScheduleID==0&&eD(m()(e.expires)),e?.dateCompleted?eT(m()(e.dateCompleted)):e?.completed&&eT(m()(e.completed)),e?.maintenanceScheduleID>0&&(eP(!0),t4(e?.maintenanceSchedule?.occursEvery),eY(e?.maintenanceSchedule),aQ(e),e?.basicComponentID>0&&sn({variables:{id:+e?.basicComponentID}})),en(e),an({value:e?.taskType??"expiry",label:e?.taskType=="Expiry"?"Due by date":e?.taskType=="EngineHours"?"Due by engine hours":e?.taskType=="Uses"?"Due by number of uses":e?.taskType=="Frequency"?"Recurring task":"Due by date"}),tf(e?.inventory?.id>0?{label:e.inventory.item,value:e.inventory.id}:{label:null,value:"0"}),e?.documents.nodes?.length>0&&aD(e?.documents.nodes?.map(e=>e.id).join(",")),e?.attachmentLinks?.nodes&&eX(e?.attachmentLinks?.nodes.map(e=>({label:e.link,value:e.id}))),ek({...ej,status:e?.status.replaceAll("_"," ")}),e5(parseInt(e?.projected??"0")-parseInt(e?.actual??"0")),e?.documents?.nodes?.length>0&&ey(e?.documents?.nodes),e?.comments?ty(e?.comments):ty(""),e?.R2File?.nodes?.length>0&&at(e?.R2File?.nodes)});let aD=async e=>{await aw({variables:{id:e}})},[aw]=(0,n.t)(i.ZX,{fetchPolicy:"cache-and-network",onCompleted:e=>{let{isSuccess:t,data:a}=e.getFile;t&&(ep.push(a),ey(ep))},onError:e=>{console.error("queryFilesEntry error",e)}}),[aN]=(0,l.D)(o.zfn,{onCompleted:e=>{let t=e.createSeaLogsFileLinks;t.id>0&&(ei([...el,t]),eG?eX([...eG,{label:t.link,value:t.id}]):eX([{label:t.link,value:t.id}]))},onError:e=>{console.error("createSeaLogsFileLinksEntry error",e)}}),aS=[{value:"None",label:"None",color:"#1f2937"},{value:"Low",label:"Low",color:"#15803d"},{value:"Medium",label:"Medium",color:"#f97316"},{value:"High",label:"High",color:"#e11d48"}],aE=[{label:"Engine Hours",value:"Hours"},{label:"Days",value:"Days"},{label:"Weeks",value:"Weeks"},{label:"Months",value:"Months"},{label:"Number of sailings",value:"Uses"}],aI=[{value:"Open",label:"Open"},{value:"Save As Draft",label:"Save as Draft"},{value:"In Progress",label:"In Progress"},{value:"On Hold",label:"On Hold"},{value:"Completed",label:"Completed"}],aT=Number(eZ.filter(e=>"Completed"==e.status).length/eZ.length*100),aO=()=>{aB({})},aB=async t=>{if(!tW){K({title:"Error",description:"You do not have permission to edit this task",variant:"destructive"});return}if(eU&&!t0){K({title:"Error",description:"You do not have permission to edit this recurring task",variant:"destructive"});return}if(eU&&"object"==typeof t){let e=document.getElementById("task-title"),t=e?e.value:"",s=t2>0?t2:ej.occursEvery;if(isNaN(+s)||0>=+s){tP("Recurring schedule"),K({title:"Error",description:'You need to set the schedule of this recurring task. Please go to the "Recurring schedule" tab and set the frequency.',variant:"destructive"});return}let r=ej.occursEveryType,n="Hours"===ej.occursEveryType?"DutyHours":"Uses"===ej.occursEveryType?"EquipmentUsages":"Frequency",l=document.getElementById("recurring-task-description"),i=l?l.value:"",o=document.getElementById("high-warn-within"),c=o?o.value:"",d=document.getElementById("medium-warn-within"),u=d?d.value:"",m=document.getElementById("low-warn-within");var a={title:t,description:i,type:n,occursEveryType:r,highWarnWithin:+c,mediumWarnWithin:+u,lowWarnWithin:+(m?m.value:""),warnWithinType:r,maintenanceChecks:er.id,basicComponents:ej.basicComponentID?ej.basicComponentID:er.basicComponentID,inventoryID:ej.InventoryID>=0?ej.InventoryID:er.inventoryID};e$?.id>0&&(a.id=e$.id),s&&(a.occursEvery=+s),e$?.id>0?await aL({variables:{input:a}}):await aF({variables:{input:a}})}else{let t=document.getElementById("task-name"),a=!!t&&t.value;if(!a){K({title:"Error",description:"You need to set the name of this task",variant:"destructive"});return}let s=document.getElementById("task-workorder"),r=s?s.value:"",n=document.getElementById("task-projected"),l=n?n.value:"",i=document.getElementById("task-actual"),o=i?i.value:"",c=document.getElementById("task-difference"),d=c?c.value:"";await a_({variables:{input:{id:er.id,workOrderNumber:r,projected:l?+l:+er.projected,actual:o?+o:+er.actual,difference:d?+d:+er.difference,name:a,startDate:eO?m()(eO).format("YYYY-MM-DD"):er.startDate,completed:eC?"Invalid Date"===eC?er.completed:eC:er.completed,completedByID:ej.completedBy,dateCompleted:eI||er.dateCompleted,expires:"Invalid Date"===eC?null:eC,comments:tb,severity:ej.severity,maintenanceCategoryID:ej.category,status:ej.status?ej.status:er.status.replaceAll("_"," "),documents:ep.length>0?ep?.map(e=>+e.id).join(","):er.documents?.nodes.map(e=>+e.id).join(","),attachmentLinks:eG?eG.map(e=>e.value).join(","):er.attachmentLinks?.nodes.map(e=>e.id).join(","),assignees:ej.assignees?ej.assignees:er.assignees?.nodes.map(e=>e.id).join(","),assignedToID:ej.assignees?+ej.assignees:er?.assignedToID,basicComponentID:ej.basicComponentID?ej.basicComponentID:er.basicComponentID,inventoryID:ej.inventoryID>=0?ej.inventoryID:er.inventoryID,maintenanceCheck_SignatureID:er.maintenanceCheck_SignatureID,recurringID:0==+er.recurringID&&e$?e:er.recurringID,startHours:tZ?.startHours>=0?+tZ.startHours:+er.startHours,hoursCompleted:tZ.hoursCompleted>=0?+tZ.hoursCompleted:+er.hoursCompleted,taskType:ar.value}}})}},[aF]=(0,l.D)(o.zrG,{onCompleted:e=>{e.createComponentMaintenanceSchedule.id>0&&aB(!0)},onError:e=>{aB(!0),console.error("createMaintenanceScheduleEntry error",e)}}),[aM]=(0,l.D)(o.zrG,{onCompleted:t=>{let a=t.createComponentMaintenanceSchedule;aA({variables:{input:{id:e,maintenanceScheduleID:a.id}}}),eY(a)},onError:e=>{console.error("createMaintenanceScheduleEntry error",e)}}),[aA]=(0,l.D)(o.VgQ,{onCompleted:e=>{e.updateComponentMaintenanceCheck},onError:e=>{console.error("updateMaintenanceChecksEntry error",e)}}),[aL]=(0,l.D)(o.wm0,{onCompleted:e=>{e.updateComponentMaintenanceSchedule.id>0&&aB(!0)},onError:e=>{aB(!0),console.error("updateMaintenanceScheduleEntry error",e)}}),[a_]=(0,l.D)(o.VgQ,{onCompleted:s=>{let r=s.updateComponentMaintenanceCheck;eU&&"Completed"!=er.status&&"Completed"==ej.status?z.push("/maintenance/complete-recurring-task?taskID="+e):r.id>0&&(q.get("taskCreated")||q.get("taskCompleted")?"inventory"===t?z.push(`/inventory/view?id=${ej.inventoryID?ej.inventoryID:er.inventoryID}&inventoryTab=maintenance`):q.get("redirect_to")?z.push(q?.get("redirect_to")+""):z.push("/maintenance"):a?u&&u():q.get("redirect_to")?z.push(q?.get("redirect_to")+""):z.push("/maintenance"))},onError:e=>{console.error("updateMaintenanceChecksEntry error",e)}}),aR=()=>{a?u&&u():q.get("taskCreated")||q.get("taskCompleted")?q.get("redirect_to")?z.push(q?.get("redirect_to")+""):z.push("/maintenance"):z.back()},aU=e=>{eD(e)},aP=async()=>{if(!tQ){u&&u(),K({title:"Error",description:"You do not have permission to delete this task",variant:"destructive"});return}if(eU&&!t0){K({title:"Error",description:"You do not have permission to delete this task",variant:"destructive"});return}await a$({variables:{id:[+e]}})},[a$]=(0,l.D)(o.Bow,{onCompleted:e=>{a?u&&u():z.back()},onError:e=>{console.error("deleteMaintenanceCheckEntry error",e)}}),[aY]=(0,l.D)(o.xvb,{onCompleted:t=>{let a=t.createMaintenanceScheduleSubTask;a&&aV({variables:{input:{maintenanceScheduleSubTaskID:a.id,componentMaintenanceCheckID:e,status:"In Review"}}})},onError:e=>{console.error("createSubtaskEntry error",e)}}),[aZ]=(0,n.t)(i.AJ,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readMaintenanceCheckSubTasks.nodes;t&&eH(t)},onError:e=>{console.error("queryMaintenanceCheckSubTask error",e)}}),aH=async()=>{await aZ({variables:{id:+e}})},[aV]=(0,l.D)(o.Bsb,{onCompleted:e=>{+e.createMaintenanceCheckSubTask.id>0&&aH()},onError:e=>{console.error("createSubtaskCheckEntry error",e)}}),[az]=(0,l.D)(o.q$t,{onCompleted:e=>{let t=e.createR2File;at(ae.map(e=>e.title===t.title?{...e,id:t.id}:e))},onError:e=>{console.error("Error creating fuel receipts",e)}}),[aq]=(0,l.D)(o.q$t,{onCompleted:e=>{let t=e.createR2File;as(ae.map(e=>e.title===t.title?{...e,id:t.id}:e))},onError:e=>{console.error("Error creating fuel receipts",e)}}),aW=t=>{if(!t0){K({title:"Error",description:"You do not have permission to change recurring tasks",variant:"destructive"});return}eP(t.target.checked),e$?.id||aM({variables:{input:{}}}),!1===t.target.checked&&e$?.id&&aA({variables:{input:{id:e,maintenanceScheduleID:0}}})},aK=(e=!1)=>{let t=tZ.maintenanceSchedule.occursEveryType;if("Hours"===t||"Uses"===t);else{let a=m()(eO&&new Date(m()(eO).toISOString()).getTime()>0?new Date(e||eO):new Date).startOf("day").add(t2,t?.slice(0,-1).toLowerCase());"Invalid Date"!=a.format("DD/MM/YYYY")&&eD(a)}},aQ=e=>{ek({...ej,occursEveryType:e?.maintenanceSchedule?.occursEveryType});let t=e?.maintenanceSchedule;if(e?.maintenanceSchedule?.id>0){let a=t.occursEveryType?t.occursEveryType:"Days";if("Hours"===a||"Uses"===a){if("Uses"===a)return e.equipmentUsagesAtCheck+" Equipment Uses"}else{let s=t.occursEvery?t.occursEvery:1,r=m()(e?.startDate?new Date(e.startDate):new Date).startOf("day").add(s,a);return eD(r),r.format("DD/MM/YYYY")}}},aG=e=>{if(e?.target?.checked&&(ez(e.target.id),eW(eZ.filter(t=>t.id==e.target.id)[0].maintenanceScheduleSubTask.id),tD(eZ.filter(t=>t.id==e.target.id)[0].maintenanceScheduleSubTask.description),tN(eZ.filter(t=>t.id==e.target.id)[0].maintenanceScheduleSubTask.inventoryID),eZ.find(t=>t.id==e.target.id)?.R2File?.nodes?.length>0?as(eZ.find(t=>t.id==e.target.id).R2File.nodes):as([]),e0(!0),e2(!1)),e?.target?.checked===!1&&(e2(!0),eW(eZ.filter(t=>t.id==e.target.id)[0].maintenanceScheduleSubTask.id),ez(e.target.id),tD(eZ.filter(t=>t.id==e.target.id)[0].maintenanceScheduleSubTask.description),tN(eZ.find(t=>t.id==e.target.id).maintenanceScheduleSubTask.inventoryID),eZ.find(t=>t.id==e.target.id)?.R2File?.nodes?.length>0?as(eZ.find(t=>t.id==e.target.id).R2File.nodes):as([]),e0(!0)),"updateFindings"===e){let e=document.getElementById("subtask-findings").value;eQ(!1),eE(0),eN(""),aJ({variables:{input:{id:eV,findings:e,completedByID:eS,dateCompleted:ew}}})}if("updateSubTask"===e){let e=document.getElementById("subtask-name").value;e0(!1);let t=eZ.find(e=>e.id===eV)?.dateCompleted?m()(eZ.find(e=>e.id===eV).dateCompleted):"";e1?(eQ(!0),eE(eZ.find(e=>e.id===eV)?.completedBy?.id),eN(t)):eQ(!1),aX({variables:{input:{id:eq,task:e,description:tC,inventoryID:tw}}}),aJ({variables:{input:{id:eV,status:e1?"Completed":"In Review"}}})}if("deleteSubTask"===e){let e=eZ.filter(e=>e.id==eV)[0].findings;aJ({variables:{input:{id:eV,componentMaintenanceCheckID:"0",findings:e+"deleted from task "+eV}}}),e0(!1)}},[aX]=(0,l.D)(o.Rji,{onCompleted:e=>{e.updateMaintenanceScheduleSubTask.id>0&&aH()},onError:e=>{console.error("updateSubtaskEntry error",e)}}),[aJ]=(0,l.D)(o.Fii,{onCompleted:e=>{e.updateMaintenanceCheckSubTask.id>0&&aH()},onError:e=>{console.error("updateSubtaskCheckEntry error",e)}}),a0=async e=>{ey(ep.filter(t=>t.id!==e))},[a1]=(0,n.t)(i.rd,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readSeaLogsMembers.nodes;t&&th(t)},onError:e=>{console.error("queryCrewMemberInfo error",e)}}),a2=async e=>{await a1({variables:{crewMemberIDs:e.length>0?e:[0]}})},[a4]=(0,n.t)(i.DB,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.readComponentMaintenanceChecks;if(t.nodes.length>0){a2(Array.from(new Set(t.nodes.filter(e=>e.assignedToID>0).map(e=>e.assignedToID))));var a=t.nodes.map(e=>({id:e.id,name:e.name,basicComponentID:e.basicComponentID,comments:e.comments,description:e.description,assignedToID:e.assignedToID,expires:e.expires,status:e.status,startDate:e.startDate,isOverDue:(0,S.AT)(e),isCompleted:"Completed"===e.status?"1":"2"}));a.sort((e,t)=>"1"===e.isCompleted&&"1"===t.isCompleted?"NA"===e.expires&&"NA"!==t.expires?1:"NA"!==e.expires&&"NA"===t.expires?-1:new Date(t.expires).getTime()-new Date(e.expires).getTime():"1"===e.isCompleted?1:"1"===t.isCompleted?-1:m()(e.expires).diff(t.expires)),tm(a)}},onError:e=>{console.error("readComponentMaintenanceChecks error",e)}}),a3=e=>{J(Q.find(t=>t.id===e?.value)||null),tf({label:null,value:"0"}),ek({...ej,basicComponentID:e?.value,inventoryID:0}),es(ea.filter(t=>+t.vessel.id==+e?.value)),sn({variables:{id:+e?.value}}),ag({variables:{id:+e?.value}})},a6=e=>{tN(e?.value||0)},a5=e=>{eX(eG.filter(t=>t!==e))},a7=e=>(0,s.jsxs)("div",{className:"flex justify-between align-middle mr-2 w-fit",children:[s.jsx(v.default,{href:e.label,target:"_blank",className:"ml-2 ",children:e.label}),s.jsx("div",{className:"ml-2 ",children:s.jsx(y.Z,{className:"w-5 h-5 alert cursor-pointer",onClick:()=>a5(e)})})]}),[a8]=(0,n.t)(i.gh,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readMaintenanceCategories.nodes;t&&tL(t.map(e=>({label:e.name,value:e.id})))},onError:e=>{console.error("Error getting maintenance categories",e)}}),a9=e=>{setTimeout(()=>{tk(e.description),to(e),ta(new Date(e.time)),te(!0)},0)},se=()=>{setTimeout(()=>{tk(""),to(!1),ta(!1),te(!0)},0)},[st]=(0,l.D)(o.WXb,{refetchQueries:()=>[{query:i.nH,variables:{id:e}}],awaitRefetchQueries:!0,onCompleted:t=>{te(!1),tk(""),to(!1),ta(!1),e>0&&ss({variables:{id:e},fetchPolicy:"network-only"})},onError:t=>{console.error("❌ Error creating Task Record",t),e>0&&ss({variables:{id:e},fetchPolicy:"network-only"})}}),[sa]=(0,l.D)(o.Sku,{refetchQueries:()=>[{query:i.nH,variables:{id:e}}],awaitRefetchQueries:!0,onCompleted:t=>{te(!1),tk(""),to(!1),ta(!1),e>0&&ss({variables:{id:e},fetchPolicy:"network-only"})},onError:t=>{console.error("❌ Error updating task record",t),e>0&&ss({variables:{id:e},fetchPolicy:"network-only"})}}),[ss]=(0,n.t)(i.nH,{fetchPolicy:"cache-and-network",notifyOnNetworkStatusChange:!0,errorPolicy:"all",onCompleted:e=>{let t=e.readMissionTimelines.nodes;t?tE([...t].sort((e,t)=>new Date(t.time).getTime()-new Date(e.time).getTime())):tE([])},onError:e=>{console.error("❌ Error getting task records",e),tE([])}}),sr=e=>{tT([]),"Days"===e||"Weeks"===e||"Months"===e?ek({...ej,occursEveryType:e}):er?.basicComponentID>0||ej?.basicComponentID>0?(ek({...ej,occursEveryType:e}),sn({variables:{id:ej?.basicComponentID>0?ej.basicComponentID:er.basicComponentID}})):(ek({...ej,occursEveryType:"Days"}),K({title:"Error",description:"Please add a vessel to set this option",variant:"destructive"}))},[sn]=(0,n.t)(i.IO,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readBasicComponents.nodes.map(e=>e.id);t&&t.length>0&&sl({variables:{id:t}})},onError:e=>{console.error("getEnginesByVessel error",e)}}),[sl]=(0,n.t)(i.l6,{fetchPolicy:"no-cache",onCompleted:e=>{tT(e.readEngines.nodes)},onError:e=>{console.error("getEngines error",e)}}),si=()=>{t6(!0)},so=()=>{t6(!1)},sc=(e,t)=>{si(),tB({...tO,[t]:{value:e.target.checked}}),e$?.engineUsage?.nodes?.find(e=>e.engine.id===t)?su({variables:{input:{id:e$?.engineUsage?.nodes?.find(e=>e.engine.id===t).id,isScheduled:e.target.checked}}}):sd({variables:{input:{engineID:+t,maintenanceScheduleID:+e$.id,isScheduled:e.target.checked}}})},[sd]=(0,l.D)(o.lde,{onCompleted:e=>{e.createEngine_Usage.id>0&&(sm(),so())},onError:e=>{console.error("createEngineUsage error",e)}}),[su]=(0,l.D)(o.BYr,{onCompleted:e=>{e.updateEngine_Usage.id>0&&(sm(),so())},onError:e=>{console.error("updateEngineUsage error",e)}}),sm=async()=>{await sp({variables:{id:+e$.id}})},[sp]=(0,n.t)(i.pf,{onCompleted:e=>{let t=e.readOneMaintenanceSchedule;t&&eY(t)},onError:e=>{console.error("queryRecurringTask error",e)}}),sh=(e,t)=>{e$?.engineUsage?.nodes?.find(e=>e.engine.id===t)?su({variables:{input:{id:e$?.engineUsage?.nodes?.find(e=>e.engine.id===t).id,lastScheduleHours:+e.target.value}}}):sd({variables:{input:{engineID:t,maintenanceScheduleID:e$.id,lastScheduleHours:+e.target.value}}})},[sv]=(0,l.D)(o.xak,{onCompleted:e=>{let t=e.createMaintenanceCategory;t.id>0&&(a8({variables:{clientID:+(localStorage.getItem("clientId")??0)}}),tR(!1),ek({...ej,category:t.id}))},onError:e=>{console.error("createCategoryEntry error",e)}}),sg=e=>e.filter((e,t,a)=>t===a.findIndex(t=>t?.item?.toLowerCase()===e?.item?.toLowerCase())),sx=e=>{t7({...t5,status:e?"OutOfService":"AvailableForVoyage"}),e?sf({variables:{input:{vesselID:ej?.basicComponentID,date:m()().format("YYYY-MM-DD"),status:"OutOfService",comment:t5?.comment,reason:"Other",otherReason:"From Maintenance Task "+er?.name+" on "+m()().format("YYYY-MM-DD"),expectedReturn:eC}}}):sf({variables:{input:{vesselID:ej?.basicComponentID,date:m()().format("YYYY-MM-DD"),status:"AvailableForVoyage"}}})},[sf]=(0,l.D)(o.qb9,{onCompleted:e=>{let t=e.createVesselStatus;t7({...t5,vesselID:t?.vesselID,date:t?.date,status:t?.status,comment:t?.comment,reason:t?.reason,otherReason:t?.otherReason,expectedReturn:t?.expectedReturn}),t9(!1)},onError:e=>{K({title:"Error",description:e.message,variant:"destructive"})}}),sb=e=>{tH({...tZ,startHours:e.target.value})},[sy]=(0,n.t)(i.HC,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readCaptureImages.nodes;t&&ao(t)},onError:e=>{console.error("getFieldImages error",e)}});return(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx(P.Bu,{title:er?.name?`Task: ${er.name}`:`Task #${e}`}),!I()(tu)&&(0,s.jsxs)("div",{className:"flex items-center mx-2.5",children:["task"!==tv&&s.jsx(x.Button,{title:"Completed Tasks",className:"hover:",iconLeft:"check",onClick:()=>{tg("completed")}}),"completed"===tv&&s.jsx(x.Button,{title:"Task Details",className:"hover:",iconLeft:"back_arrow",onClick:()=>{tg("task")}})]}),"task"===tv&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(d.Zb,{className:"mx-2.5",children:[(0,s.jsxs)(d.Ol,{children:[s.jsx(P.H4,{children:"Basic Information"}),s.jsx(P.P,{children:"Set the task title, assign it to a vessel or inventory item, and configure the task type and schedule."})]}),(0,s.jsxs)(d.aY,{className:"space-y-6",children:[s.jsx(P.__,{className:"w-full",htmlFor:"task-name",label:"Title",children:s.jsx(A.I,{id:"task-name",defaultValue:er?.name,type:"text",placeholder:"Task name"})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5",children:[a?s.jsx(R.Combobox,{label:"Vessel",id:"vessel-combobox",options:Q&&Q.length>0?Q.map(e=>({value:e.id,label:e.title,vessel:am(e.id,e)})):[],buttonClassName:"w-full",labelClassName:"w-full",isLoading:!Q||0===Q.length,defaultValues:X?{value:X.id,label:X.title}:void 0,onChange:a3,placeholder:"Select vessel"}):s.jsx(R.Combobox,{id:"vessel-combobox",label:"Vessel",isLoading:!Q||0===Q.length,options:Q&&Q.length>0?Q.map(e=>({value:e.id,label:e.title,vessel:am(e.id,e)})):[],buttonClassName:"w-full",labelClassName:"w-full",value:X?{value:X.id,label:X.title}:void 0,onChange:a3,placeholder:"Select vessel"}),s.jsx(R.Combobox,{label:"Inventory",isLoading:!ea||ea.length<0,id:"task-inventory",buttonClassName:"w-full",labelClassName:"w-full",options:ea&&ea.length>0?sg(ea).map(e=>({value:e.id,label:e.item})):[],value:tx,onChange:e=>{ek({...ej,inventoryID:e?.value}),tf(e)},placeholder:"Select inventory item"})]}),s.jsx(R.Combobox,{id:"task-category",options:[{label:" --- Add new category --- ",value:"NEW_CATEGORY"},...tA&&tA.length>0?tA:[]],label:"Group to",value:tA&&tA.length>0&&tA.filter(e=>e.value===ej?.category).length>0?tA.filter(e=>e.value===ej?.category)[0]:tA&&tA.length>0&&tA.filter(e=>e.value===er?.maintenanceCategory?.id)?.length>0?tA.filter(e=>e.value===er?.maintenanceCategory?.id)[0]:void 0,placeholder:"Select Category",onChange:e=>{e&&("NEW_CATEGORY"===e.value?tR(!0):ek({...ej,category:e.value}))}})]})]}),(0,s.jsxs)(d.Zb,{className:"mx-2.5",children:[(0,s.jsxs)(d.Ol,{children:[s.jsx(P.H4,{children:"Task Schedule"}),s.jsx(P.P,{children:"Configure when this task is due and how it should be scheduled."})]}),s.jsx(d.aY,{className:"space-y-6",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-5",children:[s.jsx(R.Combobox,{id:"task-type",options:[{label:"Due by date",value:"Expiry"},{label:"Due by engine hours",value:"EngineHours"},{label:"Recurring task",value:"Recurring"}],label:"Task type",value:ar,placeholder:"Select Task Type",onChange:e=>{an(e),e?.value==="Recurring"?aW({target:{checked:!0}}):(aW({target:{checked:!1}}),tH({...tZ,maintenanceSchedule:{...tZ.maintenanceSchedule,occursEveryType:"Days"}}),sr("Days"))}}),(0,s.jsxs)("div",{children:["Expiry"===ar.value&&s.jsx(U.Z,{mode:"single",label:"Due date",type:"date",disabled:ej?.occursEveryType==="Hours"||ej?.occursEveryType==="Uses"||er?.maintenanceSchedule?.occursEveryType==="Hours"||er?.maintenanceSchedule?.occursEveryType==="Uses",value:eC&&""!==eC?eC.toDate?eC.toDate():new Date(eC):void 0,onChange:e=>{er?.maintenanceSchedule?.occursEveryType==="Hours"?K({title:"Error",description:"This task has recurring based on engine hours and not allow the date edit",variant:"destructive"}):aU(e)},placeholder:"Select due date",clearable:!0}),"EngineHours"===ar.value&&s.jsx(P.__,{className:"w-full",htmlFor:"due-engine-hours",label:"Engine hours",children:s.jsx(A.I,{id:"due-engine-hours",defaultValue:tZ?.startHours?tZ.startHours:"",type:"number",placeholder:"Enter engine hours",onChange:e=>{sb(e)}})}),(ej?.occursEveryType==="Hours"||ej?.occursEveryType==="Uses")&&s.jsx(P.P,{className:"mt-9 text-destructive",children:"This task has a recurring period based on the number of hours/uses"})]})]})})]}),(0,s.jsxs)(d.Zb,{className:"mx-2.5",children:[(0,s.jsxs)(d.Ol,{children:[s.jsx(P.H4,{children:"Task Details"}),s.jsx(P.P,{children:"Manage task details, sub-tasks, attachments, and recurring schedules."})]}),(0,s.jsxs)(d.aY,{className:"space-y-6",children:[s.jsx("div",{className:"w-full",children:tV?s.jsx(Y.Tabs,{value:tU,onValueChange:tP,children:s.jsx(H.x,{className:"w-full bg-card/0 phablet:bg-card/0 pb-2",children:(0,s.jsxs)(Y.TabsList,{className:"inline-flex w-max px-1",children:[s.jsx(Y.TabsTrigger,{value:"Details",children:"Details"}),s.jsx(Y.TabsTrigger,{value:"Sub-tasks",children:"Sub-tasks"}),s.jsx(Y.TabsTrigger,{value:"Links-docs",children:"Links-docs-images"}),eU&&s.jsx(Y.TabsTrigger,{value:"Recurring schedule",children:"Recurring schedule"}),s.jsx(Y.TabsTrigger,{value:"Notes & updates",children:"Notes & updates"})]})})}):s.jsx("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[s.jsx(x.Button,{variant:"Details"===tU?"primary":"outline",onClick:()=>tP("Details"),className:"justify-start",children:"Details"}),s.jsx(x.Button,{variant:"Sub-tasks"===tU?"primary":"outline",onClick:()=>tP("Sub-tasks"),className:"justify-start",children:"Sub-tasks"}),s.jsx(x.Button,{variant:"Links-docs"===tU?"primary":"outline",onClick:()=>tP("Links-docs"),className:"justify-start",children:"Links-docs-images"}),eU&&s.jsx(x.Button,{variant:"Recurring schedule"===tU?"primary":"outline",onClick:()=>tP("Recurring schedule"),className:"justify-start",children:"Recurring schedule"}),s.jsx(x.Button,{variant:"Notes & updates"===tU?"primary":"outline",onClick:()=>tP("Notes & updates"),className:"justify-start",children:"Notes & updates"})]})})}),"Details"===tU&&s.jsx(s.Fragment,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(P.__,{htmlFor:"task-description",label:"Task Description",children:er&&er.id&&s.jsx(V.Z,{id:"task-description",placeholder:"Task description",className:"",content:tb,handleEditorChange:e=>{ty(e)}})}),s.jsx(ef.Z,{inputId:1,sectionId:+ap,buttonType:"button",sectionName:"componentMaintenanceCheckID"}),s.jsx(P.__,{label:"Reference",htmlFor:"task-workorder",children:s.jsx(A.I,{id:"task-workorder",defaultValue:er?.workOrderNumber,type:"text",placeholder:"Work order/Reference"})}),s.jsx(R.Combobox,{label:"Allocated to:",options:ee?ee.map(e=>({value:e.id,label:`${e.firstName??""} ${e.surname??""}`,crew:e,profile:{firstName:e.firstName,surname:e.surname,avatar:null}})):[],isLoading:!ee||0===ee.length,defaultValues:er?.assignedTo&&{label:`${er.assignedTo.firstName??""} ${er.assignedTo.surname??""}`,value:er.assignedTo.id},onChange:e=>ek({...ej,assignees:e.value}),placeholder:"Select team"}),s.jsx(P.P,{children:"An email will be sent to the allocated team with order reference (if any) and details of this task."}),s.jsx(R.Combobox,{id:"task-priority",label:"Priority",options:aS,isLoading:!ea,defaultValues:aS.filter(e=>e.value===er?.severity).map(e=>({value:e.value,label:e.label}))[0],placeholder:"Select priority",onChange:e=>ek({...ej,severity:e.value})})]})}),"Sub-tasks"===tU&&(0,s.jsxs)(s.Fragment,{children:[eZ.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[s.jsx(eo,{value:aT,className:"h-2"}),s.jsx("span",{className:"leading-none flex items-center",children:aT>0&&(0,s.jsxs)("span",{children:[aT,"%"]})})]}),s.jsx("div",{className:"space-y-3",children:eZ.map(e=>{let t=ea?.find(t=>t.id===e.maintenanceScheduleSubTask.inventoryID);return s.jsx("div",{children:s.jsx("div",{className:"flex items-start gap-3 flex-1",children:s.jsx(N.tz,{id:e.id,checked:"Completed"===e.status,variant:"success",onCheckedChange:t=>{aG({target:{id:e.id,checked:!0===t}})},className:"w-full",children:(0,s.jsxs)("div",{className:"w-full flex justify-between gap-2.5 items-center",children:[(0,s.jsxs)("div",{className:"flex gap-2.5",children:[s.jsx("div",{children:e.maintenanceScheduleSubTask.task}),(e?.completedBy?.firstName||e?.dateCompleted)&&(0,s.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-2",children:["Completed"===e.status&&e?.completedBy?.firstName&&s.jsx(eu.Avatar,{size:"xs",variant:"success",children:s.jsx(eu.AvatarFallback,{children:(0,eu.getCrewInitials)(e.completedBy.firstName,e.completedBy.surname)})}),(0,s.jsxs)("div",{children:[al["tablet-sm"]&&e?.completedBy?.firstName&&(0,s.jsxs)("span",{children:[e.completedBy.firstName," ",e.completedBy.surname]}),al["tablet-lg"]&&e?.dateCompleted&&(0,s.jsxs)("span",{children:[" - ",(0,B.p6)(e.dateCompleted)]})]})]})]}),t&&s.jsx(P.Ct,{variant:"outline",className:"h-fit py-1 px-2 text-sm",type:"normal",children:t?.item}),e.findings&&s.jsx($.TooltipProvider,{children:(0,s.jsxs)($.Tooltip,{children:[s.jsx($.TooltipTrigger,{className:"p-0 h-fit",asChild:!0,children:s.jsx(x.Button,{variant:"link",size:"md",onClick:t=>{t.stopPropagation(),eR(e),eL(!0)},children:(0,eg.p)(al.small,"findings","View findings")})}),s.jsx($.TooltipContent,{children:s.jsx("p",{children:"View detailed findings"})})]})})]})})})},`${e.id}-subtask`)})})]}),s.jsx("div",{className:"my-4 flex",children:s.jsx(x.Button,{variant:"primary",onClick:()=>{eM(!0),tD("")},children:"Add sub-task"})})]}),"Links-docs"===tU&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(P.__,{label:"Links",children:Q&&s.jsx(A.I,{id:"task-title",type:"text",className:"",placeholder:"Type a link and press Enter",onKeyDown:async e=>{if("Enter"===e.key){let t=e.target.value;await aN({variables:{input:{link:t}}}),e.target.value=""}}})}),s.jsx("div",{className:"my-4 flex",children:eG?eG.map(e=>s.jsx("div",{children:a7(e)},e.value)):el.map(e=>s.jsx("div",{children:a7(e)},e.value))}),s.jsx(P.Z0,{className:"my-4 w-full"}),(0,s.jsxs)("div",{children:[s.jsx(P.__,{label:"Documents and images",children:s.jsx(c.Z,{setDocuments:ey,text:"",subText:"Drag files here or upload",documents:ep})}),s.jsx("div",{className:"my-4",children:ep.length>0&&ep.map(e=>(0,s.jsxs)("div",{className:"flex items-center gap-8 justify-between p-2.5 rounded-lg border mb-4",children:[s.jsx(T.Z,{document:e}),s.jsx(x.Button,{size:"icon",variant:"destructive",onClick:()=>a0(e.id),children:s.jsx(y.Z,{})})]},e.id))})]})]}),eU&&s.jsx(s.Fragment,{children:t0?s.jsx(s.Fragment,{children:"Recurring schedule"===tU&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(P.__,{label:"Schedule details"}),s.jsx(P.__,{htmlFor:"task-frequency",label:"Occurs every",children:s.jsx(A.I,{id:"task-frequency",defaultValue:e$?e$.occursEvery:"1",type:"number",placeholder:"Schedule every",min:1,onChange:e=>{tH({...tZ,maintenanceSchedule:{...tZ.maintenanceSchedule,occursEvery:e.target.value??"1"}}),t4(e.target.value),aK()}})}),s.jsx(R.Combobox,{id:"task-recurring-type",label:"Occurrence type",options:aE,value:ej?.occursEveryType?aE.filter(e=>e.value===ej.occursEveryType).map(e=>({value:e.value,label:e.label}))[0]:aE.filter(e=>e.value===e$.occursEveryType).map(e=>({value:e.value,label:e.label}))[0],placeholder:"Select type",onChange:e=>{e&&(tH({...tZ,maintenanceSchedule:{...tZ.maintenanceSchedule,occursEveryType:e.value}}),sr(e.value))}}),(()=>{if(ej.occursEveryType){if("Days"===ej.occursEveryType||"Weeks"===ej.occursEveryType||"Months"===ej.occursEveryType)return!0}else if(e$?.occursEveryType==="Days"||e$?.occursEveryType==="Weeks"||e$?.occursEveryType==="Months")return!0;return!1})()&&s.jsx(U.Z,{mode:"single",type:"date",label:"Last completed date",value:eO?eO.toDate():(()=>{if(e$){let e=e$.occursEveryType?e$.occursEveryType:"Days";if("Hours"!==e&&"Uses"!==e)return er?.startDate?m()(er.startDate):m()()}return m()()})().toDate(),onChange:e=>{eB(m()(e)),aK(e)},placeholder:"Enter last schedule date",disabled:!t0}),(()=>{if(ej?.occursEveryType){if("Hours"===ej.occursEveryType)return!0}else if(e$?.occursEveryType==="Hours")return!0;return!1})()&&s.jsx(s.Fragment,{children:tI.length>0&&tI.map(e=>s.jsx("div",{className:"my-4",children:(0,s.jsxs)("div",{className:"flex w-full gap-4 items-center",children:[s.jsx("div",{className:" w-1/2 ",children:(0,s.jsxs)(P.__,{htmlFor:`check_engine-${e.id}`,"data-ripple":"true","data-ripple-color":"dark","data-ripple-dark":"true",label:`${e.title}${e.type?" - "+e.type:""}`,children:[s.jsx(A.I,{type:"checkbox",id:`check_engine-${e.id}`,onChange:t=>sc(t,e.id),checked:tO[e.id]?tO[e.id].value:e$?.engineUsage?.nodes?.find(t=>t.engine.id===e.id)?.isScheduled}),s.jsx("span",{className:"absolute transition-opacity opacity-0 pointer-events-none top-2/4 left-1/3 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100"}),s.jsx("span",{className:"ml-3",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("span",{className:"",children:["Engine Hours:"," "]}),e.currentHours]})})]})}),s.jsx("div",{className:"w-full",children:s.jsx(A.I,{id:`check_engine_hours-${e.id}`,name:"check_engine_hours",type:"number",min:0,placeholder:"Enter last schedule hours",onChange:t=>tM({...tF,[e.id]:t.target.value}),onBlur:t=>sh(t,e.id),value:tF[e.id]?tF[e.id]:e$?.engineUsage?.nodes?.find(t=>t.engine.id===e.id)?.lastScheduleHours,className:"","aria-describedby":"expiry-last-schedule-error",required:!0})})]})}))}),s.jsx(L.Textarea,{id:"recurring-task-description",rows:4,defaultValue:(0,ed.j)(e$?e$.description:""),placeholder:"Description and notes"}),s.jsx(P.__,{htmlFor:"task-reminders","data-ripple":"true","data-ripple-color":"dark","data-ripple-dark":"true",leftContent:s.jsx(w.Checkbox,{id:"task-reminders",onCheckedChange:e=>{e3(e)},checked:e4,isRadioStyle:!0,size:"lg"}),label:"CREATE YOUR OWN REMINDERS (Optional)"}),e4&&(0,s.jsxs)(s.Fragment,{children:[s.jsx(P.Z0,{className:"my-6"}),s.jsx(P.__,{label:"Low warning within (days)",htmlFor:"low-warn-within",children:s.jsx(A.I,{type:"number",id:"low-warn-within",placeholder:"Low warning within",disabled:!0,value:(ej.occursEveryType?ej.occursEveryType:e$.occursEveryType)==="Months"?"14":(ej.occursEveryType?ej.occursEveryType:e$.occursEveryType)?"7":"0"})}),s.jsx(P.__,{label:"Medium warning within (days)",htmlFor:"medium-warn-within",children:s.jsx(A.I,{type:"number",id:"medium-warn-within",placeholder:"Medium warning within",disabled:!0,value:(ej.occursEveryType?ej.occursEveryType:e$.occursEveryType)==="Months"?"7":(ej.occursEveryType?ej.occursEveryType:e$.occursEveryType)?"3":"0"})}),s.jsx(P.__,{label:"High warning within (days)",htmlFor:"high-warn-within",children:s.jsx(A.I,{type:"number",id:"high-warn-within",placeholder:"High warning within",disabled:!0,value:(ej.occursEveryType?ej.occursEveryType:e$.occursEveryType)==="Months"?"3":(ej.occursEveryType?ej.occursEveryType:e$.occursEveryType)?"1":"0"})})]})]})}):s.jsx(F.Z,{errorMessage:"Oops You do not have the permission to view this section."})}),"Task costs"===tU&&(0,s.jsxs)(s.Fragment,{children:[s.jsx("p",{className:"   max-w-[40rem] leading-loose",children:"Track the expected and actual costs of repairs and maintenance in SeaLogs reports module."}),s.jsx("div",{className:"my-4 flex flex-col w-full",children:s.jsx(P.__,{htmlFor:"task-projected",label:"Projected costs",children:s.jsx(A.I,{id:"task-projected",defaultValue:er?.projected,type:"number",placeholder:"Projected",onChange:aC})})}),s.jsx("div",{className:"my-4 flex flex-col w-full",children:s.jsx(P.__,{htmlFor:"task-actual",label:"Actual costs",children:s.jsx(A.I,{id:"task-actual",defaultValue:er?.actual,type:"number",placeholder:"Actual",onChange:aC})})}),s.jsx("div",{className:"my-4 flex flex-col w-full",children:s.jsx(P.__,{htmlFor:"task-difference",label:"Difference",children:s.jsx("div",{id:"task-difference",className:"w-full p-2 border-t border-dashed",children:e6})})})]}),"Notes & updates"===tU&&(0,s.jsxs)(s.Fragment,{children:[tS.length>0&&s.jsx("div",{className:"space-y-2.5",children:tS.map(e=>s.jsx(eb,{record:e,onEdit:a9,onDelete:e=>{tl(e),tr(!0)}},`${e.id}-record-${e.time}`))}),0===tS.length&&(0,s.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[s.jsx("p",{children:"No notes or updates yet."}),s.jsx("p",{className:"text-sm",children:'Click "Add note" to create the first one.'})]}),ac.length>0&&s.jsx("div",{className:"flex flex-wrap mb-4",children:ac.map((e,t)=>s.jsx("div",{className:"w-1/5 p-1 rounded-md relative",children:s.jsx("img",{src:e.imageData,alt:`Captured ${t}`,className:"object-cover rounded-md"},t)},t))}),(0,s.jsxs)("div",{className:"mt-2 flex justify-between items-center gap-4",children:[s.jsx(x.Button,{variant:"outline",iconLeft:j.Z,onClick:se,children:"Add note"}),s.jsx(ex.Z,{file:!!(ai&&Array.isArray(ai))&&ai.filter(e=>e.fieldName===ap).sort((e,t)=>t.id-e.id),setFile:ax,inputId:ap.toString(),buttonType:"button",sectionData:{id:+ap,sectionName:"componentMaintenanceCheckID"}})]})]}),s.jsx(P.Z0,{className:"my-6"}),(0,s.jsxs)("div",{className:"pb-6 flex flex-col sm:flex-row gap-4",children:[s.jsx(R.Combobox,{id:"task-status",label:"Task status",options:aI||[],isLoading:!ea||!aI||0===aI.length,defaultValues:aI&&er?.status?aI.filter(e=>e.value===er?.status.replaceAll("_"," ")).map(e=>({value:e.value,label:e.label}))[0]:void 0,value:aI&&ej?.status?aI.filter(e=>e.value===ej?.status.replaceAll("_"," ")).map(e=>({value:e.value,label:e.label}))[0]:void 0,placeholder:"Select status",onChange:e=>{if(e){if(!tX&&"Completed"===e.value){K({title:"Error",description:"You do not have the permission to complete this task.",variant:"destructive"});return}ek({...ej,status:e.value.replaceAll("_"," ")})}},labelClassName:"w-full"}),ej?.status==="Completed"&&s.jsx(U.Z,{mode:"single",type:"date",label:"Completion date",value:eI?eI instanceof Date?eI:"function"==typeof eI.toDate?eI.toDate():new Date(eI.toString()):void 0,className:"w-full",wrapperClassName:"w-full",onChange:e=>{eT(e)},placeholder:"Enter completion date",disabled:!tX,clearable:!0}),ej?.status==="Completed"&&"EngineHours"===ar.value&&s.jsx(P.__,{className:" w-full",htmlFor:"due-engine-hours",label:"Engine hours",children:s.jsx(A.I,{id:"due-engine-hours",type:"number",placeholder:"Enter engine hours",value:tZ?.hoursCompleted||"",onChange:e=>{tH({...tZ,hoursCompleted:e.target.value}),ek({...ej,hoursCompleted:e.target.value})}})}),ej?.status==="Completed"&&s.jsx(R.Combobox,{id:"task-completed-by",label:"Completed by",labelClassName:"w-full",options:ee?ee?.map(e=>({value:e.id,label:`${e.firstName??""} ${e.surname??""}`,crew:e,profile:{firstName:e.firstName,surname:e.surname,avatar:null}})):[],isLoading:!ee||!er,defaultValues:er?.completedBy?{label:`${er.completedBy.firstName??""} ${er.completedBy.surname??""}`,value:er.completedBy.id}:null,onChange:e=>{e&&(ej.completedBy=e.value)},placeholder:"Select team"})]}),t5&&s.jsx(N.tz,{id:"vesselStatus-onChangeComplete",checked:t5?.status==="OutOfService",onCheckedChange:e=>{sx(!0===e)},disabled:t5?.status==="OnVoyage",className:`w-full ${t5?.status==="OnVoyage"?"opacity-60":""}`,children:(0,s.jsxs)("div",{className:"flex flex-col gap-1",children:[s.jsx("span",{className:"font-medium",children:`Mark vessel out of service${t5?.status?" - "+t5.status:""}`}),s.jsx("div",{className:`text-sm ${t5?.status==="OnVoyage"?"text-cinnabar-600":"text-muted-foreground"}`,children:t5?.status==="OnVoyage"?"This vessel is on a voyage and can't be set to out of service":"Until this task is complete the related vessel cannot be used normally"})]})})]})]})]}),"completed"===tv&&(0,s.jsxs)(d.Zb,{className:"mx-2.5",children:[(0,s.jsxs)(d.Ol,{children:[s.jsx(P.H4,{children:"Completed Recurring Tasks"}),s.jsx(P.P,{children:"View the history of completed recurring tasks."})]}),s.jsx(d.aY,{children:tu.length>0?s.jsx(ec.wQ,{columns:(0,ec.wu)([{accessorKey:"title",header:"Task",cellAlignment:"left",cell:({row:e})=>{let t=e.original;return(0,s.jsxs)("div",{className:"px-2 py-1",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-foreground flex items-center",children:[s.jsx(v.default,{href:`/maintenance?taskID=${t.id}&redirect_to=${W}?${q.toString()}`,className:"focus:outline-none hover:underline",children:t.name}),t?.isOverDue?.status==="High"&&s.jsx("div",{className:"inline-block rounded px-3 py-1 ml-3 text-destructive bg-slred-100 border border-destructive",children:t?.isOverDue?.days}),(t?.isOverDue?.status==="Medium"||t?.isOverDue?.days==="Save As Draft")&&s.jsx("div",{className:"inline-block rounded px-3 py-1 ml-3 text-yellow-600 bg-yellow-100 border border-yellow-600",children:t?.isOverDue?.days}),(t?.isOverDue?.status==="Low"||t?.isOverDue?.status==="Upcoming"||t?.isOverDue?.status==="Completed")&&t?.isOverDue?.days!=="Save As Draft"&&s.jsx("div",{className:"inline-block ml-3",children:t?.isOverDue?.days||t?.isOverDue?.status})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[null!==t.basicComponentID&&Q?.filter(e=>e?.id==t.basicComponentID).map(e=>s.jsx("div",{className:"inline-block rounded px-3 py-1 ml-3",children:s.jsx("span",{children:e.title})},e.id)),null!==t.comments&&s.jsx("div",{className:"w-14 flex items-center pl-1",children:(0,s.jsxs)(P.J2,{children:[s.jsx(P.CM,{asChild:!0,children:s.jsx(x.Button,{variant:"ghost",size:"icon",children:s.jsx(k.Z,{className:"text-slgreen-1000"})})}),s.jsx(P.yk,{children:t.comments})]})})]})]}),t.description&&s.jsx("div",{className:"mt-1 text-sm text-muted-foreground",children:t.description})]})}},{accessorKey:"assignedTo",header:"Assigned To",cellAlignment:"center",cell:({row:e})=>{let t=e.original,a=tp?.find(e=>e.id===t.assignedToID);return a?(0,s.jsxs)("div",{className:"flex items-center gap-2.5 justify-center",children:[s.jsx(eu.Avatar,{className:"h-8 w-8",children:s.jsx(eu.AvatarFallback,{className:"text-xs",children:(0,eu.getCrewInitials)(a.firstName,a.surname)})}),(0,s.jsxs)(v.default,{href:`/crew/info?id=${a.id}`,className:"hover:underline",children:[a.firstName," ",a.surname]})]}):s.jsx("span",{children:"-"})}},{accessorKey:"status",header:"Due Date",cellAlignment:"right",cell:({row:e})=>{let t=e.original,a=t?.isOverDue?.status==="High";return(0,s.jsxs)("div",{className:"text-right px-2 py-1",children:[s.jsx("div",{className:"text-sm text-muted-foreground mb-1",children:(0,B.p6)(t.expires)}),s.jsx("div",{className:a&&"Completed"!==t.status?"inline-block rounded px-3 py-1 bg-destructive-foreground border border-destructive text-cinnabar-700":"inline-block",children:s.jsx("span",{children:t.status.replaceAll("_"," ")})})]})}}]),data:tu,showToolbar:!1,pageSize:10}):s.jsx("div",{className:"flex justify-between items-center gap-2 p-4",children:s.jsx("p",{className:"text-muted-foreground",children:"No completed recurring tasks found."})})})]}),a?(0,s.jsxs)("div",{className:"flex justify-end gap-3 mt-4 mx-2.5",children:[tV&&s.jsx(x.Button,{variant:"back",iconLeft:s.jsx(C.Z,{}),onClick:aR,children:"Cancel"}),s.jsx(x.Button,{variant:"destructive",onClick:()=>{if(!tQ||eU&&!t0){K({title:"Error",description:"You do not have permission to delete this task",variant:"destructive"});return}e8(!0)},children:au("Delete","Archive task")}),s.jsx(x.Button,{variant:"primary",iconLeft:s.jsx(D.Z,{}),disabled:t3,onClick:aO,children:au("Update","Update task")})]}):(0,s.jsxs)(_.ActionFooter,{showFooter:!0,showCreateTask:!1,saveText:au("Update","Update task"),saveIcon:"check",saveDisabled:t3,onCancel:tV?void 0:aR,onSave:aO,noBorder:!0,className:"gap-3",children:[tV&&s.jsx(x.Button,{variant:"back",iconLeft:s.jsx(C.Z,{}),onClick:aR,children:"Cancel"}),s.jsx(x.Button,{variant:"destructive",onClick:()=>{if(!tQ||eU&&!t0){K({title:"Error",description:"You do not have permission to delete this task",variant:"destructive"});return}e8(!0)},children:au("Delete","Archive task")}),s.jsx(x.Button,{variant:"primary",disabled:t3,onClick:aO,children:au("Update","Update task")})]}),s.jsx(g.AlertDialogNew,{openDialog:eF,setOpenDialog:eM,handleCreate:()=>{let e=document.getElementById("subtask-name").value;eM(!1),aY({variables:{input:{task:e,description:tC,inventoryID:tw}}})},size:"xl",actionText:"Create sub-task",title:"Create new sub-task",children:(0,s.jsxs)(P.iP,{className:"space-y-4",children:[s.jsx(A.I,{id:"subtask-name",type:"text",className:"",placeholder:"Sub-task"}),s.jsx(R.Combobox,{id:"subtask-inventory",label:"Inventory item (optional)",options:ea&&ea.length>0?sg(ea)?.filter(e=>e.vesselID==er?.basicComponent?.id||e.vesselID==ej?.basicComponentID)?.map(e=>({value:e.id,label:e.item})):[],isLoading:!ea||0===ea.length,onChange:a6,placeholder:"Select inventory item"}),s.jsx(V.Z,{id:"comment",placeholder:"Comment",className:"",content:tC,handleEditorChange:ab})]})}),(0,s.jsxs)(g.AlertDialogNew,{openDialog:eJ,setOpenDialog:e0,title:"Sub-task details",handleCancel:()=>e0(!1),handleCreate:()=>aG("updateSubTask"),handleDestructiveAction:()=>aG("deleteSubTask"),showDestructiveAction:!0,actionText:"Update",destructiveActionText:"Delete",cancelText:"Cancel",size:"lg",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx(P.__,{label:"Sub-task name",htmlFor:"subtask-name",children:s.jsx(A.I,{id:"subtask-name",type:"text",defaultValue:eZ.filter(e=>e.id===eV)[0]?.maintenanceScheduleSubTask.task,onChange:e=>eH(eZ.map(t=>t.id===eV?{...t,maintenanceScheduleSubTask:{...t.maintenanceScheduleSubTask,task:e.target.value}}:t)),placeholder:`Sub-task ${eV}`})}),s.jsx(R.Combobox,{id:"subtask-inventory",label:"Inventory item (optional)",options:ea&&ea.length>0?sg(ea)?.filter(e=>e.vesselID==er?.basicComponent?.id||e.vesselID==ej?.basicComponentID)?.map(e=>({value:e.id,label:e.item})):[],isLoading:!ea||0===ea.length,value:ea?.filter(e=>e.id==tw)?.length>0?{value:ea.find(e=>e.id==tw).id,label:ea.find(e=>e.id==tw).item}:void 0,onChange:a6,placeholder:"Select inventory item"}),tS&&tS.length>0&&tS.filter(e=>e.subTaskID==eV).length>0&&s.jsx(H.x,{className:"mb-4 rounded-md bg-card border border-border border-dashed",children:s.jsx("div",{className:"space-y-2.5 p-2 max-h-[300px]",children:tS.filter(e=>e.subTaskID==eV).map(e=>s.jsx(eb,{record:e,onEdit:a9,onDelete:e=>{tl(e),tr(!0)}},`${e.id}-record`))})}),s.jsx(x.Button,{variant:"outline",onClick:se,children:"Add Record"}),s.jsx(M.Z,{files:aa,setFiles:as}),s.jsx(P.__,{label:"Comment",htmlFor:"comment",children:s.jsx(L.Textarea,{id:"comment",placeholder:"Comment",rows:8,value:(0,ed.j)(tC),onChange:e=>ab(e.target.value)})})]}),s.jsx(P.Z0,{className:"my-6"}),s.jsx(N.tz,{id:"task-alertChange",checked:e1,onCheckedChange:()=>e2(!e1),label:e1?"Completed":"Complete"})]}),s.jsx(g.AlertDialogNew,{openDialog:eK,setOpenDialog:eQ,handleCreate:()=>aG("updateFindings"),actionText:"Complete SubTask",title:"Add Findings",children:(0,s.jsxs)(P.iP,{className:"space-y-4",children:[s.jsx(L.Textarea,{id:"subtask-findings",rows:4,className:"w-full",placeholder:"Findings",defaultValue:(0,ed.j)(eZ.filter(e=>e.id===eV)[0]?.findings)}),s.jsx(R.Combobox,{id:"comment-author",label:"Crew member",options:tc||[],isLoading:!tc||0===tc.length,placeholder:"Crew member",className:"w-full",value:tc?.find(e=>e.value==eS)||void 0,onChange:e=>eE(e?.value)}),s.jsx(U.Z,{mode:"single",type:"datetime",label:"Select completed date",value:ew,onChange:e=>{eN(e)},clearable:!0,confirmSelection:!0})]})}),s.jsx(g.AlertDialogNew,{openDialog:e7,setOpenDialog:e8,handleCreate:aP,actionText:"Archive Task",title:"Archive Task",variant:"danger",showDestructiveAction:!0,handleDestructiveAction:aP,children:s.jsx(P.P,{children:"Are you sure you want to Archive this task?"})}),e9&&s.jsx(g.AlertDialogNew,{openDialog:e9,setOpenDialog:te,handleCreate:()=>{let t={input:{commentType:"General",description:tj,time:tt?m()(tt).format("DD/MM/YYYY HH:mm"):m()().format("DD/MM/YYYY HH:mm"),authorID:ti?.authorID,maintenanceCheckID:e,subTaskID:eJ?eV:0}};ti?.id>0?(tE(tS.map(e=>e.id===ti.id?{...e,description:tj,time:tt||e.time}:e)),sa({variables:{input:{id:ti?.id,...t.input}}})):(tE([{id:`temp-${Date.now()}`,description:tj,time:tt||new Date().toISOString(),author:{id:t.input.authorID,firstName:"You",surname:""},subTaskID:t.input.subTaskID},...tS]),st({variables:{input:{...t.input}}}))},title:ti?.id>0?"Update note":"Create new note",actionText:ti?.id>0?"Update":au("Create","Create note"),children:(0,s.jsxs)(P.iP,{className:"space-y-4",children:[s.jsx(U.Z,{mode:"single",type:"datetime",label:"Time of completion",value:tt,onChange:e=>{ta(e)},clearable:!0,modal:!0,confirmSelection:!0}),s.jsx(R.Combobox,{id:"comment-author",label:"Crew member",options:tc||[],isLoading:!tc,buttonClassName:"w-full",placeholder:"Crew member",value:tc?.find(e=>e.value==ti?.author?.id)||void 0,onChange:e=>to({...ti,authorID:e?.value})}),s.jsx(P.__,{label:"Comment",htmlFor:"comment",children:s.jsx(L.Textarea,{id:"comment",placeholder:"Comment",rows:8,value:(0,ed.j)(tj||""),onChange:e=>af(e.target.value)})})]})}),s.jsx(g.AlertDialogNew,{openDialog:ts,setOpenDialog:tr,title:"Delete Record",variant:"warning",showDestructiveAction:!0,handleDestructiveAction:()=>{tE(tS.filter(e=>e.id!==tn)),sa({variables:{input:{id:+tn,archived:!0}}}),tr(!1)},children:s.jsx(P.P,{children:"Are you sure you want to delete this record?"})}),s.jsx(g.AlertDialogNew,{openDialog:t_,setOpenDialog:tR,handleCreate:()=>{sv({variables:{input:{name:document.getElementById("task-new-category").value}}})},actionText:"Create Category",title:"Create new category",children:s.jsx(A.I,{id:"task-new-category",name:"task-new-category",type:"text",className:"w-full",placeholder:"Category"})}),s.jsx(g.AlertDialogNew,{openDialog:t8,setOpenDialog:t9,handleCreate:()=>{t5?.status==="OutOfService"?sf({variables:{input:{vesselID:t5?.vesselID,date:t5?.date,status:t5?.status,comment:t5?.comment,reason:t5?.reason,otherReason:t5?.otherReason,expectedReturn:t5?.expectedReturn}}}):sf({variables:{input:{vesselID:t5?.vesselID,date:t5?.date,status:t5?.status}}})},actionText:"Update",title:"Update Vessel Status",children:(0,s.jsxs)(P.iP,{className:"space-y-4",children:[s.jsx(U.Z,{mode:"single",type:"date",label:"Date",value:t5?.date?m()(t5.date).toDate():void 0,onChange:e=>{t7({...t5,date:e})},placeholder:"Select date",clearable:!0}),s.jsx(R.Combobox,{id:"vessel-status",options:ah&&ah.length>0?ah:[],placeholder:"Status",isLoading:!ah||0===ah.length,value:ah&&ah.length>0&&ah.find(e=>t5?.status===e.value)||void 0,onChange:e=>{t7({...t5,status:e?.value})}}),t5?.status==="OutOfService"&&s.jsx(R.Combobox,{id:"vessel-status-reason",options:av&&av.length>0?av:[],placeholder:"Reason",isLoading:!av||0===av.length,value:av&&av.length>0&&av.find(e=>t5?.reason===e.value)||void 0,onChange:e=>{t7({...t5,reason:e?.value})}}),t5?.status==="OutOfService"&&t5?.reason==="Other"&&s.jsx(L.Textarea,{id:"vessel-status-other",className:"w-full",placeholder:"Other description",value:(0,ed.j)(t5?.otherReason),onChange:e=>t7({...t5,otherReason:e.target.value})}),t5?.status==="OutOfService"&&s.jsx(P.__,{label:"Comment",htmlFor:"vessel-status-comment",children:s.jsx(L.Textarea,{id:"vessel-status-comment",placeholder:"Comment",rows:6,value:(0,ed.j)(t5?.comment||""),onChange:e=>t7({...t5,comment:e.target.value})})}),t5?.status==="OutOfService"&&s.jsx(U.Z,{mode:"single",type:"date",label:"Expected return date",value:t5?.expectedReturn?(()=>{let e=m()(t5.expectedReturn);return"function"==typeof e.toDate?e.toDate():new Date(e.toString())})():void 0,onChange:e=>{t7({...t5,expectedReturn:e})},placeholder:"Expected return date",clearable:!0})]})}),s.jsx(g.AlertDialogNew,{openDialog:eA,setOpenDialog:eL,title:"Task Findings",description:e_?`Details for ${e_.maintenanceScheduleSubTask.task}`:"",cancelText:"Close",noButton:!0,children:s.jsx("div",{className:"mt-2 p-4 bg-muted/50 rounded-lg",children:e_?.findings})})]})}},43926:(e,t,a)=>{a.d(t,{Z:()=>x});var s=a(98768),r=a(66263),n=a(60343),l=a(28147),i=a(12513),o=a(88557),c=a(27780),d=a(50526),u=a(7179);a(18937),a(73304);var m=a(39544),p=a(46877),h=a(13609),v=a(47634),g=a(34376);let x=({document:e,hideTitle:t=!1,showDeleteButton:a=!1,onDelete:x,canDelete:f=!0,deleteErrorMessage:b="You do not have permission to delete this document"})=>{let[y,j]=(0,n.useState)(!1),[k,C]=(0,n.useState)(!1),{toast:D}=(0,g.pm)(),w=()=>{if(!f){D({description:b,variant:"destructive"});return}x&&x(e.id)},N=()=>{let t=process.env.NEXT_PUBLIC_FILE_BASE_URL||"https://api.sealogs.com/assets/";return`${t}${e.fileFilename}`};return["jpg","jpeg","png","gif","webp","svg"].includes(e.fileFilename?.split(".").pop()?.toLowerCase()||"")&&!k?(0,s.jsxs)("div",{className:"group relative flex items-center gap-3",children:[s.jsx(m.Button,{variant:"ghost",onClick:()=>j(!0),className:"h-auto p-2 hover:bg-muted/50 transition-colors flex-1","aria-label":`View image: ${e.title}`,children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"relative overflow-hidden rounded-lg border border-border bg-muted/20",children:[s.jsx(l.default,{src:N()||"/placeholder.svg",alt:e.title,width:64,height:64,className:"h-16 w-16 object-cover transition-transform group-hover:scale-105",onError:()=>C(!0)}),s.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors flex items-center justify-center",children:s.jsx(p.Z,{className:"h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity"})})]}),!t&&(0,s.jsxs)("div",{className:"flex-1 text-left",children:[s.jsx("p",{className:"font-medium text-sm truncate max-w-[200px]",children:e.title}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Image •"," ",e.fileFilename?.split(".").pop()?.toUpperCase()]})]})]})}),a&&s.jsx(m.Button,{variant:"destructive",iconLeft:h.Z,iconOnly:!0,onClick:w,"aria-label":`Delete ${e.title}`}),s.jsx(i.ZP,{open:y,close:()=>j(!1),slides:[{src:N(),alt:e.title,description:e.title}],render:{buttonPrev:()=>null,buttonNext:()=>null},controller:{closeOnPullUp:!0,closeOnPullDown:!0,closeOnBackdropClick:!0},plugins:[o.Z,u.Z,d.Z,c.Z]})]}):(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[s.jsx(r.default,{href:N(),target:"_blank",rel:"noopener noreferrer",className:"group block flex-1",children:(0,s.jsxs)("div",{className:"flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors",children:[(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"h-16 w-16 rounded-lg border border-border border-dashed bg-muted/20 flex items-center justify-center group-hover:bg-muted/30 transition-colors",children:k?s.jsx(p.Z,{className:"h-6 w-6 text-muted-foreground"}):s.jsx(l.default,{src:{pdf:"/file-types/pdf.svg",xls:"/file-types/xls.svg",xlsx:"/file-types/xls.svg",ppt:"/file-types/ppt.svg",pptx:"/file-types/ppt.svg",txt:"/file-types/txt.svg",csv:"/file-types/csv.svg"}[e.fileFilename?.split(".").pop()?.toLowerCase()||""]||"/file-types/doc.svg",alt:`${e.fileFilename?.split(".").pop()?.toUpperCase()} file`,width:24,height:24,className:"h-6 w-6"})}),s.jsx("div",{className:"absolute -top-1 -right-1 bg-background border border-border rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity",children:s.jsx(v.Z,{className:"h-3 w-3 text-muted-foreground"})})]}),!t&&(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"font-medium text-sm truncate",children:e.title}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Document •"," ",e.fileFilename?.split(".").pop()?.toUpperCase()]})]})]})}),a&&s.jsx(m.Button,{variant:"destructive",iconLeft:h.Z,iconOnly:!0,onClick:w,"aria-label":`Delete ${e.title}`})]})}},24894:(e,t,a)=>{a.d(t,{Z:()=>v});var s=a(98768),r=a(60343),n=a(28147),l=a(79418),i=a(72548),o=a(67537),c=a(94060),d=a(76342),u=a(56937),m=a(71890),p=a(60797),h=a(25394);function v({setDocuments:e,text:t="Documents and Images",subText:a,bgClass:v="",documents:g,multipleUpload:x=!0}){let[f,b]=(0,r.useState)(!1),[y,j]=(0,r.useState)([]),[k,C]=(0,r.useState)(!1),[D,w]=(0,r.useState)(!1),N=(0,r.useRef)(null),S=(0,u.cn)("relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none",f?"bg-accent border-primary":"bg-accent/50 border-border","text-foreground hover:bg-accent hover:border-primary","min-h-[10rem] cursor-pointer select-none",v),E=async e=>{let t=new FormData;t.append("FileData",e,e.name.replace(/\s/g,""));try{let e=await fetch("https://api.sealogs.com/api/v2/upload",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("sl-jwt")}`},body:t}),a=await e.json();await I({variables:{id:[a[0].id]}}),w(!1)}catch(e){console.error(e)}},[I]=(0,l.t)(c.ZX,{fetchPolicy:"cache-and-network",onCompleted:e=>{j(t=>[...t,e.readFiles.nodes[0]]),C(!0)},onError:e=>console.error(e)}),[T]=(0,i.D)(d.RgS,{onCompleted:t=>{let a=t.updateFile;e(e=>x?[...e,a]:[a])},onError:e=>console.error(e)}),O=e=>{let t=Array.from(e);w(!0),t.forEach(E)},B=e=>t=>{t.preventDefault(),b(e)};return(0,s.jsxs)("div",{className:"w-full pt-4 lg:pt-0",children:[(0,s.jsxs)("form",{className:S,onSubmit:e=>e.preventDefault(),onDragEnter:B(!0),onDragOver:B(!0),onDragLeave:B(!1),onDrop:e=>{e.preventDefault(),b(!1),e.dataTransfer.files&&O(e.dataTransfer.files)},onClick:()=>N.current?.click(),"aria-label":"File uploader drop zone",children:[s.jsx("span",{className:"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider",children:t}),s.jsx(m.I,{ref:N,type:"file",className:"hidden",multiple:x,accept:".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf",onChange:e=>{e.target.files&&O(e.target.files)}}),(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2 pointer-events-none",children:[s.jsx(n.default,{src:"/sealogs-document_upload.svg",alt:"Upload illustration",width:96,height:96,className:"relative -translate-x-2.5",priority:!0}),a&&s.jsx("span",{className:"text-sm font-medium text-neutral-400",children:a})]})]}),D?(0,s.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2",children:[s.jsx(o.Z,{className:"h-5 w-5 animate-spin text-primary"}),s.jsx("span",{className:"text-sm text-muted-foreground",children:"Uploading..."})]}):s.jsx(h.h9,{openDialog:k,setOpenDialog:C,handleCreate:()=>{y.forEach((e,t)=>{let a=document.getElementById(`file-name-${t}`).value;T({variables:{input:{id:e.id,title:a}}})}),C(!1)},actionText:"Save",title:"File Name",children:s.jsx("div",{className:"space-y-4",children:y.map((e,t)=>s.jsx(p.Label,{label:`File ${t+1} Name`,htmlFor:`file-name-${t}`,children:s.jsx(m.I,{id:`file-name-${t}`,defaultValue:e.title,placeholder:"Enter file name"})},e.id))})})]})}},69422:(e,t,a)=>{a.d(t,{cn:()=>n});var s=a(28411),r=a(5001);function n(...e){return(0,r.m6)((0,s.W)(e))}},93488:(e,t,a)=>{a.d(t,{e:()=>r,p:()=>n});var s=a(50058);function r(e="phablet"){let t=(0,s.k)();return(a,s)=>t[e]?s:a}function n(e,t,a){return e?a:t}},78853:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(97428).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},49581:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(97428).Z)("MessageSquareText",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}],["path",{d:"M13 8H7",key:"14i4kc"}],["path",{d:"M17 12H7",key:"16if0g"}]])},18415:(e,t,a)=>{a.d(t,{Z:()=>s});let s=(0,a(97428).Z)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])}};