"use strict";exports.id=2925,exports.ids=[2925],exports.modules={39544:(e,r,t)=>{t.r(r),t.d(r,{Button:()=>m,buttonVariants:()=>u});var i=t(98768),s=t(60343),o=t(73404),a=t(85745),n=t(17203),d=t(67537),l=t(70684),c=t(56937),f=t(96268);let u=(0,a.j)("inline-flex items-center justify-center rounded-[6px] gap-[8.5px] whitespace-nowrap font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-5 [&_svg]:shrink-0",{variants:{variant:{primary:"h-11 py-3 border rounded-[6px] bg-primary border-primary text-primary-foreground shadow-[0_4px_6px_hsla(0,0%,0%,0.2)] hover:bg-card hover:text-accent-foreground hover:border-border",back:"bg-transparent text-curious-blue-400 group gap-[5px] transition-all duration-300",destructive:"text-destructive border bg-destructive-foreground items-center justify-center border-destructive rounded-[6px] hover:bg-cinnabar-200 dark:hover:bg-cinnabar-600 dark:text-destructive-foreground dark:bg-destructive",destructiveFill:"h-11 py-3 border rounded-[6px] bg-destructive border-destructive text-primary-foreground shadow-[0_4px_6px_hsla(0,0%,0%,0.2)] hover:bg-card hover:text-destructive hover:border-border",outline:"border border-border bg-card hover:bg-accent hover:text-accent-foreground font-normal flex-1 [&_svg]:size-auto text-input",primaryOutline:"border border-border bg-card hover:bg-accent hover:text-accent-foreground text-input font-normal",secondary:"bg-secondary text-foreground shadow-sm hover:bg-secondary/80",warning:"hover:bg-fire-bush-100 bg-fire-bush-100 border border-fire-bush-700 hover:text-fire-bush-700 text-fire-bush-700 dark:bg-fire-bush-700 dark:border-fire-bush-700 dark:hover:bg-fire-bush-800 dark:text-fire-bush-100",warningFill:"bg-fire-bush-600 text-fire-bush-50 hover:bg-fire-bush-800 dark:bg-fire-bush-500 dark:hover:bg-fire-bush-600",ghost:"hover:bg-card hover:text-input",link:"text-primary underline-offset-4 hover:underline p-0",text:"bg-transparent hover:bg-transparent shadow-none text-foreground p-0",info:"bg-curious-blue-600 text-white hover:bg-curious-blue-700"},size:{default:"h-11 xs:px-2.5 px-3  py-3",sm:"h-8 rounded-md px-3 text-xs",md:"h-11 px-3 px-2.5  py-3",lg:"h-12 xs:px-6 py-3 text-base rounded-md",icon:"size-10 p-2"}},defaultVariants:{variant:"primary",size:"default"}}),m=s.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,isLoading:m=!1,iconLeft:x,iconRight:p,iconSize:b=20,tooltip:h,iconOnly:g=!1,responsive:v=!1,extraSpace:w=16,children:y,asInput:j=!1,...N},F)=>{if(a){let s=o.g7;return i.jsx(s,{className:(0,c.cn)(u({variant:r,size:t}),e),ref:F,...N,children:y})}let k="back"!==r||x?x:n.Z,z=(0,s.useRef)(null),R=(0,s.useRef)(null),_=(0,f.rN)(F,z),I=function(e,r,t=0){let[i,o]=(0,s.useState)(!0);return i}(0,0,w),T=v&&!I&&!g,C=h||T&&y,V=h||("string"==typeof y?y:""),$=(0,c.cn)(u({variant:r,size:t}),g&&"flex items-center border-none size-fit [&_svg]:size-auto p-0 hover:bg-transparent shadow-none justify-center",T&&"px-3","text"===r&&"p-0 shadow-none","back"===r&&"gap-[5px]",j&&!g&&"overflow-hidden min-w-0","will-change-transform will-change-width will-change-padding transform-gpu hover:transition-colors hover:ease-out hover:duration-300",e),E=(0,i.jsxs)(i.Fragment,{children:[m&&i.jsx(d.Z,{className:"size-5 animate-spin"}),k&&!m&&i.jsx("span",{className:"back"===r?"relative group-hover:-translate-x-[5px] w-fit transition-transform ease-out duration-300":"flex-shrink-0 w-fit",children:s.isValidElement(k)?k:s.createElement(k,{size:b})}),y&&!g&&i.jsx("span",{ref:R,className:(0,c.cn)(T?"sr-only":"flex-shrink-0",j&&"flex-1 min-w-0 truncate whitespace-nowrap"),children:y}),p&&!m&&i.jsx("span",{className:"flex-shrink-0 w-fit",children:s.isValidElement(p)?p:s.createElement(p,{size:b})})]});return C?i.jsx(l.TooltipProvider,{children:(0,i.jsxs)(l.Tooltip,{children:[i.jsx(l.TooltipTrigger,{ref:_,className:$,disabled:m||N.disabled,...N,children:E}),i.jsx(l.TooltipContent,{children:i.jsx("p",{children:V})})]})}):i.jsx("button",{ref:_,className:$,disabled:m||N.disabled,...N,children:E})});m.displayName="Button"},57906:(e,r,t)=>{t.r(r),t.d(r,{Form:()=>l,FormControl:()=>b,FormDescription:()=>h,FormField:()=>f,FormItem:()=>x,FormLabel:()=>p,FormMessage:()=>g,useFormField:()=>u});var i=t(98768),s=t(60343),o=t(73404),a=t(66314),n=t(56937),d=t(60797);let l=a.RV,c=s.createContext({}),f=({...e})=>i.jsx(c.Provider,{value:{name:e.name},children:i.jsx(a.Qr,{...e})}),u=()=>{let e=s.useContext(c),r=s.useContext(m),{getFieldState:t,formState:i}=(0,a.Gc)(),o=t(e.name,i);if(!e)throw Error("useFormField should be used within <FormField>");let{id:n}=r;return{id:n,name:e.name,formItemId:`${n}-form-item`,formDescriptionId:`${n}-form-item-description`,formMessageId:`${n}-form-item-message`,...o}},m=s.createContext({}),x=s.forwardRef(({className:e,...r},t)=>{let o=s.useId();return i.jsx(m.Provider,{value:{id:o},children:i.jsx("div",{ref:t,className:(0,n.cn)("space-y-2 py-2",e),...r})})});x.displayName="FormItem";let p=s.forwardRef(({className:e,...r},t)=>{let{error:s,formItemId:o}=u();return i.jsx(d.Label,{ref:t,className:(0,n.cn)(s&&"text-destructive",e),htmlFor:o,...r})});p.displayName="FormLabel";let b=s.forwardRef(({...e},r)=>{let{error:t,formItemId:s,formDescriptionId:a,formMessageId:n}=u();return i.jsx(o.g7,{ref:r,id:s,"aria-describedby":t?`${a} ${n}`:`${a}`,"aria-invalid":!!t,...e})});b.displayName="FormControl";let h=s.forwardRef(({className:e,...r},t)=>{let{formDescriptionId:s}=u();return i.jsx("p",{ref:t,id:s,className:(0,n.cn)("text-[0.8rem] text-muted-foreground",e),...r})});h.displayName="FormDescription";let g=s.forwardRef(({className:e,children:r,...t},s)=>{let{error:o,formMessageId:a}=u(),d=o?String(o?.message):r;return d?i.jsx("p",{ref:s,id:a,className:(0,n.cn)("text-[0.8rem] font-medium text-destructive",e),...t,children:d}):null});g.displayName="FormMessage"},71890:(e,r,t)=>{t.d(r,{I:()=>o});var i=t(98768),s=t(56937);let o=t(60343).forwardRef(({className:e,type:r,onFocus:t,...o},a)=>i.jsx("input",{type:r,className:(0,s.cn)("flex h-11 w-full text-input text-base leading-5 rounded-md border border-border bg-card px-3 py-1 transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-background0 placeholder:text-neutral-400 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,onFocus:e=>{"number"===r&&"0"===e.target.value&&(e.target.value=""),t?.(e)},...o}));o.displayName="Input"},60797:(e,r,t)=>{t.r(r),t.d(r,{Label:()=>l});var i=t(98768),s=t(60343),o=t(36601),a=t(85745),n=t(56937);let d=(0,a.j)("font-medium text-foreground peer-disabled:cursor-not-allowed peer-disabled:opacity-70 whitespace-nowrap",{variants:{position:{top:"block mb-[7px]",bottom:"mt-[7px]",left:"whitespace-nowrap shrink-0",right:"whitespace-nowrap shrink-0"},size:{sm:"text-xs leading-4",default:"text-[14px] leading-5",md:"text-base leading-6",lg:"text-lg leading-7"}},defaultVariants:{position:"top",size:"default"}}),l=s.forwardRef(({className:e,position:r="top",size:t="default",children:a,disabled:l=!1,label:c,leftContent:f,rightContent:u,required:m=!1,interactive:x=!1,labelClassName:p,...b},h)=>{let g=s.useId(),v=b.htmlFor||g,w="string"==typeof a||"number"==typeof a,y=x?"cursor-pointer":void 0,j=(0,i.jsxs)(i.Fragment,{children:[c||(w?a:void 0),m&&i.jsx("span",{className:"ml-1 text-red-500",children:"*"})]});if(!a||w)return f||u?i.jsx(o.f,{ref:h,className:(0,n.cn)(d({position:r,size:t}),y,e),htmlFor:v,...b,children:(0,i.jsxs)("div",{className:(0,n.cn)("flex items-center",{"gap-2":f||u}),children:[f&&i.jsx("div",{className:"inline-flex items-center mr-2",children:f}),j,u&&i.jsx("div",{className:"inline-flex items-center ml-2",children:u})]})}):i.jsx(o.f,{ref:h,className:(0,n.cn)(d({position:r,size:t}),y,e),htmlFor:v,...b,children:j});let N=(0,n.cn)("flex","left"===r&&"flex-row items-center gap-[8.5px]","right"===r&&"flex-row-reverse items-center gap-[8.5px]","top"===r&&"flex-col","bottom"===r&&"flex-col-reverse",l&&"opacity-60",y),F=(0,n.cn)("inline-flex flex-col w-full",("left"===r||"right"===r)&&"min-w-0",("top"===r||"bottom"===r)&&"w-full");return(0,i.jsxs)("div",{className:(0,n.cn)(N,e),children:[(0,i.jsxs)("div",{className:(0,n.cn)("flex items-center",{"gap-2":f||u}),children:[f&&i.jsx("div",{className:"inline-flex items-center",children:f}),c&&i.jsx(o.f,{ref:h,className:(0,n.cn)(d({position:r,size:t}),p),htmlFor:v,...b,children:j}),u&&i.jsx("div",{className:"inline-flex items-center",children:u})]}),i.jsx("div",{className:F,children:a})]})});l.displayName=o.f.displayName},70684:(e,r,t)=>{t.r(r),t.d(r,{Tooltip:()=>d,TooltipContent:()=>c,TooltipProvider:()=>n,TooltipTrigger:()=>l});var i=t(98768),s=t(60343),o=t(146),a=t(56937);let n=o.zt,d=o.fC,l=o.xz,c=s.forwardRef(({className:e,sideOffset:r=4,...t},s)=>i.jsx(o.h_,{children:i.jsx(o.VY,{ref:s,sideOffset:r,className:(0,a.cn)("z-50 overflow-hidden px-3 py-1.5  text-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 bg-fire-bush-100 text-fire-bush-600 rounded-md border border-fire-bush-500",e),...t})}));c.displayName=o.VY.displayName}};