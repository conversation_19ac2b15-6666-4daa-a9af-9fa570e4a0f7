(()=>{var e={};e.id=1765,e.ids=[1765],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},31036:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),s(12127),s(19768),s(78398),s(57757),s(48045);var t=s(40060),i=s(33581),n=s(57567),o=s.n(n),a=s(51650),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);s.d(r,l);let d=["",{children:["incident-records",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,12127)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\incident-records\\create\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,19768)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\incident-records\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\incident-records\\create\\page.tsx"],u="/incident-records/create/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/incident-records/create/page",pathname:"/incident-records/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89442:(e,r,s)=>{Promise.resolve().then(s.bind(s,8971)),Promise.resolve().then(s.bind(s,20412)),Promise.resolve().then(s.bind(s,93500)),Promise.resolve().then(s.bind(s,97514)),Promise.resolve().then(s.bind(s,45305)),Promise.resolve().then(s.bind(s,44106)),Promise.resolve().then(s.bind(s,58301)),Promise.resolve().then(s.bind(s,7672)),Promise.resolve().then(s.bind(s,40533)),Promise.resolve().then(s.bind(s,57103)),Promise.resolve().then(s.bind(s,69748)),Promise.resolve().then(s.bind(s,39544)),Promise.resolve().then(s.bind(s,8750)),Promise.resolve().then(s.bind(s,81524)),Promise.resolve().then(s.bind(s,48651)),Promise.resolve().then(s.bind(s,24794)),Promise.resolve().then(s.bind(s,27514)),Promise.resolve().then(s.bind(s,83676)),Promise.resolve().then(s.bind(s,57906)),Promise.resolve().then(s.bind(s,60797)),Promise.resolve().then(s.bind(s,39650)),Promise.resolve().then(s.bind(s,15580)),Promise.resolve().then(s.bind(s,24224)),Promise.resolve().then(s.bind(s,26509)),Promise.resolve().then(s.bind(s,69852)),Promise.resolve().then(s.bind(s,36895)),Promise.resolve().then(s.bind(s,52269)),Promise.resolve().then(s.bind(s,70684))},6605:(e,r,s)=>{Promise.resolve().then(s.bind(s,331))},331:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var t=s(98768);s(60343);var i=s(64837);function n({children:e}){return t.jsx(i.Z,{children:e})}},12127:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>o});var t=s(96141);let i=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\incident-record\incident-record-form.tsx#default`);var n=s(73501);let o=()=>t.jsx(n.Zb,{children:t.jsx(i,{})})},19768:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\incident-records\layout.tsx#default`)},9999:(e,r,s)=>{"use strict";s.d(r,{a:()=>i});var t=s(60343);function i(e,r,{getInitialValueInEffect:s}={getInitialValueInEffect:!0}){let[i,n]=(0,t.useState)(!!s&&r);return(0,t.useRef)(null),i||!1}},79320:(e,r,s)=>{"use strict";s.d(r,{t:()=>o});var t=s(93140),i=s(69359),n=s(79824);function o(e,r){let s=(0,n.Q)(e);return isNaN(+s)?(0,t.L)(e,NaN):(null!=r.year&&s.setFullYear(r.year),null!=r.month&&(s=(0,i.q)(s,r.month)),null!=r.date&&s.setDate(r.date),null!=r.hours&&s.setHours(r.hours),null!=r.minutes&&s.setMinutes(r.minutes),null!=r.seconds&&s.setSeconds(r.seconds),null!=r.milliseconds&&s.setMilliseconds(r.milliseconds),s)}},99491:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(97428).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},72997:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(97428).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49008:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(97428).Z)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[864,8865,3563,6263,8189,9507,7602,2935,8822,1484,5206,6451,4234,2925,5394,4837,6342,8712,90,7346,5776,7672,3501],()=>s(31036));module.exports=t})();