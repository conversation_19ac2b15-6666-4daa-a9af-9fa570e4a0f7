(()=>{var e={};e.id=1247,e.ids=[1247],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},12950:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>d,routeModule:()=>f,tree:()=>l}),r(1844),r(50681),r(78398),r(57757),r(48045);var i=r(40060),s=r(33581),n=r(57567),a=r.n(n),o=r(51650),u={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);r.d(t,u);let l=["",{children:["settings",{children:["crew-duty",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1844)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\crew-duty\\edit\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50681)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\crew-duty\\edit\\page.tsx"],c="/settings/crew-duty/edit/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new i.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/settings/crew-duty/edit/page",pathname:"/settings/crew-duty/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},96953:(e,t,r)=>{Promise.resolve().then(r.bind(r,8605))},31941:(e,t,r)=>{Promise.resolve().then(r.bind(r,84313))},60826:e=>{e.exports=function(e){return e.split("")}},829:e=>{e.exports=function(e,t,r,i){for(var s=e.length,n=r+(i?1:-1);i?n--:++n<s;)if(t(e[n],n,e))return n;return -1}},65337:(e,t,r)=>{var i=r(829),s=r(35447),n=r(28026);e.exports=function(e,t,r){return t==t?n(e,t,r):i(e,s,r)}},35447:e=>{e.exports=function(e){return e!=e}},77420:e=>{e.exports=function(e,t,r){var i=-1,s=e.length;t<0&&(t=-t>s?0:s+t),(r=r>s?s:r)<0&&(r+=s),s=t>r?0:r-t>>>0,t>>>=0;for(var n=Array(s);++i<s;)n[i]=e[i+t];return n}},49513:(e,t,r)=>{var i=r(70458),s=/^\s+/;e.exports=function(e){return e?e.slice(0,i(e)+1).replace(s,""):e}},30482:(e,t,r)=>{var i=r(77420);e.exports=function(e,t,r){var s=e.length;return r=void 0===r?s:r,!t&&r>=s?e:i(e,t,r)}},74783:(e,t,r)=>{var i=r(65337);e.exports=function(e,t){for(var r=e.length;r--&&i(t,e[r],0)>-1;);return r}},41200:(e,t,r)=>{var i=r(65337);e.exports=function(e,t){for(var r=-1,s=e.length;++r<s&&i(t,e[r],0)>-1;);return r}},73211:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},28026:e=>{e.exports=function(e,t,r){for(var i=r-1,s=e.length;++i<s;)if(e[i]===t)return i;return -1}},66095:(e,t,r)=>{var i=r(60826),s=r(73211),n=r(92115);e.exports=function(e){return s(e)?n(e):i(e)}},70458:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},92115:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",i="\ud83c[\udffb-\udfff]",s="[^"+t+"]",n="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",o="(?:"+r+"|"+i+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[s,n,a].join("|")+")"+u+o+")*",d=RegExp(i+"(?="+i+")|(?:"+[s+r+"?",r,n,a,"["+t+"]"].join("|")+")"+(u+o+l),"g");e.exports=function(e){return e.match(d)||[]}},71241:(e,t,r)=>{var i=r(4171),s=r(96817),n=r(24436),a=Math.max,o=Math.min;e.exports=function(e,t,r){var u,l,d,c,p,f,x=0,v=!1,g=!1,m=!0;if("function"!=typeof e)throw TypeError("Expected a function");function h(t){var r=u,i=l;return u=l=void 0,x=t,c=e.apply(i,r)}function y(e){var r=e-f,i=e-x;return void 0===f||r>=t||r<0||g&&i>=d}function b(){var e,r,i,n=s();if(y(n))return w(n);p=setTimeout(b,(e=n-f,r=n-x,i=t-e,g?o(i,d-r):i))}function w(e){return(p=void 0,m&&u)?h(e):(u=l=void 0,c)}function j(){var e,r=s(),i=y(r);if(u=arguments,l=this,f=r,i){if(void 0===p)return x=e=f,p=setTimeout(b,t),v?h(e):c;if(g)return clearTimeout(p),p=setTimeout(b,t),h(f)}return void 0===p&&(p=setTimeout(b,t)),c}return t=n(t)||0,i(r)&&(v=!!r.leading,d=(g="maxWait"in r)?a(n(r.maxWait)||0,t):d,m="trailing"in r?!!r.trailing:m),j.cancel=function(){void 0!==p&&clearTimeout(p),x=0,u=f=l=p=void 0},j.flush=function(){return void 0===p?c:w(s())},j}},96817:(e,t,r)=>{var i=r(65584);e.exports=function(){return i.Date.now()}},24436:(e,t,r)=>{var i=r(49513),s=r(4171),n=r(15903),a=0/0,o=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,d=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(n(e))return a;if(s(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=s(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=i(e);var r=u.test(e);return r||l.test(e)?d(e.slice(2),r?2:8):o.test(e)?a:+e}},14826:(e,t,r)=>{var i=r(22060),s=r(49513),n=r(30482),a=r(74783),o=r(41200),u=r(66095),l=r(16266);e.exports=function(e,t,r){if((e=l(e))&&(r||void 0===t))return s(e);if(!e||!(t=i(t)))return e;var d=u(e),c=u(t),p=o(d,c),f=a(d,c)+1;return n(d,p,f).join("")}},8605:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var i=r(98768),s=r(67468),n=r(25394),a=r(69424);let o=()=>{let e=(0,a.useSearchParams)().get("id")??0;return i.jsx(n.Zb,{children:i.jsx(n.aY,{children:i.jsx(s.default,{crewDutyId:+e})})})}},84313:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var i=r(98768);r(60343);var s=r(64837);function n({children:e}){return i.jsx(s.Z,{children:e})}},67468:(e,t,r)=>{"use strict";r.d(t,{default:()=>D});var i=r(98768),s=r(72548),n=r(60343),a=r(71241),o=r.n(a),u=r(7678),l=r.n(u),d=r(14826),c=r.n(d),p=r(69424),f=r(76342),x=r(66263),v=r(78965),g=r(13842),m=r(46776),h=r(71890),y=r(57103),b=r(17203),w=r(81311),j=r(74602),C=r(39544);let D=({crewDutyId:e,onCancel:t,onCreate:r,isPopup:a=!1})=>{let[u,d]=(0,n.useState)({}),D=(0,p.useRouter)(),[q,P]=(0,n.useState)(!1),[T,N]=(0,n.useState)({title:"",abbreviation:""}),[_,S]=(0,n.useState)(!1),[E,U]=(0,n.useState)(!1);(0,g.UL)(e,d);let B=o()((t,r)=>{d({...u,[t]:r,id:+e})},300),k=e=>{let{name:t,value:r}=e.target;B(t,r)},A=async()=>{let t=!1,r={title:"",abbreviation:""};if(l()(c()(u.title))&&(t=!0,r.title="Title is required"),l()(c()(u.abbreviation))&&(t=!0,r.abbreviation="Abbreviation is required"),t){P(!0),N(r);return}let i={input:{id:+u.id,title:u.title,abbreviation:u.abbreviation}};0===e?await M({variables:i}):await R({variables:i})},[M,{loading:L}]=(0,s.D)(f.fJx,{onCompleted:e=>{let t=e.createCrewDuty;t.id>0?r?r(t):D.back():console.error("mutationCreateCrewDuty error",e)},onError:e=>{console.error("mutationCreateCrewDuty error",e)}}),[R,{loading:V}]=(0,s.D)(f.Qem,{onCompleted:e=>{e.updateCrewDuty.id>0?D.back():console.error("mutationUpdateCrewDuty error",e)},onError:e=>{console.error("mutationUpdateCrewDuty error",e)}}),$=async e=>{await G({variables:{ids:[e.id]}})},[G]=(0,s.D)(f.T5T,{onCompleted:()=>{D.push("/settings/crew-duty/list")},onError:e=>{console.error("mutationDeleteCrewDuty error",e)}});return(0,n.useEffect)(()=>{(0,m.UU)()},[]),(0,i.jsxs)(i.Fragment,{children:[i.jsx("div",{className:" flex justify-between pb-4 pt-3 items-center",children:(0,i.jsxs)(j.H3,{children:[0===e?"Create":"Edit"," Crew Duty"]})}),(0,i.jsxs)("div",{className:`${a?"grid-cols-2":"grid-cols-3"} grid gap-6`,children:[(0,i.jsxs)("div",{className:`${a?"hidden":""} my-4 `,children:["Crew duty details",i.jsx("p",{className:" mt-4 max-w-[25rem] leading-loose",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Esse minima maxime enim, consectetur hic est perferendis explicabo suscipit rem reprehenderit vitae ex sunt corrupti obcaecati aliquid natus et inventore tenetur?"})]}),(0,i.jsxs)("div",{className:"w-full grid col-span-2 gap-4",children:[(0,i.jsxs)("div",{children:[i.jsx(h.I,{name:"title",type:"text",required:!0,defaultValue:u?.title||"",onChange:k,placeholder:"Duty Title"}),i.jsx("small",{className:"text-destructive",children:q&&T.title})]}),(0,i.jsxs)("div",{children:[i.jsx(h.I,{name:"abbreviation",type:"text",placeholder:"Abbreviation",required:!0,defaultValue:u?.abbreviation||"",onChange:k}),i.jsx("small",{className:"text-destructive",children:q&&T.abbreviation})]})]})]}),a?(0,i.jsxs)("div",{className:"flex justify-end pt-8 gap-2.5",children:[i.jsx(C.Button,{variant:"back",onClick:t,children:"Cancel"}),0!==e&&(0,i.jsxs)(i.Fragment,{children:[i.jsx(C.Button,{variant:"destructive",onClick:()=>{U(!0)},children:"Delete"}),i.jsx(y.AlertDialogNew,{openDialog:E,setOpenDialog:U,title:"Delete Crew Duty",description:`Are you sure you want to delete "${u.title}"?`,variant:"danger",actionText:"Delete",handleCreate:()=>$(u)})]}),i.jsx(C.Button,{onClick:A,disabled:L||V,children:`${0===e?"Create":"Update"} Duty`})]}):(0,i.jsxs)(v.V,{children:[i.jsx(x.default,{href:"/settings/crew-duty/list",children:i.jsx(C.Button,{variant:"back",iconLeft:b.Z,className:"mr-2",children:"Cancel"})}),0!==e&&(0,i.jsxs)(i.Fragment,{children:[i.jsx(C.Button,{variant:"destructive",onClick:()=>{S(!0)},children:"Delete"}),i.jsx(y.AlertDialogNew,{openDialog:_,setOpenDialog:S,title:"Delete Crew Duty",description:`Are you sure you want to delete "${u.title}"?`,variant:"danger",actionText:"Delete",handleCreate:()=>$(u)})]}),i.jsx(C.Button,{iconLeft:w.Z,onClick:A,disabled:L||V,children:`${0===e?"Create":"Update"} Duty`})]})]})}},1844:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\crew-duty\edit\page.tsx#default`)},50681:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\layout.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,6451,4234,2925,5394,4837,6342,3842],()=>r(12950));module.exports=i})();