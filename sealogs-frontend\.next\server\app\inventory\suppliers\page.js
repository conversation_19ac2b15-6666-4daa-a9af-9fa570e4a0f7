(()=>{var e={};e.id=9195,e.ids=[9195],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},95341:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>u,routeModule:()=>h,tree:()=>d}),r(85172),r(2223),r(78398),r(57757),r(48045);var t=r(40060),i=r(33581),a=r(57567),n=r.n(a),l=r(51650),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d=["",{children:["inventory",{children:["suppliers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85172)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\suppliers\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,2223)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\suppliers\\page.tsx"],c="/inventory/suppliers/page",p={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/inventory/suppliers/page",pathname:"/inventory/suppliers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},85363:(e,s,r)=>{Promise.resolve().then(r.bind(r,71462))},42147:(e,s,r)=>{Promise.resolve().then(r.bind(r,36881))},71462:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(98768);r(60343);var i=r(64837);function a({children:e}){return t.jsx(i.Z,{children:e})}},36881:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>M});var t=r(98768),i=r(60343),a=r(39544),n=r(15580),l=r(58830),o=r(29054),d=r(26100),u=r(66263),c=r(79418),p=r(94060),h=r(7678),x=r.n(h),m=r(14826),g=r.n(m),f=r(51742),v=r(30905),y=r(39650),j=r(27514),w=r(22995),b=r(9210);let N=()=>{let{isMobile:e}=(0,w.Ap)();return(0,t.jsxs)(j.DropdownMenu,{children:[t.jsx(j.DropdownMenuTrigger,{asChild:!0,children:t.jsx(b.HV,{size:36})}),t.jsx(j.DropdownMenuContent,{side:e?"bottom":"right",align:e?"end":"start",children:t.jsx(u.default,{href:"/inventory/suppliers/new",children:t.jsx(j.DropdownMenuItem,{children:"New Supplier"})})})]})};var P=r(25394);function q(){let[e,s]=(0,i.useState)([]),[r,h]=(0,i.useState)({}),[m,j]=(0,i.useState)([]),[w,b]=(0,i.useState)(!0),[q]=(0,c.t)(p.l1,{fetchPolicy:"cache-and-network",onCompleted:e=>{let r=e.readSuppliers.nodes;r&&s(r)},onError:e=>{console.error("querySupplier error",e)}}),M=async(e={},r=m)=>{if(r.length>0){let t=r.map(async s=>await q({variables:{filter:{...e,...s}}})),i=await Promise.all(t);s(i=(i=(i=i.filter(e=>e.data.readSuppliers.nodes.length>0)).flatMap(e=>e.data.readSuppliers.nodes)).filter((e,s,r)=>r.findIndex(s=>s.id===e.id)===s))}else await q({variables:{filter:e}})},S=(0,f.wu)([{accessorKey:"title",header:({column:e})=>t.jsx(v.u,{column:e,title:"Suppliers"}),cellAlignment:"left",cell:({row:e})=>{let s=e.original;return(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"font-medium",children:[t.jsx(u.default,{href:`/inventory/suppliers/view?id=${s.id}`,className:"hover:underline",children:s.name}),s.phone&&t.jsx("span",{className:"ml-3 text-sm text-muted-foreground",children:s.phone})]}),s.email&&t.jsx("div",{className:"text-sm text-muted-foreground",children:s.email})]})},sortingFn:(e,s)=>{let r=e?.original?.name||"",t=s?.original?.name||"";return r.localeCompare(t)}},{accessorKey:"address",header:"Address",cellAlignment:"left",cell:({row:e})=>{let s=e.original;return t.jsx("div",{className:"text-sm",children:s.address||"-"})}},{accessorKey:"website",header:"Website",cellAlignment:"left",cell:({row:e})=>{let s=e.original;return s.website?t.jsx("a",{href:`https://${s.website}`,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-blue-600 hover:underline",children:s.website}):t.jsx("span",{className:"text-sm text-muted-foreground",children:"-"})}},{accessorKey:"contactPeople",header:"Contact People",cellAlignment:"left",cell:({row:e})=>{let s=e.original;return(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[s.phone&&t.jsx("div",{children:s.phone}),s.email&&t.jsx("div",{className:"text-muted-foreground",children:s.email}),!s.phone&&!s.email&&t.jsx("span",{className:"text-muted-foreground",children:"-"})]})}},{accessorKey:"notes",header:"Notes",cellAlignment:"right",cell:({row:e})=>{let s=e.original;return(0,t.jsxs)(n.Popover,{children:[t.jsx(n.PopoverTrigger,{asChild:!0,children:t.jsx(a.Button,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:t.jsx(l.Z,{className:"h-4 w-4"})})}),t.jsx(n.PopoverContent,{className:"w-80",children:(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(P.P,{className:"font-medium text-sm",children:"Notes"}),t.jsx(P.P,{className:"text-sm text-muted-foreground",children:s.notes||"No notes available"})]})})]})}}]);return(0,t.jsxs)(t.Fragment,{children:[t.jsx(y.ListHeader,{icon:t.jsx(o.Z,{className:"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff] text-curious-blue-950"}),title:"All suppliers",actions:t.jsx(N,{})}),t.jsx("div",{className:"mt-16",children:w?t.jsx(d.Z,{}):t.jsx(f.wQ,{columns:S,data:e,showToolbar:!0,pageSize:20,onChange:({type:e,data:s})=>{let t={...r},i=m;"keyword"===e&&(i=x()(g()(s.value))?[]:[{name:{contains:s.value}},{website:{contains:s.value}},{phone:{contains:s.value}},{email:{contains:s.value}},{address:{contains:s.value}}]),h(t),j(i),M(t,i)}})})]})}function M(){return t.jsx(q,{})}},2223:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\inventory\layout.tsx#default`)},85172:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\inventory\suppliers\page.tsx#default`)},29054:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(97428).Z)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},58830:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(97428).Z)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7944],()=>r(95341));module.exports=t})();