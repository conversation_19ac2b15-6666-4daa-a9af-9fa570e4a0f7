(()=>{var e={};e.id=1788,e.ids=[1788],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},58210:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>l}),r(69842),r(50681),r(78398),r(57757),r(48045);var s=r(40060),a=r(33581),n=r(57567),i=r.n(n),o=r(51650),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let l=["",{children:["settings",{children:["maintenance",{children:["category",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,69842)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\maintenance\\category\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50681)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\maintenance\\category\\page.tsx"],u="/settings/maintenance/category/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/settings/maintenance/category/page",pathname:"/settings/maintenance/category",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},31941:(e,t,r)=>{Promise.resolve().then(r.bind(r,84313))},73971:(e,t,r)=>{Promise.resolve().then(r.bind(r,62591))},84313:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(98768);r(60343);var a=r(64837);function n({children:e}){return s.jsx(a.Z,{children:e})}},62591:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(98768);r(46776);var a=r(60343),n=r(72548),i=r(76342),o=r(69424),c=r(13842),l=r(71890),d=r(78965),u=r(50088),p=r(25394);function x({categoryID:e}){let t=(0,o.useRouter)(),[r,x]=(0,a.useState)(!1),[g,m]=(0,a.useState)();(0,c.dY)(e,m);let h=async()=>{let t=document.getElementById("category-name")?.value,r=document.getElementById("category-abbr")?.value;if(""!==t)return await y({variables:{input:{id:+e,name:t,abbreviation:r}}})},[y,{loading:j}]=(0,n.D)(i.VI8,{onCompleted:e=>{e.updateMaintenanceCategory.id>0?t.back():console.error("mutationupdateMaintenanceCategory error",e)},onError:e=>{console.error("mutationupdateMaintenanceCategory error",e)}}),f=async()=>{await v({variables:{ids:[+e]}})},[v,{loading:b}]=(0,n.D)(i.wth,{onCompleted:e=>{e.deleteMaintenanceCategories?t.back():console.error("mutationDeleteMaintenanceCategory error",e)},onError:e=>{console.error("mutationDeleteMaintenanceCategory error",e)}});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(p.Zb,{className:"mb-2",children:[s.jsx(p.Ol,{children:s.jsx(p.ll,{children:s.jsx(p.H3,{children:"Edit Maintenance Category"})})}),s.jsx(p.aY,{children:s.jsx("div",{className:"grid grid-cols-2",children:(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[s.jsx(p.__,{label:"Category name",children:s.jsx(l.I,{id:"category-name",type:"text",placeholder:"Category name",defaultValue:g?.name})}),s.jsx(p.__,{label:"Abbreviation",children:s.jsx(l.I,{id:"category-abbr",type:"text",placeholder:"Abbreviation",defaultValue:g?.abbreviation})})]})})})]}),(0,s.jsxs)(d.V,{children:[s.jsx(p.zx,{variant:"back",onClick:()=>t.push("/settings/maintenance/category"),children:"Back"}),s.jsx(p.zx,{variant:"destructive",color:"rose",onClick:()=>x(!0),children:"Delete"}),s.jsx(u.Z,{text:"Update Category",type:"primary",icon:"check",color:"sky",action:h})]}),(0,s.jsxs)(p.h9,{openDialog:r,setOpenDialog:x,handleCreate:f,actionText:"Delete Category",variant:"danger",children:[s.jsx(p.H2,{slot:"title",className:"text-2xl font-light leading-6 my-2 ",children:"Delete Category"}),(0,s.jsxs)("div",{className:"my-4 flex items-center",children:["Are you sure you want to delete ",g?.name,"?"]})]})]})}var g=r(66263);let m=(0,r(97428).Z)("Rows4",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M21 7.5H3",key:"1hm9pq"}],["path",{d:"M21 12H3",key:"2avoz0"}],["path",{d:"M21 16.5H3",key:"n7jzkj"}]]);function h(){let e=(0,o.useRouter)(),t=(0,o.usePathname)(),r=(0,o.useSearchParams)(),[n,i]=(0,a.useState)();return(0,c.bB)(i),(0,s.jsxs)(s.Fragment,{children:[s.jsx(p.Bu,{title:"Maintenance Categories",actions:s.jsx(u.Z,{action:()=>e.push("/settings/maintenance/category/new"),text:"Add New",color:"sky",type:"primary",icon:"check"})}),s.jsx(p.Zb,{className:"mt-8",children:s.jsx(p.aY,{children:n&&(0,s.jsxs)(p.iA,{children:[s.jsx(p.xD,{children:(0,s.jsxs)(p.SC,{children:[s.jsx(p.ss,{children:"Name"}),s.jsx(p.ss,{children:"Abbreviation"}),s.jsx(p.ss,{children:"Recent maintenance tasks"})]})}),s.jsx(p.RM,{children:n.map(e=>(0,s.jsxs)(p.SC,{children:[s.jsx(p.pj,{children:s.jsx(g.default,{href:`/settings/maintenance/category?categoryID=${e.id}`,className:"",children:e.name})}),s.jsx(p.pj,{children:e.abbreviation}),s.jsx(p.pj,{children:e?.componentMaintenanceCheck?.nodes.length>0&&(0,s.jsxs)(p.J2,{children:[s.jsx(p.CM,{asChild:!0,children:s.jsx(p.zx,{size:"icon",variant:"primaryOutline",children:s.jsx(m,{})})}),s.jsx(p.yk,{children:e.componentMaintenanceCheck.nodes.map(e=>s.jsx(g.default,{href:`/maintenance?taskID=${e.id}&redirect_to=${t}?${r.toString()}`,className:"block py-2 ps-2 ",children:e.name},e))})]})})]},e.id))})]})})})]})}function y(){let e=(0,o.useSearchParams)().get("categoryID")??0;return s.jsx(s.Fragment,{children:0==+e?s.jsx(h,{}):s.jsx(x,{categoryID:+e})})}},50681:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\layout.tsx#default`)},69842:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\maintenance\category\page.tsx#default`)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,6451,4234,2925,5394,4837,6342,3842,88],()=>r(58210));module.exports=s})();