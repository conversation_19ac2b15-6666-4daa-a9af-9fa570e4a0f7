(()=>{var e={};e.id=8505,e.ids=[8505],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},37291:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>r.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),t(96613),t(22207),t(78398),t(57757),t(48045);var a=t(40060),l=t(33581),i=t(57567),r=t.n(i),n=t(51650),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c=["",{children:["company-details",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,96613)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\company-details\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,22207)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\company-details\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\company-details\\page.tsx"],u="/company-details/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/company-details/page",pathname:"/company-details",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83621:(e,s,t)=>{Promise.resolve().then(t.bind(t,14088))},44784:(e,s,t)=>{Promise.resolve().then(t.bind(t,89183))},14088:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(98768);t(60343);var l=t(64837);function i({children:e}){return a.jsx(l.Z,{children:e})}},89183:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>F});var a=t(98768),l=t(60343),i=t(81524),r=t(44010);let n=({value:e="NZ",onChange:s})=>{let[t,n]=(0,l.useState)(void 0);return(0,l.useEffect)(()=>{let s=r.$Y.find(e=>"NZ"===e.value);e&&(s=r.$Y.find(s=>s.value===e)),n(s||r.$Y[0])},[e]),a.jsx(i.Combobox,{options:r.$Y,placeholder:"Country",value:t,onChange:s})},o=({value:e="Pacific/Auckland",countryCode:s="ALL",onChange:t,disabled:n=!1})=>{let[o,c]=(0,l.useState)([]),[d,u]=(0,l.useState)(null);return(0,l.useEffect)(()=>{let t=(r.wX[s]||[]).map(e=>({value:e,label:e}));c(t);let a=t.find(e=>"Pacific/Auckland"===e.value);e&&(a=t.find(s=>s.value===e)),u(a||t[0])},[e,s]),a.jsx(i.Combobox,{options:o,placeholder:"Time Zone",value:d,onChange:t,isDisabled:n})};var c=t(76342),d=t(94060),u=t(72548),m=t(79418),p=t(66263),x=t(69424),h=t(28147),g=t(24894),f=t(43926),j=t(8087),v=t(81311),b=t(46776),N=t(26100),C=t(75546),y=t(71890),w=t(60797),S=t(8750),P=t(39544),T=t(36895),q=t(51742),k=t(78965),L=t(25394);let I=()=>{let[e,s]=(0,l.useState)(-1),[t,i]=(0,l.useState)(""),[r,I]=(0,l.useState)(null),[F,Z]=(0,l.useState)(""),[A,D]=(0,l.useState)(!1),[_,E]=(0,l.useState)(!1),[M,B]=(0,l.useState)(!1),[V,U]=(0,l.useState)(null),[R,z]=(0,l.useState)([]),[O,G]=(0,l.useState)(!1),[$,Y]=(0,l.useState)(!1),[X,H]=(0,l.useState)([]),Q=(0,x.useRouter)();(0,l.useEffect)(()=>{s((0,b.GJ)())},[]);let J=["Notifications for New Log Entries","Notifications for New Trip Reports","Notifications for New Trip Events","Notifications for New Field Comments","Notifications for New Tasks","Notifications for New SMU Meter Readings"].map((e,s)=>({id:s,title:e})),K=(0,q.wu)([{accessorKey:"title",header:"Title",cellAlignment:"left",cell:({row:e})=>a.jsx(p.default,{href:"/company-details",className:"text-medium hover:underline",children:e.getValue("title")})}]),[W,ee]=(0,l.useState)("detail"),[es,et]=(0,l.useState)({}),[ea,{loading:el}]=(0,u.D)(c.a4M,{onCompleted:e=>{e.updateClient,localStorage.setItem("clientTitle",es.title),localStorage.setItem("clientId",es.id)},onError:e=>{console.error("updateClient error",e)}}),[ei,{loading:er}]=(0,u.D)(c._rG,{onCompleted:e=>{e.updateAddress},onError:e=>{console.error("updateAddress error",e)}}),en=async e=>{if("detail"===e){let e={...es,documents:es.documents.nodes.map(e=>e.id).join(",")};delete e.__typename,delete e.hqAddress,await ea({variables:{input:e}});let s={...es.hqAddress};s&&(s.__typename&&delete s.__typename,await ei({variables:{input:s}}))}if("setup"===e){let e=document.querySelector("input[name=maritimeTrafficFleetEmail]").value,s=document.querySelector("input[name=masterTerm]").value;await ea({variables:{input:{id:es.id,maritimeTrafficFleetEmail:e||es.maritimeTrafficFleetEmail,masterTerm:s||es.masterTerm,useDepartment:A,useTripSchedule:_,usePilotTransfer:M}}})}"document"===e&&await ea({variables:{input:{id:es.id,documents:X.map(e=>e.id).join(",")}}})},[eo,{loading:ec}]=(0,m.t)(d.SL,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readOneClient;s&&(et(s),eh({variables:{id:[s.logoID]}}),ej({variables:{id:[s.iconLogoID]}}),H(s.documents.nodes),D(s.useDepartment),E(s.useTripSchedule),B(s.usePilotTransfer),localStorage.setItem("timezone",s.hqAddress.timeZone||"Pacific/Auckland"))},onError:e=>{console.error("readOneClient error",e)}}),ed=async()=>{await eo({variables:{filter:{id:{eq:+(localStorage.getItem("clientId")??0)}}}})},eu=e=>{let{name:s,value:t}=e.target;et({...es,[s]:t})},em=e=>{let{name:s,value:t}=e.target;et({...es,hqAddress:{...es.hqAddress,[s]:t}})};async function ep(e){let s=new FormData;s.append("FileData",e,e.name.replace(/\s/g,""));try{let e=await fetch("https://api.sealogs.com/api/v2/upload",{method:"POST",headers:{Authorization:"Bearer "+localStorage.getItem("sl-jwt")},body:s}),t=await e.json();i(t[0].location),I(t[0].id),et({...es,logoID:JSON.stringify(t[0].id)}),G(!1)}catch(e){console.error(e),G(!1)}}async function ex(e){let s=new FormData;s.append("FileData",e,e.name.replace(/\s/g,""));try{let e=await fetch("https://api.sealogs.com/api/v2/upload",{method:"POST",headers:{Authorization:"Bearer "+localStorage.getItem("sl-jwt")},body:s}),t=await e.json();Z(t[0].location),U(t[0].id),et({...es,iconLogoID:t[0].id}),Y(!1)}catch(e){console.error(e),Y(!1)}}let[eh,{data:eg,loading:ef}]=(0,m.t)(d.ZX,{fetchPolicy:"cache-and-network",onCompleted:e=>{i("https://api.sealogs.com/assets/"+e.readFiles.nodes[0].fileFilename)},onError:e=>{console.error(e)}}),[ej]=(0,m.t)(d.ZX,{fetchPolicy:"cache-and-network",onCompleted:e=>{Z("https://api.sealogs.com/assets/"+e.readFiles.nodes[0].fileFilename)},onError:e=>{console.error(e)}}),ev=async e=>{let s=X.filter(s=>s.id!==e);H(s),await ea({variables:{input:{id:es.id,documents:s.map(e=>e.id).join(",")}}})},eb=e=>{localStorage.setItem("useDepartment",e.toString()),D(e)},eN=e=>{localStorage.setItem("useTripSchedule",e.toString()),E(e)},eC=e=>{localStorage.setItem("usePilotTransfer",e.toString()),B(e)};return(0,l.useEffect)(()=>{ed()},[]),a.jsx(a.Fragment,{children:!1===e?a.jsx(N.Z,{errorMessage:"Oops You do not have the permission to view this section."}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(L.Zb,{className:"mb-5",children:[(0,a.jsxs)("div",{className:"flex mb-5 items-center",children:[a.jsx(L.H4,{children:"Company"}),a.jsx(L.P,{children:es?.title?": "+es?.title:""})]}),(0,a.jsxs)(T.Tabs,{defaultValue:"detail",value:W,onValueChange:ee,children:[(0,a.jsxs)(T.TabsList,{children:[a.jsx(T.TabsTrigger,{value:"detail",children:"Details"}),a.jsx(T.TabsTrigger,{value:"subscription",children:"Subscription"}),a.jsx(T.TabsTrigger,{value:"setup",children:"SeaLogs Setup"}),a.jsx(T.TabsTrigger,{value:"notification",children:"Notifications"}),a.jsx(T.TabsTrigger,{value:"document",children:"Company-Wide Documents"})]}),a.jsx(T.TabsContent,{value:"detail",children:(0,a.jsxs)(a.Fragment,{children:[a.jsx(L.Z0,{className:"my-4"}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 pb-4 pt-3 px-4 mb-2",children:[(0,a.jsxs)("div",{children:[a.jsx(L.H4,{children:"Company Details"}),a.jsx(L.P,{children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Facilis possimus harum eaque itaque est id reprehenderit excepturi eius temporibus, illo officia amet nobis sapiente dolorem ipsa earum adipisci recusandae cumque."})]}),(0,a.jsxs)("div",{className:"col-span-2 block pb-3 px-7 rounded-lg ",children:[a.jsx("div",{className:"mb-4 text-center"}),(0,a.jsxs)("div",{className:"flex gap-4",children:[a.jsx("div",{className:"flex grow flex-col mb-4",children:a.jsx("div",{className:"flex items-center",children:a.jsx(y.I,{name:"title",placeholder:"Title",type:"text",required:!0,defaultValue:es?.title||"",onChange:eu})})}),a.jsx("div",{className:"flex grow flex-col mb-4",children:a.jsx("div",{className:"flex items-center",children:a.jsx(y.I,{name:"phone",placeholder:"Phone Number",type:"tel",defaultValue:es?.phone||"",onChange:eu})})})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[a.jsx("div",{className:"flex grow flex-col mb-4",children:a.jsx("div",{className:"flex items-center",children:a.jsx(y.I,{name:"adminEmail",type:"email",placeholder:"Admin Email",defaultValue:es?.adminEmail||"",onChange:eu})})}),a.jsx("div",{className:"flex grow flex-col mb-4",children:a.jsx("div",{className:"flex items-center",children:a.jsx(y.I,{name:"accountsEmail",type:"email",placeholder:"Accounts Email",defaultValue:es?.accountsEmail||"",onChange:eu})})})]}),a.jsx(w.Label,{children:"Business Address"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[a.jsx("div",{className:"flex items-center",children:a.jsx(y.I,{name:"streetNumber",placeholder:"Street Number",type:"text",required:!0,defaultValue:es?.hqAddress?.streetNumber||"",onChange:em})}),a.jsx("div",{className:"flex items-center",children:a.jsx(y.I,{name:"street",placeholder:"Street",type:"text",defaultValue:es?.hqAddress?.street||"",onChange:em})}),a.jsx("div",{className:"flex items-center",children:a.jsx(y.I,{name:"locality",placeholder:"Suburb",type:"text",required:!0,defaultValue:es?.hqAddress?.locality||"",onChange:em})}),a.jsx("div",{className:"flex items-center",children:a.jsx(y.I,{name:"administrative1",placeholder:"City",type:"text",defaultValue:es?.hqAddress?.administrative1||"",onChange:em})}),a.jsx("div",{className:"flex items-center",children:a.jsx(y.I,{name:"postalCode",placeholder:"Post Code",type:"text",required:!0,defaultValue:es?.hqAddress?.postalCode||"",onChange:em})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[a.jsx("div",{className:"flex items-center",children:a.jsx(n,{value:es?.hqAddress?.country,onChange:e=>{e&&et({...es,hqAddress:{...es.hqAddress,country:e.value}})}})}),a.jsx("div",{className:"flex items-center",children:a.jsx(o,{value:es?.hqAddress?.timeZone,countryCode:es?.hqAddress?.country,onChange:e=>{e&&(et({...es,hqAddress:{...es.hqAddress,timeZone:e.value}}),localStorage.setItem("timezone",e.value))}})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[a.jsx("div",{className:"flex gap-4",children:(0,a.jsxs)("div",{className:"col-span-full",children:[a.jsx(w.Label,{htmlFor:"vesselBanner",className:"  block",children:"Company Logo"}),(0,a.jsxs)("div",{className:"flex items-center gap-x-3 h-16",children:[a.jsx("div",{className:"w-32",role:"status",children:O?(0,a.jsxs)("div",{className:"cst_loader flex justify-center",role:"status",children:[(0,a.jsxs)("svg",{"aria-hidden":"true",className:"w-6 h-6  animate-spin",viewBox:"0 0 100 101",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[a.jsx("path",{d:"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z",fill:"currentColor"}),a.jsx("path",{d:"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z",fill:"currentFill"})]}),a.jsx("span",{className:"sr-only",children:"Loading..."})]}):a.jsx("div",{className:"w-18 h-12 border rounded-[4px]  p-1  flex align-middle",children:a.jsx(h.default,{alt:"Company logo",width:100,height:100,src:t||"/sealogs-horizontal-logo.png"})})}),(0,a.jsxs)(w.Label,{className:"w-full inline-flex justify-center items-center rounded-md   p-1 md:px-5  shadow-sm ring-1 ring-inset     hover: cursor-pointer",htmlFor:"fileUpload",children:[a.jsx(y.I,{type:"file",id:"fileUpload",className:"hidden",onChange:e=>(function(e){if(G(!0),e.preventDefault(),e.target.files&&e.target.files[0])for(let s=0;s<e.target.files.length;s++)z(t=>[...t,e.target.files[s]]),ep(e.target.files[s])})(e)}),"Upload"]})]})]})}),a.jsx("div",{className:"flex gap-4",children:(0,a.jsxs)("div",{className:"col-span-full",children:[a.jsx(w.Label,{className:"  block",children:"Icon Logo"}),(0,a.jsxs)("div",{className:"flex items-center gap-x-3 h-16",children:[a.jsx("div",{className:"w-32",role:"status",children:$?(0,a.jsxs)("div",{className:"cst_loader flex justify-center",role:"status",children:[(0,a.jsxs)("svg",{"aria-hidden":"true",className:"w-6 h-6  animate-spin   ",viewBox:"0 0 100 101",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[a.jsx("path",{d:"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z",fill:"currentColor"}),a.jsx("path",{d:"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z",fill:"currentFill"})]}),a.jsx("span",{className:"sr-only",children:"Loading..."})]}):a.jsx("div",{className:"w-18 h-12 border rounded-[4px]  p-1  flex align-middle",children:a.jsx(h.default,{alt:"Icon logo",width:100,height:100,src:F||"/sealogs-horizontal-logo.png"})})}),(0,a.jsxs)(w.Label,{className:"w-full inline-flex justify-center items-center rounded-md   p-1 md:px-5  shadow-sm ring-1 ring-inset hover: cursor-pointer",htmlFor:"iconLogoUpload",children:[a.jsx(y.I,{type:"file",id:"iconLogoUpload",className:"hidden",onChange:e=>(function(e){if(Y(!0),e.preventDefault(),e.target.files&&e.target.files[0])for(let s=0;s<e.target.files.length;s++)z(t=>[...t,e.target.files[s]]),ex(e.target.files[s])})(e)}),"Upload"]})]}),a.jsx("div",{children:a.jsx("small",{children:"The recommended size is 150px 150px"})})]})})]})]})]})]})}),a.jsx(T.TabsContent,{value:"subscription",children:a.jsx("div",{className:"border-t pt-4",children:a.jsx("div",{className:"flex justify-center items-center h-96",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[a.jsx("div",{className:"text-3xl",children:"Subscription"}),a.jsx("div",{className:"text-2xl",children:"No data available"})]})})})}),a.jsx(T.TabsContent,{value:"setup",children:a.jsx(a.Fragment,{children:a.jsx("div",{className:"border-t pt-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-6 pb-4 pt-3 px-4",children:[(0,a.jsxs)("div",{className:"my-4 text-l",children:["SeaLogs Setup",a.jsx("p",{className:" mt-4 max-w-[25rem] leading-loose ",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Facilis possimus harum eaque itaque est id reprehenderit excepturi eius temporibus, illo officia amet nobis sapiente dolorem ipsa earum adipisci recusandae cumque."})]}),(0,a.jsxs)("div",{className:"col-span-2 block pt-3 pb-3 px-7   rounded-lg",children:[(0,a.jsxs)("div",{className:"flex gap-4",children:[a.jsx("div",{className:"flex grow flex-col mb-4",children:a.jsx("div",{className:"flex items-center",children:a.jsx(y.I,{name:"maritimeTrafficFleetEmail",placeholder:"Maritime Fleet Email",type:"email",required:!0,defaultValue:es?.maritimeTrafficFleetEmail??""})})}),a.jsx("div",{className:"flex grow flex-col mb-4",children:a.jsx("div",{className:"flex items-center",children:a.jsx(y.I,{name:"masterTerm",placeholder:"Master/captain preferred field name",type:"text",defaultValue:es?.masterTerm??""})})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(w.Label,{label:"Use departments",htmlFor:"client-use-department",size:"lg",leftContent:a.jsx(S.Checkbox,{id:"client-use-department",checked:A,isRadioStyle:!0,onCheckedChange:e=>{eb(e)},size:"lg",variant:"default"})}),a.jsx(w.Label,{label:"Use trip schedules",htmlFor:"client-use-trip-schedule",size:"lg",leftContent:a.jsx(S.Checkbox,{id:"client-use-trip-schedule",checked:_,isRadioStyle:!0,onCheckedChange:e=>{eN(e)},size:"lg",variant:"default"})}),a.jsx(w.Label,{label:"Use pilot transfer",htmlFor:"client-use-pilot-transfer",size:"lg",leftContent:a.jsx(S.Checkbox,{id:"client-use-pilot-transfer",checked:M,isRadioStyle:!0,onCheckedChange:e=>{eC(e)},size:"lg",variant:"default"})})]})]})]})})})}),a.jsx(T.TabsContent,{value:"notification",children:a.jsx("div",{className:"border-t pt-4",children:a.jsx(q.wQ,{columns:K,data:J,showToolbar:!1,pageSize:10,showPageSizeSelector:!1})})}),a.jsx(T.TabsContent,{value:"document",children:(0,a.jsxs)("div",{className:"grid-cols-2 gap-6 pb-4 pt-3 px-4 sm:grid hidden",children:[(0,a.jsxs)("div",{className:"flex flex-col",children:[a.jsx(g.Z,{setDocuments:H,text:"",subText:"Drag files here or upload",documents:X}),a.jsx("span",{className:"mt-3  mx-auto block",children:"\"To securely save your files, click the 'Save' button below.\""})]}),a.jsx("div",{className:"block pb-3",children:a.jsx("div",{className:"px-4 pb-4",children:X.length>0?a.jsx("div",{"aria-label":"Documents",className:"space-y-4",children:X.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-8 justify-between p-2.5 rounded-lg border mb-4 hover:bg-muted/50 transition-colors",children:[a.jsx(f.Z,{document:e}),(0,a.jsxs)(P.Button,{variant:"ghost",className:"flex gap-2 items-center",onClick:()=>ev(e.id),children:[a.jsx("span",{className:"ml-4",children:(0,C.p6)(e.created)}),a.jsx(j.Z,{className:"w-5 h-5 text-red-500 cursor-pointer"})]})]},e.id))}):a.jsx("div",{className:"flex justify-center items-center h-40",children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[a.jsx("div",{className:"text-3xl   ",children:"Documents"}),a.jsx("div",{className:"text-2xl   ",children:"No documents available"})]})})})})]})})]})]}),(0,a.jsxs)(k.V,{children:[a.jsx(P.Button,{variant:"back",onClick:()=>Q.back(),children:"Cancel"}),("detail"===W||"setup"===W||"document"===W)&&a.jsx(P.Button,{variant:"primary",iconLeft:v.Z,onClick:()=>en(W),disabled:el,isLoading:el,children:el?"Saving...":"Save"})]})]})})},F=()=>((0,l.useEffect)(()=>{(0,b.UU)()},[]),a.jsx(I,{}))},10901:(e,s,t)=>{"use strict";t.d(s,{k:()=>m});var a=t(98768),l=t(11652),i=t(41641),r=t(39303),n=t(40712),o=t(39544),c=t(24224),d=t(25394),u=t(50058);function m({table:e,pageSizeOptions:s=[10,20,30,40,50],showPageSizeSelector:t=!0}){let m=(0,u.k)();return a.jsx("div",{className:"flex items-center justify-center px-2",children:(0,a.jsxs)("div",{className:"flex gap-2 items-end phablet:items-center phablet:space-x-6 lg:space-x-8",children:[t&&a.jsx("div",{className:"flex items-center space-x-2",children:a.jsx(d.__,{label:"Rows per page",position:m.phablet?"left":"top",htmlFor:"rows-per-page",className:"text-sm",children:(0,a.jsxs)(c.Select,{value:`${e.getState().pagination.pageSize}`,onValueChange:s=>{e.setPageSize(Number(s))},children:[a.jsx(c.SelectTrigger,{className:"h-8 w-[70px]",children:a.jsx(c.SelectValue,{placeholder:e.getState().pagination.pageSize})}),a.jsx(c.SelectContent,{side:"top",children:s.map(e=>a.jsx(c.SelectItem,{value:`${e}`,children:e},e))})]})})}),(0,a.jsxs)("div",{className:"flex w-fit items-center justify-center text-sm",children:["Page ",e.getState().pagination.pageIndex+1," of"," ",e.getPageCount()]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(o.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>e.setPageIndex(0),disabled:!e.getCanPreviousPage(),children:[a.jsx("span",{className:"sr-only",children:"Go to first page"}),a.jsx(l.Z,{})]}),(0,a.jsxs)(o.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>e.previousPage(),disabled:!e.getCanPreviousPage(),children:[a.jsx("span",{className:"sr-only",children:"Go to previous page"}),a.jsx(i.Z,{})]}),(0,a.jsxs)(o.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>e.nextPage(),disabled:!e.getCanNextPage(),children:[a.jsx("span",{className:"sr-only",children:"Go to next page"}),a.jsx(r.Z,{})]}),(0,a.jsxs)(o.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>e.setPageIndex(e.getPageCount()-1),disabled:!e.getCanNextPage(),children:[a.jsx("span",{className:"sr-only",children:"Go to last page"}),a.jsx(n.Z,{})]})]})]})})}},6958:(e,s,t)=>{"use strict";t.d(s,{n:()=>i});var a=t(98768),l=t(37042);function i({table:e,onChange:s}){return a.jsx(l.Z,{onChange:s,table:e})}},43926:(e,s,t)=>{"use strict";t.d(s,{Z:()=>f});var a=t(98768),l=t(66263),i=t(60343),r=t(28147),n=t(12513),o=t(88557),c=t(27780),d=t(50526),u=t(7179);t(18937),t(73304);var m=t(39544),p=t(46877),x=t(13609),h=t(47634),g=t(34376);let f=({document:e,hideTitle:s=!1,showDeleteButton:t=!1,onDelete:f,canDelete:j=!0,deleteErrorMessage:v="You do not have permission to delete this document"})=>{let[b,N]=(0,i.useState)(!1),[C,y]=(0,i.useState)(!1),{toast:w}=(0,g.pm)(),S=()=>{if(!j){w({description:v,variant:"destructive"});return}f&&f(e.id)},P=()=>{let s=process.env.NEXT_PUBLIC_FILE_BASE_URL||"https://api.sealogs.com/assets/";return`${s}${e.fileFilename}`};return["jpg","jpeg","png","gif","webp","svg"].includes(e.fileFilename?.split(".").pop()?.toLowerCase()||"")&&!C?(0,a.jsxs)("div",{className:"group relative flex items-center gap-3",children:[a.jsx(m.Button,{variant:"ghost",onClick:()=>N(!0),className:"h-auto p-2 hover:bg-muted/50 transition-colors flex-1","aria-label":`View image: ${e.title}`,children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"relative overflow-hidden rounded-lg border border-border bg-muted/20",children:[a.jsx(r.default,{src:P()||"/placeholder.svg",alt:e.title,width:64,height:64,className:"h-16 w-16 object-cover transition-transform group-hover:scale-105",onError:()=>y(!0)}),a.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors flex items-center justify-center",children:a.jsx(p.Z,{className:"h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity"})})]}),!s&&(0,a.jsxs)("div",{className:"flex-1 text-left",children:[a.jsx("p",{className:"font-medium text-sm truncate max-w-[200px]",children:e.title}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Image •"," ",e.fileFilename?.split(".").pop()?.toUpperCase()]})]})]})}),t&&a.jsx(m.Button,{variant:"destructive",iconLeft:x.Z,iconOnly:!0,onClick:S,"aria-label":`Delete ${e.title}`}),a.jsx(n.ZP,{open:b,close:()=>N(!1),slides:[{src:P(),alt:e.title,description:e.title}],render:{buttonPrev:()=>null,buttonNext:()=>null},controller:{closeOnPullUp:!0,closeOnPullDown:!0,closeOnBackdropClick:!0},plugins:[o.Z,u.Z,d.Z,c.Z]})]}):(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(l.default,{href:P(),target:"_blank",rel:"noopener noreferrer",className:"group block flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"h-16 w-16 rounded-lg border border-border border-dashed bg-muted/20 flex items-center justify-center group-hover:bg-muted/30 transition-colors",children:C?a.jsx(p.Z,{className:"h-6 w-6 text-muted-foreground"}):a.jsx(r.default,{src:{pdf:"/file-types/pdf.svg",xls:"/file-types/xls.svg",xlsx:"/file-types/xls.svg",ppt:"/file-types/ppt.svg",pptx:"/file-types/ppt.svg",txt:"/file-types/txt.svg",csv:"/file-types/csv.svg"}[e.fileFilename?.split(".").pop()?.toLowerCase()||""]||"/file-types/doc.svg",alt:`${e.fileFilename?.split(".").pop()?.toUpperCase()} file`,width:24,height:24,className:"h-6 w-6"})}),a.jsx("div",{className:"absolute -top-1 -right-1 bg-background border border-border rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity",children:a.jsx(h.Z,{className:"h-3 w-3 text-muted-foreground"})})]}),!s&&(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"font-medium text-sm truncate",children:e.title}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Document •"," ",e.fileFilename?.split(".").pop()?.toUpperCase()]})]})]})}),t&&a.jsx(m.Button,{variant:"destructive",iconLeft:x.Z,iconOnly:!0,onClick:S,"aria-label":`Delete ${e.title}`})]})}},51742:(e,s,t)=>{"use strict";t.d(s,{QT:()=>j,wQ:()=>f,wu:()=>p});var a=t(98768),l=t(26659),i=t(6958),r=t(10901),n=t(50058),o=t(60343),c=t(24109),d=t(23416),u=t(35024),m=t(56937);function p(e){return e}let x=e=>{switch(e){case"left":return"items-left justify-start justify-items-start text-left";case"right":return"items-right justify-end justify-items-end text-right";default:return"items-center justify-center justify-items-center text-center"}},h=e=>{switch(e){case"overdue":case"upcoming":return"rounded-md [&>td:first-child]:rounded-l-lg [&>td:last-child]:rounded-r-lg";default:return""}},g=e=>{switch(e){case"overdue":return"destructive";case"upcoming":return"warning";default:return}};function f({columns:e,data:s,showToolbar:t=!0,className:p,pageSize:f=10,pageSizeOptions:j=[10,20,30,40,50],showPageSizeSelector:v=!0,onChange:b,rowStatus:N}){let[C,y]=o.useState([]),[w,S]=o.useState([]),[P,T]=o.useState({pageIndex:0,pageSize:f}),q=(0,n.k)(),k=o.useMemo(()=>e.filter(e=>e.showOnlyBelow?!q[e.showOnlyBelow]:!e.breakpoint||q[e.breakpoint]),[e,q]);o.useEffect(()=>{T(e=>({...e,pageSize:f}))},[f]);let L=(0,c.b7)({data:s,columns:k,onSortingChange:y,getCoreRowModel:(0,d.sC)(),getPaginationRowModel:(0,d.G_)(),getSortedRowModel:(0,d.tj)(),onColumnFiltersChange:S,getFilteredRowModel:(0,d.vL)(),onPaginationChange:T,state:{sorting:C,columnFilters:w,pagination:P}});return(0,a.jsxs)("div",{className:"space-y-4 pb-8",children:[t&&a.jsx(u.Zb,{className:"p-2 md:p-auto",children:a.jsx(i.n,{table:L,onChange:b})}),(0,a.jsxs)(l.iA,{className:p||"p-0 phablet:p-8 lg:p-6 xl:p-8 shadow-none border-0 phablet:border border-border bg-card rounded-lg",children:[L.getHeaderGroups().some(e=>e.headers.some(e=>e.column.columnDef.header&&""!==e.column.columnDef.header))&&a.jsx(l.xD,{children:L.getHeaderGroups().map(e=>a.jsx(l.SC,{children:e.headers.map(e=>{let s=e.column.columnDef,t="title"===e.column.id?"left":s.cellAlignment||"center";return a.jsx(l.ss,{className:"title"===e.column.id?"items-left justify-items-start text-left":x(t),children:e.isPlaceholder?null:(0,c.ie)(e.column.columnDef.header,e.getContext())},e.id)})},e.id))}),a.jsx(l.RM,{children:L.getRowModel().rows.length?L.getRowModel().rows.map(e=>{let s=N?N(e.original):"normal",t=h(s);return a.jsx(l.SC,{"data-state":e.getIsSelected()?"selected":void 0,className:(0,m.cn)("mb-4",t),children:e.getVisibleCells().map(e=>{let t=e.column.columnDef,i="title"===e.column.id?"left":t.cellAlignment||"center";return a.jsx(l.pj,{statusOverlay:"normal"!==s,statusOverlayColor:g(s),className:(0,m.cn)("","title"===e.column.id?`${k.length>1?"w-auto":"w-full"} items-left justify-items-start text-left`:x(i),t.cellClassName),children:a.jsx("div",{className:(0,m.cn)("flex px-1.5 xs:px-2.5 flex-1",x(i)),children:(0,c.ie)(e.column.columnDef.cell,e.getContext())})},e.id)})},String(e.id))}):a.jsx(l.SC,{children:a.jsx(l.pj,{colSpan:k.length,className:"h-24 text-center",children:"No results."})})})]}),(L.getCanPreviousPage()||L.getCanNextPage())&&a.jsx("div",{className:"flex items-center justify-center phablet:justify-end space-x-2 py-4",children:a.jsx(r.k,{table:L,pageSizeOptions:j,showPageSizeSelector:v})})]})}let j=f},22207:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\company-details\layout.tsx#default`)},96613:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\company-details\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,5959,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,6014],()=>t(37291));module.exports=a})();