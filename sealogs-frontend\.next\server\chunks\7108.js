exports.id=7108,exports.ids=[7108],exports.modules={5554:e=>{e.exports=function(e){return function(t){return null==e?void 0:e[t]}}},97038:(e,t,n)=>{var r=n(5554)({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"});e.exports=r},43777:(e,t,n)=>{var r=n(16266),i=n(97038),s=/&(?:amp|lt|gt|quot|#39);/g,a=RegExp(s.source);e.exports=function(e){return(e=r(e))&&a.test(e)?e.replace(s,i):e}},37543:(e,t,n)=>{"use strict";n.d(t,{f:()=>et,rx:()=>u});var r=n(75867),i=n(95284);n(46168);let s=[],a={code:"en",week:{dow:0,doy:4},direction:"ltr",buttonText:{prev:"prev",next:"next",prevYear:"prev year",nextYear:"next year",year:"year",today:"today",month:"month",week:"week",day:"day",list:"list"},weekText:"W",weekTextLong:"Week",closeHint:"Close",timeHint:"Time",eventHint:"Event",allDayText:"all-day",moreLinkText:"more",noEventsText:"No events to display"},o=Object.assign(Object.assign({},a),{buttonHints:{prev:"Previous $0",next:"Next $0",today:(e,t)=>"day"===t?"Today":`This ${e}`},viewHint:"$0 view",navLinkHint:"Go to $0",moreLinkHint:e=>`Show ${e} more event${1===e?"":"s"}`});function l(e){let t=e.length>0?e[0].code:"en",n=s.concat(e),r={en:o};for(let e of n)r[e.code]=e;return{map:r,defaultCode:t}}function c(e,t){let n,r;return"object"!=typeof e||Array.isArray(e)?(r=function(e,t){for(let n=0;n<e.length;n+=1){let r=e[n].toLocaleLowerCase().split("-");for(let e=r.length;e>0;e-=1){let n=r.slice(0,e).join("-");if(t[n])return t[n]}}return null}(n=[].concat(e||[]),t)||o,d(e,n,r)):d(e.code,[e.code],e)}function d(e,t,n){let i=(0,r.m)([a,n],["buttonText"]);delete i.code;let{week:s}=i;return delete i.week,{codeArg:e,codes:t,week:s,simpleNumberFormat:new Intl.NumberFormat(e),options:i}}function u(e){return{id:(0,r.g)(),name:e.name,premiumReleaseDate:e.premiumReleaseDate?new Date(e.premiumReleaseDate):void 0,deps:e.deps||[],reducers:e.reducers||[],isLoadingFuncs:e.isLoadingFuncs||[],contextInit:[].concat(e.contextInit||[]),eventRefiners:e.eventRefiners||{},eventDefMemberAdders:e.eventDefMemberAdders||[],eventSourceRefiners:e.eventSourceRefiners||{},isDraggableTransformers:e.isDraggableTransformers||[],eventDragMutationMassagers:e.eventDragMutationMassagers||[],eventDefMutationAppliers:e.eventDefMutationAppliers||[],dateSelectionTransformers:e.dateSelectionTransformers||[],datePointTransforms:e.datePointTransforms||[],dateSpanTransforms:e.dateSpanTransforms||[],views:e.views||{},viewPropsTransformers:e.viewPropsTransformers||[],isPropsValid:e.isPropsValid||null,externalDefTransforms:e.externalDefTransforms||[],viewContainerAppends:e.viewContainerAppends||[],eventDropTransformers:e.eventDropTransformers||[],componentInteractions:e.componentInteractions||[],calendarInteractions:e.calendarInteractions||[],themeClasses:e.themeClasses||{},eventSourceDefs:e.eventSourceDefs||[],cmdFormatter:e.cmdFormatter,recurringTypes:e.recurringTypes||[],namedTimeZonedImpl:e.namedTimeZonedImpl,initialView:e.initialView||"",elementDraggingImpl:e.elementDraggingImpl,optionChangeHandlers:e.optionChangeHandlers||{},scrollGridImpl:e.scrollGridImpl||null,listenerRefiners:e.listenerRefiners||{},optionRefiners:e.optionRefiners||{},propSetHandlers:e.propSetHandlers||{}}}class f extends r.T{}function h(e,t,n,r){if(t[e])return t[e];let i=function(e,t,n,r){let i=n[e],s=r[e],a=e=>i&&null!==i[e]?i[e]:s&&null!==s[e]?s[e]:null,o=a("component"),l=a("superType"),c=null;if(l){if(l===e)throw Error("Can't have a custom view type that references itself");c=h(l,t,n,r)}return(!o&&c&&(o=c.component),o)?{type:e,component:o,defaults:Object.assign(Object.assign({},c?c.defaults:{}),i?i.rawOptions:{}),overrides:Object.assign(Object.assign({},c?c.overrides:{}),s?s.rawOptions:{})}:null}(e,t,n,r);return i&&(t[e]=i),i}function p(e){let t="function"==typeof e?{component:e}:e,{component:n}=t;return t.content?n=g(t):!n||n.prototype instanceof r.B||(n=g(Object.assign(Object.assign({},t),{content:n}))),{superType:t.type,component:n,rawOptions:t}}function g(e){return t=>(0,i.az)(r.V.Consumer,null,n=>(0,i.az)(r.C,{elTag:"div",elClasses:(0,r.b)(n.viewSpec),renderProps:Object.assign(Object.assign({},t),{nextDayThreshold:n.options.nextDayThreshold}),generatorName:void 0,customGenerator:e.content,classNameGenerator:e.classNames,didMount:e.didMount,willUnmount:e.willUnmount}))}function m(e,t,n,i){var s;let a=(0,r.a)(e,p),o=(s=t.views,(0,r.a)(s,p)),l=function(e,t){let n,r={};for(n in e)h(n,r,e,t);for(n in t)h(n,r,e,t);return r}(a,o);return(0,r.a)(l,e=>(function(e,t,n,i,s){let a,o,l=e.overrides.duration||e.defaults.duration||i.duration||n.duration,c=null,d="",u="",f={};if(l&&(void 0===(o=v[a=JSON.stringify(l)])&&(o=(0,r.d)(l),v[a]=o),c=o)){let e=(0,r.c)(c);d=e.unit,1===e.value&&(u=d,f=t[d]?t[d].rawOptions:{})}let h=t=>{let n=t.buttonText||{},r=e.defaults.buttonTextKey;return null!=r&&null!=n[r]?n[r]:null!=n[e.type]?n[e.type]:null!=n[u]?n[u]:null},p=t=>{let n=t.buttonHints||{},r=e.defaults.buttonTextKey;return null!=r&&null!=n[r]?n[r]:null!=n[e.type]?n[e.type]:null!=n[u]?n[u]:null};return{type:e.type,component:e.component,duration:c,durationUnit:d,singleUnit:u,optionDefaults:e.defaults,optionOverrides:Object.assign(Object.assign({},f),e.overrides),buttonTextOverride:h(i)||h(n)||e.overrides.buttonText,buttonTextDefault:h(s)||e.defaults.buttonText||h(r.e)||e.type,buttonTitleOverride:p(i)||p(n)||e.overrides.buttonHint,buttonTitleDefault:p(s)||e.defaults.buttonHint||p(r.e)}})(e,o,t,n,i))}f.prototype.classes={root:"fc-theme-standard",tableCellShaded:"fc-cell-shaded",buttonGroup:"fc-button-group",button:"fc-button fc-button-primary",buttonActive:"fc-button-active"},f.prototype.baseIconClass="fc-icon",f.prototype.iconClasses={close:"fc-icon-x",prev:"fc-icon-chevron-left",next:"fc-icon-chevron-right",prevYear:"fc-icon-chevrons-left",nextYear:"fc-icon-chevrons-right"},f.prototype.rtlIconClasses={prev:"fc-icon-chevron-right",next:"fc-icon-chevron-left",prevYear:"fc-icon-chevrons-right",nextYear:"fc-icon-chevrons-left"},f.prototype.iconOverrideOption="buttonIcons",f.prototype.iconOverrideCustomButtonOption="icon",f.prototype.iconOverridePrefix="fc-icon-";let v={};function b(e){for(let t in e)if(e[t].isFetching)return!0;return!1}function y(e,t,n,r){let i={};for(let e of t)i[e.sourceId]=e;return n&&(i=A(i,n,r)),Object.assign(Object.assign({},e),i)}function A(e,t,n){return _(e,(0,r.h)(e,e=>D(e,n)?!n.options.lazyFetching||!e.fetchRange||e.isFetching||t.start<e.fetchRange.start||t.end>e.fetchRange.end:!e.latestFetchId),t,!1,n)}function _(e,t,n,i,s){let a={};for(let o in e){let l=e[o];t[o]?a[o]=function(e,t,n,i){let{options:s,calendarApi:a}=i,o=i.pluginHooks.eventSourceDefs[e.sourceDefId],l=(0,r.g)();return o.fetch({eventSource:e,range:t,isRefetch:n,context:i},n=>{let{rawEvents:r}=n;s.eventSourceSuccess&&(r=s.eventSourceSuccess.call(a,r,n.response)||r),e.success&&(r=e.success.call(a,r,n.response)||r),i.dispatch({type:"RECEIVE_EVENTS",sourceId:e.sourceId,fetchId:l,fetchRange:t,rawEvents:r})},n=>{let r=!1;s.eventSourceFailure&&(s.eventSourceFailure.call(a,n),r=!0),e.failure&&(e.failure(n),r=!0),r||console.warn(n.message,n),i.dispatch({type:"RECEIVE_EVENT_ERROR",sourceId:e.sourceId,fetchId:l,fetchRange:t,error:n})}),Object.assign(Object.assign({},e),{isFetching:!0,latestFetchId:l})}(l,n,i,s):a[o]=l}return a}function E(e,t){return(0,r.h)(e,e=>D(e,t))}function D(e,t){return!t.pluginHooks.eventSourceDefs[e.sourceDefId].ignoreRange}function C(e,t,n,r,i){return{header:e.headerToolbar?w(e.headerToolbar,e,t,n,r,i):null,footer:e.footerToolbar?w(e.footerToolbar,e,t,n,r,i):null}}function w(e,t,n,i,s,a){let o={},l=[],c=!1;for(let d in e){let u=function(e,t,n,i,s,a){let o="rtl"===t.direction,l=t.customButtons||{},c=n.buttonText||{},d=t.buttonText||{},u=n.buttonHints||{},f=t.buttonHints||{},h=e?e.split(" "):[],p=[],g=!1;return{widgets:h.map(e=>e.split(",").map(e=>{let n,h,m,v,b,y;if("title"===e)return g=!0,{buttonName:e};if(n=l[e])m=e=>{n.click&&n.click.call(e.target,e,e.target)},(v=i.getCustomButtonIconClass(n))||(v=i.getIconClass(e,o))||(b=n.text),y=n.hint||n.text;else if(h=s[e]){p.push(e),m=()=>{a.changeView(e)},(b=h.buttonTextOverride)||(v=i.getIconClass(e,o))||(b=h.buttonTextDefault);let n=h.buttonTextOverride||h.buttonTextDefault;y=(0,r.k)(h.buttonTitleOverride||h.buttonTitleDefault||t.viewHint,[n,e],n)}else if(a[e]){if(m=()=>{a[e]()},(b=c[e])||(v=i.getIconClass(e,o))||(b=d[e]),"prevYear"===e||"nextYear"===e){let t="prevYear"===e?"prev":"next";y=(0,r.k)(u[t]||f[t],[d.year||"year","year"],d[e])}else y=t=>(0,r.k)(u[e]||f[e],[d[t]||t,t],d[e])}return{buttonName:e,buttonClick:m,buttonIcon:v,buttonText:b,buttonHint:y}})),viewsWithButtons:p,hasTitle:g}}(e[d],t,n,i,s,a);o[d]=u.widgets,l.push(...u.viewsWithButtons),c=c||u.hasTitle}return{sectionWidgets:o,viewsWithButtons:l,hasTitle:c}}class R{constructor(e,t,n){this.type=e,this.getCurrentData=t,this.dateEnv=n}get calendar(){return this.getCurrentData().calendarApi}get title(){return this.getCurrentData().viewTitle}get activeStart(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.start)}get activeEnd(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.activeRange.end)}get currentStart(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.start)}get currentEnd(){return this.dateEnv.toDate(this.getCurrentData().dateProfile.currentRange.end)}getOption(e){return this.getCurrentData().options[e]}}let S=u({name:"array-event-source",eventSourceDefs:[{ignoreRange:!0,parseMeta:e=>Array.isArray(e.events)?e.events:null,fetch(e,t){t({rawEvents:e.eventSource.meta})}}]}),k=u({name:"func-event-source",eventSourceDefs:[{parseMeta:e=>"function"==typeof e.events?e.events:null,fetch(e,t,n){let{dateEnv:i}=e.context,s=e.eventSource.meta;(0,r.u)(s.bind(null,(0,r.l)(e.range,i)),e=>t({rawEvents:e}),n)}}]}),x=u({name:"json-event-source",eventSourceRefiners:{method:String,extraParams:r.n,startParam:String,endParam:String,timeZoneParam:String},eventSourceDefs:[{parseMeta:e=>e.url&&("json"===e.format||!e.format)?{url:e.url,format:"json",method:(e.method||"GET").toUpperCase(),extraParams:e.extraParams,startParam:e.startParam,endParam:e.endParam,timeZoneParam:e.timeZoneParam}:null,fetch(e,t,n){let{meta:i}=e.eventSource,s=function(e,t,n){let r,i,s,{dateEnv:a,options:o}=n,l={};return null==(r=e.startParam)&&(r=o.startParam),null==(i=e.endParam)&&(i=o.endParam),null==(s=e.timeZoneParam)&&(s=o.timeZoneParam),Object.assign(l,"function"==typeof e.extraParams?e.extraParams():e.extraParams||{}),l[r]=a.formatIso(t.start),l[i]=a.formatIso(t.end),"local"!==a.timeZone&&(l[s]=a.timeZone),l}(i,e.range,e.context);(0,r.r)(i.method,i.url,s).then(([e,n])=>{t({rawEvents:e,response:n})},n)}}]}),T={daysOfWeek:r.n,startTime:r.d,endTime:r.d,duration:r.d,startRecur:r.n,endRecur:r.n};function M(e,t){let n=(0,r.v)(t.getCurrentData().eventSources);if(1===n.length&&1===e.length&&Array.isArray(n[0]._raw)&&Array.isArray(e[0])){t.dispatch({type:"RESET_RAW_EVENTS",sourceId:n[0].sourceId,rawEvents:e[0]});return}let i=[];for(let t of e){let e=!1;for(let r=0;r<n.length;r+=1)if(n[r]._raw===t){n.splice(r,1),e=!0;break}e||i.push(t)}for(let e of n)t.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:e.sourceId});for(let e of i)t.calendarApi.addEventSource(e)}let O=[S,k,x,u({name:"simple-recurring-event",recurringTypes:[{parse(e,t){if(e.daysOfWeek||e.startTime||e.endTime||e.startRecur||e.endRecur){let n,i={daysOfWeek:e.daysOfWeek||null,startTime:e.startTime||null,endTime:e.endTime||null,startRecur:e.startRecur?t.createMarker(e.startRecur):null,endRecur:e.endRecur?t.createMarker(e.endRecur):null,dateEnv:t};return e.duration&&(n=e.duration),!n&&e.startTime&&e.endTime&&(n=(0,r.s)(e.endTime,e.startTime)),{allDayGuess:!!(!e.startTime&&!e.endTime),duration:n,typeData:i}}return null},expand(e,t,n){let i=(0,r.o)(t,{start:e.startRecur,end:e.endRecur});return i?function(e,t,n,i,s){let a=e?(0,r.f)(e):null,o=(0,r.q)(s.start),l=s.end,c=[];for(;o<l;){let e;(!a||a[o.getUTCDay()])&&(e=t?i.add(o,t):o,c.push(i.createMarker(n.toDate(e)))),o=(0,r.t)(o,1)}return c}(e.daysOfWeek,e.startTime,e.dateEnv,n,i):[]}}],eventRefiners:T}),u({name:"change-handler",optionChangeHandlers:{events(e,t){M([e],t)},eventSources:M}}),u({name:"misc",isLoadingFuncs:[e=>b(e.eventSources)],propSetHandlers:{dateProfile:function(e,t){t.emitter.trigger("datesSet",Object.assign(Object.assign({},(0,r.l)(e.activeRange,t.dateEnv)),{view:t.viewApi}))},eventStore:function(e,t){let{emitter:n}=t;n.hasHandlers("eventsSet")&&n.trigger("eventsSet",(0,r.w)(e,t))}}})];class I{constructor(e,t){this.runTaskOption=e,this.drainedOption=t,this.queue=[],this.delayedRunner=new r.D(this.drain.bind(this))}request(e,t){this.queue.push(e),this.delayedRunner.request(t)}pause(e){this.delayedRunner.pause(e)}resume(e,t){this.delayedRunner.resume(e,t)}drain(){let{queue:e}=this;for(;e.length;){let t,n=[];for(;t=e.shift();)this.runTask(t),n.push(t);this.drained(n)}}runTask(e){this.runTaskOption&&this.runTaskOption(e)}drained(e){this.drainedOption&&this.drainedOption(e)}}function z(e,t,n){let i;return i=/^(year|month)$/.test(e.currentRangeUnit)?e.currentRange:e.activeRange,n.formatRange(i.start,i.end,(0,r.x)(t.titleFormat||function(e){let{currentRangeUnit:t}=e;if("year"===t)return{year:"numeric"};if("month"===t)return{year:"numeric",month:"long"};let n=(0,r.y)(e.currentRange.start,e.currentRange.end);return null!==n&&n>1?{year:"numeric",month:"short",day:"numeric"}:{year:"numeric",month:"long",day:"numeric"}}(e)),{isEndExclusive:e.isRangeAllDay,defaultSeparator:t.titleRangeSeparator})}class N{constructor(){this.resetListeners=new Set}handleInput(e,t){let n=this.dateEnv;if(e!==n&&("function"==typeof t?this.nowFn=t:n||(this.nowAnchorDate=e.toDate(t?e.createMarker(t):e.createNowMarker()),this.nowAnchorQueried=Date.now()),this.dateEnv=e,n))for(let e of this.resetListeners.values())e()}getDateMarker(){return this.nowAnchorDate?this.dateEnv.timestampToMarker(this.nowAnchorDate.valueOf()+(Date.now()-this.nowAnchorQueried)):this.dateEnv.createMarker(this.nowFn())}addResetListener(e){this.resetListeners.add(e)}removeResetListener(e){this.resetListeners.delete(e)}}class H{constructor(e){this.computeCurrentViewData=(0,r.z)(this._computeCurrentViewData),this.organizeRawLocales=(0,r.z)(l),this.buildLocale=(0,r.z)(c),this.buildPluginHooks=function(){let e,t=[],n=[];return(i,s)=>(e&&(0,r.i)(i,t)&&(0,r.i)(s,n)||(e=function(e,t){let n={},r={premiumReleaseDate:void 0,reducers:[],isLoadingFuncs:[],contextInit:[],eventRefiners:{},eventDefMemberAdders:[],eventSourceRefiners:{},isDraggableTransformers:[],eventDragMutationMassagers:[],eventDefMutationAppliers:[],dateSelectionTransformers:[],datePointTransforms:[],dateSpanTransforms:[],views:{},viewPropsTransformers:[],isPropsValid:null,externalDefTransforms:[],viewContainerAppends:[],eventDropTransformers:[],componentInteractions:[],calendarInteractions:[],themeClasses:{},eventSourceDefs:[],cmdFormatter:null,recurringTypes:[],namedTimeZonedImpl:null,initialView:"",elementDraggingImpl:null,optionChangeHandlers:{},scrollGridImpl:null,listenerRefiners:{},optionRefiners:{},propSetHandlers:{}};function i(e){for(let t of e){let e=t.name,s=n[e];void 0===s?(n[e]=t.id,i(t.deps),r=function(e,t){var n,r;return{premiumReleaseDate:(n=e.premiumReleaseDate,r=t.premiumReleaseDate,void 0===n?r:void 0===r?n:new Date(Math.max(n.valueOf(),r.valueOf()))),reducers:e.reducers.concat(t.reducers),isLoadingFuncs:e.isLoadingFuncs.concat(t.isLoadingFuncs),contextInit:e.contextInit.concat(t.contextInit),eventRefiners:Object.assign(Object.assign({},e.eventRefiners),t.eventRefiners),eventDefMemberAdders:e.eventDefMemberAdders.concat(t.eventDefMemberAdders),eventSourceRefiners:Object.assign(Object.assign({},e.eventSourceRefiners),t.eventSourceRefiners),isDraggableTransformers:e.isDraggableTransformers.concat(t.isDraggableTransformers),eventDragMutationMassagers:e.eventDragMutationMassagers.concat(t.eventDragMutationMassagers),eventDefMutationAppliers:e.eventDefMutationAppliers.concat(t.eventDefMutationAppliers),dateSelectionTransformers:e.dateSelectionTransformers.concat(t.dateSelectionTransformers),datePointTransforms:e.datePointTransforms.concat(t.datePointTransforms),dateSpanTransforms:e.dateSpanTransforms.concat(t.dateSpanTransforms),views:Object.assign(Object.assign({},e.views),t.views),viewPropsTransformers:e.viewPropsTransformers.concat(t.viewPropsTransformers),isPropsValid:t.isPropsValid||e.isPropsValid,externalDefTransforms:e.externalDefTransforms.concat(t.externalDefTransforms),viewContainerAppends:e.viewContainerAppends.concat(t.viewContainerAppends),eventDropTransformers:e.eventDropTransformers.concat(t.eventDropTransformers),calendarInteractions:e.calendarInteractions.concat(t.calendarInteractions),componentInteractions:e.componentInteractions.concat(t.componentInteractions),themeClasses:Object.assign(Object.assign({},e.themeClasses),t.themeClasses),eventSourceDefs:e.eventSourceDefs.concat(t.eventSourceDefs),cmdFormatter:t.cmdFormatter||e.cmdFormatter,recurringTypes:e.recurringTypes.concat(t.recurringTypes),namedTimeZonedImpl:t.namedTimeZonedImpl||e.namedTimeZonedImpl,initialView:e.initialView||t.initialView,elementDraggingImpl:e.elementDraggingImpl||t.elementDraggingImpl,optionChangeHandlers:Object.assign(Object.assign({},e.optionChangeHandlers),t.optionChangeHandlers),scrollGridImpl:t.scrollGridImpl||e.scrollGridImpl,listenerRefiners:Object.assign(Object.assign({},e.listenerRefiners),t.listenerRefiners),optionRefiners:Object.assign(Object.assign({},e.optionRefiners),t.optionRefiners),propSetHandlers:Object.assign(Object.assign({},e.propSetHandlers),t.propSetHandlers)}}(r,t)):s!==t.id&&console.warn(`Duplicate plugin '${e}'`)}}return e&&i(e),i(t),r}(i,s)),t=i,n=s,e)}(),this.buildDateEnv=(0,r.z)(P),this.buildTheme=(0,r.z)(B),this.parseToolbars=(0,r.z)(C),this.buildViewSpecs=(0,r.z)(m),this.buildDateProfileGenerator=(0,r.A)(j),this.buildViewApi=(0,r.z)(U),this.buildViewUiProps=(0,r.A)(V),this.buildEventUiBySource=(0,r.z)(F,r.E),this.buildEventUiBases=(0,r.z)(W),this.parseContextBusinessHours=(0,r.A)(Q),this.buildTitle=(0,r.z)(z),this.nowManager=new N,this.emitter=new r.F,this.actionRunner=new I(this._handleAction.bind(this),this.updateData.bind(this)),this.currentCalendarOptionsInput={},this.currentCalendarOptionsRefined={},this.currentViewOptionsInput={},this.currentViewOptionsRefined={},this.currentCalendarOptionsRefiners={},this.optionsForRefining=[],this.optionsForHandling=[],this.getCurrentData=()=>this.data,this.dispatch=e=>{this.actionRunner.request(e)},this.props=e,this.actionRunner.pause(),this.nowManager=new N;let t={},n=this.computeOptionsData(e.optionOverrides,t,e.calendarApi),i=n.calendarOptions.initialView||n.pluginHooks.initialView,s=this.computeCurrentViewData(i,n,e.optionOverrides,t);e.calendarApi.currentDataManager=this,this.emitter.setThisContext(e.calendarApi),this.emitter.setOptions(s.options);let a={nowManager:this.nowManager,dateEnv:n.dateEnv,options:n.calendarOptions,pluginHooks:n.pluginHooks,calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},o=function(e,t,n){let r=e.initialDate;return null!=r?t.createMarker(r):n.getDateMarker()}(n.calendarOptions,n.dateEnv,this.nowManager),d=s.dateProfileGenerator.build(o);for(let e of((0,r.G)(d.activeRange,o)||(o=d.currentRange.start),n.pluginHooks.contextInit))e(a);let u=function(e,t,n){let i=t?t.activeRange:null;return y({},function(e,t){let n=(0,r.j)(t),i=[].concat(e.eventSources||[]),s=[];for(let a of(e.initialEvents&&i.unshift(e.initialEvents),e.events&&i.unshift(e.events),i)){let e=(0,r.p)(a,t,n);e&&s.push(e)}return s}(e,n),i,n)}(n.calendarOptions,d,a),f={dynamicOptionOverrides:t,currentViewType:i,currentDate:o,dateProfile:d,businessHours:this.parseContextBusinessHours(a),eventSources:u,eventUiBases:{},eventStore:(0,r.H)(),renderableEventStore:(0,r.H)(),dateSelection:null,eventSelection:"",eventDrag:null,eventResize:null,selectionConfig:this.buildViewUiProps(a).selectionConfig},h=Object.assign(Object.assign({},a),f);for(let e of n.pluginHooks.reducers)Object.assign(f,e(null,null,h));L(f,a)&&this.emitter.trigger("loading",!0),this.state=f,this.updateData(),this.actionRunner.resume()}resetOptions(e,t){let{props:n}=this;void 0===t?n.optionOverrides=e:(n.optionOverrides=Object.assign(Object.assign({},n.optionOverrides||{}),e),this.optionsForRefining.push(...t)),(void 0===t||t.length)&&this.actionRunner.request({type:"NOTHING"})}_handleAction(e){var t,n,i;let{props:s,state:a,emitter:o}=this,l=(t=a.dynamicOptionOverrides,"SET_OPTION"===e.type?Object.assign(Object.assign({},t),{[e.optionName]:e.rawOptionValue}):t),c=this.computeOptionsData(s.optionOverrides,l,s.calendarApi),d=(n=a.currentViewType,"CHANGE_VIEW_TYPE"===e.type&&(n=e.viewType),n),u=this.computeCurrentViewData(d,c,s.optionOverrides,l);s.calendarApi.currentDataManager=this,o.setThisContext(s.calendarApi),o.setOptions(u.options);let f={nowManager:this.nowManager,dateEnv:c.dateEnv,options:c.calendarOptions,pluginHooks:c.pluginHooks,calendarApi:s.calendarApi,dispatch:this.dispatch,emitter:o,getCurrentData:this.getCurrentData},{currentDate:h,dateProfile:p}=a;this.data&&this.data.dateProfileGenerator!==u.dateProfileGenerator&&(p=u.dateProfileGenerator.build(h)),i=h,h="CHANGE_DATE"===e.type?e.dateMarker:i,p=function(e,t,n,r){let i;switch(t.type){case"CHANGE_VIEW_TYPE":return r.build(t.dateMarker||n);case"CHANGE_DATE":return r.build(t.dateMarker);case"PREV":if((i=r.buildPrev(e,n)).isValid)return i;break;case"NEXT":if((i=r.buildNext(e,n)).isValid)return i}return e}(p,e,h,u.dateProfileGenerator),"PREV"!==e.type&&"NEXT"!==e.type&&(0,r.G)(p.currentRange,h)||(h=p.currentRange.start);let g=function(e,t,n,i){var s,a,o,l;let c=n?n.activeRange:null;switch(t.type){case"ADD_EVENT_SOURCES":return y(e,t.sources,c,i);case"REMOVE_EVENT_SOURCE":return s=t.sourceId,(0,r.h)(e,e=>e.sourceId!==s);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":if(n)return A(e,c,i);return e;case"FETCH_EVENT_SOURCES":return _(e,t.sourceIds?(0,r.f)(t.sourceIds):E(e,i),c,t.isRefetch||!1,i);case"RECEIVE_EVENTS":case"RECEIVE_EVENT_ERROR":let d;return a=t.sourceId,o=t.fetchId,l=t.fetchRange,(d=e[a])&&o===d.latestFetchId?Object.assign(Object.assign({},e),{[a]:Object.assign(Object.assign({},d),{isFetching:!1,fetchRange:l})}):e;case"REMOVE_ALL_EVENT_SOURCES":return{};default:return e}}(a.eventSources,e,p,f),m=(0,r.I)(a.eventStore,e,g,p,f),v=b(g)&&!u.options.progressiveEventRendering&&a.renderableEventStore||m,{eventUiSingleBase:D,selectionConfig:C}=this.buildViewUiProps(f),w=this.buildEventUiBySource(g),R=this.buildEventUiBases(v.defs,D,w),S={dynamicOptionOverrides:l,currentViewType:d,currentDate:h,dateProfile:p,eventSources:g,eventStore:m,renderableEventStore:v,selectionConfig:C,eventUiBases:R,businessHours:this.parseContextBusinessHours(f),dateSelection:function(e,t){switch(t.type){case"UNSELECT_DATES":return null;case"SELECT_DATES":return t.selection;default:return e}}(a.dateSelection,e),eventSelection:function(e,t){switch(t.type){case"UNSELECT_EVENT":return"";case"SELECT_EVENT":return t.eventInstanceId;default:return e}}(a.eventSelection,e),eventDrag:function(e,t){let n;switch(t.type){case"UNSET_EVENT_DRAG":return null;case"SET_EVENT_DRAG":return{affectedEvents:(n=t.state).affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return e}}(a.eventDrag,e),eventResize:function(e,t){let n;switch(t.type){case"UNSET_EVENT_RESIZE":return null;case"SET_EVENT_RESIZE":return{affectedEvents:(n=t.state).affectedEvents,mutatedEvents:n.mutatedEvents,isEvent:n.isEvent};default:return e}}(a.eventResize,e)},k=Object.assign(Object.assign({},f),S);for(let t of c.pluginHooks.reducers)Object.assign(S,t(a,e,k));let x=L(a,f),T=L(S,f);!x&&T?o.trigger("loading",!0):x&&!T&&o.trigger("loading",!1),this.state=S,s.onAction&&s.onAction(e)}updateData(){let{props:e,state:t}=this,n=this.data,i=this.computeOptionsData(e.optionOverrides,t.dynamicOptionOverrides,e.calendarApi),s=this.computeCurrentViewData(t.currentViewType,i,e.optionOverrides,t.dynamicOptionOverrides),a=this.data=Object.assign(Object.assign(Object.assign({nowManager:this.nowManager,viewTitle:this.buildTitle(t.dateProfile,s.options,i.dateEnv),calendarApi:e.calendarApi,dispatch:this.dispatch,emitter:this.emitter,getCurrentData:this.getCurrentData},i),s),t),o=i.pluginHooks.optionChangeHandlers,l=n&&n.calendarOptions,c=i.calendarOptions;if(l&&l!==c){if(l.timeZone!==c.timeZone){var d,u;let e;t.eventSources=a.eventSources=(d=a.eventSources,e=(u=t.dateProfile)?u.activeRange:null,_(d,E(d,a),e,!0,a)),t.eventStore=a.eventStore=(0,r.J)(a.eventStore,n.dateEnv,a.dateEnv),t.renderableEventStore=a.renderableEventStore=(0,r.J)(a.renderableEventStore,n.dateEnv,a.dateEnv)}for(let e in o)(-1!==this.optionsForHandling.indexOf(e)||l[e]!==c[e])&&o[e](c[e],a)}this.optionsForHandling=[],e.onData&&e.onData(a)}computeOptionsData(e,t,n){if(!this.optionsForRefining.length&&e===this.stableOptionOverrides&&t===this.stableDynamicOptionOverrides)return this.stableCalendarOptionsData;let{refinedOptions:r,pluginHooks:i,localeDefaults:s,availableLocaleData:a,extra:o}=this.processRawCalendarOptions(e,t);G(o);let l=this.buildDateEnv(r.timeZone,r.locale,r.weekNumberCalculation,r.firstDay,r.weekText,i,a,r.defaultRangeSeparator),c=this.buildViewSpecs(i.views,this.stableOptionOverrides,this.stableDynamicOptionOverrides,s),d=this.buildTheme(r,i),u=this.parseToolbars(r,this.stableOptionOverrides,d,c,n);return this.stableCalendarOptionsData={calendarOptions:r,pluginHooks:i,dateEnv:l,viewSpecs:c,theme:d,toolbarConfig:u,localeDefaults:s,availableRawLocales:a.map}}processRawCalendarOptions(e,t){let{locales:n,locale:i}=(0,r.K)([r.e,e,t]),s=this.organizeRawLocales(n),a=s.map,o=this.buildLocale(i||s.defaultCode,a).options,l=this.buildPluginHooks(e.plugins||[],O),c=this.currentCalendarOptionsRefiners=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},r.L),r.M),r.N),l.listenerRefiners),l.optionRefiners),d={},u=(0,r.K)([r.e,o,e,t]),f={},h=this.currentCalendarOptionsInput,p=this.currentCalendarOptionsRefined,g=!1;for(let e in u)-1===this.optionsForRefining.indexOf(e)&&(u[e]===h[e]||r.O[e]&&e in h&&r.O[e](h[e],u[e]))?f[e]=p[e]:c[e]?(f[e]=c[e](u[e]),g=!0):d[e]=h[e];return g&&(this.currentCalendarOptionsInput=u,this.currentCalendarOptionsRefined=f,this.stableOptionOverrides=e,this.stableDynamicOptionOverrides=t),this.optionsForHandling.push(...this.optionsForRefining),this.optionsForRefining=[],{rawOptions:this.currentCalendarOptionsInput,refinedOptions:this.currentCalendarOptionsRefined,pluginHooks:l,availableLocaleData:s,localeDefaults:o,extra:d}}_computeCurrentViewData(e,t,n,r){let i=t.viewSpecs[e];if(!i)throw Error(`viewType "${e}" is not available. Please make sure you've loaded all neccessary plugins`);let{refinedOptions:s,extra:a}=this.processRawViewOptions(i,t.pluginHooks,t.localeDefaults,n,r);G(a),this.nowManager.handleInput(t.dateEnv,s.now);let o=this.buildDateProfileGenerator({dateProfileGeneratorClass:i.optionDefaults.dateProfileGeneratorClass,nowManager:this.nowManager,duration:i.duration,durationUnit:i.durationUnit,usesMinMaxTime:i.optionDefaults.usesMinMaxTime,dateEnv:t.dateEnv,calendarApi:this.props.calendarApi,slotMinTime:s.slotMinTime,slotMaxTime:s.slotMaxTime,showNonCurrentDates:s.showNonCurrentDates,dayCount:s.dayCount,dateAlignment:s.dateAlignment,dateIncrement:s.dateIncrement,hiddenDays:s.hiddenDays,weekends:s.weekends,validRangeInput:s.validRange,visibleRangeInput:s.visibleRange,fixedWeekCount:s.fixedWeekCount});return{viewSpec:i,options:s,dateProfileGenerator:o,viewApi:this.buildViewApi(e,this.getCurrentData,t.dateEnv)}}processRawViewOptions(e,t,n,i,s){let a=(0,r.K)([r.e,e.optionDefaults,n,i,e.optionOverrides,s]),o=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},r.L),r.M),r.N),r.P),t.listenerRefiners),t.optionRefiners),l={},c=this.currentViewOptionsInput,d=this.currentViewOptionsRefined,u=!1,f={};for(let e in a)a[e]===c[e]||r.O[e]&&r.O[e](a[e],c[e])?l[e]=d[e]:(a[e]===this.currentCalendarOptionsInput[e]||r.O[e]&&r.O[e](a[e],this.currentCalendarOptionsInput[e])?e in this.currentCalendarOptionsRefined&&(l[e]=this.currentCalendarOptionsRefined[e]):o[e]?l[e]=o[e](a[e]):f[e]=a[e],u=!0);return u&&(this.currentViewOptionsInput=a,this.currentViewOptionsRefined=l),{rawOptions:this.currentViewOptionsInput,refinedOptions:this.currentViewOptionsRefined,extra:f}}}function P(e,t,n,i,s,a,o,l){let d=c(t||o.defaultCode,o.map);return new r.Q({calendarSystem:"gregory",timeZone:e,namedTimeZoneImpl:a.namedTimeZonedImpl,locale:d,weekNumberCalculation:n,firstDay:i,weekText:s,cmdFormatter:a.cmdFormatter,defaultSeparator:l})}function B(e,t){return new(t.themeClasses[e.themeSystem]||f)(e)}function j(e){return new(e.dateProfileGeneratorClass||r.R)(e)}function U(e,t,n){return new R(e,t,n)}function F(e){return(0,r.a)(e,e=>e.ui)}function W(e,t,n){let r={"":t};for(let t in e){let i=e[t];i.sourceId&&n[i.sourceId]&&(r[t]=n[i.sourceId])}return r}function V(e){let{options:t}=e;return{eventUiSingleBase:(0,r.S)({display:t.eventDisplay,editable:t.editable,startEditable:t.eventStartEditable,durationEditable:t.eventDurationEditable,constraint:t.eventConstraint,overlap:"boolean"==typeof t.eventOverlap?t.eventOverlap:void 0,allow:t.eventAllow,backgroundColor:t.eventBackgroundColor,borderColor:t.eventBorderColor,textColor:t.eventTextColor,color:t.eventColor},e),selectionConfig:(0,r.S)({constraint:t.selectConstraint,overlap:"boolean"==typeof t.selectOverlap?t.selectOverlap:void 0,allow:t.selectAllow},e)}}function L(e,t){for(let n of t.pluginHooks.isLoadingFuncs)if(n(e))return!0;return!1}function Q(e){return(0,r.U)(e.options.businessHours,e)}function G(e,t){for(let n in e)console.warn(`Unknown option '${n}'`+(t?` for view '${t}'`:""))}class Y extends r.B{render(){let e=this.props.widgetGroups.map(e=>this.renderWidgetGroup(e));return(0,i.az)("div",{className:"fc-toolbar-chunk"},...e)}renderWidgetGroup(e){let{props:t}=this,{theme:n}=this.context,r=[],s=!0;for(let a of e){let{buttonName:e,buttonClick:o,buttonText:l,buttonIcon:c,buttonHint:d}=a;if("title"===e)s=!1,r.push((0,i.az)("h2",{className:"fc-toolbar-title",id:t.titleId},t.title));else{let s=e===t.activeButton,a=!t.isTodayEnabled&&"today"===e||!t.isPrevEnabled&&"prev"===e||!t.isNextEnabled&&"next"===e,u=[`fc-${e}-button`,n.getClass("button")];s&&u.push(n.getClass("buttonActive")),r.push((0,i.az)("button",{type:"button",title:"function"==typeof d?d(t.navUnit):d,disabled:a,"aria-pressed":s,className:u.join(" "),onClick:o},l||(c?(0,i.az)("span",{className:c,role:"img"}):"")))}}if(r.length>1){let e=s&&n.getClass("buttonGroup")||"";return(0,i.az)("div",{className:e},...r)}return r[0]}}class q extends r.B{render(){let e,t,{model:n,extraClassName:r}=this.props,s=!1,a=n.sectionWidgets,o=a.center;a.left?(s=!0,e=a.left):e=a.start,a.right?(s=!0,t=a.right):t=a.end;let l=[r||"","fc-toolbar",s?"fc-toolbar-ltr":""];return(0,i.az)("div",{className:l.join(" ")},this.renderSection("start",e||[]),this.renderSection("center",o||[]),this.renderSection("end",t||[]))}renderSection(e,t){let{props:n}=this;return(0,i.az)(Y,{key:e,widgetGroups:t,title:n.title,navUnit:n.navUnit,activeButton:n.activeButton,isTodayEnabled:n.isTodayEnabled,isPrevEnabled:n.isPrevEnabled,isNextEnabled:n.isNextEnabled,titleId:n.titleId})}}class Z extends r.B{constructor(){super(...arguments),this.state={availableWidth:null},this.handleEl=e=>{this.el=e,(0,r.W)(this.props.elRef,e),this.updateAvailableWidth()},this.handleResize=()=>{this.updateAvailableWidth()}}render(){let{props:e,state:t}=this,{aspectRatio:n}=e,r=["fc-view-harness",n||e.liquid||e.height?"fc-view-harness-active":"fc-view-harness-passive"],s="",a="";return n?null!==t.availableWidth?s=t.availableWidth/n:a=`${1/n*100}%`:s=e.height||"",(0,i.az)("div",{"aria-labelledby":e.labeledById,ref:this.handleEl,className:r.join(" "),style:{height:s,paddingBottom:a}},e.children)}componentDidMount(){this.context.addResizeHandler(this.handleResize)}componentWillUnmount(){this.context.removeResizeHandler(this.handleResize)}updateAvailableWidth(){this.el&&this.props.aspectRatio&&this.setState({availableWidth:this.el.offsetWidth})}}class $ extends r.X{constructor(e){super(e),this.handleSegClick=(e,t)=>{let{component:n}=this,{context:i}=n,s=(0,r.Y)(t);if(s&&n.isValidSegDownEl(e.target)){let a=(0,r.Z)(e.target,".fc-event-forced-url"),o=a?a.querySelector("a[href]").href:"";i.emitter.trigger("eventClick",{el:t,event:new r._(n.context,s.eventRange.def,s.eventRange.instance),jsEvent:e,view:i.viewApi}),o&&!e.defaultPrevented&&(window.location.href=o)}},this.destroy=(0,r.$)(e.el,"click",".fc-event",this.handleSegClick)}}class J extends r.X{constructor(e){super(e),this.handleEventElRemove=e=>{e===this.currentSegEl&&this.handleSegLeave(null,this.currentSegEl)},this.handleSegEnter=(e,t)=>{(0,r.Y)(t)&&(this.currentSegEl=t,this.triggerEvent("eventMouseEnter",e,t))},this.handleSegLeave=(e,t)=>{this.currentSegEl&&(this.currentSegEl=null,this.triggerEvent("eventMouseLeave",e,t))},this.removeHoverListeners=(0,r.a0)(e.el,".fc-event",this.handleSegEnter,this.handleSegLeave)}destroy(){this.removeHoverListeners()}triggerEvent(e,t,n){let{component:i}=this,{context:s}=i,a=(0,r.Y)(n);(!t||i.isValidSegDownEl(t.target))&&s.emitter.trigger(e,{el:n,event:new r._(s,a.eventRange.def,a.eventRange.instance),jsEvent:t,view:s.viewApi})}}class X extends r.a1{constructor(){super(...arguments),this.buildViewContext=(0,r.z)(r.a2),this.buildViewPropTransformers=(0,r.z)(ee),this.buildToolbarProps=(0,r.z)(K),this.headerRef=(0,i.Vf)(),this.footerRef=(0,i.Vf)(),this.interactionsStore={},this.state={viewLabelId:(0,r.a3)()},this.registerInteractiveComponent=(e,t)=>{let n=(0,r.a4)(e,t),i=[$,J].concat(this.props.pluginHooks.componentInteractions).map(e=>new e(n));this.interactionsStore[e.uid]=i,r.a5[e.uid]=n},this.unregisterInteractiveComponent=e=>{let t=this.interactionsStore[e.uid];if(t){for(let e of t)e.destroy();delete this.interactionsStore[e.uid]}delete r.a5[e.uid]},this.resizeRunner=new r.D(()=>{this.props.emitter.trigger("_resize",!0),this.props.emitter.trigger("windowResize",{view:this.props.viewApi})}),this.handleWindowResize=e=>{let{options:t}=this.props;t.handleWindowResize&&e.target===window&&this.resizeRunner.request(t.windowResizeDelay)}}render(){let e,{props:t}=this,{toolbarConfig:n,options:s}=t,a=!1,o="";t.isHeightAuto||t.forPrint?o="":null!=s.height?a=!0:null!=s.contentHeight?o=s.contentHeight:e=Math.max(s.aspectRatio,.5);let l=this.buildViewContext(t.viewSpec,t.viewApi,t.options,t.dateProfileGenerator,t.dateEnv,t.nowManager,t.theme,t.pluginHooks,t.dispatch,t.getCurrentData,t.emitter,t.calendarApi,this.registerInteractiveComponent,this.unregisterInteractiveComponent),c=n.header&&n.header.hasTitle?this.state.viewLabelId:void 0;return(0,i.az)(r.V.Provider,{value:l},(0,i.az)(r.a6,{unit:"day"},r=>{let s=this.buildToolbarProps(t.viewSpec,t.dateProfile,t.dateProfileGenerator,t.currentDate,r,t.viewTitle);return(0,i.az)(i.HY,null,n.header&&(0,i.az)(q,Object.assign({ref:this.headerRef,extraClassName:"fc-header-toolbar",model:n.header,titleId:c},s)),(0,i.az)(Z,{liquid:a,height:o,aspectRatio:e,labeledById:c},this.renderView(t),this.buildAppendContent()),n.footer&&(0,i.az)(q,Object.assign({ref:this.footerRef,extraClassName:"fc-footer-toolbar",model:n.footer,titleId:""},s)))}))}componentDidMount(){let{props:e}=this;this.calendarInteractions=e.pluginHooks.calendarInteractions.map(t=>new t(e)),window.addEventListener("resize",this.handleWindowResize);let{propSetHandlers:t}=e.pluginHooks;for(let n in t)t[n](e[n],e)}componentDidUpdate(e){let{props:t}=this,{propSetHandlers:n}=t.pluginHooks;for(let r in n)t[r]!==e[r]&&n[r](t[r],t)}componentWillUnmount(){for(let e of(window.removeEventListener("resize",this.handleWindowResize),this.resizeRunner.clear(),this.calendarInteractions))e.destroy();this.props.emitter.trigger("_unmount")}buildAppendContent(){let{props:e}=this,t=e.pluginHooks.viewContainerAppends.map(t=>t(e));return(0,i.az)(i.HY,{},...t)}renderView(e){let{pluginHooks:t}=e,{viewSpec:n}=e,r={dateProfile:e.dateProfile,businessHours:e.businessHours,eventStore:e.renderableEventStore,eventUiBases:e.eventUiBases,dateSelection:e.dateSelection,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,isHeightAuto:e.isHeightAuto,forPrint:e.forPrint};for(let n of this.buildViewPropTransformers(t.viewPropsTransformers))Object.assign(r,n.transform(r,e));let s=n.component;return(0,i.az)(s,Object.assign({},r))}}function K(e,t,n,i,s,a){let o=n.build(s,void 0,!1),l=n.buildPrev(t,i,!1),c=n.buildNext(t,i,!1);return{title:a,activeButton:e.type,navUnit:e.singleUnit,isTodayEnabled:o.isValid&&!(0,r.G)(t.currentRange,s),isPrevEnabled:l.isValid,isNextEnabled:c.isValid}}function ee(e){return e.map(e=>new e)}class et extends r.a7{constructor(e,t={}){super(),this.isRendering=!1,this.isRendered=!1,this.currentClassNames=[],this.customContentRenderId=0,this.handleAction=e=>{switch(e.type){case"SET_EVENT_DRAG":case"SET_EVENT_RESIZE":this.renderRunner.tryDrain()}},this.handleData=e=>{this.currentData=e,this.renderRunner.request(e.calendarOptions.rerenderDelay)},this.handleRenderRequest=()=>{if(this.isRendering){this.isRendered=!0;let{currentData:e}=this;(0,r.a8)(()=>{(0,i.sY)((0,i.az)(r.a9,{options:e.calendarOptions,theme:e.theme,emitter:e.emitter},(t,n,s,a)=>(this.setClassNames(t),this.setHeight(n),(0,i.az)(r.aa.Provider,{value:this.customContentRenderId},(0,i.az)(X,Object.assign({isHeightAuto:s,forPrint:a},e))))),this.el)})}else this.isRendered&&(this.isRendered=!1,(0,i.sY)(null,this.el),this.setClassNames([]),this.setHeight(""))},(0,r.ab)(e),this.el=e,this.renderRunner=new r.D(this.handleRenderRequest),new H({optionOverrides:t,calendarApi:this,onAction:this.handleAction,onData:this.handleData})}render(){let e=this.isRendering;e?this.customContentRenderId+=1:this.isRendering=!0,this.renderRunner.request(),e&&this.updateSize()}destroy(){this.isRendering&&(this.isRendering=!1,this.renderRunner.request())}updateSize(){(0,r.a8)(()=>{super.updateSize()})}batchRendering(e){this.renderRunner.pause("batchRendering"),e(),this.renderRunner.resume("batchRendering")}pauseRendering(){this.renderRunner.pause("pauseRendering")}resumeRendering(){this.renderRunner.resume("pauseRendering",!0)}resetOptions(e,t){this.currentDataManager.resetOptions(e,t)}setClassNames(e){if(!(0,r.i)(e,this.currentClassNames)){let{classList:t}=this.el;for(let e of this.currentClassNames)t.remove(e);for(let n of e)t.add(n);this.currentClassNames=e}}setHeight(e){(0,r.ac)(this.el,"height",e)}}},75867:(e,t,n)=>{"use strict";let r,i,s;n.d(t,{$:()=>_,A:()=>en,B:()=>eZ,C:()=>e2,D:()=>p,E:()=>ez,F:()=>tO,G:()=>e9,H:()=>tm,I:()=>tS,J:()=>tT,K:()=>ew,L:()=>ev,M:()=>ey,N:()=>eA,O:()=>e_,P:()=>eC,Q:()=>eU,R:()=>ti,S:()=>tE,T:()=>eF,U:()=>tz,V:()=>eG,W:()=>e$,X:()=>t5,Y:()=>tF,Z:()=>m,_:()=>tP,a:()=>eM,a0:()=>E,a1:()=>eq,a2:()=>eY,a3:()=>A,a4:()=>t3,a5:()=>t6,a6:()=>t8,a7:()=>t7,a8:()=>eW,a9:()=>t4,aX:()=>ne,aY:()=>t9,a_:()=>nr,aa:()=>e1,ab:()=>u,ac:()=>b,ad:()=>tU,ax:()=>tn,b:()=>e3,b8:()=>ni,bD:()=>nd,bI:()=>nb,bM:()=>nA,bO:()=>tG,bP:()=>tL,bQ:()=>tY,bR:()=>tq,bS:()=>tZ,bT:()=>n_,bU:()=>nE,bZ:()=>nz,bc:()=>na,be:()=>U,bf:()=>B,bg:()=>F,bt:()=>X,bv:()=>K,by:()=>no,bz:()=>nc,c:()=>N,c8:()=>nM,c9:()=>nI,ca:()=>nO,cb:()=>nR,cd:()=>nS,cg:()=>nH,ci:()=>nj,cj:()=>nU,ck:()=>nN,cl:()=>nL,cm:()=>nW,cn:()=>nQ,co:()=>nZ,cq:()=>e5,ct:()=>d,cv:()=>n1,d:()=>T,e:()=>eb,f:()=>eO,g:()=>R,h:()=>eT,i:()=>H,j:()=>tR,k:()=>k,l:()=>tJ,m:()=>ex,n:()=>eS,o:()=>e7,p:()=>tw,q:()=>L,r:()=>t1,s:()=>O,t:()=>j,u:()=>tK,v:()=>eI,w:()=>tj,x:()=>em,y:()=>V,z:()=>et});var a=n(95284),o=n(46168);let l=[],c=new Map;function d(e){l.push(e),c.forEach(t=>{h(t,e)})}function u(e){e.isConnected&&e.getRootNode&&f(e.getRootNode())}function f(e){let t=c.get(e);if(!t||!t.isConnected){if(!(t=e.querySelector("style[data-fullcalendar]"))){(t=document.createElement("style")).setAttribute("data-fullcalendar","");let n=(void 0===r&&(r=function(){let e=document.querySelector('meta[name="csp-nonce"]');if(e&&e.hasAttribute("content"))return e.getAttribute("content");let t=document.querySelector("script[nonce]");return t&&t.nonce||""}()),r);n&&(t.nonce=n);let i=e===document?document.head:e,s=e===document?i.querySelector("script,link[rel=stylesheet],link[as=style],style"):i.firstChild;i.insertBefore(t,s)}c.set(e,t),function(e){for(let t of l)h(e,t)}(t)}}function h(e,t){let{sheet:n}=e,r=n.cssRules.length;t.split("}").forEach((e,t)=>{(e=e.trim())&&n.insertRule(e+"}",r+t)})}"undefined"!=typeof document&&f(document),d(':root{--fc-small-font-size:.85em;--fc-page-bg-color:#fff;--fc-neutral-bg-color:hsla(0,0%,82%,.3);--fc-neutral-text-color:grey;--fc-border-color:#ddd;--fc-button-text-color:#fff;--fc-button-bg-color:#2c3e50;--fc-button-border-color:#2c3e50;--fc-button-hover-bg-color:#1e2b37;--fc-button-hover-border-color:#1a252f;--fc-button-active-bg-color:#1a252f;--fc-button-active-border-color:#151e27;--fc-event-bg-color:#3788d8;--fc-event-border-color:#3788d8;--fc-event-text-color:#fff;--fc-event-selected-overlay-color:rgba(0,0,0,.25);--fc-more-link-bg-color:#d0d0d0;--fc-more-link-text-color:inherit;--fc-event-resizer-thickness:8px;--fc-event-resizer-dot-total-width:8px;--fc-event-resizer-dot-border-width:1px;--fc-non-business-color:hsla(0,0%,84%,.3);--fc-bg-event-color:#8fdf82;--fc-bg-event-opacity:0.3;--fc-highlight-color:rgba(188,232,241,.3);--fc-today-bg-color:rgba(255,220,40,.15);--fc-now-indicator-color:red}.fc-not-allowed,.fc-not-allowed .fc-event{cursor:not-allowed}.fc{display:flex;flex-direction:column;font-size:1em}.fc,.fc *,.fc :after,.fc :before{box-sizing:border-box}.fc table{border-collapse:collapse;border-spacing:0;font-size:1em}.fc th{text-align:center}.fc td,.fc th{padding:0;vertical-align:top}.fc a[data-navlink]{cursor:pointer}.fc a[data-navlink]:hover{text-decoration:underline}.fc-direction-ltr{direction:ltr;text-align:left}.fc-direction-rtl{direction:rtl;text-align:right}.fc-theme-standard td,.fc-theme-standard th{border:1px solid var(--fc-border-color)}.fc-liquid-hack td,.fc-liquid-hack th{position:relative}@font-face{font-family:fcicons;font-style:normal;font-weight:400;src:url("data:application/x-font-ttf;charset=utf-8;base64,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") format("truetype")}.fc-icon{speak:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;display:inline-block;font-family:fcicons!important;font-style:normal;font-variant:normal;font-weight:400;height:1em;line-height:1;text-align:center;text-transform:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;width:1em}.fc-icon-chevron-left:before{content:"\\e900"}.fc-icon-chevron-right:before{content:"\\e901"}.fc-icon-chevrons-left:before{content:"\\e902"}.fc-icon-chevrons-right:before{content:"\\e903"}.fc-icon-minus-square:before{content:"\\e904"}.fc-icon-plus-square:before{content:"\\e905"}.fc-icon-x:before{content:"\\e906"}.fc .fc-button{border-radius:0;font-family:inherit;font-size:inherit;line-height:inherit;margin:0;overflow:visible;text-transform:none}.fc .fc-button:focus{outline:1px dotted;outline:5px auto -webkit-focus-ring-color}.fc .fc-button{-webkit-appearance:button}.fc .fc-button:not(:disabled){cursor:pointer}.fc .fc-button{background-color:transparent;border:1px solid transparent;border-radius:.25em;display:inline-block;font-size:1em;font-weight:400;line-height:1.5;padding:.4em .65em;text-align:center;-webkit-user-select:none;-moz-user-select:none;user-select:none;vertical-align:middle}.fc .fc-button:hover{text-decoration:none}.fc .fc-button:focus{box-shadow:0 0 0 .2rem rgba(44,62,80,.25);outline:0}.fc .fc-button:disabled{opacity:.65}.fc .fc-button-primary{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:hover{background-color:var(--fc-button-hover-bg-color);border-color:var(--fc-button-hover-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:disabled{background-color:var(--fc-button-bg-color);border-color:var(--fc-button-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button-primary:not(:disabled).fc-button-active,.fc .fc-button-primary:not(:disabled):active{background-color:var(--fc-button-active-bg-color);border-color:var(--fc-button-active-border-color);color:var(--fc-button-text-color)}.fc .fc-button-primary:not(:disabled).fc-button-active:focus,.fc .fc-button-primary:not(:disabled):active:focus{box-shadow:0 0 0 .2rem rgba(76,91,106,.5)}.fc .fc-button .fc-icon{font-size:1.5em;vertical-align:middle}.fc .fc-button-group{display:inline-flex;position:relative;vertical-align:middle}.fc .fc-button-group>.fc-button{flex:1 1 auto;position:relative}.fc .fc-button-group>.fc-button.fc-button-active,.fc .fc-button-group>.fc-button:active,.fc .fc-button-group>.fc-button:focus,.fc .fc-button-group>.fc-button:hover{z-index:1}.fc-direction-ltr .fc-button-group>.fc-button:not(:first-child){border-bottom-left-radius:0;border-top-left-radius:0;margin-left:-1px}.fc-direction-ltr .fc-button-group>.fc-button:not(:last-child){border-bottom-right-radius:0;border-top-right-radius:0}.fc-direction-rtl .fc-button-group>.fc-button:not(:first-child){border-bottom-right-radius:0;border-top-right-radius:0;margin-right:-1px}.fc-direction-rtl .fc-button-group>.fc-button:not(:last-child){border-bottom-left-radius:0;border-top-left-radius:0}.fc .fc-toolbar{align-items:center;display:flex;justify-content:space-between}.fc .fc-toolbar.fc-header-toolbar{margin-bottom:1.5em}.fc .fc-toolbar.fc-footer-toolbar{margin-top:1.5em}.fc .fc-toolbar-title{font-size:1.75em;margin:0}.fc-direction-ltr .fc-toolbar>*>:not(:first-child){margin-left:.75em}.fc-direction-rtl .fc-toolbar>*>:not(:first-child){margin-right:.75em}.fc-direction-rtl .fc-toolbar-ltr{flex-direction:row-reverse}.fc .fc-scroller{-webkit-overflow-scrolling:touch;position:relative}.fc .fc-scroller-liquid{height:100%}.fc .fc-scroller-liquid-absolute{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-scroller-harness{direction:ltr;overflow:hidden;position:relative}.fc .fc-scroller-harness-liquid{height:100%}.fc-direction-rtl .fc-scroller-harness>.fc-scroller{direction:rtl}.fc-theme-standard .fc-scrollgrid{border:1px solid var(--fc-border-color)}.fc .fc-scrollgrid,.fc .fc-scrollgrid table{table-layout:fixed;width:100%}.fc .fc-scrollgrid table{border-left-style:hidden;border-right-style:hidden;border-top-style:hidden}.fc .fc-scrollgrid{border-bottom-width:0;border-collapse:separate;border-right-width:0}.fc .fc-scrollgrid-liquid{height:100%}.fc .fc-scrollgrid-section,.fc .fc-scrollgrid-section table,.fc .fc-scrollgrid-section>td{height:1px}.fc .fc-scrollgrid-section-liquid>td{height:100%}.fc .fc-scrollgrid-section>*{border-left-width:0;border-top-width:0}.fc .fc-scrollgrid-section-footer>*,.fc .fc-scrollgrid-section-header>*{border-bottom-width:0}.fc .fc-scrollgrid-section-body table,.fc .fc-scrollgrid-section-footer table{border-bottom-style:hidden}.fc .fc-scrollgrid-section-sticky>*{background:var(--fc-page-bg-color);position:sticky;z-index:3}.fc .fc-scrollgrid-section-header.fc-scrollgrid-section-sticky>*{top:0}.fc .fc-scrollgrid-section-footer.fc-scrollgrid-section-sticky>*{bottom:0}.fc .fc-scrollgrid-sticky-shim{height:1px;margin-bottom:-1px}.fc-sticky{position:sticky}.fc .fc-view-harness{flex-grow:1;position:relative}.fc .fc-view-harness-active>.fc-view{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-col-header-cell-cushion{display:inline-block;padding:2px 4px}.fc .fc-bg-event,.fc .fc-highlight,.fc .fc-non-business{bottom:0;left:0;position:absolute;right:0;top:0}.fc .fc-non-business{background:var(--fc-non-business-color)}.fc .fc-bg-event{background:var(--fc-bg-event-color);opacity:var(--fc-bg-event-opacity)}.fc .fc-bg-event .fc-event-title{font-size:var(--fc-small-font-size);font-style:italic;margin:.5em}.fc .fc-highlight{background:var(--fc-highlight-color)}.fc .fc-cell-shaded,.fc .fc-day-disabled{background:var(--fc-neutral-bg-color)}a.fc-event,a.fc-event:hover{text-decoration:none}.fc-event.fc-event-draggable,.fc-event[href]{cursor:pointer}.fc-event .fc-event-main{position:relative;z-index:2}.fc-event-dragging:not(.fc-event-selected){opacity:.75}.fc-event-dragging.fc-event-selected{box-shadow:0 2px 7px rgba(0,0,0,.3)}.fc-event .fc-event-resizer{display:none;position:absolute;z-index:4}.fc-event-selected .fc-event-resizer,.fc-event:hover .fc-event-resizer{display:block}.fc-event-selected .fc-event-resizer{background:var(--fc-page-bg-color);border-color:inherit;border-radius:calc(var(--fc-event-resizer-dot-total-width)/2);border-style:solid;border-width:var(--fc-event-resizer-dot-border-width);height:var(--fc-event-resizer-dot-total-width);width:var(--fc-event-resizer-dot-total-width)}.fc-event-selected .fc-event-resizer:before{bottom:-20px;content:"";left:-20px;position:absolute;right:-20px;top:-20px}.fc-event-selected,.fc-event:focus{box-shadow:0 2px 5px rgba(0,0,0,.2)}.fc-event-selected:before,.fc-event:focus:before{bottom:0;content:"";left:0;position:absolute;right:0;top:0;z-index:3}.fc-event-selected:after,.fc-event:focus:after{background:var(--fc-event-selected-overlay-color);bottom:-1px;content:"";left:-1px;position:absolute;right:-1px;top:-1px;z-index:1}.fc-h-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-h-event .fc-event-main{color:var(--fc-event-text-color)}.fc-h-event .fc-event-main-frame{display:flex}.fc-h-event .fc-event-time{max-width:100%;overflow:hidden}.fc-h-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-width:0}.fc-h-event .fc-event-title{display:inline-block;left:0;max-width:100%;overflow:hidden;right:0;vertical-align:top}.fc-h-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-start),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-end){border-bottom-left-radius:0;border-left-width:0;border-top-left-radius:0}.fc-direction-ltr .fc-daygrid-block-event:not(.fc-event-end),.fc-direction-rtl .fc-daygrid-block-event:not(.fc-event-start){border-bottom-right-radius:0;border-right-width:0;border-top-right-radius:0}.fc-h-event:not(.fc-event-selected) .fc-event-resizer{bottom:0;top:0;width:var(--fc-event-resizer-thickness)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end{cursor:w-resize;left:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-direction-ltr .fc-h-event:not(.fc-event-selected) .fc-event-resizer-end,.fc-direction-rtl .fc-h-event:not(.fc-event-selected) .fc-event-resizer-start{cursor:e-resize;right:calc(var(--fc-event-resizer-thickness)*-.5)}.fc-h-event.fc-event-selected .fc-event-resizer{margin-top:calc(var(--fc-event-resizer-dot-total-width)*-.5);top:50%}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-start,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-end{left:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc-direction-ltr .fc-h-event.fc-event-selected .fc-event-resizer-end,.fc-direction-rtl .fc-h-event.fc-event-selected .fc-event-resizer-start{right:calc(var(--fc-event-resizer-dot-total-width)*-.5)}.fc .fc-popover{box-shadow:0 2px 6px rgba(0,0,0,.15);position:absolute;z-index:9999}.fc .fc-popover-header{align-items:center;display:flex;flex-direction:row;justify-content:space-between;padding:3px 4px}.fc .fc-popover-title{margin:0 2px}.fc .fc-popover-close{cursor:pointer;font-size:1.1em;opacity:.65}.fc-theme-standard .fc-popover{background:var(--fc-page-bg-color);border:1px solid var(--fc-border-color)}.fc-theme-standard .fc-popover-header{background:var(--fc-neutral-bg-color)}');class p{constructor(e){this.drainedOption=e,this.isRunning=!1,this.isDirty=!1,this.pauseDepths={},this.timeoutId=0}request(e){this.isDirty=!0,this.isPaused()||(this.clearTimeout(),null==e?this.tryDrain():this.timeoutId=setTimeout(this.tryDrain.bind(this),e))}pause(e=""){let{pauseDepths:t}=this;t[e]=(t[e]||0)+1,this.clearTimeout()}resume(e="",t){let{pauseDepths:n}=this;e in n&&(t?delete n[e]:(n[e]-=1,n[e]<=0&&delete n[e]),this.tryDrain())}isPaused(){return Object.keys(this.pauseDepths).length}tryDrain(){if(!this.isRunning&&!this.isPaused()){for(this.isRunning=!0;this.isDirty;)this.isDirty=!1,this.drained();this.isRunning=!1}}clear(){this.clearTimeout(),this.isDirty=!1,this.pauseDepths={}}clearTimeout(){this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=0)}drained(){this.drainedOption&&this.drainedOption()}}function g(e){e.parentNode&&e.parentNode.removeChild(e)}function m(e,t){if(e.closest)return e.closest(t);if(!document.documentElement.contains(e))return null;do{var n,r;if(n=e,r=t,(n.matches||n.matchesSelector||n.msMatchesSelector).call(n,r))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}let v=/(top|left|right|bottom|width|height)$/i;function b(e,t,n){null==n?e.style[t]="":"number"==typeof n&&v.test(t)?e.style[t]=`${n}px`:e.style[t]=n}let y=0;function A(){return"fc-dom-"+(y+=1)}function _(e,t,n,r){let i=e=>{let t=m(e.target,n);t&&r.call(t,e,t)};return e.addEventListener(t,i),()=>{e.removeEventListener(t,i)}}function E(e,t,n,r){let i;return _(e,"mouseover",t,(e,t)=>{if(t!==i){i=t,n(e,t);let s=e=>{i=null,r(e,t),t.removeEventListener("mouseleave",s)};t.addEventListener("mouseleave",s)}})}function D(e){return Object.assign({onClick:e},C(e))}function C(e){return{tabIndex:0,onKeyDown(t){("Enter"===t.key||" "===t.key)&&(e(t),t.preventDefault())}}}let w=0;function R(){return String(w+=1)}function S(e,t){let n=String(e);return"000".substr(0,t-n.length)+n}function k(e,t,n){return"function"==typeof e?e(...t):"string"==typeof e?t.reduce((e,t,n)=>e.replace("$"+n,t||""),e):n}let x=/^(-?)(?:(\d+)\.)?(\d+):(\d\d)(?::(\d\d)(?:\.(\d\d\d))?)?/;function T(e,t){return"string"==typeof e?function(e){let t=x.exec(e);if(t){let e=t[1]?-1:1;return{years:0,months:0,days:e*(t[2]?parseInt(t[2],10):0),milliseconds:e*((t[3]?parseInt(t[3],10):0)*36e5+(t[4]?parseInt(t[4],10):0)*6e4+(t[5]?parseInt(t[5],10):0)*1e3+(t[6]?parseInt(t[6],10):0))}}return null}(e):"object"==typeof e&&e?M(e):"number"==typeof e?M({[t||"milliseconds"]:e}):null}function M(e){let t={years:e.years||e.year||0,months:e.months||e.month||0,days:e.days||e.day||0,milliseconds:36e5*(e.hours||e.hour||0)+6e4*(e.minutes||e.minute||0)+1e3*(e.seconds||e.second||0)+(e.milliseconds||e.millisecond||e.ms||0)},n=e.weeks||e.week;return n&&(t.days+=7*n,t.specifiedWeeks=!0),t}function O(e,t){return{years:e.years-t.years,months:e.months-t.months,days:e.days-t.days,milliseconds:e.milliseconds-t.milliseconds}}function I(e){return z(e)/864e5}function z(e){return e.years*(365*864e5)+e.months*(30*864e5)+864e5*e.days+e.milliseconds}function N(e){let t=e.milliseconds;if(t){if(t%1e3!=0)return{unit:"millisecond",value:t};if(t%6e4!=0)return{unit:"second",value:t/1e3};if(t%36e5!=0)return{unit:"minute",value:t/6e4};if(t)return{unit:"hour",value:t/36e5}}return e.days?e.specifiedWeeks&&e.days%7==0?{unit:"week",value:e.days/7}:{unit:"day",value:e.days}:e.months?{unit:"month",value:e.months}:e.years?{unit:"year",value:e.years}:{unit:"millisecond",value:0}}function H(e,t,n){let r;if(e===t)return!0;let i=e.length;if(i!==t.length)return!1;for(r=0;r<i;r+=1)if(!(n?n(e[r],t[r]):e[r]===t[r]))return!1;return!0}let P=["sun","mon","tue","wed","thu","fri","sat"];function B(e,t){let n=q(e);return n[2]+=7*t,Z(n)}function j(e,t){let n=q(e);return n[2]+=t,Z(n)}function U(e,t){let n=q(e);return n[6]+=t,Z(n)}function F(e,t){return W(e,t)/7}function W(e,t){return(t.valueOf()-e.valueOf())/864e5}function V(e,t){return J(e)===J(t)?Math.round(W(e,t)):null}function L(e){return Z([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate()])}function Q(e,t,n,r){let i;return Math.floor(Math.round(W(Z([t,0,1+(-((7+Z([t,0,i=7+n-r]).getUTCDay()-n)%7)+i-1)]),L(e)))/7)+1}function G(e){return[e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()]}function Y(e){return new Date(e[0],e[1]||0,null==e[2]?1:e[2],e[3]||0,e[4]||0,e[5]||0)}function q(e){return[e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()]}function Z(e){return 1===e.length&&(e=e.concat([0])),new Date(Date.UTC(...e))}function $(e){return!isNaN(e.valueOf())}function J(e){return 36e5*e.getUTCHours()+6e4*e.getUTCMinutes()+1e3*e.getUTCSeconds()+e.getUTCMilliseconds()}function X(e){return e.toISOString().replace(/T.*$/,"")}function K(e){return e.toISOString().match(/^\d{4}-\d{2}/)[0]}function ee(e,t=!1){let n=e<0?"-":"+",r=Math.abs(e),i=Math.floor(r/60),s=Math.round(r%60);return t?`${n+S(i,2)}:${S(s,2)}`:`GMT${n}${i}${s?`:${S(s,2)}`:""}`}function et(e,t,n){let r,i;return function(...s){if(r){if(!H(r,s)){n&&n(i);let r=e.apply(this,s);t&&t(r,i)||(i=r)}}else i=e.apply(this,s);return r=s,i}}function en(e,t,n){let r,i;return s=>{if(r){if(!ez(r,s)){n&&n(i);let r=e.call(this,s);t&&t(r,i)||(i=r)}}else i=e.call(this,s);return r=s,i}}let er={week:3,separator:9,omitZeroMinute:9,meridiem:9,omitCommas:9},ei={timeZoneName:7,era:6,year:5,month:4,day:2,weekday:2,hour:1,minute:1,second:1},es=/\s*([ap])\.?m\.?/i,ea=/,/g,eo=/\s+/g,el=/\u200e/g,ec=/UTC|GMT/;class ed{constructor(e){let t={},n={},r=9;for(let i in e)i in er?(n[i]=e[i],er[i]<9&&(r=Math.min(er[i],r))):(t[i]=e[i],i in ei&&(r=Math.min(ei[i],r)));this.standardDateProps=t,this.extendedSettings=n,this.smallestUnitNum=r,this.buildFormattingFunc=et(eu)}format(e,t){return this.buildFormattingFunc(this.standardDateProps,this.extendedSettings,t)(e)}formatRange(e,t,n,r){var i,s,a;let{standardDateProps:o,extendedSettings:l}=this,c=(i=e.marker,s=t.marker,(a=n.calendarSystem).getMarkerYear(i)!==a.getMarkerYear(s)?5:a.getMarkerMonth(i)!==a.getMarkerMonth(s)?4:a.getMarkerDay(i)!==a.getMarkerDay(s)?2:J(i)!==J(s)?1:0);if(!c)return this.format(e,n);let d=c;d>1&&("numeric"===o.year||"2-digit"===o.year)&&("numeric"===o.month||"2-digit"===o.month)&&("numeric"===o.day||"2-digit"===o.day)&&(d=1);let u=this.format(e,n),f=this.format(t,n);if(u===f)return u;let h=eu(function(e,t){let n={};for(let r in e)r in ei&&!(ei[r]<=t)||(n[r]=e[r]);return n}(o,d),l,n),p=h(e),g=h(t),m=function(e,t,n,r){let i=0;for(;i<e.length;){let s=e.indexOf(t,i);if(-1===s)break;let a=e.substr(0,s);i=s+t.length;let o=e.substr(i),l=0;for(;l<n.length;){let e=n.indexOf(r,l);if(-1===e)break;let t=n.substr(0,e);l=e+r.length;let i=n.substr(l);if(a===t&&o===i)return{before:a,after:o}}}return null}(u,p,f,g),v=l.separator||r||n.defaultSeparator||"";return m?m.before+p+v+g+m.after:u+v+f}getSmallestUnit(){switch(this.smallestUnitNum){case 7:case 6:case 5:return"year";case 4:return"month";case 3:return"week";case 2:return"day";default:return"time"}}}function eu(e,t,n){let r=Object.keys(e).length;return 1===r&&"short"===e.timeZoneName?e=>ee(e.timeZoneOffset):0===r&&t.week?e=>{var r,i,s,a,o;let l;return r=n.computeWeekNumber(e.marker),i=n.weekText,s=n.weekTextLong,a=n.locale,o=t.week,l=[],"long"===o?l.push(s):("short"===o||"narrow"===o)&&l.push(i),("long"===o||"short"===o)&&l.push(" "),l.push(a.simpleNumberFormat.format(r)),"rtl"===a.options.direction&&l.reverse(),l.join("")}:function(e,t,n){var r,i;let s;e=Object.assign({},e),t=Object.assign({},t),r=e,i=t,r.timeZoneName&&(r.hour||(r.hour="2-digit"),r.minute||(r.minute="2-digit")),"long"===r.timeZoneName&&(r.timeZoneName="short"),i.omitZeroMinute&&(r.second||r.millisecond)&&delete i.omitZeroMinute,e.timeZone="UTC";let a=new Intl.DateTimeFormat(n.locale.codes,e);if(t.omitZeroMinute){let t=Object.assign({},e);delete t.minute,s=new Intl.DateTimeFormat(n.locale.codes,t)}return r=>{var i,o,l,c,d;let u,{marker:f}=r;return i=(s&&!f.getUTCMinutes()?s:a).format(f),o=e,l=t,i=i.replace(el,""),"short"===o.timeZoneName&&(c=i,d="UTC"===n.timeZone||null==r.timeZoneOffset?"UTC":ee(r.timeZoneOffset),u=!1,c=c.replace(ec,()=>(u=!0,d)),u||(c+=` ${d}`),i=c),l.omitCommas&&(i=i.replace(ea,"").trim()),l.omitZeroMinute&&(i=i.replace(":00","")),!1===l.meridiem?i=i.replace(es,"").trim():"narrow"===l.meridiem?i=i.replace(es,(e,t)=>t.toLocaleLowerCase()):"short"===l.meridiem?i=i.replace(es,(e,t)=>`${t.toLocaleLowerCase()}m`):"lowercase"===l.meridiem&&(i=i.replace(es,e=>e.toLocaleLowerCase())),i=(i=i.replace(eo," ")).trim()}}(e,t,n)}function ef(e,t){let n=t.markerToArray(e.marker);return{marker:e.marker,timeZoneOffset:e.timeZoneOffset,array:n,year:n[0],month:n[1],day:n[2],hour:n[3],minute:n[4],second:n[5],millisecond:n[6]}}function eh(e,t,n,r){let i=ef(e,n.calendarSystem);return{date:i,start:i,end:t?ef(t,n.calendarSystem):null,timeZone:n.timeZone,localeCodes:n.locale.codes,defaultSeparator:r||n.defaultSeparator}}class ep{constructor(e){this.cmdStr=e}format(e,t,n){return t.cmdFormatter(this.cmdStr,eh(e,null,t,n))}formatRange(e,t,n,r){return n.cmdFormatter(this.cmdStr,eh(e,t,n,r))}}class eg{constructor(e){this.func=e}format(e,t,n){return this.func(eh(e,null,t,n))}formatRange(e,t,n,r){return this.func(eh(e,t,n,r))}}function em(e){return"object"==typeof e&&e?new ed(e):"string"==typeof e?new ep(e):"function"==typeof e?new eg(e):null}let ev={navLinkDayClick:eS,navLinkWeekClick:eS,duration:T,bootstrapFontAwesome:eS,buttonIcons:eS,customButtons:eS,defaultAllDayEventDuration:T,defaultTimedEventDuration:T,nextDayThreshold:T,scrollTime:T,scrollTimeReset:Boolean,slotMinTime:T,slotMaxTime:T,dayPopoverFormat:em,slotDuration:T,snapDuration:T,headerToolbar:eS,footerToolbar:eS,defaultRangeSeparator:String,titleRangeSeparator:String,forceEventDuration:Boolean,dayHeaders:Boolean,dayHeaderFormat:em,dayHeaderClassNames:eS,dayHeaderContent:eS,dayHeaderDidMount:eS,dayHeaderWillUnmount:eS,dayCellClassNames:eS,dayCellContent:eS,dayCellDidMount:eS,dayCellWillUnmount:eS,initialView:String,aspectRatio:Number,weekends:Boolean,weekNumberCalculation:eS,weekNumbers:Boolean,weekNumberClassNames:eS,weekNumberContent:eS,weekNumberDidMount:eS,weekNumberWillUnmount:eS,editable:Boolean,viewClassNames:eS,viewDidMount:eS,viewWillUnmount:eS,nowIndicator:Boolean,nowIndicatorClassNames:eS,nowIndicatorContent:eS,nowIndicatorDidMount:eS,nowIndicatorWillUnmount:eS,showNonCurrentDates:Boolean,lazyFetching:Boolean,startParam:String,endParam:String,timeZoneParam:String,timeZone:String,locales:eS,locale:eS,themeSystem:String,dragRevertDuration:Number,dragScroll:Boolean,allDayMaintainDuration:Boolean,unselectAuto:Boolean,dropAccept:eS,eventOrder:function(e){let t,n,r=[],i=[];for("string"==typeof e?i=e.split(/\s*,\s*/):"function"==typeof e?i=[e]:Array.isArray(e)&&(i=e),t=0;t<i.length;t+=1)"string"==typeof(n=i[t])?r.push("-"===n.charAt(0)?{field:n.substring(1),order:-1}:{field:n,order:1}):"function"==typeof n&&r.push({func:n});return r},eventOrderStrict:Boolean,handleWindowResize:Boolean,windowResizeDelay:Number,longPressDelay:Number,eventDragMinDistance:Number,expandRows:Boolean,height:eS,contentHeight:eS,direction:String,weekNumberFormat:em,eventResizableFromStart:Boolean,displayEventTime:Boolean,displayEventEnd:Boolean,weekText:String,weekTextLong:String,progressiveEventRendering:Boolean,businessHours:eS,initialDate:eS,now:eS,eventDataTransform:eS,stickyHeaderDates:eS,stickyFooterScrollbar:eS,viewHeight:eS,defaultAllDay:Boolean,eventSourceFailure:eS,eventSourceSuccess:eS,eventDisplay:String,eventStartEditable:Boolean,eventDurationEditable:Boolean,eventOverlap:eS,eventConstraint:eS,eventAllow:eS,eventBackgroundColor:String,eventBorderColor:String,eventTextColor:String,eventColor:String,eventClassNames:eS,eventContent:eS,eventDidMount:eS,eventWillUnmount:eS,selectConstraint:eS,selectOverlap:eS,selectAllow:eS,droppable:Boolean,unselectCancel:String,slotLabelFormat:eS,slotLaneClassNames:eS,slotLaneContent:eS,slotLaneDidMount:eS,slotLaneWillUnmount:eS,slotLabelClassNames:eS,slotLabelContent:eS,slotLabelDidMount:eS,slotLabelWillUnmount:eS,dayMaxEvents:eS,dayMaxEventRows:eS,dayMinWidth:Number,slotLabelInterval:T,allDayText:String,allDayClassNames:eS,allDayContent:eS,allDayDidMount:eS,allDayWillUnmount:eS,slotMinWidth:Number,navLinks:Boolean,eventTimeFormat:em,rerenderDelay:Number,moreLinkText:eS,moreLinkHint:eS,selectMinDistance:Number,selectable:Boolean,selectLongPressDelay:Number,eventLongPressDelay:Number,selectMirror:Boolean,eventMaxStack:Number,eventMinHeight:Number,eventMinWidth:Number,eventShortHeight:Number,slotEventOverlap:Boolean,plugins:eS,firstDay:Number,dayCount:Number,dateAlignment:String,dateIncrement:T,hiddenDays:eS,fixedWeekCount:Boolean,validRange:eS,visibleRange:eS,titleFormat:eS,eventInteractive:Boolean,noEventsText:String,viewHint:eS,navLinkHint:eS,closeHint:String,timeHint:String,eventHint:String,moreLinkClick:eS,moreLinkClassNames:eS,moreLinkContent:eS,moreLinkDidMount:eS,moreLinkWillUnmount:eS,monthStartFormat:em,handleCustomRendering:eS,customRenderingMetaMap:eS,customRenderingReplaces:Boolean},eb={eventDisplay:"auto",defaultRangeSeparator:" - ",titleRangeSeparator:" – ",defaultTimedEventDuration:"01:00:00",defaultAllDayEventDuration:{day:1},forceEventDuration:!1,nextDayThreshold:"00:00:00",dayHeaders:!0,initialView:"",aspectRatio:1.35,headerToolbar:{start:"title",center:"",end:"today prev,next"},weekends:!0,weekNumbers:!1,weekNumberCalculation:"local",editable:!1,nowIndicator:!1,scrollTime:"06:00:00",scrollTimeReset:!0,slotMinTime:"00:00:00",slotMaxTime:"24:00:00",showNonCurrentDates:!0,lazyFetching:!0,startParam:"start",endParam:"end",timeZoneParam:"timeZone",timeZone:"local",locales:[],locale:"",themeSystem:"standard",dragRevertDuration:500,dragScroll:!0,allDayMaintainDuration:!1,unselectAuto:!0,dropAccept:"*",eventOrder:"start,-duration,allDay,title",dayPopoverFormat:{month:"long",day:"numeric",year:"numeric"},handleWindowResize:!0,windowResizeDelay:100,longPressDelay:1e3,eventDragMinDistance:5,expandRows:!1,navLinks:!1,selectable:!1,eventMinHeight:15,eventMinWidth:30,eventShortHeight:30,monthStartFormat:{month:"long",day:"numeric"}},ey={datesSet:eS,eventsSet:eS,eventAdd:eS,eventChange:eS,eventRemove:eS,windowResize:eS,eventClick:eS,eventMouseEnter:eS,eventMouseLeave:eS,select:eS,unselect:eS,loading:eS,_unmount:eS,_beforeprint:eS,_afterprint:eS,_noEventDrop:eS,_noEventResize:eS,_resize:eS,_scrollRequest:eS},eA={buttonText:eS,buttonHints:eS,views:eS,plugins:eS,initialEvents:eS,events:eS,eventSources:eS},e_={headerToolbar:eE,footerToolbar:eE,buttonText:eE,buttonHints:eE,buttonIcons:eE,dateIncrement:eE,plugins:eD,events:eD,eventSources:eD,resources:eD};function eE(e,t){return"object"==typeof e&&"object"==typeof t&&e&&t?ez(e,t):e===t}function eD(e,t){return Array.isArray(e)&&Array.isArray(t)?H(e,t):e===t}let eC={type:String,component:eS,buttonText:String,buttonTextKey:String,dateProfileGeneratorClass:eS,usesMinMaxTime:Boolean,classNames:eS,content:eS,didMount:eS,willUnmount:eS};function ew(e){return ex(e,e_)}function eR(e,t){let n={},r={};for(let r in t)r in e&&(n[r]=t[r](e[r]));for(let n in e)n in t||(r[n]=e[n]);return{refined:n,extra:r}}function eS(e){return e}let{hasOwnProperty:ek}=Object.prototype;function ex(e,t){let n={};if(t){for(let r in t)if(t[r]===eE){let t=[];for(let i=e.length-1;i>=0;i-=1){let s=e[i][r];if("object"==typeof s&&s)t.unshift(s);else if(void 0!==s){n[r]=s;break}}t.length&&(n[r]=ex(t))}}for(let t=e.length-1;t>=0;t-=1){let r=e[t];for(let e in r)e in n||(n[e]=r[e])}return n}function eT(e,t){let n={};for(let r in e)t(e[r],r)&&(n[r]=e[r]);return n}function eM(e,t){let n={};for(let r in e)n[r]=t(e[r],r);return n}function eO(e){let t={};for(let n of e)t[n]=!0;return t}function eI(e){let t=[];for(let n in e)t.push(e[n]);return t}function ez(e,t){if(e===t)return!0;for(let n in e)if(ek.call(e,n)&&!(n in t))return!1;for(let n in t)if(ek.call(t,n)&&e[n]!==t[n])return!1;return!0}let eN=/^on[A-Z]/;function eH(e,t,n={}){if(e===t)return!0;for(let a in t){var r,i,s;if(!(a in e&&(r=e[a],i=t[a],s=n[a],r===i||!0===s||s&&s(r,i))))return!1}for(let n in e)if(!(n in t))return!1;return!0}let eP={};class eB{getMarkerYear(e){return e.getUTCFullYear()}getMarkerMonth(e){return e.getUTCMonth()}getMarkerDay(e){return e.getUTCDate()}arrayToMarker(e){return Z(e)}markerToArray(e){return q(e)}}eP.gregory=eB;let ej=/^\s*(\d{4})(-?(\d{2})(-?(\d{2})([T ](\d{2}):?(\d{2})(:?(\d{2})(\.(\d+))?)?(Z|(([-+])(\d{2})(:?(\d{2}))?))?)?)?)?$/;class eU{constructor(e){let t=this.timeZone=e.timeZone,n="local"!==t&&"UTC"!==t;e.namedTimeZoneImpl&&n&&(this.namedTimeZoneImpl=new e.namedTimeZoneImpl(t)),this.canComputeOffset=!!(!n||this.namedTimeZoneImpl),this.calendarSystem=new eP[e.calendarSystem],this.locale=e.locale,this.weekDow=e.locale.week.dow,this.weekDoy=e.locale.week.doy,"ISO"===e.weekNumberCalculation&&(this.weekDow=1,this.weekDoy=4),"number"==typeof e.firstDay&&(this.weekDow=e.firstDay),"function"==typeof e.weekNumberCalculation&&(this.weekNumberFunc=e.weekNumberCalculation),this.weekText=null!=e.weekText?e.weekText:e.locale.options.weekText,this.weekTextLong=(null!=e.weekTextLong?e.weekTextLong:e.locale.options.weekTextLong)||this.weekText,this.cmdFormatter=e.cmdFormatter,this.defaultSeparator=e.defaultSeparator}createMarker(e){let t=this.createMarkerMeta(e);return null===t?null:t.marker}createNowMarker(){return this.canComputeOffset?this.timestampToMarker(new Date().valueOf()):Z(G(new Date))}createMarkerMeta(e){if("string"==typeof e)return this.parse(e);let t=null;return("number"==typeof e?t=this.timestampToMarker(e):e instanceof Date?isNaN(e=e.valueOf())||(t=this.timestampToMarker(e)):Array.isArray(e)&&(t=Z(e)),null!==t&&$(t))?{marker:t,isTimeUnspecified:!1,forcedTzo:null}:null}parse(e){let t=function(e){let t=ej.exec(e);if(t){let e=new Date(Date.UTC(Number(t[1]),t[3]?Number(t[3])-1:0,Number(t[5]||1),Number(t[7]||0),Number(t[8]||0),Number(t[10]||0),t[12]?1e3*Number(`0.${t[12]}`):0));if($(e)){let n=null;return t[13]&&(n=("-"===t[15]?-1:1)*(60*Number(t[16]||0)+Number(t[18]||0))),{marker:e,isTimeUnspecified:!t[6],timeZoneOffset:n}}}return null}(e);if(null===t)return null;let{marker:n}=t,r=null;return null!==t.timeZoneOffset&&(this.canComputeOffset?n=this.timestampToMarker(n.valueOf()-6e4*t.timeZoneOffset):r=t.timeZoneOffset),{marker:n,isTimeUnspecified:t.isTimeUnspecified,forcedTzo:r}}getYear(e){return this.calendarSystem.getMarkerYear(e)}getMonth(e){return this.calendarSystem.getMarkerMonth(e)}getDay(e){return this.calendarSystem.getMarkerDay(e)}add(e,t){let n=this.calendarSystem.markerToArray(e);return n[0]+=t.years,n[1]+=t.months,n[2]+=t.days,n[6]+=t.milliseconds,this.calendarSystem.arrayToMarker(n)}subtract(e,t){let n=this.calendarSystem.markerToArray(e);return n[0]-=t.years,n[1]-=t.months,n[2]-=t.days,n[6]-=t.milliseconds,this.calendarSystem.arrayToMarker(n)}addYears(e,t){let n=this.calendarSystem.markerToArray(e);return n[0]+=t,this.calendarSystem.arrayToMarker(n)}addMonths(e,t){let n=this.calendarSystem.markerToArray(e);return n[1]+=t,this.calendarSystem.arrayToMarker(n)}diffWholeYears(e,t){let{calendarSystem:n}=this;return J(e)===J(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)&&n.getMarkerMonth(e)===n.getMarkerMonth(t)?n.getMarkerYear(t)-n.getMarkerYear(e):null}diffWholeMonths(e,t){let{calendarSystem:n}=this;return J(e)===J(t)&&n.getMarkerDay(e)===n.getMarkerDay(t)?n.getMarkerMonth(t)-n.getMarkerMonth(e)+(n.getMarkerYear(t)-n.getMarkerYear(e))*12:null}greatestWholeUnit(e,t){let n,r=this.diffWholeYears(e,t);return null!==r?{unit:"year",value:r}:null!==(r=this.diffWholeMonths(e,t))?{unit:"month",value:r}:null!==(r=null!==(n=V(e,t))&&n%7==0?n/7:null)?{unit:"week",value:r}:null!==(r=V(e,t))?{unit:"day",value:r}:(r=(t.valueOf()-e.valueOf())/36e5)%1==0?{unit:"hour",value:r}:(r=(t.valueOf()-e.valueOf())/6e4)%1==0?{unit:"minute",value:r}:(r=(t.valueOf()-e.valueOf())/1e3)%1==0?{unit:"second",value:r}:{unit:"millisecond",value:t.valueOf()-e.valueOf()}}countDurationsBetween(e,t,n){let r;return n.years&&null!==(r=this.diffWholeYears(e,t))?r/(I(n)/365):n.months&&null!==(r=this.diffWholeMonths(e,t))?r/(I(n)/30):n.days&&null!==(r=V(e,t))?r/I(n):(t.valueOf()-e.valueOf())/z(n)}startOf(e,t){return"year"===t?this.startOfYear(e):"month"===t?this.startOfMonth(e):"week"===t?this.startOfWeek(e):"day"===t?L(e):"hour"===t?Z([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours()]):"minute"===t?Z([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes()]):"second"===t?Z([e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds()]):null}startOfYear(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e)])}startOfMonth(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e)])}startOfWeek(e){return this.calendarSystem.arrayToMarker([this.calendarSystem.getMarkerYear(e),this.calendarSystem.getMarkerMonth(e),e.getUTCDate()-(e.getUTCDay()-this.weekDow+7)%7])}computeWeekNumber(e){return this.weekNumberFunc?this.weekNumberFunc(this.toDate(e)):function(e,t,n){let r=e.getUTCFullYear(),i=Q(e,r,t,n);if(i<1)return Q(e,r-1,t,n);let s=Q(e,r+1,t,n);return s>=1?Math.min(i,s):i}(e,this.weekDow,this.weekDoy)}format(e,t,n={}){return t.format({marker:e,timeZoneOffset:null!=n.forcedTzo?n.forcedTzo:this.offsetForMarker(e)},this)}formatRange(e,t,n,r={}){return r.isEndExclusive&&(t=U(t,-1)),n.formatRange({marker:e,timeZoneOffset:null!=r.forcedStartTzo?r.forcedStartTzo:this.offsetForMarker(e)},{marker:t,timeZoneOffset:null!=r.forcedEndTzo?r.forcedEndTzo:this.offsetForMarker(t)},this,r.defaultSeparator)}formatIso(e,t={}){let n=null;return t.omitTimeZoneOffset||(n=null!=t.forcedTzo?t.forcedTzo:this.offsetForMarker(e)),function(e,t,n=!1){let r=e.toISOString();return r=r.replace(".000",""),n&&(r=r.replace("T00:00:00Z","")),r.length>10&&(null==t?r=r.replace("Z",""):0!==t&&(r=r.replace("Z",ee(t,!0)))),r}(e,n,t.omitTime)}timestampToMarker(e){return"local"===this.timeZone?Z(G(new Date(e))):"UTC"!==this.timeZone&&this.namedTimeZoneImpl?Z(this.namedTimeZoneImpl.timestampToArray(e)):new Date(e)}offsetForMarker(e){return"local"===this.timeZone?-Y(q(e)).getTimezoneOffset():"UTC"===this.timeZone?0:this.namedTimeZoneImpl?this.namedTimeZoneImpl.offsetForArray(q(e)):null}toDate(e,t){return"local"===this.timeZone?Y(q(e)):new Date("UTC"===this.timeZone?e.valueOf():this.namedTimeZoneImpl?e.valueOf()-6e4*this.namedTimeZoneImpl.offsetForArray(q(e)):e.valueOf()-(t||0))}}class eF{constructor(e){this.iconOverrideOption&&this.setIconOverride(e[this.iconOverrideOption])}setIconOverride(e){let t,n;if("object"==typeof e&&e){for(n in t=Object.assign({},this.iconClasses),e)t[n]=this.applyIconOverridePrefix(e[n]);this.iconClasses=t}else!1===e&&(this.iconClasses={})}applyIconOverridePrefix(e){let t=this.iconOverridePrefix;return t&&0!==e.indexOf(t)&&(e=t+e),e}getClass(e){return this.classes[e]||""}getIconClass(e,t){let n;return(n=t&&this.rtlIconClasses&&this.rtlIconClasses[e]||this.iconClasses[e])?`${this.baseIconClass} ${n}`:""}getCustomButtonIconClass(e){let t;return this.iconOverrideCustomButtonOption&&(t=e[this.iconOverrideCustomButtonOption])?`${this.baseIconClass} ${this.applyIconOverridePrefix(t)}`:""}}function eW(e){e();let t=a.YM.debounceRendering,n=[];for(a.YM.debounceRendering=function(e){n.push(e)},a.sY(a.az(eV,{}),document.createElement("div"));n.length;)n.shift()();a.YM.debounceRendering=t}eF.prototype.classes={},eF.prototype.iconClasses={},eF.prototype.baseIconClass="",eF.prototype.iconOverridePrefix="";class eV extends a.wA{render(){return a.az("div",{})}componentDidMount(){this.setState({})}}function eL(e){let t=a.kr(e),n=t.Provider;return t.Provider=function(){let e=!this.getChildContext,t=n.apply(this,arguments);if(e){let e=[];this.shouldComponentUpdate=t=>{this.props.value!==t.value&&e.forEach(e=>{e.context=t.value,e.forceUpdate()})},this.sub=t=>{e.push(t);let n=t.componentWillUnmount;t.componentWillUnmount=()=>{e.splice(e.indexOf(t),1),n&&n.call(t)}}}return t},t}class eQ{constructor(e,t,n,r){this.execFunc=e,this.emitter=t,this.scrollTime=n,this.scrollTimeReset=r,this.handleScrollRequest=e=>{this.queuedRequest=Object.assign({},this.queuedRequest||{},e),this.drain()},t.on("_scrollRequest",this.handleScrollRequest),this.fireInitialScroll()}detach(){this.emitter.off("_scrollRequest",this.handleScrollRequest)}update(e){e&&this.scrollTimeReset?this.fireInitialScroll():this.drain()}fireInitialScroll(){this.handleScrollRequest({time:this.scrollTime})}drain(){this.queuedRequest&&this.execFunc(this.queuedRequest)&&(this.queuedRequest=null)}}let eG=eL({});function eY(e,t,n,r,i,s,a,o,l,c,d,u,f,h){return{dateEnv:i,nowManager:s,options:n,pluginHooks:o,emitter:d,dispatch:l,getCurrentData:c,calendarApi:u,viewSpec:e,viewApi:t,dateProfileGenerator:r,theme:a,isRtl:"rtl"===n.direction,addResizeHandler(e){d.on("_resize",e)},removeResizeHandler(e){d.off("_resize",e)},createScrollResponder:e=>new eQ(e,d,T(n.scrollTime),n.scrollTimeReset),registerInteractiveComponent:f,unregisterInteractiveComponent:h}}class eq extends a.wA{shouldComponentUpdate(e,t){return!eH(this.props,e,this.propEquality)||!eH(this.state,t,this.stateEquality)}safeSetState(e){eH(this.state,Object.assign(Object.assign({},this.state),e),this.stateEquality)||this.setState(e)}}eq.addPropsEquality=function(e){let t=Object.create(this.prototype.propEquality);Object.assign(t,e),this.prototype.propEquality=t},eq.addStateEquality=function(e){let t=Object.create(this.prototype.stateEquality);Object.assign(t,e),this.prototype.stateEquality=t},eq.contextType=eG,eq.prototype.propEquality={},eq.prototype.stateEquality={};class eZ extends eq{}function e$(e,t){"function"==typeof e?e(t):e&&(e.current=t)}eZ.contextType=eG;class eJ extends eZ{constructor(){super(...arguments),this.id=R(),this.queuedDomNodes=[],this.currentDomNodes=[],this.handleEl=e=>{let{options:t}=this.context,{generatorName:n}=this.props;t.customRenderingReplaces&&eX(n,t)||this.updateElRef(e)},this.updateElRef=e=>{this.props.elRef&&e$(this.props.elRef,e)}}render(){let e,t;let{props:n,context:r}=this,{options:i}=r,{customGenerator:s,defaultGenerator:o,renderProps:l}=n,c=eK(n,[],this.handleEl),d=!1,u=[];if(null!=s){let n="function"==typeof s?s(l,a.az):s;if(!0===n)d=!0;else{let r=n&&"object"==typeof n;r&&"html"in n?c.dangerouslySetInnerHTML={__html:n.html}:r&&"domNodes"in n?u=Array.prototype.slice.call(n.domNodes):(r?(0,a.l$)(n):"function"!=typeof n)?e=n:t=n}}else d=!eX(n.generatorName,i);return d&&o&&(e=o(l)),this.queuedDomNodes=u,this.currentGeneratorMeta=t,(0,a.az)(n.elTag,c,e)}componentDidMount(){this.applyQueueudDomNodes(),this.triggerCustomRendering(!0)}componentDidUpdate(){this.applyQueueudDomNodes(),this.triggerCustomRendering(!0)}componentWillUnmount(){this.triggerCustomRendering(!1)}triggerCustomRendering(e){var t;let{props:n,context:r}=this,{handleCustomRendering:i,customRenderingMetaMap:s}=r.options;if(i){let r=null!==(t=this.currentGeneratorMeta)&&void 0!==t?t:null==s?void 0:s[n.generatorName];r&&i(Object.assign(Object.assign({id:this.id,isActive:e,containerEl:this.base,reportNewContainerEl:this.updateElRef,generatorMeta:r},n),{elClasses:(n.elClasses||[]).filter(e0)}))}}applyQueueudDomNodes(){let{queuedDomNodes:e,currentDomNodes:t}=this,n=this.base;if(!H(e,t)){for(let r of(t.forEach(g),e))n.appendChild(r);this.currentDomNodes=e}}}function eX(e,t){var n;return!!(t.handleCustomRendering&&e&&(null===(n=t.customRenderingMetaMap)||void 0===n?void 0:n[e]))}function eK(e,t,n){let r=Object.assign(Object.assign({},e.elAttrs),{ref:n});return(e.elClasses||t)&&(r.className=(e.elClasses||[]).concat(t||[]).concat(r.className||[]).filter(Boolean).join(" ")),e.elStyle&&(r.style=e.elStyle),r}function e0(e){return!!e}eJ.addPropsEquality({elClasses:H,elStyle:ez,elAttrs:function(e,t){for(let n of function(e,t){let n=[];for(let r in e)!ek.call(e,r)||r in t||n.push(r);for(let r in t)ek.call(t,r)&&e[r]!==t[r]&&n.push(r);return n}(e,t))if(!eN.test(n))return!1;return!0},renderProps:ez});let e1=eL(0);class e2 extends a.wA{constructor(){super(...arguments),this.InnerContent=e4.bind(void 0,this),this.handleEl=e=>{this.el=e,this.props.elRef&&(e$(this.props.elRef,e),e&&this.didMountMisfire&&this.componentDidMount())}}render(){let{props:e}=this,t=function(e,t){let n="function"==typeof e?e(t):e||[];return"string"==typeof n?[n]:n}(e.classNameGenerator,e.renderProps);if(!e.children)return(0,a.az)(eJ,Object.assign(Object.assign({},e),{elRef:this.handleEl,elTag:e.elTag||"div",elClasses:(e.elClasses||[]).concat(t),renderId:this.context}));{let n=eK(e,t,this.handleEl),r=e.children(this.InnerContent,e.renderProps,n);return e.elTag?(0,a.az)(e.elTag,n,r):r}}componentDidMount(){var e,t;this.el?null===(t=(e=this.props).didMount)||void 0===t||t.call(e,Object.assign(Object.assign({},this.props.renderProps),{el:this.el})):this.didMountMisfire=!0}componentWillUnmount(){var e,t;null===(t=(e=this.props).willUnmount)||void 0===t||t.call(e,Object.assign(Object.assign({},this.props.renderProps),{el:this.el}))}}function e4(e,t){let n=e.props;return(0,a.az)(eJ,Object.assign({renderProps:n.renderProps,generatorName:n.generatorName,customGenerator:n.customGenerator,defaultGenerator:n.defaultGenerator,renderId:e.context},t))}e2.contextType=e1;class e5 extends eZ{render(){let{props:e,context:t}=this,{options:n}=t,r={view:t.viewApi};return(0,a.az)(e2,{elRef:e.elRef,elTag:e.elTag||"div",elAttrs:e.elAttrs,elClasses:[...e3(e.viewSpec),...e.elClasses||[]],elStyle:e.elStyle,renderProps:r,classNameGenerator:n.viewClassNames,generatorName:void 0,didMount:n.viewDidMount,willUnmount:n.viewWillUnmount},()=>e.children)}}function e3(e){return[`fc-${e.type}-view`,"fc-view"]}function e6(e,t){let n,r,i=[],{start:s}=t;for(e.sort(e8),n=0;n<e.length;n+=1)(r=e[n]).start>s&&i.push({start:s,end:r.start}),r.end>s&&(s=r.end);return s<t.end&&i.push({start:s,end:t.end}),i}function e8(e,t){return e.start.valueOf()-t.start.valueOf()}function e7(e,t){let{start:n,end:r}=e,i=null;return null!==t.start&&(n=null===n?t.start:new Date(Math.max(n.valueOf(),t.start.valueOf()))),null!=t.end&&(r=null===r?t.end:new Date(Math.min(r.valueOf(),t.end.valueOf()))),(null===n||null===r||n<r)&&(i={start:n,end:r}),i}function e9(e,t){return(null===e.start||t>=e.start)&&(null===e.end||t<e.end)}function te(e){let t=Math.floor(W(e.start,e.end))||1,n=L(e.start),r=j(n,t);return{start:n,end:r}}function tt(e,t=T(0)){let n=null,r=null;if(e.end){r=L(e.end);let n=e.end.valueOf()-r.valueOf();n&&n>=z(t)&&(r=j(r,1))}return e.start&&(n=L(e.start),r&&r<=n&&(r=j(n,1))),{start:n,end:r}}function tn(e){let t=tt(e);return W(t.start,t.end)>1}function tr(e,t,n,r){let i,s;return"year"===r?T(n.diffWholeYears(e,t),"year"):"month"===r?T(n.diffWholeMonths(e,t),"month"):{years:0,months:0,days:Math.round(W(i=L(e),s=L(t))),milliseconds:t.valueOf()-s.valueOf()-(e.valueOf()-i.valueOf())}}class ti{constructor(e){this.props=e,this.initHiddenDays()}buildPrev(e,t,n){let{dateEnv:r}=this.props,i=r.subtract(r.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(i,-1,n)}buildNext(e,t,n){let{dateEnv:r}=this.props,i=r.add(r.startOf(t,e.currentRangeUnit),e.dateIncrement);return this.build(i,1,n)}build(e,t,n=!0){var r,i,s,a;let o,l,c,d,u,f,{props:h}=this;if(o=this.buildValidRange(),o=this.trimHiddenDays(o),n){r=e,e=null!=(i=o).start&&r<i.start?i.start:null!=i.end&&r>=i.end?new Date(i.end.valueOf()-1):r}return l=this.buildCurrentRangeInfo(e,t),c=/^(year|month|week|day)$/.test(l.unit),d=this.buildRenderRange(this.trimHiddenDays(l.range),l.unit,c),u=d=this.trimHiddenDays(d),h.showNonCurrentDates||(u=e7(u,l.range)),u=e7(u=this.adjustActiveRange(u),o),s=l.range,a=o,f=(null===s.end||null===a.start||s.end>a.start)&&(null===s.start||null===a.end||s.start<a.end),e9(d,e)||(e=d.start),{currentDate:e,validRange:o,currentRange:l.range,currentRangeUnit:l.unit,isRangeAllDay:c,activeRange:u,renderRange:d,slotMinTime:h.slotMinTime,slotMaxTime:h.slotMaxTime,isValid:f,dateIncrement:this.buildDateIncrement(l.duration)}}buildValidRange(){let e=this.props.validRangeInput,t="function"==typeof e?e.call(this.props.calendarApi,this.props.dateEnv.toDate(this.props.nowManager.getDateMarker())):e;return this.refineRange(t)||{start:null,end:null}}buildCurrentRangeInfo(e,t){let n,{props:r}=this,i=null,s=null,a=null;return r.duration?(i=r.duration,s=r.durationUnit,a=this.buildRangeFromDuration(e,t,i,s)):(n=this.props.dayCount)?(s="day",a=this.buildRangeFromDayCount(e,t,n)):(a=this.buildCustomVisibleRange(e))?s=r.dateEnv.greatestWholeUnit(a.start,a.end).unit:(s=N(i=this.getFallbackDuration()).unit,a=this.buildRangeFromDuration(e,t,i,s)),{duration:i,unit:s,range:a}}getFallbackDuration(){return T({day:1})}adjustActiveRange(e){let{dateEnv:t,usesMinMaxTime:n,slotMinTime:r,slotMaxTime:i}=this.props,{start:s,end:a}=e;return n&&(0>I(r)&&(s=L(s),s=t.add(s,r)),I(i)>1&&(a=j(a=L(a),-1),a=t.add(a,i))),{start:s,end:a}}buildRangeFromDuration(e,t,n,r){let i,s,a,{dateEnv:o,dateAlignment:l}=this.props;if(!l){let{dateIncrement:e}=this.props;l=e&&z(e)<z(n)?N(e).unit:r}function c(){i=o.startOf(e,l),s=o.add(i,n),a={start:i,end:s}}return 1>=I(n)&&this.isHiddenDay(i)&&(i=L(i=this.skipHiddenDays(i,t))),c(),this.trimHiddenDays(a)||(e=this.skipHiddenDays(e,t),c()),a}buildRangeFromDayCount(e,t,n){let r,{dateEnv:i,dateAlignment:s}=this.props,a=0,o=e;s&&(o=i.startOf(o,s)),o=L(o),r=o=this.skipHiddenDays(o,t);do r=j(r,1),this.isHiddenDay(r)||(a+=1);while(a<n);return{start:o,end:r}}buildCustomVisibleRange(e){let{props:t}=this,n=t.visibleRangeInput,r="function"==typeof n?n.call(t.calendarApi,t.dateEnv.toDate(e)):n,i=this.refineRange(r);return i&&(null==i.start||null==i.end)?null:i}buildRenderRange(e,t,n){return e}buildDateIncrement(e){let t,{dateIncrement:n}=this.props;return n||((t=this.props.dateAlignment)?T(1,t):e||T({days:1}))}refineRange(e){if(e){var t;let n,r;let i=(t=this.props.dateEnv,n=null,r=null,(e.start&&(n=t.createMarker(e.start)),e.end&&(r=t.createMarker(e.end)),!n&&!r||n&&r&&r<n)?null:{start:n,end:r});return i&&(i=tt(i)),i}return null}initHiddenDays(){let e,t=this.props.hiddenDays||[],n=[],r=0;for(!1===this.props.weekends&&t.push(0,6),e=0;e<7;e+=1)(n[e]=-1!==t.indexOf(e))||(r+=1);if(!r)throw Error("invalid hiddenDays");this.isHiddenDayHash=n}trimHiddenDays(e){let{start:t,end:n}=e;return(t&&(t=this.skipHiddenDays(t)),n&&(n=this.skipHiddenDays(n,-1,!0)),null==t||null==n||t<n)?{start:t,end:n}:null}isHiddenDay(e){return e instanceof Date&&(e=e.getUTCDay()),this.isHiddenDayHash[e]}skipHiddenDays(e,t=1,n=!1){for(;this.isHiddenDayHash[(e.getUTCDay()+(n?t:0)+7)%7];)e=j(e,t);return e}}function ts(e,t,n,r){return{instanceId:R(),defId:e,range:t,forcedStartTzo:null==n?null:n,forcedEndTzo:null==r?null:r}}function ta(e,t,n){let{dateEnv:r,pluginHooks:i,options:s}=n,{defs:a,instances:o}=e;for(let e in o=eT(o,e=>!a[e.defId].recurringDef),a){let n=a[e];if(n.recurringDef){let{duration:a}=n.recurringDef;for(let l of(a||(a=n.allDay?s.defaultAllDayEventDuration:s.defaultTimedEventDuration),function(e,t,n,r,i){let s=i[e.recurringDef.typeId].expand(e.recurringDef.typeData,{start:r.subtract(n.start,t),end:n.end},r);return e.allDay&&(s=s.map(L)),s}(n,a,t,r,i.recurringTypes))){let t=ts(e,{start:l,end:r.add(l,a)});o[t.instanceId]=t}}}return{defs:a,instances:o}}let to={id:String,groupId:String,title:String,url:String,interactive:Boolean},tl={start:eS,end:eS,date:eS,allDay:Boolean},tc=Object.assign(Object.assign(Object.assign({},to),tl),{extendedProps:eS});function td(e,t,n,r,i=tf(n),s,a){let o;let{refined:l,extra:c}=tu(e,n,i),d=(o=null,t&&(o=t.defaultAllDay),null==o&&(o=n.options.defaultAllDay),o),u=function(e,t,n,r){for(let i=0;i<r.length;i+=1){let s=r[i].parse(e,n);if(s){let{allDay:n}=e;return null==n&&null==(n=t)&&null==(n=s.allDayGuess)&&(n=!1),{allDay:n,duration:s.duration,typeData:s.typeData,typeId:i}}}return null}(l,d,n.dateEnv,n.pluginHooks.recurringTypes);if(u){let e=th(l,c,t?t.sourceId:"",u.allDay,!!u.duration,n,s);return e.recurringDef={typeId:u.typeId,typeData:u.typeData,duration:u.duration},{def:e,instance:null}}let f=function(e,t,n,r){let i,s,{allDay:a}=e,o=null,l=!1,c=null,d=null!=e.start?e.start:e.date;if(i=n.dateEnv.createMarkerMeta(d))o=i.marker;else if(!r)return null;return null!=e.end&&(s=n.dateEnv.createMarkerMeta(e.end)),null==a&&(a=null!=t?t:(!i||i.isTimeUnspecified)&&(!s||s.isTimeUnspecified)),a&&o&&(o=L(o)),s&&(c=s.marker,a&&(c=L(c)),o&&c<=o&&(c=null)),c?l=!0:r||(l=n.options.forceEventDuration||!1,c=n.dateEnv.add(o,a?n.options.defaultAllDayEventDuration:n.options.defaultTimedEventDuration)),{allDay:a,hasEnd:l,range:{start:o,end:c},forcedStartTzo:i?i.forcedTzo:null,forcedEndTzo:s?s.forcedTzo:null}}(l,d,n,r);if(f){let e=th(l,c,t?t.sourceId:"",f.allDay,f.hasEnd,n,s),r=ts(e.defId,f.range,f.forcedStartTzo,f.forcedEndTzo);return a&&e.publicId&&a[e.publicId]&&(r.instanceId=a[e.publicId]),{def:e,instance:r}}return null}function tu(e,t,n=tf(t)){return eR(e,n)}function tf(e){return Object.assign(Object.assign(Object.assign({},tA),tc),e.pluginHooks.eventRefiners)}function th(e,t,n,r,i,s,a){let o={title:e.title||"",groupId:e.groupId||"",publicId:e.id||"",url:e.url||"",recurringDef:null,defId:(a&&e.id?a[e.id]:"")||R(),sourceId:n,allDay:r,hasEnd:i,interactive:e.interactive,ui:tE(e,s),extendedProps:Object.assign(Object.assign({},e.extendedProps||{}),t)};for(let t of s.pluginHooks.eventDefMemberAdders)Object.assign(o,t(e));return Object.freeze(o.ui.classNames),Object.freeze(o.extendedProps),o}function tp(e,t,n,r,i,s){let a=tm(),o=tf(n);for(let l of e){let e=td(l,t,n,r,o,i,s);e&&tg(e,a)}return a}function tg(e,t=tm()){return t.defs[e.def.defId]=e.def,e.instance&&(t.instances[e.instance.instanceId]=e.instance),t}function tm(){return{defs:{},instances:{}}}function tv(e,t){return{defs:Object.assign(Object.assign({},e.defs),t.defs),instances:Object.assign(Object.assign({},e.instances),t.instances)}}function tb(e,t){let n=eT(e.defs,t),r=eT(e.instances,e=>n[e.defId]);return{defs:n,instances:r}}function ty(e){return Array.isArray(e)?e:"string"==typeof e?e.split(/\s+/):[]}let tA={display:String,editable:Boolean,startEditable:Boolean,durationEditable:Boolean,constraint:eS,overlap:eS,allow:eS,className:ty,classNames:ty,color:String,backgroundColor:String,borderColor:String,textColor:String},t_={display:null,startEditable:null,durationEditable:null,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]};function tE(e,t){var n;let r=Array.isArray(n=e.constraint)?tp(n,null,t,!0):"object"==typeof n&&n?tp([n],null,t,!0):null!=n?String(n):null;return{display:e.display||null,startEditable:null!=e.startEditable?e.startEditable:e.editable,durationEditable:null!=e.durationEditable?e.durationEditable:e.editable,constraints:null!=r?[r]:[],overlap:null!=e.overlap?e.overlap:null,allows:null!=e.allow?[e.allow]:[],backgroundColor:e.backgroundColor||e.color||"",borderColor:e.borderColor||e.color||"",textColor:e.textColor||"",classNames:(e.className||[]).concat(e.classNames||[])}}function tD(e,t){return{display:null!=t.display?t.display:e.display,startEditable:null!=t.startEditable?t.startEditable:e.startEditable,durationEditable:null!=t.durationEditable?t.durationEditable:e.durationEditable,constraints:e.constraints.concat(t.constraints),overlap:"boolean"==typeof t.overlap?t.overlap:e.overlap,allows:e.allows.concat(t.allows),backgroundColor:t.backgroundColor||e.backgroundColor,borderColor:t.borderColor||e.borderColor,textColor:t.textColor||e.textColor,classNames:e.classNames.concat(t.classNames)}}let tC={id:String,defaultAllDay:Boolean,url:String,format:String,events:eS,eventDataTransform:eS,success:eS,failure:eS};function tw(e,t,n=tR(t)){let r;if("string"==typeof e?r={url:e}:"function"==typeof e||Array.isArray(e)?r={events:e}:"object"==typeof e&&e&&(r=e),r){let{refined:i,extra:s}=eR(r,n),a=function(e,t){let n=t.pluginHooks.eventSourceDefs;for(let t=n.length-1;t>=0;t-=1){let r=n[t].parseMeta(e);if(r)return{sourceDefId:t,meta:r}}return null}(i,t);if(a)return{_raw:e,isFetching:!1,latestFetchId:"",fetchRange:null,defaultAllDay:i.defaultAllDay,eventDataTransform:i.eventDataTransform,success:i.success,failure:i.failure,publicId:i.id||"",sourceId:R(),sourceDefId:a.sourceDefId,meta:a.meta,ui:tE(i,t),extendedProps:s}}return null}function tR(e){return Object.assign(Object.assign(Object.assign({},tA),tC),e.pluginHooks.eventSourceRefiners)}function tS(e,t,n,r,i){switch(t.type){case"RECEIVE_EVENTS":return function(e,t,n,r,i,s){if(t&&n===t.latestFetchId){let n=tp(tk(i,t,s),t,s);return r&&(n=ta(n,r,s)),tv(tM(e,t.sourceId),n)}return e}(e,n[t.sourceId],t.fetchId,t.fetchRange,t.rawEvents,i);case"RESET_RAW_EVENTS":return function(e,t,n,r,i){let{defIdMap:s,instanceIdMap:a}=function(e){let{defs:t,instances:n}=e,r={},i={};for(let e in t){let{publicId:n}=t[e];n&&(r[n]=e)}for(let e in n){let{publicId:r}=t[n[e].defId];r&&(i[r]=e)}return{defIdMap:r,instanceIdMap:i}}(e);return ta(tp(tk(n,t,i),t,i,!1,s,a),r,i)}(e,n[t.sourceId],t.rawEvents,r.activeRange,i);case"ADD_EVENTS":var s,a;return s=t.eventStore,(a=r?r.activeRange:null)&&(s=ta(s,a,i)),tv(e,s);case"RESET_EVENTS":return t.eventStore;case"MERGE_EVENTS":return tv(e,t.eventStore);case"PREV":case"NEXT":case"CHANGE_DATE":case"CHANGE_VIEW_TYPE":if(r)return ta(e,r.activeRange,i);return e;case"REMOVE_EVENTS":return function(e,t){let{defs:n,instances:r}=e,i={},s={};for(let e in n)t.defs[e]||(i[e]=n[e]);for(let e in r)!t.instances[e]&&i[r[e].defId]&&(s[e]=r[e]);return{defs:i,instances:s}}(e,t.eventStore);case"REMOVE_EVENT_SOURCE":return tM(e,t.sourceId);case"REMOVE_ALL_EVENT_SOURCES":return tb(e,e=>!e.sourceId);case"REMOVE_ALL_EVENTS":return tm();default:return e}}function tk(e,t,n){let r=n.options.eventDataTransform,i=t?t.eventDataTransform:null;return i&&(e=tx(e,i)),r&&(e=tx(e,r)),e}function tx(e,t){let n;if(t)for(let r of(n=[],e)){let e=t(r);e?n.push(e):null==e&&n.push(r)}else n=e;return n}function tT(e,t,n){let{defs:r}=e,i=eM(e.instances,e=>r[e.defId].allDay?e:Object.assign(Object.assign({},e),{range:{start:n.createMarker(t.toDate(e.range.start,e.forcedStartTzo)),end:n.createMarker(t.toDate(e.range.end,e.forcedEndTzo))},forcedStartTzo:n.canComputeOffset?null:e.forcedStartTzo,forcedEndTzo:n.canComputeOffset?null:e.forcedEndTzo}));return{defs:r,instances:i}}function tM(e,t){return tb(e,e=>e.sourceId!==t)}class tO{constructor(){this.handlers={},this.thisContext=null}setThisContext(e){this.thisContext=e}setOptions(e){this.options=e}on(e,t){(function(e,t,n){(e[t]||(e[t]=[])).push(n)})(this.handlers,e,t)}off(e,t){var n;n=this.handlers,t?n[e]&&(n[e]=n[e].filter(e=>e!==t)):delete n[e]}trigger(e,...t){let n=this.handlers[e]||[];for(let r of[].concat(this.options&&this.options[e]||[],n))r.apply(this.thisContext,t)}hasHandlers(e){return!!(this.handlers[e]&&this.handlers[e].length||this.options&&this.options[e])}}let tI={startTime:"09:00",endTime:"17:00",daysOfWeek:[1,2,3,4,5],display:"inverse-background",classNames:"fc-non-business",groupId:"_businessHours"};function tz(e,t){return tp((!0===e?[{}]:Array.isArray(e)?e.filter(e=>e.daysOfWeek):"object"==typeof e&&e?[e]:[]).map(e=>Object.assign(Object.assign({},tI),e)),null,t)}function tN(e,t,n){let{dateEnv:r,options:i}=n,s=t;return e?(s=L(s),s=r.add(s,i.defaultAllDayEventDuration)):s=r.add(s,i.defaultTimedEventDuration),s}class tH{constructor(e,t){this.context=e,this.internalEventSource=t}remove(){this.context.dispatch({type:"REMOVE_EVENT_SOURCE",sourceId:this.internalEventSource.sourceId})}refetch(){this.context.dispatch({type:"FETCH_EVENT_SOURCES",sourceIds:[this.internalEventSource.sourceId],isRefetch:!0})}get id(){return this.internalEventSource.publicId}get url(){return this.internalEventSource.meta.url}get format(){return this.internalEventSource.meta.format}}class tP{constructor(e,t,n){this._context=e,this._def=t,this._instance=n||null}setProp(e,t){if(e in tl)console.warn("Could not set date-related prop 'name'. Use one of the date-related methods instead.");else if("id"===e)t=to[e](t),this.mutate({standardProps:{publicId:t}});else if(e in to)t=to[e](t),this.mutate({standardProps:{[e]:t}});else if(e in tA){let n=tA[e](t);n="color"===e?{backgroundColor:t,borderColor:t}:"editable"===e?{startEditable:t,durationEditable:t}:{[e]:t},this.mutate({standardProps:{ui:n}})}else console.warn(`Could not set prop '${e}'. Use setExtendedProp instead.`)}setExtendedProp(e,t){this.mutate({extendedProps:{[e]:t}})}setStart(e,t={}){let{dateEnv:n}=this._context,r=n.createMarker(e);if(r&&this._instance){let e=tr(this._instance.range.start,r,n,t.granularity);t.maintainDuration?this.mutate({datesDelta:e}):this.mutate({startDelta:e})}}setEnd(e,t={}){let n,{dateEnv:r}=this._context;if((null==e||(n=r.createMarker(e)))&&this._instance){if(n){let e=tr(this._instance.range.end,n,r,t.granularity);this.mutate({endDelta:e})}else this.mutate({standardProps:{hasEnd:!1}})}}setDates(e,t,n={}){let r,{dateEnv:i}=this._context,s={allDay:n.allDay},a=i.createMarker(e);if(a&&(null==t||(r=i.createMarker(t)))&&this._instance){let e=this._instance.range;!0===n.allDay&&(e=te(e));let t=tr(e.start,a,i,n.granularity);if(r){let a=tr(e.end,r,i,n.granularity);t.years===a.years&&t.months===a.months&&t.days===a.days&&t.milliseconds===a.milliseconds?this.mutate({datesDelta:t,standardProps:s}):this.mutate({startDelta:t,endDelta:a,standardProps:s})}else s.hasEnd=!1,this.mutate({datesDelta:t,standardProps:s})}}moveStart(e){let t=T(e);t&&this.mutate({startDelta:t})}moveEnd(e){let t=T(e);t&&this.mutate({endDelta:t})}moveDates(e){let t=T(e);t&&this.mutate({datesDelta:t})}setAllDay(e,t={}){let n={allDay:e},{maintainDuration:r}=t;null==r&&(r=this._context.options.allDayMaintainDuration),this._def.allDay!==e&&(n.hasEnd=r),this.mutate({standardProps:n})}formatRange(e){let{dateEnv:t}=this._context,n=this._instance,r=em(e);return this._def.hasEnd?t.formatRange(n.range.start,n.range.end,r,{forcedStartTzo:n.forcedStartTzo,forcedEndTzo:n.forcedEndTzo}):t.format(n.range.start,r,{forcedTzo:n.forcedStartTzo})}mutate(e){let t=this._instance;if(t){let n=this._def,r=this._context,{eventStore:i}=r.getCurrentData(),s=function(e,t){let n=e.instances[t];if(n){let t=e.defs[n.defId],r=tb(e,e=>!!(t.groupId&&t.groupId===e.groupId));return r.defs[t.defId]=t,r.instances[n.instanceId]=n,r}return tm()}(i,t.instanceId);s=function(e,t,n,r){let i=tW(e.defs,t),s=tm();for(let t in e.defs){let a=e.defs[t];s.defs[t]=function(e,t,n,r){let i=n.standardProps||{};null==i.hasEnd&&t.durationEditable&&(n.startDelta||n.endDelta)&&(i.hasEnd=!0);let s=Object.assign(Object.assign(Object.assign({},e),i),{ui:Object.assign(Object.assign({},e.ui),i.ui)});for(let e of(n.extendedProps&&(s.extendedProps=Object.assign(Object.assign({},s.extendedProps),n.extendedProps)),r.pluginHooks.eventDefMutationAppliers))e(s,n,r);return!s.hasEnd&&r.options.forceEventDuration&&(s.hasEnd=!0),s}(a,i[t],n,r)}for(let t in e.instances){let a=e.instances[t],o=s.defs[a.defId];s.instances[t]=function(e,t,n,r,i){let{dateEnv:s}=i,a=r.standardProps&&!0===r.standardProps.allDay,o=r.standardProps&&!1===r.standardProps.hasEnd,l=Object.assign({},e);return a&&(l.range=te(l.range)),r.datesDelta&&n.startEditable&&(l.range={start:s.add(l.range.start,r.datesDelta),end:s.add(l.range.end,r.datesDelta)}),r.startDelta&&n.durationEditable&&(l.range={start:s.add(l.range.start,r.startDelta),end:l.range.end}),r.endDelta&&n.durationEditable&&(l.range={start:l.range.start,end:s.add(l.range.end,r.endDelta)}),o&&(l.range={start:l.range.start,end:tN(t.allDay,l.range.start,i)}),t.allDay&&(l.range={start:L(l.range.start),end:L(l.range.end)}),l.range.end<l.range.start&&(l.range.end=tN(t.allDay,l.range.start,i)),l}(a,o,i[a.defId],n,r)}return s}(s,{"":{display:"",startEditable:!0,durationEditable:!0,constraints:[],overlap:null,allows:[],backgroundColor:"",borderColor:"",textColor:"",classNames:[]}},e,r);let a=new tP(r,n,t);this._def=s.defs[n.defId],this._instance=s.instances[t.instanceId],r.dispatch({type:"MERGE_EVENTS",eventStore:s}),r.emitter.trigger("eventChange",{oldEvent:a,event:this,relatedEvents:tj(s,r,t),revert(){r.dispatch({type:"RESET_EVENTS",eventStore:i})}})}}remove(){let e=this._context,t=tB(this);e.dispatch({type:"REMOVE_EVENTS",eventStore:t}),e.emitter.trigger("eventRemove",{event:this,relatedEvents:[],revert(){e.dispatch({type:"MERGE_EVENTS",eventStore:t})}})}get source(){let{sourceId:e}=this._def;return e?new tH(this._context,this._context.getCurrentData().eventSources[e]):null}get start(){return this._instance?this._context.dateEnv.toDate(this._instance.range.start):null}get end(){return this._instance&&this._def.hasEnd?this._context.dateEnv.toDate(this._instance.range.end):null}get startStr(){let e=this._instance;return e?this._context.dateEnv.formatIso(e.range.start,{omitTime:this._def.allDay,forcedTzo:e.forcedStartTzo}):""}get endStr(){let e=this._instance;return e&&this._def.hasEnd?this._context.dateEnv.formatIso(e.range.end,{omitTime:this._def.allDay,forcedTzo:e.forcedEndTzo}):""}get id(){return this._def.publicId}get groupId(){return this._def.groupId}get allDay(){return this._def.allDay}get title(){return this._def.title}get url(){return this._def.url}get display(){return this._def.ui.display||"auto"}get startEditable(){return this._def.ui.startEditable}get durationEditable(){return this._def.ui.durationEditable}get constraint(){return this._def.ui.constraints[0]||null}get overlap(){return this._def.ui.overlap}get allow(){return this._def.ui.allows[0]||null}get backgroundColor(){return this._def.ui.backgroundColor}get borderColor(){return this._def.ui.borderColor}get textColor(){return this._def.ui.textColor}get classNames(){return this._def.ui.classNames}get extendedProps(){return this._def.extendedProps}toPlainObject(e={}){let t=this._def,{ui:n}=t,{startStr:r,endStr:i}=this,s={allDay:t.allDay};return t.title&&(s.title=t.title),r&&(s.start=r),i&&(s.end=i),t.publicId&&(s.id=t.publicId),t.groupId&&(s.groupId=t.groupId),t.url&&(s.url=t.url),n.display&&"auto"!==n.display&&(s.display=n.display),e.collapseColor&&n.backgroundColor&&n.backgroundColor===n.borderColor?s.color=n.backgroundColor:(n.backgroundColor&&(s.backgroundColor=n.backgroundColor),n.borderColor&&(s.borderColor=n.borderColor)),n.textColor&&(s.textColor=n.textColor),n.classNames.length&&(s.classNames=n.classNames),Object.keys(t.extendedProps).length&&(e.collapseExtendedProps?Object.assign(s,t.extendedProps):s.extendedProps=t.extendedProps),s}toJSON(){return this.toPlainObject()}}function tB(e){let t=e._def,n=e._instance;return{defs:{[t.defId]:t},instances:n?{[n.instanceId]:n}:{}}}function tj(e,t,n){let{defs:r,instances:i}=e,s=[],a=n?n.instanceId:"";for(let e in i){let n=i[e],o=r[n.defId];n.instanceId!==a&&s.push(new tP(t,o,n))}return s}function tU(e,t,n,r){let i={},s={},a={},o=[],l=[],c=tW(e.defs,t);for(let t in e.defs){let n=e.defs[t];"inverse-background"!==c[n.defId].display||(n.groupId?(i[n.groupId]=[],a[n.groupId]||(a[n.groupId]=n)):s[t]=[])}for(let t in e.instances){let a=e.instances[t],d=e.defs[a.defId],u=c[d.defId],f=a.range,h=!d.allDay&&r?tt(f,r):f,p=e7(h,n);p&&("inverse-background"===u.display?d.groupId?i[d.groupId].push(p):s[a.defId].push(p):"none"!==u.display&&("background"===u.display?o:l).push({def:d,ui:u,instance:a,range:p,isStart:h.start&&h.start.valueOf()===p.start.valueOf(),isEnd:h.end&&h.end.valueOf()===p.end.valueOf()}))}for(let e in i)for(let t of e6(i[e],n)){let n=a[e],r=c[n.defId];o.push({def:n,ui:r,instance:null,range:t,isStart:!1,isEnd:!1})}for(let t in s)for(let r of e6(s[t],n))o.push({def:e.defs[t],ui:c[t],instance:null,range:r,isStart:!1,isEnd:!1});return{bg:o,fg:l}}function tF(e){return e.fcSeg||e.parentNode.fcSeg||null}function tW(e,t){return eM(e,e=>tV(e,t))}function tV(e,t){let n=[];return t[""]&&n.push(t[""]),t[e.defId]&&n.push(t[e.defId]),n.push(e.ui),n.reduce(tD,t_)}function tL(e,t){let n=e.map(tQ);return n.sort((e,n)=>(function(e,t,n){let r,i;for(r=0;r<n.length;r+=1)if(i=function(e,t,n){var r,i;return n.func?n.func(e,t):(r=e[n.field],i=t[n.field],(r||i?null==i?-1:null==r?1:"string"==typeof r||"string"==typeof i?String(r).localeCompare(String(i)):r-i:0)*(n.order||1))}(e,t,n[r]))return i;return 0})(e,n,t)),n.map(e=>e._seg)}function tQ(e){let{eventRange:t}=e,n=t.def,r=t.instance?t.instance.range:t.range,i=r.start?r.start.valueOf():0,s=r.end?r.end.valueOf():0;return Object.assign(Object.assign(Object.assign({},n.extendedProps),n),{id:n.publicId,start:i,end:s,duration:s-i,allDay:Number(n.allDay),_seg:e})}function tG(e,t,n,r,i,s,a){let{dateEnv:o,options:l}=n,{displayEventTime:c,displayEventEnd:d}=l,u=e.eventRange.def,f=e.eventRange.instance;null==c&&(c=!1!==r),null==d&&(d=!1!==i);let h=f.range.start,p=f.range.end,g=s||e.start||e.eventRange.range.start,m=a||e.end||e.eventRange.range.end,v=L(h).valueOf()===L(g).valueOf(),b=L(U(p,-1)).valueOf()===L(U(m,-1)).valueOf();return c&&!u.allDay&&(v||b)?(g=v?h:g,m=b?p:m,d&&u.hasEnd)?o.formatRange(g,m,t,{forcedStartTzo:s?null:f.forcedStartTzo,forcedEndTzo:a?null:f.forcedEndTzo}):o.format(g,t,{forcedTzo:s?null:f.forcedStartTzo}):""}function tY(e,t,n){let r=e.eventRange.range;return{isPast:r.end<=(n||t.start),isFuture:r.start>=(n||t.end),isToday:t&&e9(t,r.start)}}function tq(e){return e.instance?e.instance.instanceId:`${e.def.defId}:${e.range.start.toISOString()}`}function tZ(e,t){let{def:n,instance:r}=e.eventRange,{url:i}=n;if(i)return{href:i};let{emitter:s,options:a}=t,{eventInteractive:o}=a;return(null==o&&null==(o=n.interactive)&&(o=!!s.hasHandlers("eventClick")),o)?C(e=>{s.trigger("eventClick",{el:e.target,event:new tP(t,n,r),jsEvent:e,view:t.viewApi})}):{}}let t$={start:eS,end:eS,allDay:Boolean};function tJ(e,t,n){return Object.assign(Object.assign({},tX(e,t,n)),{timeZone:t.timeZone})}function tX(e,t,n){return{start:t.toDate(e.start),end:t.toDate(e.end),startStr:t.formatIso(e.start,{omitTime:n}),endStr:t.formatIso(e.end,{omitTime:n})}}function tK(e,t,n){let r=!1,i=function(e){r||(r=!0,t(e))},s=function(e){r||(r=!0,n(e))},a=e(i,s);a&&"function"==typeof a.then&&a.then(i,s)}class t0 extends Error{constructor(e,t){super(e),this.response=t}}function t1(e,t,n){let r={method:e=e.toUpperCase()};return"GET"===e?t+=(-1===t.indexOf("?")?"?":"&")+new URLSearchParams(n):(r.body=new URLSearchParams(n),r.headers={"Content-Type":"application/x-www-form-urlencoded"}),fetch(t,r).then(e=>{if(e.ok)return e.json().then(t=>[t,e],()=>{throw new t0("Failure parsing JSON",e)});throw new t0("Request failed",e)})}function t2(){return null==i&&(i=function(){if("undefined"==typeof document)return!0;let e=document.createElement("div");e.style.position="absolute",e.style.top="0px",e.style.left="0px",e.innerHTML="<table><tr><td><div></div></td></tr></table>",e.querySelector("table").style.height="100px",e.querySelector("div").style.height="100%",document.body.appendChild(e);let t=e.querySelector("div").offsetHeight>0;return document.body.removeChild(e),t}()),i}class t4 extends eZ{constructor(){super(...arguments),this.state={forPrint:!1},this.handleBeforePrint=()=>{eW(()=>{this.setState({forPrint:!0})})},this.handleAfterPrint=()=>{eW(()=>{this.setState({forPrint:!1})})}}render(){let{props:e}=this,{options:t}=e,{forPrint:n}=this.state,r=n||"auto"===t.height||"auto"===t.contentHeight,i=r||null==t.height?"":t.height,s=["fc",n?"fc-media-print":"fc-media-screen",`fc-direction-${t.direction}`,e.theme.getClass("root")];return t2()||s.push("fc-liquid-hack"),e.children(s,i,r,n)}componentDidMount(){let{emitter:e}=this.props;e.on("_beforeprint",this.handleBeforePrint),e.on("_afterprint",this.handleAfterPrint)}componentWillUnmount(){let{emitter:e}=this.props;e.off("_beforeprint",this.handleBeforePrint),e.off("_afterprint",this.handleAfterPrint)}}class t5{constructor(e){this.component=e.component,this.isHitComboAllowed=e.isHitComboAllowed||null}destroy(){}}function t3(e,t){return{component:e,el:t.el,useEventCenter:null==t.useEventCenter||t.useEventCenter,isHitComboAllowed:t.isHitComboAllowed||null}}let t6={};class t8 extends a.wA{constructor(e,t){super(e,t),this.handleRefresh=()=>{let e=this.computeTiming();e.state.nowDate.valueOf()!==this.state.nowDate.valueOf()&&this.setState(e.state),this.clearTimeout(),this.setTimeout(e.waitMs)},this.handleVisibilityChange=()=>{document.hidden||this.handleRefresh()},this.state=this.computeTiming().state}render(){let{props:e,state:t}=this;return e.children(t.nowDate,t.todayRange)}componentDidMount(){this.setTimeout(),this.context.nowManager.addResetListener(this.handleRefresh),document.addEventListener("visibilitychange",this.handleVisibilityChange)}componentDidUpdate(e){e.unit!==this.props.unit&&(this.clearTimeout(),this.setTimeout())}componentWillUnmount(){this.clearTimeout(),this.context.nowManager.removeResetListener(this.handleRefresh),document.removeEventListener("visibilitychange",this.handleVisibilityChange)}computeTiming(){let e,t,{props:n,context:r}=this,i=r.nowManager.getDateMarker(),s=r.dateEnv.startOf(i,n.unit),a=r.dateEnv.add(s,T(1,n.unit)).valueOf()-i.valueOf();return a=Math.min(864e5,a),{state:{nowDate:s,todayRange:(t=j(e=L(s),1),{start:e,end:t})},waitMs:a}}setTimeout(e=this.computeTiming().waitMs){this.timeoutId=setTimeout(()=>{let e=this.computeTiming();this.setState(e.state,()=>{this.setTimeout(e.waitMs)})},e)}clearTimeout(){this.timeoutId&&clearTimeout(this.timeoutId)}}t8.contextType=eG;class t7{getCurrentData(){return this.currentDataManager.getCurrentData()}dispatch(e){this.currentDataManager.dispatch(e)}get view(){return this.getCurrentData().viewApi}batchRendering(e){e()}updateSize(){this.trigger("_resize",!0)}setOption(e,t){this.dispatch({type:"SET_OPTION",optionName:e,rawOptionValue:t})}getOption(e){return this.currentDataManager.currentCalendarOptionsInput[e]}getAvailableLocaleCodes(){return Object.keys(this.getCurrentData().availableRawLocales)}on(e,t){let{currentDataManager:n}=this;n.currentCalendarOptionsRefiners[e]?n.emitter.on(e,t):console.warn(`Unknown listener name '${e}'`)}off(e,t){this.currentDataManager.emitter.off(e,t)}trigger(e,...t){this.currentDataManager.emitter.trigger(e,...t)}changeView(e,t){this.batchRendering(()=>{if(this.unselect(),t){if(t.start&&t.end)this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e}),this.dispatch({type:"SET_OPTION",optionName:"visibleRange",rawOptionValue:t});else{let{dateEnv:n}=this.getCurrentData();this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e,dateMarker:n.createMarker(t)})}}else this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:e})})}zoomTo(e,t){let n,r=this.getCurrentData();t=t||"day",n=r.viewSpecs[t]||this.getUnitViewSpec(t),this.unselect(),n?this.dispatch({type:"CHANGE_VIEW_TYPE",viewType:n.type,dateMarker:e}):this.dispatch({type:"CHANGE_DATE",dateMarker:e})}getUnitViewSpec(e){let t,n,{viewSpecs:r,toolbarConfig:i}=this.getCurrentData(),s=[].concat(i.header?i.header.viewsWithButtons:[],i.footer?i.footer.viewsWithButtons:[]);for(let e in r)s.push(e);for(t=0;t<s.length;t+=1)if((n=r[s[t]])&&n.singleUnit===e)return n;return null}prev(){this.unselect(),this.dispatch({type:"PREV"})}next(){this.unselect(),this.dispatch({type:"NEXT"})}prevYear(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,-1)})}nextYear(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.dateEnv.addYears(e.currentDate,1)})}today(){let e=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:e.nowManager.getDateMarker()})}gotoDate(e){let t=this.getCurrentData();this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:t.dateEnv.createMarker(e)})}incrementDate(e){let t=this.getCurrentData(),n=T(e);n&&(this.unselect(),this.dispatch({type:"CHANGE_DATE",dateMarker:t.dateEnv.add(t.currentDate,n)}))}getDate(){let e=this.getCurrentData();return e.dateEnv.toDate(e.currentDate)}formatDate(e,t){let{dateEnv:n}=this.getCurrentData();return n.format(n.createMarker(e),em(t))}formatRange(e,t,n){let{dateEnv:r}=this.getCurrentData();return r.formatRange(r.createMarker(e),r.createMarker(t),em(n),n)}formatIso(e,t){let{dateEnv:n}=this.getCurrentData();return n.formatIso(n.createMarker(e),{omitTime:t})}select(e,t){let n;n=null==t?null!=e.start?e:{start:e,end:null}:{start:e,end:t};let r=this.getCurrentData(),i=function(e,t,n){let r=function(e,t){let{refined:n,extra:r}=eR(e,t$),i=n.start?t.createMarkerMeta(n.start):null,s=n.end?t.createMarkerMeta(n.end):null,{allDay:a}=n;return null==a&&(a=i&&i.isTimeUnspecified&&(!s||s.isTimeUnspecified)),Object.assign({range:{start:i?i.marker:null,end:s?s.marker:null},allDay:a},r)}(e,t),{range:i}=r;if(!i.start)return null;if(!i.end){if(null==n)return null;i.end=t.add(i.start,n)}return r}(n,r.dateEnv,T({days:1}));i&&(this.dispatch({type:"SELECT_DATES",selection:i}),function(e,t,n){n.emitter.trigger("select",Object.assign(Object.assign({},function(e,t){var n;let r={};for(let n of t.pluginHooks.dateSpanTransforms)Object.assign(r,n(e,t));return Object.assign(r,(n=t.dateEnv,Object.assign(Object.assign({},tX(e.range,n,e.allDay)),{allDay:e.allDay}))),r}(e,n)),{jsEvent:null,view:n.viewApi||n.calendarApi.view}))}(i,0,r))}unselect(e){let t=this.getCurrentData();t.dateSelection&&(this.dispatch({type:"UNSELECT_DATES"}),function(e,t){t.emitter.trigger("unselect",{jsEvent:e?e.origEvent:null,view:t.viewApi||t.calendarApi.view})}(e,t))}addEvent(e,t){let n;if(e instanceof tP){let t=e._def,n=e._instance;return this.getCurrentData().eventStore.defs[t.defId]||(this.dispatch({type:"ADD_EVENTS",eventStore:tg({def:t,instance:n})}),this.triggerEventAdd(e)),e}let r=this.getCurrentData();if(t instanceof tH)n=t.internalEventSource;else if("boolean"==typeof t)t&&([n]=eI(r.eventSources));else if(null!=t){let e=this.getEventSourceById(t);if(!e)return console.warn(`Could not find an event source with ID "${t}"`),null;n=e.internalEventSource}let i=td(e,n,r,!1);if(i){let e=new tP(r,i.def,i.def.recurringDef?null:i.instance);return this.dispatch({type:"ADD_EVENTS",eventStore:tg(i)}),this.triggerEventAdd(e),e}return null}triggerEventAdd(e){let{emitter:t}=this.getCurrentData();t.trigger("eventAdd",{event:e,relatedEvents:[],revert:()=>{this.dispatch({type:"REMOVE_EVENTS",eventStore:tB(e)})}})}getEventById(e){let t=this.getCurrentData(),{defs:n,instances:r}=t.eventStore;for(let i in e=String(e),n){let s=n[i];if(s.publicId===e){if(s.recurringDef)return new tP(t,s,null);for(let e in r){let n=r[e];if(n.defId===s.defId)return new tP(t,s,n)}}}return null}getEvents(){let e=this.getCurrentData();return tj(e.eventStore,e)}removeAllEvents(){this.dispatch({type:"REMOVE_ALL_EVENTS"})}getEventSources(){let e=this.getCurrentData(),t=e.eventSources,n=[];for(let r in t)n.push(new tH(e,t[r]));return n}getEventSourceById(e){let t=this.getCurrentData(),n=t.eventSources;for(let r in e=String(e),n)if(n[r].publicId===e)return new tH(t,n[r]);return null}addEventSource(e){let t=this.getCurrentData();if(e instanceof tH)return t.eventSources[e.internalEventSource.sourceId]||this.dispatch({type:"ADD_EVENT_SOURCES",sources:[e.internalEventSource]}),e;let n=tw(e,t);return n?(this.dispatch({type:"ADD_EVENT_SOURCES",sources:[n]}),new tH(t,n)):null}removeAllEventSources(){this.dispatch({type:"REMOVE_ALL_EVENT_SOURCES"})}refetchEvents(){this.dispatch({type:"FETCH_EVENT_SOURCES",isRefetch:!0})}scrollToTime(e){let t=T(e);t&&this.trigger("_scrollRequest",{time:t})}}function t9(e,t,n,r){return{dow:e.getUTCDay(),isDisabled:!!(r&&(!r.activeRange||!e9(r.activeRange,e))),isOther:!!(r&&!e9(r.currentRange,e)),isToday:!!(t&&e9(t,e)),isPast:!!(n?e<n:!!t&&e<t.start),isFuture:!!(n?e>n:!!t&&e>=t.end)}}function ne(e,t){let n=["fc-day",`fc-day-${P[e.dow]}`];return e.isDisabled?n.push("fc-day-disabled"):(e.isToday&&(n.push("fc-day-today"),n.push(t.getClass("today"))),e.isPast&&n.push("fc-day-past"),e.isFuture&&n.push("fc-day-future"),e.isOther&&n.push("fc-day-other")),n}tm();let nt=em({year:"numeric",month:"long",day:"numeric"}),nn=em({week:"long"});function nr(e,t,n="day",r=!0){let{dateEnv:i,options:s,calendarApi:a}=e,o=i.format(t,"week"===n?nn:nt);if(s.navLinks){let e=i.toDate(t),l=e=>{let r="day"===n?s.navLinkDayClick:"week"===n?s.navLinkWeekClick:null;"function"==typeof r?r.call(a,i.toDate(t),e):("string"==typeof r&&(n=r),a.zoomTo(t,n))};return Object.assign({title:k(s.navLinkHint,[o,e],o),"data-navlink":""},r?D(l):{onClick:l})}return{"aria-label":o}}class ni{constructor(e,t,n,r){this.els=t;let i=this.originClientRect=e.getBoundingClientRect();n&&this.buildElHorizontals(i.left),r&&this.buildElVerticals(i.top)}buildElHorizontals(e){let t=[],n=[];for(let r of this.els){let i=r.getBoundingClientRect();t.push(i.left-e),n.push(i.right-e)}this.lefts=t,this.rights=n}buildElVerticals(e){let t=[],n=[];for(let r of this.els){let i=r.getBoundingClientRect();t.push(i.top-e),n.push(i.bottom-e)}this.tops=t,this.bottoms=n}leftToIndex(e){let t,{lefts:n,rights:r}=this,i=n.length;for(t=0;t<i;t+=1)if(e>=n[t]&&e<r[t])return t}topToIndex(e){let t,{tops:n,bottoms:r}=this,i=n.length;for(t=0;t<i;t+=1)if(e>=n[t]&&e<r[t])return t}getWidth(e){return this.rights[e]-this.lefts[e]}getHeight(e){return this.bottoms[e]-this.tops[e]}similarTo(e){return ns(this.tops||[],e.tops||[])&&ns(this.bottoms||[],e.bottoms||[])&&ns(this.lefts||[],e.lefts||[])&&ns(this.rights||[],e.rights||[])}}function ns(e,t){let n=e.length;if(n!==t.length)return!1;for(let r=0;r<n;r++)if(Math.round(e[r])!==Math.round(t[r]))return!1;return!0}class na extends eZ{constructor(){super(...arguments),this.uid=R()}prepareHits(){}queryHit(e,t,n,r){return null}isValidSegDownEl(e){return!this.props.eventDrag&&!this.props.eventResize&&!m(e,".fc-event-mirror")}isValidDateDownEl(e){return!m(e,".fc-event:not(.fc-bg-event)")&&!m(e,".fc-more-link")&&!m(e,"a[data-navlink]")&&!m(e,".fc-popover")}}class no{constructor(e=e=>e.thickness||1){this.getEntryThickness=e,this.strictOrder=!1,this.allowReslicing=!1,this.maxCoord=-1,this.maxStackCnt=-1,this.levelCoords=[],this.entriesByLevel=[],this.stackCnts={}}addSegs(e){let t=[];for(let n of e)this.insertEntry(n,t);return t}insertEntry(e,t){let n=this.findInsertion(e);this.isInsertionValid(n,e)?this.insertEntryAt(e,n):this.handleInvalidInsertion(n,e,t)}isInsertionValid(e,t){return(-1===this.maxCoord||e.levelCoord+this.getEntryThickness(t)<=this.maxCoord)&&(-1===this.maxStackCnt||e.stackCnt<this.maxStackCnt)}handleInvalidInsertion(e,t,n){if(this.allowReslicing&&e.touchingEntry){let r=Object.assign(Object.assign({},t),{span:nd(t.span,e.touchingEntry.span)});n.push(r),this.splitEntry(t,e.touchingEntry,n)}else n.push(t)}splitEntry(e,t,n){let r=e.span,i=t.span;r.start<i.start&&this.insertEntry({index:e.index,thickness:e.thickness,span:{start:r.start,end:i.start}},n),r.end>i.end&&this.insertEntry({index:e.index,thickness:e.thickness,span:{start:i.end,end:r.end}},n)}insertEntryAt(e,t){let{entriesByLevel:n,levelCoords:r}=this;-1===t.lateral?(nu(r,t.level,t.levelCoord),nu(n,t.level,[e])):nu(n[t.level],t.lateral,e),this.stackCnts[nc(e)]=t.stackCnt}findInsertion(e){let{levelCoords:t,entriesByLevel:n,strictOrder:r,stackCnts:i}=this,s=t.length,a=0,o=-1,l=-1,c=null,d=0;for(let u=0;u<s;u+=1){let s;let f=t[u];if(!r&&f>=a+this.getEntryThickness(e))break;let h=n[u],p=nf(h,e.span.start,nl),g=p[0]+p[1];for(;(s=h[g])&&s.span.start<e.span.end;){let e=f+this.getEntryThickness(s);e>a&&(a=e,c=s,o=u,l=g),e===a&&(d=Math.max(d,i[nc(s)]+1)),g+=1}}let u=0;if(c)for(u=o+1;u<s&&t[u]<a;)u+=1;let f=-1;return u<s&&t[u]===a&&(f=nf(n[u],e.span.end,nl)[0]),{touchingLevel:o,touchingLateral:l,touchingEntry:c,stackCnt:d,levelCoord:a,level:u,lateral:f}}toRects(){let{entriesByLevel:e,levelCoords:t}=this,n=e.length,r=[];for(let i=0;i<n;i+=1){let n=e[i],s=t[i];for(let e of n)r.push(Object.assign(Object.assign({},e),{thickness:this.getEntryThickness(e),levelCoord:s}))}return r}}function nl(e){return e.span.end}function nc(e){return e.index+":"+e.span.start}function nd(e,t){let n=Math.max(e.start,t.start),r=Math.min(e.end,t.end);return n<r?{start:n,end:r}:null}function nu(e,t,n){e.splice(t,0,n)}function nf(e,t,n){let r=0,i=e.length;if(!i||t<n(e[r]))return[0,0];if(t>n(e[i-1]))return[i,0];for(;r<i;){let s=Math.floor(r+(i-r)/2),a=n(e[s]);if(t<a)i=s;else{if(!(t>a))return[s,1];r=s+1}}return[r,0]}let nh="fc-col-header-cell";function np(e){return e.text}class ng extends eZ{render(){let{dateEnv:e,options:t,theme:n,viewApi:r}=this.context,{props:i}=this,{date:s,dateProfile:o}=i,l=t9(s,i.todayRange,null,o),c=[nh].concat(ne(l,n)),d=e.format(s,i.dayHeaderFormat),u=!l.isDisabled&&i.colCnt>1?nr(this.context,s):{},f=e.toDate(s);e.namedTimeZoneImpl&&(f=U(f,36e5));let h=Object.assign(Object.assign(Object.assign({date:f,view:r},i.extraRenderProps),{text:d}),l);return(0,a.az)(e2,{elTag:"th",elClasses:c,elAttrs:Object.assign({role:"columnheader",colSpan:i.colSpan,"data-date":l.isDisabled?void 0:X(s)},i.extraDataAttrs),renderProps:h,generatorName:"dayHeaderContent",customGenerator:t.dayHeaderContent,defaultGenerator:np,classNameGenerator:t.dayHeaderClassNames,didMount:t.dayHeaderDidMount,willUnmount:t.dayHeaderWillUnmount},e=>(0,a.az)("div",{className:"fc-scrollgrid-sync-inner"},!l.isDisabled&&(0,a.az)(e,{elTag:"a",elAttrs:u,elClasses:["fc-col-header-cell-cushion",i.isSticky&&"fc-sticky"]})))}}let nm=em({weekday:"long"});class nv extends eZ{render(){let{props:e}=this,{dateEnv:t,theme:n,viewApi:r,options:i}=this.context,s=j(new Date(2592e5),e.dow),o={dow:e.dow,isDisabled:!1,isFuture:!1,isPast:!1,isToday:!1,isOther:!1},l=t.format(s,e.dayHeaderFormat),c=Object.assign(Object.assign(Object.assign(Object.assign({date:s},o),{view:r}),e.extraRenderProps),{text:l});return(0,a.az)(e2,{elTag:"th",elClasses:[nh,...ne(o,n),...e.extraClassNames||[]],elAttrs:Object.assign({role:"columnheader",colSpan:e.colSpan},e.extraDataAttrs),renderProps:c,generatorName:"dayHeaderContent",customGenerator:i.dayHeaderContent,defaultGenerator:np,classNameGenerator:i.dayHeaderClassNames,didMount:i.dayHeaderDidMount,willUnmount:i.dayHeaderWillUnmount},n=>(0,a.az)("div",{className:"fc-scrollgrid-sync-inner"},(0,a.az)(n,{elTag:"a",elClasses:["fc-col-header-cell-cushion",e.isSticky&&"fc-sticky"],elAttrs:{"aria-label":t.format(s,nm)}})))}}class nb extends eZ{constructor(){super(...arguments),this.createDayHeaderFormatter=et(ny)}render(){let{context:e}=this,{dates:t,dateProfile:n,datesRepDistinctDays:r,renderIntro:i}=this.props,s=this.createDayHeaderFormatter(e.options.dayHeaderFormat,r,t.length);return(0,a.az)(t8,{unit:"day"},(e,o)=>(0,a.az)("tr",{role:"row"},i&&i("day"),t.map(e=>r?(0,a.az)(ng,{key:e.toISOString(),date:e,dateProfile:n,todayRange:o,colCnt:t.length,dayHeaderFormat:s}):(0,a.az)(nv,{key:e.getUTCDay(),dow:e.getUTCDay(),dayHeaderFormat:s}))))}}function ny(e,t,n){return e||(!t||n>10?em({weekday:"short"}):n>1?em({weekday:"short",month:"numeric",day:"numeric",omitCommas:!0}):em({weekday:"long"}))}class nA{constructor(e,t){let n=e.start,{end:r}=e,i=[],s=[],a=-1;for(;n<r;)t.isHiddenDay(n)?i.push(a+.5):(i.push(a+=1),s.push(n)),n=j(n,1);this.dates=s,this.indices=i,this.cnt=s.length}sliceRange(e){let t=this.getDateDayIndex(e.start),n=this.getDateDayIndex(j(e.end,-1)),r=Math.max(0,t),i=Math.min(this.cnt-1,n);return(r=Math.ceil(r))<=(i=Math.floor(i))?{firstIndex:r,lastIndex:i,isStart:t===r,isEnd:n===i}:null}getDateDayIndex(e){let{indices:t}=this,n=Math.floor(W(this.dates[0],e));return n<0?t[0]-1:n>=t.length?t[t.length-1]+1:t[n]}}class n_{constructor(e,t){let n,r,i,{dates:s}=e;if(t){for(n=1,r=s[0].getUTCDay();n<s.length&&s[n].getUTCDay()!==r;n+=1);i=Math.ceil(s.length/n)}else i=1,n=s.length;this.rowCnt=i,this.colCnt=n,this.daySeries=e,this.cells=this.buildCells(),this.headerDates=this.buildHeaderDates()}buildCells(){let e=[];for(let t=0;t<this.rowCnt;t+=1){let n=[];for(let e=0;e<this.colCnt;e+=1)n.push(this.buildCell(t,e));e.push(n)}return e}buildCell(e,t){let n=this.daySeries.dates[e*this.colCnt+t];return{key:n.toISOString(),date:n}}buildHeaderDates(){let e=[];for(let t=0;t<this.colCnt;t+=1)e.push(this.cells[0][t].date);return e}sliceRange(e){let{colCnt:t}=this,n=this.daySeries.sliceRange(e),r=[];if(n){let{firstIndex:e,lastIndex:i}=n,s=e;for(;s<=i;){let a=Math.floor(s/t),o=Math.min((a+1)*t,i+1);r.push({row:a,firstCol:s%t,lastCol:(o-1)%t,isStart:n.isStart&&s===e,isEnd:n.isEnd&&o-1===i}),s=o}}return r}}class nE{constructor(){this.sliceBusinessHours=et(this._sliceBusinessHours),this.sliceDateSelection=et(this._sliceDateSpan),this.sliceEventStore=et(this._sliceEventStore),this.sliceEventDrag=et(this._sliceInteraction),this.sliceEventResize=et(this._sliceInteraction),this.forceDayIfListItem=!1}sliceProps(e,t,n,r,...i){let{eventUiBases:s}=e,a=this.sliceEventStore(e.eventStore,s,t,n,...i);return{dateSelectionSegs:this.sliceDateSelection(e.dateSelection,t,n,s,r,...i),businessHourSegs:this.sliceBusinessHours(e.businessHours,t,n,r,...i),fgEventSegs:a.fg,bgEventSegs:a.bg,eventDrag:this.sliceEventDrag(e.eventDrag,s,t,n,...i),eventResize:this.sliceEventResize(e.eventResize,s,t,n,...i),eventSelection:e.eventSelection}}sliceNowDate(e,t,n,r,...i){return this._sliceDateSpan({range:{start:e,end:U(e,1)},allDay:!1},t,n,{},r,...i)}_sliceBusinessHours(e,t,n,r,...i){return e?this._sliceEventStore(ta(e,nD(t,!!n),r),{},t,n,...i).bg:[]}_sliceEventStore(e,t,n,r,...i){if(e){let s=tU(e,t,nD(n,!!r),r);return{bg:this.sliceEventRanges(s.bg,i),fg:this.sliceEventRanges(s.fg,i)}}return{bg:[],fg:[]}}_sliceInteraction(e,t,n,r,...i){if(!e)return null;let s=tU(e.mutatedEvents,t,nD(n,!!r),r);return{segs:this.sliceEventRanges(s.fg,i),affectedInstances:e.affectedEvents.instances,isEvent:e.isEvent}}_sliceDateSpan(e,t,n,r,i,...s){if(!e)return[];let a=nD(t,!!n),o=e7(e.range,a);if(o){var l;let t,n;let a=(l=e=Object.assign(Object.assign({},e),{range:o}),{def:n=th((t=tu({editable:!1},i)).refined,t.extra,"",l.allDay,!0,i),ui:tV(n,r),instance:ts(n.defId,l.range),range:l.range,isStart:!0,isEnd:!0}),c=this.sliceRange(e.range,...s);for(let e of c)e.eventRange=a;return c}return[]}sliceEventRanges(e,t){let n=[];for(let r of e)n.push(...this.sliceEventRange(r,t));return n}sliceEventRange(e,t){let n=e.range;this.forceDayIfListItem&&"list-item"===e.ui.display&&(n={start:n.start,end:j(n.start,1)});let r=this.sliceRange(n,...t);for(let t of r)t.eventRange=e,t.isStart=e.isStart&&t.isStart,t.isEnd=e.isEnd&&t.isEnd;return r}}function nD(e,t){let n=e.activeRange;return t?n:{start:U(n.start,e.slotMinTime.milliseconds),end:U(n.end,e.slotMaxTime.milliseconds-864e5)}}function nC(e){let{instances:t}=e,n=[];for(let e in t)n.push(t[e].range);return n}let nw=/^(visible|hidden)$/;class nR extends eZ{constructor(){super(...arguments),this.handleEl=e=>{this.el=e,e$(this.props.elRef,e)}}render(){let{props:e}=this,{liquid:t,liquidIsAbsolute:n}=e,r=t&&n,i=["fc-scroller"];return t&&(n?i.push("fc-scroller-liquid-absolute"):i.push("fc-scroller-liquid")),(0,a.az)("div",{ref:this.handleEl,className:i.join(" "),style:{overflowX:e.overflowX,overflowY:e.overflowY,left:r&&-(e.overcomeLeft||0)||"",right:r&&-(e.overcomeRight||0)||"",bottom:r&&-(e.overcomeBottom||0)||"",marginLeft:!r&&-(e.overcomeLeft||0)||"",marginRight:!r&&-(e.overcomeRight||0)||"",marginBottom:!r&&-(e.overcomeBottom||0)||"",maxHeight:e.maxHeight||""}},e.children)}needsXScrolling(){if(nw.test(this.props.overflowX))return!1;let{el:e}=this,t=this.el.getBoundingClientRect().width-this.getYScrollbarWidth(),{children:n}=e;for(let e=0;e<n.length;e+=1)if(n[e].getBoundingClientRect().width>t)return!0;return!1}needsYScrolling(){if(nw.test(this.props.overflowY))return!1;let{el:e}=this,t=this.el.getBoundingClientRect().height-this.getXScrollbarWidth(),{children:n}=e;for(let e=0;e<n.length;e+=1)if(n[e].getBoundingClientRect().height>t)return!0;return!1}getXScrollbarWidth(){return nw.test(this.props.overflowX)?0:this.el.offsetHeight-this.el.clientHeight}getYScrollbarWidth(){return nw.test(this.props.overflowY)?0:this.el.offsetWidth-this.el.clientWidth}}class nS{constructor(e){this.masterCallback=e,this.currentMap={},this.depths={},this.callbackMap={},this.handleValue=(e,t)=>{let{depths:n,currentMap:r}=this,i=!1,s=!1;null!==e?(i=t in r,r[t]=e,n[t]=(n[t]||0)+1,s=!0):(n[t]-=1,n[t]||(delete r[t],delete this.callbackMap[t],i=!0)),this.masterCallback&&(i&&this.masterCallback(null,String(t)),s&&this.masterCallback(e,String(t)))}}createRef(e){let t=this.callbackMap[e];return t||(t=this.callbackMap[e]=t=>{this.handleValue(t,String(e))}),t}collect(e,t,n){return function(e,t=0,n,r=1){let i=[];null==n&&(n=Object.keys(e).length);for(let s=t;s<n;s+=r){let t=e[s];void 0!==t&&i.push(t)}return i}(this.currentMap,e,t,n)}getAll(){return eI(this.currentMap)}}function nk(e,t){return e.liquid&&t.liquid}function nx(e,t){return H(e,t,ez)}function nT(e,t){let n=[];for(let r of e){let e=r.span||1;for(let i=0;i<e;i+=1)n.push((0,a.az)("col",{style:{width:"shrink"===r.width?null==t?4:t:r.width||"",minWidth:r.minWidth||""}}))}return(0,a.az)("colgroup",{},...n)}function nM(e){return(0,a.az)("div",{className:"fc-scrollgrid-sticky-shim",style:{width:e.clientWidth,minWidth:e.tableMinWidth}})}function nO(e){let{stickyHeaderDates:t}=e;return(null==t||"auto"===t)&&(t="auto"===e.height||"auto"===e.viewHeight),t}function nI(e){let{stickyFooterScrollbar:t}=e;return(null==t||"auto"===t)&&(t="auto"===e.height||"auto"===e.viewHeight),t}class nz extends eZ{constructor(){super(...arguments),this.processCols=et(e=>e,nx),this.renderMicroColGroup=et(nT),this.scrollerRefs=new nS,this.scrollerElRefs=new nS(this._handleScrollerEl.bind(this)),this.state={shrinkWidth:null,forceYScrollbars:!1,scrollerClientWidths:{},scrollerClientHeights:{}},this.handleSizing=()=>{this.safeSetState(Object.assign({shrinkWidth:this.computeShrinkWidth()},this.computeScrollerDims()))}}render(){var e;let t,n,{props:r,state:i,context:s}=this,o=r.sections||[],l=this.processCols(r.cols),c=this.renderMicroColGroup(l,i.shrinkWidth),d=(e=r.liquid,t=["fc-scrollgrid",s.theme.getClass("table")],e&&t.push("fc-scrollgrid-liquid"),t);r.collapsibleWidth&&d.push("fc-scrollgrid-collapsible");let u=o.length,f=0,h=[],p=[],g=[];for(;f<u&&"header"===(n=o[f]).type;)h.push(this.renderSection(n,c,!0)),f+=1;for(;f<u&&"body"===(n=o[f]).type;)p.push(this.renderSection(n,c,!1)),f+=1;for(;f<u&&"footer"===(n=o[f]).type;)g.push(this.renderSection(n,c,!0)),f+=1;let m=!t2(),v={role:"rowgroup"};return(0,a.az)("table",{role:"grid",className:d.join(" "),style:{height:r.height}},!!(!m&&h.length)&&(0,a.az)("thead",v,...h),!!(!m&&p.length)&&(0,a.az)("tbody",v,...p),!!(!m&&g.length)&&(0,a.az)("tfoot",v,...g),m&&(0,a.az)("tbody",v,...h,...p,...g))}renderSection(e,t,n){var r;let i;return"outerContent"in e?(0,a.az)(a.HY,{key:e.key},e.outerContent):(0,a.az)("tr",{key:e.key,role:"presentation",className:(r=this.props.liquid,i=["fc-scrollgrid-section",`fc-scrollgrid-section-${e.type}`,e.className],r&&e.liquid&&null==e.maxHeight&&i.push("fc-scrollgrid-section-liquid"),e.isSticky&&i.push("fc-scrollgrid-section-sticky"),i).join(" ")},this.renderChunkTd(e,t,e.chunk,n))}renderChunkTd(e,t,n,r){if("outerContent"in n)return n.outerContent;let{props:i}=this,{forceYScrollbars:s,scrollerClientWidths:o,scrollerClientHeights:l}=this.state,c=null!=e.maxHeight||nk(i,e),d=nk(i,e),u=i.liquid?s?"scroll":c?"auto":"hidden":"visible",f=e.key,h=function(e,t,n,r){let{expandRows:i}=n;return"function"==typeof t.content?t.content(n):(0,a.az)("table",{role:"presentation",className:[t.tableClassName,e.syncRowHeights?"fc-scrollgrid-sync-table":""].join(" "),style:{minWidth:n.tableMinWidth,width:n.clientWidth,height:i?n.clientHeight:""}},n.tableColGroupNode,(0,a.az)(r?"thead":"tbody",{role:"presentation"},"function"==typeof t.rowContent?t.rowContent(n):t.rowContent))}(e,n,{tableColGroupNode:t,tableMinWidth:"",clientWidth:i.collapsibleWidth||void 0===o[f]?null:o[f],clientHeight:void 0!==l[f]?l[f]:null,expandRows:e.expandRows,syncRowHeights:!1,rowSyncHeights:[],reportRowHeightChange:()=>{}},r);return(0,a.az)(r?"th":"td",{ref:n.elRef,role:"presentation"},(0,a.az)("div",{className:`fc-scroller-harness${d?" fc-scroller-harness-liquid":""}`},(0,a.az)(nR,{ref:this.scrollerRefs.createRef(f),elRef:this.scrollerElRefs.createRef(f),overflowY:u,overflowX:i.liquid?"hidden":"visible",maxHeight:e.maxHeight,liquid:d,liquidIsAbsolute:!0},h)))}_handleScrollerEl(e,t){let n=function(e,t){for(let n of e)if(n.key===t)return n;return null}(this.props.sections,t);n&&e$(n.chunk.scrollerElRef,e)}componentDidMount(){this.handleSizing(),this.context.addResizeHandler(this.handleSizing)}componentDidUpdate(){this.handleSizing()}componentWillUnmount(){this.context.removeResizeHandler(this.handleSizing)}computeShrinkWidth(){return!function(e){for(let t of e)if("shrink"===t.width)return!0;return!1}(this.props.cols)?0:function(e){let t=function(e,t){let n=e instanceof HTMLElement?[e]:e,r=[];for(let e=0;e<n.length;e+=1){let i=n[e].querySelectorAll(t);for(let e=0;e<i.length;e+=1)r.push(i[e])}return r}(e,".fc-scrollgrid-shrink"),n=0;for(let e of t)n=Math.max(n,function(e){let t=e.querySelector(".fc-scrollgrid-shrink-frame"),n=e.querySelector(".fc-scrollgrid-shrink-cushion");if(!t)throw Error("needs fc-scrollgrid-shrink-frame className");if(!n)throw Error("needs fc-scrollgrid-shrink-cushion className");return e.getBoundingClientRect().width-t.getBoundingClientRect().width+n.getBoundingClientRect().width}(e));return Math.ceil(n)}(this.scrollerElRefs.getAll())}computeScrollerDims(){let e,t;let n=(s||((e=document.createElement("div")).style.overflow="scroll",e.style.position="absolute",e.style.top="-9999px",e.style.left="-9999px",document.body.appendChild(e),t={x:e.offsetHeight-e.clientHeight,y:e.offsetWidth-e.clientWidth},document.body.removeChild(e),s=t),s),{scrollerRefs:r,scrollerElRefs:i}=this,a=!1,o={},l={};for(let e in r.currentMap){let t=r.currentMap[e];if(t&&t.needsYScrolling()){a=!0;break}}for(let e of this.props.sections){let t=e.key,r=i.currentMap[t];if(r){let e=r.parentNode;o[t]=Math.floor(e.getBoundingClientRect().width-(a?n.y:0)),l[t]=Math.floor(e.getBoundingClientRect().height)}}return{forceYScrollbars:a,scrollerClientWidths:o,scrollerClientHeights:l}}}nz.addStateEquality({scrollerClientWidths:ez,scrollerClientHeights:ez});class nN extends eZ{constructor(){super(...arguments),this.buildPublicEvent=et((e,t,n)=>new tP(e,t,n)),this.handleEl=e=>{if(this.el=e,e$(this.props.elRef,e),e){var t;t=this.props.seg,e.fcSeg=t}}}render(){var e;let t;let{props:n,context:r}=this,{options:i}=r,{seg:s}=n,{eventRange:o}=s,{ui:l}=o,c={event:this.buildPublicEvent(r,o.def,o.instance),view:r.viewApi,timeText:n.timeText,textColor:l.textColor,backgroundColor:l.backgroundColor,borderColor:l.borderColor,isDraggable:!n.disableDragging&&function(e,t){let{pluginHooks:n}=t,r=n.isDraggableTransformers,{def:i,ui:s}=e.eventRange,a=s.startEditable;for(let e of r)a=e(a,i,s,t);return a}(s,r),isStartResizable:!n.disableResizing&&s.isStart&&s.eventRange.ui.durationEditable&&r.options.eventResizableFromStart,isEndResizable:!n.disableResizing&&(e=s).isEnd&&e.eventRange.ui.durationEditable,isMirror:!!(n.isDragging||n.isResizing||n.isDateSelecting),isStart:!!s.isStart,isEnd:!!s.isEnd,isPast:!!n.isPast,isFuture:!!n.isFuture,isToday:!!n.isToday,isSelected:!!n.isSelected,isDragging:!!n.isDragging,isResizing:!!n.isResizing};return(0,a.az)(e2,{elRef:this.handleEl,elTag:n.elTag,elAttrs:n.elAttrs,elClasses:[...(t=["fc-event"],c.isMirror&&t.push("fc-event-mirror"),c.isDraggable&&t.push("fc-event-draggable"),(c.isStartResizable||c.isEndResizable)&&t.push("fc-event-resizable"),c.isDragging&&t.push("fc-event-dragging"),c.isResizing&&t.push("fc-event-resizing"),c.isSelected&&t.push("fc-event-selected"),c.isStart&&t.push("fc-event-start"),c.isEnd&&t.push("fc-event-end"),c.isPast&&t.push("fc-event-past"),c.isToday&&t.push("fc-event-today"),c.isFuture&&t.push("fc-event-future"),t),...s.eventRange.ui.classNames,...n.elClasses||[]],elStyle:n.elStyle,renderProps:c,generatorName:"eventContent",customGenerator:i.eventContent,defaultGenerator:n.defaultGenerator,classNameGenerator:i.eventClassNames,didMount:i.eventDidMount,willUnmount:i.eventWillUnmount},n.children)}componentDidUpdate(e){if(this.el&&this.props.seg!==e.seg){var t,n;t=this.el,n=this.props.seg,t.fcSeg=n}}}class nH extends eZ{render(){let{props:e,context:t}=this,{options:n}=t,{seg:r}=e,{ui:i}=r.eventRange,s=tG(r,n.eventTimeFormat||e.defaultTimeFormat,t,e.defaultDisplayEventTime,e.defaultDisplayEventEnd);return(0,a.az)(nN,Object.assign({},e,{elTag:"a",elStyle:{borderColor:i.borderColor,backgroundColor:i.backgroundColor},elAttrs:tZ(r,t),defaultGenerator:nP,timeText:s}),(e,t)=>(0,a.az)(a.HY,null,(0,a.az)(e,{elTag:"div",elClasses:["fc-event-main"],elStyle:{color:t.textColor}}),!!t.isStartResizable&&(0,a.az)("div",{className:"fc-event-resizer fc-event-resizer-start"}),!!t.isEndResizable&&(0,a.az)("div",{className:"fc-event-resizer fc-event-resizer-end"})))}}function nP(e){return(0,a.az)("div",{className:"fc-event-main-frame"},e.timeText&&(0,a.az)("div",{className:"fc-event-time"},e.timeText),(0,a.az)("div",{className:"fc-event-title-container"},(0,a.az)("div",{className:"fc-event-title fc-sticky"},e.event.title||(0,a.az)(a.HY,null,"\xa0"))))}nH.addPropsEquality({seg:ez});let nB=em({day:"numeric"});class nj extends eZ{constructor(){super(...arguments),this.refineRenderProps=en(nF)}render(){let{props:e,context:t}=this,{options:n}=t,r=this.refineRenderProps({date:e.date,dateProfile:e.dateProfile,todayRange:e.todayRange,isMonthStart:e.isMonthStart||!1,showDayNumber:e.showDayNumber,extraRenderProps:e.extraRenderProps,viewApi:t.viewApi,dateEnv:t.dateEnv,monthStartFormat:n.monthStartFormat});return(0,a.az)(e2,{elRef:e.elRef,elTag:e.elTag,elAttrs:Object.assign(Object.assign({},e.elAttrs),r.isDisabled?{}:{"data-date":X(e.date)}),elClasses:[...ne(r,t.theme),...e.elClasses||[]],elStyle:e.elStyle,renderProps:r,generatorName:"dayCellContent",customGenerator:n.dayCellContent,defaultGenerator:e.defaultGenerator,classNameGenerator:r.isDisabled?void 0:n.dayCellClassNames,didMount:n.dayCellDidMount,willUnmount:n.dayCellWillUnmount},e.children)}}function nU(e){return!!(e.dayCellContent||eX("dayCellContent",e))}function nF(e){let{date:t,dateEnv:n,dateProfile:r,isMonthStart:i}=e,s=t9(t,e.todayRange,null,r),a=e.showDayNumber?n.format(t,i?e.monthStartFormat:nB):"";return Object.assign(Object.assign(Object.assign({date:n.toDate(t),view:e.viewApi},s),{isMonthStart:i,dayNumberText:a}),e.extraRenderProps)}class nW extends eZ{render(){let{props:e}=this,{seg:t}=e;return(0,a.az)(nN,{elTag:"div",elClasses:["fc-bg-event"],elStyle:{backgroundColor:t.eventRange.ui.backgroundColor},defaultGenerator:nV,seg:t,timeText:"",isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,isPast:e.isPast,isFuture:e.isFuture,isToday:e.isToday,disableDragging:!0,disableResizing:!0})}}function nV(e){let{title:t}=e.event;return t&&(0,a.az)("div",{className:"fc-event-title"},e.event.title)}function nL(e){return(0,a.az)("div",{className:`fc-${e}`})}let nQ=e=>(0,a.az)(eG.Consumer,null,t=>{let{dateEnv:n,options:r}=t,{date:i}=e,s=r.weekNumberFormat||e.defaultFormat,o=n.computeWeekNumber(i),l=n.format(i,s);return(0,a.az)(e2,{elRef:e.elRef,elTag:e.elTag,elAttrs:e.elAttrs,elClasses:e.elClasses,elStyle:e.elStyle,renderProps:{num:o,text:l,date:i},generatorName:"weekNumberContent",customGenerator:r.weekNumberContent,defaultGenerator:nG,classNameGenerator:r.weekNumberClassNames,didMount:r.weekNumberDidMount,willUnmount:r.weekNumberWillUnmount},e.children)});function nG(e){return e.text}class nY extends eZ{constructor(){super(...arguments),this.state={titleId:A()},this.handleRootEl=e=>{this.rootEl=e,this.props.elRef&&e$(this.props.elRef,e)},this.handleDocumentMouseDown=e=>{let t=function(e){var t,n;return null!==(n=null===(t=e.composedPath)||void 0===t?void 0:t.call(e)[0])&&void 0!==n?n:e.target}(e);this.rootEl.contains(t)||this.handleCloseClick()},this.handleDocumentKeyDown=e=>{"Escape"===e.key&&this.handleCloseClick()},this.handleCloseClick=()=>{let{onClose:e}=this.props;e&&e()}}render(){let{theme:e,options:t}=this.context,{props:n,state:r}=this,i=["fc-popover",e.getClass("popover")].concat(n.extraClassNames||[]);return(0,o.jz)((0,a.az)("div",Object.assign({},n.extraAttrs,{id:n.id,className:i.join(" "),"aria-labelledby":r.titleId,ref:this.handleRootEl}),(0,a.az)("div",{className:"fc-popover-header "+e.getClass("popoverHeader")},(0,a.az)("span",{className:"fc-popover-title",id:r.titleId},n.title),(0,a.az)("span",{className:"fc-popover-close "+e.getIconClass("close"),title:t.closeHint,onClick:this.handleCloseClick})),(0,a.az)("div",{className:"fc-popover-body "+e.getClass("popoverContent")},n.children)),n.parentEl)}componentDidMount(){document.addEventListener("mousedown",this.handleDocumentMouseDown),document.addEventListener("keydown",this.handleDocumentKeyDown),this.updateSize()}componentWillUnmount(){document.removeEventListener("mousedown",this.handleDocumentMouseDown),document.removeEventListener("keydown",this.handleDocumentKeyDown)}updateSize(){let{isRtl:e}=this.context,{alignmentEl:t,alignGridTop:n}=this.props,{rootEl:r}=this,i=function(e){let t=function(e){let t=[];for(;e instanceof HTMLElement;){let n=window.getComputedStyle(e);if("fixed"===n.position)break;/(auto|scroll)/.test(n.overflow+n.overflowY+n.overflowX)&&t.push(e),e=e.parentNode}return t}(e),n=e.getBoundingClientRect();for(let e of t){let t=function(e,t){let n={left:Math.max(e.left,t.left),right:Math.min(e.right,t.right),top:Math.max(e.top,t.top),bottom:Math.min(e.bottom,t.bottom)};return n.left<n.right&&n.top<n.bottom&&n}(n,e.getBoundingClientRect());if(!t)return null;n=t}return n}(t);if(i){let s=r.getBoundingClientRect(),a=n?m(t,".fc-scrollgrid").getBoundingClientRect().top:i.top,o=e?i.right-s.width:i.left;a=Math.max(a,10),o=Math.max(o=Math.min(o,document.documentElement.clientWidth-10-s.width),10);let l=r.offsetParent.getBoundingClientRect();(function(e,t){for(let n in t)b(e,n,t[n])})(r,{top:a-l.top,left:o-l.left})}}}class nq extends na{constructor(){super(...arguments),this.handleRootEl=e=>{this.rootEl=e,e?this.context.registerInteractiveComponent(this,{el:e,useEventCenter:!1}):this.context.unregisterInteractiveComponent(this)}}render(){let{options:e,dateEnv:t}=this.context,{props:n}=this,{startDate:r,todayRange:i,dateProfile:s}=n,o=t.format(r,e.dayPopoverFormat);return(0,a.az)(nj,{elRef:this.handleRootEl,date:r,dateProfile:s,todayRange:i},(t,r,i)=>(0,a.az)(nY,{elRef:i.ref,id:n.id,title:o,extraClassNames:["fc-more-popover"].concat(i.className||[]),extraAttrs:i,parentEl:n.parentEl,alignmentEl:n.alignmentEl,alignGridTop:n.alignGridTop,onClose:n.onClose},nU(e)&&(0,a.az)(t,{elTag:"div",elClasses:["fc-more-popover-misc"]}),n.children))}queryHit(e,t,n,r){let{rootEl:i,props:s}=this;return e>=0&&e<n&&t>=0&&t<r?{dateProfile:s.dateProfile,dateSpan:Object.assign({allDay:!s.forceTimed,range:{start:s.startDate,end:s.endDate}},s.extraDateSpan),dayEl:i,rect:{left:0,top:0,right:n,bottom:r},layer:1}:null}}class nZ extends eZ{constructor(){super(...arguments),this.state={isPopoverOpen:!1,popoverId:A()},this.handleLinkEl=e=>{this.linkEl=e,this.props.elRef&&e$(this.props.elRef,e)},this.handleClick=e=>{let{props:t,context:n}=this,{moreLinkClick:r}=n.options,i=nJ(t).start;function s(e){let{def:t,instance:r,range:i}=e.eventRange;return{event:new tP(n,t,r),start:n.dateEnv.toDate(i.start),end:n.dateEnv.toDate(i.end),isStart:e.isStart,isEnd:e.isEnd}}"function"==typeof r&&(r=r({date:i,allDay:!!t.allDayDate,allSegs:t.allSegs.map(s),hiddenSegs:t.hiddenSegs.map(s),jsEvent:e,view:n.viewApi})),r&&"popover"!==r?"string"==typeof r&&n.calendarApi.zoomTo(i,r):this.setState({isPopoverOpen:!0})},this.handlePopoverClose=()=>{this.setState({isPopoverOpen:!1})}}render(){let{props:e,state:t}=this;return(0,a.az)(eG.Consumer,null,n=>{let{viewApi:r,options:i,calendarApi:s}=n,{moreLinkText:o}=i,{moreCnt:l}=e,c=nJ(e),d="function"==typeof o?o.call(s,l):`+${l} ${o}`,u=k(i.moreLinkHint,[l],d),f={num:l,shortText:`+${l}`,text:d,view:r};return(0,a.az)(a.HY,null,!!e.moreCnt&&(0,a.az)(e2,{elTag:e.elTag||"a",elRef:this.handleLinkEl,elClasses:[...e.elClasses||[],"fc-more-link"],elStyle:e.elStyle,elAttrs:Object.assign(Object.assign(Object.assign({},e.elAttrs),D(this.handleClick)),{title:u,"aria-expanded":t.isPopoverOpen,"aria-controls":t.isPopoverOpen?t.popoverId:""}),renderProps:f,generatorName:"moreLinkContent",customGenerator:i.moreLinkContent,defaultGenerator:e.defaultGenerator||n$,classNameGenerator:i.moreLinkClassNames,didMount:i.moreLinkDidMount,willUnmount:i.moreLinkWillUnmount},e.children),t.isPopoverOpen&&(0,a.az)(nq,{id:t.popoverId,startDate:c.start,endDate:c.end,dateProfile:e.dateProfile,todayRange:e.todayRange,extraDateSpan:e.extraDateSpan,parentEl:this.parentEl,alignmentEl:e.alignmentElRef?e.alignmentElRef.current:this.linkEl,alignGridTop:e.alignGridTop,forceTimed:e.forceTimed,onClose:this.handlePopoverClose},e.popoverContent()))})}componentDidMount(){this.updateParentEl()}componentDidUpdate(){this.updateParentEl()}updateParentEl(){this.linkEl&&(this.parentEl=m(this.linkEl,".fc-view-harness"))}}function n$(e){return e.text}function nJ(e){if(e.allDayDate)return{start:e.allDayDate,end:j(e.allDayDate,1)};let{hiddenSegs:t}=e;return{start:t.reduce(nX).eventRange.range.start,end:t.reduce(nK).eventRange.range.end}}function nX(e,t){return e.eventRange.range.start<t.eventRange.range.start?e:t}function nK(e,t){return e.eventRange.range.end>t.eventRange.range.end?e:t}class n0{constructor(){this.handlers=[]}set(e){for(let t of(this.currentValue=e,this.handlers))t(e)}subscribe(e){this.handlers.push(e),void 0!==this.currentValue&&e(this.currentValue)}}class n1 extends n0{constructor(){super(...arguments),this.map=new Map}handle(e){let{map:t}=this,n=!1;e.isActive?(t.set(e.id,e),n=!0):t.has(e.id)&&(t.delete(e.id),n=!0),n&&this.set(t)}}},58688:(e,t,n)=>{"use strict";n.d(t,{Z:()=>z});var r=n(37543),i=n(75867),s=n(95284);class a extends i.bc{constructor(){super(...arguments),this.headerElRef=(0,s.Vf)()}renderSimpleLayout(e,t){let{props:n,context:r}=this,a=[],o=(0,i.ca)(r.options);return e&&a.push({type:"header",key:"header",isSticky:o,chunk:{elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}}),a.push({type:"body",key:"body",liquid:!0,chunk:{content:t}}),(0,s.az)(i.cq,{elClasses:["fc-daygrid"],viewSpec:r.viewSpec},(0,s.az)(i.bZ,{liquid:!n.isHeightAuto&&!n.forPrint,collapsibleWidth:n.forPrint,cols:[],sections:a}))}renderHScrollLayout(e,t,n,r){let a=this.context.pluginHooks.scrollGridImpl;if(!a)throw Error("No ScrollGrid implementation");let{props:o,context:l}=this,c=!o.forPrint&&(0,i.ca)(l.options),d=!o.forPrint&&(0,i.c9)(l.options),u=[];return e&&u.push({type:"header",key:"header",isSticky:c,chunks:[{key:"main",elRef:this.headerElRef,tableClassName:"fc-col-header",rowContent:e}]}),u.push({type:"body",key:"body",liquid:!0,chunks:[{key:"main",content:t}]}),d&&u.push({type:"footer",key:"footer",isSticky:!0,chunks:[{key:"main",content:i.c8}]}),(0,s.az)(i.cq,{elClasses:["fc-daygrid"],viewSpec:l.viewSpec},(0,s.az)(a,{liquid:!o.isHeightAuto&&!o.forPrint,forPrint:o.forPrint,collapsibleWidth:o.forPrint,colGroups:[{cols:[{span:n,minWidth:r}]}],sections:u}))}}function o(e,t){let n=[];for(let e=0;e<t;e+=1)n[e]=[];for(let t of e)n[t.row].push(t);return n}function l(e,t){let n=[];for(let e=0;e<t;e+=1)n[e]=[];for(let t of e)n[t.firstCol].push(t);return n}function c(e,t){let n=[];if(e){for(let r=0;r<t;r+=1)n[r]={affectedInstances:e.affectedInstances,isEvent:e.isEvent,segs:[]};for(let t of e.segs)n[t.row].segs.push(t)}else for(let e=0;e<t;e+=1)n[e]=null;return n}let d=(0,i.x)({hour:"numeric",minute:"2-digit",omitZeroMinute:!0,meridiem:"narrow"});function u(e){let{display:t}=e.eventRange.ui;return"list-item"===t||"auto"===t&&!e.eventRange.def.allDay&&e.firstCol===e.lastCol&&e.isStart&&e.isEnd}class f extends i.B{render(){let{props:e}=this;return(0,s.az)(i.cg,Object.assign({},e,{elClasses:["fc-daygrid-event","fc-daygrid-block-event","fc-h-event"],defaultTimeFormat:d,defaultDisplayEventEnd:e.defaultDisplayEventEnd,disableResizing:!e.seg.eventRange.def.allDay}))}}class h extends i.B{render(){let{props:e,context:t}=this,{options:n}=t,{seg:r}=e,a=n.eventTimeFormat||d,o=(0,i.bO)(r,a,t,!0,e.defaultDisplayEventEnd);return(0,s.az)(i.ck,Object.assign({},e,{elTag:"a",elClasses:["fc-daygrid-event","fc-daygrid-dot-event"],elAttrs:(0,i.bS)(e.seg,t),defaultGenerator:p,timeText:o,isResizing:!1,isDateSelecting:!1}))}}function p(e){return(0,s.az)(s.HY,null,(0,s.az)("div",{className:"fc-daygrid-event-dot",style:{borderColor:e.borderColor||e.backgroundColor}}),e.timeText&&(0,s.az)("div",{className:"fc-event-time"},e.timeText),(0,s.az)("div",{className:"fc-event-title"},e.event.title||(0,s.az)(s.HY,null,"\xa0")))}class g extends i.B{constructor(){super(...arguments),this.compileSegs=(0,i.z)(m)}render(){let{props:e}=this,{allSegs:t,invisibleSegs:n}=this.compileSegs(e.singlePlacements);return(0,s.az)(i.co,{elClasses:["fc-daygrid-more-link"],dateProfile:e.dateProfile,todayRange:e.todayRange,allDayDate:e.allDayDate,moreCnt:e.moreCnt,allSegs:t,hiddenSegs:n,alignmentElRef:e.alignmentElRef,alignGridTop:e.alignGridTop,extraDateSpan:e.extraDateSpan,popoverContent:()=>{let n=(e.eventDrag?e.eventDrag.affectedInstances:null)||(e.eventResize?e.eventResize.affectedInstances:null)||{};return(0,s.az)(s.HY,null,t.map(t=>{let r=t.eventRange.instance.instanceId;return(0,s.az)("div",{className:"fc-daygrid-event-harness",key:r,style:{visibility:n[r]?"hidden":""}},u(t)?(0,s.az)(h,Object.assign({seg:t,isDragging:!1,isSelected:r===e.eventSelection,defaultDisplayEventEnd:!1},(0,i.bQ)(t,e.todayRange))):(0,s.az)(f,Object.assign({seg:t,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:r===e.eventSelection,defaultDisplayEventEnd:!1},(0,i.bQ)(t,e.todayRange))))}))}})}}function m(e){let t=[],n=[];for(let r of e)t.push(r.seg),r.isVisible||n.push(r.seg);return{allSegs:t,invisibleSegs:n}}let v=(0,i.x)({week:"narrow"});class b extends i.bc{constructor(){super(...arguments),this.rootElRef=(0,s.Vf)(),this.state={dayNumberId:(0,i.a3)()},this.handleRootEl=e=>{(0,i.W)(this.rootElRef,e),(0,i.W)(this.props.elRef,e)}}render(){let{context:e,props:t,state:n,rootElRef:r}=this,{options:a,dateEnv:o}=e,{date:l,dateProfile:c}=t,d=t.showDayNumber&&function(e,t,n){let{start:r,end:s}=t,a=(0,i.be)(s,-1),o=n.getYear(r),l=n.getMonth(r),c=n.getYear(a),d=n.getMonth(a);return!(o===c&&l===d)&&!!(e.valueOf()===r.valueOf()||1===n.getDay(e)&&e.valueOf()<s.valueOf())}(l,c.currentRange,o);return(0,s.az)(i.ci,{elTag:"td",elRef:this.handleRootEl,elClasses:["fc-daygrid-day",...t.extraClassNames||[]],elAttrs:Object.assign(Object.assign(Object.assign({},t.extraDataAttrs),t.showDayNumber?{"aria-labelledby":n.dayNumberId}:{}),{role:"gridcell"}),defaultGenerator:y,date:l,dateProfile:c,todayRange:t.todayRange,showDayNumber:t.showDayNumber,isMonthStart:d,extraRenderProps:t.extraRenderProps},(o,c)=>(0,s.az)("div",{ref:t.innerElRef,className:"fc-daygrid-day-frame fc-scrollgrid-sync-inner",style:{minHeight:t.minHeight}},t.showWeekNumber&&(0,s.az)(i.cn,{elTag:"a",elClasses:["fc-daygrid-week-number"],elAttrs:(0,i.a_)(e,l,"week"),date:l,defaultFormat:v}),!c.isDisabled&&(t.showDayNumber||(0,i.cj)(a)||t.forceDayTop)?(0,s.az)("div",{className:"fc-daygrid-day-top"},(0,s.az)(o,{elTag:"a",elClasses:["fc-daygrid-day-number",d&&"fc-daygrid-month-start"],elAttrs:Object.assign(Object.assign({},(0,i.a_)(e,l)),{id:n.dayNumberId})})):t.showDayNumber?(0,s.az)("div",{className:"fc-daygrid-day-top",style:{visibility:"hidden"}},(0,s.az)("a",{className:"fc-daygrid-day-number"},"\xa0")):void 0,(0,s.az)("div",{className:"fc-daygrid-day-events",ref:t.fgContentElRef},t.fgContent,(0,s.az)("div",{className:"fc-daygrid-day-bottom",style:{marginTop:t.moreMarginTop}},(0,s.az)(g,{allDayDate:l,singlePlacements:t.singlePlacements,moreCnt:t.moreCnt,alignmentElRef:r,alignGridTop:!t.showDayNumber,extraDateSpan:t.extraDateSpan,dateProfile:t.dateProfile,eventSelection:t.eventSelection,eventDrag:t.eventDrag,eventResize:t.eventResize,todayRange:t.todayRange}))),(0,s.az)("div",{className:"fc-daygrid-day-bg"},t.bgContent)))}}function y(e){return e.dayNumberText||(0,s.az)(s.HY,null,"\xa0")}function A(e){return e.eventRange.instance.instanceId+":"+e.firstCol}function _(e){return A(e)+":"+e.lastCol}function E(e,t,n,r){if(e.firstCol===t&&e.lastCol===n-1)return e;let s=e.eventRange,a=s.range,o=(0,i.o)(a,{start:r[t].date,end:(0,i.t)(r[n-1].date,1)});return Object.assign(Object.assign({},e),{firstCol:t,lastCol:n-1,eventRange:{def:s.def,ui:Object.assign(Object.assign({},s.ui),{durationEditable:!1}),instance:s.instance,range:o},isStart:e.isStart&&o.start.valueOf()===a.start.valueOf(),isEnd:e.isEnd&&o.end.valueOf()===a.end.valueOf()})}class D extends i.by{constructor(){super(...arguments),this.hiddenConsumes=!1,this.forceHidden={}}addSegs(e){let t=super.addSegs(e),{entriesByLevel:n}=this,r=e=>!this.forceHidden[(0,i.bz)(e)];for(let e=0;e<n.length;e+=1)n[e]=n[e].filter(r);return t}handleInvalidInsertion(e,t,n){let{entriesByLevel:r,forceHidden:s}=this,{touchingEntry:a,touchingLevel:o,touchingLateral:l}=e;if(this.hiddenConsumes&&a){let e=(0,i.bz)(a);if(!s[e]){if(this.allowReslicing){let e=Object.assign(Object.assign({},a),{span:(0,i.bD)(a.span,t.span)});s[(0,i.bz)(e)]=!0,r[o][l]=e,n.push(e),this.splitEntry(a,t,n)}else s[e]=!0,n.push(a)}}super.handleInvalidInsertion(e,t,n)}}class C extends i.bc{constructor(){super(...arguments),this.cellElRefs=new i.cd,this.frameElRefs=new i.cd,this.fgElRefs=new i.cd,this.segHarnessRefs=new i.cd,this.rootElRef=(0,s.Vf)(),this.state={framePositions:null,maxContentHeight:null,segHeights:{}},this.handleResize=e=>{e&&this.updateSizing(!0)}}render(){let{props:e,state:t,context:n}=this,{options:r}=n,a=e.cells.length,o=l(e.businessHourSegs,a),c=l(e.bgEventSegs,a),d=l(this.getHighlightSegs(),a),u=l(this.getMirrorSegs(),a),{singleColPlacements:f,multiColPlacements:h,moreCnts:p,moreMarginTops:g}=function(e,t,n,r,i,s,a){let o=new D(t=>i[e[t.index].eventRange.instance.instanceId+":"+t.span.start+":"+(t.span.end-1)]||1);o.allowReslicing=!0,o.strictOrder=r,!0===t||!0===n?(o.maxCoord=s,o.hiddenConsumes=!0):"number"==typeof t?o.maxStackCnt=t:"number"==typeof n&&(o.maxStackCnt=n,o.hiddenConsumes=!0);let l=[],c=[];for(let t=0;t<e.length;t+=1){let n=e[t];null!=i[_(n)]?l.push({index:t,span:{start:n.firstCol,end:n.lastCol+1}}):c.push(n)}let d=o.addSegs(l),{singleColPlacements:u,multiColPlacements:f,leftoverMargins:h}=function(e,t,n){let r=function(e,t){let n=[];for(let e=0;e<t;e+=1)n.push([]);for(let t of e)for(let e=t.span.start;e<t.span.end;e+=1)n[e].push(t);return n}(e,n.length),i=[],s=[],a=[];for(let e=0;e<n.length;e+=1){let o=r[e],l=[],c=0,d=0;for(let r of o){let i=t[r.index];l.push({seg:E(i,e,e+1,n),isVisible:!0,isAbsolute:!1,absoluteTop:r.levelCoord,marginTop:r.levelCoord-c}),c=r.levelCoord+r.thickness}let u=[];for(let r of(c=0,d=0,o)){let i=t[r.index],s=r.span.end-r.span.start>1,a=r.span.start===e;d+=r.levelCoord-c,c=r.levelCoord+r.thickness,s?(d+=r.thickness,a&&u.push({seg:E(i,r.span.start,r.span.end,n),isVisible:!0,isAbsolute:!0,absoluteTop:r.levelCoord,marginTop:0})):a&&(u.push({seg:E(i,r.span.start,r.span.end,n),isVisible:!0,isAbsolute:!1,absoluteTop:r.levelCoord,marginTop:d}),d=0)}i.push(l),s.push(u),a.push(d)}return{singleColPlacements:i,multiColPlacements:s,leftoverMargins:a}}(o.toRects(),e,a),p=[],g=[];for(let e of c){f[e.firstCol].push({seg:e,isVisible:!1,isAbsolute:!0,absoluteTop:0,marginTop:0});for(let t=e.firstCol;t<=e.lastCol;t+=1)u[t].push({seg:E(e,t,t+1,a),isVisible:!1,isAbsolute:!1,absoluteTop:0,marginTop:0})}for(let e=0;e<a.length;e+=1)p.push(0);for(let t of d){let n=e[t.index],r=t.span;f[r.start].push({seg:E(n,r.start,r.end,a),isVisible:!1,isAbsolute:!0,absoluteTop:0,marginTop:0});for(let e=r.start;e<r.end;e+=1)p[e]+=1,u[e].push({seg:E(n,e,e+1,a),isVisible:!1,isAbsolute:!1,absoluteTop:0,marginTop:0})}for(let e=0;e<a.length;e+=1)g.push(h[e]);return{singleColPlacements:u,multiColPlacements:f,moreCnts:p,moreMarginTops:g}}((0,i.bP)(e.fgEventSegs,r.eventOrder),e.dayMaxEvents,e.dayMaxEventRows,r.eventOrderStrict,t.segHeights,t.maxContentHeight,e.cells),m=e.eventDrag&&e.eventDrag.affectedInstances||e.eventResize&&e.eventResize.affectedInstances||{};return(0,s.az)("tr",{ref:this.rootElRef,role:"row"},e.renderIntro&&e.renderIntro(),e.cells.map((t,n)=>{let r=this.renderFgSegs(n,e.forPrint?f[n]:h[n],e.todayRange,m),i=this.renderFgSegs(n,function(e,t){if(!e.length)return[];let n=function(e){let t={};for(let n of e)for(let e of n)t[e.seg.eventRange.instance.instanceId]=e.absoluteTop;return t}(t);return e.map(e=>({seg:e,isVisible:!0,isAbsolute:!0,absoluteTop:n[e.eventRange.instance.instanceId],marginTop:0}))}(u[n],h),e.todayRange,{},!!e.eventDrag,!!e.eventResize,!1);return(0,s.az)(b,{key:t.key,elRef:this.cellElRefs.createRef(t.key),innerElRef:this.frameElRefs.createRef(t.key),dateProfile:e.dateProfile,date:t.date,showDayNumber:e.showDayNumbers,showWeekNumber:e.showWeekNumbers&&0===n,forceDayTop:e.showWeekNumbers,todayRange:e.todayRange,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,extraRenderProps:t.extraRenderProps,extraDataAttrs:t.extraDataAttrs,extraClassNames:t.extraClassNames,extraDateSpan:t.extraDateSpan,moreCnt:p[n],moreMarginTop:g[n],singlePlacements:f[n],fgContentElRef:this.fgElRefs.createRef(t.key),fgContent:(0,s.az)(s.HY,null,(0,s.az)(s.HY,null,r),(0,s.az)(s.HY,null,i)),bgContent:(0,s.az)(s.HY,null,this.renderFillSegs(d[n],"highlight"),this.renderFillSegs(o[n],"non-business"),this.renderFillSegs(c[n],"bg-event")),minHeight:e.cellMinHeight})}))}componentDidMount(){this.updateSizing(!0),this.context.addResizeHandler(this.handleResize)}componentDidUpdate(e,t){let n=this.props;this.updateSizing(!(0,i.E)(e,n))}componentWillUnmount(){this.context.removeResizeHandler(this.handleResize)}getHighlightSegs(){let{props:e}=this;return e.eventDrag&&e.eventDrag.segs.length?e.eventDrag.segs:e.eventResize&&e.eventResize.segs.length?e.eventResize.segs:e.dateSelectionSegs}getMirrorSegs(){let{props:e}=this;return e.eventResize&&e.eventResize.segs.length?e.eventResize.segs:[]}renderFgSegs(e,t,n,r,a,o,l){let{context:c}=this,{eventSelection:d}=this.props,{framePositions:p}=this.state,g=1===this.props.cells.length,m=a||o||l,v=[];if(p)for(let e of t){let{seg:t}=e,{instanceId:b}=t.eventRange.instance,y=e.isVisible&&!r[b],E=e.isAbsolute,D="",C="";E&&(c.isRtl?(C=0,D=p.lefts[t.lastCol]-p.lefts[t.firstCol]):(D=0,C=p.rights[t.firstCol]-p.rights[t.lastCol])),v.push((0,s.az)("div",{className:"fc-daygrid-event-harness"+(E?" fc-daygrid-event-harness-abs":""),key:A(t),ref:m?null:this.segHarnessRefs.createRef(_(t)),style:{visibility:y?"":"hidden",marginTop:E?"":e.marginTop,top:E?e.absoluteTop:"",left:D,right:C}},u(t)?(0,s.az)(h,Object.assign({seg:t,isDragging:a,isSelected:b===d,defaultDisplayEventEnd:g},(0,i.bQ)(t,n))):(0,s.az)(f,Object.assign({seg:t,isDragging:a,isResizing:o,isDateSelecting:l,isSelected:b===d,defaultDisplayEventEnd:g},(0,i.bQ)(t,n)))))}return v}renderFillSegs(e,t){let{isRtl:n}=this.context,{todayRange:r}=this.props,{framePositions:a}=this.state,o=[];if(a)for(let l of e){let e=n?{right:0,left:a.lefts[l.lastCol]-a.lefts[l.firstCol]}:{left:0,right:a.rights[l.firstCol]-a.rights[l.lastCol]};o.push((0,s.az)("div",{key:(0,i.bR)(l.eventRange),className:"fc-daygrid-bg-harness",style:e},"bg-event"===t?(0,s.az)(i.cm,Object.assign({seg:l},(0,i.bQ)(l,r))):(0,i.cl)(t)))}return(0,s.az)(s.HY,{},...o)}updateSizing(e){let{props:t,state:n,frameElRefs:r}=this;if(!t.forPrint&&null!==t.clientWidth){if(e){let e=t.cells.map(e=>r.currentMap[e.key]);if(e.length){let t=this.rootElRef.current,r=new i.b8(t,e,!0,!1);n.framePositions&&n.framePositions.similarTo(r)||this.setState({framePositions:new i.b8(t,e,!0,!1)})}}let s=this.state.segHeights,a=this.querySegHeights(),o=!0===t.dayMaxEvents||!0===t.dayMaxEventRows;this.safeSetState({segHeights:Object.assign(Object.assign({},s),a),maxContentHeight:o?this.computeMaxContentHeight():null})}}querySegHeights(){let e=this.segHarnessRefs.currentMap,t={};for(let n in e){let r=Math.round(e[n].getBoundingClientRect().height);t[n]=Math.max(t[n]||0,r)}return t}computeMaxContentHeight(){let e=this.props.cells[0].key,t=this.cellElRefs.currentMap[e],n=this.fgElRefs.currentMap[e];return t.getBoundingClientRect().bottom-n.getBoundingClientRect().top}getCellEls(){let e=this.cellElRefs.currentMap;return this.props.cells.map(t=>e[t.key])}}C.addStateEquality({segHeights:i.E});class w extends i.bc{constructor(){super(...arguments),this.splitBusinessHourSegs=(0,i.z)(o),this.splitBgEventSegs=(0,i.z)(R),this.splitFgEventSegs=(0,i.z)(o),this.splitDateSelectionSegs=(0,i.z)(o),this.splitEventDrag=(0,i.z)(c),this.splitEventResize=(0,i.z)(c),this.rowRefs=new i.cd}render(){let{props:e,context:t}=this,n=e.cells.length,r=this.splitBusinessHourSegs(e.businessHourSegs,n),a=this.splitBgEventSegs(e.bgEventSegs,n),o=this.splitFgEventSegs(e.fgEventSegs,n),l=this.splitDateSelectionSegs(e.dateSelectionSegs,n),c=this.splitEventDrag(e.eventDrag,n),d=this.splitEventResize(e.eventResize,n),u=n>=7&&e.clientWidth?e.clientWidth/t.options.aspectRatio/6:null;return(0,s.az)(i.a6,{unit:"day"},(t,i)=>(0,s.az)(s.HY,null,e.cells.map((t,f)=>(0,s.az)(C,{ref:this.rowRefs.createRef(f),key:t.length?t[0].date.toISOString():f,showDayNumbers:n>1,showWeekNumbers:e.showWeekNumbers,todayRange:i,dateProfile:e.dateProfile,cells:t,renderIntro:e.renderRowIntro,businessHourSegs:r[f],eventSelection:e.eventSelection,bgEventSegs:a[f],fgEventSegs:o[f],dateSelectionSegs:l[f],eventDrag:c[f],eventResize:d[f],dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,clientWidth:e.clientWidth,clientHeight:e.clientHeight,cellMinHeight:u,forPrint:e.forPrint}))))}componentDidMount(){this.registerInteractiveComponent()}componentDidUpdate(){this.registerInteractiveComponent()}registerInteractiveComponent(){if(!this.rootEl){let e=this.rowRefs.currentMap[0].getCellEls()[0],t=e?e.closest(".fc-daygrid-body"):null;t&&(this.rootEl=t,this.context.registerInteractiveComponent(this,{el:t,isHitComboAllowed:this.props.isHitComboAllowed}))}}componentWillUnmount(){this.rootEl&&(this.context.unregisterInteractiveComponent(this),this.rootEl=null)}prepareHits(){this.rowPositions=new i.b8(this.rootEl,this.rowRefs.collect().map(e=>e.getCellEls()[0]),!1,!0),this.colPositions=new i.b8(this.rootEl,this.rowRefs.currentMap[0].getCellEls(),!0,!1)}queryHit(e,t){let{colPositions:n,rowPositions:r}=this,i=n.leftToIndex(e),s=r.topToIndex(t);if(null!=s&&null!=i){let e=this.props.cells[s][i];return{dateProfile:this.props.dateProfile,dateSpan:Object.assign({range:this.getCellRange(s,i),allDay:!0},e.extraDateSpan),dayEl:this.getCellEl(s,i),rect:{left:n.lefts[i],right:n.rights[i],top:r.tops[s],bottom:r.bottoms[s]},layer:0}}return null}getCellEl(e,t){return this.rowRefs.currentMap[e].getCellEls()[t]}getCellRange(e,t){let n=this.props.cells[e][t].date,r=(0,i.t)(n,1);return{start:n,end:r}}}function R(e,t){return o(e.filter(S),t)}function S(e){return e.eventRange.def.allDay}class k extends i.bc{constructor(){super(...arguments),this.elRef=(0,s.Vf)(),this.needsScrollReset=!1}render(){let{props:e}=this,{dayMaxEventRows:t,dayMaxEvents:n,expandRows:r}=e,i=!0===n||!0===t;i&&!r&&(i=!1,t=null,n=null);let a=["fc-daygrid-body",i?"fc-daygrid-body-balanced":"fc-daygrid-body-unbalanced",r?"":"fc-daygrid-body-natural"];return(0,s.az)("div",{ref:this.elRef,className:a.join(" "),style:{width:e.clientWidth,minWidth:e.tableMinWidth}},(0,s.az)("table",{role:"presentation",className:"fc-scrollgrid-sync-table",style:{width:e.clientWidth,minWidth:e.tableMinWidth,height:r?e.clientHeight:""}},e.colGroupNode,(0,s.az)("tbody",{role:"presentation"},(0,s.az)(w,{dateProfile:e.dateProfile,cells:e.cells,renderRowIntro:e.renderRowIntro,showWeekNumbers:e.showWeekNumbers,clientWidth:e.clientWidth,clientHeight:e.clientHeight,businessHourSegs:e.businessHourSegs,bgEventSegs:e.bgEventSegs,fgEventSegs:e.fgEventSegs,dateSelectionSegs:e.dateSelectionSegs,eventSelection:e.eventSelection,eventDrag:e.eventDrag,eventResize:e.eventResize,dayMaxEvents:n,dayMaxEventRows:t,forPrint:e.forPrint,isHitComboAllowed:e.isHitComboAllowed}))))}componentDidMount(){this.requestScrollReset()}componentDidUpdate(e){e.dateProfile!==this.props.dateProfile?this.requestScrollReset():this.flushScrollReset()}requestScrollReset(){this.needsScrollReset=!0,this.flushScrollReset()}flushScrollReset(){if(this.needsScrollReset&&this.props.clientWidth){var e,t;let n;let r=(e=this.elRef.current,(t=this.props.dateProfile).currentRangeUnit.match(/year|month/)&&(n=e.querySelector(`[data-date="${(0,i.bv)(t.currentDate)}-01"]`)),n||(n=e.querySelector(`[data-date="${(0,i.bt)(t.currentDate)}"]`)),n);if(r){let e=r.closest(".fc-daygrid-body"),t=e.closest(".fc-scroller"),n=r.getBoundingClientRect().top-e.getBoundingClientRect().top;t.scrollTop=n?n+1:0}this.needsScrollReset=!1}}}class x extends i.bU{constructor(){super(...arguments),this.forceDayIfListItem=!0}sliceRange(e,t){return t.sliceRange(e)}}class T extends i.bc{constructor(){super(...arguments),this.slicer=new x,this.tableRef=(0,s.Vf)()}render(){let{props:e,context:t}=this;return(0,s.az)(k,Object.assign({ref:this.tableRef},this.slicer.sliceProps(e,e.dateProfile,e.nextDayThreshold,t,e.dayTableModel),{dateProfile:e.dateProfile,cells:e.dayTableModel.cells,colGroupNode:e.colGroupNode,tableMinWidth:e.tableMinWidth,renderRowIntro:e.renderRowIntro,dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,showWeekNumbers:e.showWeekNumbers,expandRows:e.expandRows,headerAlignElRef:e.headerAlignElRef,clientWidth:e.clientWidth,clientHeight:e.clientHeight,forPrint:e.forPrint}))}}class M extends a{constructor(){super(...arguments),this.buildDayTableModel=(0,i.z)(O),this.headerRef=(0,s.Vf)(),this.tableRef=(0,s.Vf)()}render(){let{options:e,dateProfileGenerator:t}=this.context,{props:n}=this,r=this.buildDayTableModel(n.dateProfile,t),a=e.dayHeaders&&(0,s.az)(i.bI,{ref:this.headerRef,dateProfile:n.dateProfile,dates:r.headerDates,datesRepDistinctDays:1===r.rowCnt}),o=t=>(0,s.az)(T,{ref:this.tableRef,dateProfile:n.dateProfile,dayTableModel:r,businessHours:n.businessHours,dateSelection:n.dateSelection,eventStore:n.eventStore,eventUiBases:n.eventUiBases,eventSelection:n.eventSelection,eventDrag:n.eventDrag,eventResize:n.eventResize,nextDayThreshold:e.nextDayThreshold,colGroupNode:t.tableColGroupNode,tableMinWidth:t.tableMinWidth,dayMaxEvents:e.dayMaxEvents,dayMaxEventRows:e.dayMaxEventRows,showWeekNumbers:e.weekNumbers,expandRows:!n.isHeightAuto,headerAlignElRef:this.headerElRef,clientWidth:t.clientWidth,clientHeight:t.clientHeight,forPrint:n.forPrint});return e.dayMinWidth?this.renderHScrollLayout(a,o,r.colCnt,e.dayMinWidth):this.renderSimpleLayout(a,o)}}function O(e,t){let n=new i.bM(e.renderRange,t);return new i.bT(n,/year|month|week/.test(e.currentRangeUnit))}class I extends i.R{buildRenderRange(e,t,n){let r=super.buildRenderRange(e,t,n),{props:s}=this;return function(e){let t,{dateEnv:n,currentRange:r}=e,{start:s,end:a}=r;if(e.snapToWeek&&(s=n.startOfWeek(s),(t=n.startOfWeek(a)).valueOf()!==a.valueOf()&&(a=(0,i.bf)(t,1))),e.fixedWeekCount){let e=n.startOfWeek(n.startOfMonth((0,i.t)(r.end,-1))),t=Math.ceil((0,i.bg)(e,a));a=(0,i.bf)(a,6-t)}return{start:s,end:a}}({currentRange:r,snapToWeek:/^(year|month)$/.test(t),fixedWeekCount:s.fixedWeekCount,dateEnv:s.dateEnv})}}(0,i.ct)(':root{--fc-daygrid-event-dot-width:8px}.fc-daygrid-day-events:after,.fc-daygrid-day-events:before,.fc-daygrid-day-frame:after,.fc-daygrid-day-frame:before,.fc-daygrid-event-harness:after,.fc-daygrid-event-harness:before{clear:both;content:"";display:table}.fc .fc-daygrid-body{position:relative;z-index:1}.fc .fc-daygrid-day.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-daygrid-day-frame{min-height:100%;position:relative}.fc .fc-daygrid-day-top{display:flex;flex-direction:row-reverse}.fc .fc-day-other .fc-daygrid-day-top{opacity:.3}.fc .fc-daygrid-day-number{padding:4px;position:relative;z-index:4}.fc .fc-daygrid-month-start{font-size:1.1em;font-weight:700}.fc .fc-daygrid-day-events{margin-top:1px}.fc .fc-daygrid-body-balanced .fc-daygrid-day-events{left:0;position:absolute;right:0}.fc .fc-daygrid-body-unbalanced .fc-daygrid-day-events{min-height:2em;position:relative}.fc .fc-daygrid-body-natural .fc-daygrid-day-events{margin-bottom:1em}.fc .fc-daygrid-event-harness{position:relative}.fc .fc-daygrid-event-harness-abs{left:0;position:absolute;right:0;top:0}.fc .fc-daygrid-bg-harness{bottom:0;position:absolute;top:0}.fc .fc-daygrid-day-bg .fc-non-business{z-index:1}.fc .fc-daygrid-day-bg .fc-bg-event{z-index:2}.fc .fc-daygrid-day-bg .fc-highlight{z-index:3}.fc .fc-daygrid-event{margin-top:1px;z-index:6}.fc .fc-daygrid-event.fc-event-mirror{z-index:7}.fc .fc-daygrid-day-bottom{font-size:.85em;margin:0 2px}.fc .fc-daygrid-day-bottom:after,.fc .fc-daygrid-day-bottom:before{clear:both;content:"";display:table}.fc .fc-daygrid-more-link{border-radius:3px;cursor:pointer;line-height:1;margin-top:1px;max-width:100%;overflow:hidden;padding:2px;position:relative;white-space:nowrap;z-index:4}.fc .fc-daygrid-more-link:hover{background-color:rgba(0,0,0,.1)}.fc .fc-daygrid-week-number{background-color:var(--fc-neutral-bg-color);color:var(--fc-neutral-text-color);min-width:1.5em;padding:2px;position:absolute;text-align:center;top:0;z-index:5}.fc .fc-more-popover .fc-popover-body{min-width:220px;padding:10px}.fc-direction-ltr .fc-daygrid-event.fc-event-start,.fc-direction-rtl .fc-daygrid-event.fc-event-end{margin-left:2px}.fc-direction-ltr .fc-daygrid-event.fc-event-end,.fc-direction-rtl .fc-daygrid-event.fc-event-start{margin-right:2px}.fc-direction-ltr .fc-daygrid-more-link{float:left}.fc-direction-ltr .fc-daygrid-week-number{border-radius:0 0 3px 0;left:0}.fc-direction-rtl .fc-daygrid-more-link{float:right}.fc-direction-rtl .fc-daygrid-week-number{border-radius:0 0 0 3px;right:0}.fc-liquid-hack .fc-daygrid-day-frame{position:static}.fc-daygrid-event{border-radius:3px;font-size:var(--fc-small-font-size);position:relative;white-space:nowrap}.fc-daygrid-block-event .fc-event-time{font-weight:700}.fc-daygrid-block-event .fc-event-time,.fc-daygrid-block-event .fc-event-title{padding:1px}.fc-daygrid-dot-event{align-items:center;display:flex;padding:2px 0}.fc-daygrid-dot-event .fc-event-title{flex-grow:1;flex-shrink:1;font-weight:700;min-width:0;overflow:hidden}.fc-daygrid-dot-event.fc-event-mirror,.fc-daygrid-dot-event:hover{background:rgba(0,0,0,.1)}.fc-daygrid-dot-event.fc-event-selected:before{bottom:-10px;top:-10px}.fc-daygrid-event-dot{border:calc(var(--fc-daygrid-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-daygrid-event-dot-width)/2);box-sizing:content-box;height:0;margin:0 4px;width:0}.fc-direction-ltr .fc-daygrid-event .fc-event-time{margin-right:3px}.fc-direction-rtl .fc-daygrid-event .fc-event-time{margin-left:3px}');var z=(0,r.rx)({name:"@fullcalendar/daygrid",initialView:"dayGridMonth",views:{dayGrid:{component:M,dateProfileGeneratorClass:I},dayGridDay:{type:"dayGrid",duration:{days:1}},dayGridWeek:{type:"dayGrid",duration:{weeks:1}},dayGridMonth:{type:"dayGrid",duration:{months:1},fixedWeekCount:!0},dayGridYear:{type:"dayGrid",duration:{years:1}}}})},97892:(e,t,n)=>{"use strict";n.d(t,{Z:()=>m});var r=n(37543),i=n(75867),s=n(95284);class a extends i.B{constructor(){super(...arguments),this.state={textId:(0,i.a3)()}}render(){let{theme:e,dateEnv:t,options:n,viewApi:r}=this.context,{cellId:a,dayDate:l,todayRange:c}=this.props,{textId:d}=this.state,u=(0,i.aY)(l,c),f=n.listDayFormat?t.format(l,n.listDayFormat):"",h=n.listDaySideFormat?t.format(l,n.listDaySideFormat):"",p=Object.assign({date:t.toDate(l),view:r,textId:d,text:f,sideText:h,navLinkAttrs:(0,i.a_)(this.context,l),sideNavLinkAttrs:(0,i.a_)(this.context,l,"day",!1)},u);return(0,s.az)(i.C,{elTag:"tr",elClasses:["fc-list-day",...(0,i.aX)(u,e)],elAttrs:{"data-date":(0,i.bt)(l)},renderProps:p,generatorName:"dayHeaderContent",customGenerator:n.dayHeaderContent,defaultGenerator:o,classNameGenerator:n.dayHeaderClassNames,didMount:n.dayHeaderDidMount,willUnmount:n.dayHeaderWillUnmount},t=>(0,s.az)("th",{scope:"colgroup",colSpan:3,id:a,"aria-labelledby":d},(0,s.az)(t,{elTag:"div",elClasses:["fc-list-day-cushion",e.getClass("tableCellShaded")]})))}}function o(e){return(0,s.az)(s.HY,null,e.text&&(0,s.az)("a",Object.assign({id:e.textId,className:"fc-list-day-text"},e.navLinkAttrs),e.text),e.sideText&&(0,s.az)("a",Object.assign({"aria-hidden":!0,className:"fc-list-day-side-text"},e.sideNavLinkAttrs),e.sideText))}let l=(0,i.x)({hour:"numeric",minute:"2-digit",meridiem:"short"});class c extends i.B{render(){let{props:e,context:t}=this,{options:n}=t,{seg:r,timeHeaderId:a,eventHeaderId:o,dateHeaderId:c}=e,u=n.eventTimeFormat||l;return(0,s.az)(i.ck,Object.assign({},e,{elTag:"tr",elClasses:["fc-list-event",r.eventRange.def.url&&"fc-event-forced-url"],defaultGenerator:()=>{let e;return e=(0,i.bS)(r,t),(0,s.az)("a",Object.assign({},e),r.eventRange.def.title)},seg:r,timeText:"",disableDragging:!0,disableResizing:!0}),(e,n)=>(0,s.az)(s.HY,null,function(e,t,n,r,a){let{options:o}=n;if(!1!==o.displayEventTime){let l,c=e.eventRange.def,u=e.eventRange.instance,f=!1;if(c.allDay?f=!0:(0,i.ax)(e.eventRange.range)?e.isStart?l=(0,i.bO)(e,t,n,null,null,u.range.start,e.end):e.isEnd?l=(0,i.bO)(e,t,n,null,null,e.start,u.range.end):f=!0:l=(0,i.bO)(e,t,n),f){let e={text:n.options.allDayText,view:n.viewApi};return(0,s.az)(i.C,{elTag:"td",elClasses:["fc-list-event-time"],elAttrs:{headers:`${r} ${a}`},renderProps:e,generatorName:"allDayContent",customGenerator:o.allDayContent,defaultGenerator:d,classNameGenerator:o.allDayClassNames,didMount:o.allDayDidMount,willUnmount:o.allDayWillUnmount})}return(0,s.az)("td",{className:"fc-list-event-time"},l)}return null}(r,u,t,a,c),(0,s.az)("td",{"aria-hidden":!0,className:"fc-list-event-graphic"},(0,s.az)("span",{className:"fc-list-event-dot",style:{borderColor:n.borderColor||n.backgroundColor}})),(0,s.az)(e,{elTag:"td",elClasses:["fc-list-event-title"],elAttrs:{headers:`${o} ${c}`}})))}}function d(e){return e.text}class u extends i.bc{constructor(){super(...arguments),this.computeDateVars=(0,i.z)(h),this.eventStoreToSegs=(0,i.z)(this._eventStoreToSegs),this.state={timeHeaderId:(0,i.a3)(),eventHeaderId:(0,i.a3)(),dateHeaderIdRoot:(0,i.a3)()},this.setRootEl=e=>{e?this.context.registerInteractiveComponent(this,{el:e}):this.context.unregisterInteractiveComponent(this)}}render(){let{props:e,context:t}=this,{dayDates:n,dayRanges:r}=this.computeDateVars(e.dateProfile),a=this.eventStoreToSegs(e.eventStore,e.eventUiBases,r);return(0,s.az)(i.cq,{elRef:this.setRootEl,elClasses:["fc-list",t.theme.getClass("table"),!1!==t.options.stickyHeaderDates?"fc-list-sticky":""],viewSpec:t.viewSpec},(0,s.az)(i.cb,{liquid:!e.isHeightAuto,overflowX:e.isHeightAuto?"visible":"hidden",overflowY:e.isHeightAuto?"visible":"auto"},a.length>0?this.renderSegList(a,n):this.renderEmptyMessage()))}renderEmptyMessage(){let{options:e,viewApi:t}=this.context,n={text:e.noEventsText,view:t};return(0,s.az)(i.C,{elTag:"div",elClasses:["fc-list-empty"],renderProps:n,generatorName:"noEventsContent",customGenerator:e.noEventsContent,defaultGenerator:f,classNameGenerator:e.noEventsClassNames,didMount:e.noEventsDidMount,willUnmount:e.noEventsWillUnmount},e=>(0,s.az)(e,{elTag:"div",elClasses:["fc-list-empty-cushion"]}))}renderSegList(e,t){let{theme:n,options:r}=this.context,{timeHeaderId:o,eventHeaderId:l,dateHeaderIdRoot:d}=this.state,u=function(e){let t,n,r=[];for(t=0;t<e.length;t+=1)(r[(n=e[t]).dayIndex]||(r[n.dayIndex]=[])).push(n);return r}(e);return(0,s.az)(i.a6,{unit:"day"},(e,f)=>{let h=[];for(let n=0;n<u.length;n+=1){let p=u[n];if(p){let u=(0,i.bt)(t[n]),g=d+"-"+u;for(let d of(h.push((0,s.az)(a,{key:u,cellId:g,dayDate:t[n],todayRange:f})),p=(0,i.bP)(p,r.eventOrder)))h.push((0,s.az)(c,Object.assign({key:u+":"+d.eventRange.instance.instanceId,seg:d,isDragging:!1,isResizing:!1,isDateSelecting:!1,isSelected:!1,timeHeaderId:o,eventHeaderId:l,dateHeaderId:g},(0,i.bQ)(d,f,e))))}}return(0,s.az)("table",{className:"fc-list-table "+n.getClass("table")},(0,s.az)("thead",null,(0,s.az)("tr",null,(0,s.az)("th",{scope:"col",id:o},r.timeHint),(0,s.az)("th",{scope:"col","aria-hidden":!0}),(0,s.az)("th",{scope:"col",id:l},r.eventHint))),(0,s.az)("tbody",null,h))})}_eventStoreToSegs(e,t,n){return this.eventRangesToSegs((0,i.ad)(e,t,this.props.dateProfile.activeRange,this.context.options.nextDayThreshold).fg,n)}eventRangesToSegs(e,t){let n=[];for(let r of e)n.push(...this.eventRangeToSegs(r,t));return n}eventRangeToSegs(e,t){let n,r,s,{dateEnv:a}=this.context,{nextDayThreshold:o}=this.context.options,l=e.range,c=e.def.allDay,d=[];for(n=0;n<t.length;n+=1)if((r=(0,i.o)(l,t[n]))&&(d.push(s={component:this,eventRange:e,start:r.start,end:r.end,isStart:e.isStart&&r.start.valueOf()===l.start.valueOf(),isEnd:e.isEnd&&r.end.valueOf()===l.end.valueOf(),dayIndex:n}),!s.isEnd&&!c&&n+1<t.length&&l.end<a.add(t[n+1].start,o))){s.end=l.end,s.isEnd=!0;break}return d}}function f(e){return e.text}function h(e){let t=(0,i.q)(e.renderRange.start),n=e.renderRange.end,r=[],s=[];for(;t<n;)r.push(t),s.push({start:t,end:(0,i.t)(t,1)}),t=(0,i.t)(t,1);return{dayDates:r,dayRanges:s}}(0,i.ct)(':root{--fc-list-event-dot-width:10px;--fc-list-event-hover-bg-color:#f5f5f5}.fc-theme-standard .fc-list{border:1px solid var(--fc-border-color)}.fc .fc-list-empty{align-items:center;background-color:var(--fc-neutral-bg-color);display:flex;height:100%;justify-content:center}.fc .fc-list-empty-cushion{margin:5em 0}.fc .fc-list-table{border-style:hidden;width:100%}.fc .fc-list-table tr>*{border-left:0;border-right:0}.fc .fc-list-sticky .fc-list-day>*{background:var(--fc-page-bg-color);position:sticky;top:0}.fc .fc-list-table thead{left:-10000px;position:absolute}.fc .fc-list-table tbody>tr:first-child th{border-top:0}.fc .fc-list-table th{padding:0}.fc .fc-list-day-cushion,.fc .fc-list-table td{padding:8px 14px}.fc .fc-list-day-cushion:after{clear:both;content:"";display:table}.fc-theme-standard .fc-list-day-cushion{background-color:var(--fc-neutral-bg-color)}.fc-direction-ltr .fc-list-day-text,.fc-direction-rtl .fc-list-day-side-text{float:left}.fc-direction-ltr .fc-list-day-side-text,.fc-direction-rtl .fc-list-day-text{float:right}.fc-direction-ltr .fc-list-table .fc-list-event-graphic{padding-right:0}.fc-direction-rtl .fc-list-table .fc-list-event-graphic{padding-left:0}.fc .fc-list-event.fc-event-forced-url{cursor:pointer}.fc .fc-list-event:hover td{background-color:var(--fc-list-event-hover-bg-color)}.fc .fc-list-event-graphic,.fc .fc-list-event-time{white-space:nowrap;width:1px}.fc .fc-list-event-dot{border:calc(var(--fc-list-event-dot-width)/2) solid var(--fc-event-border-color);border-radius:calc(var(--fc-list-event-dot-width)/2);box-sizing:content-box;display:inline-block;height:0;width:0}.fc .fc-list-event-title a{color:inherit;text-decoration:none}.fc .fc-list-event.fc-event-forced-url:hover a{text-decoration:underline}');let p={listDayFormat:g,listDaySideFormat:g,noEventsClassNames:i.n,noEventsContent:i.n,noEventsDidMount:i.n,noEventsWillUnmount:i.n};function g(e){return!1===e?null:(0,i.x)(e)}var m=(0,r.rx)({name:"@fullcalendar/list",optionRefiners:p,views:{list:{component:u,buttonTextKey:"list",listDayFormat:{month:"long",day:"numeric",year:"numeric"}},listDay:{type:"list",duration:{days:1},listDayFormat:{weekday:"long"}},listWeek:{type:"list",duration:{weeks:1},listDayFormat:{weekday:"long"},listDaySideFormat:{month:"long",day:"numeric",year:"numeric"}},listMonth:{type:"list",duration:{month:1},listDaySideFormat:{weekday:"long"}},listYear:{type:"list",duration:{year:1},listDaySideFormat:{weekday:"long"}}}})},48967:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(60343),i=n(61222),s=n(37543),a=n(75867);let o=18>parseInt(String(r.version).split(".")[0]);class l extends r.Component{constructor(){super(...arguments),this.elRef=(0,r.createRef)(),this.isUpdating=!1,this.isUnmounting=!1,this.state={customRenderingMap:new Map},this.requestResize=()=>{this.isUnmounting||(this.cancelResize(),this.resizeId=requestAnimationFrame(()=>{this.doResize()}))}}render(){let e=[];for(let t of this.state.customRenderingMap.values())e.push(r.createElement(c,{key:t.id,customRendering:t}));return r.createElement("div",{ref:this.elRef},e)}componentDidMount(){let e;this.isUnmounting=!1;let t=new a.cv;this.handleCustomRendering=t.handle.bind(t),this.calendar=new s.f(this.elRef.current,Object.assign(Object.assign({},this.props),{handleCustomRendering:this.handleCustomRendering})),this.calendar.render(),this.calendar.on("_beforeprint",()=>{(0,i.flushSync)(()=>{})}),t.subscribe(t=>{let n=Date.now(),r=!e;(o||r||this.isUpdating||this.isUnmounting||n-e<100?d:i.flushSync)(()=>{this.setState({customRenderingMap:t},()=>{e=n,r?this.doResize():this.requestResize()})})})}componentDidUpdate(){this.isUpdating=!0,this.calendar.resetOptions(Object.assign(Object.assign({},this.props),{handleCustomRendering:this.handleCustomRendering})),this.isUpdating=!1}componentWillUnmount(){this.isUnmounting=!0,this.cancelResize(),this.calendar.destroy()}doResize(){this.calendar.updateSize()}cancelResize(){void 0!==this.resizeId&&(cancelAnimationFrame(this.resizeId),this.resizeId=void 0)}getApi(){return this.calendar}}l.act=d;class c extends r.PureComponent{render(){let{customRendering:e}=this.props,{generatorMeta:t}=e,n="function"==typeof t?t(e.renderProps):t;return(0,i.createPortal)(n,e.containerEl)}}function d(e){e()}},46168:(e,t,n)=>{"use strict";n.d(t,{jz:()=>T});var r,i,s,a=n(95284),o=0,l=[],c=[],d=a.YM.__b,u=a.YM.__r,f=a.YM.diffed,h=a.YM.__c,p=a.YM.unmount;function g(){for(var e;e=l.shift();)if(e.__P&&e.__H)try{e.__H.__h.forEach(v),e.__H.__h.forEach(b),e.__H.__h=[]}catch(t){e.__H.__h=[],a.YM.__e(t,e.__v)}}a.YM.__b=function(e){r=null,d&&d(e)},a.YM.__r=function(e){u&&u(e);var t=(r=e.__c).__H;t&&(i===r?(t.__h=[],r.__h=[],t.__.forEach(function(e){e.__N&&(e.__=e.__N),e.__V=c,e.__N=e.i=void 0})):(t.__h.forEach(v),t.__h.forEach(b),t.__h=[])),i=r},a.YM.diffed=function(e){f&&f(e);var t=e.__c;t&&t.__H&&(t.__H.__h.length&&(1!==l.push(t)&&s===a.YM.requestAnimationFrame||((s=a.YM.requestAnimationFrame)||function(e){var t,n=function(){clearTimeout(r),m&&cancelAnimationFrame(t),setTimeout(e)},r=setTimeout(n,100);m&&(t=requestAnimationFrame(n))})(g)),t.__H.__.forEach(function(e){e.i&&(e.__H=e.i),e.__V!==c&&(e.__=e.__V),e.i=void 0,e.__V=c})),i=r=null},a.YM.__c=function(e,t){t.some(function(e){try{e.__h.forEach(v),e.__h=e.__h.filter(function(e){return!e.__||b(e)})}catch(n){t.some(function(e){e.__h&&(e.__h=[])}),t=[],a.YM.__e(n,e.__v)}}),h&&h(e,t)},a.YM.unmount=function(e){p&&p(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach(function(e){try{v(e)}catch(e){t=e}}),n.__H=void 0,t&&a.YM.__e(t,n.__v))};var m="function"==typeof requestAnimationFrame;function v(e){var t=r,n=e.__c;"function"==typeof n&&(e.__c=void 0,n()),r=t}function b(e){var t=r;e.__c=e.__(),r=t}function y(e,t){for(var n in e)if("__source"!==n&&!(n in t))return!0;for(var r in t)if("__source"!==r&&e[r]!==t[r])return!0;return!1}function A(e){this.props=e}(A.prototype=new a.wA).isPureReactComponent=!0,A.prototype.shouldComponentUpdate=function(e,t){return y(this.props,e)||y(this.state,t)};var _=a.YM.__b;a.YM.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),_&&_(e)},"undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref");var E=(a.bR,a.YM.__e);a.YM.__e=function(e,t,n,r){if(e.then){for(var i,s=t;s=s.__;)if((i=s.__c)&&i.__c)return null==t.__e&&(t.__e=n.__e,t.__k=n.__k),i.__c(e,t)}E(e,t,n,r)};var D=a.YM.unmount;function C(){this.__u=0,this.t=null,this.__b=null}function w(e){var t=e.__.__c;return t&&t.__a&&t.__a(e)}function R(){this.u=null,this.o=null}a.YM.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&!0===e.__h&&(e.type=null),D&&D(e)},(C.prototype=new a.wA).__c=function(e,t){var n=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(n);var i=w(r.__v),s=!1,a=function(){s||(s=!0,n.__R=null,i?i(o):o())};n.__R=a;var o=function(){if(!--r.__u){if(r.state.__a){var e,t=r.state.__a;r.__v.__k[0]=function e(t,n,r){return t&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(t){return e(t,n,r)}),t.__c&&t.__c.__P===n&&(t.__e&&r.insertBefore(t.__e,t.__d),t.__c.__e=!0,t.__c.__P=r)),t}(t,t.__c.__P,t.__c.__O)}for(r.setState({__a:r.__b=null});e=r.t.pop();)e.forceUpdate()}},l=!0===t.__h;r.__u++||l||r.setState({__a:r.__b=r.__v.__k[0]}),e.then(a,a)},C.prototype.componentWillUnmount=function(){this.t=[]},C.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=function e(t,n,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(e){"function"==typeof e.__c&&e.__c()}),t.__c.__H=null),null!=(t=function(e,t){for(var n in t)e[n]=t[n];return e}({},t)).__c&&(t.__c.__P===r&&(t.__c.__P=n),t.__c=null),t.__k=t.__k&&t.__k.map(function(t){return e(t,n,r)})),t}(this.__b,n,r.__O=r.__P)}this.__b=null}var i=t.__a&&(0,a.az)(a.HY,null,e.fallback);return i&&(i.__h=null),[(0,a.az)(a.HY,null,t.__a?null:e.children),i]};var S=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&("t"!==e.props.revealOrder[0]||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};function k(e){return this.getChildContext=function(){return e.context},e.children}function x(e){var t=this,n=e.i;t.componentWillUnmount=function(){(0,a.sY)(null,t.l),t.l=null,t.i=null},t.i&&t.i!==n&&t.componentWillUnmount(),e.__v?(t.l||(t.i=n,t.l={nodeType:1,parentNode:n,childNodes:[],appendChild:function(e){this.childNodes.push(e),t.i.appendChild(e)},insertBefore:function(e,n){this.childNodes.push(e),t.i.appendChild(e)},removeChild:function(e){this.childNodes.splice(this.childNodes.indexOf(e)>>>1,1),t.i.removeChild(e)}}),(0,a.sY)((0,a.az)(k,{context:t.context},e.__v),t.l)):t.l&&t.componentWillUnmount()}function T(e,t){var n=(0,a.az)(x,{__v:e,i:t});return n.containerInfo=t,n}(R.prototype=new a.wA).__a=function(e){var t=this,n=w(t.__v),r=t.o.get(e);return r[0]++,function(i){var s=function(){t.props.revealOrder?(r.push(i),S(t,e,r)):i()};n?n(s):s()}},R.prototype.render=function(e){this.u=null,this.o=new Map;var t=(0,a.bR)(e.children);e.revealOrder&&"b"===e.revealOrder[0]&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},R.prototype.componentDidUpdate=R.prototype.componentDidMount=function(){var e=this;this.o.forEach(function(t,n){S(e,n,t)})};var M="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,O=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,I="undefined"!=typeof document;a.wA.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(e){Object.defineProperty(a.wA.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})});var z=a.YM.event;function N(){}function H(){return this.cancelBubble}function P(){return this.defaultPrevented}a.YM.event=function(e){return z&&(e=z(e)),e.persist=N,e.isPropagationStopped=H,e.isDefaultPrevented=P,e.nativeEvent=e};var B={configurable:!0,get:function(){return this.class}},j=a.YM.vnode;a.YM.vnode=function(e){var t=e.type,n=e.props,r=n;if("string"==typeof t){var i=-1===t.indexOf("-");for(var s in r={},n){var o,l=n[s];I&&"children"===s&&"noscript"===t||"value"===s&&"defaultValue"in n&&null==l||("defaultValue"===s&&"value"in n&&null==n.value?s="value":"download"===s&&!0===l?l="":/ondoubleclick/i.test(s)?s="ondblclick":/^onchange(textarea|input)/i.test(s+t)&&(o=n.type,!("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/i:/fil|che|ra/i).test(o))?s="oninput":/^onfocus$/i.test(s)?s="onfocusin":/^onblur$/i.test(s)?s="onfocusout":/^on(Ani|Tra|Tou|BeforeInp|Compo)/.test(s)?s=s.toLowerCase():i&&O.test(s)?s=s.replace(/[A-Z0-9]/g,"-$&").toLowerCase():null===l&&(l=void 0),/^oninput$/i.test(s)&&r[s=s.toLowerCase()]&&(s="oninputCapture"),r[s]=l)}"select"==t&&r.multiple&&Array.isArray(r.value)&&(r.value=(0,a.bR)(n.children).forEach(function(e){e.props.selected=-1!=r.value.indexOf(e.props.value)})),"select"==t&&null!=r.defaultValue&&(r.value=(0,a.bR)(n.children).forEach(function(e){e.props.selected=r.multiple?-1!=r.defaultValue.indexOf(e.props.value):r.defaultValue==e.props.value})),e.props=r,n.class!=n.className&&(B.enumerable="className"in n,null!=n.className&&(r.class=n.className),Object.defineProperty(r,"className",B))}e.$$typeof=M,j&&j(e)};var U=a.YM.__r;a.YM.__r=function(e){U&&U(e),e.__c},a.HY,a.az,a.kr,a.Vf,a.HY,a.wA},95284:(e,t,n)=>{"use strict";n.d(t,{HY:()=>A,Tm:()=>P,Vf:()=>y,YM:()=>i,ZB:()=>H,az:()=>v,bR:()=>function e(t,n){return n=n||[],null==t||"boolean"==typeof t||(Array.isArray(t)?t.some(function(t){e(t,n)}):n.push(t)),n},kr:()=>B,l$:()=>a,sY:()=>N,wA:()=>w});var r,i,s,a,o,l,c,d,u,f={},h=[],p=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function g(e,t){for(var n in t)e[n]=t[n];return e}function m(e){var t=e.parentNode;t&&t.removeChild(e)}function v(e,t,n){var i,s,a,o={};for(a in t)"key"==a?i=t[a]:"ref"==a?s=t[a]:o[a]=t[a];if(arguments.length>2&&(o.children=arguments.length>3?r.call(arguments,2):n),"function"==typeof e&&null!=e.defaultProps)for(a in e.defaultProps)void 0===o[a]&&(o[a]=e.defaultProps[a]);return b(e,o,i,s,null)}function b(e,t,n,r,a){var o={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:null==a?++s:a};return null==a&&null!=i.vnode&&i.vnode(o),o}function y(){return{current:null}}function A(e){return e.children}function _(e,t,n){"-"===t[0]?e.setProperty(t,null==n?"":n):e[t]=null==n?"":"number"!=typeof n||p.test(t)?n:n+"px"}function E(e,t,n,r,i){var s;e:if("style"===t){if("string"==typeof n)e.style.cssText=n;else{if("string"==typeof r&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||_(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||_(e.style,t,n[t])}}else if("o"===t[0]&&"n"===t[1])s=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase() in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+s]=n,n?r||e.addEventListener(t,s?C:D,s):e.removeEventListener(t,s?C:D,s);else if("dangerouslySetInnerHTML"!==t){if(i)t=t.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!==t&&"height"!==t&&"href"!==t&&"list"!==t&&"form"!==t&&"tabIndex"!==t&&"download"!==t&&t in e)try{e[t]=null==n?"":n;break e}catch(e){}"function"==typeof n||(null==n||!1===n&&-1==t.indexOf("-")?e.removeAttribute(t):e.setAttribute(t,n))}}function D(e){o=!0;try{return this.l[e.type+!1](i.event?i.event(e):e)}finally{o=!1}}function C(e){o=!0;try{return this.l[e.type+!0](i.event?i.event(e):e)}finally{o=!1}}function w(e,t){this.props=e,this.context=t}function R(e,t){if(null==t)return e.__?R(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if(null!=(n=e.__k[t])&&null!=n.__e)return n.__e;return"function"==typeof e.type?R(e):null}function S(e){(!e.__d&&(e.__d=!0)&&l.push(e)&&!k.__r++||c!==i.debounceRendering)&&((c=i.debounceRendering)||function(e){o?setTimeout(e):d(e)})(k)}function k(){var e,t,n,r,i,s,a,o;for(l.sort(function(e,t){return e.__v.__b-t.__v.__b});e=l.shift();)e.__d&&(t=l.length,r=void 0,i=void 0,a=(s=(n=e).__v).__e,(o=n.__P)&&(r=[],(i=g({},s)).__v=s.__v+1,M(o,s,i,n.__n,void 0!==o.ownerSVGElement,null!=s.__h?[a]:null,r,null==a?R(s):a,s.__h),O(r,s),s.__e!=a&&function e(t){var n,r;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,n=0;n<t.__k.length;n++)if(null!=(r=t.__k[n])&&null!=r.__e){t.__e=t.__c.base=r.__e;break}return e(t)}}(s)),l.length>t&&l.sort(function(e,t){return e.__v.__b-t.__v.__b}));k.__r=0}function x(e,t,n,r,s,a,o,l,c,d){var u,p,g,v,y,_,E,D=r&&r.__k||h,C=D.length;for(n.__k=[],u=0;u<t.length;u++)if(null!=(v=n.__k[u]=null==(v=t[u])||"boolean"==typeof v?null:"string"==typeof v||"number"==typeof v||"bigint"==typeof v?b(null,v,null,null,v):Array.isArray(v)?b(A,{children:v},null,null,null):v.__b>0?b(v.type,v.props,v.key,v.ref?v.ref:null,v.__v):v)){if(v.__=n,v.__b=n.__b+1,null===(g=D[u])||g&&v.key==g.key&&v.type===g.type)D[u]=void 0;else for(p=0;p<C;p++){if((g=D[p])&&v.key==g.key&&v.type===g.type){D[p]=void 0;break}g=null}M(e,v,g=g||f,s,a,o,l,c,d),y=v.__e,(p=v.ref)&&g.ref!=p&&(E||(E=[]),g.ref&&E.push(g.ref,null,v),E.push(p,v.__c||y,v)),null!=y?(null==_&&(_=y),"function"==typeof v.type&&v.__k===g.__k?v.__d=c=function e(t,n,r){for(var i,s=t.__k,a=0;s&&a<s.length;a++)(i=s[a])&&(i.__=t,n="function"==typeof i.type?e(i,n,r):T(r,i,i,s,i.__e,n));return n}(v,c,e):c=T(e,v,g,D,y,c),"function"==typeof n.type&&(n.__d=c)):c&&g.__e==c&&c.parentNode!=e&&(c=R(g))}for(n.__e=_,u=C;u--;)null!=D[u]&&("function"==typeof n.type&&null!=D[u].__e&&D[u].__e==n.__d&&(n.__d=function e(t){var n,r,i;if(null==t.type||"string"==typeof t.type)return t.__e;if(t.__k){for(n=t.__k.length-1;n>=0;n--)if((r=t.__k[n])&&(i=e(r)))return i}return null}(r).nextSibling),function e(t,n,r){var s,a;if(i.unmount&&i.unmount(t),(s=t.ref)&&(s.current&&s.current!==t.__e||I(s,null,n)),null!=(s=t.__c)){if(s.componentWillUnmount)try{s.componentWillUnmount()}catch(e){i.__e(e,n)}s.base=s.__P=null,t.__c=void 0}if(s=t.__k)for(a=0;a<s.length;a++)s[a]&&e(s[a],n,r||"function"!=typeof t.type);r||null==t.__e||m(t.__e),t.__=t.__e=t.__d=void 0}(D[u],D[u]));if(E)for(u=0;u<E.length;u++)I(E[u],E[++u],E[++u])}function T(e,t,n,r,i,s){var a,o,l;if(void 0!==t.__d)a=t.__d,t.__d=void 0;else if(null==n||i!=s||null==i.parentNode)e:if(null==s||s.parentNode!==e)e.appendChild(i),a=null;else{for(o=s,l=0;(o=o.nextSibling)&&l<r.length;l+=1)if(o==i)break e;e.insertBefore(i,s),a=s}return void 0!==a?a:i.nextSibling}function M(e,t,n,s,a,o,l,c,d){var u,h,p,v,b,y,_,D,C,S,k,T,M,O,I,N=t.type;if(void 0!==t.constructor)return null;null!=n.__h&&(d=n.__h,c=t.__e=n.__e,t.__h=null,o=[c]),(u=i.__b)&&u(t);try{e:if("function"==typeof N){if(D=t.props,C=(u=N.contextType)&&s[u.__c],S=u?C?C.props.value:u.__:s,n.__c?_=(h=t.__c=n.__c).__=h.__E:("prototype"in N&&N.prototype.render?t.__c=h=new N(D,S):(t.__c=h=new w(D,S),h.constructor=N,h.render=z),C&&C.sub(h),h.props=D,h.state||(h.state={}),h.context=S,h.__n=s,p=h.__d=!0,h.__h=[],h._sb=[]),null==h.__s&&(h.__s=h.state),null!=N.getDerivedStateFromProps&&(h.__s==h.state&&(h.__s=g({},h.__s)),g(h.__s,N.getDerivedStateFromProps(D,h.__s))),v=h.props,b=h.state,h.__v=t,p)null==N.getDerivedStateFromProps&&null!=h.componentWillMount&&h.componentWillMount(),null!=h.componentDidMount&&h.__h.push(h.componentDidMount);else{if(null==N.getDerivedStateFromProps&&D!==v&&null!=h.componentWillReceiveProps&&h.componentWillReceiveProps(D,S),!h.__e&&null!=h.shouldComponentUpdate&&!1===h.shouldComponentUpdate(D,h.__s,S)||t.__v===n.__v){for(t.__v!==n.__v&&(h.props=D,h.state=h.__s,h.__d=!1),t.__e=n.__e,t.__k=n.__k,t.__k.forEach(function(e){e&&(e.__=t)}),k=0;k<h._sb.length;k++)h.__h.push(h._sb[k]);h._sb=[],h.__h.length&&l.push(h);break e}null!=h.componentWillUpdate&&h.componentWillUpdate(D,h.__s,S),null!=h.componentDidUpdate&&h.__h.push(function(){h.componentDidUpdate(v,b,y)})}if(h.context=S,h.props=D,h.__P=e,T=i.__r,M=0,"prototype"in N&&N.prototype.render){for(h.state=h.__s,h.__d=!1,T&&T(t),u=h.render(h.props,h.state,h.context),O=0;O<h._sb.length;O++)h.__h.push(h._sb[O]);h._sb=[]}else do h.__d=!1,T&&T(t),u=h.render(h.props,h.state,h.context),h.state=h.__s;while(h.__d&&++M<25);h.state=h.__s,null!=h.getChildContext&&(s=g(g({},s),h.getChildContext())),p||null==h.getSnapshotBeforeUpdate||(y=h.getSnapshotBeforeUpdate(v,b)),I=null!=u&&u.type===A&&null==u.key?u.props.children:u,x(e,Array.isArray(I)?I:[I],t,n,s,a,o,l,c,d),h.base=t.__e,t.__h=null,h.__h.length&&l.push(h),_&&(h.__E=h.__=null),h.__e=!1}else null==o&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=function(e,t,n,i,s,a,o,l){var c,d,u,h=n.props,p=t.props,g=t.type,v=0;if("svg"===g&&(s=!0),null!=a){for(;v<a.length;v++)if((c=a[v])&&"setAttribute"in c==!!g&&(g?c.localName===g:3===c.nodeType)){e=c,a[v]=null;break}}if(null==e){if(null===g)return document.createTextNode(p);e=s?document.createElementNS("http://www.w3.org/2000/svg",g):document.createElement(g,p.is&&p),a=null,l=!1}if(null===g)h===p||l&&e.data===p||(e.data=p);else{if(a=a&&r.call(e.childNodes),d=(h=n.props||f).dangerouslySetInnerHTML,u=p.dangerouslySetInnerHTML,!l){if(null!=a)for(h={},v=0;v<e.attributes.length;v++)h[e.attributes[v].name]=e.attributes[v].value;(u||d)&&(u&&(d&&u.__html==d.__html||u.__html===e.innerHTML)||(e.innerHTML=u&&u.__html||""))}if(function(e,t,n,r,i){var s;for(s in n)"children"===s||"key"===s||s in t||E(e,s,null,n[s],r);for(s in t)i&&"function"!=typeof t[s]||"children"===s||"key"===s||"value"===s||"checked"===s||n[s]===t[s]||E(e,s,t[s],n[s],r)}(e,p,h,s,l),u)t.__k=[];else if(x(e,Array.isArray(v=t.props.children)?v:[v],t,n,i,s&&"foreignObject"!==g,a,o,a?a[0]:n.__k&&R(n,0),l),null!=a)for(v=a.length;v--;)null!=a[v]&&m(a[v]);l||("value"in p&&void 0!==(v=p.value)&&(v!==e.value||"progress"===g&&!v||"option"===g&&v!==h.value)&&E(e,"value",v,h.value,!1),"checked"in p&&void 0!==(v=p.checked)&&v!==e.checked&&E(e,"checked",v,h.checked,!1))}return e}(n.__e,t,n,s,a,o,l,d);(u=i.diffed)&&u(t)}catch(e){t.__v=null,(d||null!=o)&&(t.__e=c,t.__h=!!d,o[o.indexOf(c)]=null),i.__e(e,t,n)}}function O(e,t){i.__c&&i.__c(t,e),e.some(function(t){try{e=t.__h,t.__h=[],e.some(function(e){e.call(t)})}catch(e){i.__e(e,t.__v)}})}function I(e,t,n){try{"function"==typeof e?e(t):e.current=t}catch(e){i.__e(e,n)}}function z(e,t,n){return this.constructor(e,n)}function N(e,t,n){var s,a,o;i.__&&i.__(e,t),a=(s="function"==typeof n)?null:n&&n.__k||t.__k,o=[],M(t,e=(!s&&n||t).__k=v(A,null,[e]),a||f,f,void 0!==t.ownerSVGElement,!s&&n?[n]:a?null:t.firstChild?r.call(t.childNodes):null,o,!s&&n?n:a?a.__e:t.firstChild,s),O(o,e)}function H(e,t){N(e,t,H)}function P(e,t,n){var i,s,a,o=g({},e.props);for(a in t)"key"==a?i=t[a]:"ref"==a?s=t[a]:o[a]=t[a];return arguments.length>2&&(o.children=arguments.length>3?r.call(arguments,2):n),b(e.type,o,i||e.key,s||e.ref,null)}function B(e,t){var n={__c:t="__cC"+u++,__:e,Consumer:function(e,t){return e.children(t)},Provider:function(e){var n,r;return this.getChildContext||(n=[],(r={})[t]=this,this.getChildContext=function(){return r},this.shouldComponentUpdate=function(e){this.props.value!==e.value&&n.some(function(e){e.__e=!0,S(e)})},this.sub=function(e){n.push(e);var t=e.componentWillUnmount;e.componentWillUnmount=function(){n.splice(n.indexOf(e),1),t&&t.call(e)}}),e.children}};return n.Provider.__=n.Consumer.contextType=n}r=h.slice,i={__e:function(e,t,n,r){for(var i,s,a;t=t.__;)if((i=t.__c)&&!i.__)try{if((s=i.constructor)&&null!=s.getDerivedStateFromError&&(i.setState(s.getDerivedStateFromError(e)),a=i.__d),null!=i.componentDidCatch&&(i.componentDidCatch(e,r||{}),a=i.__d),a)return i.__E=i}catch(t){e=t}throw e}},s=0,a=function(e){return null!=e&&void 0===e.constructor},o=!1,w.prototype.setState=function(e,t){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=g({},this.state),"function"==typeof e&&(e=e(g({},n),this.props)),e&&g(n,e),null!=e&&this.__v&&(t&&this._sb.push(t),S(this))},w.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),S(this))},w.prototype.render=A,l=[],d="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,k.__r=0,u=0}};