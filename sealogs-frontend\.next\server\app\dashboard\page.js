(()=>{var e={};e.id=7702,e.ids=[7702,7944],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},70578:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(56814),r(61086),r(78398),r(57757),r(48045);var n=r(40060),a=r(33581),i=r(57567),s=r.n(i),o=r(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,56814)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\dashboard\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,61086)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\dashboard\\page.tsx"],u="/dashboard/page",h={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},96126:(e,t,r)=>{Promise.resolve().then(r.bind(r,10091))},15015:(e,t,r)=>{Promise.resolve().then(r.bind(r,22392))},34324:(e,t,r)=>{"use strict";var n=r(54262);function a(){}function i(){}i.resetWarningCache=a,e.exports=function(){function e(e,t,r,a,i,s){if(s!==n){var o=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:a};return r.PropTypes=r,r}},77454:(e,t,r)=>{e.exports=r(34324)()},54262:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},10091:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(98768);r(60343);var a=r(64837);function i({children:e}){return n.jsx(a.Z,{children:e})}},22392:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>tK});var n=r(98768),a=r(60343),i=r.n(a);let s={i8:"3.4.0"};r(26012),r(87729),r(60385),r(30314),r(51445);var o=r(66263),l=r(79418),c=r(51742),d=r(99891),u=r(9210),h=r(29019),p=r(9626),f=r(81543),m=r(86830),v=r.n(m),y=r(28288),g=r.n(y),x=r(38656),b=r.n(x),j=r(28411),k=r(89620),w=r(35590),S=r(99829),O=r(70953),C=r(31222),N=r(43229),A=r(80131),D=r(12307),P=r(56361),T=["type","layout","connectNulls","ref"],M=["key"];function E(e){return(E="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function R(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function I(){return(I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function Z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(r),!0).forEach(function(t){B(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function _(e){return function(e){if(Array.isArray(e))return L(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return L(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return L(e,void 0)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function $(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,z(n.key),n)}}function V(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(V=function(){return!!e})()}function W(e){return(W=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function q(e,t){return(q=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function B(e,t,r){return(t=z(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function z(e){var t=function(e,t){if("object"!=E(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=E(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==E(t)?t:t+""}var H=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,a=arguments.length,i=Array(a),s=0;s<a;s++)i[s]=arguments[s];return t=n,r=[].concat(i),t=W(t),B(e=function(e,t){if(t&&("object"===E(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,V()?Reflect.construct(t,r||[],W(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),B(e,"generateSimpleStrokeDasharray",function(e,t){return"".concat(t,"px ").concat(e-t,"px")}),B(e,"getStrokeDasharray",function(t,r,a){var i=a.reduce(function(e,t){return e+t});if(!i)return e.generateSimpleStrokeDasharray(r,t);for(var s=t%i,o=r-t,l=[],c=0,d=0;c<a.length;d+=a[c],++c)if(d+a[c]>s){l=[].concat(_(a.slice(0,c)),[s-d]);break}var u=l.length%2==0?[0,o]:[o];return[].concat(_(n.repeat(a,Math.floor(t/i))),_(l),u).map(function(e){return"".concat(e,"px")}).join(", ")}),B(e,"id",(0,N.EL)("recharts-line-")),B(e,"pathRef",function(t){e.mainCurve=t}),B(e,"handleAnimationEnd",function(){e.setState({isAnimationFinished:!0}),e.props.onAnimationEnd&&e.props.onAnimationEnd()}),B(e,"handleAnimationStart",function(){e.setState({isAnimationFinished:!1}),e.props.onAnimationStart&&e.props.onAnimationStart()}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&q(e,t)}(n,e),t=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();this.setState({totalLength:e})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();e!==this.state.totalLength&&this.setState({totalLength:e})}}},{key:"getTotalLength",value:function(){var e=this.mainCurve;try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,a=r.xAxis,s=r.yAxis,o=r.layout,l=r.children,c=(0,A.NN)(l,C.W);if(!c)return null;var d=function(e,t){return{x:e.x,y:e.y,value:e.value,errorVal:(0,P.F$)(e.payload,t)}};return i().createElement(S.m,{clipPath:e?"url(#clipPath-".concat(t,")"):null},c.map(function(e){return i().cloneElement(e,{key:"bar-".concat(e.props.dataKey),data:n,xAxis:a,yAxis:s,layout:o,dataPointFormatter:d})}))}},{key:"renderDots",value:function(e,t,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,s=a.dot,o=a.points,l=a.dataKey,c=(0,A.L6)(this.props,!1),d=(0,A.L6)(s,!0),u=o.map(function(e,t){var r=F(F(F({key:"dot-".concat(t),r:3},c),d),{},{index:t,cx:e.x,cy:e.y,value:e.value,dataKey:l,payload:e.payload,points:o});return n.renderDotItem(s,r)}),h={clipPath:e?"url(#clipPath-".concat(t?"":"dots-").concat(r,")"):null};return i().createElement(S.m,I({className:"recharts-line-dots",key:"dots"},h),u)}},{key:"renderCurveStatically",value:function(e,t,r,n){var a=this.props,s=a.type,o=a.layout,l=a.connectNulls,c=(a.ref,R(a,T)),d=F(F(F({},(0,A.L6)(c,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:t?"url(#clipPath-".concat(r,")"):null,points:e},n),{},{type:s,layout:o,connectNulls:l});return i().createElement(k.H,I({},d,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(e,t){var r=this,n=this.props,a=n.points,s=n.strokeDasharray,o=n.isAnimationActive,l=n.animationBegin,c=n.animationDuration,d=n.animationEasing,u=n.animationId,h=n.animateNewValues,p=n.width,m=n.height,v=this.state,y=v.prevPoints,g=v.totalLength;return i().createElement(f.ZP,{begin:l,duration:c,isActive:o,easing:d,from:{t:0},to:{t:1},key:"line-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var i,o=n.t;if(y){var l=y.length/a.length,c=a.map(function(e,t){var r=Math.floor(t*l);if(y[r]){var n=y[r],a=(0,N.k4)(n.x,e.x),i=(0,N.k4)(n.y,e.y);return F(F({},e),{},{x:a(o),y:i(o)})}if(h){var s=(0,N.k4)(2*p,e.x),c=(0,N.k4)(m/2,e.y);return F(F({},e),{},{x:s(o),y:c(o)})}return F(F({},e),{},{x:e.x,y:e.y})});return r.renderCurveStatically(c,e,t)}var d=(0,N.k4)(0,g)(o);if(s){var u="".concat(s).split(/[,\s]+/gim).map(function(e){return parseFloat(e)});i=r.getStrokeDasharray(d,g,u)}else i=r.generateSimpleStrokeDasharray(g,d);return r.renderCurveStatically(a,e,t,{strokeDasharray:i})})}},{key:"renderCurve",value:function(e,t){var r=this.props,n=r.points,a=r.isAnimationActive,i=this.state,s=i.prevPoints,o=i.totalLength;return a&&n&&n.length&&(!s&&o>0||!b()(s,n))?this.renderCurveWithAnimation(e,t):this.renderCurveStatically(n,e,t)}},{key:"render",value:function(){var e,t=this.props,r=t.hide,n=t.dot,a=t.points,s=t.className,o=t.xAxis,l=t.yAxis,c=t.top,d=t.left,u=t.width,h=t.height,p=t.isAnimationActive,f=t.id;if(r||!a||!a.length)return null;var m=this.state.isAnimationFinished,v=1===a.length,y=(0,j.Z)("recharts-line",s),x=o&&o.allowDataOverflow,b=l&&l.allowDataOverflow,k=x||b,w=g()(f)?this.id:f,C=null!==(e=(0,A.L6)(n,!1))&&void 0!==e?e:{r:3,strokeWidth:2},N=C.r,D=C.strokeWidth,P=((0,A.jf)(n)?n:{}).clipDot,T=void 0===P||P,M=2*(void 0===N?3:N)+(void 0===D?2:D);return i().createElement(S.m,{className:y},x||b?i().createElement("defs",null,i().createElement("clipPath",{id:"clipPath-".concat(w)},i().createElement("rect",{x:x?d:d-u/2,y:b?c:c-h/2,width:x?u:2*u,height:b?h:2*h})),!T&&i().createElement("clipPath",{id:"clipPath-dots-".concat(w)},i().createElement("rect",{x:d-M/2,y:c-M/2,width:u+M,height:h+M}))):null,!v&&this.renderCurve(k,w),this.renderErrorBar(k,w),(v||n)&&this.renderDots(k,T,w),(!p||m)&&O.e.renderCallByParent(this.props,a))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}},{key:"repeat",value:function(e,t){for(var r=e.length%2!=0?[].concat(_(e),[0]):e,n=[],a=0;a<t;++a)n=[].concat(_(n),_(r));return n}},{key:"renderDotItem",value:function(e,t){var r;if(i().isValidElement(e))r=i().cloneElement(e,t);else if(v()(e))r=e(t);else{var n=t.key,a=R(t,M),s=(0,j.Z)("recharts-line-dot","boolean"!=typeof e?e.className:"");r=i().createElement(w.o,I({key:n},a,{className:s}))}return r}}],t&&$(n.prototype,t),r&&$(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a.PureComponent);B(H,"displayName","Line"),B(H,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!D.x.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),B(H,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,a=e.xAxisTicks,i=e.yAxisTicks,s=e.dataKey,o=e.bandSize,l=e.displayedData,c=e.offset,d=t.layout;return F({points:l.map(function(e,t){var l=(0,P.F$)(e,s);return"horizontal"===d?{x:(0,P.Hv)({axis:r,ticks:a,bandSize:o,entry:e,index:t}),y:g()(l)?null:n.scale(l),value:l,payload:e}:{x:g()(l)?null:r.scale(l),y:(0,P.Hv)({axis:n,ticks:i,bandSize:o,entry:e,index:t}),value:l,payload:e}}),layout:d},c)});var K=r(31170),U=r(28501),G=r.n(U),Y=r(51128),Q=r(821),X=r(60288),J=r(34718),ee=r(94438),et=r(81156);function er(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],a=0;a<e.length;a+=t){if(void 0!==r&&!0!==r(e[a]))return;n.push(e[a])}return n}function en(e,t,r,n,a){if(e*t<e*n||e*t>e*a)return!1;var i=r();return e*(t-e*i/2-n)>=0&&e*(t+e*i/2-a)<=0}function ea(e){return(ea="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ei(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function es(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ei(Object(r),!0).forEach(function(t){var n,a;n=t,a=r[t],(n=function(e){var t=function(e,t){if("object"!=ea(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ea(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ea(t)?t:t+""}(n))in e?Object.defineProperty(e,n,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[n]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ei(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var eo=["viewBox"],el=["viewBox"],ec=["ticks"];function ed(e){return(ed="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eu(){return(eu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function eh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ep(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eh(Object(r),!0).forEach(function(t){ex(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ef(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function em(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,eb(n.key),n)}}function ev(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ev=function(){return!!e})()}function ey(e){return(ey=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function eg(e,t){return(eg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function ex(e,t,r){return(t=eb(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eb(e){var t=function(e,t){if("object"!=ed(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ed(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ed(t)?t:t+""}var ej=function(e){var t,r;function n(e){var t,r,a;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,a=[e],r=ey(r),(t=function(e,t){if(t&&("object"===ed(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,ev()?Reflect.construct(r,a||[],ey(this).constructor):r.apply(this,a))).state={fontSize:"",letterSpacing:""},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&eg(e,t)}(n,e),t=[{key:"shouldComponentUpdate",value:function(e,t){var r=e.viewBox,n=ef(e,eo),a=this.props,i=a.viewBox,s=ef(a,el);return!(0,Y.w)(r,i)||!(0,Y.w)(n,s)||!(0,Y.w)(t,this.state)}},{key:"componentDidMount",value:function(){var e=this.layerReference;if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];t&&this.setState({fontSize:window.getComputedStyle(t).fontSize,letterSpacing:window.getComputedStyle(t).letterSpacing})}}},{key:"getTickLineCoord",value:function(e){var t,r,n,a,i,s,o=this.props,l=o.x,c=o.y,d=o.width,u=o.height,h=o.orientation,p=o.tickSize,f=o.mirror,m=o.tickMargin,v=f?-1:1,y=e.tickSize||p,g=(0,N.hj)(e.tickCoord)?e.tickCoord:e.coordinate;switch(h){case"top":t=r=e.coordinate,s=(n=(a=c+ +!f*u)-v*y)-v*m,i=g;break;case"left":n=a=e.coordinate,i=(t=(r=l+ +!f*d)-v*y)-v*m,s=g;break;case"right":n=a=e.coordinate,i=(t=(r=l+ +f*d)+v*y)+v*m,s=g;break;default:t=r=e.coordinate,s=(n=(a=c+ +f*u)+v*y)+v*m,i=g}return{line:{x1:t,y1:n,x2:r,y2:a},tick:{x:i,y:s}}}},{key:"getTickTextAnchor",value:function(){var e,t=this.props,r=t.orientation,n=t.mirror;switch(r){case"left":e=n?"start":"end";break;case"right":e=n?"end":"start";break;default:e="middle"}return e}},{key:"getTickVerticalAnchor",value:function(){var e=this.props,t=e.orientation,r=e.mirror,n="end";switch(t){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var e=this.props,t=e.x,r=e.y,n=e.width,a=e.height,s=e.orientation,o=e.mirror,l=e.axisLine,c=ep(ep(ep({},(0,A.L6)(this.props,!1)),(0,A.L6)(l,!1)),{},{fill:"none"});if("top"===s||"bottom"===s){var d=+("top"===s&&!o||"bottom"===s&&o);c=ep(ep({},c),{},{x1:t,y1:r+d*a,x2:t+n,y2:r+d*a})}else{var u=+("left"===s&&!o||"right"===s&&o);c=ep(ep({},c),{},{x1:t+u*n,y1:r,x2:t+u*n,y2:r+a})}return i().createElement("line",eu({},c,{className:(0,j.Z)("recharts-cartesian-axis-line",G()(l,"className"))}))}},{key:"renderTicks",value:function(e,t,r){var a=this,s=this.props,o=s.tickLine,l=s.stroke,c=s.tick,d=s.tickFormatter,u=s.unit,h=function(e,t,r){var n,a,i,s,o,l=e.tick,c=e.ticks,d=e.viewBox,u=e.minTickGap,h=e.orientation,p=e.interval,f=e.tickFormatter,m=e.unit,y=e.angle;if(!c||!c.length||!l)return[];if((0,N.hj)(p)||D.x.isSsr)return er(c,("number"==typeof p&&(0,N.hj)(p)?p:0)+1);var g="top"===h||"bottom"===h?"width":"height",x=m&&"width"===g?(0,ee.xE)(m,{fontSize:t,letterSpacing:r}):{width:0,height:0},b=function(e,n){var a,i,s=v()(f)?f(e.value,n):e.value;return"width"===g?(i={width:(a=(0,ee.xE)(s,{fontSize:t,letterSpacing:r})).width+x.width,height:a.height+x.height},(0,et.xE)(i,y)):(0,ee.xE)(s,{fontSize:t,letterSpacing:r})[g]},j=c.length>=2?(0,N.uY)(c[1].coordinate-c[0].coordinate):1,k=(n="width"===g,a=d.x,i=d.y,s=d.width,o=d.height,1===j?{start:n?a:i,end:n?a+s:i+o}:{start:n?a+s:i+o,end:n?a:i});return"equidistantPreserveStart"===p?function(e,t,r,n,a){for(var i,s=(n||[]).slice(),o=t.start,l=t.end,c=0,d=1,u=o;d<=s.length;)if(i=function(){var t,i=null==n?void 0:n[c];if(void 0===i)return{v:er(n,d)};var s=c,h=function(){return void 0===t&&(t=r(i,s)),t},p=i.coordinate,f=0===c||en(e,p,h,u,l);f||(c=0,u=o,d+=1),f&&(u=p+e*(h()/2+a),c+=d)}())return i.v;return[]}(j,k,b,c,u):("preserveStart"===p||"preserveStartEnd"===p?function(e,t,r,n,a,i){var s=(n||[]).slice(),o=s.length,l=t.start,c=t.end;if(i){var d=n[o-1],u=r(d,o-1),h=e*(d.coordinate+e*u/2-c);s[o-1]=d=es(es({},d),{},{tickCoord:h>0?d.coordinate-h*e:d.coordinate}),en(e,d.tickCoord,function(){return u},l,c)&&(c=d.tickCoord-e*(u/2+a),s[o-1]=es(es({},d),{},{isShow:!0}))}for(var p=i?o-1:o,f=function(t){var n,i=s[t],o=function(){return void 0===n&&(n=r(i,t)),n};if(0===t){var d=e*(i.coordinate-e*o()/2-l);s[t]=i=es(es({},i),{},{tickCoord:d<0?i.coordinate-d*e:i.coordinate})}else s[t]=i=es(es({},i),{},{tickCoord:i.coordinate});en(e,i.tickCoord,o,l,c)&&(l=i.tickCoord+e*(o()/2+a),s[t]=es(es({},i),{},{isShow:!0}))},m=0;m<p;m++)f(m);return s}(j,k,b,c,u,"preserveStartEnd"===p):function(e,t,r,n,a){for(var i=(n||[]).slice(),s=i.length,o=t.start,l=t.end,c=function(t){var n,c=i[t],d=function(){return void 0===n&&(n=r(c,t)),n};if(t===s-1){var u=e*(c.coordinate+e*d()/2-l);i[t]=c=es(es({},c),{},{tickCoord:u>0?c.coordinate-u*e:c.coordinate})}else i[t]=c=es(es({},c),{},{tickCoord:c.coordinate});en(e,c.tickCoord,d,o,l)&&(l=c.tickCoord-e*(d()/2+a),i[t]=es(es({},c),{},{isShow:!0}))},d=s-1;d>=0;d--)c(d);return i}(j,k,b,c,u)).filter(function(e){return e.isShow})}(ep(ep({},this.props),{},{ticks:e}),t,r),p=this.getTickTextAnchor(),f=this.getTickVerticalAnchor(),m=(0,A.L6)(this.props,!1),y=(0,A.L6)(c,!1),g=ep(ep({},m),{},{fill:"none"},(0,A.L6)(o,!1)),x=h.map(function(e,t){var r=a.getTickLineCoord(e),s=r.line,x=r.tick,b=ep(ep(ep(ep({textAnchor:p,verticalAnchor:f},m),{},{stroke:"none",fill:l},y),x),{},{index:t,payload:e,visibleTicksCount:h.length,tickFormatter:d});return i().createElement(S.m,eu({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,J.bw)(a.props,e,t)),o&&i().createElement("line",eu({},g,s,{className:(0,j.Z)("recharts-cartesian-axis-tick-line",G()(o,"className"))})),c&&n.renderTickItem(c,b,"".concat(v()(d)?d(e.value,t):e.value).concat(u||"")))});return i().createElement("g",{className:"recharts-cartesian-axis-ticks"},x)}},{key:"render",value:function(){var e=this,t=this.props,r=t.axisLine,n=t.width,a=t.height,s=t.ticksGenerator,o=t.className;if(t.hide)return null;var l=this.props,c=l.ticks,d=ef(l,ec),u=c;return(v()(s)&&(u=s(c&&c.length>0?this.props:d)),n<=0||a<=0||!u||!u.length)?null:i().createElement(S.m,{className:(0,j.Z)("recharts-cartesian-axis",o),ref:function(t){e.layerReference=t}},r&&this.renderAxisLine(),this.renderTicks(u,this.state.fontSize,this.state.letterSpacing),X._.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(e,t,r){var n=(0,j.Z)(t.className,"recharts-cartesian-axis-tick-value");return i().isValidElement(e)?i().cloneElement(e,ep(ep({},t),{},{className:n})):v()(e)?e(ep(ep({},t),{},{className:n})):i().createElement(Q.x,eu({},t,{className:"recharts-cartesian-axis-tick-value"}),r)}}],t&&em(n.prototype,t),r&&em(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a.Component);function ek(e){return(ek="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ew(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(ew=function(){return!!e})()}function eS(e){return(eS=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function eO(e,t){return(eO=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eC(e,t,r){return(t=eN(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eN(e){var t=function(e,t){if("object"!=ek(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ek(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ek(t)?t:t+""}function eA(){return(eA=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function eD(e){var t=e.xAxisId,r=(0,K.zn)(),n=(0,K.Mw)(),i=(0,K.bH)(t);return null==i?null:a.createElement(ej,eA({},i,{className:(0,j.Z)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return(0,P.uY)(e,!0)}}))}ex(ej,"displayName","CartesianAxis"),ex(ej,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var eP=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=eS(e),function(e,t){if(t&&("object"===ek(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,ew()?Reflect.construct(e,t||[],eS(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&eO(e,t)}(r,e),t=[{key:"render",value:function(){return a.createElement(eD,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,eN(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a.Component);function eT(e){return(eT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function eM(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(eM=function(){return!!e})()}function eE(e){return(eE=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function eR(e,t){return(eR=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eI(e,t,r){return(t=eZ(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eZ(e){var t=function(e,t){if("object"!=eT(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=eT(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==eT(t)?t:t+""}function eF(){return(eF=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}eC(eP,"displayName","XAxis"),eC(eP,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});var e_=function(e){var t=e.yAxisId,r=(0,K.zn)(),n=(0,K.Mw)(),i=(0,K.Ud)(t);return null==i?null:a.createElement(ej,eF({},i,{className:(0,j.Z)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(e){return(0,P.uY)(e,!0)}}))},eL=function(e){var t;function r(){var e,t;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,r),e=r,t=arguments,e=eE(e),function(e,t){if(t&&("object"===eT(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,eM()?Reflect.construct(e,t||[],eE(this).constructor):e.apply(this,t))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&eR(e,t)}(r,e),t=[{key:"render",value:function(){return a.createElement(e_,this.props)}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,eZ(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(a.Component);eI(eL,"displayName","YAxis"),eI(eL,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var e$=(0,p.z)({chartName:"LineChart",GraphicalChild:H,axisComponents:[{axisType:"xAxis",AxisComp:eP},{axisType:"yAxis",AxisComp:eL}],formatAxisMap:et.t9}),eV=r(10706),eW=r(43160),eq=r(38440),eB=r(85114),ez=r(28599),eH=r(20148);let eK=e=>{let t={onVoyage:0,availableForVoyage:0,outOfService:0};for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)){let n=e[r];t.onVoyage+=n.onVoyage,t.availableForVoyage+=n.availableForVoyage,t.outOfService+=n.outOfService}return t},eU=(e,t)=>{let r=[],n=new Date(e.getTime());do r.push(eV.WU(n,"yyyy-MM")+"-01"),n=new Date(ez.z(n,1).getTime());while(!1===eH.x(n,t));return r.push(eV.WU(n,"yyyy-MM")+"-01"),r};var eG=r(45519);let eY=(0,eG.ZP)`
    query readVesselStatuss(
        $filter: VesselStatusFilterFields = {}
        $offset: Int = 0
        $limit: Int = 100
    ) {
        readVesselStatuss(
            sort: { date: ASC }
            filter: $filter
            limit: $limit
            offset: $offset
        ) {
            nodes {
                id
                date
                status
                vesselID
                created
            }
        }
    }
`,eQ=(0,eG.ZP)`
    query ReadVessels(
        $vesselFilter: VesselFilterFields = {}
        $statusFilter: VesselStatusFilterFields = {}
    ) {
        readVessels(filter: $vesselFilter) {
            nodes {
                id
                statusHistory(
                    filter: $statusFilter
                    sort: { date: DESC, created: DESC }
                    limit: 1
                ) {
                    nodes {
                        id
                        date
                        created
                        status
                    }
                }
            }
        }
    }
`;var eX=r(25394);let eJ={service_days_percent:{label:"Service Status",color:"#7B8795"}};function e1({startMonth:e,endMonth:t}){let[r,i]=(0,a.useState)([]),[s,{called:o,loading:c}]=(0,l.t)(eY,{fetchPolicy:"cache-and-network",onError:e=>{console.error("queryLogBookEntrySections error",e)}}),[d]=(0,l.t)(eQ,{fetchPolicy:"cache-and-network",onError:e=>{console.error("queryLogBookEntrySections error",e)}});return(0,a.useCallback)(async()=>{let r=eV.WU(eW.N(e),"yyyy-MM-dd"),n=eV.WU(eq.V(t),"yyyy-MM-dd"),{data:a}=await s({variables:{filter:{date:{gte:r,lte:n}},limit:0}}),{data:o}=await d({variables:{statusFilter:{date:{lte:r}},filter:{archived:{eq:!1}}}}),l=o?.readVessels?.nodes??[],c=a?.readVesselStatuss?.nodes??[],u=eU(e,t),h={};l.forEach(e=>{h[e.id]=(0,eB.O)(e.statusHistory?.nodes??[])}),i(u.map(e=>{let t=c.filter(t=>eV.WU(t.date,"yyyy-MM")===eV.WU(e,"yyyy-MM")),r=l.map(e=>e.id),n={};r.forEach(r=>{let a=t.filter(e=>e.vesselID===r),i=h[r],s=eW.N(e),o=eq.V(e);n[r]=(0,eB.D)(a,i,s,o),a.length>0&&(h[r]=(0,eB.O)(a))});let a=100,i=0,s=0;if(Object.keys(n).length>0){let e=eK(n);i=e.onVoyage+e.availableForVoyage,a=(s=e.onVoyage+e.availableForVoyage+e.outOfService)>0?i/s*100:100}return{title:eV.WU(e,"MMM yy"),service_days_percent:parseFloat(a.toFixed(2)),service_days:i,total_days:s}}))},[e,t]),(0,n.jsxs)("div",{className:"flex flex-col h-80",children:[(0,n.jsxs)("div",{children:[n.jsx(eX.P,{children:"Vessels in service"}),n.jsx(e0,{data:r})]}),n.jsx("div",{className:"p-2 h-40",children:n.jsx(h.BO,{config:eJ,children:(0,n.jsxs)(e$,{accessibilityLayer:!0,data:r,margin:{top:20,left:12,right:12},children:[n.jsx(h.h7,{cursor:!1,content:n.jsx(h.dg,{hideLabel:!0,hideIndicator:!0,className:"chartToolTip",formatter:(e,t,r,a)=>{let i=r.payload;return(0,n.jsxs)("div",{className:"max-w-sm",children:[n.jsx("div",{children:i.title}),(0,n.jsxs)("div",{className:"font-normal text-xs",children:["Your vessels were in service",n.jsx("br",{}),"for"," ",(0,n.jsxs)("b",{className:"font-semibold",children:[e,"%"]})," ","of the month"]})]})}})}),n.jsx(H,{dataKey:"service_days_percent",type:"linear",stroke:"var(--color-service_days_percent)",strokeWidth:1.5,dot:{stroke:"var(--color-service_days_percent)",strokeWidth:1.5,fill:"#BEDEF9",r:5},activeDot:{r:6}})]})})})]})}let e0=({data:e})=>{let t=(0,a.useMemo)(()=>{let t=e.length,r=e[t-1];return r?.service_days_percent??100},[e]),r=(0,a.useMemo)(()=>{let t=e.length,r=e[t-1],n=e[t-2];return parseFloat(((r?.service_days_percent??100)-(n?.service_days_percent??100)).toFixed(2))},[e]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("p",{className:"text-5xl font-black mb-1",children:[t,"%"]}),0!==r&&(0,n.jsxs)("p",{className:"text-curious-blue-400 text-sm",children:[r>0&&"+",r,"% from last month"]}),0===r&&n.jsx("p",{className:"text-curious-blue-400 text-xs",children:"No changes from last month"})]})};var e2=r(74602),e3=r(65128);let e4=(0,eG.ZP)`
    query ReadOneSeaLogsMember($filter: SeaLogsMemberFilterFields) {
        readOneSeaLogsMember(filter: $filter) {
            client {
                hqAddress {
                    timeZone
                }
            }
            departments {
                nodes {
                    basicComponents {
                        nodes {
                            id
                            title
                            identifier
                        }
                    }
                }
            }
        }
    }
`,e5=(0,eG.ZP)`
    query ReadDashboardData($archived: Int = 0) {
        readDashboardData(archived: $archived) {
            vessels {
                id
                title
                tasksDue
                trainingsDue
                showOnDashboard
                icon
                iconMode
                photoID
                logentryID
                vesselStatus
            }
        }
    }
`;function e6(){var e;let[t,r]=(0,a.useState)(!0),[i,s]=(0,a.useState)([]),[h,p]=(0,a.useState)([]),[f,m]=(0,a.useState)(!1),[v]=(0,l.t)(e4,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneSeaLogsMember;if(t){localStorage.setItem("timezone",t.client.hqAddress.timeZone||"Pacific/Auckland");let e=t.departments.nodes.flatMap(e=>e.basicComponents.nodes);m(e),0===e.length&&m(!0)}},onError:e=>{console.error("querySeaLogsMembers error",e)}}),[y]=(0,l.t)(e5,{fetchPolicy:"no-cache",onCompleted:async e=>{e.readDashboardData},onError:e=>{console.error("queryVessels error",e)}}),g=(e,t)=>{s(r=>{let n=r.findIndex(t=>t.id==e.id);if(-1===n)return r;let a=[...r];return e.status=t,a[n]=e,a})},x=(0,c.wu)([{accessorKey:"title",header:"",cell:({row:e})=>{let t=e.original,r=t.status;return n.jsx(n.Fragment,{children:r&&"OutOfService"!==r?(0,n.jsxs)(o.default,{className:" inline-flex flex-row gap-2 whitespace-nowrap items-center",href:`/vessel/info?id=${t.id}&name=${t.title}`,children:[n.jsx("div",{className:"border-2 shrink-0 border-border rounded-full hidden small:inline-block",children:n.jsx(d.Z,{vessel:t})}),(0,n.jsxs)("div",{className:"grid",children:[n.jsx("span",{className:"font-medium truncate hover:text-curious-blue-400",children:t.title}),0!==t.logentryID?n.jsx("div",{className:"text-curious-blue-400 text-[10px]",children:"ON VOYAGE"}):n.jsx("div",{className:"text-curious-blue-400 text-[10px]",children:"READY FOR VOYAGE"})]})]}):(0,n.jsxs)(o.default,{className:"flex flex-row gap-2 whitespace-nowrap items-center",href:`/vessel/info?id=${t.id}&name=${t.title}`,children:[(0,n.jsxs)("div",{className:"relative hidden small:inline-block shrink-0 overflow-hidden border-2 border-destructive rounded-full",children:[n.jsx(d.Z,{vessel:t}),n.jsx("div",{className:"absolute inset-0 bg-destructive opacity-50 rounded-full",children:"        "})]}),(0,n.jsxs)("div",{className:"grid",children:[n.jsx("span",{className:"font-medium opacity-50 truncate",children:t.title}),n.jsx("div",{className:"inline-block text-[10px] text-destructive",children:"OUT OF SERVICE"})]})]})})}},{accessorKey:"trainingsDue",header:"Training",cellAlignment:"center",cell:({row:e})=>{let t=e.original;return n.jsx("div",{className:"flex flex-1 justify-center",children:t.trainingsDue>0?n.jsx("div",{className:"alert !rounded-full flex w-8 h-8",children:t.trainingsDue}):n.jsx("div",{className:"text-bright-turquoise-600 border bg-bright-turquoise-100 border-bright-turquoise-600 items-center justify-center p-2 rounded-full flex w-8 h-8",children:n.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"#00a396","aria-hidden":"true",children:n.jsx("path",{fillRule:"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z",clipRule:"evenodd"})})})})}},{accessorKey:"tasksDue",header:"Tasks",cellAlignment:"center",cell:({row:e})=>{let t=e.original;return n.jsx("div",{className:"flex flex-1 justify-center",children:t.tasksDue>0?n.jsx("div",{className:"alert !rounded-full flex flex-shrink-0 w-8 h-8",children:t.tasksDue}):n.jsx("div",{className:"text-bright-turquoise-600 border bg-bright-turquoise-100 border-bright-turquoise-600 items-center justify-center p-2 rounded-full flex w-8 h-8",children:n.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"#27AB83","aria-hidden":"true",children:n.jsx("path",{fillRule:"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z",clipRule:"evenodd"})})})})}},{id:"actions",enableHiding:!1,cellAlignment:"right",cell:({row:e})=>{let t=e.original;return n.jsx(e3.z,{vessel:t,onChangeStatusSuccess:e=>g(t,e)})}}]);return(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex py-3 items-baseline gap-2 phablet:gap-4",children:[n.jsx(u.bF,{}),n.jsx(o.default,{href:"/vessel",children:n.jsx(e2.H1,{children:"Vessels"})})]}),n.jsx(e1,{startMonth:(e=new Date,(0,ez.z)(e,-5)),endMonth:new Date}),h.length>0&&(0,n.jsxs)("div",{className:"space-y-5",children:[n.jsx(c.wQ,{columns:x,data:h.map(e=>({...e,trainingsDue:e.trainingsDue||0,tasksDue:e.tasksDue||0})),showToolbar:!1,className:"p-0 border-0 shadow-none"}),n.jsx("div",{className:"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center",children:n.jsx(o.default,{href:"/vessel",className:"text-accent-foreground uppercase hover:text-curious-blue-400 text-xs",children:"See all vessels"})})]})]})}(0,eG.ZP)`
    query ReadVessels(
        $vesselFilter: VesselFilterFields = {}
        $statusFilter: VesselStatusFilterFields = {}
    ) {
        readVessels(filter: $vesselFilter) {
            nodes {
                id
                statusHistory(
                    filter: $statusFilter
                    sort: { date: DESC, created: DESC }
                    limit: 1
                ) {
                    nodes {
                        id
                        date
                        status
                        created
                        comment
                        reason
                        otherReason
                        expectedReturn
                    }
                }
            }
        }
    }
`;var e7=r(57659),e8=r(69424),e9=r(13842);r(46776);var te=r(15559),tt=r(26445),tr=r(80076),tn=r(95949),ta=r(60307),ti=r(78445);function ts(e){return(ts="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function to(){return(to=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function tl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function tc(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?tl(Object(r),!0).forEach(function(t){tf(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):tl(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function td(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,tm(n.key),n)}}function tu(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(tu=function(){return!!e})()}function th(e){return(th=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function tp(e,t){return(tp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function tf(e,t,r){return(t=tm(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function tm(e){var t=function(e,t){if("object"!=ts(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=ts(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==ts(t)?t:t+""}var tv=function(e){var t,r;function n(e){var t,r,a;return function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n),r=n,a=[e],r=th(r),tf(t=function(e,t){if(t&&("object"===ts(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,tu()?Reflect.construct(r,a||[],th(this).constructor):r.apply(this,a)),"pieRef",null),tf(t,"sectorRefs",[]),tf(t,"id",(0,N.EL)("recharts-pie-")),tf(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),v()(e)&&e()}),tf(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),v()(e)&&e()}),t.state={isAnimationFinished:!e.isAnimationActive,prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,sectorToFocus:0},t}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&tp(e,t)}(n,e),t=[{key:"isActiveIndex",value:function(e){var t=this.props.activeIndex;return Array.isArray(t)?-1!==t.indexOf(e):e===t}},{key:"hasActiveIndex",value:function(){var e=this.props.activeIndex;return Array.isArray(e)?0!==e.length:e||0===e}},{key:"renderLabels",value:function(e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var t=this.props,r=t.label,a=t.labelLine,s=t.dataKey,o=t.valueKey,l=(0,A.L6)(this.props,!1),c=(0,A.L6)(r,!1),d=(0,A.L6)(a,!1),u=r&&r.offsetRadius||20,h=e.map(function(e,t){var h=(e.startAngle+e.endAngle)/2,p=(0,tr.op)(e.cx,e.cy,e.outerRadius+u,h),f=tc(tc(tc(tc({},l),e),{},{stroke:"none"},c),{},{index:t,textAnchor:n.getTextAnchor(p.x,e.cx)},p),m=tc(tc(tc(tc({},l),e),{},{fill:"none",stroke:e.fill},d),{},{index:t,points:[(0,tr.op)(e.cx,e.cy,e.outerRadius,h),p]}),v=s;return g()(s)&&g()(o)?v="value":g()(s)&&(v=o),i().createElement(S.m,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},a&&n.renderLabelLineItem(a,m,"line"),n.renderLabelItem(r,f,(0,P.F$)(e,v)))});return i().createElement(S.m,{className:"recharts-pie-labels"},h)}},{key:"renderSectorsStatically",value:function(e){var t=this,r=this.props,n=r.activeShape,a=r.blendStroke,s=r.inactiveShape;return e.map(function(r,o){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==e.length)return null;var l=t.isActiveIndex(o),c=s&&t.hasActiveIndex()?s:null,d=tc(tc({},r),{},{stroke:a?r.fill:r.stroke,tabIndex:-1});return i().createElement(S.m,to({ref:function(e){e&&!t.sectorRefs.includes(e)&&t.sectorRefs.push(e)},tabIndex:-1,className:"recharts-pie-sector"},(0,J.bw)(t.props,r,o),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(o)}),i().createElement(ti.bn,to({option:l?n:c,isActive:l,shapeType:"sector"},d)))})}},{key:"renderSectorsWithAnimation",value:function(){var e=this,t=this.props,r=t.sectors,n=t.isAnimationActive,a=t.animationBegin,s=t.animationDuration,o=t.animationEasing,l=t.animationId,c=this.state,d=c.prevSectors,u=c.prevIsAnimationActive;return i().createElement(f.ZP,{begin:a,duration:s,isActive:n,easing:o,from:{t:0},to:{t:1},key:"pie-".concat(l,"-").concat(u),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(t){var n=t.t,a=[],s=(r&&r[0]).startAngle;return r.forEach(function(e,t){var r=d&&d[t],i=t>0?G()(e,"paddingAngle",0):0;if(r){var o=(0,N.k4)(r.endAngle-r.startAngle,e.endAngle-e.startAngle),l=tc(tc({},e),{},{startAngle:s+i,endAngle:s+o(n)+i});a.push(l),s=l.endAngle}else{var c=e.endAngle,u=e.startAngle,h=(0,N.k4)(0,c-u)(n),p=tc(tc({},e),{},{startAngle:s+i,endAngle:s+h+i});a.push(p),s=p.endAngle}}),i().createElement(S.m,null,e.renderSectorsStatically(a))})}},{key:"attachKeyboardHandlers",value:function(e){var t=this;e.onkeydown=function(e){if(!e.altKey)switch(e.key){case"ArrowLeft":var r=++t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[r].focus(),t.setState({sectorToFocus:r});break;case"ArrowRight":var n=--t.state.sectorToFocus<0?t.sectorRefs.length-1:t.state.sectorToFocus%t.sectorRefs.length;t.sectorRefs[n].focus(),t.setState({sectorToFocus:n});break;case"Escape":t.sectorRefs[t.state.sectorToFocus].blur(),t.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var e=this.props,t=e.sectors,r=e.isAnimationActive,n=this.state.prevSectors;return r&&t&&t.length&&(!n||!b()(n,t))?this.renderSectorsWithAnimation():this.renderSectorsStatically(t)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var e=this,t=this.props,r=t.hide,n=t.sectors,a=t.className,s=t.label,o=t.cx,l=t.cy,c=t.innerRadius,d=t.outerRadius,u=t.isAnimationActive,h=this.state.isAnimationFinished;if(r||!n||!n.length||!(0,N.hj)(o)||!(0,N.hj)(l)||!(0,N.hj)(c)||!(0,N.hj)(d))return null;var p=(0,j.Z)("recharts-pie",a);return i().createElement(S.m,{tabIndex:this.props.rootTabIndex,className:p,ref:function(t){e.pieRef=t}},this.renderSectors(),s&&this.renderLabels(n),X._.renderCallByParent(this.props,null,!1),(!u||h)&&O.e.renderCallByParent(this.props,n,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return t.prevIsAnimationActive!==e.isAnimationActive?{prevIsAnimationActive:e.isAnimationActive,prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:[],isAnimationFinished:!0}:e.isAnimationActive&&e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curSectors:e.sectors,prevSectors:t.curSectors,isAnimationFinished:!0}:e.sectors!==t.curSectors?{curSectors:e.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(e,t){return e>t?"start":e<t?"end":"middle"}},{key:"renderLabelLineItem",value:function(e,t,r){if(i().isValidElement(e))return i().cloneElement(e,t);if(v()(e))return e(t);var n=(0,j.Z)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return i().createElement(k.H,to({},t,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(e,t,r){if(i().isValidElement(e))return i().cloneElement(e,t);var n=r;if(v()(e)&&(n=e(t),i().isValidElement(n)))return n;var a=(0,j.Z)("recharts-pie-label-text","boolean"==typeof e||v()(e)?"":e.className);return i().createElement(Q.x,to({},t,{alignmentBaseline:"middle",className:a}),n)}}],t&&td(n.prototype,t),r&&td(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a.PureComponent);tf(tv,"displayName","Pie"),tf(tv,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!D.x.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),tf(tv,"parseDeltaAngle",function(e,t){return(0,N.uY)(t-e)*Math.min(Math.abs(t-e),360)}),tf(tv,"getRealPieData",function(e){var t=e.data,r=e.children,n=(0,A.L6)(e,!1),a=(0,A.NN)(r,tn.b);return t&&t.length?t.map(function(e,t){return tc(tc(tc({payload:e},n),e),a&&a[t]&&a[t].props)}):a&&a.length?a.map(function(e){return tc(tc({},n),e.props)}):[]}),tf(tv,"parseCoordinateOfPie",function(e,t){var r=t.top,n=t.left,a=t.width,i=t.height,s=(0,tr.$4)(a,i);return{cx:n+(0,N.h1)(e.cx,a,a/2),cy:r+(0,N.h1)(e.cy,i,i/2),innerRadius:(0,N.h1)(e.innerRadius,s,0),outerRadius:(0,N.h1)(e.outerRadius,s,.8*s),maxRadius:e.maxRadius||Math.sqrt(a*a+i*i)/2}}),tf(tv,"getComposedData",function(e){var t,r,n=e.item,a=e.offset,i=void 0!==n.type.defaultProps?tc(tc({},n.type.defaultProps),n.props):n.props,s=tv.getRealPieData(i);if(!s||!s.length)return null;var o=i.cornerRadius,l=i.startAngle,c=i.endAngle,d=i.paddingAngle,u=i.dataKey,h=i.nameKey,p=i.valueKey,f=i.tooltipType,m=Math.abs(i.minAngle),v=tv.parseCoordinateOfPie(i,a),y=tv.parseDeltaAngle(l,c),x=Math.abs(y),b=u;g()(u)&&g()(p)?((0,ta.Z)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),b="value"):g()(u)&&((0,ta.Z)(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),b=p);var j=s.filter(function(e){return 0!==(0,P.F$)(e,b,0)}).length,k=x-j*m-(x>=360?j:j-1)*d,w=s.reduce(function(e,t){var r=(0,P.F$)(t,b,0);return e+((0,N.hj)(r)?r:0)},0);return w>0&&(t=s.map(function(e,t){var n,a=(0,P.F$)(e,b,0),i=(0,P.F$)(e,h,t),s=((0,N.hj)(a)?a:0)/w,c=(n=t?r.endAngle+(0,N.uY)(y)*d*(0!==a?1:0):l)+(0,N.uY)(y)*((0!==a?m:0)+s*k),u=(n+c)/2,p=(v.innerRadius+v.outerRadius)/2,g=[{name:i,value:a,payload:e,dataKey:b,type:f}],x=(0,tr.op)(v.cx,v.cy,p,u);return r=tc(tc(tc({percent:s,cornerRadius:o,name:i,tooltipPayload:g,midAngle:u,middleRadius:p,tooltipPosition:x},e),v),{},{value:(0,P.F$)(e,b),startAngle:n,endAngle:c,payload:e,paddingAngle:(0,N.uY)(y)*d})})),tc(tc({},v),{},{sectors:t,data:s})});var ty=(0,p.z)({chartName:"PieChart",GraphicalChild:tv,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:te.I},{axisType:"radiusAxis",AxisComp:tt.S}],formatAxisMap:tr.t9,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});function tg({chartConfig:e,chartData:t}){return n.jsx("div",{className:"flex flex-col border-0 !shadow-none !p-0",children:n.jsx("div",{className:"h-full w-full p-0",children:n.jsx(h.BO,{config:e,className:"mx-auto aspect-square max-h-[250px] pb-0 [&_.recharts-pie-label-text]:fill-foreground",children:(0,n.jsxs)(ty,{children:[n.jsx(h.h7,{content:n.jsx(h.dg,{hideLabel:!0,hideIndicator:!0,className:"chartToolTip"})}),n.jsx(tv,{data:t,dataKey:"amount",nameKey:"title",stroke:"#fff",paddingAngle:5})]})})})})}let tx=(0,eG.ZP)`
    query ReadComponentMaintenanceChecks(
        $limit: Int = 500
        $offset: Int = 0
        $filter: ComponentMaintenanceCheckFilterFields = {}
    ) {
        readComponentMaintenanceChecks(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            nodes {
                id
                archived
                startDate
                expires
                completed
                status
                dateCompleted
                maintenanceSchedule {
                    occursEveryType
                    occursEvery
                    engineUsage {
                        nodes {
                            isScheduled
                            lastScheduleHours
                            engine {
                                currentHours
                            }
                        }
                    }
                }
            }
        }
    }
`;function tb(){let[e,t]=(0,a.useState)(),[r,i]=(0,a.useState)({}),[s,o]=(0,a.useState)(!0),[c,d]=(0,a.useState)([]),[u,h]=(0,a.useState)(!1),[p,f]=(0,a.useState)(!1),[m,v]=(0,a.useState)(),[y,g]=(0,a.useState)(),[x,b]=(0,a.useState)(),[j,k]=(0,a.useState)(),[w,S]=(0,a.useState)(),[O,C]=(0,a.useState)(),[N]=(0,l.t)(tx,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readComponentMaintenanceChecks.nodes;t&&A(t)},onError:e=>{console.error("queryMaintenanceChecks error",e)}}),A=e=>{t(e.filter(e=>!1===e.archived).map(e=>({...e,isOverDue:(0,e9.AT)(e)})))};return(0,n.jsxs)("div",{className:"flex flex-col text-center",children:[n.jsx(eX.P,{children:"Tasks due - days"}),n.jsx(tg,{chartData:[{title:"Tasks overdue",amount:m,fill:"var(--color-overdue)",stroke:"hsl(1, 97%, 60%)"},{title:"Tasks due < 30",amount:j,fill:"var(--color-thirtyDays)",stroke:"hsl(205, 78%, 48%)"},{title:"Tasks due 30 - 90",amount:w,fill:"var(--color-thirtyToNinety)",stroke:"hsl(205, 32%, 45%)"},{title:"Tasks due > 90",amount:O,fill:"var(--color-ninetyPlus)",stroke:"hsl(174, 100%, 40%)"}],chartConfig:{amount:{label:"Amount"},overdue:{label:"Tasks overdue",color:"var(--chart-1)"},thirtyDays:{label:"Tasks due under 30-days",color:"var(--chart-3)"},thirtyToNinety:{label:"Tasks due 30-90 days",color:"var(--chart-4)"},ninetyPlus:{label:"Tasks due 90+ days",color:"var(--chart-5)"}}})]})}var tj=r(35024);let tk=(0,eG.ZP)`
    query ReadComponentMaintenanceChecks(
        $limit: Int = 500
        $offset: Int = 0
        $filter: ComponentMaintenanceCheckFilterFields = {}
    ) {
        readComponentMaintenanceChecks(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            nodes {
                id
                archived
                startDate
                dateCompleted
                completed
                expires
                status
                maintenanceSchedule {
                    occursEveryType
                    occursEvery
                    engineUsage {
                        nodes {
                            isScheduled
                            lastScheduleHours
                            engine {
                                currentHours
                            }
                        }
                    }
                }
            }
        }
    }
`;function tw(){let[e,t]=(0,a.useState)(),[r,i]=(0,a.useState)({}),[s,o]=(0,a.useState)(!0),[c,d]=(0,a.useState)([]),[u,h]=(0,a.useState)(!1),[p,f]=(0,a.useState)(!1),[m,v]=(0,a.useState)(),[y,g]=(0,a.useState)(),[x,b]=(0,a.useState)(),[j,k]=(0,a.useState)(),[w,S]=(0,a.useState)(),[O,C]=(0,a.useState)(),[N]=(0,l.t)(tk,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readComponentMaintenanceChecks.nodes;t&&A(t)},onError:e=>{console.error("queryMaintenanceChecks error",e)}}),A=e=>{t(e.filter(e=>!1===e.archived).map(e=>({...e,isOverDue:(0,e9.AT)(e)})))};return(0,n.jsxs)("div",{children:[n.jsx(tg,{chartData:[{title:"Task overdue",amount:m,fill:"var(--color-overdue)",stroke:"hsl(1, 97%, 60%)"},{title:"Tasks due < 30",amount:j,fill:"var(--color-thirtyDays)",stroke:"hsl(205, 78%, 48%)"},{title:"Tasks due 30 - 100",amount:w,fill:"var(--color-thirtyToNinety)",stroke:"hsl(205, 32%, 45%)"},{title:"Tasks due > 100",amount:O,fill:"var(--color-ninetyPlus)",stroke:"hsl(174, 100%, 40%)"}],chartConfig:{amount:{label:"Amount"},overdue:{label:"Task overdue based on engine hours",color:"var(--chart-1)"},thirtyDays:{label:"Tasks due within 30 hours",color:"var(--chart-3)"},thirtyToNinety:{label:"Tasks due 30 to 100 hours",color:"var(--chart-4)"},ninetyPlus:{label:"Tasks due in more than 100 hours",color:"var(--chart-5)"}}}),(0,n.jsxs)(tj.Ol,{className:"items-center pb-0",children:[n.jsx(tj.ll,{children:"Tasks due - engine hours"}),n.jsx(tj.SZ,{})]})]})}let tS=(0,eG.ZP)`
    query ReadComponentMaintenanceCheckList(
        $inventoryID: Int!
        $vesselID: Int!
    ) {
        readComponentMaintenanceCheckList(
            inventoryID: $inventoryID
            vesselID: $vesselID
        ) {
            list
        }
    }
`;var tO=r(84340);function tC(){let e=(0,e8.usePathname)(),t=(0,e8.useSearchParams)(),[r,i]=(0,a.useState)([]),[s,d]=(0,a.useState)(!0),[u]=(0,l.t)(tS,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readComponentMaintenanceCheckList[0].list;t&&i(t)},onError:e=>{console.error("queryMaintenanceChecks error",e)}}),h=(0,c.wu)([{accessorKey:"title",header:"",cell:({row:r})=>{let a=r.original;return n.jsx("div",{className:"flex items-center h-full",children:n.jsx(o.default,{href:`/maintenance?taskID=${a.id}&redirect_to=${e}?${t.toString()}`,className:`${"High"===a.severity?"group-hover:text-destructive":""} hover:text-curious-blue-400`,children:a.name})})}},{accessorKey:"due",header:"",cellAlignment:"right",cell:({row:e})=>{let t=e.original,r=t.isOverDue?.status,a=t.isOverDue?.day;return n.jsx(n.Fragment,{children:"High"===r?n.jsx("div",{className:"flex-1 flex justify-end",children:n.jsx("div",{className:` items-end w-fit
                                            ${"High"===r?"alert whitespace-nowrap":""}
                                            `,children:-1*a+" days ago"})}):n.jsx(tO.OE,{maintenanceCheck:t})})}}]);return(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex py-3 items-baseline gap-2 phablet:gap-4",children:[n.jsx(e7.y,{}),n.jsx(o.default,{href:"/maintenance",className:"text-4xl font-bold",children:"Maintenance"})]}),(0,n.jsxs)("div",{className:"grid xs:grid-cols-2 xs:h-80",children:[n.jsx(tb,{}),n.jsx(tw,{})]}),(0,n.jsxs)("div",{className:"space-y-5",children:[r&&r?.filter(e=>!("Completed"===e.status||"Save_As_Draft"===e.status))?.length>0?n.jsx(c.wQ,{columns:h,data:r.filter(e=>!("Completed"===e.status||"Save_As_Draft"===e.status)).slice(0,5),showToolbar:!1,className:"p-0 pt-8 border-0 shadow-none"}):(0,n.jsxs)("div",{className:"flex justify-between items-center gap-2 p-2 pt-4",children:[n.jsx("div",{children:(0,n.jsxs)("svg",{className:"!w-[75px] h-auto",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 148.02 147.99",children:[n.jsx("path",{d:"M70.84.56c16-.53,30.66,3.59,43.98,12.35,12.12,8.24,21.1,19.09,26.92,32.55,6.14,14.85,7.38,30.11,3.74,45.78-3.92,15.59-11.95,28.57-24.1,38.96-13.11,10.9-28.24,16.66-45.39,17.28-16.75.33-31.88-4.39-45.39-14.17-13.29-9.92-22.34-22.84-27.16-38.76-4.03-14.16-3.9-28.29.39-42.38,5-15.45,14-27.97,27.01-37.6C42.77,5.97,56.1,1.31,70.84.56Z",fill:"#fefefe",fillRule:"evenodd",stroke:"#024450",strokeMiterlimit:"10",strokeWidth:"1.02px"}),n.jsx("path",{d:"M63.03,13.61c1.74.02,3.47.13,5.19.32,2.15.26,4.31.51,6.46.78,1.18.34,2.08,1.04,2.69,2.11.56,1,.85,2.06.87,3.2,1.5,2.89,2.99,5.79,4.47,8.69.09.17.19.32.32.46,1.72,1.08,3.12,2.48,4.2,4.2.42.79.72,1.63.9,2.5-.04.01-.07.04-.1.07.58,1.01.64,2.04.17,3.11-.47.88-1.1,1.62-1.92,2.21-1.17.81-2.44,1.45-3.79,1.92-.07.56-.13,1.13-.17,1.7,0,.86-.03,1.72-.1,2.57-.14.56-.42,1.04-.85,1.43-.38.3-.8.39-1.26.27-.01,1.92-.46,3.73-1.33,5.44-.59,2.66-1.36,5.27-2.33,7.82-.4,1.04-.96,1.99-1.67,2.84-.36-.12-.73-.2-1.12-.27-.28,0-.53.08-.78.22-.23.16-.45.33-.68.49-.83.87-1.67,1.73-2.52,2.57-.78.67-1.68,1.03-2.72,1.09-.09-.26-.18-.52-.27-.78-.26-.26-.58-.43-.95-.51-1.68-.23-3.27.06-4.76.87-.28.24-.56.48-.85.7-.95-1.87-2.36-3.27-4.25-4.2-.37-.14-.74-.25-1.12-.34-.42-.03-.84-.03-1.26,0-.19.06-.38.1-.58.1-.58-.66-1.04-1.39-1.38-2.21-1.11-2.73-1.98-5.53-2.62-8.4-.89-1.7-1.33-3.51-1.33-5.44-.97.14-1.64-.25-2.01-1.17-.12-.3-.2-.6-.24-.92-.01-.76-.03-1.52-.05-2.28-.02-.39-.07-.78-.15-1.17-1.41-.47-2.77-1.07-4.05-1.82-.82-.49-1.54-1.09-2.16-1.82-.66-.81-.93-1.73-.83-2.77.33-1.03.65-2.06.92-3.11.56-1.18,1.32-2.22,2.26-3.13,1.27-1.15,2.67-2.11,4.2-2.89,1.39-2.69,2.79-5.37,4.17-8.06.01-1.77.66-3.26,1.92-4.49.47-.39,1-.67,1.6-.83,3.29-.42,6.57-.79,9.85-1.09Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M63.17,14.97c2.44.07,4.86.25,7.28.56,1.3.16,2.59.33,3.88.49.85.26,1.5.78,1.92,1.58.43.87.64,1.79.63,2.77,1.18,2.31,2.37,4.62,3.57,6.92-3.88-1.88-7.97-3.04-12.28-3.5-5.82-.65-11.53-.15-17.14,1.5-1.08.33-2.13.73-3.16,1.19l-.05-.05c1.01-2.01,2.04-4.02,3.08-6.02,0-1.18.3-2.26.92-3.25.41-.57.95-.95,1.63-1.14,3.23-.44,6.47-.78,9.71-1.04Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M22.83,121.38c-.05.7-.06,1.42-.05,2.14h-1.31v-1.84c.04-6.98.54-13.92,1.48-20.82.54-4.01,1.44-7.94,2.67-11.8.83-2.63,2.05-5.06,3.64-7.28,1.23-1.49,2.67-2.74,4.32-3.74,0-.15-.03-.29-.12-.41,3.43-.91,6.85-1.76,10.29-2.55,2.46,6.94,4.9,13.88,7.33,20.82h25.63c2.42-6.97,4.87-13.93,7.35-20.87,1.78.46,3.56.91,5.34,1.36,1.34-2.25,3.04-4.21,5.1-5.87.78-4.96,2.07-9.78,3.88-14.47.65-1.62,1.43-3.17,2.33-4.66.76-1.21,1.68-2.27,2.79-3.18-1.36-.17-2.34-.88-2.94-2.11-.04-.09-.06-.19-.07-.29-2.47-.68-3.87-2.31-4.2-4.85-.2-2.64-.39-5.28-.58-7.91-.03-.54,0-1.09.07-1.63-.17-1.88.57-3.25,2.23-4.13,1.68-.73,3.36-1.46,5.05-2.18.39-.11.79-.17,1.19-.17,3.64.42,7.27.88,10.9,1.38,1.72.41,2.66,1.5,2.82,3.25-.02,1.36-.63,2.38-1.8,3.06,1.1,1.14,1.33,2.44.7,3.91-.33.64-.82,1.14-1.43,1.5,1.22,1.38,1.34,2.85.36,4.42-.31.42-.69.75-1.14,1,1.02,1.05,1.29,2.27.8,3.66-.77,1.59-2.04,2.3-3.81,2.11-.7-.09-1.39-.17-2.09-.24,1.17,1.13,2.15,2.4,2.94,3.81,1.95,3.61,3.36,7.43,4.22,11.46,2.2.83,4.31,1.85,6.33,3.03.89.53,1.66,1.2,2.31,2.01.7,1.3,1.09,2.69,1.17,4.17.08,2.03-.09,4.03-.53,6.02-.48,2.16-1.04,4.3-1.7,6.41-.79,2.37-1.56,4.75-2.33,7.14-.74.36-1.49.39-2.26.07-1.22-.53-2.31-1.25-3.28-2.16-1.78,5.28-4.16,10.26-7.14,14.95-.02.04-.03.09-.02.15,3.62.73,6.54,2.56,8.76,5.49,1.2,1.7,1.84,3.59,1.92,5.68,0,.23-.01.45-.02.68-.42.42-.93.64-1.53.66-1.25.03-2.48-.12-3.69-.44-2.04-.52-4.08-1.05-6.12-1.6-.88-.23-1.78-.37-2.69-.41-.84.03-1.68.16-2.5.36-1.96.52-3.91,1.04-5.87,1.55-.95.21-1.9.39-2.86.53-.49.03-.97.03-1.46,0-.49-.08-.9-.3-1.24-.66-.08-2.31.54-4.41,1.84-6.31,1.21-1.71,2.74-3.06,4.59-4.05.75-.38,1.51-.72,2.28-1.04-2.93-4.67-5.04-9.68-6.33-15.05-.58-2.67-.91-5.37-.97-8.11-.39.24-.79.48-1.19.7-.06.04-.1.1-.12.17-1.41,3.89-2.79,7.79-4.15,11.7h1.02c1.11,12.83,2.22,25.66,3.35,38.49h-56.89c1.1-12.83,2.22-25.66,3.35-38.49.39.01.78,0,1.17-.05-1.95-5.48-3.88-10.97-5.8-16.46-.03-.04-.08-.05-.12-.02-1.95,1.22-3.53,2.82-4.73,4.78-1.06,1.86-1.92,3.82-2.57,5.87-.84,2.72-1.51,5.49-1.99,8.3-.9,5.53-1.47,11.1-1.7,16.7-.09,2.12-.15,4.24-.17,6.36Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M60.99,25.7c4.24-.18,8.43.18,12.57,1.09,2.09.5,4.11,1.17,6.07,2.04,2.05.9,3.86,2.16,5.41,3.76.3.38.58.77.85,1.17-1.92-1.08-3.96-1.91-6.12-2.5-4.32-1.11-8.7-1.74-13.15-1.89-5.41-.23-10.78.09-16.12.97-2.72.53-5.36,1.34-7.91,2.43-.62.33-1.24.65-1.84.97.76-1.17,1.71-2.16,2.86-2.96,2.19-1.5,4.57-2.61,7.14-3.35,3.35-.98,6.76-1.56,10.24-1.72Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M103.75,26.28c1.16-.16,2.11.22,2.84,1.12.64,1.04.61,2.06-.1,3.06-.2.24-.44.44-.7.61-1.53.69-3.07,1.37-4.61,2.04-.38.15-.77.28-1.17.39-.11.09-.19.19-.27.32,0,.77.24,1.45.73,2.04.29.28.59.53.9.78-1.35,1.23-1.62,2.67-.8,4.32.28.46.65.84,1.09,1.14-.75.57-1.19,1.32-1.31,2.26-1.73-.68-2.64-1.96-2.74-3.83-.19-2.49-.37-4.98-.53-7.48.06-.89.08-1.78.05-2.67.18-.77.61-1.36,1.29-1.77,1.78-.79,3.56-1.55,5.34-2.31Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M107.73,26.67c2.3.3,4.59.6,6.89.9,1.21.16,1.87.84,1.99,2.04-.12,1.31-.83,2-2.16,2.06-2.2-.25-4.39-.54-6.58-.87.52-1.02.63-2.09.32-3.2-.13-.33-.28-.63-.46-.92Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M51.08,48.56c-.66-.05-1.32-.06-1.99-.05v-6.02c1.29-1.06,2.2-2.39,2.74-3.98.79-2.34,1.25-4.76,1.38-7.23,6.35-.8,12.71-.84,19.08-.12.66.1,1.33.2,1.99.29.15,1.96.45,3.89.9,5.8.37,1.45.98,2.79,1.8,4.03.23.32.49.61.75.9.25.22.52.42.8.61.02,1.91.05,3.82.07,5.73-.65,0-1.3,0-1.94.02-1.31,1.17-2.84,1.72-4.61,1.65-.6,0-1.11-.24-1.5-.68-4.45-.03-8.9-.03-13.35,0-.2.29-.48.47-.83.53-2.01.37-3.77-.12-5.29-1.48Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M51.62,31.57h.19v.29c-.15,2.42-.67,4.75-1.58,6.99-.28.64-.65,1.22-1.09,1.75-.05-2.84-.06-5.69-.05-8.54.83-.19,1.67-.35,2.52-.49Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M75.7,31.77c.93.14,1.85.32,2.77.53,0,2.88,0,5.76-.02,8.64-.59-.73-1.06-1.54-1.41-2.43-.77-2.18-1.21-4.43-1.33-6.75Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M106.67,32.06c2.43.31,4.85.63,7.28.95,1.17.17,1.82.84,1.94,2.01-.13,1.26-.82,1.96-2.09,2.09-3.63-.46-7.25-.92-10.87-1.38-.76-.11-1.33-.5-1.7-1.17,1.57-.72,3.16-1.42,4.76-2.09.25-.1.48-.24.68-.41Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M47.59,32.45c.06.5.1,1.02.1,1.55s-.01,1.04-.05,1.55c-1.54-.26-2.47.37-2.79,1.89-.05.4-.07.81-.07,1.21.04,1.09.13,2.17.24,3.25-.01.06-.03.13-.05.19-1.51-.5-2.9-1.22-4.17-2.16-1.83-1.54-1.81-3.06.05-4.56,1.6-1.13,3.35-1.97,5.24-2.52.5-.14,1-.28,1.5-.41Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M80.02,32.74c1.93.51,3.72,1.32,5.39,2.4.65.47,1.17,1.04,1.58,1.72.26.66.21,1.29-.15,1.89-.26.41-.58.77-.95,1.09-.99.74-2.05,1.35-3.2,1.82-.01-.07-.03-.15-.05-.22.14-1.25.2-2.5.17-3.76-.23-1.67-1.18-2.38-2.84-2.14-.01-.95,0-1.88.05-2.82Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M46.76,36.82c.28-.06.5.02.66.24.11.21.19.44.24.68.03,3.02.03,6.05,0,9.08-.02.32-.12.61-.29.87-.2.21-.36.17-.49-.1-.08-.16-.15-.32-.19-.49,0-1.69-.11-3.37-.34-5.05-.07-.92-.14-1.84-.19-2.77-.03-.52-.03-1.03,0-1.55.03-.43.24-.74.61-.92Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M80.4,36.82c.54-.08.87.15,1,.68.05.39.08.78.07,1.17-.12,2.11-.29,4.21-.51,6.31-.01.69-.03,1.39-.05,2.09-.31,1.03-.61,1.03-.92,0-.03-3.14-.03-6.28,0-9.42.04-.33.18-.6.41-.83Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M103.12,37.2c.55,0,1.1.03,1.65.12,3,.38,5.99.79,8.98,1.21,1.03.45,1.48,1.23,1.33,2.35-.34,1.04-1.06,1.57-2.16,1.6-3.32-.39-6.64-.83-9.95-1.29-1.32-.53-1.76-1.48-1.33-2.84.34-.58.84-.97,1.48-1.17Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M55.6,39.73c.69-.09,1.19.19,1.48.83.11,1.07-.36,1.6-1.43,1.58-.75-.26-1.05-.79-.9-1.58.16-.41.44-.69.85-.83Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M71.38,39.73c1.1-.05,1.6.46,1.48,1.55-.26.65-.73.93-1.43.85-.72-.26-1.01-.77-.9-1.53.16-.41.45-.7.85-.87Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M103.36,42.74c.28,0,.55,0,.83.02,2.9.37,5.8.76,8.69,1.17,1.14.43,1.61,1.25,1.43,2.45-.36,1.01-1.08,1.53-2.16,1.55-2.95-.37-5.89-.76-8.83-1.14-1.35-.44-1.86-1.35-1.53-2.74.33-.68.85-1.12,1.58-1.31Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M105.6,48.71c.77-.03,1.48.16,2.14.56,1.03.7,1.89,1.57,2.6,2.6,1.44,2.18,2.58,4.51,3.45,6.99.51,1.49.98,3,1.38,4.51-1.76,1.45-3.78,2.26-6.07,2.45-3.98.14-7.17-1.35-9.59-4.49-.36-.52-.68-1.08-.97-1.65.8-2.72,1.93-5.29,3.4-7.72.5-.78,1.07-1.5,1.72-2.16.56-.53,1.21-.89,1.94-1.09Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M48.95,49.87c.55,0,1.1,0,1.65.02,1.75,1.37,3.72,1.87,5.92,1.5.46-.12.88-.31,1.26-.58,4.06-.03,8.12-.03,12.18,0,.52.39,1.1.62,1.75.68,1.66.14,3.21-.2,4.66-1.02.28-.17.53-.36.78-.58.52-.02,1.03-.03,1.55-.02-.09,1.5-.48,2.9-1.19,4.22-.62,2.83-1.46,5.6-2.52,8.3-.2.41-.41.82-.63,1.21-.76-.1-1.48.04-2.16.41-.31.19-.6.4-.87.63-.83.87-1.66,1.73-2.52,2.57-.28.23-.58.42-.92.56-.21-.14-.41-.31-.58-.51-.8-.47-1.66-.69-2.6-.66-1.14.03-2.25.23-3.33.61-.29.12-.56.25-.83.41-1.09-1.47-2.45-2.61-4.08-3.42-.96-.41-1.96-.59-3.01-.53-.3-.48-.56-.97-.8-1.48-1.02-2.64-1.84-5.34-2.48-8.11-.69-1.33-1.11-2.73-1.24-4.22Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M56.08,52.16h15.63c.1,3.78-1.57,6.45-5,7.99-3.43,1.14-6.36.38-8.81-2.26-1.34-1.67-1.95-3.58-1.82-5.73Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M57.44,53.52h12.82c-.34,2.61-1.73,4.42-4.17,5.41-2.78.86-5.16.23-7.16-1.87-.87-1.02-1.36-2.2-1.48-3.54Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M108.07,57.98c.73-.04,1.2.28,1.43.97.07.73-.25,1.2-.95,1.43-.78.06-1.25-.28-1.43-1.04-.02-.68.3-1.14.95-1.36Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M97.93,61.43c2.16,3.27,5.21,5.17,9.13,5.7,3.08.26,5.88-.5,8.4-2.26,1.31,5.5,1.83,11.09,1.58,16.75-.43,4.08-1.4,8.03-2.91,11.84-1.9,4.73-4.25,9.21-7.04,13.45-.02.04-.03.09-.02.15,2.96.22,5.6,1.25,7.91,3.08,2.18,1.83,3.39,4.17,3.64,7.01-.91.1-1.82.04-2.72-.17-2.26-.54-4.51-1.13-6.75-1.75-1.06-.25-2.14-.42-3.23-.51-.95.04-1.87.18-2.79.41-2.31.61-4.63,1.2-6.94,1.8-.49.09-.97.17-1.46.24-.48.04-.96.03-1.43-.02.05-1.6.51-3.07,1.36-4.42,1.47-2.19,3.43-3.77,5.9-4.73.72-.26,1.45-.49,2.18-.68.02-.02.04-.04.05-.07-3.76-5.59-6.28-11.71-7.55-18.35-.46-2.83-.61-5.68-.44-8.54.33-6.44,1.37-12.75,3.13-18.93Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M117.1,65.84c1.84.71,3.6,1.58,5.29,2.6.69.4,1.3.91,1.82,1.53.56,1.06.89,2.19.97,3.4.07,1.36,0,2.72-.19,4.08-.41,2.46-1,4.89-1.75,7.28-.77,2.41-1.54,4.82-2.31,7.23-.27.02-.53-.02-.78-.12-1.2-.58-2.27-1.33-3.23-2.26.18-.88.39-1.75.63-2.62.85-3.74,1.13-7.53.83-11.36-.18-3.29-.62-6.54-1.29-9.76Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M74.34,66.33h.24c.19,1.79.56,3.53,1.09,5.24.11.25.22.5.32.75-.36.23-.74.44-1.14.61-.17-.24-.3-.5-.39-.78-.63-1.84-1-3.73-1.14-5.66.34-.05.68-.11,1.02-.17Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M53.32,66.43c.44.04.87.09,1.31.15-.18,1.61-.48,3.19-.9,4.76-.21.64-.46,1.25-.75,1.84-.4-.18-.79-.4-1.17-.63.42-.98.76-1.98,1-3.01.2-1.03.37-2.07.51-3.11Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M94.09,72.59s.05.1.05.17c-.44,2.97-.69,5.96-.75,8.96-1.2.85-2.49,1.55-3.86,2.11-.23.09-.48.15-.73.17-.14-1.48.05-2.92.56-4.32.83-2.16,2.02-4.1,3.54-5.83.39-.43.79-.85,1.19-1.26Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M47.25,75.84h1.31c-.01.11,0,.2.05.29.07,1.56.51,3,1.33,4.32,1.4,2.09,3.23,3.67,5.51,4.73,4.67,2.1,9.46,2.42,14.37.97,2.59-.78,4.83-2.11,6.72-4,1.37-1.45,2.23-3.16,2.57-5.15.04-.39.07-.78.07-1.17h1.36c-.09,2.63-1,4.93-2.74,6.89-2.24,2.39-4.95,4.01-8.13,4.88-4.65,1.22-9.21.98-13.69-.73-2.73-1.09-4.99-2.79-6.77-5.12-1.26-1.77-1.92-3.74-1.97-5.92Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M42.78,76.62s.09,0,.12.05c3.03,8.57,6.04,17.15,9.03,25.73.06,1.62-.66,2.74-2.16,3.37-1.72.65-3.31.43-4.76-.68-.38-.33-.66-.72-.85-1.19-2.97-8.44-5.93-16.88-8.91-25.31.02-.04.05-.08.1-.1,2.49-.59,4.97-1.21,7.43-1.87Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M84.92,76.62c1.28.33,2.55.66,3.83.97-.54,1.17-.93,2.38-1.19,3.64-.23,1.22-.22,2.45.02,3.66.28.32.63.48,1.07.46.57-.04,1.12-.17,1.65-.39.01.02.03.05.05.07-2.3,6.42-4.6,12.83-6.92,19.25-.78,1.11-1.85,1.72-3.23,1.82-1.5.11-2.75-.38-3.76-1.48-.56-.74-.74-1.57-.53-2.48,2.99-8.52,5.99-17.03,9-25.53Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M51.57,97.25c8.22-.03,16.42,0,24.61.1-.56,1.55-1.1,3.1-1.63,4.66-.25,1.9.4,3.39,1.97,4.49,1.5.93,3.13,1.19,4.85.78,1.23-.34,2.25-1.01,3.03-2.01.2-.29.36-.59.49-.92.85-2.36,1.68-4.72,2.5-7.09h.34c1.03,11.84,2.05,23.69,3.06,35.53v.24h-53.88v-.24c1-11.84,2.02-23.69,3.06-35.53.16-.01.31,0,.46.05.84,2.39,1.68,4.79,2.52,7.18.53,1.13,1.36,1.95,2.5,2.45,1.63.67,3.26.68,4.9.05,2.14-.96,3.1-2.6,2.89-4.93-.53-1.61-1.09-3.21-1.67-4.81Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M47.59,100.16c1.54-.14,2.53.52,2.99,1.99.13,1.48-.51,2.45-1.92,2.89-1.13.17-2-.21-2.65-1.14-.64-1.3-.41-2.41.7-3.33.28-.18.57-.32.87-.41Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M79.14,100.16c1.43-.15,2.4.45,2.89,1.8.26,1.42-.27,2.41-1.58,2.99-1.51.37-2.57-.16-3.18-1.58-.31-1.63.31-2.69,1.87-3.2Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M52.01,106.13h23.69c0,6.7,0,13.4-.02,20.1-.32,2.21-1.54,3.66-3.66,4.34-.28.04-.55.09-.83.15-4.92.03-9.84.03-14.76,0-2.51-.47-3.98-1.97-4.39-4.49-.02-6.7-.03-13.4-.02-20.1Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),n.jsx("path",{d:"M74.34,107.49c0,6.25,0,12.49-.02,18.74-.33,1.73-1.35,2.78-3.08,3.13-4.94.03-9.87.03-14.81,0-1.9-.43-2.92-1.62-3.06-3.57v-18.3h20.97Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"})]})}),n.jsx("p",{className:"text-foreground",children:"Holy mackerel! You are up to date with all your maintenance. Only thing left to do is, to go fishing"})]}),n.jsx("div",{className:"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center",children:(0,n.jsxs)(o.default,{href:"/maintenance",className:"text-accent-foreground uppercase group hover:text-curious-blue-400 text-xs",children:["See all"," ",n.jsx("span",{className:"hidden group-hover:text-curious-blue-400 md:inline-block",children:"\xa0maintenance\xa0"})," ","tasks"]})})]})]})}var tN=r(29428),tA=r(94970),tD=(0,p.z)({chartName:"BarChart",GraphicalChild:tA.$,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:eP},{axisType:"yAxis",AxisComp:eL}],formatAxisMap:et.t9});function tP({chartConfig:e,chartData:t,cardTitle:r,cardInfo:a}){return(0,n.jsxs)("div",{className:"flex flex-col min-h-0 overflow-auto shadow-none max-h-80",children:[(0,n.jsxs)("div",{children:[n.jsx(eX.P,{children:r}),a]}),n.jsx(h.BO,{config:e,className:"flex flex-col overflow-auto size-full",children:(0,n.jsxs)(tD,{accessibilityLayer:!0,data:t,layout:"vertical",margin:{left:33,right:10},barCategoryGap:"20%",children:[n.jsx(eL,{dataKey:"title",type:"category",tick:{fontSize:14,fill:"#71717A"},tickLine:!1,tickMargin:5,axisLine:{stroke:"#CBD3D6",strokeDasharray:"4 2"},tickFormatter:t=>e[t]?.label}),n.jsx(eP,{dataKey:"amount",type:"number",hide:!0,padding:{left:10}}),n.jsx(h.h7,{cursor:!1,content:n.jsx(h.dg,{hideLabel:!0,hideIndicator:!0,className:"chartToolTip min-w-0",nameKey:"title"})}),n.jsx(tA.$,{dataKey:"amount",layout:"vertical",radius:5})]})})]})}var tT=r(93791);let tM=(0,eG.ZP)`
    query ReadTrainingSessionDues(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingSessionDueFilterFields = {}
    ) {
        readTrainingSessionDues(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: {
                dueDate: ASC
                trainingTypeID: ASC
                vesselID: ASC
                memberID: ASC
            }
        ) {
            nodes {
                id
                dueDate
                vesselID
                vessel {
                    id
                    title
                }
                trainingTypeID
                trainingType {
                    id
                    title
                }
                member {
                    id
                    firstName
                    surname
                }
                trainingSession {
                    trainingLocationType
                }
            }
        }
    }
`,tE=(0,eG.ZP)`
    query ReadTrainingSessions(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingSessionFilterFields = {}
    ) {
        readTrainingSessions(limit: $limit, offset: $offset, filter: $filter) {
            nodes {
                id
                date
            }
        }
    }
`;function tR(){let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)([]),[s,d]=(0,a.useState)(!0),[u,h]=(0,a.useState)(!0),[p,f]=(0,a.useState)([]),[m,v]=(0,a.useState)([]),y=(0,c.wu)([{accessorKey:"title",header:"",cell:({row:e})=>{let t=e.original;return n.jsx("div",{className:"flex items-center h-full",children:(0,n.jsxs)("div",{className:"grid",children:[n.jsx("span",{className:"font-medium truncate hover:text-primary",children:t.vessel.title||t.trainingLocationType}),n.jsx("span",{className:"text-[10px] text-curious-blue-400 uppercase",children:t.trainingType.title},t.trainingType.id)]})})}},{accessorKey:"due",header:"",cellAlignment:"right",cell:({row:e})=>{let t=new Date(e.original.dueDate),r=new Date;t.setHours(0,0,0,0),r.setHours(0,0,0,0);let a=Math.floor((r.getTime()-t.getTime())/864e5);return n.jsx("div",{className:"flex justify-end items-end text-nowrap",children:a>0?(0,n.jsxs)("div",{className:"alert border rounded-md",children:[a+" ","days ago"]}):(0,n.jsxs)("div",{className:"text-foreground",children:["Due -"," "+-1*a+" ","days"]})})}}]),[g]=(0,l.t)(tM,{fetchPolicy:"cache-and-network",onCompleted:e=>{let r=e.readTrainingSessionDues.nodes;if(r){let e=r.map(e=>({...e,status:(0,e9.nu)(e)})),n=Object.values(e.reduce((e,t)=>{let r=`${t.vesselID}-${t.trainingTypeID}-${t.dueDate}`;return e[r]||(e[r]={id:t.id,vesselID:t.vesselID,vessel:t.vessel,trainingTypeID:t.trainingTypeID,trainingType:t.trainingType,dueDate:t.dueDate,status:t.status,trainingLocationType:t.trainingSession.trainingLocationType,members:[]}),e[r].members.push(t.member),e},{})).map(e=>{let t=e.members.reduce((e,t)=>{let r=e.find(e=>e.id===t.id);return r?(r.firstName=t.firstName,r.surname=t.surname):e.push(t),e},[]);return{id:e.id,vesselID:e.vesselID,vessel:e.vessel,trainingTypeID:e.trainingTypeID,trainingType:e.trainingType,status:e.status,dueDate:e.dueDate,trainingLocationType:e.trainingLocationType,members:t}}),a=e.filter(e=>e.status.isOverdue);t(n.splice(0,5)),i(a)}},onError:e=>{console.error("readTrainingSessionDues error",e)}}),x=async e=>{let t={};e.vesselID&&(t.vesselID=e.vesselID),e.trainingTypes&&(t.trainingTypeID={eq:e.trainingTypes.id.contains}),e.members&&(t.memberID={eq:e.members.id.contains}),e.date?t.dueDate=e.date:t.dueDate={ne:null},await g({variables:{filter:t}});let r=new Date,n=new Date(r.getFullYear(),r.getMonth(),1).toISOString(),a=new Date(r.getFullYear(),r.getMonth()+1,1).toISOString();await b({variables:{limit:1e3,offset:0,filter:{date:{gte:n,lt:a}}}})},[b]=(0,l.t)(tE,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTrainingSessions.nodes;if(t){let e=new Date;f(t.filter(t=>new Date(t.date)<e)),v(t)}},onError:e=>{console.error("queryTrainingList error",e)}}),j=async(e=0,t={...k})=>{await b({variables:{filter:t,offset:100*e,limit:100}})},{filter:k,setFilter:w,handleFilterChange:S}=(0,tT.q)({initialFilter:{},loadList:j,loadDues:x,toggleOverdue:h}),O=[{title:"scheduled",amount:e.filter(e=>e.dueDate>new Date().toISOString()).length,fill:"var(--color-scheduled)",stroke:"hsl(207, 86%, 39%)"},{title:"completed",amount:p.length,fill:"var(--color-completed)",stroke:"hsl(176, 97%, 26%)"},{title:"overdue",amount:e.filter(e=>e.dueDate<new Date().toISOString()).length,fill:"var(--color-overdue)",stroke:"hsl(1, 83%, 54%)"}];return(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex py-3 items-baseline gap;2 phablet:gap-4",children:[n.jsx(tN._,{className:"h-12 w-12 ring-1 p-1 rounded-full"}),n.jsx(o.default,{href:"/crew-training",children:n.jsx(e2.H1,{children:"Training / drills"})})]}),n.jsx(tP,{chartConfig:{amount:{label:"Amount"},scheduled:{label:"Scheduled",color:"var(--chart-3)"},completed:{label:"Completed",color:"var(--chart-5)"},overdue:{label:"Overdue",color:"var(--chart-1)"}},chartData:O,cardTitle:"Training completed this month",cardInfo:(0,n.jsxs)(n.Fragment,{children:[n.jsx("h2",{className:"text-5xl font-black mb-1",children:0===e.length?"0%":(p.length/(m.length+e.length)*100).toFixed(2)+"%"}),(0,n.jsxs)("span",{className:"text-curious-blue-400 text-sm",children:[m.length+e.length," ","training / drills due this month"]})]})}),(0,n.jsxs)("div",{className:"space-y-5",children:[e&&e.length>0?n.jsx(c.wQ,{columns:y,data:e,showToolbar:!1,className:"p-0 pt-8 border-0 shadow-none"}):(0,n.jsxs)("div",{className:"flex-1 flex justify-between items-center gap-2 p-2 pt-4",children:[n.jsx("div",{children:(0,n.jsxs)("svg",{className:"!w-[75px] h-auto",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 147 147.01",children:[n.jsx("path",{d:"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z",fill:"#ffffff",strokeWidth:"0px"}),n.jsx("path",{d:"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z",fill:"#052350",fillRule:"evenodd",opacity:".97",strokeWidth:"0px"}),n.jsx("path",{d:"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z",fill:"#2998e9",strokeWidth:"0px"}),n.jsx("path",{d:"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z",fill:"#2998e9",strokeWidth:"0px"}),n.jsx("path",{d:"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z",fill:"#024450",strokeWidth:"0px"}),n.jsx("path",{d:"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z",fill:"#052451",strokeWidth:"0px"}),n.jsx("path",{d:"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z",fill:"#052250",strokeWidth:"0px"}),n.jsx("path",{d:"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z",fill:"#2998e9",strokeWidth:"0px"}),n.jsx("path",{d:"M45.34,125.8h23.84v6.63h-23.84v-6.63Z",fill:"#052350",strokeWidth:"0"}),n.jsx("path",{d:"M70.17,125.8h6.58v6.63h-6.58v-6.63Z",fill:"#052250",strokeWidth:"0"}),n.jsx("path",{d:"M77.77,125.8h23.84v6.63h-23.84v-6.63Z",fill:"#052350",strokeWidth:"0"}),n.jsx("path",{d:"M67.98,127.01v4.2h-21.42v-4.2h21.42Z",fill:"#2a99ea",strokeWidth:"0"}),n.jsx("path",{d:"M75.58,127.01v4.2h-4.2v-4.2h4.2Z",fill:"#2a99ea",strokeWidth:"0"}),n.jsx("path",{d:"M78.99,127.01h21.42v4.2h-21.42v-4.2Z",fill:"#2a99ea",strokeWidth:"0"}),n.jsx("path",{d:"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z",fill:"#ffffff",strokeWidth:"0"})]})}),n.jsx("p",{className:"text-foreground",children:"WOW! Look at that. You are ship-shaped and trained to the gills. Great job!"})]}),n.jsx("div",{className:"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center",children:(0,n.jsxs)(o.default,{href:"/crew-training",className:"text-accent-foreground uppercase group hover:text-curious-blue-400 text-xs",children:["See all"," ",n.jsx("span",{className:"hidden md:inline-block group-hover:text-curious-blue-400",children:"\xa0crew\xa0"})," ","training"]})})]})]})}var tI=r(7626);function tZ(){return(0,n.jsxs)(eX.Zb,{className:"size-full",children:[n.jsx(eX.aY,{children:n.jsx(tI.Z,{initialView:"listWeek",isDashboard:!0})}),n.jsx(eX.eW,{className:"p-0",children:n.jsx(eX.zx,{className:"w-full",asChild:!0,children:n.jsx(o.default,{href:"/calendar",children:"View Full Calendar"})})})]})}var tF=r(47520);let t_=(0,eG.ZP)`
    query ReadDashboardData($archived: Int = 0) {
        readDashboardData(archived: $archived) {
            vessels {
                id
                showOnDashboard
                title
                icon
                iconMode
                photoID
                vesselPosition {
                    id
                    lat
                    long
                    geoLocation {
                        id
                        title
                        lat
                        long
                    }
                }
            }
        }
    }
`,tL=(0,eG.ZP)`
    query ReadVessels(
        $vesselFilter: VesselFilterFields = {}
        $statusFilter: VesselStatusFilterFields = {}
    ) {
        readVessels(filter: $vesselFilter) {
            nodes {
                id
                title
                archived
                showOnDashboard
                statusHistory(
                    filter: $statusFilter
                    sort: { date: DESC, created: DESC }
                    limit: 1
                ) {
                    nodes {
                        id
                        date
                        status
                        created
                        comment
                        reason
                        otherReason
                        expectedReturn
                    }
                }
            }
        }
    }
`,t$=(0,tF.default)(async()=>{},{loadableGenerated:{modules:["app\\ui\\dashboard\\overview-components\\map\\map.tsx -> @/components/full-map"]},ssr:!1});function tV(){let[e,t]=(0,a.useState)(!0),[r,i]=(0,a.useState)([]),[s,o]=(0,a.useState)([]),[c,d]=(0,a.useState)(!1),[u]=(0,l.t)(tL,{fetchPolicy:"cache-and-network",onError:e=>{console.error("queryLogBookEntrySections error",e)}}),[h]=(0,l.t)(t_,{fetchPolicy:"no-cache",onCompleted:async e=>{e.readDashboardData},onError:e=>{console.error("queryVessels error",e)}});return n.jsx(n.Fragment,{children:r&&r.length?n.jsx(t$,{className:"h-[100cvh]",vessels:r}):n.jsx(eX.Od,{className:"size-full"})})}function tW(e){return(0,n.jsxs)("div",{className:"w-full flex flex-col gap-8 overflow-hidden relative",children:[(0,n.jsxs)("div",{className:"grid grid-col-1 lg:grid-cols-3 gap-8 lg:gap-6 xl:gap-8",children:[n.jsx(tj.Zb,{children:n.jsx(e6,{})}),n.jsx(tj.Zb,{children:n.jsx(tC,{})}),n.jsx(tj.Zb,{children:n.jsx(tR,{})})]}),(0,n.jsxs)("div",{className:"w-full grid lg:grid-cols-6 gap-y-5 lg:gap-6 xl:gap-8 pb-10",children:[n.jsx("div",{className:"lg:col-span-2",children:n.jsx(tZ,{})}),n.jsx("div",{className:"lg:col-span-4 h-full min-h-[400px]",children:n.jsx(tV,{})})]})]})}var tq=r(15806),tB=r(74389),tz=r(36895);function tH(){let[e,t]=(0,a.useState)([]);return(0,n.jsxs)(tz.Tabs,{defaultValue:"overview",children:[(0,n.jsxs)(tz.TabsList,{children:[n.jsx(tz.TabsTrigger,{value:"overview",children:"Overview"}),n.jsx(tz.TabsTrigger,{value:"reports",children:"Reports"}),n.jsx(tz.TabsTrigger,{value:"notifications",children:"Notifications"})]}),n.jsx(tz.TabsContent,{value:"overview",children:n.jsx(tW,{})}),n.jsx(tz.TabsContent,{value:"reports",children:n.jsx(tB.Z,{})}),n.jsx(tz.TabsContent,{value:"notifications",children:n.jsx(tq.Z,{})})]})}let tK=e=>{let[t,r]=(0,a.useState)(!0),[i,o]=(0,a.useState)("dashboard"),[l,c]=(0,a.useState)("");return(0,a.useEffect)(()=>{t&&(c(localStorage.getItem("clientTitle")||""),r(!1))},[t]),(0,n.jsxs)(n.Fragment,{children:["dashboard"===i&&n.jsx("div",{className:"block w-full",children:n.jsx(tH,{})}),(0,n.jsxs)("div",{className:"fixed bottom-2 right-2 shadow-md z-100 rounded-md p-2 bg-background text-muted-foreground",children:["v",s.i8]})]})}},93791:(e,t,r)=>{"use strict";r.d(t,{q:()=>a});var n=r(60343);function a(e){let{initialFilter:t,loadList:r,loadDues:a,toggleOverdue:i}=e,[s,o]=(0,n.useState)(t),l=(0,n.useCallback)(({type:e,data:t})=>{let n={...s};"vessel"===e&&(Array.isArray(t)&&t.length?n.vesselID={in:t.map(e=>+e.value)}:t&&!Array.isArray(t)?n.vesselID={eq:+t.value}:delete n.vesselID),"trainingType"===e&&(Array.isArray(t)&&t.length?n.trainingTypes={id:{in:t.map(e=>+e.value)}}:t&&!Array.isArray(t)?n.trainingTypes={id:{contains:+t.value}}:delete n.trainingTypes),"trainer"===e&&(Array.isArray(t)&&t.length?n.trainer={id:{in:t.map(e=>+e.value)}}:t&&!Array.isArray(t)?n.trainer={id:{eq:+t.value}}:delete n.trainer),"member"===e&&(Array.isArray(t)&&t.length?n.members={id:{in:t.map(e=>+e.value)}}:t&&!Array.isArray(t)?n.members={id:{eq:+t.value}}:delete n.members),"dateRange"===e&&(t?.startDate&&t?.endDate?n.date={gte:t.startDate,lte:t.endDate}:delete n.date),"overdue"===e&&void 0!==t&&i(e=>!t),o(n),a(n),r(0,n)},[s,a,r,i]);return{filter:s,setFilter:o,handleFilterChange:l}}},15806:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f,z:()=>p});var n=r(98768),a=r(60343),i=r(13842),s=r(66263),o=r(69424),l=r(75546),c=r(51742),d=r(25394),u=r(69748),h=r(99891);let p=e=>{if(!e)return"";let t=new Date(e),r=Math.abs(new Date().getTime()-t.getTime()),n=Math.floor(r/6e4),a=Math.floor(r/36e5),i=Math.floor(r/864e5);return 0===i?0===a?0===n?"Just now":1===n?"1 minute ago":`${n} minutes ago`:1===a?"1 hour ago":`${a} hours ago`:1===i?"Yesterday":i<=7?`${i} days ago`:(0,l.p6)(e)};function f(e){let[t,r]=(0,a.useState)();(0,o.useSearchParams)(),(0,i.__)(r);let l=e=>{let t="";return"Fitness"==e&&(t="crew"),"SafetyActions"==e&&(t="crew"),"WaterQuality"==e&&(t="crew"),"IMSafe"==e&&(t="crew"),"Safety"==e&&(t="dailyChecks"),"HighWaterAlarm"==e&&(t="dailyChecks"),"FirstAid"==e&&(t="dailyChecks"),"SafetyEquipment"==e&&(t="dailyChecks"),"FireExtinguisher"==e&&(t="dailyChecks"),"Hull"==e&&(t="dailyChecks"),"Hull_HullStructure"==e&&(t="dailyChecks"),"Hull_DeckEquipment"==e&&(t="dailyChecks"),"Hull_DayShapes"==e&&(t="dailyChecks"),"TenderOperationalChecks"==e&&(t="dailyChecks"),"Anchor"==e&&(t="dailyChecks"),"WindscreenCheck"==e&&(t="dailyChecks"),"NightLineDockLinesRelease"==e&&(t="dailyChecks"),"Propulsion"==e&&(t="dailyChecks"),"PreEngineAndPropulsion"==e&&(t="dailyChecks"),"EngineCheckPropellers"==e&&(t="dailyChecks"),"EngineOilWater"==e&&(t="dailyChecks"),"EngineMountsAndStabilisers"==e&&(t="dailyChecks"),"ElectricalChecks"==e&&(t="dailyChecks"),"ElectricalVisualFields"==e&&(t="dailyChecks"),"Generator"==e&&(t="dailyChecks"),"ShorePower"==e&&(t="dailyChecks"),"SteeringChecks"==e&&(t="dailyChecks"),"Navigation"==e&&(t="dailyChecks"),"NavigationCharts"==e&&(t="dailyChecks"),"NavigationChecks"==e&&(t="dailyChecks"),"Radio"==e&&(t="dailyChecks"),"OtherNavigation"==e&&(t="dailyChecks"),"LogBookSignOff"==e&&(t="signOff"),"Review"==e&&(t="signOff"),"SafetyEquipmentCheck"==e&&(t="signOff"),"ForecastAccuracy"==e&&(t="signOff"),"Power"==e&&(t="signOff"),"BatteryMaintenance"==e&&(t="signOff"),"CircuitInspections"==e&&(t="signOff"),"MooringAndAnchoring"==e&&(t="signOff"),"CargoAndAccessEquipment"==e&&(t="signOff"),"HatchesAndWatertightDoors"==e&&(t="signOff"),"GalleyAppliances"==e&&(t="signOff"),"WasteManagement"==e&&(t="signOff"),"VentilationAndAirConditioning"==e&&(t="signOff"),"EmergencyReadiness"==e&&(t="signOff"),"EnvironmentalCompliance"==e&&(t="signOff"),t},f=e=>{let t="";return"DailyCheckFuel"==e&&(t="Engine Checks"),"DailyCheckEngine"==e&&(t="Engine Checks"),"Safety"==e&&(t="Safety Checks"),"HighWaterAlarm"==e&&(t="Safety Checks"),"FirstAid"==e&&(t="Safety Checks"),"SafetyEquipment"==e&&(t="Safety Checks"),"FireExtinguisher"==e&&(t="Safety Checks"),"Hull"==e&&(t="Deck operations and exterior checks"),"Hull_HullStructure"==e&&(t="Deck operations and exterior checks"),"Hull_DeckEquipment"==e&&(t="Deck operations and exterior checks"),"Hull_DayShapes"==e&&(t="Deck operations and exterior checks"),"TenderOperationalChecks"==e&&(t="Deck operations and exterior checks"),"Anchor"==e&&(t="Deck operations and exterior checks"),"WindscreenCheck"==e&&(t="Deck operations and exterior checks"),"NightLineDockLinesRelease"==e&&(t="Deck operations and exterior checks"),"Propulsion"==e&&(t="Engine Checks"),"PreEngineAndPropulsion"==e&&(t="Engine Checks"),"EngineCheckPropellers"==e&&(t="Engine Checks"),"EngineOilWater"==e&&(t="Engine Checks"),"EngineMountsAndStabilisers"==e&&(t="Engine Checks"),"ElectricalChecks"==e&&(t="Engine Checks"),"ElectricalVisualFields"==e&&(t="Engine Checks"),"Generator"==e&&(t="Engine Checks"),"ShorePower"==e&&(t="Engine Checks"),"SteeringChecks"==e&&(t="Engine Checks"),"Navigation"==e&&(t="Navigation"),"NavigationCharts"==e&&(t="Navigation"),"NavigationChecks"==e&&(t="Navigation"),"Radio"==e&&(t="Navigation"),"OtherNavigation"==e&&(t="Navigation"),t},m=(0,c.wu)([{accessorKey:"title",header:"Comment",cellAlignment:"left",cell:({row:e})=>{let t=e.original;return(0,n.jsxs)("div",{className:"space-y-1 py-2.5",children:[(0,n.jsxs)("div",{className:"flex items-start justify-between",children:[(0,n.jsxs)("span",{className:"flex flex-col items-center sm:flex-row gap-x-2.5",children:[n.jsx(s.default,{href:`/log-entries?vesselID=${t.logBookEntry.vehicleID}&logentryID=${t.logBookEntry.id}&firstTab=${l(t.fieldName)}&secondTab=${f(t.fieldName)}`,children:n.jsx("div",{className:"hover:underline",children:t.commentType})}),t.logBookEntry.vehicle?.title&&(0,n.jsxs)(n.Fragment,{children:[n.jsx("span",{className:"hidden sm:block",children:"-"}),(0,n.jsxs)("div",{className:"inline-flex items-center gap-2 text-nowrap text-sm",children:[n.jsx("div",{className:"size-5 flex items-center justify-center flex-shrink-0 [&_img]:!size-5 [&_svg]:!size-5",children:n.jsx(h.Z,{vessel:t.logBookEntry.vehicle})}),n.jsx("span",{children:t.logBookEntry.vehicle.title})]})]})]}),n.jsx(s.default,{href:`/log-entries?vesselID=${t.logBookEntry.vehicleID}&logentryID=${t.logBookEntry.id}&firstTab=${l(t.fieldName)}&secondTab=${f(t.fieldName)}`,children:p(t.lastEdited)})]}),n.jsx(d.P,{children:t.comment})]})}},{accessorKey:"author",header:"Author",cellAlignment:"center",cell:({row:e})=>{let t=e.original,r=`${t?.seaLogsMember?.firstName||""} ${t?.seaLogsMember?.surname||""}`.trim();return r&&t?.seaLogsMember?.firstName!==null?n.jsx("div",{className:"flex-1 flex justify-center",children:(0,n.jsxs)(d.u,{children:[n.jsx(d.aJ,{asChild:!0,children:(0,n.jsxs)(s.default,{className:"w-fit flex justify-center items-center group/crew gap-2.5",href:`/log-entries?vesselID=${t.logBookEntry.vehicleID}&logentryID=${t.logBookEntry.id}&firstTab=${l(t.fieldName)}&secondTab=${f(t.fieldName)}`,children:[n.jsx(u.Avatar,{size:"sm",variant:"secondary",children:n.jsx(u.AvatarFallback,{className:"text-sm",children:(0,u.getCrewInitials)(t?.seaLogsMember?.firstName,t?.seaLogsMember?.surname)})}),n.jsx("div",{className:"hidden lg:block group-hover/crew:underline",children:r||"Unknown Author"})]})}),n.jsx(d._v,{className:"lg:hidden",children:r||"Unknown Author"})]})}):null}}]);return(0,n.jsxs)(n.Fragment,{children:[n.jsx("div",{className:"h-full flex justify-between items-center p-2",children:n.jsx(d.H2,{children:"Notifications"})}),t&&n.jsx(c.wQ,{columns:m,data:t,showToolbar:!1,pageSize:20})]})}},74389:(e,t,r)=>{"use strict";r.d(t,{Z:()=>b});var n=r(98768),a=r(69424),i=r(53363),s=r(97428);let o=(0,s.Z)("Fuel",[["line",{x1:"3",x2:"15",y1:"22",y2:"22",key:"xegly4"}],["line",{x1:"4",x2:"14",y1:"9",y2:"9",key:"xcnuvu"}],["path",{d:"M14 22V4a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v18",key:"16j0yd"}],["path",{d:"M14 13h2a2 2 0 0 1 2 2v2a2 2 0 0 0 2 2a2 2 0 0 0 2-2V9.83a2 2 0 0 0-.59-1.42L18 5",key:"7cu91f"}]]),l=(0,s.Z)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var c=r(72997);let d=(0,s.Z)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]),u=(0,s.Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),h=(0,s.Z)("Gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]]),p=(0,s.Z)("ListChecks",[["path",{d:"m3 17 2 2 4-4",key:"1jhpwq"}],["path",{d:"m3 7 2 2 4-4",key:"1obspn"}],["path",{d:"M13 6h8",key:"15sg57"}],["path",{d:"M13 12h8",key:"h98zly"}],["path",{d:"M13 18h8",key:"oe0vm4"}]]);var f=r(70996);let m=(0,s.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),v=(0,s.Z)("Navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]]);var y=r(25394),g=r(66263);let x=[{name:"Crew Seatime Report",icon:n.jsx(i.Z,{}),url:"/reporting/crew-seatime-report"},{name:"Simple Fuel Report",icon:n.jsx(o,{}),url:"/reporting/simple-fuel-report"},{name:"Activity Report",icon:n.jsx(l,{}),url:"/reporting/activity-reports"},{name:"Engine Hours Report",icon:n.jsx(c.Z,{}),url:"/reporting/engine-hours-report"},{name:"Maintenance Status and Activity Report",icon:n.jsx(d,{}),url:"/reporting/maintenance-status-activity"},{name:"Maintenance Cost Track Report",icon:n.jsx(u,{}),url:"/reporting/maintenance-cost-track"},{name:"Fuel Analysis Report",icon:n.jsx(h,{}),url:"/reporting/fuel-analysis"},{name:"Fuel Tasking Analysis Report",icon:n.jsx(p,{}),url:"/reporting/fuel-tasking-analysis"},{name:"Detailed Fuel Report",icon:n.jsx(f.Z,{}),url:"/reporting/detailed-fuel-report"},{name:"Summary Fuel Report",icon:n.jsx(f.Z,{}),url:"/reporting/fuel-summary-report"},{name:"Service Report",icon:n.jsx(m,{}),url:"/reporting/service-report"},{name:"Trip Report",icon:n.jsx(v,{}),url:"/reporting/trip-report"}];function b(){return(0,a.useRouter)(),(0,n.jsxs)("div",{className:"w-full grid gap-8 mt-8",children:[n.jsx(y.H1,{children:"Reporting"}),n.jsx("div",{className:"flex flex-wrap gap-2",children:x.map(e=>n.jsx(g.default,{href:e.url,children:n.jsx(y.zx,{iconLeft:e.icon,variant:"primaryOutline",children:e.name})}))})]})}},4454:(e,t,r)=>{"use strict";r.d(t,{n:()=>f});var n=r(98768),a=r(17203),i=r(8087),s=r(27514),o=r(26509),l=r(13006),c=r(9210),d=r(56937),u=r(25394),h=r(60343),p=r(69424);function f({items:e,onBack:t,onDistructAction:r,ShowDistructive:f=!1,setOpen:m,logBookConfig:v,backLabel:y="Back to vessel",disTructLabel:g="Delete logbook entry",onSelect:x,triggerIcon:b}){let[j,k]=(0,l.v1)("tab",{defaultValue:"crew"}),[w,S]=(0,h.useState)(),O=(0,p.useRouter)(),C=async e=>{e.url?O.push(e.url):x?x(e.value):e.value!==j&&await k(e.value)};return(0,n.jsxs)(s.DropdownMenu,{children:[n.jsx(s.DropdownMenuTrigger,{className:"h-10 flex items-center",asChild:!!b,children:b?n.jsx(u.zx,{variant:"ghost",onClick:e=>e.stopPropagation(),size:"sm",className:"h-8 w-8 p-0",children:b}):n.jsx(c.HV,{size:36})}),(0,n.jsxs)(s.DropdownMenuContent,{align:"end",className:"w-[256px] p-0",children:[(0,n.jsxs)("div",{className:"text-input flex flex-col items-center justify-center py-[9px]",children:[(0,n.jsxs)(s.DropdownMenuItem,{hoverEffect:!1,onClick:t,className:(0,d.cn)("flex items-center gap-[8px] px-[26px] w-full h-11 rounded-md cursor-pointer focus:bg-accent","hover:bg-accent hover:px-[6px] hover:w-[233px] hover:border hover:border-border"),children:[n.jsx(a.Z,{size:16}),n.jsx("span",{children:y})]}),e.map((e,t)=>(0,n.jsxs)(s.DropdownMenuItem,{onClick:()=>C(e),children:[e.icon?e.icon:(0,n.jsxs)("div",{className:"h-full w-fit flex z-10 relative flex-col items-center justify-center",children:[n.jsx("div",{className:(0,d.cn)("border-l -translate-y-[17.5px] absolute inset-y-0 border-wedgewood-200 border-dashed",0===t&&"invisible")}),n.jsx("div",{className:(0,d.cn)("size-[11px] z-10 rounded-full","group-hover:border-primary group-hover:bg-curious-blue-200",j===e.value?"border border-primary bg-curious-blue-200":"border border-cool-wedgewood-200 bg-outer-space-50")})]}),n.jsx("div",{className:(0,d.cn)("relative z-20",j!==e.value||e.url?"":"font-medium text-accent-foreground"),children:e.label})]},t))]}),v&&w&&n.jsx(n.Fragment,{children:"Off"!==w.status&&(0,n.jsxs)(n.Fragment,{children:[n.jsx(o.Separator,{className:"border-border"}),(0,n.jsxs)(s.DropdownMenuItem,{hoverEffect:!1,className:(0,d.cn)("px-[47px] h-[61px] text-text hover:text-foreground flex gap-2.5 group py-[21px] relative focus:bg-outer-space-50 rounded-none"),onClick:()=>{m(!0)},children:[n.jsx("span",{className:"relative z-20 text-text uppercase hover:text-primary text-sm",children:"Radio logs"}),n.jsx("div",{className:(0,d.cn)("absolute w-20 left-12 inset-y-0","group-hover:bg-outer-space-50 group-hover:w-full group-hover:left-0","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-[width,left] group-hover:ease-out group-hover:duration-300","outline-none focus:outline-none active:outline-none")})]})]})}),f&&(0,n.jsxs)(n.Fragment,{children:[n.jsx(o.Separator,{className:"border-border"}),(0,n.jsxs)(s.DropdownMenuItem,{hoverEffect:!1,onClick:r,className:(0,d.cn)("group relative h-[61px] px-[26px] py-[21px] cursor-pointer focus:bg-destructive/5 rounded-none text-destructive focus:text-destructive"),children:[(0,n.jsxs)("div",{className:"relative gap-2.5 flex items-center z-20",children:[n.jsx(i.Z,{size:24}),n.jsx("span",{children:g})]}),n.jsx("div",{className:(0,d.cn)("absolute w-full h-11 bottom-0 inset-x-0","group-hover:bg-destructive/focus:bg-destructive/5 group-hover:h-full","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-[height,color] group-hover:ease-out group-hover:duration-300","outline-none focus:outline-none active:outline-none")})]})]})]})]})}},85114:(e,t,r)=>{"use strict";r.d(t,{D:()=>s,O:()=>i});var n=r(54110),a=r(10706);let i=e=>{let t=[...e].sort((e,t)=>new Date(t.created??t.date).getTime()-new Date(e.created??e.date).getTime()).shift();return t?.status??"AvailableForVoyage"},s=(e,t,r,i)=>{if(0===e.length){let e=i,a=new Date;n.w(i,a)>0&&(e=a);let s=n.w(e,r)+1;return{onVoyage:"OnVoyage"===t?s:0,availableForVoyage:"AvailableForVoyage"===t?s:0,outOfService:"OutOfService"===t?s:0}}let s="OnVoyage"===t?1:0,o="AvailableForVoyage"===t?1:0,l="OutOfService"===t?1:0,c=null,d=null,u=e[0];if(a.WU(u.date,"yyyy-MM-dd")!==a.WU(r,"yyyy-MM-dd")&&(c=r,d=t),e.forEach(e=>{if(null===c){c=e.date,d=e.status;return}let t=n.w(e.date,c);if(0!==t)switch(d){case"OnVoyage":s+=t;break;case"AvailableForVoyage":o+=t;break;case"OutOfService":l+=t}c=e.date,d=e.status}),n.w(i,c)>0){let e=i,t=new Date;n.w(i,t)>0&&(e=t);let r=n.w(e,c);switch(d){case"OnVoyage":s+=r;break;case"AvailableForVoyage":o+=r;break;case"OutOfService":l+=r}}return{onVoyage:s,availableForVoyage:o,outOfService:l}}},65128:(e,t,r)=>{"use strict";r.d(t,{z:()=>x});var n=r(98768),a=r(9210),i=r(34376),s=r(60343),o=r(72548),l=r(76342),c=r(33849),d=r(25394),u=r(29342),h=r(56937);let p=[{label:"On Voyage",value:"OnVoyage"},{label:"Ready For Voyage",value:"AvailableForVoyage"},{label:"Out Of Service",value:"OutOfService"}],f=[{label:"Crew Unavailable",value:"CrewUnavailable"},{label:"Skipper/Master Unavailable",value:"MasterUnavailable"},{label:"Planned Maintenance",value:"PlannedMaintenance"},{label:"Breakdown",value:"Breakdown"},{label:"Other",value:"Other"}];function m({vessel:e,display:t,setDisplay:r,onChangeStatusSuccess:a}){let{toast:m}=(0,i.pm)(),[v,y]=(0,s.useState)(null),[g]=(0,o.D)(l.qb9,{onCompleted:t=>{t.createLogBookEntry,y({...v,vesselID:e?.id,date:v?.date,status:v?.status,comment:v?.comment,reason:v?.reason,otherReason:v?.otherReason,expectedReturn:v?.expectedReturn}),r(!1),a&&a(v)},onError:e=>{m({variant:"destructive",description:e.message})}});return n.jsx(d.h9,{openDialog:t,size:"xl",setOpenDialog:r,handleCreate:()=>{v?.status==="OutOfService"?g({variables:{input:{vesselID:e?.id,date:v?.date,status:v?.status,comment:v?.comment,reason:v?.reason,otherReason:v?.otherReason,expectedReturn:v?.expectedReturn}}}):g({variables:{input:{vesselID:e?.id,date:v?.date,status:v?.status}}})},title:"Update Vessel Status",actionText:"Update",children:n.jsx("div",{className:"",children:(0,n.jsxs)("div",{className:"mb-4 md:mb-0",children:[n.jsx("div",{className:"my-4",children:n.jsx(u.Z,{mode:"single",onChange:e=>{y({...v,date:e})},className:"w-full",placeholder:"Select date",value:v?.date})}),(0,n.jsxs)("div",{className:(0,h.cn)("grid gap-2.5 my-4",v?.status==="OutOfService"?"grid-cols-2":"grid-cols-1"),children:[n.jsx(d.__,{label:"Status",children:n.jsx(d.hQ,{id:"vessel-status",options:p,placeholder:"Status",value:p.find(e=>v?.status===e.value),onChange:t=>{0===e.logentryID?"OnVoyage"===t.value?m({variant:"destructive",description:"There is no Open LogBook entry, Please create a Logbook entry to set the vessel on voyage"}):y({...v,status:t?.value}):m({variant:"destructive",description:"There is an Open LogBook entry, Please complete the entry in order to update the vessel status"})}})}),v?.status==="OutOfService"&&n.jsx(d.__,{label:"Reason for out of service status",children:n.jsx(d.hQ,{id:"vessel-status-reason",options:f,placeholder:"Reason",value:f.find(e=>v?.reason===e.value),onChange:e=>{y({...v,reason:e?.value})}})})]}),v?.status==="OutOfService"&&v?.reason==="Other"&&n.jsx("div",{className:"flex items-center my-4",children:n.jsx(d.gx,{id:"vessel-status-other",placeholder:"Other description",value:v?.otherReason,onChange:e=>y({...v,otherReason:e.target.value})})}),v?.status==="OutOfService"&&n.jsx(d.__,{label:"Comments",className:"my-4",children:n.jsx(c.Z,{id:"comment",placeholder:"Comment",content:v?.comment,handleEditorChange:e=>y({...v,comment:e})})}),v?.status==="OutOfService"&&(0,n.jsxs)("div",{className:"my-4",children:[n.jsx(d.__,{label:"Expected date of return"}),n.jsx(u.Z,{mode:"single",onChange:e=>{y({...v,expectedReturn:e})},className:"w-full",placeholder:"Select date",value:v?.expectedReturn})]})]})})})}var v=r(4454),y=r(69424),g=r(46020);let x=({vessel:e,onChangeStatusSuccess:t})=>{let[r,o]=(0,s.useState)(!1),l=(0,y.useRouter)(),{toast:c}=(0,i.pm)(),d=()=>{if(e.logentryID>0){c({variant:"destructive",description:"There is an Open LogBook entry, Please complete the entry in order to update the vessel status"});return}o(!0)},u=[{label:"Maintenance",value:"maintenance",icon:n.jsx(a.y5,{className:"icons h-6 w-6 bg-accent/0 ring-0"}),url:`/vessel/info?id=${e.id}&tab=maintenance`},{label:"Crew",value:"crew",icon:n.jsx(a.Ko,{className:"icons h-6 w-6"}),url:`/vessel/info?id=${e.id}&tab=crew`},{label:"Training & drills",value:"crew_training",icon:n.jsx(a._z,{className:"icons h-6 w-6 bg-accent/0 ring-0"}),url:`/vessel/info?id=${e.id}&tab=crew_training`},{label:0!==e.logentryID?"Open Logbook entry":"Logbook entry",value:0!==e.logentryID?"logbook-open":"logbook",icon:n.jsx(a.V1,{className:"icons h-6 w-6"}),url:0!==e.logentryID?`/log-entries?vesselID=${e.id}&logentryID=${e.logentryID}`:`/vessel/info?id=${e.id}`},{label:"Documents",value:"documents",icon:n.jsx(a.EU,{className:"icons h-6 w-6"}),url:`/vessel/info?id=${e.id}&tab=documents`}];return(0,n.jsxs)(n.Fragment,{children:[n.jsx(v.n,{backLabel:"View vessel",triggerIcon:n.jsx(g.Z,{className:"h-4 w-4"}),onBack:()=>l.push(`/vessel/info?id=${e.id}`),items:u,onDistructAction:()=>d(),ShowDistructive:!0,disTructLabel:e.status&&"OutOfService"!==e.status.status?"Take vessel out of service":"Change vessel status"}),n.jsx(m,{vessel:e,display:r,setDisplay:o,onChangeStatusSuccess:t})]})}},10901:(e,t,r)=>{"use strict";r.d(t,{k:()=>h});var n=r(98768),a=r(11652),i=r(41641),s=r(39303),o=r(40712),l=r(39544),c=r(24224),d=r(25394),u=r(50058);function h({table:e,pageSizeOptions:t=[10,20,30,40,50],showPageSizeSelector:r=!0}){let h=(0,u.k)();return n.jsx("div",{className:"flex items-center justify-center px-2",children:(0,n.jsxs)("div",{className:"flex gap-2 items-end phablet:items-center phablet:space-x-6 lg:space-x-8",children:[r&&n.jsx("div",{className:"flex items-center space-x-2",children:n.jsx(d.__,{label:"Rows per page",position:h.phablet?"left":"top",htmlFor:"rows-per-page",className:"text-sm",children:(0,n.jsxs)(c.Select,{value:`${e.getState().pagination.pageSize}`,onValueChange:t=>{e.setPageSize(Number(t))},children:[n.jsx(c.SelectTrigger,{className:"h-8 w-[70px]",children:n.jsx(c.SelectValue,{placeholder:e.getState().pagination.pageSize})}),n.jsx(c.SelectContent,{side:"top",children:t.map(e=>n.jsx(c.SelectItem,{value:`${e}`,children:e},e))})]})})}),(0,n.jsxs)("div",{className:"flex w-fit items-center justify-center text-sm",children:["Page ",e.getState().pagination.pageIndex+1," of"," ",e.getPageCount()]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsxs)(l.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>e.setPageIndex(0),disabled:!e.getCanPreviousPage(),children:[n.jsx("span",{className:"sr-only",children:"Go to first page"}),n.jsx(a.Z,{})]}),(0,n.jsxs)(l.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>e.previousPage(),disabled:!e.getCanPreviousPage(),children:[n.jsx("span",{className:"sr-only",children:"Go to previous page"}),n.jsx(i.Z,{})]}),(0,n.jsxs)(l.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>e.nextPage(),disabled:!e.getCanNextPage(),children:[n.jsx("span",{className:"sr-only",children:"Go to next page"}),n.jsx(s.Z,{})]}),(0,n.jsxs)(l.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>e.setPageIndex(e.getPageCount()-1),disabled:!e.getCanNextPage(),children:[n.jsx("span",{className:"sr-only",children:"Go to last page"}),n.jsx(o.Z,{})]})]})]})})}},30905:(e,t,r)=>{"use strict";r.d(t,{u:()=>u});var n=r(98768),a=r(97428);let i=(0,a.Z)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]),s=(0,a.Z)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);var o=r(84961),l=r(56937),c=r(39544),d=r(27514);function u({column:e,title:t,className:r}){return e.getCanSort()?n.jsx("div",{children:(0,n.jsxs)(d.DropdownMenu,{children:[n.jsx(d.DropdownMenuTrigger,{asChild:!0,children:n.jsx(c.Button,{variant:"ghost",size:"sm",iconRight:"desc"===e.getIsSorted()?n.jsx(i,{className:"w-4 h-4"}):"asc"===e.getIsSorted()?n.jsx(s,{className:"w-4 h-4"}):n.jsx(o.Z,{className:"w-4 h-4"}),className:(0,l.cn)("h-8 small:p-auto cursor-default relative z-10 bg-card text-sm text-neutral-400 font-normal items-center px-1",r),children:t})}),(0,n.jsxs)(d.DropdownMenuContent,{align:"start",children:[(0,n.jsxs)(d.DropdownMenuItem,{onClick:()=>e.toggleSorting(!1),children:[n.jsx(s,{className:"h-3.5 w-3.5 text-input"}),"Asc"]}),(0,n.jsxs)(d.DropdownMenuItem,{onClick:()=>e.toggleSorting(!0),children:[n.jsx(i,{className:"h-3.5 w-3.5 text-input"}),"Desc"]}),e.getIsSorted()&&(0,n.jsxs)(n.Fragment,{children:[n.jsx(d.DropdownMenuSeparator,{}),n.jsx(d.DropdownMenuItem,{onClick:()=>e.clearSorting(),children:"Clear"})]})]})]})}):n.jsx("div",{className:(0,l.cn)(r),children:t})}},6958:(e,t,r)=>{"use strict";r.d(t,{n:()=>i});var n=r(98768),a=r(37042);function i({table:e,onChange:t}){return n.jsx(a.Z,{onChange:t,table:e})}},51742:(e,t,r)=>{"use strict";r.d(t,{QT:()=>g,wQ:()=>y,wu:()=>p});var n=r(98768),a=r(26659),i=r(6958),s=r(10901),o=r(50058),l=r(60343),c=r(24109),d=r(23416),u=r(35024),h=r(56937);function p(e){return e}let f=e=>{switch(e){case"left":return"items-left justify-start justify-items-start text-left";case"right":return"items-right justify-end justify-items-end text-right";default:return"items-center justify-center justify-items-center text-center"}},m=e=>{switch(e){case"overdue":case"upcoming":return"rounded-md [&>td:first-child]:rounded-l-lg [&>td:last-child]:rounded-r-lg";default:return""}},v=e=>{switch(e){case"overdue":return"destructive";case"upcoming":return"warning";default:return}};function y({columns:e,data:t,showToolbar:r=!0,className:p,pageSize:y=10,pageSizeOptions:g=[10,20,30,40,50],showPageSizeSelector:x=!0,onChange:b,rowStatus:j}){let[k,w]=l.useState([]),[S,O]=l.useState([]),[C,N]=l.useState({pageIndex:0,pageSize:y}),A=(0,o.k)(),D=l.useMemo(()=>e.filter(e=>e.showOnlyBelow?!A[e.showOnlyBelow]:!e.breakpoint||A[e.breakpoint]),[e,A]);l.useEffect(()=>{N(e=>({...e,pageSize:y}))},[y]);let P=(0,c.b7)({data:t,columns:D,onSortingChange:w,getCoreRowModel:(0,d.sC)(),getPaginationRowModel:(0,d.G_)(),getSortedRowModel:(0,d.tj)(),onColumnFiltersChange:O,getFilteredRowModel:(0,d.vL)(),onPaginationChange:N,state:{sorting:k,columnFilters:S,pagination:C}});return(0,n.jsxs)("div",{className:"space-y-4 pb-8",children:[r&&n.jsx(u.Zb,{className:"p-2 md:p-auto",children:n.jsx(i.n,{table:P,onChange:b})}),(0,n.jsxs)(a.iA,{className:p||"p-0 phablet:p-8 lg:p-6 xl:p-8 shadow-none border-0 phablet:border border-border bg-card rounded-lg",children:[P.getHeaderGroups().some(e=>e.headers.some(e=>e.column.columnDef.header&&""!==e.column.columnDef.header))&&n.jsx(a.xD,{children:P.getHeaderGroups().map(e=>n.jsx(a.SC,{children:e.headers.map(e=>{let t=e.column.columnDef,r="title"===e.column.id?"left":t.cellAlignment||"center";return n.jsx(a.ss,{className:"title"===e.column.id?"items-left justify-items-start text-left":f(r),children:e.isPlaceholder?null:(0,c.ie)(e.column.columnDef.header,e.getContext())},e.id)})},e.id))}),n.jsx(a.RM,{children:P.getRowModel().rows.length?P.getRowModel().rows.map(e=>{let t=j?j(e.original):"normal",r=m(t);return n.jsx(a.SC,{"data-state":e.getIsSelected()?"selected":void 0,className:(0,h.cn)("mb-4",r),children:e.getVisibleCells().map(e=>{let r=e.column.columnDef,i="title"===e.column.id?"left":r.cellAlignment||"center";return n.jsx(a.pj,{statusOverlay:"normal"!==t,statusOverlayColor:v(t),className:(0,h.cn)("","title"===e.column.id?`${D.length>1?"w-auto":"w-full"} items-left justify-items-start text-left`:f(i),r.cellClassName),children:n.jsx("div",{className:(0,h.cn)("flex px-1.5 xs:px-2.5 flex-1",f(i)),children:(0,c.ie)(e.column.columnDef.cell,e.getContext())})},e.id)})},String(e.id))}):n.jsx(a.SC,{children:n.jsx(a.pj,{colSpan:D.length,className:"h-24 text-center",children:"No results."})})})]}),(P.getCanPreviousPage()||P.getCanNextPage())&&n.jsx("div",{className:"flex items-center justify-center phablet:justify-end space-x-2 py-4",children:n.jsx(s.k,{table:P,pageSizeOptions:g,showPageSizeSelector:x})})]})}let g=y},29019:(e,t,r)=>{"use strict";r.d(t,{BO:()=>h,dg:()=>m,h7:()=>f});var n=r(98768),a=r(60343),i=r(59447),s=r(52419),o=r(94309),l=r(56937);let c={light:"",dark:".dark"},d=a.createContext(null);function u(){let e=a.useContext(d);if(!e)throw Error("useChart must be used within a <ChartContainer />");return e}let h=a.forwardRef(({id:e,className:t,children:r,config:s,...o},c)=>{let u=a.useId(),h=`chart-${e||u.replace(/:/g,"")}`;return n.jsx(d.Provider,{value:{config:s},children:(0,n.jsxs)("div",{"data-chart":h,ref:c,className:(0,l.cn)("flex aspect-video justify-center  [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#127FBF']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",t),...o,children:[n.jsx(p,{id:h,config:s}),n.jsx(i.h,{children:r})]})})});h.displayName="Chart";let p=({id:e,config:t})=>{let r=Object.entries(t).filter(([,e])=>e.theme||e.color);return r.length?n.jsx("style",{dangerouslySetInnerHTML:{__html:Object.entries(c).map(([t,n])=>`
${n} [data-chart=${e}] {
${r.map(([e,r])=>{let n=r.theme?.[t]||r.color;return n?`  --color-${e}: ${n};`:null}).join("\n")}
}
`).join("\n")}}):null},f=s.u,m=a.forwardRef(({active:e,payload:t,className:r,indicator:i="dot",hideLabel:s=!1,hideIndicator:o=!1,label:c,labelFormatter:d,labelClassName:h,formatter:p,color:f,nameKey:m,labelKey:y},g)=>{let{config:x}=u(),b=a.useMemo(()=>{if(s||!t?.length)return null;let[e]=t,r=`${y||e.dataKey||e.name||"value"}`,a=v(x,e,r),i=y||"string"!=typeof c?a?.label:x[c]?.label||c;return d?n.jsx("div",{className:(0,l.cn)("font-medium",h),children:d(i,t)}):i?n.jsx("div",{className:(0,l.cn)("font-medium",h),children:i}):null},[c,d,t,s,h,x,y]);if(!e||!t?.length)return null;let j=1===t.length&&"dot"!==i;return(0,n.jsxs)("div",{ref:g,className:(0,l.cn)("grid min-w-[8rem] items-start gap-1.5 rounded-lg border bg-background px-2.5 py-1.5 shadow-xl",r),children:[j?null:b,n.jsx("div",{className:"grid gap-1.5",children:t.map((e,t)=>{let r=`${m||e.name||e.dataKey||"value"}`,a=v(x,e,r),s=f||e.payload.fill||e.color;return n.jsx("div",{className:(0,l.cn)("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground","dot"===i&&"items-center"),children:p&&e?.value!==void 0&&e.name?p(e.value,e.name,e,t,e.payload):(0,n.jsxs)(n.Fragment,{children:[a?.icon?n.jsx(a.icon,{}):!o&&n.jsx("div",{className:(0,l.cn)("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":"dot"===i,"w-1":"line"===i,"w-0 border-[1.5px] border-dashed bg-transparent":"dashed"===i,"my-0.5":j&&"dashed"===i}),style:{"--color-bg":s,"--color-border":s}}),(0,n.jsxs)("div",{className:(0,l.cn)("flex flex-1 justify-between leading-none",j?"items-end":"items-center"),children:[(0,n.jsxs)("div",{className:"grid gap-1.5 pr-3",children:[j?b:null,n.jsx("span",{className:"text-foreground",children:a?.label||e.name})]}),e.value&&n.jsx("span",{className:"font-mono font-medium tabular-nums text-foreground",children:e.value.toLocaleString()})]})]})},e.dataKey)})})]})});function v(e,t,r){if("object"!=typeof t||null===t)return;let n="payload"in t&&"object"==typeof t.payload&&null!==t.payload?t.payload:void 0,a=r;return r in t&&"string"==typeof t[r]?a=t[r]:n&&r in n&&"string"==typeof n[r]&&(a=n[r]),a in e?e[a]:e[r]}m.displayName="ChartTooltip",o.D,a.forwardRef(({className:e,hideIcon:t=!1,payload:r,verticalAlign:a="bottom",nameKey:i},s)=>{let{config:o}=u();return r?.length?n.jsx("div",{ref:s,className:(0,l.cn)("flex items-center justify-center gap-4","top"===a?"pb-3":"pt-3",e),children:r.map(e=>{let r=`${i||e.dataKey||"value"}`,a=v(o,e,r);return(0,n.jsxs)("div",{className:(0,l.cn)("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[a?.icon&&!t?n.jsx(a.icon,{}):n.jsx("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:e.color}}),a?.label]},e.value)})}):null}).displayName="ChartLegend"},61086:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\dashboard\layout.tsx#default`)},56814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\dashboard\page.tsx#default`)},51445:()=>{},87729:()=>{},60385:()=>{},30314:()=>{},26012:()=>{},8087:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},70996:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},53363:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,9707,6250,8822,5741,813,7108,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7380,8410,7626],()=>r(70578));module.exports=n})();