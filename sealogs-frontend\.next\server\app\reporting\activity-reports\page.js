(()=>{var e={};e.id=3178,e.ids=[3178],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},98913:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c}),n(69452),n(71238),n(78398),n(57757),n(48045);var s=n(40060),i=n(33581),r=n(57567),a=n.n(r),l=n(51650),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);n.d(t,o);let c=["",{children:["reporting",{children:["activity-reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,69452)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\activity-reports\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(n.bind(n,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\activity-reports\\page.tsx"],m="/reporting/activity-reports/page",p={require:n,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/reporting/activity-reports/page",pathname:"/reporting/activity-reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},58309:(e,t,n)=>{Promise.resolve().then(n.bind(n,78645))},88332:(e,t,n)=>{Promise.resolve().then(n.bind(n,44517))},91973:(e,t,n)=>{"use strict";n.d(t,{u:()=>s});let s=(e,t="report.csv")=>{let n=new Blob([e.map(e=>e.join(",")).join("\n")],{type:"text/csv;charset=utf-8,"}),s=URL.createObjectURL(n);Object.assign(document.createElement("a"),{href:s,download:t}).click(),URL.revokeObjectURL(s)}},2604:(e,t,n)=>{"use strict";n.d(t,{S:()=>a});var s=n(9707),i=n(58774),r=n.n(i);function a(e,t){let n=new s.default(t);r()(n,{head:e.headers,body:e.body,foot:e.footers,margin:5,...e.userOptions}),n.save(e.fileName||"report.pdf")}},78645:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>P});var s=n(98768);n(46776);var i=n(26100),r=n(37042),a=n(60343),l=n(49517),o=n(88006),c=n(79418),d=n(83179),m=n.n(d),p=n(91973),u=n(2604),h=n(26659);let g=["Vessel","Date","Time","Title","Location","Passengers On","Passengers Off"];function f({dateRange:e,vessels:t,lastGenerated:n}){let[i,{called:r,loading:d,data:f}]=(0,c.t)(o.HP,{fetchPolicy:"cache-and-network",onError:e=>{console.error("GET_ARRIVAL_DEPARTURE_EVENT_ENTRIES error",e)}});(0,a.useCallback)(()=>{let n={};null!==e.startDate&&null!==e.endDate&&(n.startDate={gte:e.startDate,lte:e.endDate}),t.length>0&&(n.vehicleID={in:t.map(e=>e.value)}),i({variables:{filter:n}})},[e,t]);let v=(0,a.useMemo)(()=>{let e=f?.readLogBookEntries?.nodes??[];if(0===e.length)return[];let t=[];return e.forEach(function(e){if("Locked"!==e.state||0==e.vehicle.id||0===e.logBookEntrySections.nodes.length)return;let n=e.logBookEntrySections.nodes.reduce((e,t)=>[...e,...t.tripEvents.nodes],[]);0!==n.length&&n.forEach(n=>{let s=n.eventType_PassengerDropFacility,i={vesselID:e.vehicle.id,vesselName:e.vehicle.title,date:new Date(s.created?s.created:e.created),title:s.title,location:s.geoLocation?.title??"N/A",passengersBoarding:s.paxOn??0,passengersEmbarking:s.paxOff??0,time:s.time};t.push(i)})}),t.sort((e,t)=>e.date.getTime()-t.date.getTime()),t},[r,d,f]);return(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[s.jsx(l.Z,{onDownloadCsv:()=>{if(0===v.length)return;let e=[];e.push(["vessel","date","time","title","location","passengers on","passengers off"]),v.forEach(t=>{e.push([t.vesselName,m()(t.date).format("DD/MM/YYYY"),t.time??m()(t.date).format("HH:mm"),t.title,t.location,t.passengersBoarding,t.passengersEmbarking])}),(0,p.u)(e)},onDownloadPdf:()=>{if(0===v.length)return;let e=v.map(e=>[e.vesselName,m()(e.date).format("DD/MM/YYYY"),e.time??m()(e.date).format("HH:mm"),e.title,e.location,e.passengersBoarding,e.passengersEmbarking]);(0,u.S)({headers:[["Vessel","Date","Time","Title","Location","Passengers on","Passengers off"]],body:e})}}),(0,s.jsxs)(h.iA,{children:[s.jsx(h.xD,{children:s.jsx(h.SC,{children:g.map(e=>s.jsx(h.ss,{children:e},e))})}),s.jsx(h.RM,{children:s.jsx(x,{isLoading:r&&d,reportData:v})})]})]})}function x({reportData:e,isLoading:t}){return t?s.jsx(h.SC,{children:s.jsx(h.pj,{colSpan:g.length,className:"text-center  h-32",children:"Loading..."})}):0==e.length?s.jsx(h.SC,{children:s.jsx(h.pj,{colSpan:g.length,className:"text-center  h-32",children:"No Data Available"})}):e.map((e,t)=>(0,s.jsxs)(h.SC,{className:"group border-b  hover: ",children:[s.jsx(h.pj,{className:"px-2 py-3 text-left w-[15%]",children:s.jsx("div",{className:" inline-block",children:e.vesselName})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:m()(e.date).format("DD/M/YY")})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:e.time??m()(e.date).format("HH:mm")})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[15%]",children:s.jsx("div",{className:" inline-block",children:e.title})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[15%]",children:s.jsx("div",{className:" inline-block",children:e.location})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[15%]",children:s.jsx("div",{className:" inline-block",children:e.passengersBoarding})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[15%]",children:s.jsx("div",{className:" inline-block",children:e.passengersEmbarking})})]},"report-item-"+t))}let v=["Vessel","Date","Time","Location","Name of Fuel Tank","Fuel before refuelling","Added fuel","Fuel after refuelling","Comment and receipts"];function j({dateRange:e,vessels:t,lastGenerated:n}){let[i,{called:r,loading:d,data:g}]=(0,c.t)(o.SD,{fetchPolicy:"cache-and-network",onError:e=>{console.error("GET_REFUELING_BUNKERING_EVENT_ENTRIES error",e)}});(0,a.useCallback)(()=>{let n={};null!==e.startDate&&null!==e.endDate&&(n.startDate={gte:e.startDate,lte:e.endDate}),t.length>0&&(n.vehicleID={in:t.map(e=>e.value)}),i({variables:{filter:n}})},[e,t]);let f=(0,a.useMemo)(()=>{let e=g?.readLogBookEntries?.nodes??[];if(0===e.length)return[];let t=[];return e.forEach(function(e){if("Locked"!==e.state||0==e.vehicle.id||0===e.logBookEntrySections.nodes.length)return;let n=e.logBookEntrySections.nodes,s=n.reduce((e,t)=>[...e,...t.tripEvents.nodes],[]);if(0===s.length)return;let i=s.filter(e=>e.eventType_RefuellingBunkering.fuelLog.nodes.length>0);if(0===i.length)return;let r=n.reduce((e,t)=>[...e,...t.sectionMemberComments.nodes],[]).map(e=>e?.comment).filter(e=>null!=e||""!=e);i.forEach(n=>{let s=n.eventType_RefuellingBunkering,i=s.geoLocation;s.fuelLog.nodes.forEach(function(n){let s={vesselName:e.vehicle.title,dateTime:new Date(n.created),location:i?.title??"N/A",fuelBefore:n.fuelBefore,fuelAdded:n.fuelAdded,fuelAfter:n.fuelAfter,fuelTank:n.fuelTank?.title??"N/A",comment:r.join(", ")};t.push(s)})})}),t.sort((e,t)=>e.dateTime.getTime()-t.dateTime.getTime()),t},[r,d,g]);return(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[s.jsx(l.Z,{onDownloadCsv:()=>{if(0===f.length)return;let e=[];e.push(["vessel","date time","location","name of fuel tank","fuel before refuelling","added fuel","fuel after refuelling","comment and receipts"]),f.forEach(t=>{e.push([t.vesselName,t.dateTime.toISOString(),t.location,t.fuelTank,t.fuelBefore+"",t.fuelAdded+"",t.fuelAfter+"",t.comment??""])}),(0,p.u)(e)},onDownloadPdf:()=>{if(0===f.length)return;let e=f.map(e=>[e.vesselName,m()(e.dateTime).format("DD/M/YY"),m()(e.dateTime).format("HH:mm"),e.location,e.fuelTank,e.fuelBefore+"",e.fuelAdded+"",e.fuelAfter+"",e.comment??""]);(0,u.S)({headers:[["Vessel","Date","Time","Location","Name of Fuel Tank","Fuel before refuelling","Added fuel","Fuel after refuelling","Comment and receipts"]],body:e})}}),(0,s.jsxs)(h.iA,{children:[s.jsx(h.xD,{children:s.jsx(h.SC,{children:v.map(e=>s.jsx(h.ss,{children:e},e))})}),s.jsx(h.RM,{children:s.jsx(N,{reportData:f,isLoading:r&&d})})]})]})}function N({reportData:e,isLoading:t}){return t?s.jsx(h.SC,{children:s.jsx(h.pj,{colSpan:v.length,className:"text-center  h-32",children:"Loading..."})}):0==e.length?s.jsx(h.SC,{children:s.jsx(h.pj,{colSpan:v.length,className:"text-center  h-32",children:"No Data Available"})}):e.map((e,t)=>(0,s.jsxs)(h.SC,{className:"group border-b  hover: ",children:[s.jsx(h.pj,{className:"px-2 py-3 text-left w-[15%]",children:s.jsx("div",{className:" inline-block",children:e.vesselName})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:m()(e.dateTime).format("DD/M/YY")})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:m()(e.dateTime).format("HH:mm")})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.location})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.fuelTank})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.fuelBefore})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.fuelAdded})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.fuelAfter})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[20%]",children:s.jsx("div",{className:" inline-block",children:e.comment})})]},"report-item-"+t))}let w={lifeJacket:"Life Jacket infringement issued",speedOrNavigation:"Speed / navigation infringement issued",towing:"Towing infringement issued",swimmingOrDiving:"Swimming / diving infringement issued",mooring:"Mooring / embarkation / ramps or jetty infringements",other:"Other"},k=e=>{switch(e){case"lifeJacket":return{FailingToCarryLifejackets:"Failing to carry accessible and sufficient lifejackets of appropriate size for each person on board the vessel",FailingToWearLifejacketWhenInstructed:"Failing to wear properly secured lifejacket of appropriate size when instructed by person in charge of recreational vessel",FailingToEnsureLifejacketOnVessel6mOrLess:"Failing to ensure persons on recreational vessel 6 metres or less wear a proper lifejacket when the vessel is making way",FailingToEnsureLifejacketForChildrenOnVesselGreaterThan6m:"Failing to ensure persons 10 years or younger on recreational vessel greater than 6 metres wear a proper lifejacket at all times",FailingToEnsureLifejacketOnJetBoat:"Failing to ensure persons on a recreational jet boat wear proper lifejacket when making way",FailingToEnsureLifejacketInDanger:"Failing to ensure persons on a recreational vessel wear a proper lifejacket in a situation of danger or risk",TowingWithoutLifejacketOver5Knots:"Towing a person who is not wearing a properly secured lifejacket of appropriate size while the vessel is doing a speed exceeding 5 knots, or being towed by a vessel at a speed exceeding 5 knots while not wearing a properly secured lifejacket of appropriate size"};case"speedOrNavigation":return{UnsupervisedUnderagePersonOperatingVessel:"Being an unsupervised underage person operating a powered vessel capable of exceeding 10 knots",AllowingUnsupervisedUnderagePerson:"Allowing an unsupervised underage person to operate powered vessel capable of exceeding 10 knots",Exceeding5KnotRestriction50Metres:"Exceeding the 5-knot speed restriction within 50 metres of vessel, floating structure, or person",Exceeding5KnotRestriction200MetresShore:"Exceeding the 5-knot speed restriction within 200 metres of shore or structure",Exceeding5KnotRestriction200MetresFlagA:"Exceeding the 5-knot speed restriction within 200 metres of vessel flying Flag A of International Code of Signals",Exceeding5KnotRestrictionBodyExtending:"Exceeding 5-knot speed restriction while person has part of body extending from powered vessel",BeingTowedExceeding5Knots:"Being towed at speed of more than 5 knots in restricted-speed locations",NavigatingWithoutDueCare:"Navigating vessel without due care and caution or at speed or in manner so as to endanger any person",FailingToKeepStarboard:"Failing to ensure vessel keeps to starboard side of river channel",FailingToGiveWayDownstream:"Failing to give way to vessel coming downstream when going upstream",OperatingInUnsafeConditions:"Operating vessel in conditions that do not permit safe operation",Exceeding5KnotLimitLakes:"Exceeding 5-knot speed limit on specified lakes in powered vessel"};case"towing":return{TowingExceeding5KnotsNoLookout:"Towing person from vessel at speed exceeding 5 knots without lookout of appropriate age",BeingTowedExceeding5KnotsNoLookout:"Being towed from vessel at speed exceeding 5 knots without lookout of appropriate age",TowingExceeding5KnotsNight:"Towing person from vessel between sunset and sunrise or in restricted visibility",BeingTowedExceeding5KnotsNight:"Being towed from vessel between sunset and sunrise",ParasailingFranktonArm:"Lake Wakatipu: operating vessel involved in parasailing in Frankton arm of lake"};case"swimmingOrDiving":return{CreatingNuisance:"Creating nuisance through use or control of vessel, speed of vessel, or speed of item or object towed behind or used with vessel",SwimmingNearJettyWithNoSwimmingSign:"Swimming, diving, jumping, or related activities from or within 50 metres of jetty or wharf with “no swimming” signage",SwimmingInHarbourmasterArea:"Swimming or diving in area identified by harbourmaster"};case"mooring":return{EmbarkingOrDisembarkingWhileVesselIsMakingWay:"Embarking or disembarking while vessel is making way",AnchoringVesselInMannerThatObstructsPassageOfVessels:"Anchoring vessel in manner that obstructs passage of vessels or obstructs approach to wharf, pier, or jetty, or creates hazard to vessels at anchor",FailingToMoorVesselInSecureManner:"Failing to moor vessel in secure manner or without adequate or safe means of accessing vessel",PlacingObstructionInWatersLikelyToRestrictNavigation:"Placing obstruction in waters likely to restrict navigation, or cause injury, death, or damage",TyingVesselToNavigationAidWithoutPermission:"Tying vessel to navigation aid without permission",DamagingRemovingDefacingOrInterferingWithNavigationAid:"Damaging, removing, defacing, or otherwise interfering with navigation aid",ObstructingUseOfJettyWharfRampOrLaunchFacility:"Obstructing use of jetty, wharf, ramp, or launch facility owned or operated by Queenstown Lakes District Council",RefuellingVesselWithPassengersOnBoard:"Refuelling vessel with passengers on board"};case"other":return{PermittingVesselToContinueAfterWaterSkiDropped:"Permitting vessel to continue onwards after water ski or similar object dropped by person being towed",FailingToEnsureWakeSafety:"Failing to ensure wake does not prevent people from safely using waterway, or does not cause danger or risk of danger, or does not cause risk of harm",CreatingNuisanceThroughVesselUse:"Creating nuisance through use or control of vessel, speed of vessel, or speed of item or object towed behind or used with vessel",FailingToKeepVesselSeaworthy:"Failing to keep vessel in seaworthy condition or leaving vessel sunk, stranded, or abandoned",FailingToConductHotWorkSafely:"Failing to conduct hot work operations in accordance with Code of Safe Working Practices for Merchant Seafarers",FailingToTakeFirePrecautions:"Failing to take fire precautions before or during hot work operations",FailingToMaintainDangerousGoodsRecord:"Failing to maintain or make available written record of dangerous goods loaded or unloaded onto vessel",FailingToApplyForOrganisedWaterActivity:"Failing to apply to the harbourmaster when intending to conduct organised water activity"};default:return{}}},y=["Date","Time","Location","Vessel Name","Vessel registration","Owner full name","Owner date of birth","Owner occupation.","Owner address","Owner phone number","Owner Email Address","Infringement incurred","List of checked infringements","Comments"];function b({dateRange:e,vessels:t,lastGenerated:n}){let[i,{called:r,loading:d,data:g}]=(0,c.t)(o.QT,{fetchPolicy:"cache-and-network",onError:e=>{console.error("GET_INFRINGEMENT_NOTICES_EVENT_ENTRIES error",e)}});(0,a.useCallback)(()=>{let n={};null!==e.startDate&&null!==e.endDate&&(n.startDate={gte:e.startDate,lte:e.endDate}),t.length>0&&(n.vehicleID={in:t.map(e=>e.value)}),i({variables:{filter:n}})},[e,t]);let f=(0,a.useMemo)(()=>{let e=g?.readLogBookEntries?.nodes??[];if(0===e.length)return[];let t=[];return e.forEach(function(e){if("Locked"!==e.state||0==e.vehicle.id||0===e.logBookEntrySections.nodes.length)return;let n=e.logBookEntrySections.nodes.reduce((e,t)=>[...e,...t.tripEvents.nodes],[]);0!==n.length&&n.forEach(n=>{let s=n.infringementNotice;if(null===s.created)return;let i=JSON.parse(s.infringementData),r=i?.infringementUsed??[];if(0===r.length)return;let a=new Date(s.created);r.forEach(r=>{let l=i[r].map(e=>(function(e,t){let n=k(e);return Object.hasOwn(n,t)?n[t]:null})(r,e)).filter(e=>null!==e),o=Object.hasOwn(w,r)?w[r]:"N/A",c={date:a,time:s.time??m()(a).format("HH:mm:ss"),location:s.geoLocation.title??"N/A",vesselName:e.vehicle.title,vesselRegistration:e.vehicle.registration,ownerFullName:s.ownerFullName,ownerDOB:s.ownerDOB?new Date(s.ownerDOB):null,ownerOccupation:s.ownerOccupation,ownerAddress:s.ownerAddress,ownerPhoneNumber:s.ownerPhone,ownerEmail:s.ownerEmail,infringementIncurred:o,checkedInfringements:l,comments:n.notes};t.push(c)})})}),t},[r,d,g]);return(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[s.jsx(l.Z,{onDownloadCsv:()=>{if(0===f.length)return;let e=[["date","time","location","vessel name","vessel registration","owner full name","owner date of birth","owner occupation.","owner address","owner phone number","owner email address","infringement incurred","list of checked infringements","comments"]];f.forEach(t=>{e.push([m()(t.date).format("DD/MM/YYYY"),t.time,t.location??"N/A",t.vesselName,t.vesselRegistration??"N/A",t.ownerFullName??"N/A",t.ownerDOB?m()(t.ownerDOB).format("DD/MM/YYYY"):"N/A",t.ownerOccupation??"N/A",t.ownerAddress??"N/A",t.ownerPhoneNumber??"N/A",t.ownerEmail??"N/A",t.infringementIncurred,t.checkedInfringements.join("/"),t.comments??"N/A"])}),(0,p.u)(e)},onDownloadPdf:()=>{if(0===f.length)return;let e=f.map(e=>[{content:m()(e.date).format("DD/MM/YYYY"),styles:{cellWidth:25}},{content:e.time,styles:{cellWidth:25}},{content:e.location??"N/A",styles:{cellWidth:25}},{content:e.vesselName,styles:{cellWidth:35}},{content:e.vesselRegistration??"N/A",styles:{cellWidth:30}},{content:e.ownerFullName??"N/A",styles:{cellWidth:32}},{content:e.ownerDOB?m()(e.ownerDOB).format("DD/MM/YYYY"):"N/A",styles:{cellWidth:25}},{content:e.ownerOccupation??"N/A",styles:{cellWidth:30}},{content:e.ownerAddress??"N/A",styles:{cellWidth:40}},{content:m()(e.date).format("DD/MM/YYYY"),styles:{cellWidth:25}},{content:e.time,styles:{cellWidth:25}},{content:e.location??"N/A",styles:{cellWidth:30}},{content:e.vesselName,styles:{cellWidth:35}},{content:e.vesselRegistration??"N/A",styles:{cellWidth:30}},{content:e.ownerFullName??"N/A",styles:{cellWidth:40}},{content:e.ownerPhoneNumber??"N/A",styles:{cellWidth:40}},{content:e.ownerEmail??"N/A",styles:{cellWidth:40}},{content:m()(e.date).format("DD/MM/YYYY"),styles:{cellWidth:25}},{content:e.time,styles:{cellWidth:25}},{content:e.location??"N/A",styles:{cellWidth:30}},{content:e.vesselName,styles:{cellWidth:35}},{content:e.vesselRegistration??"N/A",styles:{cellWidth:30}},{content:e.infringementIncurred,styles:{cellWidth:65}},{content:e.comments??"N/A",styles:{cellWidth:70}},{content:m()(e.date).format("DD/MM/YYYY"),styles:{cellWidth:25}},{content:e.time,styles:{cellWidth:25}},{content:e.location??"N/A",styles:{cellWidth:30}},{content:e.vesselName,styles:{cellWidth:35}},{content:e.vesselRegistration??"N/A",styles:{cellWidth:30}},{content:e.infringementIncurred,styles:{cellWidth:50}},{content:e.checkedInfringements.join("; "),styles:{cellWidth:82}}]);(0,u.S)({headers:[["Date","Time","Location","Vessel Name","Vessel registration","Owner full name","Owner date of birth","Owner occupation","Owner address","Date","Time","Location","Vessel Name","Vessel registration","Owner full name","Owner phone number","Owner Email Address","Date","Time","Location","Vessel Name","Vessel registration","Infringement incurred","Comments","Date","Time","Location","Vessel Name","Vessel registration","Infringement incurred","List of checked infringements"]],body:e,userOptions:{horizontalPageBreak:!0}},{orientation:"landscape"})}}),(0,s.jsxs)(h.iA,{children:[s.jsx(h.xD,{children:s.jsx(h.SC,{className:"overflow-x-auto",children:y.map(e=>s.jsx(h.ss,{children:e},e))})}),s.jsx(h.RM,{children:s.jsx(D,{reportData:f,isLoading:r&&d})})]})]})}function D({reportData:e,isLoading:t}){return t?s.jsx(h.SC,{children:s.jsx(h.pj,{colSpan:y.length,className:"text-center  h-32",children:"Loading..."})}):0==e.length?s.jsx(h.SC,{children:s.jsx(h.pj,{colSpan:y.length,className:"text-center  h-32",children:"No Data Available"})}):e.map((e,t)=>(0,s.jsxs)(h.SC,{className:"group border-b hover: ",children:[s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block ",children:m()(e.date).format("DD/MM/YY")})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block ",children:e.time})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block",children:e.location??"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block",children:e.vesselName})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block",children:e.vesselRegistration??"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block",children:e.ownerFullName??"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block",children:e.ownerDOB?m()(e.ownerDOB).format("DD/MM/YY"):"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block",children:e.ownerOccupation??"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block",children:e.ownerAddress??"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block",children:e.ownerPhoneNumber??"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block",children:e.ownerEmail??"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block",children:e.infringementIncurred})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("ol",{className:" flex flex-col list-decimal",children:e.checkedInfringements.map(e=>s.jsx("li",{children:e}))})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[7%]",children:s.jsx("div",{className:" inline-block",children:e.comments})})]},"report-item-"+t))}let S=["Vessel","Date","Start time of event","Start location of event","End time of Event","End location of event","Safe speed estimated","Actual speed observed","SOPs completed","Comments"];function E({dateRange:e,vessels:t,lastGenerated:n}){let[i,{called:r,loading:d,data:g}]=(0,c.t)(o.BA,{fetchPolicy:"cache-and-network",onError:e=>{console.error("GET_RESTRICTED_VISIBILIY_EVENT_ENTRIES error",e)}});(0,a.useCallback)(()=>{let n={};null!==e.startDate&&null!==e.endDate&&(n.startDate={gte:e.startDate,lte:e.endDate}),t.length>0&&(n.vehicleID={in:t.map(e=>e.value)}),i({variables:{filter:n}})},[e,t]);let f=(0,a.useMemo)(()=>{let e=g?.readLogBookEntries?.nodes??[];if(0===e.length)return[];let t=[];return e.forEach(function(e){if("Locked"!==e.state||0==e.vehicle.id||0===e.logBookEntrySections.nodes.length)return;let n=e.logBookEntrySections.nodes.reduce((e,t)=>[...e,...t.tripEvents.nodes],[]);0!==n.length&&n.forEach(n=>{let s=n.eventType_RestrictedVisibility;if(null===s.created)return;let i=new Date(s.created),r=s.lookout&&s.navLights&&s.radarWatch&&s.radioWatch&&s.soundSignals,a={vesselName:e.vehicle.title,date:i,startTime:s.crossingTime??"N/A",startLocation:s.startLocation.title??"N/A",endTime:s.crossedTime??"N/A",endLocation:s.startLocation.title??"N/A",actualSpeed:s.approxSafeSpeed?Number(s.approxSafeSpeed):null,estSafeSpeed:s.estSafeSpeed?Number(s.estSafeSpeed):null,sopCompleted:r,comments:s.report};t.push(a)})}),t.sort((e,t)=>e.date.getTime()-t.date.getTime()),t},[r,d,g]);return(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[s.jsx(l.Z,{onDownloadCsv:()=>{if(0===f.length)return;let e=[["vessel","date","start time of event","start location of event","end time of event","end location of event","safe speed estimated","actual speed observed","sops completed","comments"]];f.forEach(t=>{e.push([t.vesselName,m()(t.date).format("DD/MM/YYYY"),t.startTime,t.startLocation,t.endTime,t.endLocation,t.actualSpeed?t.actualSpeed+"":"N/A",t.estSafeSpeed?t.estSafeSpeed+"":"N/A",t.sopCompleted?"Yes":"No",t.comments??"N/A"])}),(0,p.u)(e)},onDownloadPdf:()=>{if(0===f.length)return;let e=f.map(e=>[e.vesselName,m()(e.date).format("DD/M/YY"),e.startTime,e.startLocation,e.endTime,e.endLocation,e.actualSpeed?e.actualSpeed+"":"N/A",e.estSafeSpeed?e.estSafeSpeed+"":"N/A",e.sopCompleted?"Yes":"No",e.comments??"N/A"]);(0,u.S)({headers:[["Vessel","Date","Start time of event","Start location of event","End time of Event","End location of event","Safe speed estimated","Actual speed observed","SOPs completed","Comments"]],body:e})}}),(0,s.jsxs)(h.iA,{children:[s.jsx(h.xD,{children:s.jsx(h.SC,{children:S.map(e=>s.jsx(h.ss,{children:e},e))})}),s.jsx(h.RM,{children:s.jsx(T,{reportData:f,isLoading:r&&d})})]})]})}function T({reportData:e,isLoading:t}){return t?s.jsx(h.SC,{children:s.jsx(h.pj,{colSpan:S.length,className:"text-center  h-32",children:"Loading..."})}):0==e.length?s.jsx(h.SC,{children:s.jsx(h.pj,{colSpan:S.length,className:"text-center  h-32",children:"No Data Available"})}):e.map((e,t)=>(0,s.jsxs)(h.SC,{className:"group border-b  hover: ",children:[s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.vesselName})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:m()(e.date).format("DD/M/YY")})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:e.startTime})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.startLocation})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.endTime})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.endLocation})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.estSafeSpeed??"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.actualSpeed??"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.sopCompleted?"YES":"NO"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block",children:e.comments})})]},"report-item-"+t))}let A=["Vessel","Date","Start time event","Start location of event","Time event ends","Risk analysis completed","Comments"];function L({dateRange:e,vessels:t,lastGenerated:n}){let[i,{called:r,loading:d,data:g}]=(0,c.t)(o.eM,{fetchPolicy:"cache-and-network",onError:e=>{console.error("GET_BAR_CROSSING_EVENT_ENTRIES error",e)}});(0,a.useCallback)(()=>{let n={};null!==e.startDate&&null!==e.endDate&&(n.startDate={gte:e.startDate,lte:e.endDate}),t.length>0&&(n.vehicleID={in:t.map(e=>e.value)}),i({variables:{filter:n}})},[e,t]);let f=e=>e.replace(/<[^>]*>/g,""),x=(0,a.useMemo)(()=>{let e=g?.readLogBookEntries?.nodes??[];if(0===e.length)return[];let t=[];return e.forEach(function(e){if("Locked"!==e.state||0==e.vehicle.id||0===e.logBookEntrySections.nodes.length)return;let n=e.logBookEntrySections.nodes.reduce((e,t)=>[...e,...t.tripEvents.nodes],[]);0!==n.length&&n.forEach(n=>{let s=n.eventType_BarCrossing;if(null===s.created)return;let i=s.crewBriefing&&s.lifeJackets&&s.lookoutPosted&&s.stability&&s.stopAssessPlan&&s.waterTightness&&s.weather,r={vesselName:e.vehicle.title,date:new Date(s.created),startTime:s.time,endTime:s.timeCompleted,startLocation:s.geoLocation.title,riskAnalysisCompleted:i,comments:s.report};t.push(r)})}),t},[r,g,d]);return(0,s.jsxs)("div",{className:"flex flex-col gap-4",children:[s.jsx(l.Z,{onDownloadCsv:()=>{if(0===x.length)return;let e=[["vessel","date","start time event","start location of event","time event ends","risk analysis completed"]];x.forEach(t=>{e.push([t.vesselName,m()(t.date).format("DD/MM/YYYY"),t.startTime??"N/A",t.startLocation??"N/A",t.endTime??"N/A",t.riskAnalysisCompleted?"YES":"NO",t.comments?f(t.comments):""])}),(0,p.u)(e)},onDownloadPdf:()=>{if(0===x.length)return;let e=x.map(e=>[e.vesselName,m()(e.date).format("DD/MM/YY"),e.startTime??"N/A",e.startLocation??"N/A",e.endTime??"N/A",e.riskAnalysisCompleted?"YES":"NO",e.comments?f(e.comments):""]);(0,u.S)({headers:[["Vessel","Date","Start time event","Start location of event","Time event ends","Risk analysis completed","Comments"]],body:e})}}),(0,s.jsxs)(h.iA,{children:[s.jsx(h.xD,{children:s.jsx(h.SC,{children:A.map(e=>s.jsx(h.ss,{children:e},e))})}),s.jsx(h.RM,{children:s.jsx(C,{reportData:x,isLoading:r&&d})})]})]})}function C({reportData:e,isLoading:t}){return t?s.jsx(h.SC,{children:s.jsx(h.pj,{colSpan:A.length,className:"text-center  h-32",children:"Loading..."})}):0==e.length?s.jsx(h.SC,{children:s.jsx(h.pj,{colSpan:A.length,className:"text-center  h-32",children:"No Data Available"})}):e.map((e,t)=>(0,s.jsxs)(h.SC,{className:"group border-b hover: ",children:[s.jsx(h.pj,{className:"px-2 py-3 text-left w-[15%]",children:s.jsx("div",{className:" inline-block",children:e.vesselName})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:m()(e.date).format("DD/MM/YY")})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:e.startTime??"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[15%]",children:s.jsx("div",{className:" inline-block",children:e.startLocation??"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[15%]",children:s.jsx("div",{className:" inline-block",children:e.endTime??"N/A"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[15%]",children:s.jsx("div",{className:" inline-block",children:e.riskAnalysisCompleted?"YES":"NO"})}),s.jsx(h.pj,{className:"px-2 py-3 text-left w-[15%]",children:s.jsx("div",{className:" inline-block",dangerouslySetInnerHTML:{__html:e.comments??""}})})]},"report-item-"+t))}var O=n(69424),Y=n(81524),M=n(25394);let B=[{label:"Arrival/Departure",value:"arrival_departure"},{label:"Refueling/Bunkering",value:"refueling_bunkering"},{label:"Infringement Notices",value:"infringement_notices"},{label:"Restricted Visibility Event",value:"restricted_visibility"},{label:"Bar Crossing Event",value:"bar_crossing"}];function R(){let e=(0,O.useRouter)(),[t,n]=(0,a.useState)(),[i,l]=(0,a.useState)([]),[o,c]=(0,a.useState)({startDate:new Date,endDate:new Date}),[d,m]=(0,a.useState)(null);return(0,s.jsxs)(s.Fragment,{children:[s.jsx(M.Bu,{title:"Activity Reports",actions:s.jsx("div",{className:"flex",children:s.jsx(M.zx,{variant:"back",onClick:()=>e.push("/reporting"),children:"Back"})})}),s.jsx(M.Zb,{className:"mt-8",children:(0,s.jsxs)(M.aY,{children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:justify-between gap-4 mb-8",children:[s.jsx("div",{children:s.jsx(Y.Combobox,{options:B,value:t,onChange:e=>{n(e)},title:"Activity Type",placeholder:"Activity Type",multi:!1})}),s.jsx(r.Z,{onChange:({type:e,data:t})=>{switch(e){case"dateRange":c(t);break;case"report_type":n(t),m(null);break;case"vessels":l(t)}},onClick:()=>{m(new Date().toISOString())}})]}),t?.value==="arrival_departure"&&s.jsx(f,{vessels:i,dateRange:o,lastGenerated:d}),t?.value==="refueling_bunkering"&&s.jsx(j,{vessels:i,dateRange:o,lastGenerated:d}),t?.value==="infringement_notices"&&s.jsx(b,{vessels:i,dateRange:o,lastGenerated:d}),t?.value==="restricted_visibility"&&s.jsx(E,{vessels:i,dateRange:o,lastGenerated:d}),t?.value==="bar_crossing"&&s.jsx(L,{vessels:i,dateRange:o,lastGenerated:d})]})})]})}function P(){let[e,t]=(0,a.useState)(!1),[n,r]=(0,a.useState)(!1);return e&&n?s.jsx(R,{}):e?s.jsx(i.Z,{errorMessage:"Oops You do not have the permission to view this section."}):s.jsx(i.Z,{})}},44517:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var s=n(98768);n(60343);var i=n(32993),r=n(64837);function a({children:e}){return s.jsx(i.default,{children:s.jsx(r.Z,{children:e})})}},49517:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var s=n(98768),i=n(39544),r=n(76915);function a({onDownloadCsv:e,onDownloadPdf:t}){return(0,s.jsxs)("div",{className:"flex gap-3 mb-3",children:[e&&s.jsx(i.Button,{iconLeft:r.Z,type:"button",onClick:e,children:"Download CSV"}),t&&s.jsx(i.Button,{iconLeft:r.Z,type:"button",onClick:t,children:"Download PDF"})]})}},69452:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s});let s=(0,n(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\activity-reports\page.tsx#default`)},71238:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s});let s=(0,n(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,9707,6250,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042],()=>n(98913));module.exports=s})();