(()=>{var e={};e.id=9257,e.ids=[9257],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},85076:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>g,tree:()=>c}),r(94633),r(71238),r(78398),r(57757),r(48045);var s=r(40060),n=r(33581),o=r(57567),i=r.n(o),a=r(51650),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c=["",{children:["reporting",{children:["engine-hours-report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,94633)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\engine-hours-report\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\engine-hours-report\\page.tsx"],d="/reporting/engine-hours-report/page",p={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/reporting/engine-hours-report/page",pathname:"/reporting/engine-hours-report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},81009:(e,t,r)=>{Promise.resolve().then(r.bind(r,31263))},88332:(e,t,r)=>{Promise.resolve().then(r.bind(r,44517))},91973:(e,t,r)=>{"use strict";r.d(t,{u:()=>s});let s=(e,t="report.csv")=>{let r=new Blob([e.map(e=>e.join(",")).join("\n")],{type:"text/csv;charset=utf-8,"}),s=URL.createObjectURL(r);Object.assign(document.createElement("a"),{href:s,download:t}).click(),URL.revokeObjectURL(s)}},2604:(e,t,r)=>{"use strict";r.d(t,{S:()=>i});var s=r(9707),n=r(58774),o=r.n(n);function i(e,t){let r=new s.default(t);o()(r,{head:e.headers,body:e.body,foot:e.footers,margin:5,...e.userOptions}),r.save(e.fileName||"report.pdf")}},31263:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var s=r(98768);r(46776);var n=r(26100),o=r(37042),i=r(88006),a=r(79418),l=r(83179),c=r.n(l),u=r(60343),d=r(49517),p=r(91973),g=r(2604),h=r(69424),x=r(26659),m=r(17203),f=r(25394);let j=["Vessel","Log Entry","Engine Name","Engine Hours Start","Engine Hours End","Engine Run Hours"];function v(){let e=(0,h.useRouter)(),[t,r]=(0,u.useState)([]),[n,l]=(0,u.useState)({startDate:new Date,endDate:new Date}),[v,{called:b,loading:N,data:y}]=(0,a.t)(i.mc,{fetchPolicy:"cache-and-network",onError:e=>{console.error("GET_ENGINE_HOURS_REPORT_ENTRIES error",e)}}),E=(0,u.useMemo)(()=>{let e=y?.readLogBookEntries?.nodes??[];if(0===e.length)return[];let t=[];return e.forEach(e=>{if("Locked"!==e.state||0==e.vehicle.id)return;let r=e.engineStartStop.nodes;0!==r.length&&r.forEach(r=>{if(0==r.engine.id)return;let s={vesselName:e.vehicle.title,logbookDate:new Date(e.startDate),engineName:r.engine.title,hoursStart:r.hoursStart??0,hoursEnd:r.hoursEnd??0,runHours:r.totalHours??0};t.push(s)})}),t},[b,N,y]);return(0,s.jsxs)(s.Fragment,{children:[s.jsx(f.Bu,{title:"Engine Hours Report",actions:s.jsx(f.zx,{variant:"back",iconLeft:m.Z,onClick:()=>e.push("/reporting"),children:"Back"})}),s.jsx(f.Zb,{className:"mt-8",children:(0,s.jsxs)(f.aY,{className:"flex flex-col gap-4",children:[s.jsx(o.Z,{onChange:({type:e,data:t})=>{switch(e){case"dateRange":l(t);break;case"vessels":r(t)}},onClick:()=>{let e={};null!==n.startDate&&null!==n.endDate&&(e.startDate={gte:n.startDate,lte:n.endDate}),t.length>0&&(e.vehicleID={in:t.map(e=>e.value)}),v({variables:{filter:e}})}}),s.jsx(d.Z,{onDownloadCsv:()=>{if(0===E.length)return[];let e=[["vessel","log entry","engine name","engine hours start","engine hours end","engine run hours"]];E.forEach(t=>{e.push([t.vesselName,c()(t.logbookDate).format("DD/MM/YYYY"),t.engineName,t.hoursStart.toLocaleString(),t.hoursEnd.toLocaleString(),t.runHours.toLocaleString()])}),(0,p.u)(e)},onDownloadPdf:()=>{if(0===E.length)return;let e=E.map(function(e){return[e.vesselName+"",c()(e.logbookDate).format("DD/MM/YY")+"",e.engineName+"",e.hoursStart.toLocaleString(),e.hoursEnd.toLocaleString(),e.runHours.toLocaleString()]}),t=E.reduce((e,t)=>e+t.runHours,0);(0,g.S)({headers:[["Vessel","Log Entry","Engine Name","Engine Hours Start","Engine Hours End","Engine Run Hours"]],body:e,footers:[[{colSpan:5,content:"Total Run Hours"},{content:t.toLocaleString()}]],userOptions:{showFoot:"lastPage"}})}}),(0,s.jsxs)(x.iA,{children:[s.jsx(x.xD,{children:s.jsx(x.SC,{children:j.map(e=>s.jsx(x.ss,{children:e},e))})}),s.jsx(S,{isLoading:b&&N,reportData:E})]})]})})]})}function S({reportData:e,isLoading:t}){let r=(0,u.useMemo)(()=>e.reduce((e,t)=>t.runHours+e,0),[e]);return t?s.jsx(x.RM,{children:s.jsx(x.SC,{children:s.jsx(x.pj,{colSpan:j.length,className:"text-center  h-32",children:"Loading..."})})}):0==e.length?s.jsx(x.RM,{children:s.jsx(x.SC,{children:s.jsx(x.pj,{colSpan:j.length,className:"text-center  h-32",children:"No Data Available"})})}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(x.RM,{children:e.map((e,t)=>(0,s.jsxs)(x.SC,{className:"group border-b  hover: ",children:[s.jsx(x.pj,{className:"px-2 py-3 text-left",children:s.jsx("div",{className:" inline-block ml-3",children:e.vesselName})}),s.jsx(x.pj,{className:"px-2 py-3 text-left",children:s.jsx("div",{className:" inline-block ",children:c()(e.logbookDate).format("DD/M/YY")})}),s.jsx(x.pj,{className:"px-2 py-3 text-left",children:s.jsx("div",{className:" inline-block ",children:e.engineName})}),s.jsx(x.pj,{className:"px-2 py-3 text-left",children:s.jsx("div",{className:" inline-block ",children:e.hoursStart.toLocaleString()})}),s.jsx(x.pj,{className:"px-2 py-3 text-left",children:s.jsx("div",{className:" inline-block ",children:e.hoursEnd.toLocaleString()})}),s.jsx(x.pj,{className:"px-2 py-3 text-left",children:s.jsx("div",{className:" inline-block ",children:e.runHours.toLocaleString()})})]},`report-entry-${t}`))}),s.jsx(x.yt,{children:(0,s.jsxs)(x.SC,{className:"group border-b  ",children:[s.jsx(x.pj,{className:"px-2 py-3 text-left",scope:"col",colSpan:5,children:s.jsx("div",{className:"inline-block ml-3",children:"Total Run Hours"})}),s.jsx(x.pj,{className:"px-2 py-3 text-left",children:s.jsx("div",{className:"inline-block ",children:r.toLocaleString()})})]})})]})}function b(){let[e,t]=(0,u.useState)(!1),[r,o]=(0,u.useState)(!1);return e&&r?s.jsx(v,{}):e?s.jsx(n.Z,{errorMessage:"Oops You do not have the permission to view this section."}):s.jsx(n.Z,{})}},44517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(98768);r(60343);var n=r(32993),o=r(64837);function i({children:e}){return s.jsx(n.default,{children:s.jsx(o.Z,{children:e})})}},49517:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var s=r(98768),n=r(39544),o=r(76915);function i({onDownloadCsv:e,onDownloadPdf:t}){return(0,s.jsxs)("div",{className:"flex gap-3 mb-3",children:[e&&s.jsx(n.Button,{iconLeft:o.Z,type:"button",onClick:e,children:"Download CSV"}),t&&s.jsx(n.Button,{iconLeft:o.Z,type:"button",onClick:t,children:"Download PDF"})]})}},94633:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\engine-hours-report\page.tsx#default`)},71238:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,9707,6250,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042],()=>r(85076));module.exports=s})();