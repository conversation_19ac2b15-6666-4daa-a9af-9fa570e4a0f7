(()=>{var e={};e.id=4718,e.ids=[4718],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},55097:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>g,originalPathname:()=>d,pages:()=>u,routeModule:()=>f,tree:()=>p}),r(86576),r(71238),r(78398),r(57757),r(48045);var s=r(40060),n=r(33581),a=r(57567),i=r.n(a),o=r(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let p=["",{children:["reporting",{children:["fuel-tasking-analysis",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,86576)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\fuel-tasking-analysis\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\fuel-tasking-analysis\\page.tsx"],d="/reporting/fuel-tasking-analysis/page",g={require:r,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/reporting/fuel-tasking-analysis/page",pathname:"/reporting/fuel-tasking-analysis",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},27535:(e,t,r)=>{Promise.resolve().then(r.bind(r,34175))},88332:(e,t,r)=>{Promise.resolve().then(r.bind(r,44517))},75546:(e,t,r)=>{"use strict";r.d(t,{Br:()=>g,fU:()=>f,o0:()=>u,p6:()=>p,vq:()=>d});var s=r(83179),n=r.n(s),a=r(7678),i=r.n(a),o=r(14826),l=r.n(o);let p=(e="",t=!0)=>{let r;if(i()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,s,n]=e.split("-"),a=t?r.slice(-2):r,i=parseInt(n,10).toString().padStart(2,"0"),o=parseInt(s,10).toString().padStart(2,"0");return`${i}/${o}/${a}`}if(!(r=e&&"object"==typeof e?n()(e.toString()):n()(e)).isValid())return"";let s=r.format("DD"),a=r.format("MM"),o=t?r.format("YY"):r.format("YYYY");return`${s}/${a}/${o}`},u=(e="",t=!0)=>{let r;if(i()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,s,n]=e.split("-"),a=t?r.slice(-2):r,i=parseInt(n,10).toString().padStart(2,"0"),o=parseInt(s,10).toString().padStart(2,"0");return`${i}/${o}/${a} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[r,s]=e.split(" "),[n,a,i]=r.split("-"),o=t?n.slice(-2):n,l=s.split(":"),p=l[0].padStart(2,"0"),u=l[1].padStart(2,"0"),d=parseInt(i,10).toString().padStart(2,"0"),g=parseInt(a,10).toString().padStart(2,"0");return`${d}/${g}/${o} ${p}:${u}`}if(!(r=e&&"object"==typeof e?n()(e.toString()):n()(e)).isValid())return"";let s=r.format("DD"),a=r.format("MM"),o=t?r.format("YY"):r.format("YYYY"),p=r.format("HH:mm");return`${s}/${a}/${o} ${p}`},d=(e="")=>i()(l()(e))?"":n()(e).format("YYYY-MM-DD HH:mm:ss"),g=(e="")=>i()(l()(e))?new Date:new Date(`${e}T10:00:00Z`),f=(e,t)=>{let r=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),s=e=>e.includes(" ")?e.replace(" ","T"):e,n=e=>{if(!e||"string"!=typeof e)return null;if(r(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(s(e))},a=n(e),i=n(t);return!a||!i||isNaN(a.getTime())||isNaN(i.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):i>a}},34175:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(98768),n=r(37042),a=r(75546),i=r(13842),o=r(94060),l=r(79418),p=r(83179),u=r.n(p),d=r(69424),g=r(60343),f=r(25394);let c=["Date","Tasking incedent/Police number","Tasking start","Tasking end","Fuel used CGNZ","Fuel used SAROP"];function T(){let e=(0,d.useRouter)(),[t,r]=(0,g.useState)(""),[p,T]=(0,g.useState)(!1),[y,v]=(0,g.useState)(!1),[k,m]=(0,g.useState)([]),[x]=(0,l.t)(o.Js,{fetchPolicy:"cache-and-network",onCompleted:e=>{var t=[];e.readOneVessel.logBookEntries.nodes?.map(e=>e.logBookEntrySections.nodes?.map(e=>{t.push(e.id)})),P({variables:{id:t}})},onError:e=>{console.error(e)}}),h=(e,t)=>{var r=t.eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0);let s=e.filter(e=>e.eventType_Tasking.cgop>0||"TaskingPaused"==e.eventType_Tasking.type||"TaskingResumed"==e.eventType_Tasking.type),n=s.filter(e=>e.eventType_Tasking.pausedTaskID==t.eventType_TaskingID&&"TaskingPaused"==e.eventType_Tasking.type),a=s.filter(e=>e.eventType_Tasking.openTaskID==t.eventType_TaskingID&&"TaskingResumed"==e.eventType_Tasking.type),i=s.filter(e=>(e.eventType_Tasking.groupID==t.eventType_TaskingID||e.eventType_Tasking.completedTaskID==t.eventType_TaskingID)&&"TaskingComplete"==e.eventType_Tasking.type);if(n.length>0&&n.forEach(e=>{r-=+e.eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0)}),a.length>0&&a.forEach(e=>{r+=+e.eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0)}),i.length>0)r-=+i[0].eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0);else if(n.length>0){let e=n[n.length-1];r-=+e.eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0)}else r=0;return r<0?0:r},_=(e,t)=>{var r=+t.eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0);let s=e.filter(e=>e.eventType_Tasking.sarop>0||"TaskingPaused"==e.eventType_Tasking.type||"TaskingResumed"==e.eventType_Tasking.type),n=s.filter(e=>e.eventType_Tasking.pausedTaskID==t.eventType_TaskingID&&"TaskingPaused"==e.eventType_Tasking.type),a=s.filter(e=>e.eventType_Tasking.openTaskID==t.eventType_TaskingID&&"TaskingResumed"==e.eventType_Tasking.type),i=s.filter(e=>(e.eventType_Tasking.groupID==t.eventType_TaskingID||e.eventType_Tasking.completedTaskID==t.eventType_TaskingID)&&"TaskingComplete"==e.eventType_Tasking.type);if(n.length>0&&n.forEach(e=>{r-=+e.eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0)}),a.length>0&&a.forEach(e=>{r+=+e.eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0)}),i.length>0)r-=+i[0].eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0);else if(n.length>0){let e=n[n.length-1];r-=+e.eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0)}else r=0;return r<0?0:r},S=(e,t)=>{if(t.eventType_Tasking.id<605)return h(e,t);var r=t.eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0);return(r+=e.filter(e=>"Tasking"===e.eventCategory&&e.eventType_Tasking.cgop>0).map(e=>e.eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0)).reduce((e,t)=>e+t,0))<0?0:r},j=(e,t)=>{if(t.eventType_Tasking.id<605)return _(e,t);var r=+t.eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0);return(r+=e.filter(e=>"Tasking"===e.eventCategory&&e.eventType_Tasking.sarop>0).map(e=>e.eventType_Tasking.fuelLog.nodes.reduce((e,t)=>e+(+t.fuelBefore-+t.fuelAfter),0)).reduce((e,t)=>e+t,0))<0?0:r},[P]=(0,l.t)(o.Zl,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTripReport_LogBookEntrySections.nodes;var r=[];t.forEach(e=>{let t=e.tripEvents.nodes.filter(e=>"Tasking"===e.eventCategory&&"TaskingStartUnderway"!==e.eventType_Tasking.type);e.tripEvents.nodes.filter(e=>"Tasking"===e.eventCategory&&"TaskingStartUnderway"===e.eventType_Tasking.type).forEach(s=>{"Tasking"===s.eventCategory&&r.push({id:s.id,fuelUsedCGNZ:S(t,s),fuelUsedSAROP:j(t,s),incidentNumber:+s.eventType_Tasking?.cgop>0?"CGOP: "+s.eventType_Tasking.cgop:s.eventType_Tasking?.sarop>0?"SAROP: "+s.eventType_Tasking.sarop:"-",fromLocation:e.fromLocation,toLocation:e.toLocation,arrivalTime:e.arriveTime,departureTime:e.departTime,created:e.created})})}),m(r)},onError:e=>{console.error("TripReport_LogBookEntrySection error",e)}}),D=e=>!p||u()(e.created).isAfter(u()(p.startDate))&&u()(e.created).isBefore(u()(p.endDate));return((0,i.uC)(v),y)?(0,s.jsxs)(s.Fragment,{children:[s.jsx(f.Bu,{title:"Fuel Tasking Analysis",actions:s.jsx("div",{className:"flex gap-2.5",children:s.jsx(f.zx,{variant:"back",onClick:()=>e.push("/reporting"),children:"Back"})})}),s.jsx(f.Zb,{className:"mt-8",children:(0,s.jsxs)(f.aY,{className:"flex flex-col gap-4",children:[s.jsx(n.Z,{onChange:e=>{"vessel"===e.type&&e.data?.value&&r(e.data?.value),"dateRange"===e.type&&T(e.data)}}),(0,s.jsxs)(f.iA,{children:[s.jsx(f.xD,{children:s.jsx(f.SC,{children:c.map(e=>s.jsx(f.ss,{children:e},e))})}),s.jsx(f.RM,{children:0==k.length?s.jsx(f.SC,{children:s.jsx(f.pj,{colSpan:c.length,className:"text-center h-32",children:"No Data Available"})}):k.filter(e=>D(e)).map(e=>(0,s.jsxs)(f.SC,{children:[s.jsx(f.pj,{children:(0,a.p6)(e.created)}),s.jsx(f.pj,{children:e.incidentNumber}),(0,s.jsxs)(f.pj,{children:[e.fromLocation?.title?e.fromLocation.title+" - ":"",e.departureTime]}),(0,s.jsxs)(f.pj,{children:[e.toLocation?.title?e.toLocation.title+" - ":"",e.arrivalTime]}),s.jsx(f.pj,{children:e.fuelUsedCGNZ}),s.jsx(f.pj,{children:e.fuelUsedSAROP})]},e.id))})]})]})})]}):s.jsx("div",{})}r(46776);var y=r(26100);function v(){let[e,t]=(0,g.useState)(!1),[r,n]=(0,g.useState)(!1);return e&&r?s.jsx(T,{}):e?s.jsx(y.Z,{errorMessage:"Oops You do not have the permission to view this section."}):s.jsx(y.Z,{})}},44517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(98768);r(60343);var n=r(32993),a=r(64837);function i({children:e}){return s.jsx(n.default,{children:s.jsx(a.Z,{children:e})})}},86576:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\fuel-tasking-analysis\page.tsx#default`)},71238:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042],()=>r(55097));module.exports=s})();