(()=>{var e={};e.id=1955,e.ids=[1955],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},30219:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>c,routeModule:()=>x,tree:()=>l}),t(69581),t(50681),t(78398),t(57757),t(48045);var r=t(40060),i=t(33581),n=t(57567),a=t.n(n),o=t(51650),u={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);t.d(s,u);let l=["",{children:["settings",{children:["crew-duty",{children:["list",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69581)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\crew-duty\\list\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,50681)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\crew-duty\\list\\page.tsx"],d="/settings/crew-duty/list/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/settings/crew-duty/list/page",pathname:"/settings/crew-duty/list",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},31941:(e,s,t)=>{Promise.resolve().then(t.bind(t,84313))},37107:(e,s,t)=>{Promise.resolve().then(t.bind(t,16916))},84313:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(98768);t(60343);var i=t(64837);function n({children:e}){return r.jsx(i.Z,{children:e})}},16916:(e,s,t)=>{"use strict";t.d(s,{default:()=>d});var r=t(98768),i=t(66263),n=t(60343),a=t(17380),o=t(13842),u=t(46776),l=t(69424),c=t(25394);let d=()=>{(0,l.useRouter)();let[e,s]=(0,n.useState)([]);return(0,o.Fo)(e=>{s(e.filter(e=>!e.Archived))}),(0,n.useEffect)(()=>{(0,u.UU)()},[]),(0,r.jsxs)(r.Fragment,{children:[r.jsx(c.Bu,{title:"Crew Duties",actions:r.jsx(r.Fragment,{children:r.jsx(i.default,{href:"/settings/crew-duty/create",children:r.jsx(c.zx,{children:"New Crew Duty"})})})}),e?r.jsx(c.Zb,{className:"mt-8",children:r.jsx(c.aY,{children:(0,r.jsxs)(c.iA,{children:[r.jsx(c.xD,{children:(0,r.jsxs)(c.SC,{children:[r.jsx(c.ss,{children:"Title"}),r.jsx(c.ss,{children:"Abbreviation"})]})}),r.jsx(c.RM,{children:e.map(e=>(0,r.jsxs)(c.SC,{children:[r.jsx(c.pj,{children:r.jsx(i.default,{href:`/settings/crew-duty/edit?id=${e.id}`,className:"",children:e.title})}),r.jsx(c.pj,{children:e.abbreviation})]},e.id))})]})})}):r.jsx(a.hM,{})]})}},69581:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(96141);let i=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\crew-duty\list.tsx#default`),n=()=>r.jsx(i,{})},50681:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\layout.tsx#default`)}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[864,8865,3563,6263,8189,9507,7602,2935,6451,4234,2925,5394,4837,3842,7380],()=>t(30219));module.exports=r})();