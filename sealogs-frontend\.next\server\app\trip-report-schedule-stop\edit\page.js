"use strict";(()=>{var e={};e.id=5130,e.ids=[5130],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{e.exports=require("path")},17360:e=>{e.exports=require("url")},53882:(e,t,r)=>{r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,originalPathname:()=>u,pages:()=>l,routeModule:()=>g,tree:()=>d}),r(56060),r(75756),r(78398),r(57757),r(48045);var s=r(40060),o=r(33581),a=r(57567),n=r.n(a),i=r(51650),p={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>i[e]);r.d(t,p);let d=["",{children:["trip-report-schedule-stop",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,56060)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-report-schedule-stop\\edit\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,75756)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-report-schedule-stop\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-report-schedule-stop\\edit\\page.tsx"],u="/trip-report-schedule-stop/edit/page",c={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/trip-report-schedule-stop/edit/page",pathname:"/trip-report-schedule-stop/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},56060:(e,t,r)=>{r.r(t),r.d(t,{default:()=>a});var s=r(96141),o=r(7498);let a=()=>s.jsx("div",{children:s.jsx(o.Z,{})})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,5880,6451,4234,2925,5394,4837,6342,5657],()=>r(53882));module.exports=s})();