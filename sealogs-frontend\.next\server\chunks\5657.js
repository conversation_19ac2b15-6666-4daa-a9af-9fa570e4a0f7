exports.id=5657,exports.ids=[5657],exports.modules={68624:(e,t,s)=>{Promise.resolve().then(s.bind(s,42331))},26051:(e,t,s)=>{Promise.resolve().then(s.bind(s,35730))},42331:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(98768);s(60343);var a=s(64837);function i({children:e}){return r.jsx(a.Z,{children:e})}},33810:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l});var r=s(98768),a=s(83179),i=s.n(a),o=s(77866);function l({time:e=i()().format("HH:mm"),handleTimeChange:t,timeID:s,fieldName:a="Time",buttonLabel:l="Set To Now",hideButton:n=!1,disabled:c}){return r.jsx(o.j,{disabled:c,value:(()=>{if(!e||!1===e)return new Date;let t=`${i()().format("YYYY-MM-DD")} ${e}`,s=i()(t);return s.isValid()?s.toDate():new Date})(),onChange:t,use24Hour:!0,className:"tablet-sm:w-[300px] w-full",nowButton:!n,nowButtonLabel:l})}},35730:(e,t,s)=>{"use strict";s.d(t,{default:()=>L});var r=s(98768),a=s(94060),i=s(79418),o=s(72548),l=s(69424),n=s(60343),c=s(33810),d=s(83179),u=s.n(d),p=s(7678),m=s.n(p),h=s(14826),x=s.n(h),f=s(76342),g=s(71890),j=s(8750),v=s(39544),S=s(81524),N=s(60797),w=s(26509),C=s(74602),D=s(34376),b=s(57103),y=s(13609),T=s(75535),k=s(3510);let L=()=>{let e=(0,l.useRouter)(),t=(0,l.useSearchParams)(),s=t.get("id")??0,d=t.get("tripReportScheduleID")??0,[p,h]=(0,n.useState)({}),[L,I]=(0,n.useState)([]),[R,H]=(0,n.useState)({}),[$,E]=(0,n.useState)([]),[M,B]=(0,n.useState)({}),[P,Z]=(0,n.useState)(!1),[O,{loading:A}]=(0,i.t)(a.IR,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readGeoLocations.nodes;t&&E(t.map(e=>({value:e.id,label:e.title})))},onError:e=>{console.error("readGeoLocations error",e)}}),[U,{loading:V}]=(0,i.t)(a.BD,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t={...e.readOneTripReportScheduleStop};t&&h(t)},onError:e=>{console.error("readOneTripReportScheduleStop error",e)}}),[z]=(0,i.t)(a.Iq,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTripReportSchedules;t&&I(t.nodes.map(e=>({value:e.id,label:e.title})))},onError:e=>{console.error("ReadTripReportSchedules error",e)}}),[F,{loading:Y}]=(0,o.D)(f.ZnR,{onCompleted:t=>{e.push(`/trip-report-schedules/edit/?id=${p.tripReportScheduleID}`)},onError:e=>{X({variant:"destructive",title:"Error",description:e.message}),console.error("updateTripReportSchedule onError",e.message)}}),[q,{loading:G}]=(0,o.D)(f.O$f,{onCompleted:t=>{e.push(`/trip-report-schedules/edit/?id=${p.tripReportScheduleID}`)},onError:e=>{X({variant:"destructive",title:"Error",description:e.message}),console.error("deleteTripReportSchedule onError",e.message)}}),[_,{loading:J}]=(0,o.D)(f.DEc,{onCompleted:t=>{e.push(`/trip-report-schedules/edit/?id=${p.tripReportScheduleID}`)},onError:e=>{X({variant:"destructive",title:"Error",description:e.message}),console.error("createTripReportScheduleStop onError",e.message)}}),K=async()=>{await z()},Q=async()=>{await U({variables:{id:s}})},W=async()=>{await O()},{toast:X}=(0,D.pm)(),ee=()=>{let e="";return m()(x()(p.title))&&(e+="\nThe title is required."),!!m()(x()(e))||(X({variant:"destructive",title:"Validation Error",description:x()(e)}),!1)},et=async()=>{if(!ee())return;let e={...p};+s>0?(delete e.__typename,delete e.stopLocation,delete e.tripReportSchedule,await F({variables:{input:e}})):await _({variables:{input:e}})},es=async()=>{await q({variables:{id:[p.id]}})};return(0,n.useEffect)(()=>{+s>0?Q():+d>0&&h({id:0,tripReportScheduleID:d,title:"",arriveTime:u()().format("HH:mm:ss"),departTime:u()().format("HH:mm:ss"),pickUp:!0,dropOff:!0,transitID:null,stopLocationID:0}),K(),W()},[s]),(0,n.useEffect)(()=>{m()(p)||m()(L)||H(L.find(e=>e.value===p.tripReportScheduleID))},[p,L]),(0,n.useEffect)(()=>{m()(p)||m()($)||B($.find(e=>e.value===p.stopLocationID))},[p,$]),(0,r.jsxs)("div",{className:"w-full mb-20 md:mb-0",children:[(0,r.jsxs)("div",{className:"px-2 lg:px-4 mt-2 ",children:[r.jsx("div",{className:"flex md:flex-nowrap md:flex-row gap-3 flex-col-reverse flex-wrap justify-between md:items-center items-start",children:(0,r.jsxs)(C.H2,{children:[+s>0?"Edit":"New"," Trip Report Schedule Stop"]})}),r.jsx(w.Separator,{className:"my-4"}),(0,r.jsxs)("div",{className:"my-4",children:[r.jsx(N.Label,{children:"Title"}),r.jsx(g.I,{defaultValue:p.title,onChange:e=>{h({...p,title:e.target.value})},type:"text",placeholder:"Title"})]}),(0,r.jsxs)("div",{className:"my-4",children:[r.jsx(N.Label,{children:"Arrive Time"}),r.jsx(c.Z,{time:p.arriveTime,handleTimeChange:e=>{h({...p,arriveTime:u()(e).format("HH:mm:ss")})},timeID:"trs-arrive-time",fieldName:"Arrive Time"})]}),(0,r.jsxs)("div",{className:"my-4",children:[r.jsx(N.Label,{children:"Depart Time"}),r.jsx(c.Z,{time:p.departTime,handleTimeChange:e=>{h({...p,departTime:u()(e).format("HH:mm:ss")})},timeID:"trs-depart-time",fieldName:"Depart Time"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 my-4",children:[r.jsx(j.Checkbox,{id:"pick-up",checked:p.pickUp,onCheckedChange:e=>{h({...p,pickUp:e})}}),r.jsx(N.Label,{htmlFor:"pick-up",className:"font-medium cursor-pointer",children:"Pick Up"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 my-4",children:[r.jsx(j.Checkbox,{id:"drop-off",checked:p.dropOff,onCheckedChange:e=>{h({...p,dropOff:e})}}),r.jsx(N.Label,{htmlFor:"drop-off",className:"font-medium cursor-pointer",children:"Drop Off"})]}),(0,r.jsxs)("div",{className:"my-4",children:[r.jsx(N.Label,{children:"Transit ID"}),r.jsx(g.I,{defaultValue:p.transitID,onChange:e=>{h({...p,transitID:e.target.value})},type:"text",placeholder:"Transit ID"})]}),(0,r.jsxs)("div",{className:"my-4",children:[r.jsx(N.Label,{children:"Trip Schedule"}),r.jsx(S.Combobox,{options:L,value:R,onChange:e=>{h({...p,tripReportScheduleID:e.value})},placeholder:"Select Trip Report Schedule"})]}),(0,r.jsxs)("div",{className:"my-4",children:[r.jsx(N.Label,{children:"Stop Location"}),r.jsx(S.Combobox,{options:$,value:M,onChange:e=>{h({...p,stopLocationID:e.value})},placeholder:"Select Stop Location"})]}),r.jsx(w.Separator,{className:"my-6"}),(0,r.jsxs)("div",{className:"flex justify-end gap-2",children:[r.jsx(v.Button,{variant:"text",iconLeft:y.Z,onClick:()=>{e.push(`/trip-report-schedules/edit/?id=${p.tripReportScheduleID}`)},children:"Cancel"}),+s>0&&r.jsx(v.Button,{variant:"destructive",iconLeft:T.Z,onClick:()=>{Z(!0)},children:"Delete"}),(0,r.jsxs)(v.Button,{iconLeft:k.Z,onClick:et,isLoading:V||Y||G||J,children:[+s>0?"Update":"Save"," Changes"]})]})]}),r.jsx(b.AlertDialogNew,{openDialog:P,setOpenDialog:Z,handleCreate:es,title:"Delete Trip Stop",description:"Are you sure you want to delete this stop?",actionText:"Delete",variant:"danger"})]})}},77866:(e,t,s)=>{"use strict";s.d(t,{j:()=>v});var r=s(98768),a=s(60343),i=s(83179),o=s.n(i),l=s(72997),n=s(52396),c=s(74932),d=s(39544),u=s(71890),p=s(60797),m=s(15580),h=s(17026),x=s(26509),f=s(56937);let g=e=>e?o().isDayjs(e)?e.toDate():e:new Date,j=(e,t)=>e.getHours()===t.getHours()&&e.getMinutes()===t.getMinutes()&&e.getSeconds()===t.getSeconds();function v({value:e=new Date,onChange:t,showSeconds:s=!1,use24Hour:i=!0,disabled:v=!1,className:S,label:N,labelPosition:w="top",mode:C="single",toValue:D,onToChange:b,nowButton:y=!1,nowButtonLabel:T="Set To Now"}){let[k,L]=a.useState(g(e)),[I,R]=a.useState(()=>"range"!==C?g(e):g(D??e)),[H,$]=a.useState(!1),[E,M]=a.useState(i);a.useEffect(()=>{let t=g(e);j(t,k)||L(t)},[e]),a.useEffect(()=>{if(!D)return;let e=g(D);j(e,I)||R(e)},[D]);let B=k.getHours(),P=k.getMinutes(),Z=k.getSeconds(),O=B>=12,A=E?B:B%12||12,U=e=>e.toString().padStart(2,"0"),V=`${U(A)}:${U(P)}${s?`:${U(Z)}`:""}${E?"":` ${O?"PM":"AM"}`}`,z=(()=>{if("range"!==C)return V;let e=I.getHours(),t=I.getMinutes(),r=I.getSeconds(),a=E?e:e%12||12,i=`${U(a)}:${U(t)}${s?`:${U(r)}`:""}${E?"":` ${e>=12?"PM":"AM"}`}`;return`${V} – ${i}`})(),F=(e,t=!1)=>{let s=new Date(t?I:k);e(s),t?R(s):L(s)},Y=(e,t,s=!1)=>F(s=>{"h"===e?s.setHours(s.getHours()+t):"m"===e?s.setMinutes(s.getMinutes()+t):s.setSeconds(s.getSeconds()+t)},s),q=(e,t)=>{let s=Number(e.target.value);Number.isNaN(s)||F(e=>{"h"===t&&e.setHours(s),"m"===t&&e.setMinutes(s),"s"===t&&e.setSeconds(s)})},G=(0,r.jsxs)(m.Popover,{open:H,onOpenChange:$,children:[r.jsx(m.PopoverTrigger,{className:"group",asChild:!0,children:(0,r.jsxs)("div",{className:(0,f.cn)((0,d.buttonVariants)({variant:"outline"}),"group-hover:bg-muted justify-between",y?"flex-none !pr-1 py-1":"w-full py-0",S),children:[r.jsx(d.Button,{variant:"text",iconLeft:l.Z,className:(0,f.cn)("justify-start !px-0 w-full"),disabled:v,children:z}),y&&r.jsx(d.Button,{className:"h-full rounded py-0",onClick:e=>{e.stopPropagation();let s=o()().toDate();L(s),t?.(s)},disabled:v,children:T})]})}),(0,r.jsxs)(m.PopoverContent,{className:"w-auto p-4",align:"start",children:[r.jsx(p.Label,{htmlFor:"tfmt",label:"24-hour format",rightContent:r.jsx(h.r,{id:"tfmt",checked:E,onCheckedChange:M})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[r.jsx(d.Button,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>Y("h",1),children:r.jsx(n.Z,{className:"h-4 w-4"})}),r.jsx(u.I,{className:"h-9 w-[4rem] text-center",value:U(A),onChange:e=>q(e,"h"),inputMode:"numeric",pattern:"[0-9]*"}),r.jsx(d.Button,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>Y("h",-1),children:r.jsx(c.Z,{className:"h-4 w-4"})})]}),r.jsx("span",{className:"text-xl text-background0",children:":"}),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[r.jsx(d.Button,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>Y("m",1),children:r.jsx(n.Z,{className:"h-4 w-4"})}),r.jsx(u.I,{className:"h-9 w-[4rem] text-center",value:U(P),onChange:e=>q(e,"m"),inputMode:"numeric",pattern:"[0-9]*"}),r.jsx(d.Button,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>Y("m",-1),children:r.jsx(c.Z,{className:"h-4 w-4"})})]}),s&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("span",{className:"text-xl text-background0",children:":"}),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[r.jsx(d.Button,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>Y("s",1),children:r.jsx(n.Z,{className:"h-4 w-4"})}),r.jsx(u.I,{className:"h-9 w-[4rem] text-center",value:U(Z),onChange:e=>q(e,"s"),inputMode:"numeric",pattern:"[0-9]*"}),r.jsx(d.Button,{variant:"ghost",size:"icon",className:"h-8 w-8",onClick:()=>Y("s",-1),children:r.jsx(c.Z,{className:"h-4 w-4"})})]})]}),!E&&r.jsx(d.Button,{variant:"outline",className:"ml-2 h-9 px-3",onClick:()=>F(e=>e.setHours(e.getHours()+12*(O?-1:1))),children:O?"PM":"AM"})]}),"range"===C&&r.jsx(r.Fragment,{children:r.jsx(x.Separator,{className:"my-3"})}),r.jsx(d.Button,{className:"w-full mt-4",onClick:()=>{t?.(k),"range"===C&&b?.(I),$(!1)},children:"Select"})]})]});return N?r.jsx(p.Label,{label:N,position:w,disabled:v,children:G}):G}},75756:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\trip-report-schedule-stop\layout.tsx#default`)},7498:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\schedules\trip-report-schedule-stop-form.tsx#default`)}};