(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4708],{64109:function(e,t,n){Promise.resolve().then(n.bind(n,38702))},52285:function(e,t,n){"use strict";n.d(t,{default:function(){return i.a}});var s=n(88752),i=n.n(s)},88752:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return o},getImageProps:function(){return l}});let s=n(10189),i=n(66903),a=n(30529),r=s._(n(59868));function l(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:r.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,n]of Object.entries(t))void 0===n&&delete t[e];return{props:t}}let o=a.Image},14699:function(e,t,n){"use strict";n.d(t,{Br:function(){return m},fU:function(){return g},o0:function(){return d},p6:function(){return c},vq:function(){return u}});var s=n(65717),i=n.n(s),a=n(17225),r=n.n(a),l=n(61929),o=n.n(l);let c=function(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(r()(o()(t)))return"";if("string"==typeof t&&/^\d{4}-\d{2}-\d{2}$/.test(t)){let[e,s,i]=t.split("-"),a=n?e.slice(-2):e,r=parseInt(i,10).toString().padStart(2,"0"),l=parseInt(s,10).toString().padStart(2,"0");return"".concat(r,"/").concat(l,"/").concat(a)}if(!(e=t&&"object"==typeof t?i()(t.toString()):i()(t)).isValid())return"";let s=e.format("DD"),a=e.format("MM"),l=n?e.format("YY"):e.format("YYYY");return"".concat(s,"/").concat(a,"/").concat(l)},d=function(){let e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=!(arguments.length>1)||void 0===arguments[1]||arguments[1];if(r()(o()(t)))return"";if("string"==typeof t&&/^\d{4}-\d{2}-\d{2}$/.test(t)){let[e,s,i]=t.split("-"),a=n?e.slice(-2):e,r=parseInt(i,10).toString().padStart(2,"0"),l=parseInt(s,10).toString().padStart(2,"0");return"".concat(r,"/").concat(l,"/").concat(a," 00:00")}if("string"==typeof t&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(t)){let[e,s]=t.split(" "),[i,a,r]=e.split("-"),l=n?i.slice(-2):i,o=s.split(":"),c=o[0].padStart(2,"0"),d=o[1].padStart(2,"0"),u=parseInt(r,10).toString().padStart(2,"0"),m=parseInt(a,10).toString().padStart(2,"0");return"".concat(u,"/").concat(m,"/").concat(l," ").concat(c,":").concat(d)}if(!(e=t&&"object"==typeof t?i()(t.toString()):i()(t)).isValid())return"";let s=e.format("DD"),a=e.format("MM"),l=n?e.format("YY"):e.format("YYYY"),c=e.format("HH:mm");return"".concat(s,"/").concat(a,"/").concat(l," ").concat(c)},u=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return r()(o()(e))?"":i()(e).format("YYYY-MM-DD HH:mm:ss")},m=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return r()(o()(e))?new Date:new Date("".concat(e,"T10:00:00Z"))},g=(e,t)=>{let n=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),s=e=>e.includes(" ")?e.replace(" ","T"):e,i=e=>{if(!e||"string"!=typeof e)return null;if(n(e)){let t=new Date().toISOString().split("T")[0];return new Date("".concat(t,"T").concat(e))}return new Date(s(e))},a=i(e),r=i(t);return!a||!r||isNaN(a.getTime())||isNaN(r.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):r>a}},54497:function(e,t,n){"use strict";var s=n(12803),i=n(52285);t.Z=function(e){let{message:t="Loading ...",errorMessage:n=""}=e;return(0,s.jsxs)("div",{className:"h-screen w-full flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{children:(0,s.jsx)(i.default,{src:"/sealogs-loading.gif",alt:"Sealogs Logo",priority:!0,width:300,height:300,unoptimized:!0})}),n?(0,s.jsx)("div",{className:"text-destructive ",children:n}):(0,s.jsx)("div",{children:t})]})}},38702:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return S}});var s=n(12803),i=n(8644),a=n(54497),r=n(20049),l=n(63213),o=n(60333),c=n(77643),d=n(17436),u=n(46624),m=n(30542),g=n(36568),f=n(14699),p=n(10341),h=n(84143),x=e=>{let{memberId:t=0,vesselId:n=0}=e,[c,f]=(0,o.useState)(!0),[p,h]=(0,o.useState)({totalCount:0,hasNextPage:!1,hasPreviousPage:!1}),[x,v]=(0,o.useState)([]),[j,b]=(0,o.useState)([]),[S,N]=(0,o.useState)(0),[w,D]=(0,o.useState)({}),[I,C]=(0,o.useState)([]),[T,P]=(0,o.useState)([]),[k,O]=(0,o.useState)([]),[M,A]=(0,o.useState)([]),[E,{loading:_}]=(0,l.t)(r.ly,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTrainingSessions.nodes,n=Array.from(new Set(t.map(e=>e.vessel.id))).filter(e=>0!=+e),s=Array.from(new Set(t.flatMap(e=>e.trainingTypes.nodes.map(e=>e.id)))),i=Array.from(new Set(t.map(e=>e.trainerID))).filter(e=>0!=+e),a=Array.from(new Set(t.flatMap(e=>e.members.nodes.map(e=>e.id))));t&&(v(t),C(n),P(s),O(i),A(a)),h(e.readTrainingSessions.pageInfo)},onError:e=>{console.error("queryTrainingList error",e)}}),R=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{...w};await E({variables:{filter:t,offset:100*e,limit:100}})},Y=e=>{e<0||e===S||(N(e),V(w),R(e,w))},[Z,{loading:z}]=(0,l.t)(r.qX,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTrainingSessionDues.nodes;t&&b(Object.values(t.filter(e=>e.vessel.seaLogsMembers.nodes.some(t=>t.id===e.memberID)).map(e=>({...e,status:(0,g.nu)(e)})).filter(e=>e.status.isOverdue||!1===e.status.isOverdue&&!0===e.status.dueWithinSevenDays).reduce((e,t)=>{let n="".concat(t.vesselID,"-").concat(t.trainingTypeID,"-").concat(t.dueDate);return e[n]||(e[n]={id:t.id,vesselID:t.vesselID,vessel:t.vessel,trainingTypeID:t.trainingTypeID,trainingType:t.trainingType,dueDate:t.dueDate,status:t.status,members:[]}),e[n].members.push(t.member),e},{})).map(e=>{let t=e.members.reduce((e,t)=>{let n=e.find(e=>e.id===t.id);return n?(n.firstName=t.firstName,n.surname=t.surname):e.push(t),e},[]);return{id:e.id,vesselID:e.vesselID,vessel:e.vessel,trainingTypeID:e.trainingTypeID,trainingType:e.trainingType,status:e.status,dueDate:e.dueDate,members:t}}))},onError:e=>{console.error("readTrainingSessionDues error",e)}}),V=async e=>{let s={};t>0&&(s.memberID={eq:+t}),n>0&&(s.vesselID={eq:+n}),e.vesselID&&(s.vesselID=e.vesselID),e.trainingTypes&&(s.trainingTypeID={eq:e.trainingTypes.id.contains}),e.members&&(s.memberID={eq:e.members.id.contains}),e.date?s.dueDate=e.date:s.dueDate={ne:null},await Z({variables:{filter:s}})};(0,o.useEffect)(()=>{if(c){let e={...w};+t>0&&(e.members={id:{contains:+t}}),D(e),V(e),R(0,e),f(!1)}},[c]);let[G,F]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{F(i.Zu)},[]),G&&((0,i.Fs)("EDIT_TRAINING",G)||(0,i.Fs)("VIEW_TRAINING",G)||(0,i.Fs)("RECORD_TRAINING",G)||(0,i.Fs)("VIEW_MEMBER_TRAINING",G)))?(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)(m.Z,{onChange:e=>{let{type:t,data:n}=e,s={...w};"vessel"===t&&(n?s.vesselID={eq:+n.value}:delete s.vesselID),"trainingType"===t&&(n?s.trainingTypes={id:{contains:+n.value}}:delete s.trainingTypes),"trainer"===t&&(n?s.trainer={id:{eq:+n.value}}:delete s.trainer),"member"===t&&(n?s.members={id:{contains:+n.value}}:delete s.members),"dateRange"===t&&(n.startDate&&n.endDate?s.date={gte:n.startDate,lte:n.endDate}:delete s.date),D(s),V(s),R(0,s)},vesselIdOptions:I,trainingTypeIdOptions:T,trainerIdOptions:k,memberIdOptions:M}),_||z?(0,s.jsx)(d.hM,{}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y,{trainingSessionDues:j}),(0,s.jsx)(u.Z,{page:S,limit:100,visiblePageCount:5,...p,onClick:e=>Y(e)})]})]}):G?(0,s.jsx)(a.Z,{errorMessage:"OopsYou do not have the permission to view this section."}):(0,s.jsx)(a.Z,{})};let v=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(0,h.wu)([{accessorKey:"dueDate",header:"Overdue / Upcoming",cellAlignment:"left",cell:e=>{let{row:t}=e,n=t.original;return(0,s.jsx)(c.default,{href:"/crew-training/info?id=".concat(n.id),className:"font-medium hover:underline",children:(0,f.p6)(n.dueDate)})}},{accessorKey:"trainingType",header:"Training/drill",cellAlignment:"left",breakpoint:"tablet-md",cell:t=>{let{row:n}=t,i=n.original;return(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{children:[i.trainingType.title,!e&&(0,s.jsxs)("span",{className:"ml-1 inline-block tablet-md:hidden",children:[": ",i.vessel.title]})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-2 tablet-md:hidden",children:[(0,s.jsx)(j,{members:i.members,isVesselView:e}),(0,s.jsx)(p.OE,{isOverdue:i.status.isOverdue,isUpcoming:i.status.dueWithinSevenDays,label:i.status.label})]})]})}},...e?[]:[{accessorKey:"vessel",header:"Where",cellAlignment:"left",breakpoint:"tablet-md",cell:e=>{let{row:t}=e;return t.original.vessel.title}}],{accessorKey:"members",header:"Who",cellAlignment:"left",breakpoint:"landscape",cell:t=>{let{row:n}=t,i=n.original;return(0,s.jsx)(j,{members:i.members,isVesselView:e})}},{accessorKey:"status",header:"",cellAlignment:"right",breakpoint:"tablet-md",cell:e=>{let{row:t}=e,n=t.original;return(0,s.jsx)(p.OE,{isOverdue:n.status.isOverdue,isUpcoming:n.status.dueWithinSevenDays,label:n.status.label})}}])},j=e=>{let{members:t,isVesselView:n=!1}=e,i=t.slice(0,3),a=t.slice(3);return(0,s.jsxs)("div",{className:"flex items-center flex-wrap gap-2",children:[i.map(e=>(0,s.jsx)("div",{className:"inline-block border rounded-md px-2 py-1 text-nowrap bg-muted/50 text-sm",children:n?e.firstName||"":"".concat(e.firstName||""," ").concat(e.surname||"").trim()},e.id)),a.length>0&&(0,s.jsxs)(p.J2,{children:[(0,s.jsx)(p.CM,{asChild:!0,children:(0,s.jsxs)(p.zx,{variant:"secondary",size:"sm",children:["+",a.length," more"]})}),(0,s.jsx)(p.yk,{className:"p-2 w-64 max-h-64 overflow-auto",children:(0,s.jsx)("div",{className:"space-y-2",children:a.map(e=>(0,s.jsx)("div",{className:"py-1",children:n?e.firstName||"":"".concat(e.firstName||""," ").concat(e.surname||"").trim()},e.id))})})]})]})},b=e=>e.status.isOverdue?"overdue":e.status.dueWithinSevenDays?"upcoming":"normal",y=e=>{let{trainingSessionDues:t,isVesselView:n=!1}=e;if(!(null==t?void 0:t.length))return null;let i=v(n);return(0,s.jsx)(h.QT,{columns:i,data:t,showToolbar:!1,rowStatus:b,pageSize:t.length})};function S(){let[e,t]=(0,o.useState)(!1),[n,r]=(0,o.useState)(!1),l=()=>{e&&((0,i.Fs)("VIEW_REPORTS",e)?r(!0):r(!1))};return((0,o.useEffect)(()=>{t(i.Zu),l()},[]),(0,o.useEffect)(()=>{l()},[e]),e&&n)?(0,s.jsx)(x,{}):e?(0,s.jsx)(a.Z,{errorMessage:"OopsYou do not have the permission to view this section."}):(0,s.jsx)(a.Z,{})}},15470:function(e,t,n){"use strict";n.d(t,{k:function(){return m}});var s=n(12803),i=n(23800),a=n(49559),r=n(50785),l=n(16489),o=n(99160),c=n(62148),d=n(10341),u=n(79909);function m(e){let{table:t,pageSizeOptions:n=[10,20,30,40,50],showPageSizeSelector:m=!0}=e,g=(0,u.k)();return(0,s.jsx)("div",{className:"flex items-center justify-center px-2",children:(0,s.jsxs)("div",{className:"flex gap-2 items-end phablet:items-center phablet:space-x-6 lg:space-x-8",children:[m&&(0,s.jsx)("div",{className:"flex items-center space-x-2",children:(0,s.jsx)(d.__,{label:"Rows per page",position:g.phablet?"left":"top",htmlFor:"rows-per-page",className:"text-sm",children:(0,s.jsxs)(c.Select,{value:"".concat(t.getState().pagination.pageSize),onValueChange:e=>{t.setPageSize(Number(e))},children:[(0,s.jsx)(c.SelectTrigger,{className:"h-8 w-[70px]",children:(0,s.jsx)(c.SelectValue,{placeholder:t.getState().pagination.pageSize})}),(0,s.jsx)(c.SelectContent,{side:"top",children:n.map(e=>(0,s.jsx)(c.SelectItem,{value:"".concat(e),children:e},e))})]})})}),(0,s.jsxs)("div",{className:"flex w-fit items-center justify-center text-sm",children:["Page ",t.getState().pagination.pageIndex+1," of"," ",t.getPageCount()]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(o.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>t.setPageIndex(0),disabled:!t.getCanPreviousPage(),children:[(0,s.jsx)("span",{className:"sr-only",children:"Go to first page"}),(0,s.jsx)(i.Z,{})]}),(0,s.jsxs)(o.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>t.previousPage(),disabled:!t.getCanPreviousPage(),children:[(0,s.jsx)("span",{className:"sr-only",children:"Go to previous page"}),(0,s.jsx)(a.Z,{})]}),(0,s.jsxs)(o.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>t.nextPage(),disabled:!t.getCanNextPage(),children:[(0,s.jsx)("span",{className:"sr-only",children:"Go to next page"}),(0,s.jsx)(r.Z,{})]}),(0,s.jsxs)(o.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>t.setPageIndex(t.getPageCount()-1),disabled:!t.getCanNextPage(),children:[(0,s.jsx)("span",{className:"sr-only",children:"Go to last page"}),(0,s.jsx)(l.Z,{})]})]})]})})}},28171:function(e,t,n){"use strict";n.d(t,{n:function(){return a}});var s=n(12803),i=n(30542);function a(e){let{table:t,onChange:n}=e;return(0,s.jsx)(i.Z,{onChange:n,table:t})}},84143:function(e,t,n){"use strict";n.d(t,{QT:function(){return v},wQ:function(){return x},wu:function(){return g}});var s=n(12803),i=n(57820),a=n(28171),r=n(15470),l=n(79909),o=n(60333),c=n(27713),d=n(10175),u=n(85034),m=n(49529);function g(e){return e}let f=e=>{switch(e){case"left":return"items-left justify-start justify-items-start text-left";case"right":return"items-right justify-end justify-items-end text-right";default:return"items-center justify-center justify-items-center text-center"}},p=e=>{switch(e){case"overdue":case"upcoming":return"rounded-md [&>td:first-child]:rounded-l-lg [&>td:last-child]:rounded-r-lg";default:return""}},h=e=>{switch(e){case"overdue":return"destructive";case"upcoming":return"warning";default:return}};function x(e){let{columns:t,data:n,showToolbar:g=!0,className:x,pageSize:v=10,pageSizeOptions:j=[10,20,30,40,50],showPageSizeSelector:b=!0,onChange:y,rowStatus:S}=e,[N,w]=o.useState([]),[D,I]=o.useState([]),[C,T]=o.useState({pageIndex:0,pageSize:v}),P=(0,l.k)(),k=o.useMemo(()=>t.filter(e=>e.showOnlyBelow?!P[e.showOnlyBelow]:!e.breakpoint||P[e.breakpoint]),[t,P]);o.useEffect(()=>{T(e=>({...e,pageSize:v}))},[v]);let O=(0,c.b7)({data:n,columns:k,onSortingChange:w,getCoreRowModel:(0,d.sC)(),getPaginationRowModel:(0,d.G_)(),getSortedRowModel:(0,d.tj)(),onColumnFiltersChange:I,getFilteredRowModel:(0,d.vL)(),onPaginationChange:T,state:{sorting:N,columnFilters:D,pagination:C}});return(0,s.jsxs)("div",{className:"space-y-4 pb-8",children:[g&&(0,s.jsx)(u.Zb,{className:"p-2 md:p-auto",children:(0,s.jsx)(a.n,{table:O,onChange:y})}),(0,s.jsxs)(i.iA,{className:x||"p-0 phablet:p-8 lg:p-6 xl:p-8 shadow-none border-0 phablet:border border-border bg-card rounded-lg",children:[O.getHeaderGroups().some(e=>e.headers.some(e=>e.column.columnDef.header&&""!==e.column.columnDef.header))&&(0,s.jsx)(i.xD,{children:O.getHeaderGroups().map(e=>(0,s.jsx)(i.SC,{children:e.headers.map(e=>{let t=e.column.columnDef,n="title"===e.column.id?"left":t.cellAlignment||"center";return(0,s.jsx)(i.ss,{className:"title"===e.column.id?"items-left justify-items-start text-left":f(n),children:e.isPlaceholder?null:(0,c.ie)(e.column.columnDef.header,e.getContext())},e.id)})},e.id))}),(0,s.jsx)(i.RM,{children:O.getRowModel().rows.length?O.getRowModel().rows.map(e=>{let t=S?S(e.original):"normal",n=p(t);return(0,s.jsx)(i.SC,{"data-state":e.getIsSelected()?"selected":void 0,className:(0,m.cn)("mb-4",n),children:e.getVisibleCells().map(e=>{let n=e.column.columnDef,a="title"===e.column.id?"left":n.cellAlignment||"center";return(0,s.jsx)(i.pj,{statusOverlay:"normal"!==t,statusOverlayColor:h(t),className:(0,m.cn)("","title"===e.column.id?"".concat(k.length>1?"w-auto":"w-full"," items-left justify-items-start text-left"):f(a),n.cellClassName),children:(0,s.jsx)("div",{className:(0,m.cn)("flex px-1.5 xs:px-2.5 flex-1",f(a)),children:(0,c.ie)(e.column.columnDef.cell,e.getContext())})},e.id)})},String(e.id))}):(0,s.jsx)(i.SC,{children:(0,s.jsx)(i.pj,{colSpan:k.length,className:"h-24 text-center",children:"No results."})})})]}),(O.getCanPreviousPage()||O.getCanNextPage())&&(0,s.jsx)("div",{className:"flex items-center justify-center phablet:justify-end space-x-2 py-4",children:(0,s.jsx)(r.k,{table:O,pageSizeOptions:j,showPageSizeSelector:b})})]})}let v=x},46624:function(e,t,n){"use strict";var s=n(12803);t.Z=e=>{let{page:t=0,limit:n=0,onClick:i,totalCount:a=0,hasNextPage:r=!1,hasPreviousPage:l=!1,visiblePageCount:o=0}=e,c=n>0?Math.ceil(a/n):0,d=n>0?o:0,u=t-d,m=t;u<0&&(u=0,m=d-1);let g=c-d,f=m+1!==g;m>=g&&(u=0,m=d-1),c<d&&(u=0,m=c-1);let p=Array.from({length:m-u+1},(e,t)=>u+t).slice(-d),h=Array.from({length:(n>0?Math.floor(a/n):0)-g+1},(e,t)=>g+t).slice(0,d);return h=(h=h.filter(e=>!p.includes(e))).filter(e=>e>=0),(p[p.length-1]+1===h[0]||p[p.length-1]-1===h[0]||h.length<=0)&&(f=!1),(0,s.jsx)("div",{className:"flex items-center justify-end p-4",children:(0,s.jsx)("nav",{"aria-label":"Log Entries pagination",children:(0,s.jsxs)("ul",{className:"inline-flex -space-x-px  h-10",children:[(0,s.jsx)("li",{children:l&&t>0&&(0,s.jsx)("button",{onClick:()=>i(0),className:" rounded-s-lg",children:"First"})}),(0,s.jsx)("li",{children:l&&(0,s.jsx)("button",{onClick:()=>i(t-1),className:"",children:"Previous"})}),Array.from({length:p.length},(e,t)=>(0,s.jsx)("li",{children:(0,s.jsx)("button",{onClick:()=>i(p[t]),className:"",children:p[t]+1})},t)),f&&(0,s.jsx)("li",{children:(0,s.jsx)("button",{onClick:()=>i(m+1),className:"".concat("flex items-center justify-center px-4 h-10 leading-tight  border   "),children:"..."})}),Array.from({length:h.length},(e,t)=>(0,s.jsx)("li",{children:(0,s.jsx)("button",{onClick:()=>i(h[t]),className:"",children:h[t]+1})},t)),(0,s.jsx)("li",{children:r&&(0,s.jsx)("button",{onClick:()=>i(t+1),className:"",children:"Next"})}),(0,s.jsx)("li",{children:r&&t*n<a&&(0,s.jsx)("button",{onClick:()=>i(c-1),className:" rounded-e-lg",children:"Last"})})]})})})}}},function(e){e.O(0,[5290,5722,3213,6059,2444,2551,6972,9511,1098,9444,8115,3265,2447,1121,7643,529,7225,4314,9296,3026,1244,49,6203,341,1750,7262,9266,298,7788,1243,542,7436,9396,6953,1744],function(){return e(e.s=64109)}),_N_E=e.O()}]);