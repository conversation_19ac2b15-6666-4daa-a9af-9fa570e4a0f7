(()=>{var e={};e.id=5674,e.ids=[5674,9414],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},25289:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>f,originalPathname:()=>c,pages:()=>d,routeModule:()=>h,tree:()=>u}),r(65784),r(19768),r(78398),r(57757),r(48045);var n=r(40060),s=r(33581),i=r(57567),a=r.n(i),o=r(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let u=["",{children:["incident-records",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,65784)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\incident-records\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,19768)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\incident-records\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\incident-records\\page.tsx"],c="/incident-records/page",f={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/incident-records/page",pathname:"/incident-records",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},6605:(e,t,r)=>{Promise.resolve().then(r.bind(r,331))},88955:(e,t,r)=>{Promise.resolve().then(r.bind(r,99778))},83179:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",r="minute",n="hour",s="week",i="month",a="quarter",o="year",l="date",u="Invalid Date",d=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,c=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,f=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},h="en",p={};p[h]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||"th")+"]"}};var m="$isDayjsObject",g=function(e){return e instanceof $||!(!e||!e[m])},v=function e(t,r,n){var s;if(!t)return h;if("string"==typeof t){var i=t.toLowerCase();p[i]&&(s=i),r&&(p[i]=r,s=i);var a=t.split("-");if(!s&&a.length>1)return e(a[0])}else{var o=t.name;p[o]=t,s=o}return!n&&s&&(h=s),s||!n&&h},x=function(e,t){if(g(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new $(r)},y={s:f,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+f(Math.floor(r/60),2,"0")+":"+f(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),s=t.clone().add(n,i),a=r-s<0,o=t.clone().add(n+(a?-1:1),i);return+(-(n+(r-s)/(a?s-o:o-s))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(u){return({M:i,y:o,w:s,d:"day",D:l,h:n,m:r,s:t,ms:e,Q:a})[u]||String(u||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};y.l=v,y.i=g,y.w=function(e,t){return x(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var $=function(){function f(e){this.$L=v(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[m]=!0}var h=f.prototype;return h.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(y.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(d);if(n){var s=n[2]-1||0,i=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,i)):new Date(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,i)}}return new Date(t)}(e),this.init()},h.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},h.$utils=function(){return y},h.isValid=function(){return this.$d.toString()!==u},h.isSame=function(e,t){var r=x(e);return this.startOf(t)<=r&&r<=this.endOf(t)},h.isAfter=function(e,t){return x(e)<this.startOf(t)},h.isBefore=function(e,t){return this.endOf(t)<x(e)},h.$g=function(e,t,r){return y.u(e)?this[t]:this.set(r,e)},h.unix=function(){return Math.floor(this.valueOf()/1e3)},h.valueOf=function(){return this.$d.getTime()},h.startOf=function(e,a){var u=this,d=!!y.u(a)||a,c=y.p(e),f=function(e,t){var r=y.w(u.$u?Date.UTC(u.$y,t,e):new Date(u.$y,t,e),u);return d?r:r.endOf("day")},h=function(e,t){return y.w(u.toDate()[e].apply(u.toDate("s"),(d?[0,0,0,0]:[23,59,59,999]).slice(t)),u)},p=this.$W,m=this.$M,g=this.$D,v="set"+(this.$u?"UTC":"");switch(c){case o:return d?f(1,0):f(31,11);case i:return d?f(1,m):f(0,m+1);case s:var x=this.$locale().weekStart||0,$=(p<x?p+7:p)-x;return f(d?g-$:g+(6-$),m);case"day":case l:return h(v+"Hours",0);case n:return h(v+"Minutes",1);case r:return h(v+"Seconds",2);case t:return h(v+"Milliseconds",3);default:return this.clone()}},h.endOf=function(e){return this.startOf(e,!1)},h.$set=function(s,a){var u,d=y.p(s),c="set"+(this.$u?"UTC":""),f=((u={}).day=c+"Date",u[l]=c+"Date",u[i]=c+"Month",u[o]=c+"FullYear",u[n]=c+"Hours",u[r]=c+"Minutes",u[t]=c+"Seconds",u[e]=c+"Milliseconds",u)[d],h="day"===d?this.$D+(a-this.$W):a;if(d===i||d===o){var p=this.clone().set(l,1);p.$d[f](h),p.init(),this.$d=p.set(l,Math.min(this.$D,p.daysInMonth())).$d}else f&&this.$d[f](h);return this.init(),this},h.set=function(e,t){return this.clone().$set(e,t)},h.get=function(e){return this[y.p(e)]()},h.add=function(e,a){var l,u=this;e=Number(e);var d=y.p(a),c=function(t){var r=x(u);return y.w(r.date(r.date()+Math.round(t*e)),u)};if(d===i)return this.set(i,this.$M+e);if(d===o)return this.set(o,this.$y+e);if("day"===d)return c(1);if(d===s)return c(7);var f=((l={})[r]=6e4,l[n]=36e5,l[t]=1e3,l)[d]||1,h=this.$d.getTime()+e*f;return y.w(h,this)},h.subtract=function(e,t){return this.add(-1*e,t)},h.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||u;var n=e||"YYYY-MM-DDTHH:mm:ssZ",s=y.z(this),i=this.$H,a=this.$m,o=this.$M,l=r.weekdays,d=r.months,f=r.meridiem,h=function(e,r,s,i){return e&&(e[r]||e(t,n))||s[r].slice(0,i)},p=function(e){return y.s(i%12||12,e,"0")},m=f||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n};return n.replace(c,function(e,n){return n||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return y.s(t.$y,4,"0");case"M":return o+1;case"MM":return y.s(o+1,2,"0");case"MMM":return h(r.monthsShort,o,d,3);case"MMMM":return h(d,o);case"D":return t.$D;case"DD":return y.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return h(r.weekdaysMin,t.$W,l,2);case"ddd":return h(r.weekdaysShort,t.$W,l,3);case"dddd":return l[t.$W];case"H":return String(i);case"HH":return y.s(i,2,"0");case"h":return p(1);case"hh":return p(2);case"a":return m(i,a,!0);case"A":return m(i,a,!1);case"m":return String(a);case"mm":return y.s(a,2,"0");case"s":return String(t.$s);case"ss":return y.s(t.$s,2,"0");case"SSS":return y.s(t.$ms,3,"0");case"Z":return s}return null}(e)||s.replace(":","")})},h.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},h.diff=function(e,l,u){var d,c=this,f=y.p(l),h=x(e),p=(h.utcOffset()-this.utcOffset())*6e4,m=this-h,g=function(){return y.m(c,h)};switch(f){case o:d=g()/12;break;case i:d=g();break;case a:d=g()/3;break;case s:d=(m-p)/6048e5;break;case"day":d=(m-p)/864e5;break;case n:d=m/36e5;break;case r:d=m/6e4;break;case t:d=m/1e3;break;default:d=m}return u?d:y.a(d)},h.daysInMonth=function(){return this.endOf(i).$D},h.$locale=function(){return p[this.$L]},h.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=v(e,t,!0);return n&&(r.$L=n),r},h.clone=function(){return y.w(this.$d,this)},h.toDate=function(){return new Date(this.valueOf())},h.toJSON=function(){return this.isValid()?this.toISOString():null},h.toISOString=function(){return this.$d.toISOString()},h.toString=function(){return this.$d.toUTCString()},f}(),b=$.prototype;return x.prototype=b,[["$ms",e],["$s",t],["$m",r],["$H",n],["$W","day"],["$M",i],["$y",o],["$D",l]].forEach(function(e){b[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),x.extend=function(e,t){return e.$i||(e(t,$,x),e.$i=!0),x},x.locale=v,x.isDayjs=g,x.unix=function(e){return x(1e3*e)},x.en=p[h],x.Ls=p,x.p={},x},e.exports=t()},18479:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,s=Array(n);++r<n;)s[r]=t(e[r],r,e);return s}},60826:e=>{e.exports=function(e){return e.split("")}},829:e=>{e.exports=function(e,t,r,n){for(var s=e.length,i=r+(n?1:-1);n?i--:++i<s;)if(t(e[i],i,e))return i;return -1}},65337:(e,t,r)=>{var n=r(829),s=r(35447),i=r(28026);e.exports=function(e,t,r){return t==t?i(e,t,r):n(e,s,r)}},35447:e=>{e.exports=function(e){return e!=e}},77420:e=>{e.exports=function(e,t,r){var n=-1,s=e.length;t<0&&(t=-t>s?0:s+t),(r=r>s?s:r)<0&&(r+=s),s=t>r?0:r-t>>>0,t>>>=0;for(var i=Array(s);++n<s;)i[n]=e[n+t];return i}},22060:(e,t,r)=>{var n=r(51858),s=r(18479),i=r(55813),a=r(15903),o=1/0,l=n?n.prototype:void 0,u=l?l.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return s(t,e)+"";if(a(t))return u?u.call(t):"";var r=t+"";return"0"==r&&1/t==-o?"-0":r}},49513:(e,t,r)=>{var n=r(70458),s=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(s,""):e}},30482:(e,t,r)=>{var n=r(77420);e.exports=function(e,t,r){var s=e.length;return r=void 0===r?s:r,!t&&r>=s?e:n(e,t,r)}},74783:(e,t,r)=>{var n=r(65337);e.exports=function(e,t){for(var r=e.length;r--&&n(t,e[r],0)>-1;);return r}},41200:(e,t,r)=>{var n=r(65337);e.exports=function(e,t){for(var r=-1,s=e.length;++r<s&&n(t,e[r],0)>-1;);return r}},73211:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},28026:e=>{e.exports=function(e,t,r){for(var n=r-1,s=e.length;++n<s;)if(e[n]===t)return n;return -1}},66095:(e,t,r)=>{var n=r(60826),s=r(73211),i=r(92115);e.exports=function(e){return s(e)?i(e):n(e)}},70458:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},92115:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",s="[^"+t+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",o="(?:"+r+"|"+n+")?",l="[\\ufe0e\\ufe0f]?",u="(?:\\u200d(?:"+[s,i,a].join("|")+")"+l+o+")*",d=RegExp(n+"(?="+n+")|(?:"+[s+r+"?",r,i,a,"["+t+"]"].join("|")+")"+(l+o+u),"g");e.exports=function(e){return e.match(d)||[]}},15903:(e,t,r)=>{var n=r(55296),s=r(48377);e.exports=function(e){return"symbol"==typeof e||s(e)&&"[object Symbol]"==n(e)}},16266:(e,t,r)=>{var n=r(22060);e.exports=function(e){return null==e?"":n(e)}},14826:(e,t,r)=>{var n=r(22060),s=r(49513),i=r(30482),a=r(74783),o=r(41200),l=r(66095),u=r(16266);e.exports=function(e,t,r){if((e=u(e))&&(r||void 0===t))return s(e);if(!e||!(t=n(t)))return e;var d=l(e),c=l(t),f=o(d,c),h=a(d,c)+1;return i(d,f,h).join("")}},3233:(e,t,r)=>{var n=r(16266),s=0;e.exports=function(e){var t=++s;return n(e)+t}},75546:(e,t,r)=>{"use strict";r.d(t,{Br:()=>f,fU:()=>h,o0:()=>d,p6:()=>u,vq:()=>c});var n=r(83179),s=r.n(n),i=r(7678),a=r.n(i),o=r(14826),l=r.n(o);let u=(e="",t=!0)=>{let r;if(a()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,n,s]=e.split("-"),i=t?r.slice(-2):r,a=parseInt(s,10).toString().padStart(2,"0"),o=parseInt(n,10).toString().padStart(2,"0");return`${a}/${o}/${i}`}if(!(r=e&&"object"==typeof e?s()(e.toString()):s()(e)).isValid())return"";let n=r.format("DD"),i=r.format("MM"),o=t?r.format("YY"):r.format("YYYY");return`${n}/${i}/${o}`},d=(e="",t=!0)=>{let r;if(a()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,n,s]=e.split("-"),i=t?r.slice(-2):r,a=parseInt(s,10).toString().padStart(2,"0"),o=parseInt(n,10).toString().padStart(2,"0");return`${a}/${o}/${i} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[r,n]=e.split(" "),[s,i,a]=r.split("-"),o=t?s.slice(-2):s,l=n.split(":"),u=l[0].padStart(2,"0"),d=l[1].padStart(2,"0"),c=parseInt(a,10).toString().padStart(2,"0"),f=parseInt(i,10).toString().padStart(2,"0");return`${c}/${f}/${o} ${u}:${d}`}if(!(r=e&&"object"==typeof e?s()(e.toString()):s()(e)).isValid())return"";let n=r.format("DD"),i=r.format("MM"),o=t?r.format("YY"):r.format("YYYY"),u=r.format("HH:mm");return`${n}/${i}/${o} ${u}`},c=(e="")=>a()(l()(e))?"":s()(e).format("YYYY-MM-DD HH:mm:ss"),f=(e="")=>a()(l()(e))?new Date:new Date(`${e}T10:00:00Z`),h=(e,t)=>{let r=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),n=e=>e.includes(" ")?e.replace(" ","T"):e,s=e=>{if(!e||"string"!=typeof e)return null;if(r(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(n(e))},i=s(e),a=s(t);return!i||!a||isNaN(i.getTime())||isNaN(a.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):a>i}},331:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(98768);r(60343);var s=r(64837);function i({children:e}){return n.jsx(s.Z,{children:e})}},99778:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>$});var n=r(98768),s=r(69424),i=r(60343),a=r(79418),o=r(94733),l=r(43692),u=r(83179),d=r.n(u),c=r(26100),f=r(75546),h=r(29342),p=r(11232),m=r(25394),g=r(66263),v=r(50088),x=r(89546);let y=()=>{let e=(0,s.useRouter)(),[t,r]=(0,i.useState)(!0),[u,y]=(0,i.useState)([]),[$,b]=(0,i.useState)({totalCount:0,hasNextPage:!1,hasPreviousPage:!1}),[S,j]=(0,i.useState)(0),[D,M]=(0,i.useState)({}),[N,{loading:w}]=(0,a.t)(o.c,{fetchPolicy:"cache-and-network",onCompleted:e=>{e&&e.readIncidentRecords&&(y(e.readIncidentRecords.nodes),b(e.readIncidentRecords.pageInfo),r(!1))},onError:e=>{console.error("Error fetching incident records:",e),r(!1)}});(0,i.useEffect)(()=>{k()},[S,D]);let k=()=>{let e={limit:100,offset:100*S};if(Object.keys(D).length>0){let t={};D.vessel&&D.vessel.value&&(t.vesselID={eq:D.vessel.value}),D.dateRange&&(D.dateRange.startDate&&(t.startDate={gte:d()(D.dateRange.startDate).startOf("day").toISOString()}),D.dateRange.endDate&&(t.endDate={lte:d()(D.dateRange.endDate).endOf("day").toISOString()})),e.filter=t}N({variables:e})},C=e=>{j(e)},P=(e,t)=>{M(r=>({...r,[e]:t})),j(0)},I=e=>e&&0!=+e.id?`${e.firstName} ${e.surname}`:"",_=e=>e&&e.nodes&&0!==e.nodes.length?e.nodes.map(e=>`${e.firstName} ${e.surname}`).join(", "):"";return(0,n.jsxs)("div",{className:"w-full py-0",children:[(0,n.jsxs)("div",{className:"flex justify-between mb-4",children:[n.jsx(m.H3,{children:"Incident Records"}),n.jsx("div",{children:n.jsx(v.Z,{text:"New Incident Record",type:"primary",color:"slblue",action:()=>{e.push("/incident-records/create")}})})]}),(0,n.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 mb-4",children:[n.jsx("div",{className:"w-full md:w-1/3",children:n.jsx(p.Z,{isClearable:!0,placeholder:"Filter by Vessel",onChange:e=>P("vessel",e)})}),n.jsx("div",{className:"w-full md:w-2/3",children:n.jsx(h.Z,{placeholder:"Filter by Date Range",onChange:e=>P("dateRange",e)})})]}),n.jsx("div",{className:"shadow-sm w-full border border-slblue-100 my-4 rounded-lg",children:t||w?n.jsx(c.Z,{}):(0,n.jsxs)(n.Fragment,{children:[n.jsx(x.Z,{headings:["Title","Start Date","End Date","Vessel","Reported By","Members to Notify"],showHeader:!0,children:0===u.length?n.jsx("tr",{children:n.jsx("td",{colSpan:6,className:"p-4 text-center",children:"No incident records found"})}):u.map(e=>(0,n.jsxs)("tr",{className:"border-b border-sldarkblue-50 even:bg-sllightblue-50/50 hover:bg-sllightblue-50",children:[n.jsx("td",{className:"p-2",children:n.jsx(g.default,{href:`/incident-records/edit/?id=${e.id}`,className:"group-hover:text-sllightblue-1000",children:e.title||"-"})}),n.jsx("td",{className:"p-2",children:(0,f.p6)(e.startDate)}),n.jsx("td",{className:"p-2",children:(0,f.p6)(e.endDate)}),n.jsx("td",{className:"p-2",children:e.vessel?.title||"-"}),n.jsx("td",{className:"p-2",children:I(e.reportedBy)}),n.jsx("td",{className:"p-2",children:_(e.membersToNotify)})]},e.id))}),n.jsx(l.Z,{page:S,limit:100,visiblePageCount:5,...$,onClick:e=>C(e)})]})})]})},$=()=>n.jsx("div",{children:n.jsx(y,{})})},52241:(e,t,r)=>{"use strict";r.d(t,{P:()=>a});var n=r(60343),s=r(79418),i=r(94060);let a=()=>{let[e,t]=(0,n.useState)({}),[r,a]=(0,n.useState)(!0),[o]=(0,s.t)(i.N5,{fetchPolicy:"cache-and-network",onCompleted:e=>{if(e.readVessels.nodes){let r={};e.readVessels.nodes.forEach(e=>{r[e.id]={...e,vesselPosition:e.vehiclePositions?.nodes?.[0]||null}}),t(r),a(!1)}},onError:e=>{console.error("queryVesselsWithIcons error",e),a(!1)}});return(0,n.useEffect)(()=>{o({variables:{filter:{archived:{eq:!1}}}})},[]),{vesselIconData:e,loading:r,getVesselWithIcon:(t,r)=>e[t]||r||{id:t,title:"Unknown Vessel"}}}},94733:(e,t,r)=>{"use strict";r.d(t,{c:()=>i,x:()=>s});var n=r(45519);let s=(0,n.ZP)`
    query GetIncidentRecord($id: ID!) {
        readOneIncidentRecord(filter: { id: { eq: $id } }) {
            id
            title
            startDate
            endDate
            incidentType
            personsInvolved
            description
            treatment
            contributingFactor
            riskAssessmentReviewed
            notifiable
            location {
                id
                title
                lat
                long
            }
            reportedBy {
                id
                firstName
                surname
            }
            vessel {
                id
                title
            }
            membersToNotify {
                nodes {
                    id
                    firstName
                    surname
                }
            }
            attachments {
                nodes {
                    id
                    title
                }
            }
        }
    }
`,i=(0,n.ZP)`
    query GetIncidentRecords(
        $limit: Int = 100
        $offset: Int = 0
        $filter: IncidentRecordFilterFields = {}
    ) {
        readIncidentRecords(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { startDate: DESC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                title
                startDate
                endDate
                vessel {
                    id
                    title
                }
                reportedBy {
                    id
                    firstName
                    surname
                }
                membersToNotify {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
            }
        }
    }
`},11232:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var n=r(98768),s=r(94060),i=r(79418),a=r(60343),o=r(81524),l=r(52241);let u=({value:e,onChange:t,isClearable:r=!1,className:u="",vesselIdOptions:d=[],filterByTrainingSessionMemberId:c=0})=>{let[f,h]=(0,a.useState)(!0),[p,m]=(0,a.useState)([]),[g,v]=(0,a.useState)([]),[x,y]=(0,a.useState)([]),[$,b]=(0,a.useState)([]),{getVesselWithIcon:S,loading:j}=(0,l.P)(),[D,{loading:M}]=(0,i.t)(s.NS,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readVessels.nodes;t&&b(t.filter(e=>!e.archived&&e.title))},onError:e=>{console.error("queryVesselList error",e)}}),N=async()=>{let e={};c>0&&(e={trainingSessions:{members:{id:{contains:c}}}}),D({variables:{filter:e={...e,archived:{eq:!1}}}})};return(0,a.useEffect)(()=>{if($.length>0&&!j){let e=$.map(e=>{let t=S(e.id,e);return{value:e.id,label:e.title,vessel:t}});e.sort((e,t)=>e.label.localeCompare(t.label)),v(e),m(e)}},[$,j]),(0,a.useEffect)(()=>{f&&(N(),h(!1))},[f]),(0,a.useEffect)(()=>{p.length>0&&y(p.find(t=>t.value===e))},[e,p]),(0,a.useEffect)(()=>{d.length>0?m(g.filter(e=>d.includes(e.value))):m(g)},[d,g]),n.jsx(o.Combobox,{options:p,defaultValues:x,onChange:e=>{y(e),t(e)},isLoading:M&&p&&!f,title:"Vessel",buttonClassName:u,labelClassName:u,placeholder:"Vessel",multi:!1})}},43692:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var n=r(98768);let s=({page:e=0,limit:t=0,onClick:r,totalCount:s=0,hasNextPage:i=!1,hasPreviousPage:a=!1,visiblePageCount:o=0})=>{let l=t>0?Math.ceil(s/t):0,u=t>0?o:0,d=e-u,c=e;d<0&&(d=0,c=u-1);let f=l-u,h=c+1!==f;c>=f&&(d=0,c=u-1),l<u&&(d=0,c=l-1);let p=Array.from({length:c-d+1},(e,t)=>d+t).slice(-u),m=Array.from({length:(t>0?Math.floor(s/t):0)-f+1},(e,t)=>f+t).slice(0,u);return m=(m=m.filter(e=>!p.includes(e))).filter(e=>e>=0),(p[p.length-1]+1===m[0]||p[p.length-1]-1===m[0]||m.length<=0)&&(h=!1),n.jsx("div",{className:"flex items-center justify-end p-4",children:n.jsx("nav",{"aria-label":"Log Entries pagination",children:(0,n.jsxs)("ul",{className:"inline-flex -space-x-px  h-10",children:[n.jsx("li",{children:a&&e>0&&n.jsx("button",{onClick:()=>r(0),className:" rounded-s-lg",children:"First"})}),n.jsx("li",{children:a&&n.jsx("button",{onClick:()=>r(e-1),className:"",children:"Previous"})}),Array.from({length:p.length},(e,t)=>n.jsx("li",{children:n.jsx("button",{onClick:()=>r(p[t]),className:"",children:p[t]+1})},t)),h&&n.jsx("li",{children:n.jsx("button",{onClick:()=>r(c+1),className:"flex items-center justify-center px-4 h-10 leading-tight  border   ",children:"..."})}),Array.from({length:m.length},(e,t)=>n.jsx("li",{children:n.jsx("button",{onClick:()=>r(m[t]),className:"",children:m[t]+1})},t)),n.jsx("li",{children:i&&n.jsx("button",{onClick:()=>r(e+1),className:"",children:"Next"})}),n.jsx("li",{children:i&&e*t<s&&n.jsx("button",{onClick:()=>r(l-1),className:" rounded-e-lg",children:"Last"})})]})})})}},89546:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var n=r(98768);let s=e=>(0,n.jsxs)("table",{className:" table-auto w-full ",cellPadding:"10",children:[n.jsx("thead",{children:n.jsx("tr",{className:e.showHeader?"":"hidden",children:e.headings.map((t,r)=>n.jsx("th",{scope:"col",className:`pb-3 pt-6 px-2 ${0===r?"rounded-tl-lg":" "}   ${e.headings.length===r+1?"rounded-tr-lg":" "}
                                ${t.includes(":")&&"last"===t.split(":")[1]?"rounded-tr-lg":""}
                                ${t.includes(":")&&"smhidden"===t.split(":")[1]?"hidden sm:block":""}
                                ${t.includes(":")&&"left"===t.split(":")[1]?"text-left":""}
                                ${t.includes(":")&&"firstHead"===t.split(":")[1]?"text-left text-nowrap font-thin  md: lg:text-2xl pl-6 rounded-tl-lg":""}  `,children:t.includes(":")?t.split(":")[0]:t},r))})}),n.jsx("tbody",{className:`  text-foreground ${e?.bodyClass}`,children:e.children})]})},19768:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\incident-records\layout.tsx#default`)},65784:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\incident-records\page.tsx#default`)},9999:(e,t,r)=>{"use strict";r.d(t,{a:()=>s});var n=r(60343);function s(e,t,{getInitialValueInEffect:r}={getInitialValueInEffect:!0}){let[s,i]=(0,n.useState)(!!r&&t);return(0,n.useRef)(null),s||!1}},79320:(e,t,r)=>{"use strict";r.d(t,{t:()=>a});var n=r(93140),s=r(69359),i=r(79824);function a(e,t){let r=(0,i.Q)(e);return isNaN(+r)?(0,n.L)(e,NaN):(null!=t.year&&r.setFullYear(t.year),null!=t.month&&(r=(0,s.q)(r,t.month)),null!=t.date&&r.setDate(t.date),null!=t.hours&&r.setHours(t.hours),null!=t.minutes&&r.setMinutes(t.minutes),null!=t.seconds&&r.setSeconds(t.seconds),null!=t.milliseconds&&r.setMilliseconds(t.milliseconds),r)}},99491:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},84961:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},72997:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},46020:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},23379:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},7671:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[864,8865,3563,6263,8189,9507,7602,6451,4234,2925,5394,4837,88,8712],()=>r(25289));module.exports=n})();