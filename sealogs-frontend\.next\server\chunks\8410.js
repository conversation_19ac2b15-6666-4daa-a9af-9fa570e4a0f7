"use strict";exports.id=8410,exports.ids=[8410],exports.modules={91973:(e,t,a)=>{a.d(t,{u:()=>s});let s=(e,t="report.csv")=>{let a=new Blob([e.map(e=>e.join(",")).join("\n")],{type:"text/csv;charset=utf-8,"}),s=URL.createObjectURL(a);Object.assign(document.createElement("a"),{href:s,download:t}).click(),URL.revokeObjectURL(s)}},2604:(e,t,a)=>{a.d(t,{S:()=>r});var s=a(9707),n=a(58774),i=a.n(n);function r(e,t){let a=new s.default(t);i()(a,{head:e.headers,body:e.body,foot:e.footers,margin:5,...e.userOptions}),a.save(e.fileName||"report.pdf")}},33849:(e,t,a)=>{a.d(t,{Z:()=>l});var s=a(98768);a(60343);var n=a(47520);a(30854);var i=a(56937);let r=(0,n.default)(async()=>{},{loadableGenerated:{modules:["app\\ui\\editor.tsx -> react-quill"]},ssr:!1});function l(e,t){return s.jsx(r,{value:e.content,placeholder:e.placeholder,onChange:e.handleEditorChange,onBlur:e.handleEditorBlur,modules:{toolbar:[[{header:[1,2,3,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"}],["link","image"],[{align:[]},{color:[]}],["clean"]]},formats:["header","bold","italic","underline","strike","blockquote","list","bullet","link","image","align","color","code-block"],className:(0,i.cn)("w-full min-h-60 mb-2 bg-card border overflow-auto text-input border-border rounded-lg",t)})}},84340:(e,t,a)=>{a.d(t,{nA:()=>R,OE:()=>_,ZP:()=>Z});var s=a(98768),n=a(60343),i=a(79418),r=a(94060),l=a(17380),o=a(66263),d=a(83179),c=a.n(d),u=a(7678),m=a.n(u),h=a(69424),p=a(46776),g=a(28271),x=a(91973),f=a(2604),v=a(51742),j=a(30905),y=a(27514),D=a(22995),b=a(13842);a(14826);var C=a(9210),N=a(34376);let w=()=>{let e="true"===(0,h.useSearchParams)().get("archived"),{isMobile:t}=(0,D.Ap)(),[a,l]=(0,n.useState)(),[o,d]=(0,n.useState)(),[c,u]=(0,n.useState)(),[m,v]=(0,n.useState)({}),[j,w]=(0,n.useState)(!0),[k,A]=(0,n.useState)([]),[S,T]=(0,n.useState)(!1),[I,$]=(0,n.useState)(!1),M=(0,h.useRouter)(),{toast:O}=(0,N.pm)(),F=()=>{S&&((0,p.Fs)("EDIT_TASK",S)?$(!0):$(!1))};(0,n.useEffect)(()=>{T(p.Zu),F()},[]),(0,n.useEffect)(()=>{F()},[S]);let[E]=(0,i.t)(r.DB,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readComponentMaintenanceChecks.nodes;t&&Y(t)},onError:e=>{console.error("queryMaintenanceChecks error",e)}});(0,n.useEffect)(()=>{j&&(L(),w(!1))},[j]);let L=async(e={...m},t=k)=>{if(t.length>0){let a=t.map(async t=>await E({variables:{filter:{...e,...t}}})),s=await Promise.all(a);Y(s=(s=(s=s.filter(e=>e.data.readComponentMaintenanceChecks.nodes.length>0)).flatMap(e=>e.data.readComponentMaintenanceChecks.nodes)).filter((e,t,a)=>a.findIndex(t=>t.id===e.id)===t))}else await E({variables:{filter:e}})};(0,b.sy)(e=>{let t=e.filter(e=>!e.archived).map(e=>({...e}));t.push({title:"Other",id:0}),d(t)});let Y=t=>{let a=t.filter(e=>!1===e.archived).map(e=>({...e,isOverDue:(0,b.AT)(e)})),s=t.filter(e=>!0===e.archived).map(e=>({...e,isOverDue:(0,b.AT)(e)}));console.log("activeTasks",a,e,s),e?l(s):l(a),q(Array.from(new Set(a.filter(e=>e.assignedToID>0).map(e=>e.assignedToID))))},[P]=(0,i.t)(r.rd,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readSeaLogsMembers.nodes;t&&u(t)},onError:e=>{console.error("queryCrewMemberInfo error",e)}}),q=async e=>{await P({variables:{crewMemberIDs:e.length>0?e:[0]}})};return(0,s.jsxs)(y.DropdownMenu,{children:[s.jsx(y.DropdownMenuTrigger,{asChild:!0,children:s.jsx(C.HV,{size:36})}),(0,s.jsxs)(y.DropdownMenuContent,{side:t?"bottom":"right",align:t?"end":"start",children:[s.jsx(y.DropdownMenuItem,{onClick:()=>{if(!1===(a&&o))return;let e=[["task","location","assigned to","due"]];a.filter(e=>"Save_As_Draft"!==e.status).forEach(t=>{e.push([t.name,o?.filter(e=>e?.id==t.basicComponentID).map(e=>e.title).join(", "),c.filter(e=>e.id===t.assignedToID).map((e,t)=>`${e.firstName} ${e.surname}`).join(", "),(0,g.N)(t.isOverDue)])}),(0,x.u)(e)},children:"Download CSV"}),s.jsx(y.DropdownMenuItem,{onClick:()=>{if(!1===(a&&o))return;let e=a.filter(e=>"Save_As_Draft"!==e.status).map(e=>[e.name,o?.filter(t=>t?.id==e.basicComponentID).map(e=>e.title).join(", "),c.filter(t=>t.id===e.assignedToID).map((e,t)=>`${e.firstName} ${e.surname}`).join(", "),(0,g.N)(e.isOverDue)]);(0,f.S)({headers:[["Task Name","Location","Assigned To","Due"]],body:e})},children:"Download PDF"}),s.jsx(y.DropdownMenuSeparator,{}),s.jsx(y.DropdownMenuItem,{onClick:()=>{if(!I){O({description:"You do not have permission to edit this section",variant:"destructive"});return}M.push("/maintenance/new?redirectTo=/maintenance")},children:"New Task"}),(0,s.jsxs)(y.DropdownMenuItem,{onClick:()=>{window.open(e?"/maintenance/":"/maintenance/?archived=true&redirectTo=/maintenance","_self")},children:["View ",e?"Active":"Archived"," Tasks"]})]})]})};var k=a(39650),A=a(69748),S=a(25394),T=a(99891),I=a(52241),$=a(50058),M=a(45519);let O=(0,M.ZP)`
    query ReadVessels(
        $limit: Int
        $offset: Int
        $filter: VesselFilterFields = {}
        $entryFilter: LogBookEntryFilterFields = {
            state: { in: [Editing, Reopened] }
        }
    ) {
        readVessels(filter: $filter, limit: $limit, offset: $offset) {
            nodes {
                id
                archived
                title
                registration
                callSign
                icon
                iconMode
                photoID
                minCrew
                showOnDashboard
                vesselType
                logBookID
                vehiclePositions(sort: { created: DESC }, limit: 1) {
                    nodes {
                        id
                        lat
                        long
                        geoLocation {
                            id
                            title
                            lat
                            long
                        }
                    }
                }
                parentComponent_Components {
                    nodes {
                        basicComponent {
                            id
                            title
                            componentCategory
                        }
                        parentComponent {
                            id
                            title
                        }
                    }
                }
                logBookEntries(filter: $entryFilter, limit: 1) {
                    nodes {
                        id
                    }
                }
                componentMaintenanceChecks {
                    nodes {
                        archived
                        name
                        expires
                        status
                        completed
                        maintenanceSchedule {
                            __typename
                        }
                    }
                }
            }
        }
    }
`;var F=a(70413),E=a(56937);let L=(e,t)=>{if(!e&&!t)return"??";let a=e?.charAt(0)?.toUpperCase()||"",s=t?.charAt(0)?.toUpperCase()||"";return`${a}${s}`||"??"},Y=e=>{if(!e)return"??";let t=e.split(" ").filter(e=>e.length>0);return 1===t.length?t[0].substring(0,2).toUpperCase():t.slice(0,2).map(e=>e.charAt(0).toUpperCase()).join("")},P=(e,t)=>t?.find(t=>t.id===e.toString()),q=(e,t)=>t?.find(t=>t.id===e),_=({maintenanceCheck:e})=>{let t=e?.isOverDue?.status==="High",a="";return(e?.isOverDue?.status&&["High","Medium","Low"].includes(e.isOverDue.status)?a=e?.isOverDue?.days:e?.isOverDue?.status==="Completed"&&e?.isOverDue?.days==="Save As Draft"?a=e?.isOverDue?.days:e?.isOverDue?.status==="Upcoming"?a=e?.isOverDue?.days:e?.isOverDue?.status==="Completed"&&m()(e?.isOverDue?.days)?a=e?.isOverDue?.status:e?.isOverDue?.status!=="Completed"||m()(e?.isOverDue?.days)||e?.isOverDue?.days==="Save As Draft"||(a=e?.isOverDue?.days),t)?s.jsx("span",{className:"alert w-fit inline-block text-nowrap rounded-md text-sm xs:text-base py-0.5 px-2 xs:px-3 xs:py-1",children:a}):s.jsx("span",{className:"text-nowrap text-sm xs:text-base",children:a})};function R({maintenanceChecks:e,vessels:t,crewInfo:a,showVessel:n=!1}){let i=(0,h.usePathname)(),r=(0,h.useSearchParams)(),{vesselIconData:l,getVesselWithIcon:d}=(0,I.P)(),u=(0,v.wu)([{accessorKey:"title",header:({column:e})=>s.jsx(j.u,{column:e,title:"Title"}),cell:({row:e})=>{let t=e.original;return(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("div",{className:"flex items-center gap-2",children:s.jsx(o.default,{href:`/maintenance?taskID=${t.id}&redirect_to=${i}?${r.toString()}`,children:t.name??`Task #${t.id} (No Name) - ${c()(t.created).format("DD/MM/YYYY")}`})}),!n&&s.jsx("div",{className:"lg:hidden",children:t.basicComponent?.id>0&&s.jsx(o.default,{href:`/vessel/info?id=${t.basicComponent.id}`,className:"text-sm text-muted-foreground hover:text-curious-blue-400",children:t.basicComponent.title})}),(0,s.jsxs)("div",{className:"md:hidden space-y-2",children:[t.assignedTo?.id>0&&(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsxs)("span",{className:"text-muted-foreground",children:["Assigned to:"," "]}),s.jsx(o.default,{href:`/crew/info?id=${t.assignedTo.id}`,className:"hover:text-curious-blue-400",children:t.assignedTo.name})]}),s.jsx("div",{children:s.jsx(_,{maintenanceCheck:t})})]})]})},sortingFn:(e,t)=>{let a=e?.original?.name||"",s=t?.original?.name||"";return a.localeCompare(s)}},...n?[]:[{accessorKey:"location",header:({column:e})=>s.jsx(j.u,{column:e,title:"Location"}),cellAlignment:"left",cell:({row:e})=>{let a=e.original,n=q(a.basicComponent?.id||0,t);return s.jsx("div",{className:"hidden lg:block",children:a.basicComponent?.id>0&&(0,s.jsxs)("div",{className:"flex items-center gap-2.5",children:[s.jsx(A.Avatar,{size:"sm",variant:"secondary",children:s.jsx(A.AvatarFallback,{className:"text-xs",children:Y(n?.title||a.basicComponent.title||void 0)})}),s.jsx(o.default,{href:`/vessel/info?id=${a.basicComponent.id}`,className:"hover:text-curious-blue-400",children:a.basicComponent.title})]})})},sortingFn:(e,t)=>{let a=e?.original?.basicComponent?.title||"",s=t?.original?.basicComponent?.title||"";return a.localeCompare(s)}}],{accessorKey:"assigned",header:({column:e})=>s.jsx(j.u,{column:e,title:"Assigned to"}),cellAlignment:"left",breakpoint:"tablet-sm",cell:({row:e})=>{let t=e.original,n=P(t.assignedTo?.id||0,a);return t.assignedTo?.id&&console.log("✅ Crew lookup:",{assignedToId:t.assignedTo.id,crewFound:!!n,crewName:n?`${n.firstName} ${n.surname}`:"Not found"}),s.jsx("div",{className:"hidden md:block",children:t.assignedTo?.id>0&&(0,s.jsxs)("div",{className:"flex items-center gap-2.5",children:[s.jsx(A.Avatar,{className:"h-8 w-8",children:s.jsx(A.AvatarFallback,{className:"text-xs",children:L(n?.firstName,n?.surname)})}),s.jsx(o.default,{href:`/crew/info?id=${t.assignedTo.id}`,className:"hover:text-curious-blue-400 hidden lg:block",children:t.assignedTo.name})]})})},sortingFn:(e,t)=>{let a=e?.original?.assignedTo?.name||"",s=t?.original?.assignedTo?.name||"";return a.localeCompare(s)}},{accessorKey:"inventory",header:"Inventory item",cellAlignment:"left",breakpoint:"tablet-lg",cell:({row:e})=>{let t=e.original;return s.jsx("div",{className:"hidden md:block",children:t.inventory?.id>0&&s.jsx(o.default,{href:`/inventory/view?id=${t.inventory.id}`,className:"hover:text-curious-blue-400",children:t.inventory.item})})}},{accessorKey:"status",header:({column:e})=>s.jsx(j.u,{column:e,title:"Status"}),cellAlignment:"right",cell:({row:e})=>{let t=e.original;return s.jsx("div",{className:"hidden md:block",children:s.jsx(_,{maintenanceCheck:t})})},sortingFn:(e,t)=>{let a=e?.original?.isOverDue?.days||"",s=t?.original?.isOverDue?.days||"";return a.localeCompare(s)}}]);return s.jsx(v.wQ,{columns:u,data:e,pageSize:20,showToolbar:!1})}function Z(){let e;let[t,a]=(0,n.useState)(),[d,u]=(0,n.useState)(),[m,p]=(0,n.useState)(),[g,x]=(0,n.useState)(),[f,y]=(0,n.useState)({}),[D,b]=(0,n.useState)(!0),[N,M]=(0,n.useState)(null),[Y,R]=(0,n.useState)(!1),[Z,U]=(0,n.useState)(!1),B=(0,h.usePathname)(),K=(0,h.useSearchParams)(),{getVesselWithIcon:V}=(0,I.P)(),H=(0,$.k)(),[z]=(0,i.t)(r.gQ,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readComponentMaintenanceCheckList[0].list;t&&Q(t)},onError:e=>{console.error("queryMaintenanceChecks error",e)}});((e,t=!1)=>{let[a,s]=(0,n.useState)(!0),r=new F.Z;(0,n.useEffect)(()=>{a&&(o(),s(!1))},[a]);let[l]=(0,i.t)(O,{fetchPolicy:"cache-and-network",onCompleted:t=>{t.readVessels.nodes&&e(t.readVessels.nodes)},onError:e=>{console.error("queryVessels error",e)}}),o=async()=>{t?e(await r.getAll()):await l({variables:{limit:200,offset:0}})}})(e=>{let t=e.filter(e=>!e.archived).map(e=>({...e}));t.push({title:"Other",id:0}),p(t)});let Q=e=>{a(e),u(e),J(Array.from(new Set(e.filter(e=>e.assignedTo.id>0).map(e=>e.assignedTo.id))))},[G]=(0,i.t)(r.rd,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readSeaLogsMembers.nodes;t&&x(t)},onError:e=>{console.error("queryCrewMemberInfo error",e)}}),J=async e=>{await G({variables:{crewMemberIDs:e.length>0?e:[0]}})},W=e=>{switch(e){case"High":return"text-destructive/80 hover:text-destructive";case"Upcoming":return"text-warning/80 hover:text-warning";default:return"hover:text-curious-blue-400"}},X=(e=g||[],(0,v.wu)([{accessorKey:"title",header:({column:e})=>s.jsx(j.u,{column:e,title:"Title"}),cell:({row:t})=>{let a=t.original,n=P(a.assignedTo?.id||0,e);q(a.basicComponent?.id||0,m);let i=a.isOverDue?.status;a.isOverDue?.day;let r=s.jsx(o.default,{href:`/maintenance?taskID=${a.id}&redirect_to=${B}?${K.toString()}`,className:(0,E.cn)("leading-tight truncate",W(i)),children:a.name??`Task #${a.id} (No Name) - ${c()(a.created).format("DD/MM/YYYY")}`});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"tablet-sm:hidden inline-flex overflow-auto items-center gap-1.5",children:[a.assignedTo?.id>0&&s.jsx(A.Avatar,{variant:"secondary",className:"h-8 w-8",children:s.jsx(A.AvatarFallback,{className:"text-xs",children:L(n?.firstName,n?.surname)})}),(0,s.jsxs)("div",{className:"grid",children:[r,a.inventory?.id>0&&s.jsx("div",{className:"flex flex-col",children:s.jsx(o.default,{href:`/inventory/view?id=${a.inventory.id}`,className:"hover:text-curious-blue-400 text-sm",children:a.inventory.item})})]})]}),(0,s.jsxs)("div",{className:"hidden tablet-sm:block",children:[s.jsx("div",{className:"grid items-center gap-2",children:r}),s.jsx("div",{className:"md:hidden",children:a.basicComponent?.id>0&&s.jsx(o.default,{href:`/vessel/info?id=${a.basicComponent.id}`,className:"text-sm text-muted-foreground hover:text-curious-blue-400",children:a.basicComponent.title})})]})]})},sortingFn:(e,t)=>{let a=e?.original?.name||"",s=t?.original?.name||"";return a.localeCompare(s)}},{accessorKey:"location",header:({column:e})=>s.jsx(j.u,{column:e,title:"Location"}),cellAlignment:"left",breakpoint:"laptop",cell:({row:e})=>{let t=e.original,a=q(t.basicComponent?.id||0,m),n=V(t.basicComponent?.id||0,a);return s.jsx(s.Fragment,{children:t.basicComponent?.id>0&&(0,s.jsxs)("div",{className:"flex items-center gap-2.5",children:[(0,s.jsxs)(S.u,{children:[s.jsx(S.aJ,{children:s.jsx("div",{className:"min-w-fit",children:s.jsx(T.Z,{vessel:n})})}),s.jsx(S._v,{children:t.basicComponent.title})]},a?.id),s.jsx(o.default,{href:`/vessel/info?id=${t.basicComponent.id}`,className:"hover:text-curious-blue-400",children:t.basicComponent.title})]})})},sortingFn:(e,t)=>{let a=e?.original?.basicComponent?.title||"",s=t?.original?.basicComponent?.title||"";return a.localeCompare(s)}},{accessorKey:"assigned",header:({column:e})=>s.jsx(j.u,{column:e,title:"Assigned to"}),cellAlignment:"left",breakpoint:"tablet-lg",cell:({row:t})=>{let a=t.original,n=P(a.assignedTo?.id||0,e);return s.jsx(s.Fragment,{children:a.assignedTo?.id>0&&(0,s.jsxs)("div",{className:"flex items-center gap-2.5",children:[s.jsx(A.Avatar,{variant:"secondary",className:"h-8 w-8",children:s.jsx(A.AvatarFallback,{className:"text-xs",children:L(n?.firstName,n?.surname)})}),s.jsx(o.default,{href:`/crew/info?id=${a.assignedTo.id}`,className:"hover:text-curious-blue-400 hidden tablet-md:block",children:a.assignedTo.name})]})})},sortingFn:(e,t)=>{let a=e?.original?.assignedTo?.name||"",s=t?.original?.assignedTo?.name||"";return a.localeCompare(s)}},{accessorKey:"inventory",header:"Inventory item",cellAlignment:"left",breakpoint:"tablet-sm",cell:({row:e})=>{let t=e.original;return s.jsx(s.Fragment,{children:t.inventory?.id>0?s.jsx(o.default,{href:`/inventory/view?id=${t.inventory.id}`,className:"hover:text-curious-blue-400",children:t.inventory.item}):s.jsx("span",{children:"-"})})}},{accessorKey:"status",header:({column:e})=>s.jsx(j.u,{column:e,title:"Status"}),cellAlignment:"right",cell:({row:e})=>{let t=e.original;if(!t)return s.jsx("div",{children:"-"});let a=t.isOverDue?.status,n=t.isOverDue?.day;return s.jsx(s.Fragment,{children:"High"===a?H["tablet-sm"]?s.jsx(_,{maintenanceCheck:t}):s.jsx("div",{className:` items-end w-fit text-sm xs:text-base py-0.5 px-1 xs:px-3 xs:py-1
                                                    ${"High"===a?"alert whitespace-nowrap":""}
                                                    `,children:-1*n+" days ago"}):s.jsx(_,{maintenanceCheck:t})})},sortingFn:(e,t)=>{let a=e?.original?.isOverDue?.days||"",s=t?.original?.isOverDue?.days||"";return a.localeCompare(s)}}]));return(0,s.jsxs)(s.Fragment,{children:[s.jsx(k.ListHeader,{title:"Maintenance",icon:s.jsx(C.y5,{className:"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]"}),actions:s.jsx(w,{}),titleClassName:""}),s.jsx("div",{className:"mt-16",children:t&&m?s.jsx(v.wQ,{columns:X,data:d||[],pageSize:20,onChange:({type:e,data:a})=>{let s={...f},n=t||[];if("vessel"===e&&(Array.isArray(a)&&a.length>0?s.basicComponentID={in:a.map(e=>+e.value)}:a&&!Array.isArray(a)?s.basicComponentID={eq:+a.value}:delete s.basicComponentID),"status"===e&&(Array.isArray(a)&&a.length>0?s.status={in:a.map(e=>e.value)}:a&&!Array.isArray(a)?s.status={eq:a.value}:delete s.status),"member"===e){if(Array.isArray(a)&&a.length>0)s.assignedToID={in:a.map(e=>+("object"==typeof e?e.value:e))};else if(a&&!Array.isArray(a)){let e="object"==typeof a?a.value:a;s.assignedToID={eq:+e}}else delete s.assignedToID}"dateRange"===e&&(a?.startDate&&a?.endDate?s.expires={gte:a.startDate,lte:a.endDate}:delete s.expires),"category"===e&&(Array.isArray(a)&&a.length>0?s.maintenanceCategoryID={in:a.map(e=>+e.value)}:a&&!Array.isArray(a)?s.maintenanceCategoryID={eq:+a.value}:delete s.maintenanceCategoryID);let i=null;"recurring"===e&&a&&!Array.isArray(a)&&(i=a.value);let r=N;if("keyword"===e||r&&r.length>0){let e=a?.value?.trim().toLowerCase();e&&e.length>0?(n=n.filter(t=>[t.name,t.comments,t.workOrderNumber].some(t=>t?.toLowerCase().includes(e))),r=a.value):r=null}if(s.basicComponentID){let e=s.basicComponentID.in||[s.basicComponentID.eq];n=n.filter(t=>e.includes(t.basicComponent?.id))}if(s.status){let e=s.status.in||[s.status.eq];n=n.filter(t=>e.includes(t.status))}if(s.assignedToID){let e=s.assignedToID.in||[s.assignedToID.eq];n=n.filter(t=>e.includes(t.assignedTo?.id))}if(s.maintenanceCategoryID){let e=s.maintenanceCategoryID.in||[s.maintenanceCategoryID.eq];n=n.filter(t=>e.includes(t.maintenanceCategoryID))}s.expires&&s.expires.gte&&s.expires.lte&&(n=n.filter(e=>c()(e.startDate).isAfter(c()(s.expires.gte))&&c()(e.startDate).isBefore(c()(s.expires.lte)))),i&&("recurring"===i?n=n.filter(e=>e.recurringID>0):"one-off"===i&&(n=n.filter(e=>!e.recurringID||0===e.recurringID))),y(s),M(r),u(n)},rowStatus:e=>{if("Completed"===e.status||e.archived||"Save_As_Draft"===e.status)return"normal";switch(e.isOverDue?.status){case"High":return"overdue";case"Upcoming":return"upcoming";default:return"normal"}}}):s.jsx(l.hM,{})})]})}},49517:(e,t,a)=>{a.d(t,{Z:()=>r});var s=a(98768),n=a(39544),i=a(76915);function r({onDownloadCsv:e,onDownloadPdf:t}){return(0,s.jsxs)("div",{className:"flex gap-3 mb-3",children:[e&&s.jsx(n.Button,{iconLeft:i.Z,type:"button",onClick:e,children:"Download CSV"}),t&&s.jsx(n.Button,{iconLeft:i.Z,type:"button",onClick:t,children:"Download PDF"})]})}},28271:(e,t,a)=>{a.d(t,{N:()=>C,Z:()=>D});var s=a(98768),n=a(37042),i=a(60343),r=a(49517),l=a(63043),o=a(79418),d=a(13842),c=a(83179),u=a.n(c),m=a(7678),h=a.n(m),p=a(91973),g=a(2604),x=a(69424),f=a(26659),v=a(17203),j=a(25394);let y=["Task Name","Inventory","Location","Assigned To","Status","Due Date","Due Status"];function D(){let e=(0,x.useRouter)(),[t,a]=(0,i.useState)([]),[c,m]=(0,i.useState)(null),[h,D]=(0,i.useState)(null),[N,w]=(0,i.useState)(null),[k,A]=(0,i.useState)({startDate:new Date,endDate:new Date}),[S,{called:T,loading:I,data:$}]=(0,o.t)(l.p,{fetchPolicy:"cache-and-network",onError:e=>{console.error("GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error",e)}}),M=(0,i.useMemo)(()=>{let e=$?.readComponentMaintenanceChecks.nodes??[];if(0===e.length)return[];let t=[];return e.forEach(e=>{let a={taskName:e.name,vesselName:e.basicComponent.title,assignedTo:0==e.assignedTo.id?void 0:`${e.assignedTo.firstName} ${e.assignedTo.surname}`,inventoryName:e.inventory.title,dueDate:e.expires?new Date(e.expires):void 0,status:e.status,dueStatus:(0,d.AT)(e)};t.push(a)}),t},[T,I,$]);return(0,s.jsxs)(s.Fragment,{children:[s.jsx(j.Bu,{title:"Maintenance status and activity report",actions:s.jsx(j.zx,{variant:"back",iconLeft:v.Z,onClick:()=>e.push("/reporting"),children:"Back"})}),s.jsx(j.Zb,{className:"mt-8",children:(0,s.jsxs)(j.aY,{className:"flex flex-col gap-4",children:[s.jsx(n.Z,{onChange:({type:e,data:t})=>{switch(e){case"vessels":a(t);break;case"category":m(t);break;case"status":D(t);break;case"dateRange":A(t);break;case"member":w(t)}},onClick:()=>{let e={};null!==k.startDate&&null!==k.endDate&&(e.expires={gte:k.startDate,lte:k.endDate}),t.length>0&&(e.basicComponentID={in:t.map(e=>e.value)}),null!==c&&(e.maintenanceCategoryID={eq:c.value}),null!==h&&(e.status={eq:h.value}),null!==N&&(e.assignedToID={eq:N.value}),S({variables:{filter:e}})}}),s.jsx(r.Z,{onDownloadPdf:()=>{if(0===M.length)return;let e=M.map(e=>[e.taskName,e.inventoryName??"",e.vesselName??"",e.assignedTo??"",e.status??"",e.dueDate?u()(e.dueDate).format("DD/MM/YYYY"):"",C(e.dueStatus)]);(0,g.S)({body:e,headers:[["Task Name","Inventory","Location","Assigned To","Status","Due Date","Due Status"]]})},onDownloadCsv:()=>{if(0===M.length)return;let e=[["task name","inventory","location","assigned to","status","due date","due status"]];M.forEach(t=>{e.push([t.taskName,t.inventoryName??"N/A",t.vesselName??"N/A",t.assignedTo??"N/A",t.status??"N/A",t.dueDate?u()(t.dueDate).format("DD/MM/YYYY"):"N/A",C(t.dueStatus)])}),(0,p.u)(e)}}),(0,s.jsxs)(f.iA,{children:[s.jsx(f.xD,{children:s.jsx(f.SC,{children:y.map(e=>s.jsx(f.ss,{children:e},e))})}),s.jsx(b,{isLoading:T&&I,reportData:M})]})]})})]})}function b({reportData:e,isLoading:t}){return t?s.jsx(f.RM,{children:s.jsx(f.SC,{children:s.jsx(f.pj,{colSpan:y.length,className:"text-center  h-32",children:"Loading..."})})}):0==e.length?s.jsx(f.RM,{children:s.jsx(f.SC,{children:s.jsx(f.pj,{colSpan:y.length,className:"text-center  h-32",children:"No Data Available"})})}):s.jsx(f.RM,{children:e.map((e,t)=>(0,s.jsxs)(f.SC,{className:"group border-b  hover: ",children:[s.jsx(f.pj,{className:"px-2 py-3 text-left w-[15%]",children:s.jsx("div",{className:" inline-block ml-3",children:e.taskName})}),s.jsx(f.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:e.inventoryName})}),s.jsx(f.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:e.vesselName})}),s.jsx(f.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:e.assignedTo})}),s.jsx(f.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:e.status})}),s.jsx(f.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:e.dueDate?u()(e.dueDate).format("DD/MM/YY"):""})}),s.jsx(f.pj,{className:"px-2 py-3 text-left w-[10%]",children:s.jsx("div",{className:" inline-block ",children:C(e.dueStatus)})})]},`report-item-${t}`))})}let C=e=>`${e?.status&&["High","Medium","Low"].includes(e.status)?e?.days:""}${e?.status==="Completed"&&e?.days==="Save As Draft"?e?.days:""}${e?.status==="Upcoming"?e?.days:""}${e?.status==="Completed"&&h()(e?.days)?e?.status:""}${e?.status!=="Completed"||h()(e?.days)||e?.days==="Save As Draft"?"":e?.days}`}};