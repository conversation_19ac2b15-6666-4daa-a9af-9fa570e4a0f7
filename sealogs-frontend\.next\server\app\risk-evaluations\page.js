(()=>{var e={};e.id=2712,e.ids=[2712],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},1532:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>g,tree:()=>d}),t(35308),t(2650),t(78398),t(57757),t(48045);var s=t(40060),i=t(33581),a=t(57567),o=t.n(a),n=t(51650),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d=["",{children:["risk-evaluations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,35308)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\risk-evaluations\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,2650)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\risk-evaluations\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\risk-evaluations\\page.tsx"],u="/risk-evaluations/page",p={require:t,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/risk-evaluations/page",pathname:"/risk-evaluations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93693:(e,r,t)=>{Promise.resolve().then(t.bind(t,17781))},90456:(e,r,t)=>{Promise.resolve().then(t.bind(t,86653))},17405:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});var s=t(83179),i=t.n(s),a=t(86708);class o{async save(e){try{let r=Object.fromEntries(Object.entries(e).map(([e,r])=>[e,"number"==typeof r?r.toString():r])),t=r.id,s={...r,idbCRUD:"Update",idbCRUDDate:i()().format("YYYY-MM-DD HH:mm:ss")},o=await this.getById(t);return o?await a.Z.TripEvent.update(t,s):await a.Z.TripEvent.add(s),o=await this.getById(t),console.log("TripEvent save",e,o),o}catch(r){console.error("TripEvent save",e,r)}}async getAll(){try{let e=await a.Z.TripEvent.toArray();return console.log("TripEvent getAll",e),e}catch(e){console.error("TripEvent getAll",e)}}async getById(e){try{let r=await a.Z.TripEvent.get(`${e}`),t=await this.addRelationships(r);return console.log("TripEvent getById",e,t),t}catch(r){console.error("TripEvent getById",e,r)}}async getByIds(e){try{let r=await a.Z.TripEvent.where("id").anyOf(e).toArray(),t=Promise.all(r.map(async e=>await this.addRelationships(e)));return console.log("TripEvent getByIds",e,t),t}catch(r){console.error("TripEvent getByIds",e,r)}}async getByFieldID(e,r){try{let t=await a.Z.TripEvent.where(`${e}`).equals(`${r}`).toArray();return console.log("TripEvent getByFieldID",e,r,t),t}catch(t){console.error("TripEvent getByFieldID",e,r,t)}}async bulkAdd(e){try{return await a.Z.TripEvent.bulkAdd(e),console.log("TripEvent bulkAdd",e),e}catch(r){if("BulkError"===r.name){let t=r.failuresByPos.map(e=>e.key),s=e.filter(e=>!t.includes(e.id));return await a.Z.TripEvent.bulkAdd(s),console.log("TripEvent bulkAdd::BulkError",e,r),e}console.error("TripEvent bulkAdd",e,r)}}async addRelationships(e){return console.log("TripEvent addRelationships",e),e}async setProperty(e){try{if(e){let r=await a.Z.TripEvent.get(`${e}`);return r.idbCRUD="Download",r.idbCRUDDate=i()().format("YYYY-MM-DD HH:mm:ss"),await a.Z.TripEvent.update(e,r),console.log("TripEvent setProperty",e,r),r}}catch(r){console.error("TripEvent setProperty",e,r)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await a.Z.TripEvent.update(e.id,e)})),console.log("TripEvent multiUpdate",e)}catch(r){console.error("TripEvent multiUpdate",e,r)}}}let n=o},17781:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(98768);t(60343);var i=t(64837);function a({children:e}){return s.jsx(i.Z,{children:e})}},86653:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>b});var s=t(98768),i=t(60343),a=t(79418),o=t(94060),n=t(40672),l=t(52994),d=t(70701),c=t(10706),u=t(25394);let p=e=>{let r={TowingChecklist:"Towing Checklist",DangerousGoods:"Dangerous Goods",BarCrossingChecklist:"Bar Crossing Checklist"}[e.type]||e.type;return"TowingChecklist"===e.type&&e.towingChecklistID&&e.towingChecklistID>0?r+=`: #${e.towingChecklistID}`:"DangerousGoods"===e.type&&e.dangerousGoodsID&&e.dangerousGoodsID>0?r+=`: #${e.dangerousGoodsID}`:"BarCrossingChecklist"===e.type&&e.barCrossingChecklistID&&e.barCrossingChecklistID>0&&(r+=`: #${e.barCrossingChecklistID}`),r},g=e=>{let r=null;if("TowingChecklist"===e.type?r=e.towingChecklist?.member:"DangerousGoods"===e.type?r=e.dangerousGoods?.member:"BarCrossingChecklist"===e.type&&(r=e.barCrossingChecklist?.member),!r)return"";let t=r.firstName||"",s=r.surname||"";return`${t} ${s}`.trim()},v=e=>{if(!e)return"";try{return(0,c.WU)(new Date(e),"dd/MM/yyyy")}catch{return e}};function h(){let[e,r]=(0,i.useState)([]),[t,c]=(0,i.useState)(!1),h=(0,d.wu)([{accessorKey:"type",header:"Risk Evaluations",cell:({row:e})=>(0,s.jsxs)("div",{className:"w-full space-y-2 landscape:space-y-0",children:[(0,s.jsxs)("div",{className:"flex flex-wrap justify-between items-center",children:[s.jsx("div",{className:"small:text-nowrap",children:p(e.original)}),g(e.original)&&(0,s.jsxs)("div",{className:"tablet-md:hidden text-card-foreground/80",children:["Memeber: ",g(e.original)]})]}),(0,s.jsxs)("div",{className:"text-sm landscape:hidden flex justify-between gap-2.5 text-card-foreground/80",children:[(0,s.jsxs)("div",{className:" text-nowrap",children:["Date:"," ",s.jsx("span",{children:v(e.original.created)})]}),s.jsx("div",{className:" tablet-md:hidden",children:e.original.vessel?.title||""})]})]}),cellAlignment:"left"},{accessorKey:"vessel.title",header:"Vessel",breakpoint:"tablet-md",cell:({row:e})=>s.jsx("div",{children:e.original.vessel?.title||""}),cellAlignment:"center"},{accessorKey:"created",header:"Date",breakpoint:"landscape",cell:({row:e})=>s.jsx("div",{children:v(e.original.created)}),cellAlignment:"center"},{accessorKey:"member",header:"Member",breakpoint:"tablet-md",cell:({row:e})=>s.jsx("div",{children:g(e.original)}),cellAlignment:"right"}]),[b]=(0,a.t)(o.f7,{fetchPolicy:"cache-and-network",onCompleted:e=>{r(e.readRiskFactors.nodes)},onError:e=>{console.error("onError",e)}});return(0,s.jsxs)(s.Fragment,{children:[s.jsx(u.Bu,{title:"Risk Evaluations"}),e&&e.length>0&&s.jsx(d.c4,{columns:h,data:e,showToolbar:!1,collapsible:!0,renderExpandedContent:e=>(0,s.jsxs)("div",{className:"p-4",children:["TowingChecklist"===e.type&&e.towingChecklistID&&s.jsx(n.Z,{selectedEvent:!1,onSidebarClose:!1,logBookConfig:!1,currentTrip:!1,crewMembers:!1,towingChecklistID:e.towingChecklistID,setTowingChecklistID:()=>{},setAllChecked:c,noSheet:!0}),"BarCrossingChecklist"===e.type&&e.barCrossingChecklistID&&s.jsx(l.Z,{selectedEvent:!1,onSidebarClose:!1,logBookConfig:!1,currentTrip:!1,crewMembers:!1,barCrossingChecklistID:e.barCrossingChecklistID,setBarCrossingChecklistID:()=>{},setAllChecked:c,noSheet:!0})]}),canExpand:e=>"TowingChecklist"===e.type&&!!e.towingChecklistID||"BarCrossingChecklist"===e.type&&!!e.barCrossingChecklistID,pageSize:20,showPageSizeSelector:!1})]})}function b(){return s.jsx(h,{})}},93778:(e,r,t)=>{"use strict";t.d(r,{Z:()=>p});var s=t(98768),i=t(60343),a=t(79418),o=t(75776),n=t(94060),l=t(39544),d=t(78853),c=t(69422),u=t(34376);function p({inputId:e=0,buttonType:r="icon",sectionName:t="logBookEntryID",sectionId:p=0,editable:g=!0}){let[v,h]=(0,i.useState)([]),[b,m]=(0,i.useState)([]),[x]=(0,a.t)(n.HC,{fetchPolicy:"cache-and-network",onCompleted:e=>{let r=e.readCaptureImages.nodes;r&&h(r)},onError:e=>{console.error("getFieldImages error",e)}}),y=async()=>{await x({variables:{filter:{[t]:{eq:p}}}})};return s.jsx(s.Fragment,{children:0===e||0===p?s.jsx("div",{className:"w-full flex",children:s.jsx(l.Button,{variant:"icon"===r?"ghost":"outline",size:"icon",iconOnly:"icon"===r,title:"Add comment",className:"icon"===r?"group":"h-10",iconLeft:s.jsx(d.Z,{className:"icon"===r?(0,c.cn)("text-curious-blue-400 group-hover:text-curious-blue-400/50","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300"):"",size:24}),onClick:()=>(0,u.Am)({title:"Please save the section first",description:"You need to save the section in order to capture or upload images.",variant:"destructive"}),children:"button"===r&&"Capture / Upload Image"})}):(0,s.jsxs)(s.Fragment,{children:[b.length>0&&s.jsx("div",{className:"flex flex-wrap mb-4",children:b.map((e,r)=>s.jsx("div",{className:"w-1/5 p-1 rounded-md relative",children:s.jsx("img",{src:e.imageData,alt:`Captured ${r}`,className:"object-cover rounded-md"},r)},r))}),g&&s.jsx("div",{className:"w-full flex",children:s.jsx(o.Z,{file:!!(v&&Array.isArray(v))&&v.filter(r=>r.fieldName===e).sort((e,r)=>r.id-e.id),setFile:y,inputId:e.toString(),buttonType:"button",sectionData:{id:p,sectionName:t}})})]})})}},8416:(e,r,t)=>{"use strict";t.d(r,{tz:()=>p});var s=t(98768),i=t(60343),a=t(85745),o=t(8750),n=t(70906),l=t(56937),d=t(74602);let c=(0,a.j)("cursor-pointer w-full px-4 rounded-e-lg will-change-transform will-change-color will-change-padding transform-gpu hover:transition-all hover:ease-out hover:duration-300",{variants:{variant:{default:"hover:bg-fire-bush-100 hover:border-yellow-vivid-600",primary:"hover:bg-light-blue-vivid-50 hover:border-light-blue-vivid-600",secondary:"hover:bg-background hover:border-neutral-400",success:"hover:bg-bright-turquoise-100 hover:border-teal-600",destructive:"hover:bg-red-vivid-50 hover:border-red-vivid-600",warning:"hover:bg-fire-bush-100 hover:border-yellow-vivid-600",pink:"hover:bg-pink-vivid-50 hover:border-pink-vivid-600",outline:"hover:bg-background hover:border-neutral-400","light-blue":"hover:bg-light-blue-vivid-50 hover:border-light-blue-vivid-600"},size:{default:"py-[10.5px]",sm:"py-2",lg:"py-6"},disabled:{true:"hover:bg-transparent hover:border-border",false:""}},defaultVariants:{variant:"default",size:"default",disabled:!1}}),u=(0,a.j)("relative inset-x-0 w-12 rounded-s-lg justify-center flex items-center",{variants:{variant:{default:"bg-light-blue-vivid-50 border-light-blue-vivid-600",primary:"bg-light-blue-vivid-50 border-light-blue-vivid-600",secondary:"bg-background border-neutral-400",success:"bg-bright-turquoise-100 border-teal-600",destructive:"bg-red-vivid-50 border-red-vivid-600",warning:"bg-fire-bush-100 border-yellow-vivid-600",pink:"bg-pink-vivid-50 border-pink-vivid-600",outline:"bg-background border-neutral-400","light-blue":"bg-light-blue-vivid-50 border-light-blue-vivid-600"}},defaultVariants:{variant:"default"}}),p=i.forwardRef(({type:e="checkbox",id:r,checked:t,onCheckedChange:a,disabled:p,value:g,name:v,label:h,children:b,className:m,variant:x,size:y,radioGroupValue:f,isRadioStyle:k=!0,rightContent:w,leftContent:C,onClick:j,...T},E)=>{let D=i.useId(),I=r||`${e}-${D}`,N="radio"===e?f===g:t;return(0,s.jsxs)("div",{ref:E,className:(0,l.cn)("flex rounded-lg relative group justify-evenly border border-border shadow-sm cursor-pointer",p&&"opacity-50 cursor-not-allowed",m),onClick:r=>{!p&&("checkbox"===e&&a?a(!t):"radio"===e&&a&&!N&&a(!0),j&&j(r))},...T,children:[s.jsx("div",{className:(0,l.cn)(u({variant:x})),children:"checkbox"===e?s.jsx(o.Checkbox,{id:I,isRadioStyle:k,checked:t,onCheckedChange:e=>{"boolean"==typeof e&&a&&a(e)},disabled:p,name:v,variant:x,size:"lg",className:"pointer-events-none"}):s.jsx(n.mJ,{id:I,value:g||"",disabled:p,variant:x,size:"md",checked:N,className:"pointer-events-none"})}),s.jsx("div",{className:(0,l.cn)("flex items-center",c({variant:"secondary",size:y,disabled:p})),children:(0,s.jsxs)("div",{className:(0,l.cn)("flex flex-1 items-center",{"gap-2":C||w}),children:[C&&s.jsx("div",{className:"inline-flex items-center",children:C}),b||h&&s.jsx(d.P,{className:(0,l.cn)("text-wrap text-foreground text-base"),children:h}),w&&s.jsx("div",{className:"inline-flex items-center",children:w})]})})]})});p.displayName="CheckFieldLabel"},2650:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\risk-evaluations\layout.tsx#default`)},35308:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\risk-evaluations\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,5741,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,5776,6933,8249],()=>t(1532));module.exports=s})();