(()=>{var e={};e.id=1060,e.ids=[1060],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},49738:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),s(89587),s(61086),s(78398),s(57757),s(48045);var t=s(40060),a=s(33581),n=s(57567),i=s.n(n),l=s(51650),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(r,o);let c=["",{children:["dashboard",{children:["log-entries",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,89587)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\dashboard\\log-entries\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,61086)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\dashboard\\log-entries\\page.tsx"],u="/dashboard/log-entries/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/log-entries/page",pathname:"/dashboard/log-entries",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},96126:(e,r,s)=>{Promise.resolve().then(s.bind(s,10091))},62401:(e,r,s)=>{Promise.resolve().then(s.bind(s,56466))},60826:e=>{e.exports=function(e){return e.split("")}},829:e=>{e.exports=function(e,r,s,t){for(var a=e.length,n=s+(t?1:-1);t?n--:++n<a;)if(r(e[n],n,e))return n;return -1}},65337:(e,r,s)=>{var t=s(829),a=s(35447),n=s(28026);e.exports=function(e,r,s){return r==r?n(e,r,s):t(e,a,s)}},35447:e=>{e.exports=function(e){return e!=e}},77420:e=>{e.exports=function(e,r,s){var t=-1,a=e.length;r<0&&(r=-r>a?0:a+r),(s=s>a?a:s)<0&&(s+=a),a=r>s?0:s-r>>>0,r>>>=0;for(var n=Array(a);++t<a;)n[t]=e[t+r];return n}},49513:(e,r,s)=>{var t=s(70458),a=/^\s+/;e.exports=function(e){return e?e.slice(0,t(e)+1).replace(a,""):e}},30482:(e,r,s)=>{var t=s(77420);e.exports=function(e,r,s){var a=e.length;return s=void 0===s?a:s,!r&&s>=a?e:t(e,r,s)}},74783:(e,r,s)=>{var t=s(65337);e.exports=function(e,r){for(var s=e.length;s--&&t(r,e[s],0)>-1;);return s}},41200:(e,r,s)=>{var t=s(65337);e.exports=function(e,r){for(var s=-1,a=e.length;++s<a&&t(r,e[s],0)>-1;);return s}},73211:e=>{var r=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return r.test(e)}},28026:e=>{e.exports=function(e,r,s){for(var t=s-1,a=e.length;++t<a;)if(e[t]===r)return t;return -1}},66095:(e,r,s)=>{var t=s(60826),a=s(73211),n=s(92115);e.exports=function(e){return a(e)?n(e):t(e)}},70458:e=>{var r=/\s/;e.exports=function(e){for(var s=e.length;s--&&r.test(e.charAt(s)););return s}},92115:e=>{var r="\ud800-\udfff",s="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",t="\ud83c[\udffb-\udfff]",a="[^"+r+"]",n="(?:\ud83c[\udde6-\uddff]){2}",i="[\ud800-\udbff][\udc00-\udfff]",l="(?:"+s+"|"+t+")?",o="[\\ufe0e\\ufe0f]?",c="(?:\\u200d(?:"+[a,n,i].join("|")+")"+o+l+")*",d=RegExp(t+"(?="+t+")|(?:"+[a+s+"?",s,n,i,"["+r+"]"].join("|")+")"+(o+l+c),"g");e.exports=function(e){return e.match(d)||[]}},14826:(e,r,s)=>{var t=s(22060),a=s(49513),n=s(30482),i=s(74783),l=s(41200),o=s(66095),c=s(16266);e.exports=function(e,r,s){if((e=c(e))&&(s||void 0===r))return a(e);if(!e||!(r=t(r)))return e;var d=o(e),u=o(r),m=l(d,u),x=i(d,u)+1;return n(d,m,x).join("")}},10091:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var t=s(98768);s(60343);var a=s(64837);function n({children:e}){return t.jsx(a.Z,{children:e})}},56466:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>L});var t=s(98768),a=s(60343),n=s(76249),i=s(79895),l=s(87085);l.v1,l.v3;let o=l.v4;l.v5;var c=s(72548),d=s(76342),u=s(83179),m=s.n(u),x=s(60797),p=s(81524),g=s(26477),h=s(25394);function f(e){let[r,s]=(0,a.useState)(),[n,i]=(0,a.useState)(),[l,u]=(0,a.useState)(),[f,v]=(0,a.useState)(),[j,b]=(0,a.useState)(),[y,N]=(0,a.useState)([]),w=r=>{i(e.crewTrainingConfigData?.trainers[+r-1])},S=(0,a.useMemo)(()=>{let r=[],s=0;if(e?.crewTrainingConfigData?.trainingLocation)for(let[t,a]of Object.entries(e?.crewTrainingConfigData?.trainingLocation))r.push({value:(s++).toString(),label:`${a}`});return r},[e]),C=(0,a.useMemo)(()=>e?.crewTrainingConfigData?.trainingTypes.map((e,r)=>({value:e.id,label:e.type})),[e]),D=async()=>{n&&await k({variables:{input:{trainerId:n.value,logBookEntryId:+e.vesselID,crewMemberSignatures:y,created:m()().format("YYYY-MM-DD"),trainingTypes:[l?.value],uuid:o(),trainingSummary:j,trainingLocation:f?.label}}})},M=(e,r,s)=>{let t=[];if(y&&y.length>0){let r=y.findIndex(e=>e.MemberID===s);-1!==r?y[r].SignatureData=e:y.push({MemberID:s,SignatureData:e}),N(y)}else t.push({MemberID:s,SignatureData:e}),N(t)},[k,{loading:T,error:L,data:$}]=(0,c.D)(d.h3H,{onError:e=>{console.error(`Exception encountered @ mutationCreatCrewTraining ${e}`)}});return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:`grid grid-cols-1 my-3 md:grid-cols-3 lg:grid-cols-4 items-center   ${e.locked?"pointer-events-none":""}`,children:[t.jsx(x.Label,{className:"hidden md:block",children:"Trainer"}),t.jsx("div",{className:"flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3",children:t.jsx("div",{className:"flex items-center",children:t.jsx(p.Combobox,{placeholder:"Select Trainer",onChange:e=>w(e?.value),options:e?.crewTrainingConfigData?.trainers.map((e,r)=>({value:e.id,label:e.name}))})})})]}),(0,t.jsxs)("div",{className:`grid grid-cols-1 my-3  md:grid-cols-3 lg:grid-cols-4 items-center   ${e.locked?"pointer-events-none":""}`,children:[t.jsx(x.Label,{className:"hidden md:block",children:"Nature of Training"}),t.jsx("div",{className:"flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3",children:t.jsx("div",{className:"flex items-center",children:t.jsx(p.Combobox,{value:l,placeholder:"Select Location",onChange:e=>u(e),options:C})})})]}),(0,t.jsxs)("div",{className:`grid grid-cols-1 my-3 md:grid-cols-3 lg:grid-cols-4 items-center   ${e.locked?"pointer-events-none":""}`,children:[t.jsx(x.Label,{className:"hidden md:block",children:"Training Location"}),t.jsx("div",{className:"flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3",children:t.jsx("div",{className:"flex items-center",children:t.jsx(p.Combobox,{value:f,placeholder:"Select Position",options:S,onChange:e=>v(e)})})})]}),(0,t.jsxs)("div",{className:`grid grid-cols-1 my-3 md:grid-cols-3 lg:grid-cols-4 items-center ${e.locked?"pointer-events-none":""}`,children:[t.jsx(x.Label,{className:"hidden md:block",children:"Training Summary"}),t.jsx("div",{className:"flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3",children:t.jsx("div",{className:"flex items-center",children:t.jsx(h.gx,{id:"training-summary",rows:4,placeholder:"Training Summary ...",onChange:e=>{b(e.target.value)}})})})]}),t.jsx("hr",{className:"my-4"}),t.jsx(h.H2,{className:"mb-4",children:"Crew Members"}),t.jsx("div",{className:"grid grid-cols-1 my-3 gap-4 md:grid-cols-2 lg:grid-cols-3 items-center w-full p-4 overflow-hidden  border  rounded-lg shadow ",children:r?.map((e,r)=>t.jsx(g.Z,{member:e.label,memberId:e.value,onSignatureChanged:(e,r,s)=>r&&s?M(e,r,s):null,signature:{signatureData:y.find(r=>r.MemberID===e.value)?.SignatureData}},e.value))}),(0,t.jsxs)("div",{className:`grid grid-cols-1 my-3 md:grid-cols-3 lg:grid-cols-4 items-center   ${e.locked?"pointer-events-none":""}`,children:[t.jsx(x.Label,{className:"hidden md:block",children:"Add Member"}),t.jsx("div",{className:"flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3",children:t.jsx("div",{className:"flex items-center ",children:t.jsx(p.Combobox,{multi:!0,options:e?.crewTrainingConfigData?.crewMembers,value:r,onChange:e=>{s(e)}})})})]}),t.jsx("div",{className:`flex justify-between items-center ${e.locked?"pointer-events-none":""}`,children:t.jsx("div",{children:t.jsx(h.zx,{onClick:D,children:"Save"})})})]})}var v=s(75546),j=s(74602),b=s(39544),y=s(35024),N=s(26509),w=s(27514),S=s(20849),C=s(70996),D=s(36895),M=s(29052);let k=(0,s(97428).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),T=({tabs:e,activeTab:r,onTabChange:s,mobileTitle:a="Navigation",className:n="",tabsListClassName:i="",tabsTriggerClassName:l="",tabsContentClassName:o="",showMobileDropdown:c=!0,showScrollableTabs:d=!1})=>{let u=e.filter(e=>e.component);return(0,t.jsxs)("div",{className:`w-full ${n}`,children:[t.jsx("div",{className:"hidden md:block mb-6",children:(0,t.jsxs)(D.Tabs,{value:r,onValueChange:s,children:[d?t.jsx(M.x,{className:"w-full pb-2",children:t.jsx(D.TabsList,{className:`inline-flex w-max px-1 ${i}`,children:e.map(e=>(0,t.jsxs)(D.TabsTrigger,{value:e.value,className:l,children:[e.icon&&t.jsx("span",{className:"mr-2",children:e.icon}),e.label]},e.id))})}):t.jsx(D.TabsList,{className:i,children:e.map(e=>(0,t.jsxs)(D.TabsTrigger,{value:e.value,className:l,children:[e.icon&&t.jsx("span",{className:"mr-2",children:e.icon}),e.label]},e.id))}),u.map(e=>t.jsx(D.TabsContent,{value:e.value,className:o,children:e.component},e.id))]})}),c&&(0,t.jsxs)("div",{className:"md:hidden mb-4 flex justify-between items-center",children:[t.jsx("h2",{className:"text-lg font-medium",children:a}),(0,t.jsxs)(w.DropdownMenu,{children:[t.jsx(w.DropdownMenuTrigger,{asChild:!0,children:t.jsx(b.Button,{variant:"secondary",size:"sm",iconLeft:k,"aria-label":"Menu"})}),t.jsx(w.DropdownMenuContent,{align:"end",className:"w-56",children:e.map(e=>(0,t.jsxs)(w.DropdownMenuItem,{onClick:()=>s(e.value),className:r===e.value?"bg-muted":"",children:[e.icon&&t.jsx("span",{className:"mr-2",children:e.icon}),e.label]},e.id))})]})]}),t.jsx("div",{className:"md:hidden",children:u.find(e=>e.value===r)?.component})]})};function L(){let[e,r]=(0,a.useState)("tripLog");return(0,t.jsxs)(y.Zb,{className:"w-full mb-10",children:[(0,t.jsxs)(y.Ol,{className:"flex flex-row justify-between items-center",children:[t.jsx(j.H3,{children:"Vessel Name"}),(0,t.jsxs)(w.DropdownMenu,{children:[t.jsx(w.DropdownMenuTrigger,{asChild:!0,children:t.jsx(b.Button,{variant:"ghost",size:"icon",children:t.jsx(S.Z,{className:"h-5 w-5"})})}),t.jsx(w.DropdownMenuContent,{align:"end",children:t.jsx(w.DropdownMenuItem,{asChild:!0,children:t.jsx("a",{href:"/dashboard/log-entries",children:"Engine Log Configuration"})})})]})]}),(0,t.jsxs)(y.aY,{children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start gap-4 mb-6",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-start items-end gap-4",children:[t.jsx(n.Z,{edit_logBookEntry:!1,log_params:{disable:!1,startLabel:"Start Date",endLabel:"End Date",startDate:null,endDate:null,handleStartDateChange:!1,handleEndDateChange:!1,showOvernightCheckbox:!1,showEndDate:!1,overnight:!1,handleShowEndDate:!1},setStartDate:()=>{},setEndDate:()=>{}}),t.jsx(i.Z,{edit_logBookEntry:!1,master:{},masterTerm:"Master",setMaster:()=>({}),crewMembers:{}}),t.jsx("div",{className:"flex flex-col items-start gap-2 p-2 rounded",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx("span",{children:"Completed "}),t.jsx("span",{children:(0,v.o0)(new Date().toLocaleString())})]})})]}),t.jsx("div",{className:"flex justify-end items-center gap-4",children:t.jsx(b.Button,{variant:"secondary",iconLeft:C.Z,children:"PDF"})})]}),t.jsx(T,{tabs:[{id:"tripLog",value:"tripLog",label:"Trip Log",component:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[t.jsx("div",{}),t.jsx(N.Separator,{className:"my-4"}),t.jsx("div",{}),t.jsx(N.Separator,{className:"my-4"}),t.jsx("div",{})]}),t.jsx(N.Separator,{className:"my-4"}),(0,t.jsxs)("div",{className:"flex justify-between gap-4",children:[t.jsx(b.Button,{variant:"outline",children:"Cancel"}),t.jsx(b.Button,{children:"Save"})]})]})},{id:"engineLog",value:"engineLog",label:"Engine Log",component:t.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 items-center"})},{id:"compengineLog",value:"compengineLog",label:"Comprehensive Engine Log",component:(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("div",{className:"grid grid-cols-1 items-center"}),t.jsx(N.Separator,{className:"my-4"}),(0,t.jsxs)("div",{className:"flex justify-between gap-4",children:[t.jsx(b.Button,{variant:"outline",children:"Cancel"}),t.jsx(b.Button,{children:"Save"})]})]})},{id:"crew",value:"crew",label:"Crew",component:(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("div",{children:t.jsx(N.Separator,{className:"my-4"})}),(0,t.jsxs)("div",{className:"flex justify-between gap-4",children:[t.jsx(b.Button,{variant:"outline",children:"Cancel"}),t.jsx(b.Button,{children:"Save"})]})]})},{id:"crewTraining",value:"crewTraining",label:"Training / Drills",component:(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("div",{children:t.jsx(f,{})}),t.jsx(N.Separator,{className:"my-4"}),(0,t.jsxs)("div",{className:"flex justify-between gap-4",children:[t.jsx(b.Button,{variant:"outline",children:"Cancel"}),t.jsx(b.Button,{children:"Save"})]})]})},{id:"supernumerary",value:"supernumerary",label:"Supernumerary",component:(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx("div",{}),t.jsx(N.Separator,{className:"my-4"}),(0,t.jsxs)("div",{className:"flex justify-between gap-4",children:[t.jsx(b.Button,{variant:"outline",children:"Cancel"}),t.jsx(b.Button,{children:"Save"})]})]})},{id:"dailyChecks",value:"dailyChecks",label:"Daily Checks",component:t.jsx("div",{})}],activeTab:e,onTabChange:r,showScrollableTabs:!0,tabsListClassName:"flex-wrap justify-start"})]})]})}},75546:(e,r,s)=>{"use strict";s.d(r,{Br:()=>m,fU:()=>x,o0:()=>d,p6:()=>c,vq:()=>u});var t=s(83179),a=s.n(t),n=s(7678),i=s.n(n),l=s(14826),o=s.n(l);let c=(e="",r=!0)=>{let s;if(i()(o()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[s,t,a]=e.split("-"),n=r?s.slice(-2):s,i=parseInt(a,10).toString().padStart(2,"0"),l=parseInt(t,10).toString().padStart(2,"0");return`${i}/${l}/${n}`}if(!(s=e&&"object"==typeof e?a()(e.toString()):a()(e)).isValid())return"";let t=s.format("DD"),n=s.format("MM"),l=r?s.format("YY"):s.format("YYYY");return`${t}/${n}/${l}`},d=(e="",r=!0)=>{let s;if(i()(o()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[s,t,a]=e.split("-"),n=r?s.slice(-2):s,i=parseInt(a,10).toString().padStart(2,"0"),l=parseInt(t,10).toString().padStart(2,"0");return`${i}/${l}/${n} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[s,t]=e.split(" "),[a,n,i]=s.split("-"),l=r?a.slice(-2):a,o=t.split(":"),c=o[0].padStart(2,"0"),d=o[1].padStart(2,"0"),u=parseInt(i,10).toString().padStart(2,"0"),m=parseInt(n,10).toString().padStart(2,"0");return`${u}/${m}/${l} ${c}:${d}`}if(!(s=e&&"object"==typeof e?a()(e.toString()):a()(e)).isValid())return"";let t=s.format("DD"),n=s.format("MM"),l=r?s.format("YY"):s.format("YYYY"),c=s.format("HH:mm");return`${t}/${n}/${l} ${c}`},u=(e="")=>i()(o()(e))?"":a()(e).format("YYYY-MM-DD HH:mm:ss"),m=(e="")=>i()(o()(e))?new Date:new Date(`${e}T10:00:00Z`),x=(e,r)=>{let s=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),t=e=>e.includes(" ")?e.replace(" ","T"):e,a=e=>{if(!e||"string"!=typeof e)return null;if(s(e)){let r=new Date().toISOString().split("T")[0];return new Date(`${r}T${e}`)}return new Date(t(e))},n=a(e),i=a(r);return!n||!i||isNaN(n.getTime())||isNaN(i.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:r}),!1):i>n}},76249:(e,r,s)=>{"use strict";s.d(r,{Z:()=>d});var t=s(98768),a=s(54214);let n=(0,s(97428).Z)("CircleMinus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}]]);var i=s(60343),l=s(29342),o=s(70684),c=s(75546);function d({log_params:e,setStartDate:r,setEndDate:s,edit_logBookEntry:d=!1}){let[u,m]=(0,i.useState)((0,c.Br)(e?.startDate||"")),[x,p]=(0,i.useState)(e?.endDate?(0,c.Br)(e.endDate):e?.startDate?(0,c.Br)(e.startDate):new Date),[g,h]=(0,i.useState)(!0);return(0,t.jsxs)("div",{className:`w-full flex flex-wrap items-end gap-x-5 gap-y-8 flex-grow transition-all duration-200 ease-in-out ${d?"":"opacity-80 pointer-events-none"}`,children:[t.jsx(l.Z,{onChange:e=>{m(e),r(e)},mode:"single",type:"date",label:e.startLabel,value:u,placeholder:new Date().toLocaleDateString(),disabled:!d}),!g&&t.jsx(l.Z,{onChange:e=>{p(e),s(e)},mode:"single",type:"date",label:e.endLabel,value:x,placeholder:new Date().toLocaleDateString(),disabled:!d}),(0,t.jsxs)("div",{className:"w-fit flex items-center h-11",children:[!e.showOvernightCheckbox&&g&&t.jsx(o.TooltipProvider,{children:(0,t.jsxs)(o.Tooltip,{children:[t.jsx(o.TooltipTrigger,{className:"p-0 text-neutral-400",onClick:()=>{h(!1)},children:t.jsx(a.Z,{size:28,className:"stroke-[2px]"})}),t.jsx(o.TooltipContent,{children:t.jsx("p",{children:"Add end date"})})]})}),!g&&t.jsx(o.TooltipProvider,{children:(0,t.jsxs)(o.Tooltip,{children:[t.jsx(o.TooltipTrigger,{onClick:()=>{h(!0)},className:"p-0 text-neutral-400",children:t.jsx(n,{size:28,className:"stroke-[2px]"})}),t.jsx(o.TooltipContent,{children:t.jsx("p",{children:"Remove end date"})})]})})]})]})}},79895:(e,r,s)=>{"use strict";s.d(r,{Z:()=>d});var t=s(98768),a=s(94060),n=s(98318);s(48755);var i=s(81524),l=s(79418),o=s(69424),c=s(60343);function d({master:e,masterTerm:r,setMaster:s,crewMembers:d,offline:u=!1,edit_logBookEntry:m=!1}){(0,o.useSearchParams)().get("logentryID");let[x,p]=(0,c.useState)([]),[g]=(0,l.t)(a.Y,{fetchPolicy:"cache-and-network",onCompleted:e=>{p(Array.from(new Map(e.readCrewMembers_LogBookEntrySections.nodes.map(e=>[e.crewMember.id,e])).values()).map(e=>({label:`${e.crewMember.firstName??""} ${e.crewMember.surname??""}`,value:e.crewMember.id,profile:{firstName:e.crewMember.firstName||"",surname:e.crewMember.surname||""}})))},onError:e=>{console.error("CrewMembers_LogBookEntrySection error",e)}}),h=async e=>{let r=e.logBookEntrySections.nodes.filter(e=>"SeaLogs\\CrewMembers_LogBookEntrySection"===e.className);if(r){let e=r.map(e=>e.id);if(e?.length>0){if(u){let r=new n.Z;p((await r.getByIds(e)).map(e=>({label:`${e.crewMember.firstName??""} ${e.crewMember.surname??""}`,value:e.crewMember.id,profile:{firstName:e.crewMember.firstName||"",surname:e.crewMember.surname||""}})))}else g({variables:{filter:{id:{in:e}}}})}}},[f]=(0,l.t)(a.MI,{fetchPolicy:"cache-and-network",onCompleted:e=>{let r=e.readOneLogBookEntry;r&&h(r)},onError:e=>{console.error("queryLogBookEntry error",e)}});return t.jsx(i.Combobox,{title:r,label:r,isDisabled:!m,options:x,defaultValues:null!=e.firstName?{label:e.firstName+" "+e.surname,value:e.id,profile:{firstName:e.firstName||"",surname:e.surname||"",avatar:e.avatar}}:null,onChange:s,buttonClassName:"w-full",placeholder:`Select ${r}`})}},26477:(e,r,s)=>{"use strict";s.d(r,{Z:()=>h});var t=s(98768),a=s(60343),n=s.n(a),i=s(72184),l=s.n(i),o=s(39544),c=s(87175),d=s(4289),u=s(20502),m=s(99562),x=s(25394),p=s(56937),g=s(13842);let h=n().memo(({signature:e,member:r,memberId:s,title:n,onSignatureChanged:i,penColor:h="blue",className:f,description:v,locked:j=!1,...b})=>{let y=(0,a.useRef)(null),[N,w]=(0,a.useState)(e?.signatureData??null);(0,a.useEffect)(()=>{e?.id&&e.id>0&&!e.signatureData&&(0,g.iA)(e.id).then(w).catch(e=>console.error("Fetch sig URL failed:",e))},[e?.id,e?.signatureData]),(0,a.useEffect)(()=>{let e=y.current;e&&N&&(e.clear(),e.fromDataURL(N))},[N]);let S=(0,a.useCallback)(()=>{let e=y.current;e&&!e.isEmpty()&&i(e.toDataURL(),r,s)},[i,r,s]),C=(0,a.useCallback)(()=>{let e=y.current;e?.clear(),i("",r,s),w(null)},[i,r,s]);return(0,a.useMemo)(()=>n??r??"Signature",[n,r]),(0,t.jsxs)("div",{className:(0,p.cn)("relative w-full space-y-2",f),children:[j&&(0,t.jsxs)(c.C,{variant:"destructive",className:"absolute top-2 right-2 text-xs gap-1",children:[t.jsx(d.Z,{className:"h-3 w-3"}),"Locked"]}),t.jsx("div",{children:v&&t.jsx(x.P,{className:"text-sm text-muted-foreground",children:v})}),t.jsx(x.__,{htmlFor:"sig-canvas",children:t.jsx(l(),{...b,ref:y,penColor:h,canvasProps:{id:"sig-canvas",className:(0,p.cn)("border-2 border-dashed border-neutral-400 rounded-lg h-48",j?"bg-muted/50":"bg-white"),style:{width:"100%",touchAction:j?"none":"auto",cursor:j?"not-allowed":"crosshair"}},onEnd:S})}),j&&t.jsx("div",{className:"absolute inset-0 bg-white/60 rounded-lg pointer-events-none"}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[t.jsx(u.Z,{className:"h-4 w-4"}),"Draw your signature"]}),t.jsx(o.Button,{size:"sm",iconLeft:m.Z,className:"w-fit",onClick:C,disabled:j,"aria-label":"Clear signature",children:"Clear"})]})]})})},61086:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\dashboard\layout.tsx#default`)},89587:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\dashboard\log-entries\page.tsx#default`)},9999:(e,r,s)=>{"use strict";s.d(r,{a:()=>a});var t=s(60343);function a(e,r,{getInitialValueInEffect:s}={getInitialValueInEffect:!0}){let[a,n]=(0,t.useState)(!!s&&r);return(0,t.useRef)(null),a||!1}},79320:(e,r,s)=>{"use strict";s.d(r,{t:()=>i});var t=s(93140),a=s(69359),n=s(79824);function i(e,r){let s=(0,n.Q)(e);return isNaN(+s)?(0,t.L)(e,NaN):(null!=r.year&&s.setFullYear(r.year),null!=r.month&&(s=(0,a.q)(s,r.month)),null!=r.date&&s.setDate(r.date),null!=r.hours&&s.setHours(r.hours),null!=r.minutes&&s.setMinutes(r.minutes),null!=r.seconds&&s.setSeconds(r.seconds),null!=r.milliseconds&&s.setMilliseconds(r.milliseconds),s)}},99491:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(97428).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},72997:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(97428).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},20849:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(97428).Z)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},70996:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(97428).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[864,8865,3563,6263,8189,9507,7602,2935,8594,6451,4234,2925,5394,4837,6342,3842,8712],()=>s(49738));module.exports=t})();