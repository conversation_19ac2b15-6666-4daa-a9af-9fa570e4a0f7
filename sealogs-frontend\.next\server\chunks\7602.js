"use strict";exports.id=7602,exports.ids=[7602],exports.modules={17404:(e,t,r)=>{r.d(t,{Ry:()=>u});var n=new WeakMap,o=new WeakMap,a={},i=0,l=function(e){return e&&(e.host||l(e.parentNode))},s=function(e,t,r,s){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=l(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[r]||(a[r]=new WeakMap);var d=a[r],c=[],f=new Set,h=new Set(u),p=function(e){!e||f.has(e)||(f.add(e),p(e.parentNode))};u.forEach(p);var m=function(e){!e||h.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(s),a=null!==t&&"false"!==t,i=(n.get(e)||0)+1,l=(d.get(e)||0)+1;n.set(e,i),d.set(e,l),c.push(e),1===i&&a&&o.set(e,!0),1===l&&e.setAttribute(r,"true"),a||e.setAttribute(s,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),i++,function(){c.forEach(function(e){var t=n.get(e)-1,a=d.get(e)-1;n.set(e,t),d.set(e,a),t||(o.has(e)||e.removeAttribute(s),o.delete(e)),a||e.removeAttribute(r)}),--i||(n=new WeakMap,n=new WeakMap,o=new WeakMap,a={})}},u=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n,o=Array.from(Array.isArray(e)?e:[e]),a=t||(n=e,"undefined"==typeof document?null:(Array.isArray(n)?n[0]:n).ownerDocument.body);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live], script"))),s(o,a,r,"aria-hidden")):function(){return null}}},85521:(e,t,r)=>{r.d(t,{Z:()=>H});var n,o,a=r(91070),i=r(60343),l="right-scroll-bar-position",s="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,c=new WeakMap;function f(e){return e}var h=function(e){void 0===e&&(e={});var t,r,n,o=(void 0===t&&(t=f),r=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var o=t(e,n);return r.push(o),function(){r=r.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(n=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){n=!0;var t=[];if(r.length){var o=r;r=[],o.forEach(e),t=r}var a=function(){var r=t;t=[],r.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return o.options=(0,a.pi)({async:!0,ssr:!1},e),o}(),p=function(){},m=i.forwardRef(function(e,t){var r,n,o,l,s=i.useRef(null),f=i.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),m=f[0],v=f[1],g=e.forwardProps,w=e.children,y=e.className,b=e.removeScrollBar,x=e.enabled,M=e.shards,k=e.sideCar,C=e.noRelative,E=e.noIsolation,S=e.inert,D=e.allowPinchZoom,R=e.as,T=e.gapMode,N=(0,a._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),j=(r=[s,t],n=function(e){return r.forEach(function(t){return u(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,l=o.facade,d(function(){var e=c.get(l);if(e){var t=new Set(e),n=new Set(r),o=l.current;t.forEach(function(e){n.has(e)||u(e,null)}),n.forEach(function(e){t.has(e)||u(e,o)})}c.set(l,r)},[r]),l),P=(0,a.pi)((0,a.pi)({},N),m);return i.createElement(i.Fragment,null,x&&i.createElement(k,{sideCar:h,removeScrollBar:b,shards:M,noRelative:C,noIsolation:E,inert:S,setCallbacks:v,allowPinchZoom:!!D,lockRef:s,gapMode:T}),g?i.cloneElement(i.Children.only(w),(0,a.pi)((0,a.pi)({},P),{ref:j})):i.createElement(void 0===R?"div":R,(0,a.pi)({},P,{className:y,ref:j}),w))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:s,zeroRight:l};var v=function(e){var t=e.sideCar,r=(0,a._T)(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return i.createElement(n,(0,a.pi)({},r))};v.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||r.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=n:a.appendChild(document.createTextNode(n)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=g();return function(t,r){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},y=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},M=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[x(r),x(n),x(o)]},k=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=M(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},C=y(),E="data-scroll-locked",S=function(e,t,r,n){var o=e.left,a=e.top,i=e.right,u=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(u,"px ").concat(n,";\n  }\n  body[").concat(E,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(u,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(l," {\n    right: ").concat(u,"px ").concat(n,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(u,"px ").concat(n,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(E,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},D=function(){var e=parseInt(document.body.getAttribute(E)||"0",10);return isFinite(e)?e:0},R=function(){i.useEffect(function(){return document.body.setAttribute(E,(D()+1).toString()),function(){var e=D()-1;e<=0?document.body.removeAttribute(E):document.body.setAttribute(E,e.toString())}},[])},T=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;R();var a=i.useMemo(function(){return k(o)},[o]);return i.createElement(C,{styles:S(a,!t,o,r?"":"!important")})},N=!1;if("undefined"!=typeof window)try{var j=Object.defineProperty({},"passive",{get:function(){return N=!0,!0}});window.addEventListener("test",j,j),window.removeEventListener("test",j,j)}catch(e){N=!1}var P=!!N&&{passive:!1},I=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&!(r.overflowY===r.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===r[t])},W=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),A(e,n)){var o=O(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},A=function(e,t){return"v"===e?I(t,"overflowY"):I(t,"overflowX")},O=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},L=function(e,t,r,n,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=i*n,s=r.target,u=t.contains(s),d=!1,c=l>0,f=0,h=0;do{if(!s)break;var p=O(e,s),m=p[0],v=p[1]-p[2]-i*m;(m||v)&&A(e,s)&&(f+=v,h+=m);var g=s.parentNode;s=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!u&&s!==document.body||u&&(t.contains(s)||t===s));return c&&(o&&1>Math.abs(f)||!o&&l>f)?d=!0:!c&&(o&&1>Math.abs(h)||!o&&-l>h)&&(d=!0),d},_=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},F=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},V=0,Y=[];let B=(n=function(e){var t=i.useRef([]),r=i.useRef([0,0]),n=i.useRef(),o=i.useState(V++)[0],l=i.useState(y)[0],s=i.useRef(e);i.useEffect(function(){s.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,a.ev)([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var o,a=_(e),i=r.current,l="deltaX"in e?e.deltaX:i[0]-a[0],u="deltaY"in e?e.deltaY:i[1]-a[1],d=e.target,c=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===c&&"range"===d.type)return!1;var f=W(c,d);if(!f)return!0;if(f?o=c:(o="v"===c?"h":"v",f=W(c,d)),!f)return!1;if(!n.current&&"changedTouches"in e&&(l||u)&&(n.current=o),!o)return!0;var h=n.current||o;return L(h,t,e,"h"===h?l:u,!0)},[]),d=i.useCallback(function(e){if(Y.length&&Y[Y.length-1]===l){var r="deltaY"in e?F(e):_(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(s.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!s.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=i.useCallback(function(e,r,n,o){var a={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),f=i.useCallback(function(e){r.current=_(e),n.current=void 0},[]),h=i.useCallback(function(t){c(t.type,F(t),t.target,u(t,e.lockRef.current))},[]),p=i.useCallback(function(t){c(t.type,_(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return Y.push(l),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:p}),document.addEventListener("wheel",d,P),document.addEventListener("touchmove",d,P),document.addEventListener("touchstart",f,P),function(){Y=Y.filter(function(e){return e!==l}),document.removeEventListener("wheel",d,P),document.removeEventListener("touchmove",d,P),document.removeEventListener("touchstart",f,P)}},[]);var m=e.removeScrollBar,v=e.inert;return i.createElement(i.Fragment,null,v?i.createElement(l,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?i.createElement(T,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(n),v);var z=i.forwardRef(function(e,t){return i.createElement(m,(0,a.pi)({},e,{ref:t,sideCar:B}))});z.classNames=m.classNames;let H=z},24039:(e,t,r)=>{r.d(t,{u:()=>n});function n(e,[t,r]){return Math.min(r,Math.max(t,e))}},8971:(e,t,r)=>{r.d(t,{Content:()=>q,Header:()=>H,Item:()=>z,Root:()=>B,Trigger:()=>Z});var n=r(60343),o=r(13295),a=r(4632),i=r(48367),l=r(70112),s=r(21110),u=r(54928),d=r(78644),c=r(36183),f=r(98375),h=r(98768),p="Accordion",m=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[v,g,w]=(0,a.B)(p),[y,b]=(0,o.b)(p,[w,d.p_]),x=(0,d.p_)(),M=n.forwardRef((e,t)=>{let{type:r,...n}=e;return(0,h.jsx)(v.Provider,{scope:e.__scopeAccordion,children:"multiple"===r?(0,h.jsx)(R,{...n,ref:t}):(0,h.jsx)(D,{...n,ref:t})})});M.displayName=p;var[k,C]=y(p),[E,S]=y(p,{collapsible:!1}),D=n.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:a=()=>{},collapsible:i=!1,...l}=e,[u,d]=(0,s.T)({prop:r,defaultProp:o??"",onChange:a,caller:p});return(0,h.jsx)(k,{scope:e.__scopeAccordion,value:n.useMemo(()=>u?[u]:[],[u]),onItemOpen:d,onItemClose:n.useCallback(()=>i&&d(""),[i,d]),children:(0,h.jsx)(E,{scope:e.__scopeAccordion,collapsible:i,children:(0,h.jsx)(j,{...l,ref:t})})})}),R=n.forwardRef((e,t)=>{let{value:r,defaultValue:o,onValueChange:a=()=>{},...i}=e,[l,u]=(0,s.T)({prop:r,defaultProp:o??[],onChange:a,caller:p}),d=n.useCallback(e=>u((t=[])=>[...t,e]),[u]),c=n.useCallback(e=>u((t=[])=>t.filter(t=>t!==e)),[u]);return(0,h.jsx)(k,{scope:e.__scopeAccordion,value:l,onItemOpen:d,onItemClose:c,children:(0,h.jsx)(E,{scope:e.__scopeAccordion,collapsible:!0,children:(0,h.jsx)(j,{...i,ref:t})})})}),[T,N]=y(p),j=n.forwardRef((e,t)=>{let{__scopeAccordion:r,disabled:o,dir:a,orientation:s="vertical",...d}=e,c=n.useRef(null),p=(0,i.e)(c,t),w=g(r),y="ltr"===(0,f.gm)(a),b=(0,l.M)(e.onKeyDown,e=>{if(!m.includes(e.key))return;let t=e.target,r=w().filter(e=>!e.ref.current?.disabled),n=r.findIndex(e=>e.ref.current===t),o=r.length;if(-1===n)return;e.preventDefault();let a=n,i=o-1,l=()=>{(a=n+1)>i&&(a=0)},u=()=>{(a=n-1)<0&&(a=i)};switch(e.key){case"Home":a=0;break;case"End":a=i;break;case"ArrowRight":"horizontal"===s&&(y?l():u());break;case"ArrowDown":"vertical"===s&&l();break;case"ArrowLeft":"horizontal"===s&&(y?u():l());break;case"ArrowUp":"vertical"===s&&u()}let d=a%o;r[d].ref.current?.focus()});return(0,h.jsx)(T,{scope:r,disabled:o,direction:a,orientation:s,children:(0,h.jsx)(v.Slot,{scope:r,children:(0,h.jsx)(u.WV.div,{...d,"data-orientation":s,ref:p,onKeyDown:o?void 0:b})})})}),P="AccordionItem",[I,W]=y(P),A=n.forwardRef((e,t)=>{let{__scopeAccordion:r,value:n,...o}=e,a=N(P,r),i=C(P,r),l=x(r),s=(0,c.M)(),u=n&&i.value.includes(n)||!1,f=a.disabled||e.disabled;return(0,h.jsx)(I,{scope:r,open:u,disabled:f,triggerId:s,children:(0,h.jsx)(d.fC,{"data-orientation":a.orientation,"data-state":Y(u),...l,...o,ref:t,disabled:f,open:u,onOpenChange:e=>{e?i.onItemOpen(n):i.onItemClose(n)}})})});A.displayName=P;var O="AccordionHeader",L=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=N(p,r),a=W(O,r);return(0,h.jsx)(u.WV.h3,{"data-orientation":o.orientation,"data-state":Y(a.open),"data-disabled":a.disabled?"":void 0,...n,ref:t})});L.displayName=O;var _="AccordionTrigger",F=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=N(p,r),a=W(_,r),i=S(_,r),l=x(r);return(0,h.jsx)(v.ItemSlot,{scope:r,children:(0,h.jsx)(d.xz,{"aria-disabled":a.open&&!i.collapsible||void 0,"data-orientation":o.orientation,id:a.triggerId,...l,...n,ref:t})})});F.displayName=_;var U="AccordionContent",V=n.forwardRef((e,t)=>{let{__scopeAccordion:r,...n}=e,o=N(p,r),a=W(U,r),i=x(r);return(0,h.jsx)(d.VY,{role:"region","aria-labelledby":a.triggerId,"data-orientation":o.orientation,...i,...n,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function Y(e){return e?"open":"closed"}V.displayName=U;var B=M,z=A,H=L,Z=F,q=V},20412:(e,t,r)=>{r.d(t,{Action:()=>O,Cancel:()=>L,Content:()=>A,Description:()=>F,Overlay:()=>W,Portal:()=>I,Root:()=>j,Title:()=>_,Trigger:()=>P});var n=r(60343),o=r(13295),a=r(48367),i=r(32264),l=r(70112),s=r(73404),u=r(98768),d="AlertDialog",[c,f]=(0,o.b)(d,[i.p8]),h=(0,i.p8)(),p=e=>{let{__scopeAlertDialog:t,...r}=e,n=h(t);return(0,u.jsx)(i.fC,{...n,...r,modal:!0})};p.displayName=d;var m=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=h(r);return(0,u.jsx)(i.xz,{...o,...n,ref:t})});m.displayName="AlertDialogTrigger";var v=e=>{let{__scopeAlertDialog:t,...r}=e,n=h(t);return(0,u.jsx)(i.h_,{...n,...r})};v.displayName="AlertDialogPortal";var g=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=h(r);return(0,u.jsx)(i.aV,{...o,...n,ref:t})});g.displayName="AlertDialogOverlay";var w="AlertDialogContent",[y,b]=c(w),x=(0,s.sA)("AlertDialogContent"),M=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:o,...s}=e,d=h(r),c=n.useRef(null),f=(0,a.e)(t,c),p=n.useRef(null);return(0,u.jsx)(i.jm,{contentName:w,titleName:k,docsSlug:"alert-dialog",children:(0,u.jsx)(y,{scope:r,cancelRef:p,children:(0,u.jsxs)(i.VY,{role:"alertdialog",...d,...s,ref:f,onOpenAutoFocus:(0,l.M)(s.onOpenAutoFocus,e=>{e.preventDefault(),p.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,u.jsx)(x,{children:o}),(0,u.jsx)(N,{contentRef:c})]})})})});M.displayName=w;var k="AlertDialogTitle",C=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=h(r);return(0,u.jsx)(i.Dx,{...o,...n,ref:t})});C.displayName=k;var E="AlertDialogDescription",S=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=h(r);return(0,u.jsx)(i.dk,{...o,...n,ref:t})});S.displayName=E;var D=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=h(r);return(0,u.jsx)(i.x8,{...o,...n,ref:t})});D.displayName="AlertDialogAction";var R="AlertDialogCancel",T=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:o}=b(R,r),l=h(r),s=(0,a.e)(t,o);return(0,u.jsx)(i.x8,{...l,...n,ref:s})});T.displayName=R;var N=({contentRef:e})=>{let t=`\`${w}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${w}\` by passing a \`${E}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${w}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return n.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},j=p,P=m,I=v,W=g,A=M,O=D,L=T,_=C,F=S},27762:(e,t,r)=>{r.d(t,{NY:()=>C,Ee:()=>k,fC:()=>M});var n=r(60343),o=r(13295),a=r(20772),i=r(32183),l=r(54928),s=r(37284);function u(){return()=>{}}var d=r(98768),c="Avatar",[f,h]=(0,o.b)(c),[p,m]=f(c),v=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[a,i]=n.useState("idle");return(0,d.jsx)(p,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:i,children:(0,d.jsx)(l.WV.span,{...o,ref:t})})});v.displayName=c;var g="AvatarImage",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:c=()=>{},...f}=e,h=m(g,r),p=function(e,{referrerPolicy:t,crossOrigin:r}){let o=(0,s.useSyncExternalStore)(u,()=>!0,()=>!1),a=n.useRef(null),l=o?(a.current||(a.current=new window.Image),a.current):null,[d,c]=n.useState(()=>x(l,e));return(0,i.b)(()=>{c(x(l,e))},[l,e]),(0,i.b)(()=>{let e=e=>()=>{c(e)};if(!l)return;let n=e("loaded"),o=e("error");return l.addEventListener("load",n),l.addEventListener("error",o),t&&(l.referrerPolicy=t),"string"==typeof r&&(l.crossOrigin=r),()=>{l.removeEventListener("load",n),l.removeEventListener("error",o)}},[l,r,t]),d}(o,f),v=(0,a.W)(e=>{c(e),h.onImageLoadingStatusChange(e)});return(0,i.b)(()=>{"idle"!==p&&v(p)},[p,v]),"loaded"===p?(0,d.jsx)(l.WV.img,{...f,ref:t,src:o}):null});w.displayName=g;var y="AvatarFallback",b=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,i=m(y,r),[s,u]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>u(!0),o);return()=>window.clearTimeout(e)}},[o]),s&&"loaded"!==i.imageLoadingStatus?(0,d.jsx)(l.WV.span,{...a,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=y;var M=v,k=w,C=b},21023:(e,t,r)=>{r.d(t,{fC:()=>x,z$:()=>k});var n=r(60343),o=r(48367),a=r(13295),i=r(70112),l=r(21110),s=r(12716),u=r(22595),d=r(26983),c=r(54928),f=r(98768),h="Checkbox",[p,m]=(0,a.b)(h),[v,g]=p(h);function w(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:a,disabled:i,form:s,name:u,onCheckedChange:d,required:c,value:p="on",internal_do_not_use_render:m}=e,[g,w]=(0,l.T)({prop:r,defaultProp:a??!1,onChange:d,caller:h}),[y,b]=n.useState(null),[x,M]=n.useState(null),k=n.useRef(!1),C=!y||!!s||!!y.closest("form"),E={checked:g,disabled:i,setChecked:w,control:y,setControl:b,name:u,form:s,value:p,hasConsumerStoppedPropagationRef:k,required:c,defaultChecked:!S(a)&&a,isFormControl:C,bubbleInput:x,setBubbleInput:M};return(0,f.jsx)(v,{scope:t,...E,children:"function"==typeof m?m(E):o})}var y="CheckboxTrigger",b=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...a},l)=>{let{control:s,value:u,disabled:d,checked:h,required:p,setControl:m,setChecked:v,hasConsumerStoppedPropagationRef:w,isFormControl:b,bubbleInput:x}=g(y,e),M=(0,o.e)(l,m),k=n.useRef(h);return n.useEffect(()=>{let e=s?.form;if(e){let t=()=>v(k.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[s,v]),(0,f.jsx)(c.WV.button,{type:"button",role:"checkbox","aria-checked":S(h)?"mixed":h,"aria-required":p,"data-state":D(h),"data-disabled":d?"":void 0,disabled:d,value:u,...a,ref:M,onKeyDown:(0,i.M)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.M)(r,e=>{v(e=>!!S(e)||!e),x&&b&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})})});b.displayName=y;var x=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:a,required:i,disabled:l,value:s,onCheckedChange:u,form:d,...c}=e;return(0,f.jsx)(w,{__scopeCheckbox:r,checked:o,defaultChecked:a,disabled:l,required:i,onCheckedChange:u,name:n,form:d,value:s,internal_do_not_use_render:({isFormControl:e})=>(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(b,{...c,ref:t,__scopeCheckbox:r}),e&&(0,f.jsx)(E,{__scopeCheckbox:r})]})})});x.displayName=h;var M="CheckboxIndicator",k=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=g(M,r);return(0,f.jsx)(d.z,{present:n||S(a.checked)||!0===a.checked,children:(0,f.jsx)(c.WV.span,{"data-state":D(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});k.displayName=M;var C="CheckboxBubbleInput",E=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:a,hasConsumerStoppedPropagationRef:i,checked:l,defaultChecked:d,required:h,disabled:p,name:m,value:v,form:w,bubbleInput:y,setBubbleInput:b}=g(C,e),x=(0,o.e)(r,b),M=(0,s.D)(l),k=(0,u.t)(a);n.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(M!==l&&e){let r=new Event("click",{bubbles:t});y.indeterminate=S(l),e.call(y,!S(l)&&l),y.dispatchEvent(r)}},[y,M,l,i]);let E=n.useRef(!S(l)&&l);return(0,f.jsx)(c.WV.input,{type:"checkbox","aria-hidden":!0,defaultChecked:d??E.current,required:h,disabled:p,name:m,value:v,form:w,...t,tabIndex:-1,ref:x,style:{...t.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function S(e){return"indeterminate"===e}function D(e){return S(e)?"indeterminate":e?"checked":"unchecked"}E.displayName=C},78644:(e,t,r)=>{r.d(t,{Fw:()=>M,VY:()=>D,fC:()=>E,p_:()=>m,wy:()=>b,xz:()=>S});var n=r(60343),o=r(70112),a=r(13295),i=r(21110),l=r(32183),s=r(48367),u=r(54928),d=r(26983),c=r(36183),f=r(98768),h="Collapsible",[p,m]=(0,a.b)(h),[v,g]=p(h),w=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:o,defaultOpen:a,disabled:l,onOpenChange:s,...d}=e,[p,m]=(0,i.T)({prop:o,defaultProp:a??!1,onChange:s,caller:h});return(0,f.jsx)(v,{scope:r,disabled:l,contentId:(0,c.M)(),open:p,onOpenToggle:n.useCallback(()=>m(e=>!e),[m]),children:(0,f.jsx)(u.WV.div,{"data-state":C(p),"data-disabled":l?"":void 0,...d,ref:t})})});w.displayName=h;var y="CollapsibleTrigger",b=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,a=g(y,r);return(0,f.jsx)(u.WV.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":C(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...n,ref:t,onClick:(0,o.M)(e.onClick,a.onOpenToggle)})});b.displayName=y;var x="CollapsibleContent",M=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=g(x,e.__scopeCollapsible);return(0,f.jsx)(d.z,{present:r||o.open,children:({present:e})=>(0,f.jsx)(k,{...n,ref:t,present:e})})});M.displayName=x;var k=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:o,children:a,...i}=e,d=g(x,r),[c,h]=n.useState(o),p=n.useRef(null),m=(0,s.e)(t,p),v=n.useRef(0),w=v.current,y=n.useRef(0),b=y.current,M=d.open||c,k=n.useRef(M),E=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>k.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.b)(()=>{let e=p.current;if(e){E.current=E.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();v.current=t.height,y.current=t.width,k.current||(e.style.transitionDuration=E.current.transitionDuration,e.style.animationName=E.current.animationName),h(o)}},[d.open,o]),(0,f.jsx)(u.WV.div,{"data-state":C(d.open),"data-disabled":d.disabled?"":void 0,id:d.contentId,hidden:!M,...i,ref:m,style:{"--radix-collapsible-content-height":w?`${w}px`:void 0,"--radix-collapsible-content-width":b?`${b}px`:void 0,...e.style},children:M&&a})});function C(e){return e?"open":"closed"}var E=w,S=b,D=M},32264:(e,t,r)=>{r.d(t,{Dx:()=>en,VY:()=>er,aV:()=>et,dk:()=>eo,fC:()=>G,h_:()=>ee,jm:()=>K,p8:()=>x,x8:()=>ea,xz:()=>J});var n=r(60343),o=r(70112),a=r(48367),i=r(13295),l=r(36183),s=r(21110),u=r(97938),d=r(64807),c=r(54054),f=r(26983),h=r(54928),p=r(47962),m=r(85521),v=r(17404),g=r(73404),w=r(98768),y="Dialog",[b,x]=(0,i.b)(y),[M,k]=b(y),C=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:u=!0}=e,d=n.useRef(null),c=n.useRef(null),[f,h]=(0,s.T)({prop:o,defaultProp:a??!1,onChange:i,caller:y});return(0,w.jsx)(M,{scope:t,triggerRef:d,contentRef:c,contentId:(0,l.M)(),titleId:(0,l.M)(),descriptionId:(0,l.M)(),open:f,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),modal:u,children:r})};C.displayName=y;var E="DialogTrigger",S=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=k(E,r),l=(0,a.e)(t,i.triggerRef);return(0,w.jsx)(h.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":Z(i.open),...n,ref:l,onClick:(0,o.M)(e.onClick,i.onOpenToggle)})});S.displayName=E;var D="DialogPortal",[R,T]=b(D,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,i=k(D,t);return(0,w.jsx)(R,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,w.jsx)(f.z,{present:r||i.open,children:(0,w.jsx)(c.h,{asChild:!0,container:a,children:e})}))})};N.displayName=D;var j="DialogOverlay",P=n.forwardRef((e,t)=>{let r=T(j,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=k(j,e.__scopeDialog);return a.modal?(0,w.jsx)(f.z,{present:n||a.open,children:(0,w.jsx)(W,{...o,ref:t})}):null});P.displayName=j;var I=(0,g.Z8)("DialogOverlay.RemoveScroll"),W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(j,r);return(0,w.jsx)(m.Z,{as:I,allowPinchZoom:!0,shards:[o.contentRef],children:(0,w.jsx)(h.WV.div,{"data-state":Z(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),A="DialogContent",O=n.forwardRef((e,t)=>{let r=T(A,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=k(A,e.__scopeDialog);return(0,w.jsx)(f.z,{present:n||a.open,children:a.modal?(0,w.jsx)(L,{...o,ref:t}):(0,w.jsx)(_,{...o,ref:t})})});O.displayName=A;var L=n.forwardRef((e,t)=>{let r=k(A,e.__scopeDialog),i=n.useRef(null),l=(0,a.e)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,v.Ry)(e)},[]),(0,w.jsx)(F,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),_=n.forwardRef((e,t)=>{let r=k(A,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,w.jsx)(F,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),F=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...s}=e,c=k(A,r),f=n.useRef(null),h=(0,a.e)(t,f);return(0,p.EW)(),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(d.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,w.jsx)(u.XB,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Z(c.open),...s,ref:h,onDismiss:()=>c.onOpenChange(!1)})}),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(Q,{titleId:c.titleId}),(0,w.jsx)(X,{contentRef:f,descriptionId:c.descriptionId})]})]})}),U="DialogTitle",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(U,r);return(0,w.jsx)(h.WV.h2,{id:o.titleId,...n,ref:t})});V.displayName=U;var Y="DialogDescription",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(Y,r);return(0,w.jsx)(h.WV.p,{id:o.descriptionId,...n,ref:t})});B.displayName=Y;var z="DialogClose",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=k(z,r);return(0,w.jsx)(h.WV.button,{type:"button",...n,ref:t,onClick:(0,o.M)(e.onClick,()=>a.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}H.displayName=z;var q="DialogTitleWarning",[K,$]=(0,i.k)(q,{contentName:A,titleName:U,docsSlug:"dialog"}),Q=({titleId:e})=>{let t=$(q),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},X=({contentRef:e,descriptionId:t})=>{let r=$("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},G=C,J=S,ee=N,et=P,er=O,en=V,eo=B,ea=H},98375:(e,t,r)=>{r.d(t,{gm:()=>a});var n=r(60343);r(98768);var o=n.createContext(void 0);function a(e){let t=n.useContext(o);return e||t||"ltr"}},28969:(e,t,r)=>{r.d(t,{oC:()=>e7,VY:()=>e3,ZA:()=>e4,ck:()=>e9,wU:()=>te,__:()=>e8,Uv:()=>e2,Ee:()=>e6,Rk:()=>e5,fC:()=>e0,Z0:()=>tt,Tr:()=>tr,tu:()=>to,fF:()=>tn,xz:()=>e1});var n=r(60343),o=r(70112),a=r(48367),i=r(13295),l=r(21110),s=r(54928),u=r(4632),d=r(98375),c=r(97938),f=r(47962),h=r(64807),p=r(36183),m=r(18251),v=r(54054),g=r(26983),w=r(469),y=r(73404),b=r(20772),x=r(17404),M=r(85521),k=r(98768),C=["Enter"," "],E=["ArrowUp","PageDown","End"],S=["ArrowDown","PageUp","Home",...E],D={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},R={ltr:["ArrowLeft"],rtl:["ArrowRight"]},T="Menu",[N,j,P]=(0,u.B)(T),[I,W]=(0,i.b)(T,[P,m.D7,w.Pc]),A=(0,m.D7)(),O=(0,w.Pc)(),[L,_]=I(T),[F,U]=I(T),V=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:i,modal:l=!0}=e,s=A(t),[u,c]=n.useState(null),f=n.useRef(!1),h=(0,b.W)(i),p=(0,d.gm)(a);return n.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,k.jsx)(m.fC,{...s,children:(0,k.jsx)(L,{scope:t,open:r,onOpenChange:h,content:u,onContentChange:c,children:(0,k.jsx)(F,{scope:t,onClose:n.useCallback(()=>h(!1),[h]),isUsingKeyboardRef:f,dir:p,modal:l,children:o})})})};V.displayName=T;var Y=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=A(r);return(0,k.jsx)(m.ee,{...o,...n,ref:t})});Y.displayName="MenuAnchor";var B="MenuPortal",[z,H]=I(B,{forceMount:void 0}),Z=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:o}=e,a=_(B,t);return(0,k.jsx)(z,{scope:t,forceMount:r,children:(0,k.jsx)(g.z,{present:r||a.open,children:(0,k.jsx)(v.h,{asChild:!0,container:o,children:n})})})};Z.displayName=B;var q="MenuContent",[K,$]=I(q),Q=n.forwardRef((e,t)=>{let r=H(q,e.__scopeMenu),{forceMount:n=r.forceMount,...o}=e,a=_(q,e.__scopeMenu),i=U(q,e.__scopeMenu);return(0,k.jsx)(N.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(g.z,{present:n||a.open,children:(0,k.jsx)(N.Slot,{scope:e.__scopeMenu,children:i.modal?(0,k.jsx)(X,{...o,ref:t}):(0,k.jsx)(G,{...o,ref:t})})})})}),X=n.forwardRef((e,t)=>{let r=_(q,e.__scopeMenu),i=n.useRef(null),l=(0,a.e)(t,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,x.Ry)(e)},[]),(0,k.jsx)(ee,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),G=n.forwardRef((e,t)=>{let r=_(q,e.__scopeMenu);return(0,k.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),J=(0,y.Z8)("MenuContent.ScrollLock"),ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:i=!1,trapFocus:l,onOpenAutoFocus:s,onCloseAutoFocus:u,disableOutsidePointerEvents:d,onEntryFocus:p,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:x,disableOutsideScroll:C,...D}=e,R=_(q,r),T=U(q,r),N=A(r),P=O(r),I=j(r),[W,L]=n.useState(null),F=n.useRef(null),V=(0,a.e)(t,F,R.onContentChange),Y=n.useRef(0),B=n.useRef(""),z=n.useRef(0),H=n.useRef(null),Z=n.useRef("right"),$=n.useRef(0),Q=C?M.Z:n.Fragment,X=e=>{let t=B.current+e,r=I().filter(e=>!e.disabled),n=document.activeElement,o=r.find(e=>e.ref.current===n)?.textValue,a=function(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let i=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return i!==r?i:void 0}(r.map(e=>e.textValue),t,o),i=r.find(e=>e.textValue===a)?.ref.current;(function e(t){B.current=t,window.clearTimeout(Y.current),""!==t&&(Y.current=window.setTimeout(()=>e(""),1e3))})(t),i&&setTimeout(()=>i.focus())};n.useEffect(()=>()=>window.clearTimeout(Y.current),[]),(0,f.EW)();let G=n.useCallback(e=>Z.current===H.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],l=t[a],s=i.x,u=i.y,d=l.x,c=l.y;u>n!=c>n&&r<(d-s)*(n-u)/(c-u)+s&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,H.current?.area),[]);return(0,k.jsx)(K,{scope:r,searchRef:B,onItemEnter:n.useCallback(e=>{G(e)&&e.preventDefault()},[G]),onItemLeave:n.useCallback(e=>{G(e)||(F.current?.focus(),L(null))},[G]),onTriggerLeave:n.useCallback(e=>{G(e)&&e.preventDefault()},[G]),pointerGraceTimerRef:z,onPointerGraceIntentChange:n.useCallback(e=>{H.current=e},[]),children:(0,k.jsx)(Q,{...C?{as:J,allowPinchZoom:!0}:void 0,children:(0,k.jsx)(h.M,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.M)(s,e=>{e.preventDefault(),F.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,k.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:x,children:(0,k.jsx)(w.fC,{asChild:!0,...P,dir:T.dir,orientation:"vertical",loop:i,currentTabStopId:W,onCurrentTabStopIdChange:L,onEntryFocus:(0,o.M)(p,e=>{T.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,k.jsx)(m.VY,{role:"menu","aria-orientation":"vertical","data-state":eR(R.open),"data-radix-menu-content":"",dir:T.dir,...N,...D,ref:V,style:{outline:"none",...D.style},onKeyDown:(0,o.M)(D.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&X(e.key));let o=F.current;if(e.target!==o||!S.includes(e.key))return;e.preventDefault();let a=I().filter(e=>!e.disabled).map(e=>e.ref.current);E.includes(e.key)&&a.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(Y.current),B.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,ej(e=>{let t=e.target,r=$.current!==e.clientX;if(e.currentTarget.contains(t)&&r){let t=e.clientX>$.current?"right":"left";Z.current=t,$.current=e.clientX}}))})})})})})})});Q.displayName=q;var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,k.jsx)(s.WV.div,{role:"group",...n,ref:t})});et.displayName="MenuGroup";var er=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,k.jsx)(s.WV.div,{...n,ref:t})});er.displayName="MenuLabel";var en="MenuItem",eo="menu.itemSelect",ea=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:i,...l}=e,u=n.useRef(null),d=U(en,e.__scopeMenu),c=$(en,e.__scopeMenu),f=(0,a.e)(t,u),h=n.useRef(!1);return(0,k.jsx)(ei,{...l,ref:f,disabled:r,onClick:(0,o.M)(e.onClick,()=>{let e=u.current;if(!r&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>i?.(e),{once:!0}),(0,s.jH)(e,t),t.defaultPrevented?h.current=!1:d.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),h.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{h.current||e.currentTarget?.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==c.searchRef.current;!r&&(!t||" "!==e.key)&&C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=en;var ei=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:i=!1,textValue:l,...u}=e,d=$(en,r),c=O(r),f=n.useRef(null),h=(0,a.e)(t,f),[p,m]=n.useState(!1),[v,g]=n.useState("");return n.useEffect(()=>{let e=f.current;e&&g((e.textContent??"").trim())},[u.children]),(0,k.jsx)(N.ItemSlot,{scope:r,disabled:i,textValue:l??v,children:(0,k.jsx)(w.ck,{asChild:!0,...c,focusable:!i,children:(0,k.jsx)(s.WV.div,{role:"menuitem","data-highlighted":p?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...u,ref:h,onPointerMove:(0,o.M)(e.onPointerMove,ej(e=>{i?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,ej(e=>d.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>m(!0)),onBlur:(0,o.M)(e.onBlur,()=>m(!1))})})})}),el=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...a}=e;return(0,k.jsx)(em,{scope:e.__scopeMenu,checked:r,children:(0,k.jsx)(ea,{role:"menuitemcheckbox","aria-checked":eT(r)?"mixed":r,...a,ref:t,"data-state":eN(r),onSelect:(0,o.M)(a.onSelect,()=>n?.(!!eT(r)||!r),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var es="MenuRadioGroup",[eu,ed]=I(es,{value:void 0,onValueChange:()=>{}}),ec=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...o}=e,a=(0,b.W)(n);return(0,k.jsx)(eu,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,k.jsx)(et,{...o,ref:t})})});ec.displayName=es;var ef="MenuRadioItem",eh=n.forwardRef((e,t)=>{let{value:r,...n}=e,a=ed(ef,e.__scopeMenu),i=r===a.value;return(0,k.jsx)(em,{scope:e.__scopeMenu,checked:i,children:(0,k.jsx)(ea,{role:"menuitemradio","aria-checked":i,...n,ref:t,"data-state":eN(i),onSelect:(0,o.M)(n.onSelect,()=>a.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});eh.displayName=ef;var ep="MenuItemIndicator",[em,ev]=I(ep,{checked:!1}),eg=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...o}=e,a=ev(ep,r);return(0,k.jsx)(g.z,{present:n||eT(a.checked)||!0===a.checked,children:(0,k.jsx)(s.WV.span,{...o,ref:t,"data-state":eN(a.checked)})})});eg.displayName=ep;var ew=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,k.jsx)(s.WV.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ew.displayName="MenuSeparator";var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,o=A(r);return(0,k.jsx)(m.Eh,{...o,...n,ref:t})});ey.displayName="MenuArrow";var eb="MenuSub",[ex,eM]=I(eb),ek=e=>{let{__scopeMenu:t,children:r,open:o=!1,onOpenChange:a}=e,i=_(eb,t),l=A(t),[s,u]=n.useState(null),[d,c]=n.useState(null),f=(0,b.W)(a);return n.useEffect(()=>(!1===i.open&&f(!1),()=>f(!1)),[i.open,f]),(0,k.jsx)(m.fC,{...l,children:(0,k.jsx)(L,{scope:t,open:o,onOpenChange:f,content:d,onContentChange:c,children:(0,k.jsx)(ex,{scope:t,contentId:(0,p.M)(),triggerId:(0,p.M)(),trigger:s,onTriggerChange:u,children:r})})})};ek.displayName=eb;var eC="MenuSubTrigger",eE=n.forwardRef((e,t)=>{let r=_(eC,e.__scopeMenu),i=U(eC,e.__scopeMenu),l=eM(eC,e.__scopeMenu),s=$(eC,e.__scopeMenu),u=n.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=s,f={__scopeMenu:e.__scopeMenu},h=n.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return n.useEffect(()=>h,[h]),n.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,k.jsx)(Y,{asChild:!0,...f,children:(0,k.jsx)(ei,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":l.contentId,"data-state":eR(r.open),...e,ref:(0,a.F)(t,l.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,ej(t=>{s.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||u.current||(s.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{r.onOpenChange(!0),h()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,ej(e=>{h();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,o="right"===n,a=t[o?"left":"right"],i=t[o?"right":"left"];s.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:t.top},{x:i,y:t.top},{x:i,y:t.bottom},{x:a,y:t.bottom}],side:n}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>s.onPointerGraceIntentChange(null),300)}else{if(s.onTriggerLeave(e),e.defaultPrevented)return;s.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let n=""!==s.searchRef.current;!e.disabled&&(!n||" "!==t.key)&&D[i.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});eE.displayName=eC;var eS="MenuSubContent",eD=n.forwardRef((e,t)=>{let r=H(q,e.__scopeMenu),{forceMount:i=r.forceMount,...l}=e,s=_(q,e.__scopeMenu),u=U(q,e.__scopeMenu),d=eM(eS,e.__scopeMenu),c=n.useRef(null),f=(0,a.e)(t,c);return(0,k.jsx)(N.Provider,{scope:e.__scopeMenu,children:(0,k.jsx)(g.z,{present:i||s.open,children:(0,k.jsx)(N.Slot,{scope:e.__scopeMenu,children:(0,k.jsx)(ee,{id:d.contentId,"aria-labelledby":d.triggerId,...l,ref:f,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{u.isUsingKeyboardRef.current&&c.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==d.trigger&&s.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=R[u.dir].includes(e.key);t&&r&&(s.onOpenChange(!1),d.trigger?.focus(),e.preventDefault())})})})})})});function eR(e){return e?"open":"closed"}function eT(e){return"indeterminate"===e}function eN(e){return eT(e)?"indeterminate":e?"checked":"unchecked"}function ej(e){return t=>"mouse"===t.pointerType?e(t):void 0}eD.displayName=eS;var eP="DropdownMenu",[eI,eW]=(0,i.b)(eP,[W]),eA=W(),[eO,eL]=eI(eP),e_=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:i,onOpenChange:s,modal:u=!0}=e,d=eA(t),c=n.useRef(null),[f,h]=(0,l.T)({prop:a,defaultProp:i??!1,onChange:s,caller:eP});return(0,k.jsx)(eO,{scope:t,triggerId:(0,p.M)(),triggerRef:c,contentId:(0,p.M)(),open:f,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),modal:u,children:(0,k.jsx)(V,{...d,open:f,onOpenChange:h,dir:o,modal:u,children:r})})};e_.displayName=eP;var eF="DropdownMenuTrigger",eU=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...i}=e,l=eL(eF,r),u=eA(r);return(0,k.jsx)(Y,{asChild:!0,...u,children:(0,k.jsx)(s.WV.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...i,ref:(0,a.F)(t,l.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eU.displayName=eF;var eV=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eA(t);return(0,k.jsx)(Z,{...n,...r})};eV.displayName="DropdownMenuPortal";var eY="DropdownMenuContent",eB=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...a}=e,i=eL(eY,r),l=eA(r),s=n.useRef(!1);return(0,k.jsx)(Q,{id:i.contentId,"aria-labelledby":i.triggerId,...l,...a,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{s.current||i.triggerRef.current?.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!i.modal||n)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eB.displayName=eY;var ez=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eA(r);return(0,k.jsx)(et,{...o,...n,ref:t})});ez.displayName="DropdownMenuGroup";var eH=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eA(r);return(0,k.jsx)(er,{...o,...n,ref:t})});eH.displayName="DropdownMenuLabel";var eZ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eA(r);return(0,k.jsx)(ea,{...o,...n,ref:t})});eZ.displayName="DropdownMenuItem";var eq=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eA(r);return(0,k.jsx)(el,{...o,...n,ref:t})});eq.displayName="DropdownMenuCheckboxItem";var eK=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eA(r);return(0,k.jsx)(ec,{...o,...n,ref:t})});eK.displayName="DropdownMenuRadioGroup";var e$=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eA(r);return(0,k.jsx)(eh,{...o,...n,ref:t})});e$.displayName="DropdownMenuRadioItem";var eQ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eA(r);return(0,k.jsx)(eg,{...o,...n,ref:t})});eQ.displayName="DropdownMenuItemIndicator";var eX=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eA(r);return(0,k.jsx)(ew,{...o,...n,ref:t})});eX.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eA(r);return(0,k.jsx)(ey,{...o,...n,ref:t})}).displayName="DropdownMenuArrow";var eG=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eA(r);return(0,k.jsx)(eE,{...o,...n,ref:t})});eG.displayName="DropdownMenuSubTrigger";var eJ=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,o=eA(r);return(0,k.jsx)(eD,{...o,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eJ.displayName="DropdownMenuSubContent";var e0=e_,e1=eU,e2=eV,e3=eB,e4=ez,e8=eH,e9=eZ,e7=eq,e6=eK,e5=e$,te=eQ,tt=eX,tr=e=>{let{__scopeDropdownMenu:t,children:r,open:n,onOpenChange:o,defaultOpen:a}=e,i=eA(t),[s,u]=(0,l.T)({prop:n,defaultProp:a??!1,onChange:o,caller:"DropdownMenuSub"});return(0,k.jsx)(ek,{...i,open:s,onOpenChange:u,children:r})},tn=eG,to=eJ},47962:(e,t,r)=>{r.d(t,{EW:()=>a});var n=r(60343),o=0;function a(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??i()),document.body.insertAdjacentElement("beforeend",e[1]??i()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function i(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},64807:(e,t,r)=>{r.d(t,{M:()=>c});var n=r(60343),o=r(48367),a=r(54928),i=r(20772),l=r(98768),s="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",d={bubbles:!1,cancelable:!0},c=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:c=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...w}=e,[y,b]=n.useState(null),x=(0,i.W)(v),M=(0,i.W)(g),k=n.useRef(null),C=(0,o.e)(t,e=>b(e)),E=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(c){let e=function(e){if(E.paused||!y)return;let t=e.target;y.contains(t)?k.current=t:p(k.current,{select:!0})},t=function(e){if(E.paused||!y)return;let t=e.relatedTarget;null===t||y.contains(t)||p(k.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&p(y)});return y&&r.observe(y,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[c,y,E.paused]),n.useEffect(()=>{if(y){m.add(E);let e=document.activeElement;if(!y.contains(e)){let t=new CustomEvent(s,d);y.addEventListener(s,x),y.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(p(n,{select:t}),document.activeElement!==r)return}(f(y).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&p(y))}return()=>{y.removeEventListener(s,x),setTimeout(()=>{let t=new CustomEvent(u,d);y.addEventListener(u,M),y.dispatchEvent(t),t.defaultPrevented||p(e??document.body,{select:!0}),y.removeEventListener(u,M),m.remove(E)},0)}}},[y,x,M,E]);let S=n.useCallback(e=>{if(!r&&!c||E.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[o,a]=function(e){let t=f(e);return[h(t,e),h(t.reverse(),e)]}(t);o&&a?e.shiftKey||n!==a?e.shiftKey&&n===o&&(e.preventDefault(),r&&p(a,{select:!0})):(e.preventDefault(),r&&p(o,{select:!0})):n===t&&e.preventDefault()}},[r,c,E.paused]);return(0,l.jsx)(a.WV.div,{tabIndex:-1,...w,ref:C,onKeyDown:S})});function f(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function h(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function p(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}c.displayName="FocusScope";var m=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=v(e,t)).unshift(t)},remove(t){e=v(e,t),e[0]?.resume()}}}();function v(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},57843:(e,t,r)=>{r.d(t,{VY:()=>q,ee:()=>z,fC:()=>B,h_:()=>Z,xz:()=>H});var n=r(60343),o=r(70112),a=r(48367),i=r(13295),l=r(97938),s=r(47962),u=r(64807),d=r(36183),c=r(18251),f=r(54054),h=r(26983),p=r(54928),m=r(73404),v=r(21110),g=r(17404),w=r(85521),y=r(98768),b="Popover",[x,M]=(0,i.b)(b,[c.D7]),k=(0,c.D7)(),[C,E]=x(b),S=e=>{let{__scopePopover:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:l=!1}=e,s=k(t),u=n.useRef(null),[f,h]=n.useState(!1),[p,m]=(0,v.T)({prop:o,defaultProp:a??!1,onChange:i,caller:b});return(0,y.jsx)(c.fC,{...s,children:(0,y.jsx)(C,{scope:t,contentId:(0,d.M)(),triggerRef:u,open:p,onOpenChange:m,onOpenToggle:n.useCallback(()=>m(e=>!e),[m]),hasCustomAnchor:f,onCustomAnchorAdd:n.useCallback(()=>h(!0),[]),onCustomAnchorRemove:n.useCallback(()=>h(!1),[]),modal:l,children:r})})};S.displayName=b;var D="PopoverAnchor",R=n.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,a=E(D,r),i=k(r),{onCustomAnchorAdd:l,onCustomAnchorRemove:s}=a;return n.useEffect(()=>(l(),()=>s()),[l,s]),(0,y.jsx)(c.ee,{...i,...o,ref:t})});R.displayName=D;var T="PopoverTrigger",N=n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,i=E(T,r),l=k(r),s=(0,a.e)(t,i.triggerRef),u=(0,y.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":Y(i.open),...n,ref:s,onClick:(0,o.M)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?u:(0,y.jsx)(c.ee,{asChild:!0,...l,children:u})});N.displayName=T;var j="PopoverPortal",[P,I]=x(j,{forceMount:void 0}),W=e=>{let{__scopePopover:t,forceMount:r,children:n,container:o}=e,a=E(j,t);return(0,y.jsx)(P,{scope:t,forceMount:r,children:(0,y.jsx)(h.z,{present:r||a.open,children:(0,y.jsx)(f.h,{asChild:!0,container:o,children:n})})})};W.displayName=j;var A="PopoverContent",O=n.forwardRef((e,t)=>{let r=I(A,e.__scopePopover),{forceMount:n=r.forceMount,...o}=e,a=E(A,e.__scopePopover);return(0,y.jsx)(h.z,{present:n||a.open,children:a.modal?(0,y.jsx)(_,{...o,ref:t}):(0,y.jsx)(F,{...o,ref:t})})});O.displayName=A;var L=(0,m.Z8)("PopoverContent.RemoveScroll"),_=n.forwardRef((e,t)=>{let r=E(A,e.__scopePopover),i=n.useRef(null),l=(0,a.e)(t,i),s=n.useRef(!1);return n.useEffect(()=>{let e=i.current;if(e)return(0,g.Ry)(e)},[]),(0,y.jsx)(w.Z,{as:L,allowPinchZoom:!0,children:(0,y.jsx)(U,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),s.current||r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;s.current=n},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),F=n.forwardRef((e,t)=>{let r=E(A,e.__scopePopover),o=n.useRef(!1),a=n.useRef(!1);return(0,y.jsx)(U,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),U=n.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:h,onInteractOutside:p,...m}=e,v=E(A,r),g=k(r);return(0,s.EW)(),(0,y.jsx)(u.M,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,y.jsx)(l.XB,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:p,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:h,onDismiss:()=>v.onOpenChange(!1),children:(0,y.jsx)(c.VY,{"data-state":Y(v.open),role:"dialog",id:v.contentId,...g,...m,ref:t,style:{...m.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),V="PopoverClose";function Y(e){return e?"open":"closed"}n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,a=E(V,r);return(0,y.jsx)(p.WV.button,{type:"button",...n,ref:t,onClick:(0,o.M)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=V,n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=k(r);return(0,y.jsx)(c.Eh,{...o,...n,ref:t})}).displayName="PopoverArrow";var B=S,z=R,H=N,Z=W,q=O},93500:(e,t,r)=>{r.d(t,{Indicator:()=>F,Item:()=>_,Root:()=>L});var n=r(60343),o=r(70112),a=r(48367),i=r(13295),l=r(54928),s=r(469),u=r(21110),d=r(98375),c=r(22595),f=r(12716),h=r(26983),p=r(98768),m="Radio",[v,g]=(0,i.b)(m),[w,y]=v(m),b=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:i,checked:s=!1,required:u,disabled:d,value:c="on",onCheck:f,form:h,...m}=e,[v,g]=n.useState(null),y=(0,a.e)(t,e=>g(e)),b=n.useRef(!1),x=!v||h||!!v.closest("form");return(0,p.jsxs)(w,{scope:r,checked:s,disabled:d,children:[(0,p.jsx)(l.WV.button,{type:"button",role:"radio","aria-checked":s,"data-state":C(s),"data-disabled":d?"":void 0,disabled:d,value:c,...m,ref:y,onClick:(0,o.M)(e.onClick,e=>{s||f?.(),x&&(b.current=e.isPropagationStopped(),b.current||e.stopPropagation())})}),x&&(0,p.jsx)(k,{control:v,bubbles:!b.current,name:i,value:c,checked:s,required:u,disabled:d,form:h,style:{transform:"translateX(-100%)"}})]})});b.displayName=m;var x="RadioIndicator",M=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...o}=e,a=y(x,r);return(0,p.jsx)(h.z,{present:n||a.checked,children:(0,p.jsx)(l.WV.span,{"data-state":C(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t})})});M.displayName=x;var k=n.forwardRef(({__scopeRadio:e,control:t,checked:r,bubbles:o=!0,...i},s)=>{let u=n.useRef(null),d=(0,a.e)(u,s),h=(0,f.D)(r),m=(0,c.t)(t);return n.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[h,r,o]),(0,p.jsx)(l.WV.input,{type:"radio","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:d,style:{...i.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function C(e){return e?"checked":"unchecked"}k.displayName="RadioBubbleInput";var E=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],S="RadioGroup",[D,R]=(0,i.b)(S,[s.Pc,g]),T=(0,s.Pc)(),N=g(),[j,P]=D(S),I=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:o,value:a,required:i=!1,disabled:c=!1,orientation:f,dir:h,loop:m=!0,onValueChange:v,...g}=e,w=T(r),y=(0,d.gm)(h),[b,x]=(0,u.T)({prop:a,defaultProp:o??null,onChange:v,caller:S});return(0,p.jsx)(j,{scope:r,name:n,required:i,disabled:c,value:b,onValueChange:x,children:(0,p.jsx)(s.fC,{asChild:!0,...w,orientation:f,dir:y,loop:m,children:(0,p.jsx)(l.WV.div,{role:"radiogroup","aria-required":i,"aria-orientation":f,"data-disabled":c?"":void 0,dir:y,...g,ref:t})})})});I.displayName=S;var W="RadioGroupItem",A=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:i,...l}=e,u=P(W,r),d=u.disabled||i,c=T(r),f=N(r),h=n.useRef(null),m=(0,a.e)(t,h),v=u.value===l.value,g=n.useRef(!1);return n.useEffect(()=>{let e=e=>{E.includes(e.key)&&(g.current=!0)},t=()=>g.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,p.jsx)(s.ck,{asChild:!0,...c,focusable:!d,active:v,children:(0,p.jsx)(b,{disabled:d,required:u.required,checked:v,...f,...l,name:u.name,ref:m,onCheck:()=>u.onValueChange(l.value),onKeyDown:(0,o.M)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.M)(l.onFocus,()=>{g.current&&h.current?.click()})})})});A.displayName=W;var O=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,o=N(r);return(0,p.jsx)(M,{...o,...n,ref:t})});O.displayName="RadioGroupIndicator";var L=I,_=A,F=O},469:(e,t,r)=>{r.d(t,{Pc:()=>x,ck:()=>j,fC:()=>N});var n=r(60343),o=r(70112),a=r(4632),i=r(48367),l=r(13295),s=r(36183),u=r(54928),d=r(20772),c=r(21110),f=r(98375),h=r(98768),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[g,w,y]=(0,a.B)(v),[b,x]=(0,l.b)(v,[y]),[M,k]=b(v),C=n.forwardRef((e,t)=>(0,h.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(E,{...e,ref:t})})}));C.displayName=v;var E=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:l=!1,dir:s,currentTabStopId:g,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:b,onEntryFocus:x,preventScrollOnEntryFocus:k=!1,...C}=e,E=n.useRef(null),S=(0,i.e)(t,E),D=(0,f.gm)(s),[R,N]=(0,c.T)({prop:g,defaultProp:y??null,onChange:b,caller:v}),[j,P]=n.useState(!1),I=(0,d.W)(x),W=w(r),A=n.useRef(!1),[O,L]=n.useState(0);return n.useEffect(()=>{let e=E.current;if(e)return e.addEventListener(p,I),()=>e.removeEventListener(p,I)},[I]),(0,h.jsx)(M,{scope:r,orientation:a,dir:D,loop:l,currentTabStopId:R,onItemFocus:n.useCallback(e=>N(e),[N]),onItemShiftTab:n.useCallback(()=>P(!0),[]),onFocusableItemAdd:n.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>L(e=>e-1),[]),children:(0,h.jsx)(u.WV.div,{tabIndex:j||0===O?-1:0,"data-orientation":a,...C,ref:S,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{A.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!A.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=W().filter(e=>e.focusable);T([e.find(e=>e.active),e.find(e=>e.id===R),...e].filter(Boolean).map(e=>e.ref.current),k)}}A.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>P(!1))})})}),S="RovingFocusGroupItem",D=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:l,children:d,...c}=e,f=(0,s.M)(),p=l||f,m=k(S,r),v=m.currentTabStopId===p,y=w(r),{onFocusableItemAdd:b,onFocusableItemRemove:x,currentTabStopId:M}=m;return n.useEffect(()=>{if(a)return b(),()=>x()},[a,b,x]),(0,h.jsx)(g.ItemSlot,{scope:r,id:p,focusable:a,active:i,children:(0,h.jsx)(u.WV.span,{tabIndex:v?0:-1,"data-orientation":m.orientation,...c,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?m.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return R[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=m.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>T(r))}}),children:"function"==typeof d?d({isCurrentTabStop:v,hasTabStop:null!=M}):d})})});D.displayName=S;var R={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function T(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var N=C,j=D},97514:(e,t,r)=>{r.d(t,{Corner:()=>$,Root:()=>q,ScrollAreaScrollbar:()=>k,ScrollAreaThumb:()=>W,Viewport:()=>K});var n=r(60343),o=r(54928),a=r(26983),i=r(13295),l=r(48367),s=r(20772),u=r(98375),d=r(32183),c=r(24039),f=r(70112),h=r(98768),p="ScrollArea",[m,v]=(0,i.b)(p),[g,w]=m(p),y=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:a="hover",dir:i,scrollHideDelay:s=600,...d}=e,[c,f]=n.useState(null),[p,m]=n.useState(null),[v,w]=n.useState(null),[y,b]=n.useState(null),[x,M]=n.useState(null),[k,C]=n.useState(0),[E,S]=n.useState(0),[D,R]=n.useState(!1),[T,N]=n.useState(!1),j=(0,l.e)(t,e=>f(e)),P=(0,u.gm)(i);return(0,h.jsx)(g,{scope:r,type:a,dir:P,scrollHideDelay:s,scrollArea:c,viewport:p,onViewportChange:m,content:v,onContentChange:w,scrollbarX:y,onScrollbarXChange:b,scrollbarXEnabled:D,onScrollbarXEnabledChange:R,scrollbarY:x,onScrollbarYChange:M,scrollbarYEnabled:T,onScrollbarYEnabledChange:N,onCornerWidthChange:C,onCornerHeightChange:S,children:(0,h.jsx)(o.WV.div,{dir:P,...d,ref:j,style:{position:"relative","--radix-scroll-area-corner-width":k+"px","--radix-scroll-area-corner-height":E+"px",...e.style}})})});y.displayName=p;var b="ScrollAreaViewport",x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:a,nonce:i,...s}=e,u=w(b,r),d=n.useRef(null),c=(0,l.e)(t,d,u.onViewportChange);return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,h.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...s,ref:c,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,h.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:a})})]})});x.displayName=b;var M="ScrollAreaScrollbar",k=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,a=w(M,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:l}=a,s="horizontal"===e.orientation;return n.useEffect(()=>(s?i(!0):l(!0),()=>{s?i(!1):l(!1)}),[s,i,l]),"hover"===a.type?(0,h.jsx)(C,{...o,ref:t,forceMount:r}):"scroll"===a.type?(0,h.jsx)(E,{...o,ref:t,forceMount:r}):"auto"===a.type?(0,h.jsx)(S,{...o,ref:t,forceMount:r}):"always"===a.type?(0,h.jsx)(D,{...o,ref:t}):null});k.displayName=M;var C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,i=w(M,e.__scopeScrollArea),[l,s]=n.useState(!1);return n.useEffect(()=>{let e=i.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[i.scrollArea,i.scrollHideDelay]),(0,h.jsx)(a.z,{present:r||l,children:(0,h.jsx)(S,{"data-state":l?"visible":"hidden",...o,ref:t})})}),E=n.forwardRef((e,t)=>{var r,o;let{forceMount:i,...l}=e,s=w(M,e.__scopeScrollArea),u="horizontal"===e.orientation,d=H(()=>p("SCROLL_END"),100),[c,p]=(r="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>o[e][t]??e,r));return n.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>p("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,s.scrollHideDelay,p]),n.useEffect(()=>{let e=s.viewport,t=u?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(p("SCROLL"),d()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[s.viewport,u,p,d]),(0,h.jsx)(a.z,{present:i||"hidden"!==c,children:(0,h.jsx)(D,{"data-state":"hidden"===c?"hidden":"visible",...l,ref:t,onPointerEnter:(0,f.M)(e.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:(0,f.M)(e.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),S=n.forwardRef((e,t)=>{let r=w(M,e.__scopeScrollArea),{forceMount:o,...i}=e,[l,s]=n.useState(!1),u="horizontal"===e.orientation,d=H(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(u?e:t)}},10);return Z(r.viewport,d),Z(r.content,d),(0,h.jsx)(a.z,{present:o||l,children:(0,h.jsx)(D,{"data-state":l?"visible":"hidden",...i,ref:t})})}),D=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,a=w(M,e.__scopeScrollArea),i=n.useRef(null),l=n.useRef(0),[s,u]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=U(s.viewport,s.content),c={...o,sizes:s,onSizesChange:u,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:e=>l.current=e};function f(e,t){return function(e,t,r,n="ltr"){let o=V(r),a=t||o/2,i=r.scrollbar.paddingStart+a,l=r.scrollbar.size-r.scrollbar.paddingEnd-(o-a),s=r.content-r.viewport;return B([i,l],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,l.current,s,t)}return"horizontal"===r?(0,h.jsx)(R,{...c,ref:t,onThumbPositionChange:()=>{if(a.viewport&&i.current){let e=Y(a.viewport.scrollLeft,s,a.dir);i.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollLeft=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollLeft=f(e,a.dir))}}):"vertical"===r?(0,h.jsx)(T,{...c,ref:t,onThumbPositionChange:()=>{if(a.viewport&&i.current){let e=Y(a.viewport.scrollTop,s);i.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollTop=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollTop=f(e))}}):null}),R=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,i=w(M,e.__scopeScrollArea),[s,u]=n.useState(),d=n.useRef(null),c=(0,l.e)(t,d,i.onScrollbarXChange);return n.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,h.jsx)(P,{"data-orientation":"horizontal",...a,ref:c,sizes:r,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":V(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&o({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:F(s.paddingLeft),paddingEnd:F(s.paddingRight)}})}})}),T=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,i=w(M,e.__scopeScrollArea),[s,u]=n.useState(),d=n.useRef(null),c=(0,l.e)(t,d,i.onScrollbarYChange);return n.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,h.jsx)(P,{"data-orientation":"vertical",...a,ref:c,sizes:r,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":V(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&i.viewport&&s&&o({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:F(s.paddingTop),paddingEnd:F(s.paddingBottom)}})}})}),[N,j]=m(M),P=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:a,hasThumb:i,onThumbChange:u,onThumbPointerUp:d,onThumbPointerDown:c,onThumbPositionChange:p,onDragScroll:m,onWheelScroll:v,onResize:g,...y}=e,b=w(M,r),[x,k]=n.useState(null),C=(0,l.e)(t,e=>k(e)),E=n.useRef(null),S=n.useRef(""),D=b.viewport,R=a.content-a.viewport,T=(0,s.W)(v),j=(0,s.W)(p),P=H(g,10);function I(e){E.current&&m({x:e.clientX-E.current.left,y:e.clientY-E.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;x?.contains(t)&&T(e,R)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[D,x,R,T]),n.useEffect(j,[a,j]),Z(x,P),Z(b.content,P),(0,h.jsx)(N,{scope:r,scrollbar:x,hasThumb:i,onThumbChange:(0,s.W)(u),onThumbPointerUp:(0,s.W)(d),onThumbPositionChange:j,onThumbPointerDown:(0,s.W)(c),children:(0,h.jsx)(o.WV.div,{...y,ref:C,style:{position:"absolute",...y.style},onPointerDown:(0,f.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),E.current=x.getBoundingClientRect(),S.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",b.viewport&&(b.viewport.style.scrollBehavior="auto"),I(e))}),onPointerMove:(0,f.M)(e.onPointerMove,I),onPointerUp:(0,f.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=S.current,b.viewport&&(b.viewport.style.scrollBehavior=""),E.current=null})})})}),I="ScrollAreaThumb",W=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=j(I,e.__scopeScrollArea);return(0,h.jsx)(a.z,{present:r||o.hasThumb,children:(0,h.jsx)(A,{ref:t,...n})})}),A=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:a,...i}=e,s=w(I,r),u=j(I,r),{onThumbPositionChange:d}=u,c=(0,l.e)(t,e=>u.onThumbChange(e)),p=n.useRef(void 0),m=H(()=>{p.current&&(p.current(),p.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{if(m(),!p.current){let t=z(e,d);p.current=t,d()}};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,m,d]),(0,h.jsx)(o.WV.div,{"data-state":u.hasThumb?"visible":"hidden",...i,ref:c,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...a},onPointerDownCapture:(0,f.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;u.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,f.M)(e.onPointerUp,u.onThumbPointerUp)})});W.displayName=I;var O="ScrollAreaCorner",L=n.forwardRef((e,t)=>{let r=w(O,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,h.jsx)(_,{...e,ref:t}):null});L.displayName=O;var _=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...a}=e,i=w(O,r),[l,s]=n.useState(0),[u,d]=n.useState(0),c=!!(l&&u);return Z(i.scrollbarX,()=>{let e=i.scrollbarX?.offsetHeight||0;i.onCornerHeightChange(e),d(e)}),Z(i.scrollbarY,()=>{let e=i.scrollbarY?.offsetWidth||0;i.onCornerWidthChange(e),s(e)}),c?(0,h.jsx)(o.WV.div,{...a,ref:t,style:{width:l,height:u,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function F(e){return e?parseInt(e,10):0}function U(e,t){let r=e/t;return isNaN(r)?0:r}function V(e){let t=U(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function Y(e,t,r="ltr"){let n=V(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,a=t.scrollbar.size-o,i=t.content-t.viewport,l=(0,c.u)(e,"ltr"===r?[0,i]:[-1*i,0]);return B([0,i],[0,a-n])(l)}function B(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var z=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function o(){let a={left:e.scrollLeft,top:e.scrollTop},i=r.left!==a.left,l=r.top!==a.top;(i||l)&&t(),r=a,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function H(e,t){let r=(0,s.W)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function Z(e,t){let r=(0,s.W)(t);(0,d.b)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var q=y,K=x,$=L},51881:(e,t,r)=>{r.d(t,{$G:()=>eB,B4:()=>eP,JO:()=>eI,VY:()=>eA,Z0:()=>ez,ZA:()=>eL,__:()=>e_,ck:()=>eF,eT:()=>eU,fC:()=>eN,h_:()=>eW,l_:()=>eO,u_:()=>eY,wU:()=>eV,xz:()=>ej});var n=r(60343),o=r(61222),a=r(24039),i=r(70112),l=r(4632),s=r(48367),u=r(13295),d=r(98375),c=r(97938),f=r(47962),h=r(64807),p=r(36183),m=r(18251),v=r(54054),g=r(54928),w=r(73404),y=r(20772),b=r(21110),x=r(32183),M=r(12716),k=r(50750),C=r(17404),E=r(85521),S=r(98768),D=[" ","Enter","ArrowUp","ArrowDown"],R=[" ","Enter"],T="Select",[N,j,P]=(0,l.B)(T),[I,W]=(0,u.b)(T,[P,m.D7]),A=(0,m.D7)(),[O,L]=I(T),[_,F]=I(T),U=e=>{let{__scopeSelect:t,children:r,open:o,defaultOpen:a,onOpenChange:i,value:l,defaultValue:s,onValueChange:u,dir:c,name:f,autoComplete:h,disabled:v,required:g,form:w}=e,y=A(t),[x,M]=n.useState(null),[k,C]=n.useState(null),[E,D]=n.useState(!1),R=(0,d.gm)(c),[j,P]=(0,b.T)({prop:o,defaultProp:a??!1,onChange:i,caller:T}),[I,W]=(0,b.T)({prop:l,defaultProp:s,onChange:u,caller:T}),L=n.useRef(null),F=!x||w||!!x.closest("form"),[U,V]=n.useState(new Set),Y=Array.from(U).map(e=>e.props.value).join(";");return(0,S.jsx)(m.fC,{...y,children:(0,S.jsxs)(O,{required:g,scope:t,trigger:x,onTriggerChange:M,valueNode:k,onValueNodeChange:C,valueNodeHasChildren:E,onValueNodeHasChildrenChange:D,contentId:(0,p.M)(),value:I,onValueChange:W,open:j,onOpenChange:P,dir:R,triggerPointerDownPosRef:L,disabled:v,children:[(0,S.jsx)(N.Provider,{scope:t,children:(0,S.jsx)(_,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{V(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{V(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),F?(0,S.jsxs)(eS,{"aria-hidden":!0,required:g,tabIndex:-1,name:f,autoComplete:h,value:I,onChange:e=>W(e.target.value),disabled:v,form:w,children:[void 0===I?(0,S.jsx)("option",{value:""}):null,Array.from(U)]},Y):null]})})};U.displayName=T;var V="SelectTrigger",Y=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:o=!1,...a}=e,l=A(r),u=L(V,r),d=u.disabled||o,c=(0,s.e)(t,u.onTriggerChange),f=j(r),h=n.useRef("touch"),[p,v,w]=eR(e=>{let t=f().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eT(t,e,r);void 0!==n&&u.onValueChange(n.value)}),y=e=>{d||(u.onOpenChange(!0),w()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,S.jsx)(m.ee,{asChild:!0,...l,children:(0,S.jsx)(g.WV.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eD(u.value)?"":void 0,...a,ref:c,onClick:(0,i.M)(a.onClick,e=>{e.currentTarget.focus(),"mouse"!==h.current&&y(e)}),onPointerDown:(0,i.M)(a.onPointerDown,e=>{h.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(y(e),e.preventDefault())}),onKeyDown:(0,i.M)(a.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||v(e.key),(!t||" "!==e.key)&&D.includes(e.key)&&(y(),e.preventDefault())})})})});Y.displayName=V;var B="SelectValue",z=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:o,children:a,placeholder:i="",...l}=e,u=L(B,r),{onValueNodeHasChildrenChange:d}=u,c=void 0!==a,f=(0,s.e)(t,u.onValueNodeChange);return(0,x.b)(()=>{d(c)},[d,c]),(0,S.jsx)(g.WV.span,{...l,ref:f,style:{pointerEvents:"none"},children:eD(u.value)?(0,S.jsx)(S.Fragment,{children:i}):a})});z.displayName=B;var H=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...o}=e;return(0,S.jsx)(g.WV.span,{"aria-hidden":!0,...o,ref:t,children:n||"▼"})});H.displayName="SelectIcon";var Z=e=>(0,S.jsx)(v.h,{asChild:!0,...e});Z.displayName="SelectPortal";var q="SelectContent",K=n.forwardRef((e,t)=>{let r=L(q,e.__scopeSelect),[a,i]=n.useState();return((0,x.b)(()=>{i(new DocumentFragment)},[]),r.open)?(0,S.jsx)(G,{...e,ref:t}):a?o.createPortal((0,S.jsx)($,{scope:e.__scopeSelect,children:(0,S.jsx)(N.Slot,{scope:e.__scopeSelect,children:(0,S.jsx)("div",{children:e.children})})}),a):null});K.displayName=q;var[$,Q]=I(q),X=(0,w.Z8)("SelectContent.RemoveScroll"),G=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:l,onPointerDownOutside:u,side:d,sideOffset:p,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:w,collisionPadding:y,sticky:b,hideWhenDetached:x,avoidCollisions:M,...k}=e,D=L(q,r),[R,T]=n.useState(null),[N,P]=n.useState(null),I=(0,s.e)(t,e=>T(e)),[W,A]=n.useState(null),[O,_]=n.useState(null),F=j(r),[U,V]=n.useState(!1),Y=n.useRef(!1);n.useEffect(()=>{if(R)return(0,C.Ry)(R)},[R]),(0,f.EW)();let B=n.useCallback(e=>{let[t,...r]=F().map(e=>e.ref.current),[n]=r.slice(-1),o=document.activeElement;for(let r of e)if(r===o||(r?.scrollIntoView({block:"nearest"}),r===t&&N&&(N.scrollTop=0),r===n&&N&&(N.scrollTop=N.scrollHeight),r?.focus(),document.activeElement!==o))return},[F,N]),z=n.useCallback(()=>B([W,R]),[B,W,R]);n.useEffect(()=>{U&&z()},[U,z]);let{onOpenChange:H,triggerPointerDownPosRef:Z}=D;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(Z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(Z.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||H(!1),document.removeEventListener("pointermove",t),Z.current=null};return null!==Z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,H,Z]),n.useEffect(()=>{let e=()=>H(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[H]);let[K,Q]=eR(e=>{let t=F().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eT(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),G=n.useCallback((e,t,r)=>{let n=!Y.current&&!r;(void 0!==D.value&&D.value===t||n)&&(A(e),n&&(Y.current=!0))},[D.value]),et=n.useCallback(()=>R?.focus(),[R]),er=n.useCallback((e,t,r)=>{let n=!Y.current&&!r;(void 0!==D.value&&D.value===t||n)&&_(e)},[D.value]),en="popper"===o?ee:J,eo=en===ee?{side:d,sideOffset:p,align:m,alignOffset:v,arrowPadding:g,collisionBoundary:w,collisionPadding:y,sticky:b,hideWhenDetached:x,avoidCollisions:M}:{};return(0,S.jsx)($,{scope:r,content:R,viewport:N,onViewportChange:P,itemRefCallback:G,selectedItem:W,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:z,selectedItemText:O,position:o,isPositioned:U,searchRef:K,children:(0,S.jsx)(E.Z,{as:X,allowPinchZoom:!0,children:(0,S.jsx)(h.M,{asChild:!0,trapped:D.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.M)(a,e=>{D.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,S.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>D.onOpenChange(!1),children:(0,S.jsx)(en,{role:"listbox",id:D.contentId,"data-state":D.open?"open":"closed",dir:D.dir,onContextMenu:e=>e.preventDefault(),...k,...eo,onPlaced:()=>V(!0),ref:I,style:{display:"flex",flexDirection:"column",outline:"none",...k.style},onKeyDown:(0,i.M)(k.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Q(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=F().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>B(t)),e.preventDefault()}})})})})})})});G.displayName="SelectContentImpl";var J=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:o,...i}=e,l=L(q,r),u=Q(q,r),[d,c]=n.useState(null),[f,h]=n.useState(null),p=(0,s.e)(t,e=>h(e)),m=j(r),v=n.useRef(!1),w=n.useRef(!0),{viewport:y,selectedItem:b,selectedItemText:M,focusSelectedItem:k}=u,C=n.useCallback(()=>{if(l.trigger&&l.valueNode&&d&&f&&y&&b&&M){let e=l.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),r=l.valueNode.getBoundingClientRect(),n=M.getBoundingClientRect();if("rtl"!==l.dir){let o=n.left-t.left,i=r.left-o,l=e.left-i,s=e.width+l,u=Math.max(s,t.width),c=window.innerWidth-10,f=(0,a.u)(i,[10,Math.max(10,c-u)]);d.style.minWidth=s+"px",d.style.left=f+"px"}else{let o=t.right-n.right,i=window.innerWidth-r.right-o,l=window.innerWidth-e.right-i,s=e.width+l,u=Math.max(s,t.width),c=window.innerWidth-10,f=(0,a.u)(i,[10,Math.max(10,c-u)]);d.style.minWidth=s+"px",d.style.right=f+"px"}let i=m(),s=window.innerHeight-20,u=y.scrollHeight,c=window.getComputedStyle(f),h=parseInt(c.borderTopWidth,10),p=parseInt(c.paddingTop,10),g=parseInt(c.borderBottomWidth,10),w=h+p+u+parseInt(c.paddingBottom,10)+g,x=Math.min(5*b.offsetHeight,w),k=window.getComputedStyle(y),C=parseInt(k.paddingTop,10),E=parseInt(k.paddingBottom,10),S=e.top+e.height/2-10,D=b.offsetHeight/2,R=h+p+(b.offsetTop+D);if(R<=S){let e=i.length>0&&b===i[i.length-1].ref.current;d.style.bottom="0px";let t=f.clientHeight-y.offsetTop-y.offsetHeight;d.style.height=R+Math.max(s-S,D+(e?E:0)+t+g)+"px"}else{let e=i.length>0&&b===i[0].ref.current;d.style.top="0px";let t=Math.max(S,h+y.offsetTop+(e?C:0)+D);d.style.height=t+(w-R)+"px",y.scrollTop=R-S+y.offsetTop}d.style.margin="10px 0",d.style.minHeight=x+"px",d.style.maxHeight=s+"px",o?.(),requestAnimationFrame(()=>v.current=!0)}},[m,l.trigger,l.valueNode,d,f,y,b,M,l.dir,o]);(0,x.b)(()=>C(),[C]);let[E,D]=n.useState();(0,x.b)(()=>{f&&D(window.getComputedStyle(f).zIndex)},[f]);let R=n.useCallback(e=>{e&&!0===w.current&&(C(),k?.(),w.current=!1)},[C,k]);return(0,S.jsx)(et,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:v,onScrollButtonChange:R,children:(0,S.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,S.jsx)(g.WV.div,{...i,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});J.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:o=10,...a}=e,i=A(r);return(0,S.jsx)(m.VY,{...i,...a,ref:t,align:n,collisionPadding:o,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=I(q,{}),en="SelectViewport",eo=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:o,...a}=e,l=Q(en,r),u=er(en,r),d=(0,s.e)(t,l.onViewportChange),c=n.useRef(0);return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,S.jsx)(N.Slot,{scope:r,children:(0,S.jsx)(g.WV.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:(0,i.M)(a.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if(n?.current&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,o=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(o<n){let a=o+e,i=Math.min(n,a),l=a-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=l>0?l:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});eo.displayName=en;var ea="SelectGroup",[ei,el]=I(ea),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=(0,p.M)();return(0,S.jsx)(ei,{scope:r,id:o,children:(0,S.jsx)(g.WV.div,{role:"group","aria-labelledby":o,...n,ref:t})})});es.displayName=ea;var eu="SelectLabel",ed=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=el(eu,r);return(0,S.jsx)(g.WV.div,{id:o.id,...n,ref:t})});ed.displayName=eu;var ec="SelectItem",[ef,eh]=I(ec),ep=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:o,disabled:a=!1,textValue:l,...u}=e,d=L(ec,r),c=Q(ec,r),f=d.value===o,[h,m]=n.useState(l??""),[v,w]=n.useState(!1),y=(0,s.e)(t,e=>c.itemRefCallback?.(e,o,a)),b=(0,p.M)(),x=n.useRef("touch"),M=()=>{a||(d.onValueChange(o),d.onOpenChange(!1))};if(""===o)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,S.jsx)(ef,{scope:r,value:o,disabled:a,textId:b,isSelected:f,onItemTextChange:n.useCallback(e=>{m(t=>t||(e?.textContent??"").trim())},[]),children:(0,S.jsx)(N.ItemSlot,{scope:r,value:o,disabled:a,textValue:h,children:(0,S.jsx)(g.WV.div,{role:"option","aria-labelledby":b,"data-highlighted":v?"":void 0,"aria-selected":f&&v,"data-state":f?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...u,ref:y,onFocus:(0,i.M)(u.onFocus,()=>w(!0)),onBlur:(0,i.M)(u.onBlur,()=>w(!1)),onClick:(0,i.M)(u.onClick,()=>{"mouse"!==x.current&&M()}),onPointerUp:(0,i.M)(u.onPointerUp,()=>{"mouse"===x.current&&M()}),onPointerDown:(0,i.M)(u.onPointerDown,e=>{x.current=e.pointerType}),onPointerMove:(0,i.M)(u.onPointerMove,e=>{x.current=e.pointerType,a?c.onItemLeave?.():"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.M)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,i.M)(u.onKeyDown,e=>{c.searchRef?.current!==""&&" "===e.key||(R.includes(e.key)&&M()," "===e.key&&e.preventDefault())})})})})});ep.displayName=ec;var em="SelectItemText",ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:a,style:i,...l}=e,u=L(em,r),d=Q(em,r),c=eh(em,r),f=F(em,r),[h,p]=n.useState(null),m=(0,s.e)(t,e=>p(e),c.onItemTextChange,e=>d.itemTextRefCallback?.(e,c.value,c.disabled)),v=h?.textContent,w=n.useMemo(()=>(0,S.jsx)("option",{value:c.value,disabled:c.disabled,children:v},c.value),[c.disabled,c.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:b}=f;return(0,x.b)(()=>(y(w),()=>b(w)),[y,b,w]),(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(g.WV.span,{id:c.textId,...l,ref:m}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?o.createPortal(l.children,u.valueNode):null]})});ev.displayName=em;var eg="SelectItemIndicator",ew=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return eh(eg,r).isSelected?(0,S.jsx)(g.WV.span,{"aria-hidden":!0,...n,ref:t}):null});ew.displayName=eg;var ey="SelectScrollUpButton",eb=n.forwardRef((e,t)=>{let r=Q(ey,e.__scopeSelect),o=er(ey,e.__scopeSelect),[a,i]=n.useState(!1),l=(0,s.e)(t,o.onScrollButtonChange);return(0,x.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,S.jsx)(ek,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eb.displayName=ey;var ex="SelectScrollDownButton",eM=n.forwardRef((e,t)=>{let r=Q(ex,e.__scopeSelect),o=er(ex,e.__scopeSelect),[a,i]=n.useState(!1),l=(0,s.e)(t,o.onScrollButtonChange);return(0,x.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),a?(0,S.jsx)(ek,{...e,ref:l,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eM.displayName=ex;var ek=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:o,...a}=e,l=Q("SelectScrollButton",r),s=n.useRef(null),u=j(r),d=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),(0,x.b)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,S.jsx)(g.WV.div,{"aria-hidden":!0,...a,ref:t,style:{flexShrink:0,...a.style},onPointerDown:(0,i.M)(a.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(o,50))}),onPointerMove:(0,i.M)(a.onPointerMove,()=>{l.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(o,50))}),onPointerLeave:(0,i.M)(a.onPointerLeave,()=>{d()})})}),eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,S.jsx)(g.WV.div,{"aria-hidden":!0,...n,ref:t})});eC.displayName="SelectSeparator";var eE="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,o=A(r),a=L(eE,r),i=Q(eE,r);return a.open&&"popper"===i.position?(0,S.jsx)(m.Eh,{...o,...n,ref:t}):null}).displayName=eE;var eS=n.forwardRef(({__scopeSelect:e,value:t,...r},o)=>{let a=n.useRef(null),i=(0,s.e)(o,a),l=(0,M.D)(t);return n.useEffect(()=>{let e=a.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(l!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[l,t]),(0,S.jsx)(g.WV.select,{...r,style:{...k.C2,...r.style},ref:i,defaultValue:t})});function eD(e){return""===e||void 0===e}function eR(e){let t=(0,y.W)(e),r=n.useRef(""),o=n.useRef(0),a=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),i=n.useCallback(()=>{r.current="",window.clearTimeout(o.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),[r,a,i]}function eT(e,t,r){var n;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===o.length&&(a=a.filter(e=>e!==r));let i=a.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return i!==r?i:void 0}eS.displayName="SelectBubbleInput";var eN=U,ej=Y,eP=z,eI=H,eW=Z,eA=K,eO=eo,eL=es,e_=ed,eF=ep,eU=ev,eV=ew,eY=eb,eB=eM,ez=eC},62227:(e,t,r)=>{r.d(t,{f:()=>u});var n=r(60343),o=r(54928),a=r(98768),i="horizontal",l=["horizontal","vertical"],s=n.forwardRef((e,t)=>{let{decorative:r,orientation:n=i,...s}=e,u=l.includes(n)?n:i;return(0,a.jsx)(o.WV.div,{"data-orientation":u,...r?{role:"none"}:{"aria-orientation":"vertical"===u?u:void 0,role:"separator"},...s,ref:t})});s.displayName="Separator";var u=s},45305:(e,t,r)=>{r.d(t,{Range:()=>z,Root:()=>Y,Thumb:()=>H,Track:()=>B});var n=r(60343),o=r(24039),a=r(70112),i=r(48367),l=r(13295),s=r(21110),u=r(98375),d=r(12716),c=r(22595),f=r(54928),h=r(4632),p=r(98768),m=["PageUp","PageDown"],v=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],g={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},w="Slider",[y,b,x]=(0,h.B)(w),[M,k]=(0,l.b)(w,[x]),[C,E]=M(w),S=n.forwardRef((e,t)=>{let{name:r,min:i=0,max:l=100,step:u=1,orientation:d="horizontal",disabled:c=!1,minStepsBetweenThumbs:f=0,defaultValue:h=[i],value:g,onValueChange:w=()=>{},onValueCommit:b=()=>{},inverted:x=!1,form:M,...k}=e,E=n.useRef(new Set),S=n.useRef(0),D="horizontal"===d?T:N,[R=[],j]=(0,s.T)({prop:g,defaultProp:h,onChange:e=>{let t=[...E.current];t[S.current]?.focus(),w(e)}}),P=n.useRef(R);function I(e,t,{commit:r}={commit:!1}){let n=(String(u).split(".")[1]||"").length,a=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-i)/u)*u+i,n),s=(0,o.u)(a,[i,l]);j((e=[])=>{var n,o;let a=function(e=[],t,r){let n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,s,t);if(n=a,!(!((o=f*u)>0)||Math.min(...n.slice(0,-1).map((e,t)=>n[t+1]-e))>=o))return e;{S.current=a.indexOf(s);let t=String(a)!==String(e);return t&&r&&b(a),t?a:e}})}return(0,p.jsx)(C,{scope:e.__scopeSlider,name:r,disabled:c,min:i,max:l,valueIndexToChangeRef:S,thumbs:E.current,values:R,orientation:d,form:M,children:(0,p.jsx)(y.Provider,{scope:e.__scopeSlider,children:(0,p.jsx)(y.Slot,{scope:e.__scopeSlider,children:(0,p.jsx)(D,{"aria-disabled":c,"data-disabled":c?"":void 0,...k,ref:t,onPointerDown:(0,a.M)(k.onPointerDown,()=>{c||(P.current=R)}),min:i,max:l,inverted:x,onSlideStart:c?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t));return r.indexOf(Math.min(...r))}(R,e);I(e,t)},onSlideMove:c?void 0:function(e){I(e,S.current)},onSlideEnd:c?void 0:function(){let e=P.current[S.current];R[S.current]!==e&&b(R)},onHomeKeyDown:()=>!c&&I(i,0,{commit:!0}),onEndKeyDown:()=>!c&&I(l,R.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!c){let r=m.includes(e.key)||e.shiftKey&&v.includes(e.key),n=S.current;I(R[n]+u*(r?10:1)*t,n,{commit:!0})}}})})})})});S.displayName=w;var[D,R]=M(w,{startEdge:"left",endEdge:"right",size:"width",direction:1}),T=n.forwardRef((e,t)=>{let{min:r,max:o,dir:a,inverted:l,onSlideStart:s,onSlideMove:d,onSlideEnd:c,onStepKeyDown:f,...h}=e,[m,v]=n.useState(null),w=(0,i.e)(t,e=>v(e)),y=n.useRef(void 0),b=(0,u.gm)(a),x="ltr"===b,M=x&&!l||!x&&l;function k(e){let t=y.current||m.getBoundingClientRect(),n=V([0,t.width],M?[r,o]:[o,r]);return y.current=t,n(e-t.left)}return(0,p.jsx)(D,{scope:e.__scopeSlider,startEdge:M?"left":"right",endEdge:M?"right":"left",direction:M?1:-1,size:"width",children:(0,p.jsx)(j,{dir:b,"data-orientation":"horizontal",...h,ref:w,style:{...h.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=k(e.clientX);s?.(t)},onSlideMove:e=>{let t=k(e.clientX);d?.(t)},onSlideEnd:()=>{y.current=void 0,c?.()},onStepKeyDown:e=>{let t=g[M?"from-left":"from-right"].includes(e.key);f?.({event:e,direction:t?-1:1})}})})}),N=n.forwardRef((e,t)=>{let{min:r,max:o,inverted:a,onSlideStart:l,onSlideMove:s,onSlideEnd:u,onStepKeyDown:d,...c}=e,f=n.useRef(null),h=(0,i.e)(t,f),m=n.useRef(void 0),v=!a;function w(e){let t=m.current||f.current.getBoundingClientRect(),n=V([0,t.height],v?[o,r]:[r,o]);return m.current=t,n(e-t.top)}return(0,p.jsx)(D,{scope:e.__scopeSlider,startEdge:v?"bottom":"top",endEdge:v?"top":"bottom",size:"height",direction:v?1:-1,children:(0,p.jsx)(j,{"data-orientation":"vertical",...c,ref:h,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=w(e.clientY);l?.(t)},onSlideMove:e=>{let t=w(e.clientY);s?.(t)},onSlideEnd:()=>{m.current=void 0,u?.()},onStepKeyDown:e=>{let t=g[v?"from-bottom":"from-top"].includes(e.key);d?.({event:e,direction:t?-1:1})}})})}),j=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:i,onHomeKeyDown:l,onEndKeyDown:s,onStepKeyDown:u,...d}=e,c=E(w,r);return(0,p.jsx)(f.WV.span,{...d,ref:t,onKeyDown:(0,a.M)(e.onKeyDown,e=>{"Home"===e.key?(l(e),e.preventDefault()):"End"===e.key?(s(e),e.preventDefault()):m.concat(v).includes(e.key)&&(u(e),e.preventDefault())}),onPointerDown:(0,a.M)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),c.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,a.M)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&o(e)}),onPointerUp:(0,a.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),i(e))})})}),P="SliderTrack",I=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,o=E(P,r);return(0,p.jsx)(f.WV.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:t})});I.displayName=P;var W="SliderRange",A=n.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,a=E(W,r),l=R(W,r),s=n.useRef(null),u=(0,i.e)(t,s),d=a.values.length,c=a.values.map(e=>U(e,a.min,a.max));return(0,p.jsx)(f.WV.span,{"data-orientation":a.orientation,"data-disabled":a.disabled?"":void 0,...o,ref:u,style:{...e.style,[l.startEdge]:(d>1?Math.min(...c):0)+"%",[l.endEdge]:100-Math.max(...c)+"%"}})});A.displayName=W;var O="SliderThumb",L=n.forwardRef((e,t)=>{let r=b(e.__scopeSlider),[o,a]=n.useState(null),l=(0,i.e)(t,e=>a(e)),s=n.useMemo(()=>o?r().findIndex(e=>e.ref.current===o):-1,[r,o]);return(0,p.jsx)(_,{...e,ref:l,index:s})}),_=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:o,name:l,...s}=e,u=E(O,r),d=R(O,r),[h,m]=n.useState(null),v=(0,i.e)(t,e=>m(e)),g=!h||u.form||!!h.closest("form"),w=(0,c.t)(h),b=u.values[o],x=void 0===b?0:U(b,u.min,u.max),M=function(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}(o,u.values.length),k=w?.[d.size],C=k?function(e,t,r){let n=e/2,o=V([0,50],[0,n]);return(n-o(t)*r)*r}(k,x,d.direction):0;return n.useEffect(()=>{if(h)return u.thumbs.add(h),()=>{u.thumbs.delete(h)}},[h,u.thumbs]),(0,p.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[d.startEdge]:`calc(${x}% + ${C}px)`},children:[(0,p.jsx)(y.ItemSlot,{scope:e.__scopeSlider,children:(0,p.jsx)(f.WV.span,{role:"slider","aria-label":e["aria-label"]||M,"aria-valuemin":u.min,"aria-valuenow":b,"aria-valuemax":u.max,"aria-orientation":u.orientation,"data-orientation":u.orientation,"data-disabled":u.disabled?"":void 0,tabIndex:u.disabled?void 0:0,...s,ref:v,style:void 0===b?{display:"none"}:e.style,onFocus:(0,a.M)(e.onFocus,()=>{u.valueIndexToChangeRef.current=o})})}),g&&(0,p.jsx)(F,{name:l??(u.name?u.name+(u.values.length>1?"[]":""):void 0),form:u.form,value:b},o)]})});L.displayName=O;var F=n.forwardRef(({__scopeSlider:e,value:t,...r},o)=>{let a=n.useRef(null),l=(0,i.e)(a,o),s=(0,d.D)(t);return n.useEffect(()=>{let e=a.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(s!==t&&r){let n=new Event("input",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[s,t]),(0,p.jsx)(f.WV.input,{style:{display:"none"},...r,ref:l,defaultValue:t})});function U(e,t,r){return(0,o.u)(100/(r-t)*(e-t),[0,100])}function V(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}F.displayName="RadioBubbleInput";var Y=S,B=I,z=A,H=L},44106:(e,t,r)=>{r.d(t,{Root:()=>M,Thumb:()=>k});var n=r(60343),o=r(70112),a=r(48367),i=r(13295),l=r(21110),s=r(12716),u=r(22595),d=r(54928),c=r(98768),f="Switch",[h,p]=(0,i.b)(f),[m,v]=h(f),g=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:s,defaultChecked:u,required:h,disabled:p,value:v="on",onCheckedChange:g,form:w,...y}=e,[M,k]=n.useState(null),C=(0,a.e)(t,e=>k(e)),E=n.useRef(!1),S=!M||w||!!M.closest("form"),[D,R]=(0,l.T)({prop:s,defaultProp:u??!1,onChange:g,caller:f});return(0,c.jsxs)(m,{scope:r,checked:D,disabled:p,children:[(0,c.jsx)(d.WV.button,{type:"button",role:"switch","aria-checked":D,"aria-required":h,"data-state":x(D),"data-disabled":p?"":void 0,disabled:p,value:v,...y,ref:C,onClick:(0,o.M)(e.onClick,e=>{R(e=>!e),S&&(E.current=e.isPropagationStopped(),E.current||e.stopPropagation())})}),S&&(0,c.jsx)(b,{control:M,bubbles:!E.current,name:i,value:v,checked:D,required:h,disabled:p,form:w,style:{transform:"translateX(-100%)"}})]})});g.displayName=f;var w="SwitchThumb",y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=v(w,r);return(0,c.jsx)(d.WV.span,{"data-state":x(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});y.displayName=w;var b=n.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:o=!0,...i},l)=>{let d=n.useRef(null),f=(0,a.e)(d,l),h=(0,s.D)(r),p=(0,u.t)(t);return n.useEffect(()=>{let e=d.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[h,r,o]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:f,style:{...i.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function x(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var M=g,k=y},50384:(e,t,r)=>{r.d(t,{VY:()=>j,aV:()=>T,fC:()=>R,xz:()=>N});var n=r(60343),o=r(70112),a=r(13295),i=r(469),l=r(26983),s=r(54928),u=r(98375),d=r(21110),c=r(36183),f=r(98768),h="Tabs",[p,m]=(0,a.b)(h,[i.Pc]),v=(0,i.Pc)(),[g,w]=p(h),y=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:l,activationMode:p="automatic",...m}=e,v=(0,u.gm)(l),[w,y]=(0,d.T)({prop:n,onChange:o,defaultProp:a??"",caller:h});return(0,f.jsx)(g,{scope:r,baseId:(0,c.M)(),value:w,onValueChange:y,orientation:i,dir:v,activationMode:p,children:(0,f.jsx)(s.WV.div,{dir:v,"data-orientation":i,...m,ref:t})})});y.displayName=h;var b="TabsList",x=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,a=w(b,r),l=v(r);return(0,f.jsx)(i.fC,{asChild:!0,...l,orientation:a.orientation,dir:a.dir,loop:n,children:(0,f.jsx)(s.WV.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});x.displayName=b;var M="TabsTrigger",k=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...l}=e,u=w(M,r),d=v(r),c=S(u.baseId,n),h=D(u.baseId,n),p=n===u.value;return(0,f.jsx)(i.ck,{asChild:!0,...d,focusable:!a,active:p,children:(0,f.jsx)(s.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":h,"data-state":p?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...l,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():u.onValueChange(n)}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&u.onValueChange(n)}),onFocus:(0,o.M)(e.onFocus,()=>{let e="manual"!==u.activationMode;p||a||!e||u.onValueChange(n)})})})});k.displayName=M;var C="TabsContent",E=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:a,children:i,...u}=e,d=w(C,r),c=S(d.baseId,o),h=D(d.baseId,o),p=o===d.value,m=n.useRef(p);return n.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(l.z,{present:a||p,children:({present:r})=>(0,f.jsx)(s.WV.div,{"data-state":p?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:h,tabIndex:0,...u,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:r&&i})})});function S(e,t){return`${e}-trigger-${t}`}function D(e,t){return`${e}-content-${t}`}E.displayName=C;var R=y,T=x,N=k,j=E},12716:(e,t,r)=>{r.d(t,{D:()=>o});var n=r(60343);function o(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},27276:(e,t,r)=>{r.d(t,{mY:()=>P});var n=/[\\\/_+.#"@\[\(\{&]/,o=/[\\\/_+.#"@\[\(\{&]/g,a=/[\s-]/,i=/[\s-]/g;function l(e){return e.toLowerCase().replace(i," ")}var s=r(32264),u=r(60343),d=r(54928),c=r(36183),f=r(48367),h='[cmdk-group=""]',p='[cmdk-group-items=""]',m='[cmdk-item=""]',v=`${m}:not([aria-disabled="true"])`,g="cmdk-item-select",w="data-value",y=(e,t,r)=>(function(e,t,r){return function e(t,r,l,s,u,d,c){if(d===r.length)return u===t.length?1:.99;var f=`${u},${d}`;if(void 0!==c[f])return c[f];for(var h,p,m,v,g=s.charAt(d),w=l.indexOf(g,u),y=0;w>=0;)(h=e(t,r,l,s,w+1,d+1,c))>y&&(w===u?h*=1:n.test(t.charAt(w-1))?(h*=.8,(m=t.slice(u,w-1).match(o))&&u>0&&(h*=Math.pow(.999,m.length))):a.test(t.charAt(w-1))?(h*=.9,(v=t.slice(u,w-1).match(i))&&u>0&&(h*=Math.pow(.999,v.length))):(h*=.17,u>0&&(h*=Math.pow(.999,w-u))),t.charAt(w)!==r.charAt(d)&&(h*=.9999)),(h<.1&&l.charAt(w-1)===s.charAt(d+1)||s.charAt(d+1)===s.charAt(d)&&l.charAt(w-1)!==s.charAt(d))&&.1*(p=e(t,r,l,s,w+1,d+2,c))>h&&(h=.1*p),h>y&&(y=h),w=l.indexOf(g,w+1);return c[f]=y,y}(e=r&&r.length>0?`${e+" "+r.join(" ")}`:e,t,l(e),l(t),0,0,{})})(e,t,r),b=u.createContext(void 0),x=()=>u.useContext(b),M=u.createContext(void 0),k=()=>u.useContext(M),C=u.createContext(void 0),E=u.forwardRef((e,t)=>{let r=A(()=>{var t,r;return{search:"",value:null!=(r=null!=(t=e.value)?t:e.defaultValue)?r:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),n=A(()=>new Set),o=A(()=>new Map),a=A(()=>new Map),i=A(()=>new Set),l=I(e),{label:s,children:f,value:x,onValueChange:k,filter:C,shouldFilter:E,loop:S,disablePointerSelection:D=!1,vimBindings:R=!0,...T}=e,N=(0,c.M)(),j=(0,c.M)(),P=(0,c.M)(),O=u.useRef(null),L=_();W(()=>{if(void 0!==x){let e=x.trim();r.current.value=e,V.emit()}},[x]),W(()=>{L(6,q)},[]);let V=u.useMemo(()=>({subscribe:e=>(i.current.add(e),()=>i.current.delete(e)),snapshot:()=>r.current,setState:(e,t,n)=>{var o,a,i,s;if(!Object.is(r.current[e],t)){if(r.current[e]=t,"search"===e)Z(),z(),L(1,H);else if("value"===e){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let e=document.getElementById(P);e?e.focus():null==(o=document.getElementById(N))||o.focus()}if(L(7,()=>{var e;r.current.selectedItemId=null==(e=K())?void 0:e.id,V.emit()}),n||L(5,q),(null==(a=l.current)?void 0:a.value)!==void 0){null==(s=(i=l.current).onValueChange)||s.call(i,null!=t?t:"");return}}V.emit()}},emit:()=>{i.current.forEach(e=>e())}}),[]),Y=u.useMemo(()=>({value:(e,t,n)=>{var o;t!==(null==(o=a.current.get(e))?void 0:o.value)&&(a.current.set(e,{value:t,keywords:n}),r.current.filtered.items.set(e,B(t,n)),L(2,()=>{z(),V.emit()}))},item:(e,t)=>(n.current.add(e),t&&(o.current.has(t)?o.current.get(t).add(e):o.current.set(t,new Set([e]))),L(3,()=>{Z(),z(),r.current.value||H(),V.emit()}),()=>{a.current.delete(e),n.current.delete(e),r.current.filtered.items.delete(e);let t=K();L(4,()=>{Z(),(null==t?void 0:t.getAttribute("id"))===e&&H(),V.emit()})}),group:e=>(o.current.has(e)||o.current.set(e,new Set),()=>{a.current.delete(e),o.current.delete(e)}),filter:()=>l.current.shouldFilter,label:s||e["aria-label"],getDisablePointerSelection:()=>l.current.disablePointerSelection,listId:N,inputId:P,labelId:j,listInnerRef:O}),[]);function B(e,t){var n,o;let a=null!=(o=null==(n=l.current)?void 0:n.filter)?o:y;return e?a(e,r.current.search,t):0}function z(){if(!r.current.search||!1===l.current.shouldFilter)return;let e=r.current.filtered.items,t=[];r.current.filtered.groups.forEach(r=>{let n=o.current.get(r),a=0;n.forEach(t=>{a=Math.max(e.get(t),a)}),t.push([r,a])});let n=O.current;$().sort((t,r)=>{var n,o;let a=t.getAttribute("id"),i=r.getAttribute("id");return(null!=(n=e.get(i))?n:0)-(null!=(o=e.get(a))?o:0)}).forEach(e=>{let t=e.closest(p);t?t.appendChild(e.parentElement===t?e:e.closest(`${p} > *`)):n.appendChild(e.parentElement===n?e:e.closest(`${p} > *`))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let r=null==(t=O.current)?void 0:t.querySelector(`${h}[${w}="${encodeURIComponent(e[0])}"]`);null==r||r.parentElement.appendChild(r)})}function H(){let e=$().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(w);V.setState("value",t||void 0)}function Z(){var e,t,i,s;if(!r.current.search||!1===l.current.shouldFilter){r.current.filtered.count=n.current.size;return}r.current.filtered.groups=new Set;let u=0;for(let o of n.current){let n=B(null!=(t=null==(e=a.current.get(o))?void 0:e.value)?t:"",null!=(s=null==(i=a.current.get(o))?void 0:i.keywords)?s:[]);r.current.filtered.items.set(o,n),n>0&&u++}for(let[e,t]of o.current)for(let n of t)if(r.current.filtered.items.get(n)>0){r.current.filtered.groups.add(e);break}r.current.filtered.count=u}function q(){var e,t,r;let n=K();n&&((null==(e=n.parentElement)?void 0:e.firstChild)===n&&(null==(r=null==(t=n.closest(h))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),n.scrollIntoView({block:"nearest"}))}function K(){var e;return null==(e=O.current)?void 0:e.querySelector(`${m}[aria-selected="true"]`)}function $(){var e;return Array.from((null==(e=O.current)?void 0:e.querySelectorAll(v))||[])}function Q(e){let t=$()[e];t&&V.setState("value",t.getAttribute(w))}function X(e){var t;let r=K(),n=$(),o=n.findIndex(e=>e===r),a=n[o+e];null!=(t=l.current)&&t.loop&&(a=o+e<0?n[n.length-1]:o+e===n.length?n[0]:n[o+e]),a&&V.setState("value",a.getAttribute(w))}function G(e){let t=K(),r=null==t?void 0:t.closest(h),n;for(;r&&!n;)n=null==(r=e>0?function(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}(r,h):function(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}(r,h))?void 0:r.querySelector(v);n?V.setState("value",n.getAttribute(w)):X(e)}let J=()=>Q($().length-1),ee=e=>{e.preventDefault(),e.metaKey?J():e.altKey?G(1):X(1)},et=e=>{e.preventDefault(),e.metaKey?Q(0):e.altKey?G(-1):X(-1)};return u.createElement(d.WV.div,{ref:t,tabIndex:-1,...T,"cmdk-root":"",onKeyDown:e=>{var t;null==(t=T.onKeyDown)||t.call(T,e);let r=e.nativeEvent.isComposing||229===e.keyCode;if(!(e.defaultPrevented||r))switch(e.key){case"n":case"j":R&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":R&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),Q(0);break;case"End":e.preventDefault(),J();break;case"Enter":{e.preventDefault();let t=K();if(t){let e=new Event(g);t.dispatchEvent(e)}}}}},u.createElement("label",{"cmdk-label":"",htmlFor:Y.inputId,id:Y.labelId,style:U},s),F(e,e=>u.createElement(M.Provider,{value:V},u.createElement(b.Provider,{value:Y},e))))}),S=u.forwardRef((e,t)=>{var r,n;let o=(0,c.M)(),a=u.useRef(null),i=u.useContext(C),l=x(),s=I(e),h=null!=(n=null==(r=s.current)?void 0:r.forceMount)?n:null==i?void 0:i.forceMount;W(()=>{if(!h)return l.item(o,null==i?void 0:i.id)},[h]);let p=L(o,a,[e.value,e.children,a],e.keywords),m=k(),v=O(e=>e.value&&e.value===p.current),w=O(e=>!!h||!1===l.filter()||!e.search||e.filtered.items.get(o)>0);function y(){var e,t;b(),null==(t=(e=s.current).onSelect)||t.call(e,p.current)}function b(){m.setState("value",p.current,!0)}if(u.useEffect(()=>{let t=a.current;if(!(!t||e.disabled))return t.addEventListener(g,y),()=>t.removeEventListener(g,y)},[w,e.onSelect,e.disabled]),!w)return null;let{disabled:M,value:E,onSelect:S,forceMount:D,keywords:R,...T}=e;return u.createElement(d.WV.div,{ref:(0,f.F)(a,t),...T,id:o,"cmdk-item":"",role:"option","aria-disabled":!!M,"aria-selected":!!v,"data-disabled":!!M,"data-selected":!!v,onPointerMove:M||l.getDisablePointerSelection()?void 0:b,onClick:M?void 0:y},e.children)}),D=u.forwardRef((e,t)=>{let{heading:r,children:n,forceMount:o,...a}=e,i=(0,c.M)(),l=u.useRef(null),s=u.useRef(null),h=(0,c.M)(),p=x(),m=O(e=>!!o||!1===p.filter()||!e.search||e.filtered.groups.has(i));W(()=>p.group(i),[]),L(i,l,[e.value,e.heading,s]);let v=u.useMemo(()=>({id:i,forceMount:o}),[o]);return u.createElement(d.WV.div,{ref:(0,f.F)(l,t),...a,"cmdk-group":"",role:"presentation",hidden:!m||void 0},r&&u.createElement("div",{ref:s,"cmdk-group-heading":"","aria-hidden":!0,id:h},r),F(e,e=>u.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?h:void 0},u.createElement(C.Provider,{value:v},e))))}),R=u.forwardRef((e,t)=>{let{alwaysRender:r,...n}=e,o=u.useRef(null),a=O(e=>!e.search);return r||a?u.createElement(d.WV.div,{ref:(0,f.F)(o,t),...n,"cmdk-separator":"",role:"separator"}):null}),T=u.forwardRef((e,t)=>{let{onValueChange:r,...n}=e,o=null!=e.value,a=k(),i=O(e=>e.search),l=O(e=>e.selectedItemId),s=x();return u.useEffect(()=>{null!=e.value&&a.setState("search",e.value)},[e.value]),u.createElement(d.WV.input,{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":s.listId,"aria-labelledby":s.labelId,"aria-activedescendant":l,id:s.inputId,type:"text",value:o?e.value:i,onChange:e=>{o||a.setState("search",e.target.value),null==r||r(e.target.value)}})}),N=u.forwardRef((e,t)=>{let{children:r,label:n="Suggestions",...o}=e,a=u.useRef(null),i=u.useRef(null),l=O(e=>e.selectedItemId),s=x();return u.useEffect(()=>{if(i.current&&a.current){let e=i.current,t=a.current,r,n=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{let r=e.offsetHeight;t.style.setProperty("--cmdk-list-height",r.toFixed(1)+"px")})});return n.observe(e),()=>{cancelAnimationFrame(r),n.unobserve(e)}}},[]),u.createElement(d.WV.div,{ref:(0,f.F)(a,t),...o,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":l,"aria-label":n,id:s.listId},F(e,e=>u.createElement("div",{ref:(0,f.F)(i,s.listInnerRef),"cmdk-list-sizer":""},e)))}),j=u.forwardRef((e,t)=>{let{open:r,onOpenChange:n,overlayClassName:o,contentClassName:a,container:i,...l}=e;return u.createElement(s.fC,{open:r,onOpenChange:n},u.createElement(s.h_,{container:i},u.createElement(s.aV,{"cmdk-overlay":"",className:o}),u.createElement(s.VY,{"aria-label":e.label,"cmdk-dialog":"",className:a},u.createElement(E,{ref:t,...l}))))}),P=Object.assign(E,{List:N,Item:S,Input:T,Group:D,Separator:R,Dialog:j,Empty:u.forwardRef((e,t)=>O(e=>0===e.filtered.count)?u.createElement(d.WV.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:u.forwardRef((e,t)=>{let{progress:r,children:n,label:o="Loading...",...a}=e;return u.createElement(d.WV.div,{ref:t,...a,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":o},F(e,e=>u.createElement("div",{"aria-hidden":!0},e)))})});function I(e){let t=u.useRef(e);return W(()=>{t.current=e}),t}var W=u.useEffect;function A(e){let t=u.useRef();return void 0===t.current&&(t.current=e()),t}function O(e){let t=k(),r=()=>e(t.snapshot());return u.useSyncExternalStore(t.subscribe,r,r)}function L(e,t,r,n=[]){let o=u.useRef(),a=x();return W(()=>{var i;let l=(()=>{var e;for(let t of r){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():o.current}})(),s=n.map(e=>e.trim());a.value(e,l,s),null==(i=t.current)||i.setAttribute(w,l),o.current=l}),o}var _=()=>{let[e,t]=u.useState(),r=A(()=>new Map);return W(()=>{r.current.forEach(e=>e()),r.current=new Map},[e]),(e,n)=>{r.current.set(e,n),t({})}};function F({asChild:e,children:t},r){let n;return e&&u.isValidElement(t)?u.cloneElement("function"==typeof(n=t.type)?n(t.props):"render"in n?n.render(t.props):t,{ref:t.ref},r(t.props.children)):r(t)}var U={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}},73466:(e,t,r)=>{r.d(t,{j:()=>o});let n={};function o(){return n}},28599:(e,t,r)=>{r.d(t,{z:()=>a});var n=r(79824),o=r(93140);function a(e,t){let r=(0,n.Q)(e);if(isNaN(t))return(0,o.L)(e,NaN);if(!t)return r;let a=r.getDate(),i=(0,o.L)(e,r.getTime());return(i.setMonth(r.getMonth()+t+1,0),a>=i.getDate())?i:(r.setFullYear(i.getFullYear(),i.getMonth(),a),r)}},5734:(e,t,r)=>{r.d(t,{dP:()=>o,jE:()=>n});let n=6048e5,o=864e5},93140:(e,t,r)=>{function n(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}r.d(t,{L:()=>n})},54110:(e,t,r)=>{r.d(t,{w:()=>l});var n=r(5734),o=r(36113),a=r(79824);function i(e){let t=(0,a.Q)(e),r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),+e-+r}function l(e,t){let r=(0,o.b)(e),a=(0,o.b)(t);return Math.round((+r-i(r)-(+a-i(a)))/n.dP)}},38440:(e,t,r)=>{r.d(t,{V:()=>o});var n=r(79824);function o(e){let t=(0,n.Q)(e),r=t.getMonth();return t.setFullYear(t.getFullYear(),r+1,0),t.setHours(23,59,59,999),t}},10706:(e,t,r)=>{r.d(t,{WU:()=>L});let n={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function o(e){return (t={})=>{let r=t.width?String(t.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}let a={date:o({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:o({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:o({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},i={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function l(e){return(t,r)=>{let n;if("formatting"===(r?.context?String(r.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,o=r?.width?String(r.width):t;n=e.formattingValues[o]||e.formattingValues[t]}else{let t=e.defaultWidth,o=r?.width?String(r.width):e.defaultWidth;n=e.values[o]||e.values[t]}return n[e.argumentCallback?e.argumentCallback(t):t]}}function s(e){return(t,r={})=>{let n;let o=r.width,a=o&&e.matchPatterns[o]||e.matchPatterns[e.defaultMatchWidth],i=t.match(a);if(!i)return null;let l=i[0],s=o&&e.parsePatterns[o]||e.parsePatterns[e.defaultParseWidth],u=Array.isArray(s)?function(e,t){for(let r=0;r<e.length;r++)if(t(e[r]))return r}(s,e=>e.test(l)):function(e,t){for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return r}(s,e=>e.test(l));return n=e.valueCallback?e.valueCallback(u):u,{value:n=r.valueCallback?r.valueCallback(n):n,rest:t.slice(l.length)}}}let u={code:"en-US",formatDistance:(e,t,r)=>{let o;let a=n[e];return(o="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",t.toString()),r?.addSuffix)?r.comparison&&r.comparison>0?"in "+o:o+" ago":o},formatLong:a,formatRelative:(e,t,r,n)=>i[e],localize:{ordinalNumber:(e,t)=>{let r=Number(e),n=r%100;if(n>20||n<10)switch(n%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},era:l({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:l({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:l({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:l({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:l({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(t,r={})=>{let n=t.match(e.matchPattern);if(!n)return null;let o=n[0],a=t.match(e.parsePattern);if(!a)return null;let i=e.valueCallback?e.valueCallback(a[0]):a[0];return{value:i=r.valueCallback?r.valueCallback(i):i,rest:t.slice(o.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:s({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:s({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:s({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:s({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:s({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};var d=r(73466),c=r(54110),f=r(91985),h=r(79824),p=r(34611),m=r(39102),v=r(67293),g=r(36488);function w(e,t){let r=Math.abs(e).toString().padStart(t,"0");return(e<0?"-":"")+r}let y={y(e,t){let r=e.getFullYear(),n=r>0?r:1-r;return w("yy"===t?n%100:n,t.length)},M(e,t){let r=e.getMonth();return"M"===t?String(r+1):w(r+1,2)},d:(e,t)=>w(e.getDate(),t.length),a(e,t){let r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.toUpperCase();case"aaa":return r;case"aaaaa":return r[0];default:return"am"===r?"a.m.":"p.m."}},h:(e,t)=>w(e.getHours()%12||12,t.length),H:(e,t)=>w(e.getHours(),t.length),m:(e,t)=>w(e.getMinutes(),t.length),s:(e,t)=>w(e.getSeconds(),t.length),S(e,t){let r=t.length;return w(Math.trunc(e.getMilliseconds()*Math.pow(10,r-3)),t.length)}},b={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},x={G:function(e,t,r){let n=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return r.era(n,{width:"abbreviated"});case"GGGGG":return r.era(n,{width:"narrow"});default:return r.era(n,{width:"wide"})}},y:function(e,t,r){if("yo"===t){let t=e.getFullYear();return r.ordinalNumber(t>0?t:1-t,{unit:"year"})}return y.y(e,t)},Y:function(e,t,r,n){let o=(0,g.c)(e,n),a=o>0?o:1-o;return"YY"===t?w(a%100,2):"Yo"===t?r.ordinalNumber(a,{unit:"year"}):w(a,t.length)},R:function(e,t){return w((0,m.L)(e),t.length)},u:function(e,t){return w(e.getFullYear(),t.length)},Q:function(e,t,r){let n=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(n);case"QQ":return w(n,2);case"Qo":return r.ordinalNumber(n,{unit:"quarter"});case"QQQ":return r.quarter(n,{width:"abbreviated",context:"formatting"});case"QQQQQ":return r.quarter(n,{width:"narrow",context:"formatting"});default:return r.quarter(n,{width:"wide",context:"formatting"})}},q:function(e,t,r){let n=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(n);case"qq":return w(n,2);case"qo":return r.ordinalNumber(n,{unit:"quarter"});case"qqq":return r.quarter(n,{width:"abbreviated",context:"standalone"});case"qqqqq":return r.quarter(n,{width:"narrow",context:"standalone"});default:return r.quarter(n,{width:"wide",context:"standalone"})}},M:function(e,t,r){let n=e.getMonth();switch(t){case"M":case"MM":return y.M(e,t);case"Mo":return r.ordinalNumber(n+1,{unit:"month"});case"MMM":return r.month(n,{width:"abbreviated",context:"formatting"});case"MMMMM":return r.month(n,{width:"narrow",context:"formatting"});default:return r.month(n,{width:"wide",context:"formatting"})}},L:function(e,t,r){let n=e.getMonth();switch(t){case"L":return String(n+1);case"LL":return w(n+1,2);case"Lo":return r.ordinalNumber(n+1,{unit:"month"});case"LLL":return r.month(n,{width:"abbreviated",context:"standalone"});case"LLLLL":return r.month(n,{width:"narrow",context:"standalone"});default:return r.month(n,{width:"wide",context:"standalone"})}},w:function(e,t,r,n){let o=(0,v.Q)(e,n);return"wo"===t?r.ordinalNumber(o,{unit:"week"}):w(o,t.length)},I:function(e,t,r){let n=(0,p.l)(e);return"Io"===t?r.ordinalNumber(n,{unit:"week"}):w(n,t.length)},d:function(e,t,r){return"do"===t?r.ordinalNumber(e.getDate(),{unit:"date"}):y.d(e,t)},D:function(e,t,r){let n=function(e){let t=(0,h.Q)(e);return(0,c.w)(t,(0,f.e)(t))+1}(e);return"Do"===t?r.ordinalNumber(n,{unit:"dayOfYear"}):w(n,t.length)},E:function(e,t,r){let n=e.getDay();switch(t){case"E":case"EE":case"EEE":return r.day(n,{width:"abbreviated",context:"formatting"});case"EEEEE":return r.day(n,{width:"narrow",context:"formatting"});case"EEEEEE":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},e:function(e,t,r,n){let o=e.getDay(),a=(o-n.weekStartsOn+8)%7||7;switch(t){case"e":return String(a);case"ee":return w(a,2);case"eo":return r.ordinalNumber(a,{unit:"day"});case"eee":return r.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return r.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return r.day(o,{width:"short",context:"formatting"});default:return r.day(o,{width:"wide",context:"formatting"})}},c:function(e,t,r,n){let o=e.getDay(),a=(o-n.weekStartsOn+8)%7||7;switch(t){case"c":return String(a);case"cc":return w(a,t.length);case"co":return r.ordinalNumber(a,{unit:"day"});case"ccc":return r.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return r.day(o,{width:"narrow",context:"standalone"});case"cccccc":return r.day(o,{width:"short",context:"standalone"});default:return r.day(o,{width:"wide",context:"standalone"})}},i:function(e,t,r){let n=e.getDay(),o=0===n?7:n;switch(t){case"i":return String(o);case"ii":return w(o,t.length);case"io":return r.ordinalNumber(o,{unit:"day"});case"iii":return r.day(n,{width:"abbreviated",context:"formatting"});case"iiiii":return r.day(n,{width:"narrow",context:"formatting"});case"iiiiii":return r.day(n,{width:"short",context:"formatting"});default:return r.day(n,{width:"wide",context:"formatting"})}},a:function(e,t,r){let n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"aaa":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},b:function(e,t,r){let n;let o=e.getHours();switch(n=12===o?b.noon:0===o?b.midnight:o/12>=1?"pm":"am",t){case"b":case"bb":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"bbb":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},B:function(e,t,r){let n;let o=e.getHours();switch(n=o>=17?b.evening:o>=12?b.afternoon:o>=4?b.morning:b.night,t){case"B":case"BB":case"BBB":return r.dayPeriod(n,{width:"abbreviated",context:"formatting"});case"BBBBB":return r.dayPeriod(n,{width:"narrow",context:"formatting"});default:return r.dayPeriod(n,{width:"wide",context:"formatting"})}},h:function(e,t,r){if("ho"===t){let t=e.getHours()%12;return 0===t&&(t=12),r.ordinalNumber(t,{unit:"hour"})}return y.h(e,t)},H:function(e,t,r){return"Ho"===t?r.ordinalNumber(e.getHours(),{unit:"hour"}):y.H(e,t)},K:function(e,t,r){let n=e.getHours()%12;return"Ko"===t?r.ordinalNumber(n,{unit:"hour"}):w(n,t.length)},k:function(e,t,r){let n=e.getHours();return(0===n&&(n=24),"ko"===t)?r.ordinalNumber(n,{unit:"hour"}):w(n,t.length)},m:function(e,t,r){return"mo"===t?r.ordinalNumber(e.getMinutes(),{unit:"minute"}):y.m(e,t)},s:function(e,t,r){return"so"===t?r.ordinalNumber(e.getSeconds(),{unit:"second"}):y.s(e,t)},S:function(e,t){return y.S(e,t)},X:function(e,t,r){let n=e.getTimezoneOffset();if(0===n)return"Z";switch(t){case"X":return k(n);case"XXXX":case"XX":return C(n);default:return C(n,":")}},x:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"x":return k(n);case"xxxx":case"xx":return C(n);default:return C(n,":")}},O:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+M(n,":");default:return"GMT"+C(n,":")}},z:function(e,t,r){let n=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+M(n,":");default:return"GMT"+C(n,":")}},t:function(e,t,r){return w(Math.trunc(e.getTime()/1e3),t.length)},T:function(e,t,r){return w(e.getTime(),t.length)}};function M(e,t=""){let r=e>0?"-":"+",n=Math.abs(e),o=Math.trunc(n/60),a=n%60;return 0===a?r+String(o):r+String(o)+t+w(a,2)}function k(e,t){return e%60==0?(e>0?"-":"+")+w(Math.abs(e)/60,2):C(e,t)}function C(e,t=""){let r=Math.abs(e);return(e>0?"-":"+")+w(Math.trunc(r/60),2)+t+w(r%60,2)}let E=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});default:return t.date({width:"full"})}},S=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});default:return t.time({width:"full"})}},D={p:S,P:(e,t)=>{let r;let n=e.match(/(P+)(p+)?/)||[],o=n[1],a=n[2];if(!a)return E(e,t);switch(o){case"P":r=t.dateTime({width:"short"});break;case"PP":r=t.dateTime({width:"medium"});break;case"PPP":r=t.dateTime({width:"long"});break;default:r=t.dateTime({width:"full"})}return r.replace("{{date}}",E(o,t)).replace("{{time}}",S(a,t))}},R=/^D+$/,T=/^Y+$/,N=["D","DD","YY","YYYY"];var j=r(96583);let P=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,I=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,W=/^'([^]*?)'?$/,A=/''/g,O=/[a-zA-Z]/;function L(e,t,r){let n=(0,d.j)(),o=r?.locale??n.locale??u,a=r?.firstWeekContainsDate??r?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1,i=r?.weekStartsOn??r?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0,l=(0,h.Q)(e);if(!(0,j.J)(l)&&"number"!=typeof l||isNaN(Number((0,h.Q)(l))))throw RangeError("Invalid time value");let s=t.match(I).map(e=>{let t=e[0];return"p"===t||"P"===t?(0,D[t])(e,o.formatLong):e}).join("").match(P).map(e=>{if("''"===e)return{isToken:!1,value:"'"};let t=e[0];if("'"===t)return{isToken:!1,value:function(e){let t=e.match(W);return t?t[1].replace(A,"'"):e}(e)};if(x[t])return{isToken:!0,value:e};if(t.match(O))throw RangeError("Format string contains an unescaped latin alphabet character `"+t+"`");return{isToken:!1,value:e}});o.localize.preprocessor&&(s=o.localize.preprocessor(l,s));let c={firstWeekContainsDate:a,weekStartsOn:i,locale:o};return s.map(n=>{if(!n.isToken)return n.value;let a=n.value;return(!r?.useAdditionalWeekYearTokens&&T.test(a)||!r?.useAdditionalDayOfYearTokens&&R.test(a))&&function(e,t,r){let n=function(e,t,r){let n="Y"===e[0]?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${n} to the input \`${r}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(e,t,r);if(console.warn(n),N.includes(e))throw RangeError(n)}(a,t,String(e)),(0,x[a[0]])(l,a,o.localize,c)}).join("")}},34611:(e,t,r)=>{r.d(t,{l:()=>s});var n=r(5734),o=r(83376),a=r(39102),i=r(93140),l=r(79824);function s(e){let t=(0,l.Q)(e);return Math.round((+(0,o.T)(t)-+function(e){let t=(0,a.L)(e),r=(0,i.L)(e,0);return r.setFullYear(t,0,4),r.setHours(0,0,0,0),(0,o.T)(r)}(t))/n.jE)+1}},39102:(e,t,r)=>{r.d(t,{L:()=>i});var n=r(93140),o=r(83376),a=r(79824);function i(e){let t=(0,a.Q)(e),r=t.getFullYear(),i=(0,n.L)(e,0);i.setFullYear(r+1,0,4),i.setHours(0,0,0,0);let l=(0,o.T)(i),s=(0,n.L)(e,0);s.setFullYear(r,0,4),s.setHours(0,0,0,0);let u=(0,o.T)(s);return t.getTime()>=l.getTime()?r+1:t.getTime()>=u.getTime()?r:r-1}},67293:(e,t,r)=>{r.d(t,{Q:()=>u});var n=r(5734),o=r(48135),a=r(93140),i=r(36488),l=r(73466),s=r(79824);function u(e,t){let r=(0,s.Q)(e);return Math.round((+(0,o.z)(r,t)-+function(e,t){let r=(0,l.j)(),n=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1,s=(0,i.c)(e,t),u=(0,a.L)(e,0);return u.setFullYear(s,0,n),u.setHours(0,0,0,0),(0,o.z)(u,t)}(r,t))/n.jE)+1}},36488:(e,t,r)=>{r.d(t,{c:()=>l});var n=r(93140),o=r(48135),a=r(79824),i=r(73466);function l(e,t){let r=(0,a.Q)(e),l=r.getFullYear(),s=(0,i.j)(),u=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??s.firstWeekContainsDate??s.locale?.options?.firstWeekContainsDate??1,d=(0,n.L)(e,0);d.setFullYear(l+1,0,u),d.setHours(0,0,0,0);let c=(0,o.z)(d,t),f=(0,n.L)(e,0);f.setFullYear(l,0,u),f.setHours(0,0,0,0);let h=(0,o.z)(f,t);return r.getTime()>=c.getTime()?l+1:r.getTime()>=h.getTime()?l:l-1}},39941:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(79824);function o(e,t){let r=(0,n.Q)(e),o=(0,n.Q)(t);return r.getTime()>o.getTime()}},12397:(e,t,r)=>{r.d(t,{R:()=>o});var n=r(79824);function o(e,t){return+(0,n.Q)(e)<+(0,n.Q)(t)}},96583:(e,t,r)=>{function n(e){return e instanceof Date||"object"==typeof e&&"[object Date]"===Object.prototype.toString.call(e)}r.d(t,{J:()=>n})},20148:(e,t,r)=>{r.d(t,{x:()=>o});var n=r(79824);function o(e,t){let r=(0,n.Q)(e),o=(0,n.Q)(t);return r.getFullYear()===o.getFullYear()&&r.getMonth()===o.getMonth()}},69359:(e,t,r)=>{r.d(t,{q:()=>a});var n=r(93140),o=r(79824);function a(e,t){let r=(0,o.Q)(e),a=r.getFullYear(),i=r.getDate(),l=(0,n.L)(e,0);l.setFullYear(a,t,15),l.setHours(0,0,0,0);let s=function(e){let t=(0,o.Q)(e),r=t.getFullYear(),a=t.getMonth(),i=(0,n.L)(e,0);return i.setFullYear(r,a+1,0),i.setHours(0,0,0,0),i.getDate()}(l);return r.setMonth(t,Math.min(i,s)),r}},36113:(e,t,r)=>{r.d(t,{b:()=>o});var n=r(79824);function o(e){let t=(0,n.Q)(e);return t.setHours(0,0,0,0),t}},83376:(e,t,r)=>{r.d(t,{T:()=>o});var n=r(48135);function o(e){return(0,n.z)(e,{weekStartsOn:1})}},43160:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(79824);function o(e){let t=(0,n.Q)(e);return t.setDate(1),t.setHours(0,0,0,0),t}},48135:(e,t,r)=>{r.d(t,{z:()=>a});var n=r(79824),o=r(73466);function a(e,t){let r=(0,o.j)(),a=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,i=(0,n.Q)(e),l=i.getDay();return i.setDate(i.getDate()-((l<a?7:0)+l-a)),i.setHours(0,0,0,0),i}},91985:(e,t,r)=>{r.d(t,{e:()=>a});var n=r(79824),o=r(93140);function a(e){let t=(0,n.Q)(e),r=(0,o.L)(e,0);return r.setFullYear(t.getFullYear(),0,1),r.setHours(0,0,0,0),r}},79824:(e,t,r)=>{function n(e){let t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):new Date("number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?e:NaN)}r.d(t,{Q:()=>n})},81311:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},74932:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},41641:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},39303:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},52396:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},53294:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94460:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54214:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]])},74158:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},81515:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},52016:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},3510:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},52346:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},28898:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},86008:(e,t,r)=>{r.d(t,{_:()=>ta});var n,o={};r.r(o),r.d(o,{Button:()=>eo,CaptionLabel:()=>ea,Chevron:()=>ei,Day:()=>el,DayButton:()=>es,Dropdown:()=>eu,DropdownNav:()=>ed,Footer:()=>ec,Month:()=>ef,MonthCaption:()=>eh,MonthGrid:()=>ep,Months:()=>em,MonthsDropdown:()=>ew,Nav:()=>ey,NextMonthButton:()=>eb,Option:()=>ex,PreviousMonthButton:()=>eM,Root:()=>ek,Select:()=>eC,Week:()=>eE,WeekNumber:()=>eR,WeekNumberHeader:()=>eT,Weekday:()=>eS,Weekdays:()=>eD,Weeks:()=>eN,YearsDropdown:()=>ej});var a={};r.r(a),r.d(a,{formatCaption:()=>eI,formatDay:()=>eA,formatMonthCaption:()=>eW,formatMonthDropdown:()=>eO,formatWeekNumber:()=>eL,formatWeekNumberHeader:()=>e_,formatWeekdayName:()=>eF,formatYearCaption:()=>eV,formatYearDropdown:()=>eU});var i={};r.r(i),r.d(i,{labelCaption:()=>eB,labelDay:()=>eZ,labelDayButton:()=>eH,labelGrid:()=>eY,labelGridcell:()=>ez,labelMonthDropdown:()=>eK,labelNav:()=>eq,labelNext:()=>e$,labelPrevious:()=>eQ,labelWeekNumber:()=>eG,labelWeekNumberHeader:()=>eJ,labelWeekday:()=>eX,labelYearDropdown:()=>e0});var l=r(60343);Symbol.for("constructDateFrom");let s={},u={};function d(e,t){try{let r=(s[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format)(t).split("GMT")[1]||"";if(r in u)return u[r];return f(r,r.split(":"))}catch{if(e in u)return u[e];let t=e?.match(c);if(t)return f(e,t.slice(1));return NaN}}let c=/([+-]\d\d):?(\d\d)?/;function f(e,t){let r=+t[0],n=+(t[1]||0);return u[e]=r>0?60*r+n:60*r-n}class h extends Date{constructor(...e){super(),e.length>1&&"string"==typeof e[e.length-1]&&(this.timeZone=e.pop()),this.internal=new Date,isNaN(d(this.timeZone,this))?this.setTime(NaN):e.length?"number"==typeof e[0]&&(1===e.length||2===e.length&&"number"!=typeof e[1])?this.setTime(e[0]):"string"==typeof e[0]?this.setTime(+new Date(e[0])):e[0]instanceof Date?this.setTime(+e[0]):(this.setTime(+new Date(...e)),v(this,NaN),m(this)):this.setTime(Date.now())}static tz(e,...t){return t.length?new h(...t,e):new h(Date.now(),e)}withTimeZone(e){return new h(+this,e)}getTimezoneOffset(){return-d(this.timeZone,this)}setTime(e){return Date.prototype.setTime.apply(this,arguments),m(this),+this}[Symbol.for("constructDateFrom")](e){return new h(+new Date(e),this.timeZone)}}let p=/^(get|set)(?!UTC)/;function m(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function v(e){let t=d(e.timeZone,e),r=new Date(+e);r.setUTCHours(r.getUTCHours()-1);let n=-new Date(+e).getTimezoneOffset(),o=n- -new Date(+r).getTimezoneOffset(),a=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();o&&a&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+o);let i=n-t;i&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+i);let l=d(e.timeZone,e),s=-new Date(+e).getTimezoneOffset()-l-i;if(l!==t&&s){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+s);let t=l-d(e.timeZone,e);t&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+t),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+t))}}Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!p.test(e))return;let t=e.replace(p,"$1UTC");h.prototype[t]&&(e.startsWith("get")?h.prototype[e]=function(){return this.internal[t]()}:(h.prototype[e]=function(){return Date.prototype[t].apply(this.internal,arguments),Date.prototype.setFullYear.call(this,this.internal.getUTCFullYear(),this.internal.getUTCMonth(),this.internal.getUTCDate()),Date.prototype.setHours.call(this,this.internal.getUTCHours(),this.internal.getUTCMinutes(),this.internal.getUTCSeconds(),this.internal.getUTCMilliseconds()),v(this),+this},h.prototype[t]=function(){return Date.prototype[t].apply(this,arguments),m(this),+this}))});class g extends h{static tz(e,...t){return t.length?new g(...t,e):new g(Date.now(),e)}toISOString(){let[e,t,r]=this.tzComponents(),n=`${e}${t}:${r}`;return this.internal.toISOString().slice(0,-1)+n}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){let[e,t,r,n]=this.internal.toUTCString().split(" ");return`${e?.slice(0,-1)} ${r} ${t} ${n}`}toTimeString(){var e;let t=this.internal.toUTCString().split(" ")[4],[r,n,o]=this.tzComponents();return`${t} GMT${r}${n}${o} (${e=this.timeZone,new Intl.DateTimeFormat("en-GB",{timeZone:e,timeZoneName:"long"}).format(this).slice(12)})`}toLocaleString(e,t){return Date.prototype.toLocaleString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleDateString(e,t){return Date.prototype.toLocaleDateString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleTimeString(e,t){return Date.prototype.toLocaleTimeString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}tzComponents(){let e=this.getTimezoneOffset(),t=String(Math.floor(Math.abs(e)/60)).padStart(2,"0"),r=String(Math.abs(e)%60).padStart(2,"0");return[e>0?"-":"+",t,r]}withTimeZone(e){return new g(+this,e)}[Symbol.for("constructDateFrom")](e){return new g(+new Date(e),this.timeZone)}}var w=r(23404);let y={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function b(e){return (t={})=>{let r=t.width?String(t.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}let x={date:b({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:b({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:b({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},M={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function k(e){return(t,r)=>{let n;if("formatting"===(r?.context?String(r.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,o=r?.width?String(r.width):t;n=e.formattingValues[o]||e.formattingValues[t]}else{let t=e.defaultWidth,o=r?.width?String(r.width):e.defaultWidth;n=e.values[o]||e.values[t]}return n[e.argumentCallback?e.argumentCallback(t):t]}}function C(e){return(t,r={})=>{let n;let o=r.width,a=o&&e.matchPatterns[o]||e.matchPatterns[e.defaultMatchWidth],i=t.match(a);if(!i)return null;let l=i[0],s=o&&e.parsePatterns[o]||e.parsePatterns[e.defaultParseWidth],u=Array.isArray(s)?function(e,t){for(let r=0;r<e.length;r++)if(t(e[r]))return r}(s,e=>e.test(l)):function(e,t){for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return r}(s,e=>e.test(l));return n=e.valueCallback?e.valueCallback(u):u,{value:n=r.valueCallback?r.valueCallback(n):n,rest:t.slice(l.length)}}}let E={code:"en-US",formatDistance:(e,t,r)=>{let n;let o=y[e];return(n="string"==typeof o?o:1===t?o.one:o.other.replace("{{count}}",t.toString()),r?.addSuffix)?r.comparison&&r.comparison>0?"in "+n:n+" ago":n},formatLong:x,formatRelative:(e,t,r,n)=>M[e],localize:{ordinalNumber:(e,t)=>{let r=Number(e),n=r%100;if(n>20||n<10)switch(n%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},era:k({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:k({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:k({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:k({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:k({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(t,r={})=>{let n=t.match(e.matchPattern);if(!n)return null;let o=n[0],a=t.match(e.parsePattern);if(!a)return null;let i=e.valueCallback?e.valueCallback(a[0]):a[0];return{value:i=r.valueCallback?r.valueCallback(i):i,rest:t.slice(o.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:C({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:C({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:C({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:C({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:C({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};var S=r(79824),D=r(93140);function R(e,t){let r=(0,S.Q)(e);return isNaN(t)?(0,D.L)(e,NaN):(t&&r.setDate(r.getDate()+t),r)}var T=r(28599),N=r(54110),j=r(73466);function P(e,t){let r=(0,j.j)(),n=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0,o=(0,S.Q)(e),a=o.getDay();return o.setDate(o.getDate()+((a<n?-7:0)+6-(a-n))),o.setHours(23,59,59,999),o}var I=r(38440),W=r(10706),A=r(34611),O=r(67293),L=r(39941),_=r(12397),F=r(96583),U=r(36113),V=r(20148),Y=r(69359),B=r(83376),z=r(43160),H=r(48135),Z=r(91985);function q(e,t){let r=t.startOfMonth(e),n=r.getDay();return 1===n?r:0===n?t.addDays(r,-6):t.addDays(r,-1*(n-1))}class K{constructor(e,t){this.Date=Date,this.today=()=>this.overrides?.today?this.overrides.today():this.options.timeZone?g.tz(this.options.timeZone):new this.Date,this.newDate=(e,t,r)=>this.overrides?.newDate?this.overrides.newDate(e,t,r):this.options.timeZone?new g(e,t,r,this.options.timeZone):new Date(e,t,r),this.addDays=(e,t)=>this.overrides?.addDays?this.overrides.addDays(e,t):R(e,t),this.addMonths=(e,t)=>this.overrides?.addMonths?this.overrides.addMonths(e,t):(0,T.z)(e,t),this.addWeeks=(e,t)=>this.overrides?.addWeeks?this.overrides.addWeeks(e,t):R(e,7*t),this.addYears=(e,t)=>this.overrides?.addYears?this.overrides.addYears(e,t):(0,T.z)(e,12*t),this.differenceInCalendarDays=(e,t)=>this.overrides?.differenceInCalendarDays?this.overrides.differenceInCalendarDays(e,t):(0,N.w)(e,t),this.differenceInCalendarMonths=(e,t)=>this.overrides?.differenceInCalendarMonths?this.overrides.differenceInCalendarMonths(e,t):function(e,t){let r=(0,S.Q)(e),n=(0,S.Q)(t);return 12*(r.getFullYear()-n.getFullYear())+(r.getMonth()-n.getMonth())}(e,t),this.eachMonthOfInterval=e=>this.overrides?.eachMonthOfInterval?this.overrides.eachMonthOfInterval(e):function(e,t){let r=(0,S.Q)(e.start),n=(0,S.Q)(e.end),o=+r>+n,a=o?+r:+n,i=o?n:r;i.setHours(0,0,0,0),i.setDate(1);let l=(void 0)??1;if(!l)return[];l<0&&(l=-l,o=!o);let s=[];for(;+i<=a;)s.push((0,S.Q)(i)),i.setMonth(i.getMonth()+l);return o?s.reverse():s}(e),this.endOfBroadcastWeek=e=>this.overrides?.endOfBroadcastWeek?this.overrides.endOfBroadcastWeek(e):function(e,t){let r=q(e,t),n=function(e,t){let r=t.startOfMonth(e),n=r.getDay()>0?r.getDay():7,o=t.addDays(e,-n+1),a=t.addDays(o,34);return t.getMonth(e)===t.getMonth(a)?5:4}(e,t);return t.addDays(r,7*n-1)}(e,this),this.endOfISOWeek=e=>this.overrides?.endOfISOWeek?this.overrides.endOfISOWeek(e):P(e,{weekStartsOn:1}),this.endOfMonth=e=>this.overrides?.endOfMonth?this.overrides.endOfMonth(e):(0,I.V)(e),this.endOfWeek=(e,t)=>this.overrides?.endOfWeek?this.overrides.endOfWeek(e,t):P(e,this.options),this.endOfYear=e=>this.overrides?.endOfYear?this.overrides.endOfYear(e):function(e){let t=(0,S.Q)(e),r=t.getFullYear();return t.setFullYear(r+1,0,0),t.setHours(23,59,59,999),t}(e),this.format=(e,t,r)=>{let n=this.overrides?.format?this.overrides.format(e,t,this.options):(0,W.WU)(e,t,this.options);return this.options.numerals&&"latn"!==this.options.numerals?this.replaceDigits(n):n},this.getISOWeek=e=>this.overrides?.getISOWeek?this.overrides.getISOWeek(e):(0,A.l)(e),this.getMonth=(e,t)=>this.overrides?.getMonth?this.overrides.getMonth(e,this.options):(this.options,(0,S.Q)(e).getMonth()),this.getYear=(e,t)=>this.overrides?.getYear?this.overrides.getYear(e,this.options):(this.options,(0,S.Q)(e).getFullYear()),this.getWeek=(e,t)=>this.overrides?.getWeek?this.overrides.getWeek(e,this.options):(0,O.Q)(e,this.options),this.isAfter=(e,t)=>this.overrides?.isAfter?this.overrides.isAfter(e,t):(0,L.A)(e,t),this.isBefore=(e,t)=>this.overrides?.isBefore?this.overrides.isBefore(e,t):(0,_.R)(e,t),this.isDate=e=>this.overrides?.isDate?this.overrides.isDate(e):(0,F.J)(e),this.isSameDay=(e,t)=>this.overrides?.isSameDay?this.overrides.isSameDay(e,t):+(0,U.b)(e)==+(0,U.b)(t),this.isSameMonth=(e,t)=>this.overrides?.isSameMonth?this.overrides.isSameMonth(e,t):(0,V.x)(e,t),this.isSameYear=(e,t)=>this.overrides?.isSameYear?this.overrides.isSameYear(e,t):function(e,t){let r=(0,S.Q)(e),n=(0,S.Q)(t);return r.getFullYear()===n.getFullYear()}(e,t),this.max=e=>this.overrides?.max?this.overrides.max(e):function(e){let t;return e.forEach(function(e){let r=(0,S.Q)(e);(void 0===t||t<r||isNaN(Number(r)))&&(t=r)}),t||new Date(NaN)}(e),this.min=e=>this.overrides?.min?this.overrides.min(e):function(e){let t;return e.forEach(e=>{let r=(0,S.Q)(e);(!t||t>r||isNaN(+r))&&(t=r)}),t||new Date(NaN)}(e),this.setMonth=(e,t)=>this.overrides?.setMonth?this.overrides.setMonth(e,t):(0,Y.q)(e,t),this.setYear=(e,t)=>this.overrides?.setYear?this.overrides.setYear(e,t):function(e,t){let r=(0,S.Q)(e);return isNaN(+r)?(0,D.L)(e,NaN):(r.setFullYear(t),r)}(e,t),this.startOfBroadcastWeek=(e,t)=>this.overrides?.startOfBroadcastWeek?this.overrides.startOfBroadcastWeek(e,this):q(e,this),this.startOfDay=e=>this.overrides?.startOfDay?this.overrides.startOfDay(e):(0,U.b)(e),this.startOfISOWeek=e=>this.overrides?.startOfISOWeek?this.overrides.startOfISOWeek(e):(0,B.T)(e),this.startOfMonth=e=>this.overrides?.startOfMonth?this.overrides.startOfMonth(e):(0,z.N)(e),this.startOfWeek=(e,t)=>this.overrides?.startOfWeek?this.overrides.startOfWeek(e,this.options):(0,H.z)(e,this.options),this.startOfYear=e=>this.overrides?.startOfYear?this.overrides.startOfYear(e):(0,Z.e)(e),this.options={locale:E,...e},this.overrides=t}getDigitMap(){let{numerals:e="latn"}=this.options,t=new Intl.NumberFormat("en-US",{numberingSystem:e}),r={};for(let e=0;e<10;e++)r[e.toString()]=t.format(e);return r}replaceDigits(e){let t=this.getDigitMap();return e.replace(/\d/g,e=>t[e]||e)}formatNumber(e){return this.replaceDigits(e.toString())}}let $=new K;function Q(e,t,r=!1,n=$){let{from:o,to:a}=e,{differenceInCalendarDays:i,isSameDay:l}=n;return o&&a?(0>i(a,o)&&([o,a]=[a,o]),i(t,o)>=(r?1:0)&&i(a,t)>=(r?1:0)):!r&&a?l(a,t):!r&&!!o&&l(o,t)}function X(e){return!!(e&&"object"==typeof e&&"before"in e&&"after"in e)}function G(e){return!!(e&&"object"==typeof e&&"from"in e)}function J(e){return!!(e&&"object"==typeof e&&"after"in e)}function ee(e){return!!(e&&"object"==typeof e&&"before"in e)}function et(e){return!!(e&&"object"==typeof e&&"dayOfWeek"in e)}function er(e,t){return Array.isArray(e)&&e.every(t.isDate)}function en(e,t,r=$){let n=Array.isArray(t)?t:[t],{isSameDay:o,differenceInCalendarDays:a,isAfter:i}=r;return n.some(t=>{if("boolean"==typeof t)return t;if(r.isDate(t))return o(e,t);if(er(t,r))return t.includes(e);if(G(t))return Q(t,e,!1,r);if(et(t))return Array.isArray(t.dayOfWeek)?t.dayOfWeek.includes(e.getDay()):t.dayOfWeek===e.getDay();if(X(t)){let r=a(t.before,e),n=a(t.after,e),o=r>0,l=n<0;return i(t.before,t.after)?l&&o:o||l}return J(t)?a(e,t.after)>0:ee(t)?a(t.before,e)>0:"function"==typeof t&&t(e)})}function eo(e){return l.createElement("button",{...e})}function ea(e){return l.createElement("span",{...e})}function ei(e){let{size:t=24,orientation:r="left",className:n}=e;return l.createElement("svg",{className:n,width:t,height:t,viewBox:"0 0 24 24"},"up"===r&&l.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),"down"===r&&l.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),"left"===r&&l.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),"right"===r&&l.createElement("polygon",{points:"8 18.112 14.18888889 12 8 5.87733333 9.91111111 4 18 12 9.91111111 20"}))}function el(e){let{day:t,modifiers:r,...n}=e;return l.createElement("td",{...n})}function es(e){let{day:t,modifiers:r,...n}=e,o=l.useRef(null);return l.useEffect(()=>{r.focused&&o.current?.focus()},[r.focused]),l.createElement("button",{ref:o,...n})}function eu(e){let{options:t,className:r,components:n,classNames:o,...a}=e,i=[o[w.UI.Dropdown],r].join(" "),s=t?.find(({value:e})=>e===a.value);return l.createElement("span",{"data-disabled":a.disabled,className:o[w.UI.DropdownRoot]},l.createElement(n.Select,{className:i,...a},t?.map(({value:e,label:t,disabled:r})=>l.createElement(n.Option,{key:e,value:e,disabled:r},t))),l.createElement("span",{className:o[w.UI.CaptionLabel],"aria-hidden":!0},s?.label,l.createElement(n.Chevron,{orientation:"down",size:18,className:o[w.UI.Chevron]})))}function ed(e){return l.createElement("div",{...e})}function ec(e){return l.createElement("div",{...e})}function ef(e){let{calendarMonth:t,displayIndex:r,...n}=e;return l.createElement("div",{...n},e.children)}function eh(e){let{calendarMonth:t,displayIndex:r,...n}=e;return l.createElement("div",{...n})}function ep(e){return l.createElement("table",{...e})}function em(e){return l.createElement("div",{...e})}let ev=(0,l.createContext)(void 0);function eg(){let e=(0,l.useContext)(ev);if(void 0===e)throw Error("useDayPicker() must be used within a custom component.");return e}function ew(e){let{components:t}=eg();return l.createElement(t.Dropdown,{...e})}function ey(e){let{onPreviousClick:t,onNextClick:r,previousMonth:n,nextMonth:o,...a}=e,{components:i,classNames:s,labels:{labelPrevious:u,labelNext:d}}=eg(),c=(0,l.useCallback)(e=>{o&&r?.(e)},[o,r]),f=(0,l.useCallback)(e=>{n&&t?.(e)},[n,t]);return l.createElement("nav",{...a},l.createElement(i.PreviousMonthButton,{type:"button",className:s[w.UI.PreviousMonthButton],tabIndex:n?void 0:-1,"aria-disabled":!n||void 0,"aria-label":u(n),onClick:f},l.createElement(i.Chevron,{disabled:!n||void 0,className:s[w.UI.Chevron],orientation:"left"})),l.createElement(i.NextMonthButton,{type:"button",className:s[w.UI.NextMonthButton],tabIndex:o?void 0:-1,"aria-disabled":!o||void 0,"aria-label":d(o),onClick:c},l.createElement(i.Chevron,{disabled:!o||void 0,orientation:"right",className:s[w.UI.Chevron]})))}function eb(e){let{components:t}=eg();return l.createElement(t.Button,{...e})}function ex(e){return l.createElement("option",{...e})}function eM(e){let{components:t}=eg();return l.createElement(t.Button,{...e})}function ek(e){let{rootRef:t,...r}=e;return l.createElement("div",{...r,ref:t})}function eC(e){return l.createElement("select",{...e})}function eE(e){let{week:t,...r}=e;return l.createElement("tr",{...r})}function eS(e){return l.createElement("th",{...e})}function eD(e){return l.createElement("thead",{"aria-hidden":!0},l.createElement("tr",{...e}))}function eR(e){let{week:t,...r}=e;return l.createElement("th",{...r})}function eT(e){return l.createElement("th",{...e})}function eN(e){return l.createElement("tbody",{...e})}function ej(e){let{components:t}=eg();return l.createElement(t.Dropdown,{...e})}var eP=r(31741);function eI(e,t,r){return(r??new K(t)).format(e,"LLLL y")}let eW=eI;function eA(e,t,r){return(r??new K(t)).format(e,"d")}function eO(e,t=$){return t.format(e,"LLLL")}function eL(e,t=$){return e<10?t.formatNumber(`0${e.toLocaleString()}`):t.formatNumber(`${e.toLocaleString()}`)}function e_(){return""}function eF(e,t,r){return(r??new K(t)).format(e,"cccccc")}function eU(e,t=$){return t.format(e,"yyyy")}let eV=eU;function eY(e,t,r){return(r??new K(t)).format(e,"LLLL y")}let eB=eY;function ez(e,t,r,n){let o=(n??new K(r)).format(e,"PPPP");return t?.today&&(o=`Today, ${o}`),o}function eH(e,t,r,n){let o=(n??new K(r)).format(e,"PPPP");return t.today&&(o=`Today, ${o}`),t.selected&&(o=`${o}, selected`),o}let eZ=eH;function eq(){return""}function eK(e){return"Choose the Month"}function e$(e){return"Go to the Next Month"}function eQ(e){return"Go to the Previous Month"}function eX(e,t,r){return(r??new K(t)).format(e,"cccc")}function eG(e,t){return`Week ${e}`}function eJ(e){return"Week Number"}function e0(e){return"Choose the Year"}let e1=e=>e instanceof HTMLElement?e:null,e2=e=>[...e.querySelectorAll("[data-animated-month]")??[]],e3=e=>e1(e.querySelector("[data-animated-month]")),e4=e=>e1(e.querySelector("[data-animated-caption]")),e8=e=>e1(e.querySelector("[data-animated-weeks]")),e9=e=>e1(e.querySelector("[data-animated-nav]")),e7=e=>e1(e.querySelector("[data-animated-weekdays]"));function e6(e,t){let{month:r,defaultMonth:n,today:o=t.today(),numberOfMonths:a=1,endMonth:i,startMonth:l}=e,s=r||n||o,{differenceInCalendarMonths:u,addMonths:d,startOfMonth:c}=t;return i&&0>u(i,s)&&(s=d(i,-1*(a-1))),l&&0>u(s,l)&&(s=l),c(s)}class e5{constructor(e,t,r=$){this.date=e,this.displayMonth=t,this.outside=!!(t&&!r.isSameMonth(e,t)),this.dateLib=r}isEqualTo(e){return this.dateLib.isSameDay(e.date,this.date)&&this.dateLib.isSameMonth(e.displayMonth,this.displayMonth)}}class te{constructor(e,t){this.days=t,this.weekNumber=e}}class tt{constructor(e,t){this.date=e,this.weeks=t}}function tr(e,t){let[r,n]=(0,l.useState)(e);return[void 0===t?r:t,n]}function tn(e){return!e[w.BE.disabled]&&!e[w.BE.hidden]&&!e[w.BE.outside]}function to(e,t,r=$){return Q(e,t.from,!1,r)||Q(e,t.to,!1,r)||Q(t,e.from,!1,r)||Q(t,e.to,!1,r)}function ta(e){let t=e;t.timeZone&&((t={...e}).today&&(t.today=new g(t.today,t.timeZone)),t.month&&(t.month=new g(t.month,t.timeZone)),t.defaultMonth&&(t.defaultMonth=new g(t.defaultMonth,t.timeZone)),t.startMonth&&(t.startMonth=new g(t.startMonth,t.timeZone)),t.endMonth&&(t.endMonth=new g(t.endMonth,t.timeZone)),"single"===t.mode&&t.selected?t.selected=new g(t.selected,t.timeZone):"multiple"===t.mode&&t.selected?t.selected=t.selected?.map(e=>new g(e,t.timeZone)):"range"===t.mode&&t.selected&&(t.selected={from:t.selected.from?new g(t.selected.from,t.timeZone):void 0,to:t.selected.to?new g(t.selected.to,t.timeZone):void 0}));let{components:r,formatters:s,labels:u,dateLib:d,locale:c,classNames:f}=(0,l.useMemo)(()=>{var e,r;let n={...E,...t.locale};return{dateLib:new K({locale:n,weekStartsOn:t.broadcastCalendar?1:t.weekStartsOn,firstWeekContainsDate:t.firstWeekContainsDate,useAdditionalWeekYearTokens:t.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:t.useAdditionalDayOfYearTokens,timeZone:t.timeZone,numerals:t.numerals},t.dateLib),components:(e=t.components,{...o,...e}),formatters:(r=t.formatters,r?.formatMonthCaption&&!r.formatCaption&&(r.formatCaption=r.formatMonthCaption),r?.formatYearCaption&&!r.formatYearDropdown&&(r.formatYearDropdown=r.formatYearCaption),{...a,...r}),labels:{...i,...t.labels},locale:n,classNames:{...(0,eP.U)(),...t.classNames}}},[t.locale,t.broadcastCalendar,t.weekStartsOn,t.firstWeekContainsDate,t.useAdditionalWeekYearTokens,t.useAdditionalDayOfYearTokens,t.timeZone,t.numerals,t.dateLib,t.components,t.formatters,t.labels,t.classNames]),{captionLayout:h,mode:p,navLayout:m,numberOfMonths:v=1,onDayBlur:y,onDayClick:b,onDayFocus:x,onDayKeyDown:M,onDayMouseEnter:k,onDayMouseLeave:C,onNextClick:S,onPrevClick:D,showWeekNumber:R,styles:T}=t,{formatCaption:N,formatDay:j,formatMonthDropdown:P,formatWeekNumber:I,formatWeekNumberHeader:W,formatWeekdayName:A,formatYearDropdown:O}=s,L=function(e,t){let[r,n]=function(e,t){let{startMonth:r,endMonth:n}=e,{startOfYear:o,startOfDay:a,startOfMonth:i,endOfMonth:l,addYears:s,endOfYear:u,newDate:d,today:c}=t,{fromYear:f,toYear:h,fromMonth:p,toMonth:m}=e;!r&&p&&(r=p),!r&&f&&(r=t.newDate(f,0,1)),!n&&m&&(n=m),!n&&h&&(n=d(h,11,31));let v="dropdown"===e.captionLayout||"dropdown-years"===e.captionLayout;return r?r=i(r):f?r=d(f,0,1):!r&&v&&(r=o(s(e.today??c(),-100))),n?n=l(n):h?n=d(h,11,31):!n&&v&&(n=u(e.today??c())),[r?a(r):r,n?a(n):n]}(e,t),{startOfMonth:o,endOfMonth:a}=t,i=e6(e,t),[s,u]=tr(i,e.month?i:void 0);(0,l.useEffect)(()=>{u(e6(e,t))},[e.timeZone]);let d=function(e,t,r,n){let{numberOfMonths:o=1}=r,a=[];for(let r=0;r<o;r++){let o=n.addMonths(e,r);if(t&&o>t)break;a.push(o)}return a}(s,n,e,t),c=function(e,t,r,n){let o=e[0],a=e[e.length-1],{ISOWeek:i,fixedWeeks:l,broadcastCalendar:s}=r??{},{addDays:u,differenceInCalendarDays:d,differenceInCalendarMonths:c,endOfBroadcastWeek:f,endOfISOWeek:h,endOfMonth:p,endOfWeek:m,isAfter:v,startOfBroadcastWeek:g,startOfISOWeek:w,startOfWeek:y}=n,b=s?g(o,n):i?w(o):y(o),x=d(s?f(a):i?h(p(a)):m(p(a)),b),M=c(a,o)+1,k=[];for(let e=0;e<=x;e++){let r=u(b,e);if(t&&v(r,t))break;k.push(r)}let C=(s?35:42)*M;if(l&&k.length<C){let e=C-k.length;for(let t=0;t<e;t++){let e=u(k[k.length-1],1);k.push(e)}}return k}(d,e.endMonth?a(e.endMonth):void 0,e,t),f=function(e,t,r,n){let{addDays:o,endOfBroadcastWeek:a,endOfISOWeek:i,endOfMonth:l,endOfWeek:s,getISOWeek:u,getWeek:d,startOfBroadcastWeek:c,startOfISOWeek:f,startOfWeek:h}=n,p=e.reduce((e,p)=>{let m=r.broadcastCalendar?c(p,n):r.ISOWeek?f(p):h(p),v=r.broadcastCalendar?a(p):r.ISOWeek?i(l(p)):s(l(p)),g=t.filter(e=>e>=m&&e<=v),w=r.broadcastCalendar?35:42;if(r.fixedWeeks&&g.length<w){let e=t.filter(e=>{let t=w-g.length;return e>v&&e<=o(v,t)});g.push(...e)}let y=g.reduce((e,t)=>{let o=r.ISOWeek?u(t):d(t),a=e.find(e=>e.weekNumber===o),i=new e5(t,p,n);return a?a.days.push(i):e.push(new te(o,[i])),e},[]),b=new tt(p,y);return e.push(b),e},[]);return r.reverseMonths?p.reverse():p}(d,c,e,t),h=f.reduce((e,t)=>[...e,...t.weeks],[]),p=function(e){let t=[];return e.reduce((e,r)=>[...e,...r.weeks.reduce((e,t)=>[...e,...t.days],t)],t)}(f),m=function(e,t,r,n){if(r.disableNavigation)return;let{pagedNavigation:o,numberOfMonths:a}=r,{startOfMonth:i,addMonths:l,differenceInCalendarMonths:s}=n,u=i(e);if(!t||!(0>=s(u,t)))return l(u,-(o?a??1:1))}(s,r,e,t),v=function(e,t,r,n){if(r.disableNavigation)return;let{pagedNavigation:o,numberOfMonths:a=1}=r,{startOfMonth:i,addMonths:l,differenceInCalendarMonths:s}=n,u=i(e);if(!t||!(s(t,e)<a))return l(u,o?a:1)}(s,n,e,t),{disableNavigation:g,onMonthChange:w}=e,y=e=>h.some(t=>t.days.some(t=>t.isEqualTo(e))),b=e=>{if(g)return;let t=o(e);r&&t<o(r)&&(t=o(r)),n&&t>o(n)&&(t=o(n)),u(t),w?.(t)};return{months:f,weeks:h,days:p,navStart:r,navEnd:n,previousMonth:m,nextMonth:v,goToMonth:b,goToDay:e=>{y(e)||b(e.date)}}}(t,d),{days:_,months:F,navStart:U,navEnd:V,previousMonth:Y,nextMonth:B,goToMonth:z}=L,H=function(e,t,r){let{disabled:n,hidden:o,modifiers:a,showOutsideDays:i,broadcastCalendar:l,today:s}=t,{isSameDay:u,isSameMonth:d,startOfMonth:c,isBefore:f,endOfMonth:h,isAfter:p}=r,m=t.startMonth&&c(t.startMonth),v=t.endMonth&&h(t.endMonth),g={[w.BE.focused]:[],[w.BE.outside]:[],[w.BE.disabled]:[],[w.BE.hidden]:[],[w.BE.today]:[]},y={};for(let t of e){let{date:e,displayMonth:c}=t,h=!!(c&&!d(e,c)),w=!!(m&&f(e,m)),b=!!(v&&p(e,v)),x=!!(n&&en(e,n,r)),M=!!(o&&en(e,o,r))||w||b||!l&&!i&&h||l&&!1===i&&h,k=u(e,s??r.today());h&&g.outside.push(t),x&&g.disabled.push(t),M&&g.hidden.push(t),k&&g.today.push(t),a&&Object.keys(a).forEach(n=>{let o=a?.[n];o&&en(e,o,r)&&(y[n]?y[n].push(t):y[n]=[t])})}return e=>{let t={[w.BE.focused]:!1,[w.BE.disabled]:!1,[w.BE.hidden]:!1,[w.BE.outside]:!1,[w.BE.today]:!1},r={};for(let r in g){let n=g[r];t[r]=n.some(t=>t===e)}for(let t in y)r[t]=y[t].some(t=>t===e);return{...t,...r}}}(_,t,d),{isSelected:Z,select:q,selected:eo}=function(e,t){let r=function(e,t){let{selected:r,required:n,onSelect:o}=e,[a,i]=tr(r,o?r:void 0),l=o?r:a,{isSameDay:s}=t;return{selected:l,select:(e,t,r)=>{let a=e;return!n&&l&&l&&s(e,l)&&(a=void 0),o||i(a),o?.(a,e,t,r),a},isSelected:e=>!!l&&s(l,e)}}(e,t),n=function(e,t){let{selected:r,required:n,onSelect:o}=e,[a,i]=tr(r,o?r:void 0),l=o?r:a,{isSameDay:s}=t,u=e=>l?.some(t=>s(t,e))??!1,{min:d,max:c}=e;return{selected:l,select:(e,t,r)=>{let a=[...l??[]];if(u(e)){if(l?.length===d||n&&l?.length===1)return;a=l?.filter(t=>!s(t,e))}else a=l?.length===c?[e]:[...a,e];return o||i(a),o?.(a,e,t,r),a},isSelected:u}}(e,t),o=function(e,t){let{disabled:r,excludeDisabled:n,selected:o,required:a,onSelect:i}=e,[l,s]=tr(o,i?o:void 0),u=i?o:l;return{selected:u,select:(o,l,d)=>{let{min:c,max:f}=e,h=o?function(e,t,r=0,n=0,o=!1,a=$){let i;let{from:l,to:s}=t||{},{isSameDay:u,isAfter:d,isBefore:c}=a;if(l||s){if(l&&!s)i=u(l,e)?o?{from:l,to:void 0}:void 0:c(e,l)?{from:e,to:l}:{from:l,to:e};else if(l&&s){if(u(l,e)&&u(s,e))i=o?{from:l,to:s}:void 0;else if(u(l,e))i={from:l,to:r>0?void 0:e};else if(u(s,e))i={from:e,to:r>0?void 0:e};else if(c(e,l))i={from:e,to:s};else if(d(e,l))i={from:l,to:e};else if(d(e,s))i={from:l,to:e};else throw Error("Invalid range")}}else i={from:e,to:r>0?void 0:e};if(i?.from&&i?.to){let t=a.differenceInCalendarDays(i.to,i.from);n>0&&t>n?i={from:e,to:void 0}:r>1&&t<r&&(i={from:e,to:void 0})}return i}(o,u,c,f,a,t):void 0;return n&&r&&h?.from&&h.to&&function(e,t,r=$){let n=Array.isArray(t)?t:[t];if(n.filter(e=>"function"!=typeof e).some(t=>"boolean"==typeof t?t:r.isDate(t)?Q(e,t,!1,r):er(t,r)?t.some(t=>Q(e,t,!1,r)):G(t)?!!t.from&&!!t.to&&to(e,{from:t.from,to:t.to},r):et(t)?function(e,t,r=$){let n=Array.isArray(t)?t:[t],o=e.from,a=Math.min(r.differenceInCalendarDays(e.to,e.from),6);for(let e=0;e<=a;e++){if(n.includes(o.getDay()))return!0;o=r.addDays(o,1)}return!1}(e,t.dayOfWeek,r):X(t)?r.isAfter(t.before,t.after)?to(e,{from:r.addDays(t.after,1),to:r.addDays(t.before,-1)},r):en(e.from,t,r)||en(e.to,t,r):!!(J(t)||ee(t))&&(en(e.from,t,r)||en(e.to,t,r))))return!0;let o=n.filter(e=>"function"==typeof e);if(o.length){let t=e.from,n=r.differenceInCalendarDays(e.to,e.from);for(let e=0;e<=n;e++){if(o.some(e=>e(t)))return!0;t=r.addDays(t,1)}}return!1}({from:h.from,to:h.to},r,t)&&(h.from=o,h.to=void 0),i||s(h),i?.(h,o,l,d),h},isSelected:e=>u&&Q(u,e,!1,t)}}(e,t);switch(e.mode){case"single":return r;case"multiple":return n;case"range":return o;default:return}}(t,d)??{},{blur:ea,focused:ei,isFocusTarget:el,moveFocus:es,setFocused:eu}=function(e,t,r,o,a){let{autoFocus:i}=e,[s,u]=(0,l.useState)(),d=function(e,t,r,o){let a;let i=-1;for(let l of e){let e=t(l);tn(e)&&(e[w.BE.focused]&&i<n.FocusedModifier?(a=l,i=n.FocusedModifier):o?.isEqualTo(l)&&i<n.LastFocused?(a=l,i=n.LastFocused):r(l.date)&&i<n.Selected?(a=l,i=n.Selected):e[w.BE.today]&&i<n.Today&&(a=l,i=n.Today))}return a||(a=e.find(e=>tn(t(e)))),a}(t.days,r,o||(()=>!1),s),[c,f]=(0,l.useState)(i?d:void 0);return{isFocusTarget:e=>!!d?.isEqualTo(e),setFocused:f,focused:c,blur:()=>{u(c),f(void 0)},moveFocus:(r,n)=>{if(!c)return;let o=function e(t,r,n,o,a,i,l,s=0){if(s>365)return;let u=function(e,t,r,n,o,a,i){let{ISOWeek:l,broadcastCalendar:s}=a,{addDays:u,addMonths:d,addWeeks:c,addYears:f,endOfBroadcastWeek:h,endOfISOWeek:p,endOfWeek:m,max:v,min:g,startOfBroadcastWeek:w,startOfISOWeek:y,startOfWeek:b}=i,x=({day:u,week:c,month:d,year:f,startOfWeek:e=>s?w(e,i):l?y(e):b(e),endOfWeek:e=>s?h(e):l?p(e):m(e)})[e](r,"after"===t?1:-1);return"before"===t&&n?x=v([n,x]):"after"===t&&o&&(x=g([o,x])),x}(t,r,n.date,o,a,i,l),d=!!(i.disabled&&en(u,i.disabled,l)),c=!!(i.hidden&&en(u,i.hidden,l)),f=new e5(u,u,l);return d||c?e(t,r,f,o,a,i,l,s+1):f}(r,n,c,t.navStart,t.navEnd,e,a);o&&(t.goToDay(o),f(o))}}}(t,L,H,Z??(()=>!1),d),{labelDayButton:ed,labelGridcell:ec,labelGrid:ef,labelMonthDropdown:eh,labelNav:ep,labelPrevious:em,labelNext:eg,labelWeekday:ew,labelWeekNumber:ey,labelWeekNumberHeader:eb,labelYearDropdown:ex}=u,eM=(0,l.useMemo)(()=>(function(e,t,r){let n=e.today(),o=t?e.startOfISOWeek(n):e.startOfWeek(n),a=[];for(let t=0;t<7;t++){let r=e.addDays(o,t);a.push(r)}return a})(d,t.ISOWeek),[d,t.ISOWeek]),ek=void 0!==p||void 0!==b,eC=(0,l.useCallback)(()=>{Y&&(z(Y),D?.(Y))},[Y,z,D]),eE=(0,l.useCallback)(()=>{B&&(z(B),S?.(B))},[z,B,S]),eS=(0,l.useCallback)((e,t)=>r=>{r.preventDefault(),r.stopPropagation(),eu(e),q?.(e.date,t,r),b?.(e.date,t,r)},[q,b,eu]),eD=(0,l.useCallback)((e,t)=>r=>{eu(e),x?.(e.date,t,r)},[x,eu]),eR=(0,l.useCallback)((e,t)=>r=>{ea(),y?.(e.date,t,r)},[ea,y]),eT=(0,l.useCallback)((e,r)=>n=>{let o={ArrowLeft:["day","rtl"===t.dir?"after":"before"],ArrowRight:["day","rtl"===t.dir?"before":"after"],ArrowDown:["week","after"],ArrowUp:["week","before"],PageUp:[n.shiftKey?"year":"month","before"],PageDown:[n.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(o[n.key]){n.preventDefault(),n.stopPropagation();let[e,t]=o[n.key];es(e,t)}M?.(e.date,r,n)},[es,M,t.dir]),eN=(0,l.useCallback)((e,t)=>r=>{k?.(e.date,t,r)},[k]),ej=(0,l.useCallback)((e,t)=>r=>{C?.(e.date,t,r)},[C]),eI=(0,l.useCallback)(e=>t=>{let r=Number(t.target.value);z(d.setMonth(d.startOfMonth(e),r))},[d,z]),eW=(0,l.useCallback)(e=>t=>{let r=Number(t.target.value);z(d.setYear(d.startOfMonth(e),r))},[d,z]),{className:eA,style:eO}=(0,l.useMemo)(()=>({className:[f[w.UI.Root],t.className].filter(Boolean).join(" "),style:{...T?.[w.UI.Root],...t.style}}),[f,t.className,t.style,T]),eL=function(e){let t={"data-mode":e.mode??void 0,"data-required":"required"in e?e.required:void 0,"data-multiple-months":e.numberOfMonths&&e.numberOfMonths>1||void 0,"data-week-numbers":e.showWeekNumber||void 0,"data-broadcast-calendar":e.broadcastCalendar||void 0,"data-nav-layout":e.navLayout||void 0};return Object.entries(e).forEach(([e,r])=>{e.startsWith("data-")&&(t[e]=r)}),t}(t),e_=(0,l.useRef)(null);!function(e,t,{classNames:r,months:n,focused:o,dateLib:a}){let i=(0,l.useRef)(null),s=(0,l.useRef)(n),u=(0,l.useRef)(!1);(0,l.useLayoutEffect)(()=>{let l=s.current;if(s.current=n,!t||!e.current||!(e.current instanceof HTMLElement)||0===n.length||0===l.length||n.length!==l.length)return;let d=a.isSameMonth(n[0].date,l[0].date),c=a.isAfter(n[0].date,l[0].date),f=c?r[w.fw.caption_after_enter]:r[w.fw.caption_before_enter],h=c?r[w.fw.weeks_after_enter]:r[w.fw.weeks_before_enter],p=i.current,m=e.current.cloneNode(!0);if(m instanceof HTMLElement?(e2(m).forEach(e=>{if(!(e instanceof HTMLElement))return;let t=e3(e);t&&e.contains(t)&&e.removeChild(t);let r=e4(e);r&&r.classList.remove(f);let n=e8(e);n&&n.classList.remove(h)}),i.current=m):i.current=null,u.current||d||o)return;let v=p instanceof HTMLElement?e2(p):[],g=e2(e.current);if(g&&g.every(e=>e instanceof HTMLElement)&&v&&v.every(e=>e instanceof HTMLElement)){u.current=!0;let t=[];e.current.style.isolation="isolate";let n=e9(e.current);n&&(n.style.zIndex="1"),g.forEach((o,a)=>{let i=v[a];if(!i)return;o.style.position="relative",o.style.overflow="hidden";let l=e4(o);l&&l.classList.add(f);let s=e8(o);s&&s.classList.add(h);let d=()=>{u.current=!1,e.current&&(e.current.style.isolation=""),n&&(n.style.zIndex=""),l&&l.classList.remove(f),s&&s.classList.remove(h),o.style.position="",o.style.overflow="",o.contains(i)&&o.removeChild(i)};t.push(d),i.style.pointerEvents="none",i.style.position="absolute",i.style.overflow="hidden",i.setAttribute("aria-hidden","true");let p=e7(i);p&&(p.style.opacity="0");let m=e4(i);m&&(m.classList.add(c?r[w.fw.caption_before_exit]:r[w.fw.caption_after_exit]),m.addEventListener("animationend",d));let g=e8(i);g&&g.classList.add(c?r[w.fw.weeks_before_exit]:r[w.fw.weeks_after_exit]),o.insertBefore(i,o.firstChild)})}})}(e_,!!t.animate,{classNames:f,months:F,focused:ei,dateLib:d});let eF={dayPickerProps:t,selected:eo,select:q,isSelected:Z,months:F,nextMonth:B,previousMonth:Y,goToMonth:z,getModifiers:H,components:r,classNames:f,styles:T,labels:u,formatters:s};return l.createElement(ev.Provider,{value:eF},l.createElement(r.Root,{rootRef:t.animate?e_:void 0,className:eA,style:eO,dir:t.dir,id:t.id,lang:t.lang,nonce:t.nonce,title:t.title,role:t.role,"aria-label":t["aria-label"],...eL},l.createElement(r.Months,{className:f[w.UI.Months],style:T?.[w.UI.Months]},!t.hideNavigation&&!m&&l.createElement(r.Nav,{"data-animated-nav":t.animate?"true":void 0,className:f[w.UI.Nav],style:T?.[w.UI.Nav],"aria-label":ep(),onPreviousClick:eC,onNextClick:eE,previousMonth:Y,nextMonth:B}),F.map((e,n)=>{let o=function(e,t,r,n,o){let{startOfMonth:a,startOfYear:i,endOfYear:l,eachMonthOfInterval:s,getMonth:u}=o;return s({start:i(e),end:l(e)}).map(e=>{let i=n.formatMonthDropdown(e,o);return{value:u(e),label:i,disabled:t&&e<a(t)||r&&e>a(r)||!1}})}(e.date,U,V,s,d),a=function(e,t,r,n){if(!e||!t)return;let{startOfYear:o,endOfYear:a,addYears:i,getYear:l,isBefore:s,isSameYear:u}=n,d=o(e),c=a(t),f=[],h=d;for(;s(h,c)||u(h,c);)f.push(h),h=i(h,1);return f.map(e=>{let t=r.formatYearDropdown(e,n);return{value:l(e),label:t,disabled:!1}})}(U,V,s,d);return l.createElement(r.Month,{"data-animated-month":t.animate?"true":void 0,className:f[w.UI.Month],style:T?.[w.UI.Month],key:n,displayIndex:n,calendarMonth:e},"around"===m&&!t.hideNavigation&&0===n&&l.createElement(r.PreviousMonthButton,{type:"button",className:f[w.UI.PreviousMonthButton],tabIndex:Y?void 0:-1,"aria-disabled":!Y||void 0,"aria-label":em(Y),onClick:eC,"data-animated-button":t.animate?"true":void 0},l.createElement(r.Chevron,{disabled:!Y||void 0,className:f[w.UI.Chevron],orientation:"rtl"===t.dir?"right":"left"})),l.createElement(r.MonthCaption,{"data-animated-caption":t.animate?"true":void 0,className:f[w.UI.MonthCaption],style:T?.[w.UI.MonthCaption],calendarMonth:e,displayIndex:n},h?.startsWith("dropdown")?l.createElement(r.DropdownNav,{className:f[w.UI.Dropdowns],style:T?.[w.UI.Dropdowns]},"dropdown"===h||"dropdown-months"===h?l.createElement(r.MonthsDropdown,{className:f[w.UI.MonthsDropdown],"aria-label":eh(),classNames:f,components:r,disabled:!!t.disableNavigation,onChange:eI(e.date),options:o,style:T?.[w.UI.Dropdown],value:d.getMonth(e.date)}):l.createElement("span",null,P(e.date,d)),"dropdown"===h||"dropdown-years"===h?l.createElement(r.YearsDropdown,{className:f[w.UI.YearsDropdown],"aria-label":ex(d.options),classNames:f,components:r,disabled:!!t.disableNavigation,onChange:eW(e.date),options:a,style:T?.[w.UI.Dropdown],value:d.getYear(e.date)}):l.createElement("span",null,O(e.date,d)),l.createElement("span",{role:"status","aria-live":"polite",style:{border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap",wordWrap:"normal"}},N(e.date,d.options,d))):l.createElement(r.CaptionLabel,{className:f[w.UI.CaptionLabel],role:"status","aria-live":"polite"},N(e.date,d.options,d))),"around"===m&&!t.hideNavigation&&n===v-1&&l.createElement(r.NextMonthButton,{type:"button",className:f[w.UI.NextMonthButton],tabIndex:B?void 0:-1,"aria-disabled":!B||void 0,"aria-label":eg(B),onClick:eE,"data-animated-button":t.animate?"true":void 0},l.createElement(r.Chevron,{disabled:!B||void 0,className:f[w.UI.Chevron],orientation:"rtl"===t.dir?"left":"right"})),n===v-1&&"after"===m&&!t.hideNavigation&&l.createElement(r.Nav,{"data-animated-nav":t.animate?"true":void 0,className:f[w.UI.Nav],style:T?.[w.UI.Nav],"aria-label":ep(),onPreviousClick:eC,onNextClick:eE,previousMonth:Y,nextMonth:B}),l.createElement(r.MonthGrid,{role:"grid","aria-multiselectable":"multiple"===p||"range"===p,"aria-label":ef(e.date,d.options,d)||void 0,className:f[w.UI.MonthGrid],style:T?.[w.UI.MonthGrid]},!t.hideWeekdays&&l.createElement(r.Weekdays,{"data-animated-weekdays":t.animate?"true":void 0,className:f[w.UI.Weekdays],style:T?.[w.UI.Weekdays]},R&&l.createElement(r.WeekNumberHeader,{"aria-label":eb(d.options),className:f[w.UI.WeekNumberHeader],style:T?.[w.UI.WeekNumberHeader],scope:"col"},W()),eM.map((e,t)=>l.createElement(r.Weekday,{"aria-label":ew(e,d.options,d),className:f[w.UI.Weekday],key:t,style:T?.[w.UI.Weekday],scope:"col"},A(e,d.options,d)))),l.createElement(r.Weeks,{"data-animated-weeks":t.animate?"true":void 0,className:f[w.UI.Weeks],style:T?.[w.UI.Weeks]},e.weeks.map((e,n)=>l.createElement(r.Week,{className:f[w.UI.Week],key:e.weekNumber,style:T?.[w.UI.Week],week:e},R&&l.createElement(r.WeekNumber,{week:e,style:T?.[w.UI.WeekNumber],"aria-label":ey(e.weekNumber,{locale:c}),className:f[w.UI.WeekNumber],scope:"row",role:"rowheader"},I(e.weekNumber,d)),e.days.map(e=>{let{date:n}=e,o=H(e);if(o[w.BE.focused]=!o.hidden&&!!ei?.isEqualTo(e),o[w.fP.selected]=Z?.(n)||o.selected,G(eo)){let{from:e,to:t}=eo;o[w.fP.range_start]=!!(e&&t&&d.isSameDay(n,e)),o[w.fP.range_end]=!!(e&&t&&d.isSameDay(n,t)),o[w.fP.range_middle]=Q(eo,n,!0,d)}let a=function(e,t={},r={}){let n={...t?.[w.UI.Day]};return Object.entries(e).filter(([,e])=>!0===e).forEach(([e])=>{n={...n,...r?.[e]}}),n}(o,T,t.modifiersStyles),i=function(e,t,r={}){return Object.entries(e).filter(([,e])=>!0===e).reduce((e,[n])=>(r[n]?e.push(r[n]):t[w.BE[n]]?e.push(t[w.BE[n]]):t[w.fP[n]]&&e.push(t[w.fP[n]]),e),[t[w.UI.Day]])}(o,f,t.modifiersClassNames),s=ek||o.hidden?void 0:ec(n,o,d.options,d);return l.createElement(r.Day,{key:`${d.format(n,"yyyy-MM-dd")}_${d.format(e.displayMonth,"yyyy-MM")}`,day:e,modifiers:o,className:i.join(" "),style:a,role:"gridcell","aria-selected":o.selected||void 0,"aria-label":s,"data-day":d.format(n,"yyyy-MM-dd"),"data-month":e.outside?d.format(n,"yyyy-MM"):void 0,"data-selected":o.selected||void 0,"data-disabled":o.disabled||void 0,"data-hidden":o.hidden||void 0,"data-outside":e.outside||void 0,"data-focused":o.focused||void 0,"data-today":o.today||void 0},!o.hidden&&ek?l.createElement(r.DayButton,{className:f[w.UI.DayButton],style:T?.[w.UI.DayButton],type:"button",day:e,modifiers:o,disabled:o.disabled||void 0,tabIndex:el(e)?0:-1,"aria-label":ed(n,o,d.options,d),onClick:eS(e,o),onBlur:eR(e,o),onFocus:eD(e,o),onKeyDown:eT(e,o),onMouseEnter:eN(e,o),onMouseLeave:ej(e,o)},j(n,d.options,d)):!o.hidden&&j(e.date,d.options,d))}))))))})),t.footer&&l.createElement(r.Footer,{className:f[w.UI.Footer],style:T?.[w.UI.Footer],role:"status","aria-live":"polite"},t.footer)))}!function(e){e[e.Today=0]="Today",e[e.Selected=1]="Selected",e[e.LastFocused=2]="LastFocused",e[e.FocusedModifier=3]="FocusedModifier"}(n||(n={}))},23404:(e,t,r)=>{var n,o,a,i;r.d(t,{BE:()=>o,UI:()=>n,fP:()=>a,fw:()=>i}),function(e){e.Root="root",e.Chevron="chevron",e.Day="day",e.DayButton="day_button",e.CaptionLabel="caption_label",e.Dropdowns="dropdowns",e.Dropdown="dropdown",e.DropdownRoot="dropdown_root",e.Footer="footer",e.MonthGrid="month_grid",e.MonthCaption="month_caption",e.MonthsDropdown="months_dropdown",e.Month="month",e.Months="months",e.Nav="nav",e.NextMonthButton="button_next",e.PreviousMonthButton="button_previous",e.Week="week",e.Weeks="weeks",e.Weekday="weekday",e.Weekdays="weekdays",e.WeekNumber="week_number",e.WeekNumberHeader="week_number_header",e.YearsDropdown="years_dropdown"}(n||(n={})),function(e){e.disabled="disabled",e.hidden="hidden",e.outside="outside",e.focused="focused",e.today="today"}(o||(o={})),function(e){e.range_end="range_end",e.range_middle="range_middle",e.range_start="range_start",e.selected="selected"}(a||(a={})),function(e){e.weeks_before_enter="weeks_before_enter",e.weeks_before_exit="weeks_before_exit",e.weeks_after_enter="weeks_after_enter",e.weeks_after_exit="weeks_after_exit",e.caption_after_enter="caption_after_enter",e.caption_after_exit="caption_after_exit",e.caption_before_enter="caption_before_enter",e.caption_before_exit="caption_before_exit"}(i||(i={}))},31741:(e,t,r)=>{r.d(t,{U:()=>o});var n=r(23404);function o(){let e={};for(let t in n.UI)e[n.UI[t]]=`rdp-${n.UI[t]}`;for(let t in n.BE)e[n.BE[t]]=`rdp-${n.BE[t]}`;for(let t in n.fP)e[n.fP[t]]=`rdp-${n.fP[t]}`;for(let t in n.fw)e[n.fw[t]]=`rdp-${n.fw[t]}`;return e}}};