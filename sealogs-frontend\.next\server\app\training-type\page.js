(()=>{var e={};e.id=319,e.ids=[319],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},98993:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),r(7960),r(47908),r(78398),r(57757),r(48045);var t=r(40060),i=r(33581),n=r(57567),a=r.n(n),l=r(51650),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let c=["",{children:["training-type",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7960)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\training-type\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,47908)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\training-type\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\training-type\\page.tsx"],u="/training-type/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/training-type/page",pathname:"/training-type",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},92229:(e,s,r)=>{Promise.resolve().then(r.bind(r,40043))},56813:(e,s,r)=>{Promise.resolve().then(r.bind(r,87223))},40043:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(98768);r(60343);var i=r(64837);function n({children:e}){return t.jsx(i.Z,{children:e})}},87223:(e,s,r)=>{"use strict";r.d(s,{default:()=>_});var t=r(98768),i=r(66263),n=r(60343),a=r(79418),l=r(94060),o=r(7678),c=r.n(o),d=r(14826),u=r.n(d),p=r(51742),x=r(30905),g=r(39650),m=r(29428),h=r(27514),f=r(22995),v=r(9210);let y=()=>{let{isMobile:e}=(0,f.Ap)();return(0,t.jsxs)(h.DropdownMenu,{children:[t.jsx(h.DropdownMenuTrigger,{asChild:!0,children:t.jsx(v.HV,{size:36})}),(0,t.jsxs)(h.DropdownMenuContent,{side:e?"bottom":"right",align:e?"end":"start",children:[t.jsx(i.default,{href:"/training-type/create",children:t.jsx(h.DropdownMenuItem,{children:"Add A Training Type"})}),t.jsx(i.default,{href:"/crew-training",children:t.jsx(h.DropdownMenuItem,{children:"Crew Trainings"})})]})]})};var j=r(26100),N=r(52241),w=r(99891),T=r(87175),P=r(39544),b=r(15580),q=r(50058);let _=()=>{let[e,s]=(0,n.useState)([]),[r,o]=(0,n.useState)({}),[d,h]=(0,n.useState)(!0),[f,v]=(0,n.useState)([]),[_,C]=(0,n.useState)(0),{getVesselWithIcon:M}=(0,N.P)(),S=(0,q.k)(),[k]=(0,a.t)(l.$0,{fetchPolicy:"cache-and-network",onCompleted:t=>{let i=t.readTrainingTypes.nodes,n=t.readTrainingTypes.pageInfo.hasNextPage;if(t.readTrainingTypes.pageInfo.totalCount,i.length>0&&(s([...e,...i]),n)){let e=_+1;C(e),k({variables:{limit:100,offset:100*e,filter:{...r,...f}}})}},onError:e=>{console.error("queryTrainingTypes error",e)}});(0,n.useEffect)(()=>{d&&(s([]),C(0),z(),h(!1))},[d]);let z=async(e={...r},t=f)=>{if(t.length>0){let r=t.map(async s=>await k({variables:{filter:{...e,...s}}})),i=await Promise.all(r);s(i=(i=(i=i.filter(e=>e.data.readTrainingTypes.nodes.length>0)).flatMap(e=>e.data.readTrainingTypes.nodes)).filter((e,s,r)=>r.findIndex(s=>s.id===e.id)===s))}else await k({variables:{filter:e}})},A=(0,p.wu)([{accessorKey:"title",header:({column:e})=>t.jsx(x.u,{column:e,title:"Nature of Training"}),cellAlignment:"left",cell:({row:e})=>{let s=e.original;return t.jsx(i.default,{href:`/training-type/info?id=${s.id}`,className:"hover:underline",children:s.title})},sortingFn:(e,s)=>{let r=e?.original?.title||"",t=s?.original?.title||"";return r.localeCompare(t)}},{accessorKey:"vessels",header:({column:e})=>t.jsx(x.u,{column:e,title:"Vessels"}),cellAlignment:"left",cell:({row:e})=>{let s=e.original,r=S.small?2:1;return t.jsx("div",{className:"flex sm:flex-wrap gap-2",children:s?.vessels?.nodes.map((e,i)=>{if(i<r){let s=M(e.id,e);return t.jsx(t.Fragment,{children:S["tablet-md"]?t.jsxs(T.C,{variant:"outline",type:"normal",className:"font-normal flex items-center gap-2",children:[t.jsx("div",{className:"size-6 flex items-center justify-center flex-shrink-0 [&_img]:!size-6 [&_svg]:!size-6",children:t.jsx(w.Z,{vessel:s})}),e.title]},e.id):t.jsx("div",{className:"size-11 flex items-center justify-center flex-shrink-0 [&_img]:!size-11 [&_svg]:!size-11",children:t.jsx(w.Z,{vessel:s})})})}return i===r?t.jsxs(b.Popover,{children:[t.jsx(b.PopoverTrigger,{asChild:!0,children:t.jsxs(P.Button,{variant:"outline",className:"!p-2 bg-transparent flex-none",children:["+",s.vessels.nodes.length-r," ","more"]})}),t.jsx(b.PopoverContent,{className:"w-64 p-2",children:t.jsx("div",{className:"space-y-1",children:s.vessels.nodes.slice(r).map(e=>{let s=M(e.id,e);return t.jsxs("div",{className:"px-2 py-1 text-sm hover:bg-muted rounded flex items-center gap-2",children:[t.jsx("div",{className:"size-6 flex items-center justify-center flex-shrink-0 [&_img]:!size-6 [&_svg]:!size-6",children:t.jsx(w.Z,{vessel:s})}),e.title]},e.id)})})})]},e.id):null})})},sortingFn:(e,s)=>{let r=e?.original?.vessels?.nodes?.[0]?.title||"",t=s?.original?.vessels?.nodes?.[0]?.title||"";return r.localeCompare(t)}},{accessorKey:"occursEvery",header:({column:e})=>t.jsx(x.u,{column:e,title:"Occurs Every (days)"}),breakpoint:"desktop",cell:({row:e})=>{let s=e.original;return t.jsx("span",{children:s.occursEvery?s.occursEvery:"-"})}},{accessorKey:"details",header:({column:e})=>t.jsx(x.u,{column:e,title:"Other details"}),breakpoint:"laptop",cellAlignment:"right",cell:({row:e})=>{let s=e.original;return(0,t.jsxs)("div",{className:"space-y-2 flex flex-col items-end",children:[s.occursEvery?(0,t.jsxs)("div",{className:"w-fit desktop:hidden flex items-center gap-2",children:[t.jsx("span",{className:"text-sm font-medium",children:"Occurs Every:"}),(0,t.jsxs)(T.C,{variant:"secondary",type:"normal",className:"size-fit py-1 px-2",children:[s.occursEvery," days"]})]}):null,0!=s.mediumWarnWithin?(0,t.jsxs)("div",{className:"w-fit flex items-center gap-2",children:[t.jsx("span",{className:"text-sm",children:"Medium Warning Within:"}),(0,t.jsxs)(T.C,{variant:"warning",type:"normal",className:"size-fit py-1 px-2",children:[s.mediumWarnWithin," days"]})]}):null,s.highWarnWithin?(0,t.jsxs)("div",{className:"w-fit flex items-center gap-2",children:[t.jsx("span",{className:"text-sm",children:"High Warning Within:"}),(0,t.jsxs)(T.C,{variant:"destructive",type:"normal",className:"size-fit py-1 px-2",children:[s.highWarnWithin," days"]})]}):null]})}}]);return(0,t.jsxs)(t.Fragment,{children:[t.jsx(g.ListHeader,{icon:t.jsx(m._,{className:"h-12 w-12 ring-1 p-0.5 rounded-full bg-background"}),title:"Training Types",actions:t.jsx(y,{})}),t.jsx("div",{className:"mt-16",children:d?t.jsx(j.Z,{}):t.jsx(p.wQ,{columns:A,data:e,pageSize:10,onChange:({type:e,data:t})=>{let i={...r};"vessel"===e&&(Array.isArray(t)&&t.length>0?i.vessels={id:{in:t.map(e=>+e.value)}}:t&&!Array.isArray(t)?i.vessels={id:{contains:+t.value}}:delete i.vessels);let n=f;"keyword"===e&&(n=c()(u()(t.value))?[]:[{title:{contains:t.value}},{procedure:{contains:t.value}}]),o(i),v(n),s([]),C(0),z(i,n)},showToolbar:!0})})]})}},47908:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\training-type\layout.tsx#default`)},7960:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(96141);let i=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\training-type\list.tsx#default`),n=()=>t.jsx(i,{})}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7944],()=>r(98993));module.exports=t})();