"use strict";exports.id=6342,exports.ids=[6342],exports.modules={76342:(t,e,n)=>{n.d(e,{TRb:()=>w,zrG:()=>N,xvb:()=>x,fJx:()=>u,h3H:()=>o,LFI:()=>tC,U2:()=>tS,Lss:()=>tl,apX:()=>tt,FVO:()=>tE,De6:()=>eF,ooB:()=>tn,T:()=>tD,pfm:()=>y,_Pq:()=>D,CQz:()=>F,R4e:()=>e5,jwd:()=>eM,jV1:()=>tP,xak:()=>tv,Bsb:()=>j,Xfh:()=>H,a1J:()=>e1,Di8:()=>tk,MfI:()=>te,job:()=>tU,q$t:()=>S,cKm:()=>tm,zfn:()=>W,EuY:()=>J,kdg:()=>tB,fQS:()=>k,UO3:()=>to,aL5:()=>L,xUA:()=>p,dd:()=>r,ZKO:()=>a,ZQB:()=>d,qK0:()=>s,YMB:()=>X,qb9:()=>M,VD2:()=>eR,xoj:()=>td,VyK:()=>ta,b1D:()=>eE,ZZG:()=>t7,Npr:()=>tO,ymw:()=>tq,tE3:()=>t2,WKg:()=>ek,VN3:()=>eT,fwu:()=>eD,lde:()=>t9,CVO:()=>ep,FT2:()=>ed,j_c:()=>en,n:()=>eo,W3J:()=>eW,Xwv:()=>es,usS:()=>t3,_AY:()=>eb,Zme:()=>tJ,vtx:()=>tN,ovu:()=>eV,B$M:()=>tH,Us6:()=>tz,WXb:()=>t5,YMp:()=>el,Km9:()=>eL,H6U:()=>eg,sPU:()=>tA,Q2n:()=>ec,Wzj:()=>et,DEc:()=>e2,f4G:()=>tx,MGR:()=>eI,UB7:()=>eH,VOg:()=>eh,ALb:()=>tK,mkP:()=>eA,jqs:()=>ej,Bow:()=>G,T5T:()=>m,Ck7:()=>tR,xrA:()=>g,pmE:()=>R,pFH:()=>b,pip:()=>e9,Ybw:()=>h,wth:()=>tf,l9V:()=>tF,_bR:()=>c,m$Y:()=>tj,XWI:()=>eG,O$f:()=>e0,sn:()=>e7,qA5:()=>eN,Vq7:()=>e3,ftk:()=>e4,iXP:()=>eK,F$U:()=>eX,rd7:()=>$,bky:()=>C,_rG:()=>l,a4M:()=>P,VgQ:()=>V,Fii:()=>A,wm0:()=>O,Rji:()=>K,Qem:()=>T,G7d:()=>t$,zv_:()=>tg,KMF:()=>ty,nvD:()=>th,UtL:()=>Q,iv4:()=>tZ,RgS:()=>ts,B2:()=>ef,wMh:()=>ti,SBj:()=>I,HcK:()=>v,TB5:()=>f,iW9:()=>e6,cvT:()=>tI,VI8:()=>tM,S3v:()=>Y,DN_:()=>tT,JS_:()=>tL,f9L:()=>tc,KZM:()=>z,GHe:()=>t_,rRz:()=>tu,D7q:()=>_,TV:()=>Z,_Bk:()=>B,Vhv:()=>E,AXh:()=>U,FxW:()=>q,H3E:()=>tr,HBr:()=>tp,i2f:()=>eU,QjH:()=>t1,HAf:()=>tb,xaU:()=>tW,XY1:()=>eP,cGi:()=>eZ,r_w:()=>ev,BYr:()=>t8,Icx:()=>ea,p_r:()=>er,zfb:()=>ei,j_E:()=>eu,_qs:()=>eO,czP:()=>em,cdV:()=>t4,tJM:()=>t0,edP:()=>tQ,VMX:()=>ew,d5s:()=>tX,gsC:()=>tY,Sku:()=>t6,dxh:()=>eS,Ape:()=>e_,C$E:()=>eC,UV2:()=>tw,nJw:()=>e$,kst:()=>ee,T7x:()=>eJ,ZnR:()=>eQ,V8T:()=>tV,V4y:()=>ey,cNX:()=>eY,tLr:()=>ez,_YT:()=>eB,$nS:()=>tG,xuT:()=>ex,fBE:()=>eq});var i=n(45519);let p=(0,i.ZP)`
    mutation CreateToken($email: String!, $password: String!) {
        createToken(email: $email, password: $password) {
            token
            status
            member {
                firstName
                surname
                superAdmin
                id
                availableClients
            }
        }
    }
`,a=(0,i.ZP)`
    mutation CreateTrainingSessionDue($input: CreateTrainingSessionDueInput!) {
        createTrainingSessionDue(input: $input) {
            id
        }
    }
`,u=(0,i.ZP)`
    mutation CreateCrewDuty($input: CreateCrewDutyInput!) {
        createCrewDuty(input: $input) {
            id
        }
    }
`,o=(0,i.ZP)`
    mutation CreateCrewTraining_LogBookEntrySection(
        $input: CreateCrewTraining_LogBookEntrySectionInput!
    ) {
        createCrewTraining_LogBookEntrySection(input: $input) {
            id
        }
    }
`,r=(0,i.ZP)`
    mutation CreateTrainingSession($input: CreateTrainingSessionInput!) {
        createTrainingSession(input: $input) {
            id
        }
    }
`,d=(0,i.ZP)`
    mutation CreateTrainingType($input: CreateTrainingTypeInput!) {
        createTrainingType(input: $input) {
            id
        }
    }
`,s=(0,i.ZP)`
    mutation CreateSeaLogsMember($input: CreateSeaLogsMemberInput!) {
        createSeaLogsMember(input: $input) {
            id
            firstName
            surname
        }
    }
`,m=(0,i.ZP)`
    mutation DeleteCrewDuties($ids: [ID]!) {
        deleteCrewDuties(ids: $ids)
    }
`,c=(0,i.ZP)`
    mutation DeleteTrainingTypes($ids: [ID]!) {
        deleteTrainingTypes(ids: $ids)
    }
`;(0,i.ZP)`
    mutation DeleteUser($ids: [ID]!) {
        deleteSeaLogsMembers(ids: $ids)
    }
`,(0,i.ZP)`
    mutation LOGIN($userName: String!, $password: String!) {
        login(password: $password, userName: $userName) {
            isSuccess
            data {
                jwt
                message
                refreshJWT
                tokenId
                userId
            }
        }
    }
`;let $=(0,i.ZP)`
    mutation RequestResetPassword($email: String!) {
        requestResetPassword(email: $email) {
            result
            message
        }
    }
`,C=(0,i.ZP)`
    mutation ResetPassword(
        $token: String!
        $password: String!
        $passwordConfirm: String!
    ) {
        resetPassword(
            token: $token
            password: $password
            passwordConfirm: $passwordConfirm
        ) {
            result
            message
        }
    }
`,g=(0,i.ZP)`
    mutation DeleteFuelTanks($ids: [ID]!) {
        deleteFuelTanks(ids: $ids)
    }
`,S=(0,i.ZP)`
    mutation CreateR2File($input: CreateR2FileInput!) {
        createR2File(input: $input) {
            id
            title
        }
    }
`,l=(0,i.ZP)`
    mutation UpdateAddress($input: UpdateAddressInput!) {
        updateAddress(input: $input) {
            id
        }
    }
`,y=(0,i.ZP)`
    mutation CreateInfringementNotice($input: CreateInfringementNoticeInput!) {
        createInfringementNotice(input: $input) {
            id
        }
    }
`,I=(0,i.ZP)`
    mutation UpdateInfringementNotice($input: UpdateInfringementNoticeInput!) {
        updateInfringementNotice(input: $input) {
            id
        }
    }
`,P=(0,i.ZP)`
    mutation UpdateClient($input: UpdateClientInput!) {
        updateClient(input: $input) {
            id
        }
    }
`,k=(0,i.ZP)`
    mutation CreateCaptureImage($input: CreateCaptureImageInput!) {
        createCaptureImage(input: $input) {
            id
            name
        }
    }
`,T=(0,i.ZP)`
    mutation UpdateCrewDuty($input: UpdateCrewDutyInput!) {
        updateCrewDuty(input: $input) {
            id
        }
    }
`,Z=(0,i.ZP)`
    mutation UpdateTrainingSession($input: UpdateTrainingSessionInput!) {
        updateTrainingSession(input: $input) {
            id
        }
    }
`,E=(0,i.ZP)`
    mutation UpdateTrainingType($input: UpdateTrainingTypeInput!) {
        updateTrainingType(input: $input) {
            id
        }
    }
`,U=(0,i.ZP)`
    mutation UpdateSeaLogsMember($input: UpdateSeaLogsMemberInput!) {
        updateSeaLogsMember(input: $input) {
            id
            client {
                useDepartment
            }
        }
    }
`,L=(0,i.ZP)`
    mutation CreateSupplier($input: CreateSupplierInput!) {
        createSupplier(input: $input) {
            id
            name
        }
    }
`,_=(0,i.ZP)`
    mutation UpdateSupplier($input: UpdateSupplierInput!) {
        updateSupplier(input: $input) {
            id
            name
            supplierContacts {
                nodes {
                    id
                    name
                    phone
                    email
                }
            }
        }
    }
`,B=(0,i.ZP)`
    mutation UpdateTrainingSessionDue($input: UpdateTrainingSessionDueInput!) {
        updateTrainingSessionDue(input: $input) {
            id
        }
    }
`;(0,i.ZP)`
    mutation DeleteSupplier($id: Int!) {
        deleteSupplier(ID: $id) {
            isSuccess
            data
        }
    }
`;let h=(0,i.ZP)`
    mutation DeleteLogBookEntry($ids: [ID]!) {
        deleteLogBookEntries(ids: $ids)
    }
`,D=(0,i.ZP)`
    mutation CreateInventory($input: CreateInventoryInput!) {
        createInventory(input: $input) {
            id
            item
        }
    }
`,v=(0,i.ZP)`
    mutation UpdateInventory($input: UpdateInventoryInput!) {
        updateInventory(input: $input) {
            id
        }
    }
`,M=(0,i.ZP)`
    mutation CreateVesselStatus($input: CreateVesselStatusInput!) {
        createVesselStatus(input: $input) {
            id
            date
            status
            comment
            created
            reason
            otherReason
            expectedReturn
            vesselID
        }
    }
`,R=(0,i.ZP)`
    mutation DeleteInventories($ids: [ID]!) {
        deleteInventories(ids: $ids)
    }
`,F=(0,i.ZP)`
    mutation CreateInventoryCategory($input: CreateInventoryCategoryInput!) {
        createInventoryCategory(input: $input) {
            id
            name
            abbreviation
        }
    }
`,f=(0,i.ZP)`
    mutation UpdateInventoryCategory($input: UpdateInventoryCategoryInput!) {
        updateInventoryCategory(input: $input) {
            id
        }
    }
`,b=(0,i.ZP)`
    mutation DeleteInventoryCategory($ids: [ID]!) {
        deleteInventoryCategories(ids: $ids)
    }
`,w=(0,i.ZP)`
    mutation CreateComponentMaintenanceCheck(
        $input: CreateComponentMaintenanceCheckInput!
    ) {
        createComponentMaintenanceCheck(input: $input) {
            id
        }
    }
`,V=(0,i.ZP)`
    mutation UpdateComponentMaintenanceCheck(
        $input: UpdateComponentMaintenanceCheckInput!
    ) {
        updateComponentMaintenanceCheck(input: $input) {
            id
        }
    }
`,G=(0,i.ZP)`
    mutation DeleteComponentMaintenanceChecks($id: [ID]!) {
        deleteComponentMaintenanceChecks(ids: $id)
    }
`,W=(0,i.ZP)`
    mutation CreateSeaLogsFileLinks($input: CreateSeaLogsFileLinksInput!) {
        createSeaLogsFileLinks(input: $input) {
            id
            link
        }
    }
`;(0,i.ZP)`
    mutation UpdateMaintenanceCheck_Signature(
        $input: UpdateMaintenanceCheck_SignatureInput!
    ) {
        updateMaintenanceCheck_Signature(input: $input) {
            id
        }
    }
`,(0,i.ZP)`
    mutation CreateMaintenanceCheck_Signature(
        $input: CreateMaintenanceCheck_SignatureInput!
    ) {
        createMaintenanceCheck_Signature(input: $input) {
            id
        }
    }
`;let O=(0,i.ZP)`
    mutation UpdateComponentMaintenanceSchedule(
        $input: UpdateComponentMaintenanceScheduleInput!
    ) {
        updateComponentMaintenanceSchedule(input: $input) {
            id
        }
    }
`,N=(0,i.ZP)`
    mutation CreateComponentMaintenanceSchedule(
        $input: CreateComponentMaintenanceScheduleInput!
    ) {
        createComponentMaintenanceSchedule(input: $input) {
            id
        }
    }
`,A=(0,i.ZP)`
    mutation UpdateMaintenanceCheckSubTask(
        $input: UpdateMaintenanceCheckSubTaskInput!
    ) {
        updateMaintenanceCheckSubTask(input: $input) {
            id
            status
        }
    }
`,x=(0,i.ZP)`
    mutation CreateMaintenanceScheduleSubTask(
        $input: CreateMaintenanceScheduleSubTaskInput!
    ) {
        createMaintenanceScheduleSubTask(input: $input) {
            id
        }
    }
`,K=(0,i.ZP)`
    mutation UpdateMaintenanceScheduleSubTask(
        $input: UpdateMaintenanceScheduleSubTaskInput!
    ) {
        updateMaintenanceScheduleSubTask(input: $input) {
            id
        }
    }
`,j=(0,i.ZP)`
    mutation CreateMaintenanceCheckSubTask(
        $input: CreateMaintenanceCheckSubTaskInput!
    ) {
        createMaintenanceCheckSubTask(input: $input) {
            id
            status
            findings
            maintenanceScheduleSubTask {
                task
                description
            }
            completedBy {
                id
                firstName
                surname
            }
        }
    }
`,q=(0,i.ZP)`
    mutation UpdateVessel($input: UpdateVesselInput!) {
        updateVessel(input: $input) {
            id
            documents {
                nodes {
                    id
                    fileFilename
                    name
                    title
                }
            }
        }
    }
`,X=(0,i.ZP)`
    mutation CreateVessel($input: CreateVesselInput!) {
        createVessel(input: $input) {
            id
        }
    }
`,H=(0,i.ZP)`
    mutation CreateMemberTraining_Signature(
        $input: CreateMemberTraining_SignatureInput!
    ) {
        createMemberTraining_Signature(input: $input) {
            id
        }
    }
`,Y=(0,i.ZP)`
    mutation UpdateMemberTraining_Signature(
        $input: UpdateMemberTraining_SignatureInput!
    ) {
        updateMemberTraining_Signature(input: $input) {
            id
        }
    }
`,z=(0,i.ZP)`
    mutation UpdateSeaLogsGroup($input: UpdateSeaLogsGroupInput!) {
        updateSeaLogsGroup(input: $input) {
            id
            code
            description
            title
            permissionCodes
            permissions(limit: 1000) {
                nodes {
                    id
                    code
                    groupID
                }
            }
        }
    }
`,J=(0,i.ZP)`
    mutation CreateSeaLogsGroup($input: CreateSeaLogsGroupInput!) {
        createSeaLogsGroup(input: $input) {
            id
            title
            description
            permissionCodes
            permissions(limit: 1000) {
                nodes {
                    id
                    code
                    groupID
                }
            }
        }
    }
`,Q=(0,i.ZP)`
    mutation UpdateEngine($input: UpdateEngineInput!) {
        updateEngine(input: $input) {
            id
        }
    }
`,tt=(0,i.ZP)`
    mutation CreateEngine($input: CreateEngineInput!) {
        createEngine(input: $input) {
            id
        }
    }
`,te=(0,i.ZP)`
    mutation CreateParentComponent_Component(
        $input: CreateParentComponent_ComponentInput!
    ) {
        createParentComponent_Component(input: $input) {
            id
        }
    }
`,tn=(0,i.ZP)`
    mutation CreateFuelTank($input: CreateFuelTankInput!) {
        createFuelTank(input: $input) {
            id
        }
    }
`,ti=(0,i.ZP)`
    mutation UpdateFuelTank($input: UpdateFuelTankInput!) {
        updateFuelTank(input: $input) {
            id
        }
    }
`,tp=(0,i.ZP)`
    mutation UpdateWaterTank($input: UpdateWaterTankInput!) {
        updateWaterTank(input: $input) {
            id
        }
    }
`,ta=(0,i.ZP)`
    mutation CreateWaterTank($input: CreateWaterTankInput!) {
        createWaterTank(input: $input) {
            id
        }
    }
`,tu=(0,i.ZP)`
    mutation UpdateSewageSystem($input: UpdateSewageSystemInput!) {
        updateSewageSystem(input: $input) {
            id
        }
    }
`,to=(0,i.ZP)`
    mutation CreateSewageSystem($input: CreateSewageSystemInput!) {
        createSewageSystem(input: $input) {
            id
        }
    }
`,tr=(0,i.ZP)`
    mutation UpdateVesselSpecifics($input: UpdateVesselSpecificsInput!) {
        updateVesselSpecifics(input: $input) {
            id
        }
    }
`,td=(0,i.ZP)`
    mutation CreateVesselSpecifics($input: CreateVesselSpecificsInput!) {
        createVesselSpecifics(input: $input) {
            id
        }
    }
`;(0,i.ZP)`
    mutation UpdateBasicComponent($input: UpdateBasicComponentInput!) {
        updateBasicComponent(input: $input) {
            id
            documents {
                nodes {
                    id
                    fileFilename
                    name
                    title
                }
            }
        }
    }
`;let ts=(0,i.ZP)`
    mutation UpdateFile($input: UpdateFileInput!) {
        updateFile(input: $input) {
            id
            fileFilename
            name
            title
            created
        }
    }
`,tm=(0,i.ZP)`
    mutation CreateRadioLog($input: CreateRadioLogInput!) {
        createRadioLog(input: $input) {
            id
        }
    }
`,tc=(0,i.ZP)`
    mutation UpdateRadioLog($input: UpdateRadioLogInput!) {
        updateRadioLog(input: $input) {
            id
        }
    }
`,t$=(0,i.ZP)`
    mutation UpdateCustomisedComponentField(
        $input: UpdateCustomisedComponentFieldInput!
    ) {
        updateCustomisedComponentField(input: $input) {
            id
        }
    }
`,tC=(0,i.ZP)`
    mutation CreateCustomisedComponentField(
        $input: CreateCustomisedComponentFieldInput!
    ) {
        createCustomisedComponentField(input: $input) {
            id
        }
    }
`,tg=(0,i.ZP)`
    mutation UpdateCustomisedComponentFieldData(
        $input: UpdateCustomisedComponentFieldDataInput!
    ) {
        updateCustomisedComponentFieldData(input: $input) {
            id
            status
            comment
            trainingSessionID
            customisedComponentFieldID
        }
    }
`,tS=(0,i.ZP)`
    mutation CreateCustomisedComponentFieldData(
        $input: CreateCustomisedComponentFieldDataInput!
    ) {
        createCustomisedComponentFieldData(input: $input) {
            id
            status
            comment
            trainingSessionID
            customisedComponentFieldID
        }
    }
`,tl=(0,i.ZP)`
    mutation CreateCustomisedLogBookComponent(
        $input: CreateCustomisedLogBookComponentInput!
    ) {
        createCustomisedLogBookComponent(input: $input) {
            id
        }
    }
`,ty=(0,i.ZP)`
    mutation UpdateCustomisedLogBookComponent(
        $input: UpdateCustomisedLogBookComponentInput!
    ) {
        updateCustomisedLogBookComponent(input: $input) {
            id
            active
        }
    }
`,tI=(0,i.ZP)`
    mutation UpdateLogBookEntry($input: UpdateLogBookEntryInput!) {
        updateLogBookEntry(input: $input) {
            id
        }
    }
`,tP=(0,i.ZP)`
    mutation CreateLogBookEntry($input: CreateLogBookEntryInput!) {
        createLogBookEntry(input: $input) {
            id
        }
    }
`,tk=(0,i.ZP)`
    mutation CreateOtherShip($input: CreateOtherShipInput!) {
        createOtherShip(input: $input) {
            id
            title
            registration
            details
        }
    }
`,tT=(0,i.ZP)`
    mutation UpdateOtherShip($input: UpdateOtherShipInput!) {
        updateOtherShip(input: $input) {
            id
        }
    }
`,tZ=(0,i.ZP)`
    mutation UpdateEngineStartStop($input: UpdateEngineStartStopInput!) {
        updateEngineStartStop(input: $input) {
            id
        }
    }
`,tE=(0,i.ZP)`
    mutation CreateEngineStartStop($input: CreateEngineStartStopInput!) {
        createEngineStartStop(input: $input) {
            id
        }
    }
`,tU=(0,i.ZP)`
    mutation CreatePilotTransfer($input: CreatePilotTransferInput!) {
        createPilotTransfer(input: $input) {
            id
            transferType
            transferTime
            safetyBriefing
            comment
            pilots {
                nodes {
                    id
                    firstName
                    surname
                }
            }
            transferees {
                nodes {
                    id
                    firstName
                    surname
                }
            }
        }
    }
`,tL=(0,i.ZP)`
    mutation UpdatePilotTransfer($input: UpdatePilotTransferInput!) {
        updatePilotTransfer(input: $input) {
            id
        }
    }
`,t_=(0,i.ZP)`
    mutation UpdateSectionMemberComment(
        $input: UpdateSectionMemberCommentInput!
    ) {
        updateSectionMemberComment(input: $input) {
            id
        }
    }
`,tB=(0,i.ZP)`
    mutation CreateSectionMemberComment(
        $input: CreateSectionMemberCommentInput!
    ) {
        createSectionMemberComment(input: $input) {
            id
        }
    }
`,th=(0,i.ZP)`
    mutation UpdateCustomisedLogBookConfig(
        $input: UpdateCustomisedLogBookConfigInput!
    ) {
        updateCustomisedLogBookConfig(input: $input) {
            id
        }
    }
`,tD=(0,i.ZP)`
    mutation CreateGeoLocation($input: CreateGeoLocationInput!) {
        createGeoLocation(input: $input) {
            title
            id
            lat
            long
        }
    }
`,tv=(0,i.ZP)`
    mutation CreateMaintenanceCategory(
        $input: CreateMaintenanceCategoryInput!
    ) {
        createMaintenanceCategory(input: $input) {
            id
            name
        }
    }
`,tM=(0,i.ZP)`
    mutation UpdateMaintenanceCategory(
        $input: UpdateMaintenanceCategoryInput!
    ) {
        updateMaintenanceCategory(input: $input) {
            id
            name
        }
    }
`,tR=(0,i.ZP)`
    mutation DeleteCustomisedComponentField($ids: [ID]!) {
        deleteCustomisedComponentFields(ids: $ids)
    }
`,tF=(0,i.ZP)`
    mutation DeleteCaptureImages($ids: [ID]!) {
        deleteCaptureImages(ids: $ids)
    }
`;(0,i.ZP)`
    mutation DeleteCustomisedComponentFieldData($ids: [ID]!) {
        deleteCustomisedComponentFieldData(ids: $ids)
    }
`;let tf=(0,i.ZP)`
    mutation DeleteMaintenanceCategory($ids: [ID]!) {
        deleteMaintenanceCategories(ids: $ids)
    }
`;(0,i.ZP)`
    mutation updateAssetReporting_LogBookEntrySection(
        $input: UpdateAssetReporting_LogBookEntrySectionInput!
    ) {
        updateAssetReporting_LogBookEntrySection(input: $input) {
            id
        }
    }
`;let tb=(0,i.ZP)`
    mutation UpdateCrewMembers_LogBookEntrySection(
        $input: UpdateCrewMembers_LogBookEntrySectionInput!
    ) {
        updateCrewMembers_LogBookEntrySection(input: $input) {
            id
        }
    }
`;(0,i.ZP)`
    mutation updateCrewTraining_LogBookEntrySection(
        $input: UpdateCrewTraining_LogBookEntrySectionInput!
    ) {
        updateCrewTraining_LogBookEntrySection(input: $input) {
            id
        }
    }
`,(0,i.ZP)`
    mutation UpdateEngineer_LogBookEntrySection(
        $input: UpdateEngineer_LogBookEntrySectionInput!
    ) {
        updateEngineer_LogBookEntrySection(input: $input) {
            id
        }
    }
`,(0,i.ZP)`
    mutation (
        $input: UpdateEngine_LogBookEntrySectionInput!
    ) {
        updateEngine_LogBookEntrySection(input: $input) {
            id
        }
    }
`,(0,i.ZP)`
    mutation UpdateFuel_LogBookEntrySection(
        $input: UpdateFuel_LogBookEntrySectionInput!
    ) {
        updateFuel_LogBookEntrySection(input: $input) {
            id
        }
    }
`,(0,i.ZP)`
    mutation UpdatePorts_LogBookEntrySection(
        $input: UpdatePorts_LogBookEntrySectionInput!
    ) {
        updatePorts_LogBookEntrySection(input: $input) {
            id
        }
    }
`;let tw=(0,i.ZP)`
    mutation UpdateSupernumerary_LogBookEntrySection(
        $input: UpdateSupernumerary_LogBookEntrySectionInput!
    ) {
        updateSupernumerary_LogBookEntrySection(input: $input) {
            id
        }
    }
`,tV=(0,i.ZP)`
    mutation UpdateTripReport_LogBookEntrySection(
        $input: UpdateTripReport_LogBookEntrySectionInput!
    ) {
        updateTripReport_LogBookEntrySection(input: $input) {
            id
        }
    }
`,tG=(0,i.ZP)`
    mutation UpdateVesselDailyCheck_LogBookEntrySection(
        $input: UpdateVesselDailyCheck_LogBookEntrySectionInput!
    ) {
        updateVesselDailyCheck_LogBookEntrySection(input: $input) {
            id
        }
    }
`;(0,i.ZP)`
    mutation UpdateVoyageSummary_LogBookEntrySection(
        $input: UpdateVoyageSummary_LogBookEntrySectionInput!
    ) {
        updateVoyageSummary_LogBookEntrySection(input: $input) {
            id
        }
    }
`;let tW=(0,i.ZP)`
    mutation UpdateCrewWelfare_LogBookEntrySection(
        $input: UpdateCrewWelfare_LogBookEntrySectionInput!
    ) {
        updateCrewWelfare_LogBookEntrySection(input: $input) {
            id
        }
    }
`;(0,i.ZP)`
    mutation createAssetReporting_LogBookEntrySection(
        $input: CreateAssetReporting_LogBookEntrySectionInput!
    ) {
        createAssetReporting_LogBookEntrySection(input: $input) {
            id
        }
    }
`;let tO=(0,i.ZP)`
    mutation CreateCrewMembers_LogBookEntrySection(
        $input: CreateCrewMembers_LogBookEntrySectionInput!
    ) {
        createCrewMembers_LogBookEntrySection(input: $input) {
            id
        }
    }
`;(0,i.ZP)`
    mutation CreateCrewTraining_LogBookEntrySection(
        $input: CreateCrewTraining_LogBookEntrySectionInput!
    ) {
        createCrewTraining_LogBookEntrySection(input: $input) {
            id
        }
    }
`,(0,i.ZP)`
    mutation CreateEngineer_LogBookEntrySection(
        $input: CreateEngineer_LogBookEntrySectionInput!
    ) {
        createEngineer_LogBookEntrySection(input: $input) {
            id
        }
    }
`,(0,i.ZP)`
    mutation CreateEngine_LogBookEntrySection(
        $input: CreateEngine_LogBookEntrySectionInput!
    ) {
        createEngine_LogBookEntrySection(input: $input) {
            id
        }
    }
`;let tN=(0,i.ZP)`
    mutation CreateFuel_LogBookEntrySection(
        $input: CreateFuel_LogBookEntrySectionInput!
    ) {
        createFuel_LogBookEntrySection(input: $input) {
            id
        }
    }
`;(0,i.ZP)`
    mutation CreatePorts_LogBookEntrySection(
        $input: CreatePorts_LogBookEntrySectionInput!
    ) {
        createPorts_LogBookEntrySection(input: $input) {
            id
        }
    }
`;let tA=(0,i.ZP)`
    mutation CreateSupernumerary_LogBookEntrySection(
        $input: CreateSupernumerary_LogBookEntrySectionInput!
    ) {
        createSupernumerary_LogBookEntrySection(input: $input) {
            id
        }
    }
`,tx=(0,i.ZP)`
    mutation CreateTripReport_LogBookEntrySection(
        $input: CreateTripReport_LogBookEntrySectionInput!
    ) {
        createTripReport_LogBookEntrySection(input: $input) {
            id
        }
    }
`,tK=(0,i.ZP)`
    mutation CreateVesselDailyCheck_LogBookEntrySection(
        $input: CreateVesselDailyCheck_LogBookEntrySectionInput!
    ) {
        createVesselDailyCheck_LogBookEntrySection(input: $input) {
            id
        }
    }
`;(0,i.ZP)`
    mutation CreateVoyageSummary_LogBookEntrySection(
        $input: CreateVoyageSummary_LogBookEntrySectionInput!
    ) {
        createVoyageSummary_LogBookEntrySection(input: $input) {
            id
        }
    }
`;let tj=(0,i.ZP)`
    mutation DeleteCrewMembers_LogBookEntrySections($ids: [ID]!) {
        deleteCrewMembers_LogBookEntrySections(ids: $ids)
    }
`,tq=(0,i.ZP)`
    mutation CreateCrewWelfare_LogBookEntrySection(
        $input: CreateCrewWelfare_LogBookEntrySectionInput!
    ) {
        createCrewWelfare_LogBookEntrySection(input: $input) {
            id
        }
    }
`,tX=(0,i.ZP)`
    mutation UpdateLogBookEntrySection_Signature(
        $input: UpdateLogBookEntrySection_SignatureInput!
    ) {
        updateLogBookEntrySection_Signature(input: $input) {
            id
        }
    }
`,tH=(0,i.ZP)`
    mutation CreateLogBookEntrySection_Signature(
        $input: CreateLogBookEntrySection_SignatureInput!
    ) {
        createLogBookEntrySection_Signature(input: $input) {
            id
            logBookEntrySectionID
        }
    }
`,tY=(0,i.ZP)`
    mutation UpdateLogBookSignOff_LogBookEntrySection(
        $input: UpdateLogBookSignOff_LogBookEntrySectionInput!
    ) {
        updateLogBookSignOff_LogBookEntrySection(input: $input) {
            id
        }
    }
`,tz=(0,i.ZP)`
    mutation CreateLogBookSignOff_LogBookEntrySection(
        $input: CreateLogBookSignOff_LogBookEntrySectionInput!
    ) {
        createLogBookSignOff_LogBookEntrySection(input: $input) {
            id
        }
    }
`,tJ=(0,i.ZP)`
    mutation CreateFuelTankStartStop($input: CreateFuelTankStartStopInput!) {
        createFuelTankStartStop(input: $input) {
            id
        }
    }
`,tQ=(0,i.ZP)`
    mutation UpdateFuelTankStartStop($input: UpdateFuelTankStartStopInput!) {
        updateFuelTankStartStop(input: $input) {
            id
        }
    }
`,t0=(0,i.ZP)`
    mutation UpdateFuelTank($input: UpdateFuelTankInput!) {
        updateFuelTank(input: $input) {
            id
        }
    }
`,t2=(0,i.ZP)`
    mutation CreateCustomisedLogBookConfig(
        $input: CreateCustomisedLogBookConfigInput!
    ) {
        createCustomisedLogBookConfig(input: $input) {
            id
        }
    }
`,t3=(0,i.ZP)`
    mutation CreateEventType_VesselRescue(
        $input: CreateEventType_VesselRescueInput!
    ) {
        createEventType_VesselRescue(input: $input) {
            id
        }
    }
`,t4=(0,i.ZP)`
    mutation UpdateEventType_VesselRescue(
        $input: UpdateEventType_VesselRescueInput!
    ) {
        updateEventType_VesselRescue(input: $input) {
            id
        }
    }
`,t7=(0,i.ZP)`
    mutation CreateCGEventMission($input: CreateCGEventMissionInput!) {
        createCGEventMission(input: $input) {
            id
        }
    }
`,t1=(0,i.ZP)`
    mutation UpdateCGEventMission($input: UpdateCGEventMissionInput!) {
        updateCGEventMission(input: $input) {
            id
        }
    }
`,t5=(0,i.ZP)`
    mutation CreateMissionTimeline($input: CreateMissionTimelineInput!) {
        createMissionTimeline(input: $input) {
            id
        }
    }
`,t6=(0,i.ZP)`
    mutation UpdateMissionTimeline($input: UpdateMissionTimelineInput!) {
        updateMissionTimeline(input: $input) {
            id
        }
    }
`,t9=(0,i.ZP)`
    mutation CreateEngine_Usage($input: CreateEngine_UsageInput!) {
        createEngine_Usage(input: $input) {
            id
        }
    }
`,t8=(0,i.ZP)`
    mutation UpdateEngine_Usage($input: UpdateEngine_UsageInput!) {
        updateEngine_Usage(input: $input) {
            id
        }
    }
`,et=(0,i.ZP)`
    mutation CreateTripEvent($input: CreateTripEventInput!) {
        createTripEvent(input: $input) {
            id
        }
    }
`,ee=(0,i.ZP)`
    mutation UpdateTripEvent($input: UpdateTripEventInput!) {
        updateTripEvent(input: $input) {
            id
        }
    }
`,en=(0,i.ZP)`
    mutation CreateEventType_PersonRescue(
        $input: CreateEventType_PersonRescueInput!
    ) {
        createEventType_PersonRescue(input: $input) {
            id
        }
    }
`,ei=(0,i.ZP)`
    mutation UpdateEventType_PersonRescue(
        $input: UpdateEventType_PersonRescueInput!
    ) {
        updateEventType_PersonRescue(input: $input) {
            id
        }
    }
`,ep=(0,i.ZP)`
    mutation CreateEventType_BarCrossing(
        $input: CreateEventType_BarCrossingInput!
    ) {
        createEventType_BarCrossing(input: $input) {
            id
        }
    }
`,ea=(0,i.ZP)`
    mutation UpdateEventType_BarCrossing(
        $input: UpdateEventType_BarCrossingInput!
    ) {
        updateEventType_BarCrossing(input: $input) {
            id
        }
    }
`,eu=(0,i.ZP)`
    mutation UpdateEventType_RestrictedVisibility(
        $input: UpdateEventType_RestrictedVisibilityInput!
    ) {
        updateEventType_RestrictedVisibility(input: $input) {
            id
        }
    }
`,eo=(0,i.ZP)`
    mutation CreateEventType_RestrictedVisibility(
        $input: CreateEventType_RestrictedVisibilityInput!
    ) {
        createEventType_RestrictedVisibility(input: $input) {
            id
        }
    }
`,er=(0,i.ZP)`
    mutation UpdateEventType_PassengerDropFacility(
        $input: UpdateEventType_PassengerDropFacilityInput!
    ) {
        updateEventType_PassengerDropFacility(input: $input) {
            id
        }
    }
`,ed=(0,i.ZP)`
    mutation CreateEventType_PassengerDropFacility(
        $input: CreateEventType_PassengerDropFacilityInput!
    ) {
        createEventType_PassengerDropFacility(input: $input) {
            id
        }
    }
`,es=(0,i.ZP)`
    mutation CreateEventType_Tasking($input: CreateEventType_TaskingInput!) {
        createEventType_Tasking(input: $input) {
            id
            tripEventID
        }
    }
`,em=(0,i.ZP)`
    mutation UpdateEventType_Tasking($input: UpdateEventType_TaskingInput!) {
        updateEventType_Tasking(input: $input) {
            id
        }
    }
`,ec=(0,i.ZP)`
    mutation CreateTowingChecklist($input: CreateTowingChecklistInput!) {
        createTowingChecklist(input: $input) {
            id
        }
    }
`,e$=(0,i.ZP)`
    mutation UpdateTowingChecklist($input: UpdateTowingChecklistInput!) {
        updateTowingChecklist(input: $input) {
            id
        }
    }
`,eC=(0,i.ZP)`
    mutation UpdateRiskFactor($input: UpdateRiskFactorInput!) {
        updateRiskFactor(input: $input) {
            id
        }
    }
`,eg=(0,i.ZP)`
    mutation CreateRiskFactor($input: CreateRiskFactorInput!) {
        createRiskFactor(input: $input) {
            id
        }
    }
`,eS=(0,i.ZP)`
    mutation UpdateMitigationStrategy($input: UpdateMitigationStrategyInput!) {
        updateMitigationStrategy(input: $input) {
            id
        }
    }
`,el=(0,i.ZP)`
    mutation CreateMitigationStrategy($input: CreateMitigationStrategyInput!) {
        createMitigationStrategy(input: $input) {
            id
        }
    }
`,ey=(0,i.ZP)`
    mutation UpdateTripReport_Stop($input: UpdateTripReport_StopInput!) {
        updateTripReport_Stop(input: $input) {
            id
        }
    }
`,eI=(0,i.ZP)`
    mutation CreateTripReport_Stop($input: CreateTripReport_StopInput!) {
        createTripReport_Stop(input: $input) {
            id
        }
    }
`,eP=(0,i.ZP)`
    mutation UpdateDangerousGoodsChecklist(
        $input: UpdateDangerousGoodsChecklistInput!
    ) {
        updateDangerousGoodsChecklist(input: $input) {
            id
        }
    }
`,ek=(0,i.ZP)`
    mutation CreateDangerousGoodsChecklist(
        $input: CreateDangerousGoodsChecklistInput!
    ) {
        createDangerousGoodsChecklist(input: $input) {
            id
        }
    }
`,eT=(0,i.ZP)`
    mutation CreateDangerousGoodsRecord(
        $input: CreateDangerousGoodsRecordInput!
    ) {
        createDangerousGoodsRecord(input: $input) {
            id
        }
    }
`,eZ=(0,i.ZP)`
    mutation UpdateDangerousGoodsRecord(
        $input: UpdateDangerousGoodsRecordInput!
    ) {
        updateDangerousGoodsRecord(input: $input) {
            id
        }
    }
`,eE=(0,i.ZP)`
    mutation CreateBarCrossingChecklist(
        $input: CreateBarCrossingChecklistInput!
    ) {
        createBarCrossingChecklist(input: $input) {
            id
        }
    }
`,eU=(0,i.ZP)`
    mutation UpdateBarCrossingChecklist(
        $input: UpdateBarCrossingChecklistInput!
    ) {
        updateBarCrossingChecklist(input: $input) {
            id
        }
    }
`,eL=(0,i.ZP)`
    mutation CreateRefuellingBunkering(
        $input: CreateRefuellingBunkeringInput!
    ) {
        createRefuellingBunkering(input: $input) {
            id
        }
    }
`,e_=(0,i.ZP)`
    mutation UpdateRefuellingBunkering(
        $input: UpdateRefuellingBunkeringInput!
    ) {
        updateRefuellingBunkering(input: $input) {
            id
        }
    }
`,eB=(0,i.ZP)`
    mutation UpdateTripUpdate($input: UpdateTripUpdateInput!) {
        updateTripUpdate(input: $input) {
            id
        }
    }
`,eh=(0,i.ZP)`
    mutation CreateTripUpdate($input: CreateTripUpdateInput!) {
        createTripUpdate(input: $input) {
            id
        }
    }
`,eD=(0,i.ZP)`
    mutation CreateDepartment($input: CreateDepartmentInput!) {
        createDepartment(input: $input) {
            id
        }
    }
`,ev=(0,i.ZP)`
    mutation UpdateDepartment($input: UpdateDepartmentInput!) {
        updateDepartment(input: $input) {
            id
        }
    }
`,eM=(0,i.ZP)`
    mutation CreateLogBook($input: CreateLogBookInput!) {
        createLogBook(input: $input) {
            id
        }
    }
`,eR=(0,i.ZP)`
    mutation CreateVehiclePosition($input: CreateVehiclePositionInput!) {
        createVehiclePosition(input: $input) {
            id
        }
    }
`;(0,i.ZP)`
    mutation UpdateVehiclePosition($input: UpdateVehiclePositionInput!) {
        updateVehiclePosition(input: $input) {
            id
        }
    }
`;let eF=(0,i.ZP)`
    mutation CreateFuelLog($input: CreateFuelLogInput!) {
        createFuelLog(input: $input) {
            id
        }
    }
`,ef=(0,i.ZP)`
    mutation UpdateFuelLog($input: UpdateFuelLogInput!) {
        updateFuelLog(input: $input) {
            id
        }
    }
`,eb=(0,i.ZP)`
    mutation CreateFavoriteLocation($input: CreateFavoriteLocationInput!) {
        createFavoriteLocation(input: $input) {
            id
        }
    }
`,ew=(0,i.ZP)`
    mutation UpdateGeoLocation($input: UpdateGeoLocationInput!) {
        updateGeoLocation(input: $input) {
            id
        }
    }
`,eV=(0,i.ZP)`
    mutation CreateGeoLocation($input: CreateGeoLocationInput!) {
        createGeoLocation(input: $input) {
            id
        }
    }
`,eG=(0,i.ZP)`
    mutation DeleteGeoLocations($ids: [ID]!) {
        deleteGeoLocations(ids: $ids)
    }
`,eW=(0,i.ZP)`
    mutation CreateEventType_Supernumerary(
        $input: CreateEventType_SupernumeraryInput!
    ) {
        createEventType_Supernumerary(input: $input) {
            id
        }
    }
`,eO=(0,i.ZP)`
    mutation UpdateEventType_Supernumerary(
        $input: UpdateEventType_SupernumeraryInput!
    ) {
        updateEventType_Supernumerary(input: $input) {
            id
        }
    }
`,eN=(0,i.ZP)`
    mutation DeleteTripReport_LogBookEntrySections($ids: [ID]!) {
        deleteTripReport_LogBookEntrySections(ids: $ids)
    }
`;(0,i.ZP)`
    mutation DeleteSeaLogsGroups($ids: [ID]!) {
        deleteSeaLogsGroups(ids: $ids)
    }
`,(0,i.ZP)`
    mutation DeleteSectionMemberComments($ids: [ID]!) {
        deleteSectionMemberComments(ids: $ids)
    }
`;let eA=(0,i.ZP)`
    mutation CreateWeatherForecast($input: CreateWeatherForecastInput!) {
        createWeatherForecast(input: $input) {
            id
        }
    }
`,ex=(0,i.ZP)`
    mutation UpdateWeatherForecast($input: UpdateWeatherForecastInput!) {
        updateWeatherForecast(input: $input) {
            id
        }
    }
`,eK=(0,i.ZP)`
    mutation DeleteWeatherForecasts($ids: [ID]!) {
        deleteWeatherForecasts(ids: $ids)
    }
`,ej=(0,i.ZP)`
    mutation CreateWeatherTide($input: CreateWeatherTideInput!) {
        createWeatherTide(input: $input) {
            id
        }
    }
`,eq=(0,i.ZP)`
    mutation UpdateWeatherTide($input: UpdateWeatherTideInput!) {
        updateWeatherTide(input: $input) {
            id
        }
    }
`,eX=(0,i.ZP)`
    mutation DeleteWeatherTides($ids: [ID]!) {
        deleteWeatherTides(ids: $ids)
    }
`;(0,i.ZP)`
    mutation CreateWeatherObservation($input: CreateWeatherObservationInput!) {
        createWeatherObservation(input: $input) {
            id
        }
    }
`,(0,i.ZP)`
    mutation UpdateWeatherObservation($input: UpdateWeatherObservationInput!) {
        updateWeatherObservation(input: $input) {
            id
        }
    }
`,(0,i.ZP)`
    mutation DeleteWeatherObservations($ids: [ID]!) {
        deleteWeatherObservations(ids: $ids)
    }
`;let eH=(0,i.ZP)`
    mutation CreateTripScheduleImport($input: CreateTripScheduleImportInput!) {
        createTripScheduleImport(input: $input) {
            id
        }
    }
`,eY=(0,i.ZP)`
    mutation UpdateTripScheduleImport($input: UpdateTripScheduleImportInput!) {
        updateTripScheduleImport(input: $input) {
            id
        }
    }
`,ez=(0,i.ZP)`
    mutation UpdateTripScheduleService(
        $input: UpdateTripScheduleServiceInput!
    ) {
        updateTripScheduleService(input: $input) {
            id
        }
    }
`,eJ=(0,i.ZP)`
    mutation UpdateTripReportSchedule($input: UpdateTripReportScheduleInput!) {
        updateTripReportSchedule(input: $input) {
            id
        }
    }
`,eQ=(0,i.ZP)`
    mutation UpdateTripReportScheduleStop(
        $input: UpdateTripReportScheduleStopInput!
    ) {
        updateTripReportScheduleStop(input: $input) {
            id
        }
    }
`,e0=(0,i.ZP)`
    mutation DeleteTripReportScheduleStop($id: [ID]!) {
        deleteTripReportScheduleStops(ids: $id)
    }
`,e2=(0,i.ZP)`
    mutation CreateTripReportScheduleStop(
        $input: CreateTripReportScheduleStopInput!
    ) {
        createTripReportScheduleStop(input: $input) {
            id
        }
    }
`,e3=(0,i.ZP)`
    mutation DeleteTripScheduleImports($ids: [ID]!) {
        deleteTripScheduleImports(ids: $ids)
    }
`,e4=(0,i.ZP)`
    mutation DeleteTripScheduleServices($ids: [ID]!) {
        deleteTripScheduleServices(ids: $ids)
    }
`,e7=(0,i.ZP)`
    mutation DeleteTripReportSchedules($ids: [ID]!) {
        deleteTripReportSchedules(ids: $ids)
    }
`,e1=(0,i.ZP)`
    mutation createOtherCompany($input: CreateOtherCompanyInput!) {
        createOtherCompany(input: $input) {
            id
            title
        }
    }
`,e5=(0,i.ZP)`
    mutation createKeyContact($input: CreateKeyContactInput!) {
        createKeyContact(input: $input) {
            id
        }
    }
`,e6=(0,i.ZP)`
    mutation updateKeyContact($input: UpdateKeyContactInput!) {
        updateKeyContact(input: $input) {
            id
        }
    }
`,e9=(0,i.ZP)`
    mutation deleteKeyContacts($ids: [ID]!) {
        deleteKeyContacts(ids: $ids)
    }
`}};