(()=>{var e={};e.id=7251,e.ids=[7251],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},19197:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d}),t(49354),t(50681),t(78398),t(57757),t(48045);var r=t(40060),a=t(33581),i=t(57567),o=t.n(i),l=t(51650),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(s,n);let d=["",{children:["settings",{children:["user-role",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,49354)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\user-role\\edit\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,50681)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\user-role\\edit\\page.tsx"],p="/settings/user-role/edit/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/settings/user-role/edit/page",pathname:"/settings/user-role/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},31941:(e,s,t)=>{Promise.resolve().then(t.bind(t,84313))},26957:(e,s,t)=>{Promise.resolve().then(t.bind(t,45177))},84313:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(98768);t(60343);var a=t(64837);function i({children:e}){return r.jsx(a.Z,{children:e})}},45177:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(98768),a=t(61791),i=t(69424);let o=()=>{let e=(0,i.useSearchParams)().get("id")??0;return r.jsx(a.default,{roleID:+e})}},61791:(e,s,t)=>{"use strict";t.d(s,{default:()=>y});var r=t(98768),a=t(69424),i=t(60343),o=t(14826),l=t.n(o),n=t(7678),d=t.n(n),c=t(72548),p=t(79418),u=t(26100),m=t(94060),x=t(76342),g=t(46776),h=t(71890),f=t(60797),v=t(78965),j=t(50088),b=t(25394);let y=({roleID:e=0})=>{let[s,t]=(0,i.useState)(-1),o=(0,a.useRouter)(),[n,y]=(0,i.useState)({}),[P,N]=(0,i.useState)({}),[C,S]=(0,i.useState)([]),[w,_]=(0,i.useState)(!0),[E,L]=(0,i.useState)([]),[k,M]=(0,i.useState)(!1);(0,i.useEffect)(()=>{t((0,g.GJ)())},[]);let T=s=>{let{name:t,value:r}=s.target;y({...n,id:e,[t]:r})},[R,{loading:G}]=(0,c.D)(x.EuY,{onCompleted:e=>{e.createSeaLogsGroup&&o.push("/settings/user-role")},onError:e=>{N({...P,response:e.message})}}),[U,{loading:q}]=(0,c.D)(x.KZM,{onCompleted:e=>{e.updateSeaLogsGroup&&o.push("/settings/user-role")},onError:e=>{N({...P,response:e.message})}}),V=async()=>{if(k)return;if(d()(l()(n?.title))){N({...P,title:"Title is required"});return}N({});let s={...n};delete s.__typename,delete s.permissions,delete s.members,s.permissionCodes=E.join(","),e>0?await U({variables:{input:s}}):await R({variables:{input:s}})},[B,{loading:D}]=(0,p.t)(m.Wu,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readOneSeaLogsGroup;s&&(L(s.permissions.nodes.map(e=>e.code)),y(s),M("admin"===s.code))},onError:e=>{console.error("readOneSealLogsGroup error",e)}}),Z=async()=>{await B({variables:{id:e}})},[O,{loading:A}]=(0,p.t)(m.F5,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readPermissionTypes;if(s){let e=s.reduce((e,s)=>{let{category:t,...r}=s;return e[t]=e[t]||[],e[t].push(r),e[t].sort((e,s)=>e.sort>s.sort),e},{});console.info("permissions",e),S(e)}},onError:e=>{console.error("readPermissionTypes error",e)}}),F=async()=>{await O()},H=(e,s)=>{L(t=>e?[...t,s]:t.filter(e=>e!==s))};return((0,i.useEffect)(()=>{e>0&&Z()},[e]),(0,i.useEffect)(()=>{w&&((0,g.UU)(),F(),_(!1))},[w]),(0,i.useEffect)(()=>{0==e&&L(Object.values(C).flatMap(e=>e.filter(e=>!0===e.default).map(e=>e.code)))},[C]),!1===s)?r.jsx(u.Z,{errorMessage:"Oops You do not have the permission to view this section."}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(b.Zb,{className:"mb-4",children:[r.jsx(b.ll,{children:(0,r.jsxs)(b.H3,{children:[e>0?"Edit":"New"," User Role"]})}),(0,r.jsxs)(b.aY,{className:"flex flex-col gap-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3",children:[r.jsx(b.H3,{className:"text-lg",children:"Role Details"}),(0,r.jsxs)("div",{className:"flex flex-col gap-4 md:col-span-2",children:[P?.response&&r.jsx(b.bZ,{variant:"destructive",children:P?.response}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,r.jsxs)(f.Label,{label:"Title",children:[r.jsx(h.I,{name:"title",type:"text",value:n?.title??"",placeholder:"Title",onChange:T,disabled:k}),r.jsx("small",{className:"text-red-500",children:P?.title})]}),r.jsx(f.Label,{label:"Code",children:r.jsx(h.I,{name:"code",type:"text",value:n?.code??"",placeholder:"Code",onChange:T,disabled:k})})]}),r.jsx(f.Label,{label:"Description",children:r.jsx(b.gx,{name:"description",value:n?.description??"",placeholder:"Description",onChange:T,rows:5,disabled:k})})]})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-3",children:[r.jsx(b.H3,{className:"text-lg",children:"Permissions"}),r.jsx("div",{className:"flex flex-col gap-2 md:col-span-2",children:Object.keys(C).map(e=>(0,r.jsxs)("div",{className:"flex flex-col border rounded-lg p-3 border-secondary",children:[r.jsx(b.P,{className:"font-medium mb-4",children:e}),r.jsx("div",{className:"flex flex-col gap-2.5",children:C[e].map(e=>(0,r.jsxs)("div",{className:"items-top flex space-x-2",children:[r.jsx(b.XZ,{id:e.code,value:e.code,checked:E.includes(e.code),onCheckedChange:s=>H(!0===s,e.code)}),(0,r.jsxs)("div",{className:"grid gap-1.5 leading-none",children:[r.jsx("label",{htmlFor:e.code,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:e.name}),e.help&&r.jsx("p",{className:"text-sm text-muted-foreground",children:e.help})]})]},e.code))})]},e))})]})]})]}),(0,r.jsxs)(v.V,{children:[r.jsx(b.zx,{variant:"back",onClick:()=>o.back(),disabled:G||D||q,children:"Cancel"}),!k&&r.jsx(j.Z,{text:`${e>0?"Update":"Create"} Role`,type:"primary",icon:"check",color:"sky",action:V,isDisabled:G||D||q})]})]})}},50681:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\layout.tsx#default`)},49354:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\user-role\edit\page.tsx#default`)}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[864,8865,3563,6263,8189,9507,7602,9414,6451,4234,2925,5394,4837,6342,88],()=>t(19197));module.exports=r})();