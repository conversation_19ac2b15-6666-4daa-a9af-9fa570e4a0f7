(()=>{var e={};e.id=7035,e.ids=[7035],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},29673:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>l}),s(60977),s(71238),s(78398),s(57757),s(48045);var r=s(40060),n=s(33581),a=s(57567),o=s.n(a),i=s(51650),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let l=["",{children:["reporting",{children:["maintenance-cost-track",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,60977)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\maintenance-cost-track\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\maintenance-cost-track\\page.tsx"],u="/reporting/maintenance-cost-track/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/reporting/maintenance-cost-track/page",pathname:"/reporting/maintenance-cost-track",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},88332:(e,t,s)=>{Promise.resolve().then(s.bind(s,44517))},77134:(e,t,s)=>{Promise.resolve().then(s.bind(s,49558))},91973:(e,t,s)=>{"use strict";s.d(t,{u:()=>r});let r=(e,t="report.csv")=>{let s=new Blob([e.map(e=>e.join(",")).join("\n")],{type:"text/csv;charset=utf-8,"}),r=URL.createObjectURL(s);Object.assign(document.createElement("a"),{href:r,download:t}).click(),URL.revokeObjectURL(r)}},2604:(e,t,s)=>{"use strict";s.d(t,{S:()=>o});var r=s(9707),n=s(58774),a=s.n(n);function o(e,t){let s=new r.default(t);a()(s,{head:e.headers,body:e.body,foot:e.footers,margin:5,...e.userOptions}),s.save(e.fileName||"report.pdf")}},44517:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(98768);s(60343);var n=s(32993),a=s(64837);function o({children:e}){return r.jsx(n.default,{children:r.jsx(a.Z,{children:e})})}},49558:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>S});var r=s(98768);s(46776);var n=s(26100),a=s(37042),o=s(63043),i=s(79418),c=s(60343),l=s(49517),d=s(13842),u=s(83179),p=s.n(u),x=s(91973),m=s(2604),h=s(69424),g=s(26659),f=s(17203),j=s(25394);let v=["Task Name","Inventory","Location","Assigned To","Status","Due Date","Projected","Actual","Difference"];function N(){let e=(0,h.useRouter)(),[t,s]=(0,c.useState)([]),[n,u]=(0,c.useState)(null),[N,S]=(0,c.useState)(null),[k,y]=(0,c.useState)(null),[D,w]=(0,c.useState)({startDate:new Date,endDate:new Date}),[L,{called:T,loading:q,data:C}]=(0,i.t)(o.p,{fetchPolicy:"cache-and-network",onError:e=>{console.error("GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error",e)}}),P=(0,c.useMemo)(()=>{let e=C?.readComponentMaintenanceChecks.nodes??[];if(0===e.length)return[];let t=[];return e.forEach(e=>{let s={taskName:e.name,vesselName:e.basicComponent.title,assignedTo:0==e.assignedTo.id?void 0:`${e.assignedTo.firstName} ${e.assignedTo.surname}`,inventoryName:e.inventory.title,dueDate:e.expires?new Date(e.expires):void 0,status:e.status,dueStatus:(0,d.AT)(e),projected:e.projected??0,actual:e.actual??0,difference:(e.projected??0)-(e.actual??0)};t.push(s)}),t},[T,q,C]);return(0,r.jsxs)(r.Fragment,{children:[r.jsx(j.Bu,{title:"Maintenance cost task report",actions:r.jsx(j.zx,{variant:"back",iconLeft:f.Z,onClick:()=>e.push("/reporting"),children:"Back"})}),r.jsx(j.Zb,{className:"mt-8",children:(0,r.jsxs)(j.aY,{className:"flex flex-col gap-4",children:[r.jsx(a.Z,{onChange:({type:e,data:t})=>{switch(e){case"vessels":s(t);break;case"category":u(t);break;case"status":S(t);break;case"dateRange":w(t);break;case"member":y(t)}},onClick:()=>{let e={};null!==D.startDate&&null!==D.endDate&&(e.expires={gte:D.startDate,lte:D.endDate}),t.length>0&&(e.basicComponentID={in:t.map(e=>e.value)}),null!==n&&(e.maintenanceCategoryID={eq:n.value}),null!==N&&(e.status={eq:N.value}),null!==k&&(e.assignedToID={eq:k.value}),L({variables:{filter:e}})}}),r.jsx(l.Z,{onDownloadPdf:()=>{if(0===P.length)return;let e=P.map(e=>[e.taskName,e.inventoryName,e.vesselName,e.assignedTo,e.status,e.dueDate?p()(e.dueDate).format("DD/MM/YYYY"):"",e.projected.toLocaleString(),e.actual.toLocaleString(),e.difference.toLocaleString()]),t=P.reduce((e,t)=>e+t.projected,0),s=P.reduce((e,t)=>e+t.actual,0),r=P.reduce((e,t)=>e+t.difference,0);(0,m.S)({body:e,headers:[[{content:"Task Name"},{content:"Inventory"},{content:"Location"},{content:"Assigned To"},{content:"Status"},{content:"Due Date"},{content:"Projected"},{content:"Actual"},{content:"Difference"}]],footers:[[{colSpan:6,content:"Total"},{content:t.toLocaleString()},{content:s.toLocaleString()},{content:r.toLocaleString()}]],userOptions:{showFoot:"lastPage"}})},onDownloadCsv:()=>{if(0===P.length)return;let e=[["task name","inventory","location","assigned to","status","due date","projected","actual","difference"]];P.forEach(t=>{e.push([t.taskName,t.inventoryName??"N/A",t.vesselName??"N/A",t.assignedTo??"N/A",t.status??"N/A",t.dueDate?p()(t.dueDate).format("DD/MM/YYYY"):"N/A",t.projected.toLocaleString(),t.actual.toLocaleString(),t.difference<0?"":t.difference.toLocaleString()])}),(0,x.u)(e)}}),(0,r.jsxs)(g.iA,{children:[r.jsx(g.xD,{children:r.jsx(g.SC,{children:v.map(e=>r.jsx(g.ss,{children:e},e))})}),r.jsx(b,{isLoading:T&&q,reportData:P})]})]})})]})}function b({reportData:e,isLoading:t}){let s=(0,c.useMemo)(()=>e.reduce((e,t)=>t.projected+e,0),[e]),n=(0,c.useMemo)(()=>e.reduce((e,t)=>t.actual+e,0),[e]);return t?r.jsx(g.RM,{children:r.jsx(g.SC,{children:r.jsx(g.pj,{colSpan:v.length,className:"text-center  h-32",children:"Loading..."})})}):0==e.length?r.jsx(g.RM,{children:r.jsx(g.SC,{children:r.jsx(g.pj,{colSpan:v.length,className:"text-center  h-32",children:"No Data Available"})})}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(g.RM,{children:e.map(function(e,t){return(0,r.jsxs)(g.SC,{className:"group border-b hover:",children:[r.jsx(g.pj,{className:"px-2 py-3 text-left w-[15%]",children:r.jsx("div",{className:" inline-block",children:e.taskName})}),r.jsx(g.pj,{className:"px-2 py-3 text-left  w-[15%]",children:r.jsx("div",{className:" inline-block",children:e.inventoryName})}),r.jsx(g.pj,{className:"px-2 py-3 text-left w-[15%]",children:r.jsx("div",{className:" inline-block",children:e.vesselName})}),r.jsx(g.pj,{className:"px-2 py-3 text-left w-[15%]",children:r.jsx("div",{className:" inline-block",children:e.assignedTo})}),r.jsx(g.pj,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:" inline-block",children:e.status})}),r.jsx(g.pj,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:" inline-block",children:e.dueDate?p()(e.dueDate).format("DD/MM/YY"):""})}),r.jsx(g.pj,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:" inline-block",children:e.projected.toLocaleString()})}),r.jsx(g.pj,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:" inline-block",children:e.actual.toLocaleString()})}),r.jsx(g.pj,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:" inline-block",children:e.difference.toLocaleString()})})]},`report-item-${t}`)})}),r.jsx(g.yt,{children:(0,r.jsxs)(g.SC,{className:"group border-b  ",children:[r.jsx(g.ss,{className:"px-2 py-3 text-left",scope:"col",colSpan:6,children:r.jsx("div",{className:"inline-block ml-3",children:"Total"})}),r.jsx(g.ss,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:"inline-block ",children:s.toLocaleString()})}),r.jsx(g.ss,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:"inline-block ",children:n.toLocaleString()})}),r.jsx(g.ss,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:"inline-block ",children:(s-n).toLocaleString()})})]})})]})}function S(){let[e,t]=(0,c.useState)(!1),[s,a]=(0,c.useState)(!1);return e&&s?r.jsx(N,{}):e?r.jsx(n.Z,{errorMessage:"Oops You do not have the permission to view this section."}):r.jsx(n.Z,{})}},49517:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var r=s(98768),n=s(39544),a=s(76915);function o({onDownloadCsv:e,onDownloadPdf:t}){return(0,r.jsxs)("div",{className:"flex gap-3 mb-3",children:[e&&r.jsx(n.Button,{iconLeft:a.Z,type:"button",onClick:e,children:"Download CSV"}),t&&r.jsx(n.Button,{iconLeft:a.Z,type:"button",onClick:t,children:"Download PDF"})]})}},71238:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)},60977:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\maintenance-cost-track\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,9707,6250,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042],()=>s(29673));module.exports=r})();