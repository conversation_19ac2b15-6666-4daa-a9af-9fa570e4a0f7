(()=>{var e={};e.id=406,e.ids=[406],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},52505:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>f,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>o}),r(54578),r(83569),r(78398),r(57757),r(48045);var n=r(40060),s=r(33581),a=r(57567),l=r.n(a),i=r(51650),u={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>i[e]);r.d(t,u);let o=["",{children:["log-entries",{children:["customise-log-books",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,54578)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\log-entries\\customise-log-books\\edit\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,83569)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\log-entries\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\log-entries\\customise-log-books\\edit\\page.tsx"],d="/log-entries/customise-log-books/edit/page",f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/log-entries/customise-log-books/edit/page",pathname:"/log-entries/customise-log-books/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63166:(e,t,r)=>{Promise.resolve().then(r.bind(r,75649))},61707:(e,t,r)=>{Promise.resolve().then(r.bind(r,23745))},83179:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",r="minute",n="hour",s="week",a="month",l="quarter",i="year",u="date",o="Invalid Date",c=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,d=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,f=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},p="en",h={};h[p]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||"th")+"]"}};var b="$isDayjsObject",m=function(e){return e instanceof x||!(!e||!e[b])},v=function e(t,r,n){var s;if(!t)return p;if("string"==typeof t){var a=t.toLowerCase();h[a]&&(s=a),r&&(h[a]=r,s=a);var l=t.split("-");if(!s&&l.length>1)return e(l[0])}else{var i=t.name;h[i]=t,s=i}return!n&&s&&(p=s),s||!n&&p},g=function(e,t){if(m(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new x(r)},y={s:f,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+f(Math.floor(r/60),2,"0")+":"+f(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),s=t.clone().add(n,a),l=r-s<0,i=t.clone().add(n+(l?-1:1),a);return+(-(n+(r-s)/(l?s-i:i-s))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(o){return({M:a,y:i,w:s,d:"day",D:u,h:n,m:r,s:t,ms:e,Q:l})[o]||String(o||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};y.l=v,y.i=m,y.w=function(e,t){return g(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var x=function(){function f(e){this.$L=v(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[b]=!0}var p=f.prototype;return p.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(y.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(c);if(n){var s=n[2]-1||0,a=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,a)):new Date(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,a)}}return new Date(t)}(e),this.init()},p.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},p.$utils=function(){return y},p.isValid=function(){return this.$d.toString()!==o},p.isSame=function(e,t){var r=g(e);return this.startOf(t)<=r&&r<=this.endOf(t)},p.isAfter=function(e,t){return g(e)<this.startOf(t)},p.isBefore=function(e,t){return this.endOf(t)<g(e)},p.$g=function(e,t,r){return y.u(e)?this[t]:this.set(r,e)},p.unix=function(){return Math.floor(this.valueOf()/1e3)},p.valueOf=function(){return this.$d.getTime()},p.startOf=function(e,l){var o=this,c=!!y.u(l)||l,d=y.p(e),f=function(e,t){var r=y.w(o.$u?Date.UTC(o.$y,t,e):new Date(o.$y,t,e),o);return c?r:r.endOf("day")},p=function(e,t){return y.w(o.toDate()[e].apply(o.toDate("s"),(c?[0,0,0,0]:[23,59,59,999]).slice(t)),o)},h=this.$W,b=this.$M,m=this.$D,v="set"+(this.$u?"UTC":"");switch(d){case i:return c?f(1,0):f(31,11);case a:return c?f(1,b):f(0,b+1);case s:var g=this.$locale().weekStart||0,x=(h<g?h+7:h)-g;return f(c?m-x:m+(6-x),b);case"day":case u:return p(v+"Hours",0);case n:return p(v+"Minutes",1);case r:return p(v+"Seconds",2);case t:return p(v+"Milliseconds",3);default:return this.clone()}},p.endOf=function(e){return this.startOf(e,!1)},p.$set=function(s,l){var o,c=y.p(s),d="set"+(this.$u?"UTC":""),f=((o={}).day=d+"Date",o[u]=d+"Date",o[a]=d+"Month",o[i]=d+"FullYear",o[n]=d+"Hours",o[r]=d+"Minutes",o[t]=d+"Seconds",o[e]=d+"Milliseconds",o)[c],p="day"===c?this.$D+(l-this.$W):l;if(c===a||c===i){var h=this.clone().set(u,1);h.$d[f](p),h.init(),this.$d=h.set(u,Math.min(this.$D,h.daysInMonth())).$d}else f&&this.$d[f](p);return this.init(),this},p.set=function(e,t){return this.clone().$set(e,t)},p.get=function(e){return this[y.p(e)]()},p.add=function(e,l){var u,o=this;e=Number(e);var c=y.p(l),d=function(t){var r=g(o);return y.w(r.date(r.date()+Math.round(t*e)),o)};if(c===a)return this.set(a,this.$M+e);if(c===i)return this.set(i,this.$y+e);if("day"===c)return d(1);if(c===s)return d(7);var f=((u={})[r]=6e4,u[n]=36e5,u[t]=1e3,u)[c]||1,p=this.$d.getTime()+e*f;return y.w(p,this)},p.subtract=function(e,t){return this.add(-1*e,t)},p.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||o;var n=e||"YYYY-MM-DDTHH:mm:ssZ",s=y.z(this),a=this.$H,l=this.$m,i=this.$M,u=r.weekdays,c=r.months,f=r.meridiem,p=function(e,r,s,a){return e&&(e[r]||e(t,n))||s[r].slice(0,a)},h=function(e){return y.s(a%12||12,e,"0")},b=f||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n};return n.replace(d,function(e,n){return n||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return y.s(t.$y,4,"0");case"M":return i+1;case"MM":return y.s(i+1,2,"0");case"MMM":return p(r.monthsShort,i,c,3);case"MMMM":return p(c,i);case"D":return t.$D;case"DD":return y.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return p(r.weekdaysMin,t.$W,u,2);case"ddd":return p(r.weekdaysShort,t.$W,u,3);case"dddd":return u[t.$W];case"H":return String(a);case"HH":return y.s(a,2,"0");case"h":return h(1);case"hh":return h(2);case"a":return b(a,l,!0);case"A":return b(a,l,!1);case"m":return String(l);case"mm":return y.s(l,2,"0");case"s":return String(t.$s);case"ss":return y.s(t.$s,2,"0");case"SSS":return y.s(t.$ms,3,"0");case"Z":return s}return null}(e)||s.replace(":","")})},p.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},p.diff=function(e,u,o){var c,d=this,f=y.p(u),p=g(e),h=(p.utcOffset()-this.utcOffset())*6e4,b=this-p,m=function(){return y.m(d,p)};switch(f){case i:c=m()/12;break;case a:c=m();break;case l:c=m()/3;break;case s:c=(b-h)/6048e5;break;case"day":c=(b-h)/864e5;break;case n:c=b/36e5;break;case r:c=b/6e4;break;case t:c=b/1e3;break;default:c=b}return o?c:y.a(c)},p.daysInMonth=function(){return this.endOf(a).$D},p.$locale=function(){return h[this.$L]},p.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=v(e,t,!0);return n&&(r.$L=n),r},p.clone=function(){return y.w(this.$d,this)},p.toDate=function(){return new Date(this.valueOf())},p.toJSON=function(){return this.isValid()?this.toISOString():null},p.toISOString=function(){return this.$d.toISOString()},p.toString=function(){return this.$d.toUTCString()},f}(),$=x.prototype;return g.prototype=$,[["$ms",e],["$s",t],["$m",r],["$H",n],["$W","day"],["$M",a],["$y",i],["$D",u]].forEach(function(e){$[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),g.extend=function(e,t){return e.$i||(e(t,x,g),e.$i=!0),g},g.locale=v,g.isDayjs=m,g.unix=function(e){return g(1e3*e)},g.en=h[p],g.Ls=h,g.p={},g},e.exports=t()},18479:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,s=Array(n);++r<n;)s[r]=t(e[r],r,e);return s}},22060:(e,t,r)=>{var n=r(51858),s=r(18479),a=r(55813),l=r(15903),i=1/0,u=n?n.prototype:void 0,o=u?u.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(a(t))return s(t,e)+"";if(l(t))return o?o.call(t):"";var r=t+"";return"0"==r&&1/t==-i?"-0":r}},15903:(e,t,r)=>{var n=r(55296),s=r(48377);e.exports=function(e){return"symbol"==typeof e||s(e)&&"[object Symbol]"==n(e)}},16266:(e,t,r)=>{var n=r(22060);e.exports=function(e){return null==e?"":n(e)}},3233:(e,t,r)=>{var n=r(16266),s=0;e.exports=function(e){var t=++s;return n(e)+t}},75649:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(98768),s=r(25394),a=r(81524),l=r(60343);function i(){let e=[{label:"Main Engine",value:"1"},{label:"Auxiliary Engine",value:"2"},{label:"Generator",value:"3"}];return n.jsx("div",{className:"w-full",children:n.jsx("div",{className:"relative overflow-x-auto shadow-md sm:rounded-lg",children:n.jsx("div",{className:"grid grid-cols-1 px-4 pb-4 items-center  ",children:[{label:"Fuel records",type:"fields",fields:[{label:"Entered by",value:!0,type:"text"},{label:"Fuel start",value:!0,type:"number"},{label:"Fuel end",value:!0,type:"number"},{label:"Fuel type",value:!0,type:"text"},{label:"Nautical miles",value:!1,type:"number"}],value:!0},{label:"Engine fluid records",type:"fields",fields:[{label:"Gearbox oil",value:!0,type:"number"},{label:"Steering fluid",value:!0,type:"number"},{label:"Hydraulic fluid",value:!0,type:"number"},{label:"Hynautics",value:!0,type:"number"},{label:"Thrust bearing oil",value:!0,type:"number"},{label:"Nozzle power steering fluid",value:!0,type:"number"},{label:"Compressor oil",value:!0,type:"number"},{label:"Oil - other",value:!0,type:"number"}],value:!0},{label:"Sewage disposal records",type:"fields",fields:[{label:"Discharge facility",value:!0,type:"text"},{label:"Position",value:!0,type:"text"},{label:"Volume",value:!0,type:"number"},{label:"Sewage",value:!0,type:"text"},{label:"Name",value:!0,type:"text"}],value:!0},{label:"Total running hours",type:"number",value:!0},{label:"Nautical miles",type:"number",value:!1},{label:"Fuel added",type:"number",value:!0},{label:"Fuel temp",type:"number",value:!0},{label:"Fuel pressure",type:"number",value:!0},{label:"Fuel diff pressure",type:"number",value:!0},{label:"Fuel rate",type:"number",value:!0},{label:"Fuel day tank level",type:"number",value:!0},{label:"Header tank level",type:"number",value:!0},{label:"RPM",type:"number",value:!0},{label:"Boost",type:"number",value:!0},{label:"Manifold temp",type:"number",value:!0},{label:"Generator temp",type:"number",value:!0},{label:"Coolant temp",type:"number",value:!0},{label:"Coolant level OK",type:"bool",value:!0},{label:"Thrust bearing temp",type:"number",value:!0},{label:"Shaft bearing temp",type:"number",value:!0},{label:"Oil level OK",type:"bool",value:!0},{label:"Oil pressure",type:"number",value:!0},{label:"Lube oil OK",type:"bool",value:!0},{label:"Lube oil temp",type:"number",value:!0},{label:"Lube oil pressure",type:"number",value:!0},{label:"Volts",type:"number",value:!0},{label:"Kilo Watt load",type:"number",value:!0},{label:"Overboard pressure",type:"number",value:!0},{label:"Overboard discharge",type:"number",value:!0}].map(t=>(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 lg:grid-cols-4 border-b py-2",children:[n.jsx("div",{className:"flex flex-col gap-3 flex-wrap pt-2",children:n.jsx(s.H2,{className:"",children:t.label})}),(0,n.jsxs)("div",{className:"col-span-1 md:col-span-3 lg:col-span-3",children:[n.jsx("div",{className:"flex",children:n.jsx(a.Combobox,{multi:!0,defaultValues:e,options:e,onChange:()=>{},placeholder:"Select Engines"})}),"fields"===t.type&&n.jsx("div",{className:"flex flex-col gap-1 py-1  ",children:"fields"===t.type&&n.jsx(n.Fragment,{children:t.fields?.map(t=>n.jsxs("div",{className:"flex items-center    p-1",children:[n.jsx(s.H3,{className:"w-64",children:t.label}),n.jsx("div",{className:"flex flex-col w-full grid-cols-1 md:col-span-2 lg:col-span-3",children:n.jsx(a.Combobox,{multi:!0,options:e,onChange:()=>{},placeholder:"Select Engines"})})]},t.label))})})]})]},t.label))})})})}var u=r(74602),o=r(35024),c=r(36895);function d(){let[e,t]=(0,l.useState)("tripLog");return n.jsx("div",{className:"w-full",children:(0,n.jsxs)(o.Zb,{children:[n.jsx(o.Ol,{children:n.jsx(u.H3,{children:"Logbook Configuration"})}),n.jsx(o.aY,{children:(0,n.jsxs)(c.Tabs,{value:e,onValueChange:t,children:[(0,n.jsxs)(c.TabsList,{children:[n.jsx(c.TabsTrigger,{value:"tripLog",children:"Logbook"}),n.jsx(c.TabsTrigger,{value:"crew",children:"Crew"}),n.jsx(c.TabsTrigger,{value:"crewTraining",children:"Training / Drills"})]}),n.jsx(c.TabsContent,{value:"engineConf",children:n.jsx(i,{})}),n.jsx(c.TabsContent,{value:"tripLog"}),n.jsx(c.TabsContent,{value:"crew"}),n.jsx(c.TabsContent,{value:"crewTraining"})]})})]})})}},23745:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(98768);r(60343);var s=r(64837);function a({children:e}){return n.jsx(s.Z,{children:e})}},54578:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\log-entries\customise-log-books\edit\page.tsx#default`)},83569:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\log-entries\layout.tsx#default`)},84961:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},46020:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},23379:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},7671:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[864,8865,3563,6263,8189,9507,7602,6451,4234,2925,5394,4837],()=>r(52505));module.exports=n})();