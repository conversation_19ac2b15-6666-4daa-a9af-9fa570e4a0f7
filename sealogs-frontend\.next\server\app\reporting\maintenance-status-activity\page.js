(()=>{var e={};e.id=3029,e.ids=[3029],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},54718:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>x,tree:()=>c}),s(56339),s(71238),s(78398),s(57757),s(48045);var a=s(40060),r=s(33581),n=s(57567),i=s.n(n),o=s(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["reporting",{children:["maintenance-status-activity",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,56339)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\maintenance-status-activity\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\maintenance-status-activity\\page.tsx"],d="/reporting/maintenance-status-activity/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/reporting/maintenance-status-activity/page",pathname:"/reporting/maintenance-status-activity",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88332:(e,t,s)=>{Promise.resolve().then(s.bind(s,44517))},35576:(e,t,s)=>{Promise.resolve().then(s.bind(s,93368))},91973:(e,t,s)=>{"use strict";s.d(t,{u:()=>a});let a=(e,t="report.csv")=>{let s=new Blob([e.map(e=>e.join(",")).join("\n")],{type:"text/csv;charset=utf-8,"}),a=URL.createObjectURL(s);Object.assign(document.createElement("a"),{href:a,download:t}).click(),URL.revokeObjectURL(a)}},2604:(e,t,s)=>{"use strict";s.d(t,{S:()=>i});var a=s(9707),r=s(58774),n=s.n(r);function i(e,t){let s=new a.default(t);n()(s,{head:e.headers,body:e.body,foot:e.footers,margin:5,...e.userOptions}),s.save(e.fileName||"report.pdf")}},44517:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(98768);s(60343);var r=s(32993),n=s(64837);function i({children:e}){return a.jsx(r.default,{children:a.jsx(n.Z,{children:e})})}},93368:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var a=s(98768);s(46776);var r=s(26100),n=s(28271),i=s(60343);function o(){let[e,t]=(0,i.useState)(!1),[s,o]=(0,i.useState)(!1);return e&&s?a.jsx(n.Z,{}):e?a.jsx(r.Z,{errorMessage:"Oops You do not have the permission to view this section."}):a.jsx(r.Z,{})}},49517:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var a=s(98768),r=s(39544),n=s(76915);function i({onDownloadCsv:e,onDownloadPdf:t}){return(0,a.jsxs)("div",{className:"flex gap-3 mb-3",children:[e&&a.jsx(r.Button,{iconLeft:n.Z,type:"button",onClick:e,children:"Download CSV"}),t&&a.jsx(r.Button,{iconLeft:n.Z,type:"button",onClick:t,children:"Download PDF"})]})}},28271:(e,t,s)=>{"use strict";s.d(t,{N:()=>D,Z:()=>y});var a=s(98768),r=s(37042),n=s(60343),i=s(49517),o=s(63043),l=s(79418),c=s(13842),u=s(83179),d=s.n(u),p=s(7678),x=s.n(p),m=s(91973),h=s(2604),g=s(69424),v=s(26659),f=s(17203),j=s(25394);let N=["Task Name","Inventory","Location","Assigned To","Status","Due Date","Due Status"];function y(){let e=(0,g.useRouter)(),[t,s]=(0,n.useState)([]),[u,p]=(0,n.useState)(null),[x,y]=(0,n.useState)(null),[S,k]=(0,n.useState)(null),[C,q]=(0,n.useState)({startDate:new Date,endDate:new Date}),[w,{called:T,loading:M,data:P}]=(0,l.t)(o.p,{fetchPolicy:"cache-and-network",onError:e=>{console.error("GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error",e)}}),_=(0,n.useMemo)(()=>{let e=P?.readComponentMaintenanceChecks.nodes??[];if(0===e.length)return[];let t=[];return e.forEach(e=>{let s={taskName:e.name,vesselName:e.basicComponent.title,assignedTo:0==e.assignedTo.id?void 0:`${e.assignedTo.firstName} ${e.assignedTo.surname}`,inventoryName:e.inventory.title,dueDate:e.expires?new Date(e.expires):void 0,status:e.status,dueStatus:(0,c.AT)(e)};t.push(s)}),t},[T,M,P]);return(0,a.jsxs)(a.Fragment,{children:[a.jsx(j.Bu,{title:"Maintenance status and activity report",actions:a.jsx(j.zx,{variant:"back",iconLeft:f.Z,onClick:()=>e.push("/reporting"),children:"Back"})}),a.jsx(j.Zb,{className:"mt-8",children:(0,a.jsxs)(j.aY,{className:"flex flex-col gap-4",children:[a.jsx(r.Z,{onChange:({type:e,data:t})=>{switch(e){case"vessels":s(t);break;case"category":p(t);break;case"status":y(t);break;case"dateRange":q(t);break;case"member":k(t)}},onClick:()=>{let e={};null!==C.startDate&&null!==C.endDate&&(e.expires={gte:C.startDate,lte:C.endDate}),t.length>0&&(e.basicComponentID={in:t.map(e=>e.value)}),null!==u&&(e.maintenanceCategoryID={eq:u.value}),null!==x&&(e.status={eq:x.value}),null!==S&&(e.assignedToID={eq:S.value}),w({variables:{filter:e}})}}),a.jsx(i.Z,{onDownloadPdf:()=>{if(0===_.length)return;let e=_.map(e=>[e.taskName,e.inventoryName??"",e.vesselName??"",e.assignedTo??"",e.status??"",e.dueDate?d()(e.dueDate).format("DD/MM/YYYY"):"",D(e.dueStatus)]);(0,h.S)({body:e,headers:[["Task Name","Inventory","Location","Assigned To","Status","Due Date","Due Status"]]})},onDownloadCsv:()=>{if(0===_.length)return;let e=[["task name","inventory","location","assigned to","status","due date","due status"]];_.forEach(t=>{e.push([t.taskName,t.inventoryName??"N/A",t.vesselName??"N/A",t.assignedTo??"N/A",t.status??"N/A",t.dueDate?d()(t.dueDate).format("DD/MM/YYYY"):"N/A",D(t.dueStatus)])}),(0,m.u)(e)}}),(0,a.jsxs)(v.iA,{children:[a.jsx(v.xD,{children:a.jsx(v.SC,{children:N.map(e=>a.jsx(v.ss,{children:e},e))})}),a.jsx(b,{isLoading:T&&M,reportData:_})]})]})})]})}function b({reportData:e,isLoading:t}){return t?a.jsx(v.RM,{children:a.jsx(v.SC,{children:a.jsx(v.pj,{colSpan:N.length,className:"text-center  h-32",children:"Loading..."})})}):0==e.length?a.jsx(v.RM,{children:a.jsx(v.SC,{children:a.jsx(v.pj,{colSpan:N.length,className:"text-center  h-32",children:"No Data Available"})})}):a.jsx(v.RM,{children:e.map((e,t)=>(0,a.jsxs)(v.SC,{className:"group border-b  hover: ",children:[a.jsx(v.pj,{className:"px-2 py-3 text-left w-[15%]",children:a.jsx("div",{className:" inline-block ml-3",children:e.taskName})}),a.jsx(v.pj,{className:"px-2 py-3 text-left w-[10%]",children:a.jsx("div",{className:" inline-block ",children:e.inventoryName})}),a.jsx(v.pj,{className:"px-2 py-3 text-left w-[10%]",children:a.jsx("div",{className:" inline-block ",children:e.vesselName})}),a.jsx(v.pj,{className:"px-2 py-3 text-left w-[10%]",children:a.jsx("div",{className:" inline-block ",children:e.assignedTo})}),a.jsx(v.pj,{className:"px-2 py-3 text-left w-[10%]",children:a.jsx("div",{className:" inline-block ",children:e.status})}),a.jsx(v.pj,{className:"px-2 py-3 text-left w-[10%]",children:a.jsx("div",{className:" inline-block ",children:e.dueDate?d()(e.dueDate).format("DD/MM/YY"):""})}),a.jsx(v.pj,{className:"px-2 py-3 text-left w-[10%]",children:a.jsx("div",{className:" inline-block ",children:D(e.dueStatus)})})]},`report-item-${t}`))})}let D=e=>`${e?.status&&["High","Medium","Low"].includes(e.status)?e?.days:""}${e?.status==="Completed"&&e?.days==="Save As Draft"?e?.days:""}${e?.status==="Upcoming"?e?.days:""}${e?.status==="Completed"&&x()(e?.days)?e?.status:""}${e?.status!=="Completed"||x()(e?.days)||e?.days==="Save As Draft"?"":e?.days}`},71238:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)},56339:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\maintenance-status-activity\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,9707,6250,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042],()=>s(54718));module.exports=a})();