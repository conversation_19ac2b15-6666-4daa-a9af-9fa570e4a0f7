(()=>{var e={};e.id=5547,e.ids=[5547],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},39501:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,originalPathname:()=>p,pages:()=>u,routeModule:()=>g,tree:()=>d}),r(74804),r(71238),r(78398),r(57757),r(48045);var s=r(40060),n=r(33581),a=r(57567),i=r.n(a),o=r(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["reporting",{children:["fuel-summary-report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74804)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\fuel-summary-report\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\fuel-summary-report\\page.tsx"],p="/reporting/fuel-summary-report/page",c={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/reporting/fuel-summary-report/page",pathname:"/reporting/fuel-summary-report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},83613:(e,t,r)=>{Promise.resolve().then(r.bind(r,70453))},88332:(e,t,r)=>{Promise.resolve().then(r.bind(r,44517))},75546:(e,t,r)=>{"use strict";r.d(t,{Br:()=>c,fU:()=>g,o0:()=>u,p6:()=>d,vq:()=>p});var s=r(83179),n=r.n(s),a=r(7678),i=r.n(a),o=r(14826),l=r.n(o);let d=(e="",t=!0)=>{let r;if(i()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,s,n]=e.split("-"),a=t?r.slice(-2):r,i=parseInt(n,10).toString().padStart(2,"0"),o=parseInt(s,10).toString().padStart(2,"0");return`${i}/${o}/${a}`}if(!(r=e&&"object"==typeof e?n()(e.toString()):n()(e)).isValid())return"";let s=r.format("DD"),a=r.format("MM"),o=t?r.format("YY"):r.format("YYYY");return`${s}/${a}/${o}`},u=(e="",t=!0)=>{let r;if(i()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,s,n]=e.split("-"),a=t?r.slice(-2):r,i=parseInt(n,10).toString().padStart(2,"0"),o=parseInt(s,10).toString().padStart(2,"0");return`${i}/${o}/${a} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[r,s]=e.split(" "),[n,a,i]=r.split("-"),o=t?n.slice(-2):n,l=s.split(":"),d=l[0].padStart(2,"0"),u=l[1].padStart(2,"0"),p=parseInt(i,10).toString().padStart(2,"0"),c=parseInt(a,10).toString().padStart(2,"0");return`${p}/${c}/${o} ${d}:${u}`}if(!(r=e&&"object"==typeof e?n()(e.toString()):n()(e)).isValid())return"";let s=r.format("DD"),a=r.format("MM"),o=t?r.format("YY"):r.format("YYYY"),d=r.format("HH:mm");return`${s}/${a}/${o} ${d}`},p=(e="")=>i()(l()(e))?"":n()(e).format("YYYY-MM-DD HH:mm:ss"),c=(e="")=>i()(l()(e))?new Date:new Date(`${e}T10:00:00Z`),g=(e,t)=>{let r=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),s=e=>e.includes(" ")?e.replace(" ","T"):e,n=e=>{if(!e||"string"!=typeof e)return null;if(r(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(s(e))},a=n(e),i=n(t);return!a||!i||isNaN(a.getTime())||isNaN(i.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):i>a}},70453:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(98768),n=r(37042),a=r(75546),i=r(94060),o=r(79418),l=r(83179),d=r.n(l),u=r(69424),p=r(60343),c=r(89546),g=r(25394);let f=["Date","Fuel Tank","Fuel used"];function x(){let e=(0,u.useRouter)(),[t,r]=(0,p.useState)(""),[l,x]=(0,p.useState)(!1),[m,h]=(0,p.useState)([]),[y]=(0,o.t)(i.Js,{onCompleted:e=>{var t=[];e.readOneVessel.logBookEntries.nodes?.map(e=>e.logBookEntrySections.nodes?.map(e=>{t.push(e.id)})),v({variables:{id:t}})},onError:e=>{console.error(e)}}),[v]=(0,o.t)(i.Zl,{onCompleted:e=>{let t=e.readTripReport_LogBookEntrySections.nodes;var r=[];t.forEach(e=>{var t=[];e.tripEvents.nodes.forEach(r=>{"PassengerDropFacility"===r.eventCategory&&r.eventType_PassengerDropFacility.fuelLog.nodes.length>0&&t.push({id:e.id,fuelLog:r.eventType_PassengerDropFacility.fuelLog.nodes,created:e.created,category:r.eventCategory}),"Tasking"===r.eventCategory&&r.eventType_Tasking.fuelLog.nodes.length>0&&t.push({id:e.id,fuelLog:r.eventType_Tasking.fuelLog.nodes,created:e.created,category:r.eventCategory}),"RefuellingBunkering"===r.eventCategory&&r.eventType_RefuellingBunkering.fuelLog.nodes.length>0&&t.push({id:e.id,fuelLog:r.eventType_RefuellingBunkering.fuelLog.nodes,created:e.created,category:r.eventCategory})}),t.length>0&&r.push({id:e.id,fuelLog:t,created:e.created})}),h(r)},onError:e=>{console.error("TripReport_LogBookEntrySection error",e)}}),j=e=>!l||d()(e.created).isAfter(d()(l.startDate))&&d()(e.created).isBefore(d()(l.endDate)),S=(e,t)=>e.fuelLog[0].fuelLog.filter(e=>t===e.fuelTank.title)[0].fuelBefore,L=(e,t)=>e.fuelLog.reduce((e,r)=>e+r.fuelLog.filter(e=>e.fuelTank.title===t).reduce((e,t)=>e+t.fuelAdded,0),0),$=(e,t)=>e.fuelLog[e.fuelLog.length-1].fuelLog.filter(e=>t===e.fuelTank.title)[0].fuelAfter,T=(e,t)=>S(e,t)===$(e,t)&&0===L(e,t),b=e=>s.jsx(c.Z,{headClasses:"  ",headings:["","Start","Added","End"],children:Array.from(new Set(e.fuelLog.flatMap(t=>t.fuelLog.filter(t=>!T(e,t.fuelTank.title)).map(e=>e.fuelTank.title)))).map(t=>(0,s.jsxs)("tr",{children:[s.jsx("td",{className:"text-left pl-4 py-2",children:t}),s.jsx("td",{children:S(e,t)}),s.jsx("td",{children:L(e,t)}),s.jsx("td",{children:$(e,t)})]},t))}),q=e=>{let t=e.fuelLog[0].fuelLog.reduce((e,t)=>e+t.fuelBefore,0),r=e.fuelLog[e.fuelLog.length-1].fuelLog.reduce((e,t)=>e+t.fuelAfter,0);return+t+ +e.fuelLog.reduce((e,t)=>e+t.fuelLog.reduce((e,t)=>e+t.fuelAdded,0),0)-+r};return(0,s.jsxs)(s.Fragment,{children:[s.jsx(g.Bu,{title:"Fuel Summary Report",actions:s.jsx("div",{className:"flex gap-2.5",children:s.jsx(g.zx,{variant:"back",onClick:()=>e.push("/reporting"),children:"Back"})})}),s.jsx(g.Zb,{className:"mt-8",children:(0,s.jsxs)(g.aY,{className:"flex flex-col gap-4",children:[s.jsx(n.Z,{onChange:e=>{"vessel"===e.type&&e.data?.value&&r(e.data?.value),"dateRange"===e.type&&x(e.data)}}),(0,s.jsxs)(g.iA,{children:[s.jsx(g.xD,{children:s.jsx(g.SC,{children:f.map(e=>s.jsx(g.ss,{children:e},e))})}),s.jsx(g.RM,{children:0==m.length?s.jsx(g.SC,{children:s.jsx(g.pj,{colSpan:f.length,className:"text-center h-32",children:"No Data Available"})}):m.filter(e=>j(e)).map(e=>(0,s.jsxs)(g.SC,{children:[s.jsx(g.pj,{children:(0,a.p6)(e.created)}),s.jsx(g.pj,{children:b(e)}),s.jsx(g.pj,{children:q(e)})]},e.id))})]})]})})]})}r(46776);var m=r(26100);function h(){let[e,t]=(0,p.useState)(!1),[r,n]=(0,p.useState)(!1);return e&&r?s.jsx(x,{}):e?s.jsx(m.Z,{errorMessage:"Oops You do not have the permission to view this section."}):s.jsx(m.Z,{})}},44517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(98768);r(60343);var n=r(32993),a=r(64837);function i({children:e}){return s.jsx(n.default,{children:s.jsx(a.Z,{children:e})})}},89546:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(98768);let n=e=>(0,s.jsxs)("table",{className:" table-auto w-full ",cellPadding:"10",children:[s.jsx("thead",{children:s.jsx("tr",{className:e.showHeader?"":"hidden",children:e.headings.map((t,r)=>s.jsx("th",{scope:"col",className:`pb-3 pt-6 px-2 ${0===r?"rounded-tl-lg":" "}   ${e.headings.length===r+1?"rounded-tr-lg":" "}
                                ${t.includes(":")&&"last"===t.split(":")[1]?"rounded-tr-lg":""}
                                ${t.includes(":")&&"smhidden"===t.split(":")[1]?"hidden sm:block":""}
                                ${t.includes(":")&&"left"===t.split(":")[1]?"text-left":""}
                                ${t.includes(":")&&"firstHead"===t.split(":")[1]?"text-left text-nowrap font-thin  md: lg:text-2xl pl-6 rounded-tl-lg":""}  `,children:t.includes(":")?t.split(":")[0]:t},r))})}),s.jsx("tbody",{className:`  text-foreground ${e?.bodyClass}`,children:e.children})]})},74804:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\fuel-summary-report\page.tsx#default`)},71238:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042],()=>r(39501));module.exports=s})();