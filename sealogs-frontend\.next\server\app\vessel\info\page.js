(()=>{var e={};e.id=3715,e.ids=[3715],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},80331:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>o}),t(78753),t(42832),t(78398),t(57757),t(48045);var l=t(40060),a=t(33581),r=t(57567),i=t.n(r),n=t(51650),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let o=["",{children:["vessel",{children:["info",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78753)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\vessel\\info\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,42832)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\vessel\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\vessel\\info\\page.tsx"],h="/vessel/info/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new l.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/vessel/info/page",pathname:"/vessel/info",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19111:(e,s,t)=>{Promise.resolve().then(t.bind(t,4498))},897:(e,s,t)=>{Promise.resolve().then(t.bind(t,57710))},28288:e=>{e.exports=function(e){return null==e}},13386:(e,s,t)=>{"use strict";t.d(s,{x:()=>I,default:()=>T});var l=t(98768),a=t(60343),r=t(94060),i=t(79418),n=t(72548),d=t(66263),o=t(76342),c=t(13842),h=t(7678),u=t.n(h),m=t(69424),x=t(22995),p=t(39544),g=t(51742),f=t(30905),v=t(25394),j=t(15580),y=t(81311),b=t(53294),N=t(81257),k=t(27514),w=t(9210);let C=({onChange:e})=>{let{isMobile:s}=(0,x.Ap)(),[t,r]=(0,a.useState)(!0),i=()=>{r(s=>{let t=!s;return e(t),t})};return(0,l.jsxs)(k.DropdownMenu,{children:[l.jsx(k.DropdownMenuTrigger,{asChild:!0,children:l.jsx(w.HV,{size:36})}),(0,l.jsxs)(k.DropdownMenuContent,{side:s?"bottom":"right",align:s?"end":"start",children:[l.jsx(k.DropdownMenuItem,{onClick:()=>i(),children:t?"Archived Crews":"Active Crews"}),l.jsx(k.DropdownMenuSeparator,{}),l.jsx(d.default,{href:"/user/create",children:l.jsx(k.DropdownMenuItem,{children:"Add Crew"})})]})]})};var D=t(39650),S=t(99891),M=t(52241);function T(e){(0,m.useRouter)();let[s,t]=(0,a.useState)([]),[h,p]=(0,a.useState)([]),{vesselIconData:j,getVesselWithIcon:b}=(0,M.P)(),[k,w]=(0,a.useState)(!0),{isMobile:T}=(0,x.Ap)(),[I,E]=(0,a.useState)(!0),[Z,A]=(0,a.useState)([]),[O,$]=(0,a.useState)([]),[R,P]=(0,a.useState)({totalCount:0,hasNextPage:!1,hasPreviousPage:!1}),[L,W]=(0,a.useState)(0),[B,F]=(0,a.useState)({isArchived:{eq:!1}}),[_,V]=(0,a.useState)(null);(0,c.Fo)(e=>{$(e.filter(e=>k?!e.archived:e.archived).map(e=>({label:e.title,value:e.id})))});let q=(e,s=0,t=0)=>e.filter(e=>+e.parentID===s).flatMap(s=>{let l=q(e,+s.id,t+1);return[{...s,level:t},...l]}),[H,{loading:z}]=(0,i.t)(r.d5,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readDepartments.nodes;s&&A(q(s))},onError:e=>{console.error("queryCrewMembers error",e)}});(0,c.lY)(e=>{p(e.map(e=>({label:e.title,value:e.id})))});let[K]=(0,i.t)(r.qJ,{fetchPolicy:"cache-and-network",onCompleted:e=>(U(e.readSeaLogsMembers.nodes),P(e.readSeaLogsMembers.pageInfo),e.readSeaLogsMembers.nodes),onError:e=>{console.error("queryCrewMembers error",e)}}),U=e=>{t((0,c.Vp)(e,h))},G=((e,s)=>{let t=[...e].map(e=>{let s=e.trainingStatus.dues;return 0===s.length?{...e,trainingStatus:{label:"Good",dues:[]}}:s.some(e=>e.status.isOverdue)?{...e,trainingStatus:{label:"Overdue",dues:s.filter(e=>e.status.isOverdue)}}:{...e,trainingStatus:{label:" ",dues:s.filter(e=>!e.status.isOverdue)}}});return s?t.filter(e=>{let t=e.trainingStatus?.label,l=e.trainingStatus?.dues||[];return"Good"===s?"Good"===t:"Overdue"===s?"Overdue"===t:"Due Soon"!==s||" "===t&&l.length>0}):t})(s,_),Y=async(e=0,s={...B})=>{await K({variables:{limit:100,offset:100*e,filter:s}})},[Q]=(0,n.D)(o.AXh,{onCompleted:()=>{},onError:e=>{console.error("mutationUpdateUser error",e)}}),J=({type:e,data:s})=>{let t={...B};if("trainingStatus"===e){s&&s.value?V(s.value):V(null);return}"vessel"===e&&(Array.isArray(s)&&s.length>0?t.vehicles={id:{in:s.map(e=>+e.value)}}:s&&!Array.isArray(s)?t.vehicles={id:{contains:+s.value}}:delete t.vehicles),"crewDuty"===e&&(Array.isArray(s)&&s.length>0?t.primaryDutyID={in:s.map(e=>+e.value)}:s&&!Array.isArray(s)?t.primaryDutyID={eq:+s.value}:delete t.primaryDutyID),"keyword"===e&&(u()(s.value)?delete t.q:t.q={contains:s.value}),"isArchived"===e&&(void 0!==s?t.isArchived={eq:!s}:delete t.isArchived),F(t),Y(0,t)},X=[{accessorKey:"title",header:({column:e})=>l.jsx(f.u,{column:e,title:"Name"}),cell:({row:e})=>{let s=e.original,t=`${s.firstName} ${s.surname}`;return(0,l.jsxs)("div",{className:"flex-1 flex justify-start items-center gap-2",children:[l.jsx(v.qE,{size:"sm",variant:s.trainingStatus?.label==="Overdue"?"destructive":"success",children:l.jsx(v.Q5,{children:(0,v.xE)(s.firstName,s.surname)})}),l.jsx("div",{className:"grid min-w-32",children:l.jsx(d.default,{href:`/crew/info?id=${s.id}`,className:"items-center truncate pl-2 text-nowrap",children:t||"--"})})]})},sortingFn:(e,s)=>{let t=`${e?.original?.firstName} ${e?.original?.surname}`||"",l=`${s?.original?.firstName} ${s?.original?.surname}`||"";return t.localeCompare(l)}},{accessorKey:"vehicles",cellAlignment:"left",header:({column:e})=>l.jsx(f.u,{column:e,title:"Vessel"}),cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"flex flex-row gap-2 py-2.5",children:s.vehicles.nodes.map(e=>{let s=b(e.id,e);return l.jsx("div",{className:"flex items-center text-start gap-2",children:(0,l.jsxs)(v.u,{children:[l.jsx(v.aJ,{children:l.jsx("div",{className:"min-w-fit",children:l.jsx(S.Z,{vessel:s})})}),l.jsx(v._v,{children:e.title})]},e.id)},String(e.id))})})},sortingFn:(e,s)=>{let t=e?.original?.vehicles?.nodes?.[0]?.title||"",l=s?.original?.vehicles?.nodes?.[0]?.title||"";return t.localeCompare(l)}},{accessorKey:"primaryDuty",cellAlignment:"right",header:({column:e})=>l.jsx(f.u,{column:e,title:"Primary Duty"}),cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"whitespace-normal px-5",children:s.primaryDuty.title})},sortingFn:(e,s)=>{let t=e?.original?.primaryDuty?.title||"",l=s?.original?.primaryDuty?.title||"";return t.localeCompare(l)}},{accessorKey:"trainingStatus",header:({column:e})=>l.jsx("div",{className:"flex justify-center w-full",children:l.jsx(f.u,{column:e,title:"Training"})}),cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"flex justify-center w-full",children:"Overdue"===s.trainingStatus.label?l.jsx(v.Ct,{variant:"destructive",type:"circle",children:s.trainingStatus.dues.length}):l.jsx(v.Ct,{variant:"success",type:"circle",children:l.jsx(y.Z,{className:"h-5 w-5"})})})}}],ee=(e,s)=>{J({type:e,data:s})};return(0,l.jsxs)(l.Fragment,{children:[l.jsx(D.ListHeader,{icon:l.jsx(N.K,{className:"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]"}),title:"All crew",actions:l.jsx(C,{onChange:e=>{ee("isArchived",e)}})}),l.jsx("div",{className:"mt-16",children:l.jsx(g.wQ,{columns:X,data:G,pageSize:20,onChange:J})})]})}let I=({crewList:e,vessels:s,handleCrewDuty:t=!1,showSurname:n})=>{let[o,h]=(0,a.useState)(!1),[u,m]=(0,a.useState)([]),[x,f]=(0,a.useState)(!0),N=(e,s=0,t=0)=>e.filter(e=>+e.parentID===s).flatMap(s=>{let l=N(e,+s.id,t+1);return[{...s,level:t},...l]}),[k,{loading:w}]=(0,i.t)(r.d5,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readDepartments.nodes;s&&m(N(s))},onError:e=>{console.error("queryCrewMembers error",e)}}),C=async()=>{await k()};(0,a.useEffect)(()=>{x&&(C(),f(!1))},[x]);let D=(0,c.Vp)(e,s).map(e=>{let s=e.trainingStatus.dues.filter(s=>e.vehicles.nodes.some(e=>e.id===s.vesselID)),t={...e.trainingStatus,dues:s};return 0===s.length&&(t.label="Good"),{...e,trainingStatus:t}});(0,a.useEffect)(()=>{},[]);let S=(0,g.wu)([{accessorKey:"title",header:"",cell:({row:e})=>{let s=e.original;return(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"flex gap-2.5 items-center",children:[l.jsx(v.qE,{size:"sm",variant:s.trainingStatus?.label!=="Good"?"destructive":"success",children:l.jsx(v.Q5,{children:(0,v.xE)(s.firstName,s.surname)})}),(0,l.jsxs)(d.default,{href:`/crew/info?id=${s.id}`,className:"flex items-center pl-2 text-nowrap",children:[s.firstName||"--",!0==n?(0,l.jsxs)("span",{children:["\xa0",s.surname||"--"]}):(0,l.jsxs)("span",{className:"hidden md:flex",children:["\xa0",s.surname||"--"]})]})]}),t&&l.jsx("div",{className:"flex md:hidden flex-col",children:s.vehicles.nodes&&s.vehicles.nodes.map((e,t)=>t<2?l.jsx("div",{className:"bg-muted font-light rounded-lg p-2 border m-1 border-border text-nowrap",children:l.jsx(d.default,{className:"max-w-32 overflow-hidden block",href:`/vessel/info?id=${e.id}`,children:e.title})},e.id):2===t?(0,l.jsxs)(j.Popover,{children:[l.jsx(j.PopoverTrigger,{asChild:!0,children:(0,l.jsxs)(p.Button,{variant:"outline",size:"sm",className:"text-orange-500",children:["+"," ",s.vehicles.nodes.length-2," ","more"]})}),l.jsx(j.PopoverContent,{className:"p-0 w-64",children:l.jsx("div",{className:"max-h-full bg-background rounded",children:s.vehicles.nodes.slice(2).map(e=>l.jsx("div",{className:"flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2",children:l.jsx("div",{className:"text-sm",children:l.jsx(d.default,{href:`/vessel/info?id=${e.id}`,children:e.title})})},e.id))})})]},e.id):null)})]})}},{accessorKey:"vessel",header:()=>l.jsx(l.Fragment,{children:t&&"Vessel"}),cellAlignment:"left",cell:({row:e})=>{let s=e.original;return l.jsx(l.Fragment,{children:t&&l.jsx("div",{children:s.vehicles.nodes&&s.vehicles.nodes.map((e,t)=>t<2?l.jsx("div",{className:"bg-muted inline-block font-light rounded-lg p-2 border border-border m-1",children:l.jsx(d.default,{href:`/vessel/info?id=${e.id}`,children:e.title})},e.id):2===t?(0,l.jsxs)(j.Popover,{children:[l.jsx(j.PopoverTrigger,{asChild:!0,children:(0,l.jsxs)(p.Button,{variant:"outline",size:"sm",className:"text-orange-500",children:["+"," ",s.vehicles.nodes.length-2," ","more"]})}),l.jsx(j.PopoverContent,{className:"p-0 w-64",children:l.jsx("div",{className:"max-h-full bg-background rounded",children:s.vehicles.nodes.slice(2).map(e=>l.jsx("div",{className:"flex cursor-pointer hover:bg-muted items-center overflow-auto px-3 py-2",children:l.jsx("div",{className:"text-sm",children:l.jsx(d.default,{href:`/vessel/info?id=${e.id}`,children:e.title})})},e.id))})})]},e.id):null)})})}},{accessorKey:"primaryDuty",header:"Primary Duty",cellAlignment:"left",cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"text-wrap text-right whitespace-normal",children:s.primaryDuty.title})}},{accessorKey:"trainingStatus",header:"Training Status",cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"flex justify-center",children:"Good"!==s.trainingStatus.label?(0,l.jsxs)(j.Popover,{triggerType:"hover",children:[l.jsx(j.PopoverTrigger,{asChild:!0,children:l.jsx(b.Z,{strokeWidth:1,className:"h-9 w-9 text-destructive cursor-pointer"})}),l.jsx(j.PopoverContent,{children:l.jsx("div",{className:"bg-background rounded p-2",children:l.jsx("div",{className:"text-xs whitespace-nowrap font-medium focus:outline-none inline-block rounded",children:s.trainingStatus.dues.map((e,s)=>l.jsx("div",{children:`${e.trainingType.title} - ${e.status.label}`},s))})})})]}):l.jsx(v.Ct,{variant:"success",type:"circle",children:l.jsx(y.Z,{className:"h-5 w-5"})})})}},{accessorKey:"departments",header:()=>l.jsx(l.Fragment,{children:o&&"true"===localStorage.getItem("useDepartment")&&"Departments"}),cell:({row:e})=>{let s=e.original;return l.jsx("div",{children:o&&"true"===localStorage.getItem("useDepartment")&&l.jsx(l.Fragment,{children:s.departments&&s.departments.nodes.length>0?s.departments.nodes.map(e=>l.jsx(d.default,{href:`/department/info?id=${e.id}`,className:"flex flex-col text-nowrap",children:u.find(s=>s.id===e.id)?.title},e.id)):l.jsx("span",{children:"No departments found"})})})}}]);return l.jsx(g.wQ,{columns:S,showToolbar:!1,data:D,pageSize:20})}},55361:(e,s,t)=>{"use strict";t.d(s,{Z:()=>j});var l=t(98768),a=t(60343),r=t(7678),i=t.n(r),n=t(76342),d=t(79418),o=t(72548),c=t(73366),h=t(10090),u=t(81524),m=t(24794),x=t(39544),p=t(71890);function g({openDialog:e,setOpenDialog:s,handleCreate:t,actionText:a,error:r}){return l.jsx(m.Dialog,{open:e,onOpenChange:s,children:(0,l.jsxs)(m.DialogContent,{children:[l.jsx(m.DialogHeader,{children:l.jsx(m.DialogTitle,{className:"font-medium",children:"Add Crew Member"})}),(0,l.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(new FormData(e.currentTarget))},children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 gap-4 border-t pt-6",children:[(0,l.jsxs)("div",{className:"flex gap-4",children:[l.jsx(p.I,{id:"crew-firstName",name:"firstName",type:"text",placeholder:"First Name"}),l.jsx(p.I,{id:"crew-surname",name:"surname",type:"text",placeholder:"Surname"})]}),(0,l.jsxs)("div",{className:"flex gap-4",children:[l.jsx(p.I,{id:"crew-username",name:"username",type:"text",placeholder:"Username"}),l.jsx(p.I,{id:"crew-password",name:"password",type:"password",placeholder:"Password"})]}),(0,l.jsxs)("div",{className:"flex gap-4",children:[l.jsx(p.I,{id:"crew-email",name:"email",type:"email",placeholder:"Email"}),l.jsx(p.I,{id:"crew-phoneNumber",name:"phoneNumber",type:"text",placeholder:"Phone Number"})]}),r&&l.jsx("div",{className:"text-rose-600",children:r.message})]}),l.jsx(m.DialogFooter,{className:"mt-6",children:l.jsx(x.Button,{type:"submit",children:a})})]})]})})}var f=t(45519);let v=(0,f.ZP)`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                isArchived
                firstName
                surname
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
                departments {
                    nodes {
                        id
                    }
                }
                groups {
                    nodes {
                        id
                        title
                        code
                    }
                }
            }
        }
    }
`,j=({value:e=[],onChange:s,memberIdOptions:t=[],departments:r=[],filterByAdmin:m=!1,offline:x=!1,vesselID:p=0})=>{let[f,j]=(0,a.useState)(!0),[y,b]=(0,a.useState)([]),[N,k]=(0,a.useState)(!1),[w,C]=(0,a.useState)([]),[D,S]=(0,a.useState)(!1),M=new c.Z,T=e=>{let s=p>0?e.filter(e=>e.vehicles.nodes.some(e=>+e.id===p)):e,l={value:"newCrewMember",label:"--- Create Crew Member ---"},a=s.filter(e=>!m||!Z(e));if(r.length>0){let e=r.flatMap(e=>e.id),s=a.filter(s=>s.departments.nodes.some(s=>e.includes(s.id))).map(e=>({value:e.id,label:`${e.firstName??""} ${e.surname??""}`}));0===t.length?b([l,...s]):b(s.filter(e=>t.includes(e.value)))}else{let e=a.map(e=>({value:e.id,label:`${e.firstName??""} ${e.surname??""}`}));0===t.length?b([l,...e]):b(e.filter(e=>t.includes(e.value)))}},[I]=(0,d.t)(v,{fetchPolicy:"cache-and-network",onError:e=>{console.error("querySeaLogsMembersList error",e)}}),E=async()=>{let e=[],s=0,t=!0;try{for(;t;){let l=await I({variables:{filter:{isArchived:{eq:!1}},limit:100,offset:s}});if(l.data?.readSeaLogsMembers){let a=l.data.readSeaLogsMembers.nodes,r=l.data.readSeaLogsMembers.pageInfo;a&&a.length>0&&(e=[...e,...a]),t=r?.hasNextPage||!1,s+=100}else t=!1}e.length>0&&T(e)}catch(e){console.error("Error loading all crew members:",e)}};(0,a.useEffect)(()=>{f&&!x&&(E(),j(!1))},[f,x]),(0,a.useEffect)(()=>{x&&M.getAll().then(e=>{T(e)})},[x]);let Z=e=>e.groups.nodes?.filter(e=>"admin"===e.code).length>0,[A]=(0,o.D)(n.qK0,{fetchPolicy:"no-cache",onCompleted:t=>{let l=t.createSeaLogsMember;if(l.id>0){k(!1);let t={value:l.id,label:l.firstName+" "+l.surname};b([...y,t]),C([...w,l.id]),s([t,...e.map(e=>y.find(s=>s.value===e))]),S(!1)}},onError:e=>{console.error("createUser error",e.message),S(e)}}),O=async()=>{let t={input:{firstName:document.getElementById("crew-firstName").value?document.getElementById("crew-firstName").value:null,surname:document.getElementById("crew-surname").value?document.getElementById("crew-surname").value:null,email:document.getElementById("crew-email").value?document.getElementById("crew-email").value:null,phoneNumber:document.getElementById("crew-phoneNumber").value?document.getElementById("crew-phoneNumber").value:null,username:document.getElementById("crew-username").value?document.getElementById("crew-username").value:null,password:document.getElementById("crew-password").value?document.getElementById("crew-password").value:null}};if(x){let l=await M.save({...t.input,id:(0,h.lY)()});k(!1);let a={value:l.id,label:l.firstName+" "+l.surname};b([...y,a]),C([...w,l.id]),s([a,...e.map(e=>y.find(s=>s.value===e))]),S(!1)}else await A({variables:t})};return(0,a.useEffect)(()=>{if(i()(e)||0===e.length){C([]);return}i()(y)||C(e.map(e=>{let s=String(e);return y.find(e=>String(e.value)===s)||(console.warn("CrewMultiSelectDropdown - Could not find crew with ID:",e),{value:s,label:`Unknown (${e})`})}).filter(Boolean))},[e,y]),(0,l.jsxs)(l.Fragment,{children:[l.jsx(u.Combobox,{options:y,value:w,onChange:e=>{if(!e){C([]),s([]);return}let t=Array.isArray(e)?e:[e];if(t.find(e=>"newCrewMember"===e.value)){k(!0);return}if(0===t.length){C([]),s([]);return}let l=t.filter(e=>e&&"object"==typeof e);C(l),s(l)},placeholder:"Select Crew",multi:!0,responsiveBadges:!0,isLoading:!y}),l.jsx(g,{openDialog:N,setOpenDialog:k,handleCreate:O,actionText:"Add Crew Member",error:D})]})}},15806:(e,s,t)=>{"use strict";t.d(s,{Z:()=>x,z:()=>m});var l=t(98768),a=t(60343),r=t(13842),i=t(66263),n=t(69424),d=t(75546),o=t(51742),c=t(25394),h=t(69748),u=t(99891);let m=e=>{if(!e)return"";let s=new Date(e),t=Math.abs(new Date().getTime()-s.getTime()),l=Math.floor(t/6e4),a=Math.floor(t/36e5),r=Math.floor(t/864e5);return 0===r?0===a?0===l?"Just now":1===l?"1 minute ago":`${l} minutes ago`:1===a?"1 hour ago":`${a} hours ago`:1===r?"Yesterday":r<=7?`${r} days ago`:(0,d.p6)(e)};function x(e){let[s,t]=(0,a.useState)();(0,n.useSearchParams)(),(0,r.__)(t);let d=e=>{let s="";return"Fitness"==e&&(s="crew"),"SafetyActions"==e&&(s="crew"),"WaterQuality"==e&&(s="crew"),"IMSafe"==e&&(s="crew"),"Safety"==e&&(s="dailyChecks"),"HighWaterAlarm"==e&&(s="dailyChecks"),"FirstAid"==e&&(s="dailyChecks"),"SafetyEquipment"==e&&(s="dailyChecks"),"FireExtinguisher"==e&&(s="dailyChecks"),"Hull"==e&&(s="dailyChecks"),"Hull_HullStructure"==e&&(s="dailyChecks"),"Hull_DeckEquipment"==e&&(s="dailyChecks"),"Hull_DayShapes"==e&&(s="dailyChecks"),"TenderOperationalChecks"==e&&(s="dailyChecks"),"Anchor"==e&&(s="dailyChecks"),"WindscreenCheck"==e&&(s="dailyChecks"),"NightLineDockLinesRelease"==e&&(s="dailyChecks"),"Propulsion"==e&&(s="dailyChecks"),"PreEngineAndPropulsion"==e&&(s="dailyChecks"),"EngineCheckPropellers"==e&&(s="dailyChecks"),"EngineOilWater"==e&&(s="dailyChecks"),"EngineMountsAndStabilisers"==e&&(s="dailyChecks"),"ElectricalChecks"==e&&(s="dailyChecks"),"ElectricalVisualFields"==e&&(s="dailyChecks"),"Generator"==e&&(s="dailyChecks"),"ShorePower"==e&&(s="dailyChecks"),"SteeringChecks"==e&&(s="dailyChecks"),"Navigation"==e&&(s="dailyChecks"),"NavigationCharts"==e&&(s="dailyChecks"),"NavigationChecks"==e&&(s="dailyChecks"),"Radio"==e&&(s="dailyChecks"),"OtherNavigation"==e&&(s="dailyChecks"),"LogBookSignOff"==e&&(s="signOff"),"Review"==e&&(s="signOff"),"SafetyEquipmentCheck"==e&&(s="signOff"),"ForecastAccuracy"==e&&(s="signOff"),"Power"==e&&(s="signOff"),"BatteryMaintenance"==e&&(s="signOff"),"CircuitInspections"==e&&(s="signOff"),"MooringAndAnchoring"==e&&(s="signOff"),"CargoAndAccessEquipment"==e&&(s="signOff"),"HatchesAndWatertightDoors"==e&&(s="signOff"),"GalleyAppliances"==e&&(s="signOff"),"WasteManagement"==e&&(s="signOff"),"VentilationAndAirConditioning"==e&&(s="signOff"),"EmergencyReadiness"==e&&(s="signOff"),"EnvironmentalCompliance"==e&&(s="signOff"),s},x=e=>{let s="";return"DailyCheckFuel"==e&&(s="Engine Checks"),"DailyCheckEngine"==e&&(s="Engine Checks"),"Safety"==e&&(s="Safety Checks"),"HighWaterAlarm"==e&&(s="Safety Checks"),"FirstAid"==e&&(s="Safety Checks"),"SafetyEquipment"==e&&(s="Safety Checks"),"FireExtinguisher"==e&&(s="Safety Checks"),"Hull"==e&&(s="Deck operations and exterior checks"),"Hull_HullStructure"==e&&(s="Deck operations and exterior checks"),"Hull_DeckEquipment"==e&&(s="Deck operations and exterior checks"),"Hull_DayShapes"==e&&(s="Deck operations and exterior checks"),"TenderOperationalChecks"==e&&(s="Deck operations and exterior checks"),"Anchor"==e&&(s="Deck operations and exterior checks"),"WindscreenCheck"==e&&(s="Deck operations and exterior checks"),"NightLineDockLinesRelease"==e&&(s="Deck operations and exterior checks"),"Propulsion"==e&&(s="Engine Checks"),"PreEngineAndPropulsion"==e&&(s="Engine Checks"),"EngineCheckPropellers"==e&&(s="Engine Checks"),"EngineOilWater"==e&&(s="Engine Checks"),"EngineMountsAndStabilisers"==e&&(s="Engine Checks"),"ElectricalChecks"==e&&(s="Engine Checks"),"ElectricalVisualFields"==e&&(s="Engine Checks"),"Generator"==e&&(s="Engine Checks"),"ShorePower"==e&&(s="Engine Checks"),"SteeringChecks"==e&&(s="Engine Checks"),"Navigation"==e&&(s="Navigation"),"NavigationCharts"==e&&(s="Navigation"),"NavigationChecks"==e&&(s="Navigation"),"Radio"==e&&(s="Navigation"),"OtherNavigation"==e&&(s="Navigation"),s},p=(0,o.wu)([{accessorKey:"title",header:"Comment",cellAlignment:"left",cell:({row:e})=>{let s=e.original;return(0,l.jsxs)("div",{className:"space-y-1 py-2.5",children:[(0,l.jsxs)("div",{className:"flex items-start justify-between",children:[(0,l.jsxs)("span",{className:"flex flex-col items-center sm:flex-row gap-x-2.5",children:[l.jsx(i.default,{href:`/log-entries?vesselID=${s.logBookEntry.vehicleID}&logentryID=${s.logBookEntry.id}&firstTab=${d(s.fieldName)}&secondTab=${x(s.fieldName)}`,children:l.jsx("div",{className:"hover:underline",children:s.commentType})}),s.logBookEntry.vehicle?.title&&(0,l.jsxs)(l.Fragment,{children:[l.jsx("span",{className:"hidden sm:block",children:"-"}),(0,l.jsxs)("div",{className:"inline-flex items-center gap-2 text-nowrap text-sm",children:[l.jsx("div",{className:"size-5 flex items-center justify-center flex-shrink-0 [&_img]:!size-5 [&_svg]:!size-5",children:l.jsx(u.Z,{vessel:s.logBookEntry.vehicle})}),l.jsx("span",{children:s.logBookEntry.vehicle.title})]})]})]}),l.jsx(i.default,{href:`/log-entries?vesselID=${s.logBookEntry.vehicleID}&logentryID=${s.logBookEntry.id}&firstTab=${d(s.fieldName)}&secondTab=${x(s.fieldName)}`,children:m(s.lastEdited)})]}),l.jsx(c.P,{children:s.comment})]})}},{accessorKey:"author",header:"Author",cellAlignment:"center",cell:({row:e})=>{let s=e.original,t=`${s?.seaLogsMember?.firstName||""} ${s?.seaLogsMember?.surname||""}`.trim();return t&&s?.seaLogsMember?.firstName!==null?l.jsx("div",{className:"flex-1 flex justify-center",children:(0,l.jsxs)(c.u,{children:[l.jsx(c.aJ,{asChild:!0,children:(0,l.jsxs)(i.default,{className:"w-fit flex justify-center items-center group/crew gap-2.5",href:`/log-entries?vesselID=${s.logBookEntry.vehicleID}&logentryID=${s.logBookEntry.id}&firstTab=${d(s.fieldName)}&secondTab=${x(s.fieldName)}`,children:[l.jsx(h.Avatar,{size:"sm",variant:"secondary",children:l.jsx(h.AvatarFallback,{className:"text-sm",children:(0,h.getCrewInitials)(s?.seaLogsMember?.firstName,s?.seaLogsMember?.surname)})}),l.jsx("div",{className:"hidden lg:block group-hover/crew:underline",children:t||"Unknown Author"})]})}),l.jsx(c._v,{className:"lg:hidden",children:t||"Unknown Author"})]})}):null}}]);return(0,l.jsxs)(l.Fragment,{children:[l.jsx("div",{className:"h-full flex justify-between items-center p-2",children:l.jsx(c.H2,{children:"Notifications"})}),s&&l.jsx(o.wQ,{columns:p,data:s,showToolbar:!1,pageSize:20})]})}},14608:(e,s,t)=>{"use strict";t.d(s,{X:()=>y,Z:()=>v});var l=t(98768),a=t(94060),r=t(79418),i=t(60343),n=t(66263),d=t(17380),o=t(43692),c=t(37042),h=t(13842),u=t(75546),m=t(46776),x=t(26100),p=t(60797),g=t(51742),f=t(25394);let v=({memberId:e=0,vesselId:s=0})=>{let[t,n]=(0,i.useState)(!0),[u,p]=(0,i.useState)({totalCount:0,hasNextPage:!1,hasPreviousPage:!1}),[g,v]=(0,i.useState)([]),[j,b]=(0,i.useState)([]),[N,k]=(0,i.useState)(0),[w,C]=(0,i.useState)({}),[D,S]=(0,i.useState)([]),[M,T]=(0,i.useState)([]),[I,E]=(0,i.useState)([]),[Z,A]=(0,i.useState)([]),[O,{loading:$}]=(0,r.t)(a.ly,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readTrainingSessions.nodes,t=Array.from(new Set(s.map(e=>e.vessel.id))).filter(e=>0!=+e),l=Array.from(new Set(s.flatMap(e=>e.trainingTypes.nodes.map(e=>e.id)))),a=Array.from(new Set(s.map(e=>e.trainerID))).filter(e=>0!=+e),r=Array.from(new Set(s.flatMap(e=>e.members.nodes.map(e=>e.id))));s&&(v(s),S(t),T(l),E(a),A(r)),p(e.readTrainingSessions.pageInfo)},onError:e=>{console.error("queryTrainingList error",e)}}),R=async(e=0,s={...w})=>{await O({variables:{filter:s,offset:100*e,limit:100}})},P=e=>{e<0||e===N||(k(e),B(w),R(e,w))},[L,{loading:W}]=(0,r.t)(a.qX,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readTrainingSessionDues.nodes;s&&b(Object.values(s.filter(e=>e.vessel.seaLogsMembers.nodes.some(s=>s.id===e.memberID)).map(e=>({...e,status:(0,h.nu)(e)})).filter(e=>e.status.isOverdue||!1===e.status.isOverdue&&!0===e.status.dueWithinSevenDays).reduce((e,s)=>{let t=`${s.vesselID}-${s.trainingTypeID}-${s.dueDate}`;return e[t]||(e[t]={id:s.id,vesselID:s.vesselID,vessel:s.vessel,trainingTypeID:s.trainingTypeID,trainingType:s.trainingType,dueDate:s.dueDate,status:s.status,members:[]}),e[t].members.push(s.member),e},{})).map(e=>{let s=e.members.reduce((e,s)=>{let t=e.find(e=>e.id===s.id);return t?(t.firstName=s.firstName,t.surname=s.surname):e.push(s),e},[]);return{id:e.id,vesselID:e.vesselID,vessel:e.vessel,trainingTypeID:e.trainingTypeID,trainingType:e.trainingType,status:e.status,dueDate:e.dueDate,members:s}}))},onError:e=>{console.error("readTrainingSessionDues error",e)}}),B=async t=>{let l={};e>0&&(l.memberID={eq:+e}),s>0&&(l.vesselID={eq:+s}),t.vesselID&&(l.vesselID=t.vesselID),t.trainingTypes&&(l.trainingTypeID={eq:t.trainingTypes.id.contains}),t.members&&(l.memberID={eq:t.members.id.contains}),t.date?l.dueDate=t.date:l.dueDate={ne:null},await L({variables:{filter:l}})};(0,i.useEffect)(()=>{if(t){let s={...w};+e>0&&(s.members={id:{contains:+e}}),C(s),B(s),R(0,s),n(!1)}},[t]);let[F,_]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{_(m.Zu)},[]),F&&((0,m.Fs)("EDIT_TRAINING",F)||(0,m.Fs)("VIEW_TRAINING",F)||(0,m.Fs)("RECORD_TRAINING",F)||(0,m.Fs)("VIEW_MEMBER_TRAINING",F)))?l.jsx(f.Zb,{children:(0,l.jsxs)(f.aY,{className:"flex flex-col gap-4",children:[l.jsx(c.Z,{onChange:({type:e,data:s})=>{let t={...w};"vessel"===e&&(s?t.vesselID={eq:+s.value}:delete t.vesselID),"trainingType"===e&&(s?t.trainingTypes={id:{contains:+s.value}}:delete t.trainingTypes),"trainer"===e&&(s?t.trainer={id:{eq:+s.value}}:delete t.trainer),"member"===e&&(s?t.members={id:{contains:+s.value}}:delete t.members),"dateRange"===e&&(s.startDate&&s.endDate?t.date={gte:s.startDate,lte:s.endDate}:delete t.date),C(t),B(t),R(0,t)},vesselIdOptions:D,trainingTypeIdOptions:M,trainerIdOptions:I,memberIdOptions:Z}),$||W?l.jsx(d.hM,{}):(0,l.jsxs)(l.Fragment,{children:[l.jsx(y,{trainingList:g,memberId:e,trainingSessionDues:j}),l.jsx(o.Z,{page:N,limit:100,visiblePageCount:5,...u,onClick:e=>P(e)})]})]})}):F?l.jsx(x.Z,{errorMessage:"OopsYou do not have the permission to view this section."}):l.jsx(x.Z,{})},j=({remainingMembers:e})=>(0,l.jsxs)(f.J2,{children:[l.jsx(f.CM,{asChild:!0,children:(0,l.jsxs)(f.zx,{variant:"primaryOutline",children:["+ ",e.length," more"]})}),l.jsx(f.yk,{className:"w-64",children:e.map(e=>l.jsx("div",{className:"flex cursor-pointer items-center overflow-auto",children:l.jsx("div",{className:"ps-3 py-2",children:l.jsx("div",{className:"",children:`${e.firstName??""} ${e.surname??""}`})})},e.id))})]}),y=({trainingList:e,trainingSessionDues:s,memberId:t=0,isVesselView:a=!1})=>{let r=[{accessorKey:"title",header:"Completed",cell:({row:e})=>{let s=e.original;return(0,l.jsxs)("div",{children:[l.jsx(n.default,{href:`/crew-training/info?id=${s.id}`,className:"  ",children:(0,u.p6)(s.date)}),(0,l.jsxs)("div",{className:"flex flex-wrap md:hidden",children:[s.trainingTypes.nodes?s.trainingTypes.nodes.map(e=>(0,l.jsxs)("span",{children:[e.title,","]},e.id)):"",!1==a&&(0,l.jsxs)("span",{className:"inline-block md:hidden",children:[":\xa0",s.vessel.title?s.vessel.title:""]})]}),l.jsx("div",{className:"flex flex-wrap lg:hidden ",children:s.members.nodes.map((e,t)=>t<2?a?(0,l.jsxs)("div",{className:"inline-block  border   rounded-lg  m-1 p-2 outline-none ",children:[`${e.firstName??""}`," "]},e.id):l.jsx("div",{className:"inline-block  border   rounded-lg  m-1 p-2 outline-none ",children:`${e.firstName??""} ${e.surname??""}`},e.id):2===t?l.jsx(j,{remainingMembers:s.members.nodes.slice(2)}):void 0)}),(0,l.jsxs)("div",{className:"flex flex-row gap-2 md:hidden items-baseline",children:[l.jsx(p.Label,{className:" !w-auto",children:"Trainer:"}),l.jsx(l.Fragment,{children:s.trainer?.id===+t?"You":`${s.trainer&&s.trainer.firstName||""} ${s.trainer&&s.trainer.surname||""}`})]})]})}},{accessorKey:"trainingDrill",header:"Training/drill",cell:({row:e})=>{let s=e.original;return l.jsx("td",{className:"hidden md:table-cell !w-1/3",children:s.trainingTypes.nodes?s.trainingTypes.nodes.map(e=>(0,l.jsxs)("span",{children:[e.title,",\xa0"]},e.id)):""})}},{accessorKey:"vessel",header:()=>l.jsx(l.Fragment,{children:!1==a&&"Where"}),cell:({row:e})=>{let s=e.original;return l.jsx("div",{children:!1==a&&l.jsx("div",{className:"hidden md:table-cell lg:align-middle",children:s.vessel.title?s.vessel.title:""})})}},{accessorKey:"who",header:"Who",cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"hidden lg:table-cell lg:align-middle",children:l.jsx("div",{className:"flex flex-wrap",children:s.members.nodes.map((e,t)=>t<2?a?(0,l.jsxs)("div",{className:"inline-block  border   rounded-lg  m-1 p-2 text-nowrap outline-none",children:[`${e.firstName??""} ${e.surname??""}`," "]},e.id):l.jsx("div",{className:"inline-block  border   rounded-lg  m-1 p-2 text-nowrap outline-none",children:`${e.firstName??""} ${e.surname??""}`},e.id):2===t?l.jsx(j,{remainingMembers:s.members.nodes.slice(2)}):void 0)})})}},{accessorKey:"trainer",header:"Trainer",cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"hidden md:table-cell lg:align-middle text-nowrap",children:l.jsx("div",{className:" border   rounded-lg  m-1 p-2 text-nowrap outline-none ",children:s.trainer?.id===+t?"You":`${s.trainer&&s.trainer.firstName||""} ${s.trainer&&s.trainer.surname||""}`})})}}];return l.jsx(l.Fragment,{children:e?.length>0?l.jsx(g.wQ,{columns:r,data:e,pageSize:20,showToolbar:!1}):l.jsx("tr",{className:"group border-b hover: ",children:l.jsx("td",{colSpan:4,className:"p-4",children:(0,l.jsxs)("div",{className:"flex justify-between items-center gap-2 p-2 pt-4",children:[(0,l.jsxs)("svg",{className:"!w-[75px] h-auto",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 147 147.01",children:[l.jsx("path",{d:"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z",fill:"#ffffff",strokeWidth:"0px"}),l.jsx("path",{d:"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z",fill:"#052350",fillRule:"evenodd",opacity:".97",strokeWidth:"0px"}),l.jsx("path",{d:"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z",fill:"#2998e9",strokeWidth:"0px"}),l.jsx("path",{d:"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z",fill:"#2998e9",strokeWidth:"0px"}),l.jsx("path",{d:"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z",fill:"#024450",strokeWidth:"0px"}),l.jsx("path",{d:"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z",fill:"#052451",strokeWidth:"0px"}),l.jsx("path",{d:"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z",fill:"#052250",strokeWidth:"0px"}),l.jsx("path",{d:"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z",fill:"#2998e9",strokeWidth:"0px"}),l.jsx("path",{d:"M45.34,125.8h23.84v6.63h-23.84v-6.63Z",fill:"#052350",strokeWidth:"0"}),l.jsx("path",{d:"M70.17,125.8h6.58v6.63h-6.58v-6.63Z",fill:"#052250",strokeWidth:"0"}),l.jsx("path",{d:"M77.77,125.8h23.84v6.63h-23.84v-6.63Z",fill:"#052350",strokeWidth:"0"}),l.jsx("path",{d:"M67.98,127.01v4.2h-21.42v-4.2h21.42Z",fill:"#2a99ea",strokeWidth:"0"}),l.jsx("path",{d:"M75.58,127.01v4.2h-4.2v-4.2h4.2Z",fill:"#2a99ea",strokeWidth:"0"}),l.jsx("path",{d:"M78.99,127.01h21.42v4.2h-21.42v-4.2Z",fill:"#2a99ea",strokeWidth:"0"}),l.jsx("path",{d:"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z",fill:"#ffffff",strokeWidth:"0"})]}),l.jsx("p",{className:"  ",children:"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!"})]})})})})}},4498:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>e_});var l=t(98768),a=t(39544),r=t(94060),i=t(76342),n=t(72548),d=t(79418),o=t(60343),c=t(69424),h=t(66263),u=t(13842);t(7678);var m=t(83179),x=t.n(m),p=t(55361),g=t(34376),f=t(28254),v=t(46776);let j=e=>{let s=localStorage.getItem(`tasksDue-${e}`);return s?parseInt(s,10):0},y=e=>{let s=localStorage.getItem(`trainingsDue-${e}`);return s?parseInt(s,10):0};function b({bannerImage:e}){return l.jsx("div",{className:"block",children:e?l.jsx("div",{className:"w-100 lg:!h-[500px] md:!h-[500px] !h-[250px] bg-center bg-cover rounded-xl border-b",style:{backgroundImage:`url('${e}')`}}):l.jsx("div",{className:"w-full lg:h-[500px] md:h-[500px] h-[250px] bg-center bg-cover rounded-xl border-b",style:{backgroundImage:"url('/sealogs-SeaLogs_hero.png')"}})})}var N=t(40977),k=t(75546),w=t(32946),C=t(51742),D=t(74602),S=t(25394);function M({vesselId:e,logbooks:s,imCrew:t,handleCreateNewLogEntry:r,isNewLogEntryDisabled:i,setVesselTab:n,vesselTitle:d,scrollToTabs:c}){let u=(0,f.d)(),[m,x]=(0,o.useState)(5),p=(0,o.useRef)(null),g=s?.slice(0,m)||[],v=s&&s.length>m;return(0,l.jsxs)(S.Zb,{ref:p,children:[(0,l.jsxs)("div",{className:"flex p-3 phablet:py-3 phablet:px-0 items-baseline gap-2 phablet:gap-4",children:[l.jsx(N.V,{className:"h-12 w-12 ring-1 p-1 bg-[#fff] rounded-full"}),l.jsx(h.default,{href:"/vessel",children:l.jsx(D.H1,{children:d})})]}),s.length>0?l.jsx(l.Fragment,{children:l.jsx("div",{className:"p-0 phablet:pt-1",children:l.jsx(C.wQ,{columns:[{accessorKey:"title",header:"",cell:({row:s})=>{let t=s.original;return l.jsx("div",{className:"flex justify-between items-center",children:(0,l.jsxs)(h.default,{href:`/log-entries?vesselID=${e}&logentryID=${t.id}`,className:`${(t.state,"")} hover:text-primary`,children:["Locked"===t.state?"Log entry":l.jsx("strong",{children:"Open log: "})," ",t?.startDate?(0,k.p6)(t.startDate):""]})})}},{accessorKey:"due",header:"",cellAlignment:"right",cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"flex justify-end",children:l.jsx("div",{className:` w-fit ${"Locked"===s.state?"p-2":"alert"} whitespace-nowrap`,children:s.state})})}}],data:g,showToolbar:!1,pageSize:u?g.length:5,className:"border-0 shadow-none"})})}):(0,l.jsxs)("div",{className:"flex justify-between items-center gap-2 p-2 pt-4",children:[l.jsx("div",{children:l.jsx(w.k,{})}),l.jsx("p",{children:!t&&l.jsx(a.Button,{onClick:r,disabled:i,children:"Create a log entry"})})]}),s.length>5&&l.jsx("div",{className:"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center",children:u&&v?(0,l.jsxs)(a.Button,{variant:"outline",onClick:()=>x(e=>e+20),className:"text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto",children:["View More",l.jsx("span",{className:"hidden md:inline-block",children:"\xa0log entries\xa0"})]}):u&&!v&&s.length>5?(0,l.jsxs)(a.Button,{variant:"outline",onClick:()=>{x(5),p.current?.scrollIntoView({behavior:"smooth",block:"center"})},className:"text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto",children:["View Less",l.jsx("span",{className:"hidden md:inline-block",children:"\xa0log entries\xa0"})]}):(0,l.jsxs)(h.default,{href:"#",onClick:e=>{e.preventDefault(),n("logEntries"),c?.()},className:"hidden md:block text-accent-foreground uppercase hover:text-primary text-xs",children:["View all",l.jsx("span",{className:"hidden md:inline-block",children:"\xa0log entries\xa0"})]})})]})}var T=t(57659);let I=(0,o.forwardRef)(({color:e="currentColor",size:s=75,strokeWidth:t=0,className:a="!w-[75px] h-auto",...r},i)=>(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 148.02 147.99",width:s,height:"auto",className:a,ref:i,...r,children:[l.jsx("path",{d:"M70.84.56c16-.53,30.66,3.59,43.98,12.35,12.12,8.24,21.1,19.09,26.92,32.55,6.14,14.85,7.38,30.11,3.74,45.78-3.92,15.59-11.95,28.57-24.1,38.96-13.11,10.9-28.24,16.66-45.39,17.28-16.75.33-31.88-4.39-45.39-14.17-13.29-9.92-22.34-22.84-27.16-38.76-4.03-14.16-3.9-28.29.39-42.38,5-15.45,14-27.97,27.01-37.6C42.77,5.97,56.1,1.31,70.84.56Z",fill:"#fefefe",fillRule:"evenodd",stroke:"#024450",strokeMiterlimit:"10",strokeWidth:"1.02px"}),l.jsx("path",{d:"M63.03,13.61c1.74.02,3.47.13,5.19.32,2.15.26,4.31.51,6.46.78,1.18.34,2.08,1.04,2.69,2.11.56,1,.85,2.06.87,3.2,1.5,2.89,2.99,5.79,4.47,8.69.09.17.19.32.32.46,1.72,1.08,3.12,2.48,4.2,4.2.42.79.72,1.63.9,2.5-.04.01-.07.04-.1.07.58,1.01.64,2.04.17,3.11-.47.88-1.1,1.62-1.92,2.21-1.17.81-2.44,1.45-3.79,1.92-.07.56-.13,1.13-.17,1.7,0,.86-.03,1.72-.1,2.57-.14.56-.42,1.04-.85,1.43-.38.3-.8.39-1.26.27-.01,1.92-.46,3.73-1.33,5.44-.59,2.66-1.36,5.27-2.33,7.82-.4,1.04-.96,1.99-1.67,2.84-.36-.12-.73-.2-1.12-.27-.28,0-.53.08-.78.22-.23.16-.45.33-.68.49-.83.87-1.67,1.73-2.52,2.57-.78.67-1.68,1.03-2.72,1.09-.09-.26-.18-.52-.27-.78-.26-.26-.58-.43-.95-.51-1.68-.23-3.27.06-4.76.87-.28.24-.56.48-.85.7-.95-1.87-2.36-3.27-4.25-4.2-.37-.14-.74-.25-1.12-.34-.42-.03-.84-.03-1.26,0-.19.06-.38.1-.58.1-.58-.66-1.04-1.39-1.38-2.21-1.11-2.73-1.98-5.53-2.62-8.4-.89-1.7-1.33-3.51-1.33-5.44-.97.14-1.64-.25-2.01-1.17-.12-.3-.2-.6-.24-.92-.01-.76-.03-1.52-.05-2.28-.02-.39-.07-.78-.15-1.17-1.41-.47-2.77-1.07-4.05-1.82-.82-.49-1.54-1.09-2.16-1.82-.66-.81-.93-1.73-.83-2.77.33-1.03.65-2.06.92-3.11.56-1.18,1.32-2.22,2.26-3.13,1.27-1.15,2.67-2.11,4.2-2.89,1.39-2.69,2.79-5.37,4.17-8.06.01-1.77.66-3.26,1.92-4.49.47-.39,1-.67,1.6-.83,3.29-.42,6.57-.79,9.85-1.09Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M63.17,14.97c2.44.07,4.86.25,7.28.56,1.3.16,2.59.33,3.88.49.85.26,1.5.78,1.92,1.58.43.87.64,1.79.63,2.77,1.18,2.31,2.37,4.62,3.57,6.92-3.88-1.88-7.97-3.04-12.28-3.5-5.82-.65-11.53-.15-17.14,1.5-1.08.33-2.13.73-3.16,1.19l-.05-.05c1.01-2.01,2.04-4.02,3.08-6.02,0-1.18.3-2.26.92-3.25.41-.57.95-.95,1.63-1.14,3.23-.44,6.47-.78,9.71-1.04Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M22.83,121.38c-.05.7-.06,1.42-.05,2.14h-1.31v-1.84c.04-6.98.54-13.92,1.48-20.82.54-4.01,1.44-7.94,2.67-11.8.83-2.63,2.05-5.06,3.64-7.28,1.23-1.49,2.67-2.74,4.32-3.74,0-.15-.03-.29-.12-.41,3.43-.91,6.85-1.76,10.29-2.55,2.46,6.94,4.9,13.88,7.33,20.82h25.63c2.42-6.97,4.87-13.93,7.35-20.87,1.78.46,3.56.91,5.34,1.36,1.34-2.25,3.04-4.21,5.1-5.87.78-4.96,2.07-9.78,3.88-14.47.65-1.62,1.43-3.17,2.33-4.66.76-1.21,1.68-2.27,2.79-3.18-1.36-.17-2.34-.88-2.94-2.11-.04-.09-.06-.19-.07-.29-2.47-.68-3.87-2.31-4.2-4.85-.2-2.64-.39-5.28-.58-7.91-.03-.54,0-1.09.07-1.63-.17-1.88.57-3.25,2.23-4.13,1.68-.73,3.36-1.46,5.05-2.18.39-.11.79-.17,1.19-.17,3.64.42,7.27.88,10.9,1.38,1.72.41,2.66,1.5,2.82,3.25-.02,1.36-.63,2.38-1.8,3.06,1.1,1.14,1.33,2.44.7,3.91-.33.64-.82,1.14-1.43,1.5,1.22,1.38,1.34,2.85.36,4.42-.31.42-.69.75-1.14,1,1.02,1.05,1.29,2.27.8,3.66-.77,1.59-2.04,2.3-3.81,2.11-.7-.09-1.39-.17-2.09-.24,1.17,1.13,2.15,2.4,2.94,3.81,1.95,3.61,3.36,7.43,4.22,11.46,2.2.83,4.31,1.85,6.33,3.03.89.53,1.66,1.2,2.31,2.01.7,1.3,1.09,2.69,1.17,4.17.08,2.03-.09,4.03-.53,6.02-.48,2.16-1.04,4.3-1.7,6.41-.79,2.37-1.56,4.75-2.33,7.14-.74.36-1.49.39-2.26.07-1.22-.53-2.31-1.25-3.28-2.16-1.78,5.28-4.16,10.26-7.14,14.95-.02.04-.03.09-.02.15,3.62.73,6.54,2.56,8.76,5.49,1.2,1.7,1.84,3.59,1.92,5.68,0,.23-.01.45-.02.68-.42.42-.93.64-1.53.66-1.25.03-2.48-.12-3.69-.44-2.04-.52-4.08-1.05-6.12-1.6-.88-.23-1.78-.37-2.69-.41-.84.03-1.68.16-2.5.36-1.96.52-3.91,1.04-5.87,1.55-.95.21-1.9.39-2.86.53-.49.03-.97.03-1.46,0-.49-.08-.9-.3-1.24-.66-.08-2.31.54-4.41,1.84-6.31,1.21-1.71,2.74-3.06,4.59-4.05.75-.38,1.51-.72,2.28-1.04-2.93-4.67-5.04-9.68-6.33-15.05-.58-2.67-.91-5.37-.97-8.11-.39.24-.79.48-1.19.7-.06.04-.1.1-.12.17-1.41,3.89-2.79,7.79-4.15,11.7h1.02c1.11,12.83,2.22,25.66,3.35,38.49h-56.89c1.1-12.83,2.22-25.66,3.35-38.49.39.01.78,0,1.17-.05-1.95-5.48-3.88-10.97-5.8-16.46-.03-.04-.08-.05-.12-.02-1.95,1.22-3.53,2.82-4.73,4.78-1.06,1.86-1.92,3.82-2.57,5.87-.84,2.72-1.51,5.49-1.99,8.3-.9,5.53-1.47,11.1-1.7,16.7-.09,2.12-.15,4.24-.17,6.36Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M60.99,25.7c4.24-.18,8.43.18,12.57,1.09,2.09.5,4.11,1.17,6.07,2.04,2.05.9,3.86,2.16,5.41,3.76.3.38.58.77.85,1.17-1.92-1.08-3.96-1.91-6.12-2.5-4.32-1.11-8.7-1.74-13.15-1.89-5.41-.23-10.78.09-16.12.97-2.72.53-5.36,1.34-7.91,2.43-.62.33-1.24.65-1.84.97.76-1.17,1.71-2.16,2.86-2.96,2.19-1.5,4.57-2.61,7.14-3.35,3.35-.98,6.76-1.56,10.24-1.72Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M103.75,26.28c1.16-.16,2.11.22,2.84,1.12.64,1.04.61,2.06-.1,3.06-.2.24-.44.44-.7.61-1.53.69-3.07,1.37-4.61,2.04-.38.15-.77.28-1.17.39-.11.09-.19.19-.27.32,0,.77.24,1.45.73,2.04.29.28.59.53.9.78-1.35,1.23-1.62,2.67-.8,4.32.28.46.65.84,1.09,1.14-.75.57-1.19,1.32-1.31,2.26-1.73-.68-2.64-1.96-2.74-3.83-.19-2.49-.37-4.98-.53-7.48.06-.89.08-1.78.05-2.67.18-.77.61-1.36,1.29-1.77,1.78-.79,3.56-1.55,5.34-2.31Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M107.73,26.67c2.3.3,4.59.6,6.89.9,1.21.16,1.87.84,1.99,2.04-.12,1.31-.83,2-2.16,2.06-2.2-.25-4.39-.54-6.58-.87.52-1.02.63-2.09.32-3.2-.13-.33-.28-.63-.46-.92Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M51.08,48.56c-.66-.05-1.32-.06-1.99-.05v-6.02c1.29-1.06,2.2-2.39,2.74-3.98.79-2.34,1.25-4.76,1.38-7.23,6.35-.8,12.71-.84,19.08-.12.66.1,1.33.2,1.99.29.15,1.96.45,3.89.9,5.8.37,1.45.98,2.79,1.8,4.03.23.32.49.61.75.9.25.22.52.42.8.61.02,1.91.05,3.82.07,5.73-.65,0-1.3,0-1.94.02-1.31,1.17-2.84,1.72-4.61,1.65-.6,0-1.11-.24-1.5-.68-4.45-.03-8.9-.03-13.35,0-.2.29-.48.47-.83.53-2.01.37-3.77-.12-5.29-1.48Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M51.62,31.57h.19v.29c-.15,2.42-.67,4.75-1.58,6.99-.28.64-.65,1.22-1.09,1.75-.05-2.84-.06-5.69-.05-8.54.83-.19,1.67-.35,2.52-.49Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M75.7,31.77c.93.14,1.85.32,2.77.53,0,2.88,0,5.76-.02,8.64-.59-.73-1.06-1.54-1.41-2.43-.77-2.18-1.21-4.43-1.33-6.75Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M106.67,32.06c2.43.31,4.85.63,7.28.95,1.17.17,1.82.84,1.94,2.01-.13,1.26-.82,1.96-2.09,2.09-3.63-.46-7.25-.92-10.87-1.38-.76-.11-1.33-.5-1.7-1.17,1.57-.72,3.16-1.42,4.76-2.09.25-.1.48-.24.68-.41Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M47.59,32.45c.06.5.1,1.02.1,1.55s-.01,1.04-.05,1.55c-1.54-.26-2.47.37-2.79,1.89-.05.4-.07.81-.07,1.21.04,1.09.13,2.17.24,3.25-.01.06-.03.13-.05.19-1.51-.5-2.9-1.22-4.17-2.16-1.83-1.54-1.81-3.06.05-4.56,1.6-1.13,3.35-1.97,5.24-2.52.5-.14,1-.28,1.5-.41Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M80.02,32.74c1.93.51,3.72,1.32,5.39,2.4.65.47,1.17,1.04,1.58,1.72.26.66.21,1.29-.15,1.89-.26.41-.58.77-.95,1.09-.99.74-2.05,1.35-3.2,1.82-.01-.07-.03-.15-.05-.22.14-1.25.2-2.5.17-3.76-.23-1.67-1.18-2.38-2.84-2.14-.01-.95,0-1.88.05-2.82Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M46.76,36.82c.28-.06.5.02.66.24.11.21.19.44.24.68.03,3.02.03,6.05,0,9.08-.02.32-.12.61-.29.87-.2.21-.36.17-.49-.1-.08-.16-.15-.32-.19-.49,0-1.69-.11-3.37-.34-5.05-.07-.92-.14-1.84-.19-2.77-.03-.52-.03-1.03,0-1.55.03-.43.24-.74.61-.92Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M80.4,36.82c.54-.08.87.15,1,.68.05.39.08.78.07,1.17-.12,2.11-.29,4.21-.51,6.31-.01.69-.03,1.39-.05,2.09-.31,1.03-.61,1.03-.92,0-.03-3.14-.03-6.28,0-9.42.04-.33.18-.6.41-.83Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M103.12,37.2c.55,0,1.1.03,1.65.12,3,.38,5.99.79,8.98,1.21,1.03.45,1.48,1.23,1.33,2.35-.34,1.04-1.06,1.57-2.16,1.6-3.32-.39-6.64-.83-9.95-1.29-1.32-.53-1.76-1.48-1.33-2.84.34-.58.84-.97,1.48-1.17Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M55.6,39.73c.69-.09,1.19.19,1.48.83.11,1.07-.36,1.6-1.43,1.58-.75-.26-1.05-.79-.9-1.58.16-.41.44-.69.85-.83Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M71.38,39.73c1.1-.05,1.6.46,1.48,1.55-.26.65-.73.93-1.43.85-.72-.26-1.01-.77-.9-1.53.16-.41.45-.7.85-.87Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M103.36,42.74c.28,0,.55,0,.83.02,2.9.37,5.8.76,8.69,1.17,1.14.43,1.61,1.25,1.43,2.45-.36,1.01-1.08,1.53-2.16,1.55-2.95-.37-5.89-.76-8.83-1.14-1.35-.44-1.86-1.35-1.53-2.74.33-.68.85-1.12,1.58-1.31Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M105.6,48.71c.77-.03,1.48.16,2.14.56,1.03.7,1.89,1.57,2.6,2.6,1.44,2.18,2.58,4.51,3.45,6.99.51,1.49.98,3,1.38,4.51-1.76,1.45-3.78,2.26-6.07,2.45-3.98.14-7.17-1.35-9.59-4.49-.36-.52-.68-1.08-.97-1.65.8-2.72,1.93-5.29,3.4-7.72.5-.78,1.07-1.5,1.72-2.16.56-.53,1.21-.89,1.94-1.09Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M48.95,49.87c.55,0,1.1,0,1.65.02,1.75,1.37,3.72,1.87,5.92,1.5.46-.12.88-.31,1.26-.58,4.06-.03,8.12-.03,12.18,0,.52.39,1.1.62,1.75.68,1.66.14,3.21-.2,4.66-1.02.28-.17.53-.36.78-.58.52-.02,1.03-.03,1.55-.02-.09,1.5-.48,2.9-1.19,4.22-.62,2.83-1.46,5.6-2.52,8.3-.2.41-.41.82-.63,1.21-.76-.1-1.48.04-2.16.41-.31.19-.6.4-.87.63-.83.87-1.66,1.73-2.52,2.57-.28.23-.58.42-.92.56-.21-.14-.41-.31-.58-.51-.8-.47-1.66-.69-2.6-.66-1.14.03-2.25.23-3.33.61-.29.12-.56.25-.83.41-1.09-1.47-2.45-2.61-4.08-3.42-.96-.41-1.96-.59-3.01-.53-.3-.48-.56-.97-.8-1.48-1.02-2.64-1.84-5.34-2.48-8.11-.69-1.33-1.11-2.73-1.24-4.22Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M56.08,52.16h15.63c.1,3.78-1.57,6.45-5,7.99-3.43,1.14-6.36.38-8.81-2.26-1.34-1.67-1.95-3.58-1.82-5.73Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M57.44,53.52h12.82c-.34,2.61-1.73,4.42-4.17,5.41-2.78.86-5.16.23-7.16-1.87-.87-1.02-1.36-2.2-1.48-3.54Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M108.07,57.98c.73-.04,1.2.28,1.43.97.07.73-.25,1.2-.95,1.43-.78.06-1.25-.28-1.43-1.04-.02-.68.3-1.14.95-1.36Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M97.93,61.43c2.16,3.27,5.21,5.17,9.13,5.7,3.08.26,5.88-.5,8.4-2.26,1.31,5.5,1.83,11.09,1.58,16.75-.43,4.08-1.4,8.03-2.91,11.84-1.9,4.73-4.25,9.21-7.04,13.45-.02.04-.03.09-.02.15,2.96.22,5.6,1.25,7.91,3.08,2.18,1.83,3.39,4.17,3.64,7.01-.91.1-1.82.04-2.72-.17-2.26-.54-4.51-1.13-6.75-1.75-1.06-.25-2.14-.42-3.23-.51-.95.04-1.87.18-2.79.41-2.31.61-4.63,1.2-6.94,1.8-.49.09-.97.17-1.46.24-.48.04-.96.03-1.43-.02.05-1.6.51-3.07,1.36-4.42,1.47-2.19,3.43-3.77,5.9-4.73.72-.26,1.45-.49,2.18-.68.02-.02.04-.04.05-.07-3.76-5.59-6.28-11.71-7.55-18.35-.46-2.83-.61-5.68-.44-8.54.33-6.44,1.37-12.75,3.13-18.93Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M117.1,65.84c1.84.71,3.6,1.58,5.29,2.6.69.4,1.3.91,1.82,1.53.56,1.06.89,2.19.97,3.4.07,1.36,0,2.72-.19,4.08-.41,2.46-1,4.89-1.75,7.28-.77,2.41-1.54,4.82-2.31,7.23-.27.02-.53-.02-.78-.12-1.2-.58-2.27-1.33-3.23-2.26.18-.88.39-1.75.63-2.62.85-3.74,1.13-7.53.83-11.36-.18-3.29-.62-6.54-1.29-9.76Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M74.34,66.33h.24c.19,1.79.56,3.53,1.09,5.24.11.25.22.5.32.75-.36.23-.74.44-1.14.61-.17-.24-.3-.5-.39-.78-.63-1.84-1-3.73-1.14-5.66.34-.05.68-.11,1.02-.17Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M53.32,66.43c.44.04.87.09,1.31.15-.18,1.61-.48,3.19-.9,4.76-.21.64-.46,1.25-.75,1.84-.4-.18-.79-.4-1.17-.63.42-.98.76-1.98,1-3.01.2-1.03.37-2.07.51-3.11Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M94.09,72.59s.05.1.05.17c-.44,2.97-.69,5.96-.75,8.96-1.2.85-2.49,1.55-3.86,2.11-.23.09-.48.15-.73.17-.14-1.48.05-2.92.56-4.32.83-2.16,2.02-4.1,3.54-5.83.39-.43.79-.85,1.19-1.26Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M47.25,75.84h1.31c-.01.11,0,.2.05.29.07,1.56.51,3,1.33,4.32,1.4,2.09,3.23,3.67,5.51,4.73,4.67,2.1,9.46,2.42,14.37.97,2.59-.78,4.83-2.11,6.72-4,1.37-1.45,2.23-3.16,2.57-5.15.04-.39.07-.78.07-1.17h1.36c-.09,2.63-1,4.93-2.74,6.89-2.24,2.39-4.95,4.01-8.13,4.88-4.65,1.22-9.21.98-13.69-.73-2.73-1.09-4.99-2.79-6.77-5.12-1.26-1.77-1.92-3.74-1.97-5.92Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M42.78,76.62s.09,0,.12.05c3.03,8.57,6.04,17.15,9.03,25.73.06,1.62-.66,2.74-2.16,3.37-1.72.65-3.31.43-4.76-.68-.38-.33-.66-.72-.85-1.19-2.97-8.44-5.93-16.88-8.91-25.31.02-.04.05-.08.1-.1,2.49-.59,4.97-1.21,7.43-1.87Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M84.92,76.62c1.28.33,2.55.66,3.83.97-.54,1.17-.93,2.38-1.19,3.64-.23,1.22-.22,2.45.02,3.66.28.32.63.48,1.07.46.57-.04,1.12-.17,1.65-.39.01.02.03.05.05.07-2.3,6.42-4.6,12.83-6.92,19.25-.78,1.11-1.85,1.72-3.23,1.82-1.5.11-2.75-.38-3.76-1.48-.56-.74-.74-1.57-.53-2.48,2.99-8.52,5.99-17.03,9-25.53Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M51.57,97.25c8.22-.03,16.42,0,24.61.1-.56,1.55-1.1,3.1-1.63,4.66-.25,1.9.4,3.39,1.97,4.49,1.5.93,3.13,1.19,4.85.78,1.23-.34,2.25-1.01,3.03-2.01.2-.29.36-.59.49-.92.85-2.36,1.68-4.72,2.5-7.09h.34c1.03,11.84,2.05,23.69,3.06,35.53v.24h-53.88v-.24c1-11.84,2.02-23.69,3.06-35.53.16-.01.31,0,.46.05.84,2.39,1.68,4.79,2.52,7.18.53,1.13,1.36,1.95,2.5,2.45,1.63.67,3.26.68,4.9.05,2.14-.96,3.1-2.6,2.89-4.93-.53-1.61-1.09-3.21-1.67-4.81Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M47.59,100.16c1.54-.14,2.53.52,2.99,1.99.13,1.48-.51,2.45-1.92,2.89-1.13.17-2-.21-2.65-1.14-.64-1.3-.41-2.41.7-3.33.28-.18.57-.32.87-.41Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M79.14,100.16c1.43-.15,2.4.45,2.89,1.8.26,1.42-.27,2.41-1.58,2.99-1.51.37-2.57-.16-3.18-1.58-.31-1.63.31-2.69,1.87-3.2Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M52.01,106.13h23.69c0,6.7,0,13.4-.02,20.1-.32,2.21-1.54,3.66-3.66,4.34-.28.04-.55.09-.83.15-4.92.03-9.84.03-14.76,0-2.51-.47-3.98-1.97-4.39-4.49-.02-6.7-.03-13.4-.02-20.1Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M74.34,107.49c0,6.25,0,12.49-.02,18.74-.33,1.73-1.35,2.78-3.08,3.13-4.94.03-9.87.03-14.81,0-1.9-.43-2.92-1.62-3.06-3.57v-18.3h20.97Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"})]}));I.displayName="SealogsMaintenanceEmptyStateIcon";var E=t(51927),Z=t(50058),A=t(84340);function O({maintenanceTasks:e,pathname:s,setVesselTab:t,scrollToTabs:r}){let i=(0,c.useSearchParams)(),n=(0,f.d)(),[d,u]=(0,o.useState)(5);(0,Z.k)();let m=(0,o.useRef)(null),x=e?.filter(e=>!("Completed"===e.status||"Save_As_Draft"===e.status))||[],p=n?x.slice(0,d):x.slice(0,5),g=x&&x.length>d;return(0,l.jsxs)(S.Zb,{ref:m,children:[(0,l.jsxs)("div",{className:"lg:flex flex-col lg:justify-between ",children:[(0,l.jsxs)("div",{className:"flex py-3 items-baseline  gap-2 phablet:gap-4",children:[l.jsx(T.y,{className:"h-12 w-12 ring-1 p-1 rounded-full"}),l.jsx(h.default,{onClick:()=>t("maintenance"),href:"",children:l.jsx(D.H1,{children:"Maintenance"})})]}),x.length>0?l.jsx(l.Fragment,{children:l.jsx("div",{className:"pt-0 phablet:pt-1",children:l.jsx(C.wQ,{columns:[{accessorKey:"title",header:"",cell:({row:e})=>{let t=e.original;return l.jsx("div",{className:"flex items-center h-full",children:l.jsx(h.default,{href:`/maintenance?taskID=${t.id}&redirect_to=${s}?${i.toString()}`,className:`${"High"===t.severity?"group-hover:text-destructive":""} hover:text-primary`,children:t.name})})}},{accessorKey:"assignedToID",header:"",breakpoint:"standard",cellAlignment:"left",cell:({row:e})=>{let s=e.original;if(!s.assignedTo||!s.assignedTo.id||"0"===s.assignedTo.id)return l.jsx("div",{className:"w-6 text-center lg:hidden",children:"-"});let t={id:s.assignedTo.id,name:s.assignedTo.name,firstName:s.assignedTo.name.split(" ")[0]||"",surname:s.assignedTo.name.split(" ").slice(1).join(" ")||""},a=s.assignedTo.name;return(0,l.jsxs)("div",{className:"flex items-center lg:hidden gap-2.5",children:[(0,l.jsxs)(S.u,{children:[l.jsx(S.aJ,{children:l.jsx(S.qE,{size:"sm",variant:"secondary",children:l.jsx(S.Q5,{className:"text-sm",children:(0,S.xE)(t.firstName,t.surname)})})}),l.jsx(S._v,{children:(0,l.jsxs)(h.default,{href:`/crew/info?id=${t.id}`,className:"hover:underline flex items-center gap-2",children:[a," ",l.jsx(E.Z,{size:16})]})})]}),l.jsx(h.default,{href:`/crew/info?id=${s.assignedTo.id}`,className:"hover:underline hidden tablet-md:block",children:s.assignedTo.name})]})}},{accessorKey:"due",header:"",cellAlignment:"right",cell:({row:e})=>{let s=e?.original;if(!s)return l.jsx("div",{children:"-"});let t=s.isOverDue?.status,a=s.isOverDue?.day;return l.jsx(l.Fragment,{children:"High"===t?l.jsx("div",{className:"flex-1 flex justify-end",children:l.jsx("div",{className:` items-end w-fit
                            ${"High"===t?"alert whitespace-nowrap":""}
                            `,children:-1*a+" days ago"})}):l.jsx(A.OE,{maintenanceCheck:s})})}}],data:p,showToolbar:!1,pageSize:n?p.length:5,className:"border-0 shadow-none"})})}):(0,l.jsxs)("div",{className:"flex justify-between items-center gap-2 p-2 pt-4",children:[l.jsx("div",{children:l.jsx(I,{})}),l.jsx(D.P,{children:"Holy mackerel! You are up to date with all your maintenance. Only thing left to do is, to go fishing"})]})]}),x.length>5&&l.jsx("div",{className:"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center",children:n&&g?(0,l.jsxs)(a.Button,{variant:"outline",onClick:()=>u(e=>e+40),className:"text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto",children:["View More",l.jsx("span",{className:"hidden md:inline-block",children:"\xa0maintenance tasks\xa0"})]}):n&&!g&&x.length>5?(0,l.jsxs)(a.Button,{variant:"outline",onClick:()=>{u(5),m.current?.scrollIntoView({behavior:"smooth",block:"start"})},className:"text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto",children:["View Less",l.jsx("span",{className:"hidden md:inline-block",children:"\xa0maintenance tasks\xa0"})]}):(0,l.jsxs)(h.default,{href:"#",onClick:e=>{e.preventDefault(),t("maintenance"),r?.()},className:"hidden md:block text-accent-foreground uppercase hover:text-primary text-xs",children:["View all",l.jsx("span",{className:"hidden md:inline-block",children:"\xa0maintenance tasks\xa0"})]})})]})}var $=t(29428),R=t(94575);function P({trainingSessionDuesSummary:e,setVesselTab:s,scrollToTabs:t}){let r=(0,f.d)(),[i,n]=(0,o.useState)(5),d=(0,o.useRef)(null),c=r?e?.slice(0,i)||[]:e?.slice(0,5)||[],u=e&&e.length>i;return(0,l.jsxs)(S.Zb,{ref:d,children:[(0,l.jsxs)("div",{className:"flex py-3 items-baseline gap-2 phablet:gap-4",children:[l.jsx($._,{className:"h-12 w-12 ring-1 p-1 rounded-full"}),l.jsx(h.default,{href:"/vessel",children:l.jsx(D.H1,{children:"Training / drills"})})]}),e&&e.length>0?(0,l.jsxs)(l.Fragment,{children:[l.jsx("div",{className:"pt-0 phablet:pt-1",children:l.jsx(C.wQ,{columns:[{accessorKey:"title",header:"",cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"flex items-center h-full",children:l.jsx("div",{className:"flex flex-col hover:text-primary",children:l.jsx("span",{children:s.trainingType.title},s.trainingType.id)})})}},{accessorKey:"due",header:"",cellAlignment:"right",cell:({row:e})=>{let s=new Date(e.original.dueDate),t=new Date;s.setHours(0,0,0,0),t.setHours(0,0,0,0);let a=Math.floor((t.getTime()-s.getTime())/864e5);return l.jsx("div",{className:"flex justify-between items-center text-nowrap",children:a>0?(0,l.jsxs)("div",{className:"alert",children:[a+" ","days ago"]}):(0,l.jsxs)("div",{className:"p-2 text-foreground",children:["Due -"," "+-1*a+" ","days"]})})}}],data:c,showToolbar:!1,pageSize:r?c.length:5,className:"border-0 shadow-none"})}),e.length>5&&l.jsx("div",{className:"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center",children:r&&u?(0,l.jsxs)(a.Button,{variant:"outline",onClick:()=>n(e=>e+20),className:"text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto",children:["View More",l.jsx("span",{className:"hidden md:inline-block",children:"\xa0crew training\xa0"})]}):r&&!u&&e.length>5?(0,l.jsxs)(a.Button,{variant:"outline",onClick:()=>{n(5),d.current?.scrollIntoView({behavior:"smooth",block:"start"})},className:"text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto",children:["View Less",l.jsx("span",{className:"hidden md:inline-block",children:"\xa0crew training\xa0"})]}):(0,l.jsxs)(h.default,{href:"#",onClick:e=>{e.preventDefault(),s("crew_training"),t?.()},className:"hidden md:block text-accent-foreground uppercase hover:text-primary text-xs",children:["View all",l.jsx("span",{className:"hidden md:inline-block",children:"\xa0crew training\xa0"})]})})]}):(0,l.jsxs)("div",{className:"flex justify-between items-center gap-2 p-2 pt-4",children:[l.jsx("div",{children:l.jsx(R.I,{})}),l.jsx("p",{className:"  ",children:"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!"})]})]})}var L=t(81257),W=t(53294),B=t(81311),F=t(53363),_=t(15580);function V({crewInfo:e,setVesselTab:s,vesselId:t,pathname:r}){let i=(0,f.d)(),[n,d]=(0,o.useState)(5),[c,u]=(0,o.useState)(!1),m=(0,o.useRef)(null),x=i?e?.slice(0,n)||[]:e?.slice(0,5)||[],p=e&&e.length>n,g=e&&e.length>5;return l.jsx(S.Zb,{ref:m,children:(0,l.jsxs)("div",{className:"lg:flex flex-col lg:justify-between",children:[(0,l.jsxs)("div",{className:"flex py-3 items-baseline gap-2 phablet:gap-4",children:[l.jsx("div",{className:"h-12 w-12 ring-1 p-1 rounded-full",children:l.jsx(L.K,{className:"h-full w-full"})}),l.jsx(h.default,{onClick:()=>s("crew"),href:"",children:l.jsx(D.H1,{children:"Crew"})})]}),e&&e.length>0?(0,l.jsxs)(l.Fragment,{children:[l.jsx("div",{className:"pt-0 phablet:pt-1",children:l.jsx(C.wQ,{columns:[{accessorKey:"title",header:"",cell:({row:e})=>{let s=e.original;return(0,l.jsxs)("div",{className:"flex gap-2.5 items-center",children:[l.jsx(S.qE,{size:"sm",variant:s.trainingStatus?.label&&"Good"!==s.trainingStatus.label?"destructive":"success",children:l.jsx(S.Q5,{children:(0,S.xE)(s.firstName,s.surname)})}),(0,l.jsxs)("div",{className:"grid min-w-32",children:[(0,l.jsxs)(h.default,{href:`/crew/info?id=${s.id}`,className:"items-center truncate pl-2 text-nowrap",children:[s.firstName||"--",(0,l.jsxs)("span",{className:"hidden md:flex",children:["\xa0",s.surname||"--"]})]}),l.jsx("div",{className:"text-sm text-muted-foreground pl-2",children:s.primaryDuty?.title||"No duty assigned"})]})]})}},{accessorKey:"trainingStatus",header:"",cellAlignment:"right",cell:({row:e})=>{let s=e.original;return l.jsx(l.Fragment,{children:s.trainingStatus?.label&&"Good"!==s.trainingStatus.label?(0,l.jsxs)(_.Popover,{triggerType:"hover",children:[l.jsx(_.PopoverTrigger,{asChild:!0,children:l.jsx(W.Z,{strokeWidth:1,className:"h-9 w-9 text-destructive cursor-pointer"})}),l.jsx(_.PopoverContent,{children:l.jsx("div",{className:"bg-background rounded p-2",children:l.jsx("div",{className:"text-xs whitespace-nowrap font-medium focus:outline-none inline-block rounded",children:s.trainingStatus?.dues?.map((e,s)=>l.jsx("div",{children:`${e.trainingType?.title||"Unknown"} - ${e.status?.label||"Unknown"}`},s))})})})]}):(0,l.jsxs)(S.u,{children:[l.jsx(S.aJ,{children:l.jsx(S.Ct,{variant:"success",type:"circle",children:l.jsx(B.Z,{className:"h-5 w-5"})})}),l.jsx(S._v,{children:l.jsx("p",{children:"Training is up to date"})})]})})}}],data:x,showToolbar:!1,pageSize:i?x.length:5,className:"border-0 shadow-none"})}),i&&g&&l.jsx("div",{className:"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center",children:l.jsx(a.Button,{variant:"outline",className:"text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto",onClick:()=>{p?(d(n+20),u(!0)):(d(5),u(!1),m.current?.scrollIntoView({behavior:"smooth",block:"start"}))},children:p?"View More":"View Less"})})]}):(0,l.jsxs)("div",{className:"flex justify-between items-center gap-2 p-2 pt-4",children:[l.jsx("div",{children:l.jsx(F.Z,{className:"h-12 w-12 text-muted-foreground"})}),l.jsx("div",{className:"text-center",children:l.jsx("p",{className:"text-muted-foreground",children:"No crew assigned"})})]})]})})}var q=t(94446),H=t(97428);let z=(0,H.Z)("Package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]);var K=t(87175);function U({inventories:e,setVesselTab:s,vesselId:t,pathname:r}){let i=(0,f.d)(),n=(0,c.useSearchParams)(),[d,m]=(0,o.useState)(5),[x,p]=(0,o.useState)(!1),g=(0,o.useRef)(null),v=i?e?.slice(0,d)||[]:e?.slice(0,5)||[],j=e&&e.length>d,y=e&&e.length>5;return l.jsx(S.Zb,{ref:g,children:(0,l.jsxs)("div",{className:"lg:flex flex-col lg:justify-between",children:[(0,l.jsxs)("div",{className:"flex py-3 items-baseline gap-2 phablet:gap-4",children:[l.jsx("div",{className:"h-12 w-12 ring-1 p-1 rounded-full",children:l.jsx(q.A,{className:"h-full w-full"})}),l.jsx(h.default,{onClick:()=>s("inventory"),href:"",children:l.jsx(D.H1,{children:"Inventory"})})]}),e&&e.length>0?(0,l.jsxs)(l.Fragment,{children:[l.jsx("div",{className:"pt-0 phablet:pt-1",children:l.jsx(C.wQ,{columns:[{accessorKey:"item",header:"",cellAlignment:"left",cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"flex items-center h-full",children:(0,l.jsxs)(h.default,{href:`/inventory/view/?id=${s.id}&redirect_to=${r}?${n.toString()}&tab=inventory`,className:"hover:text-primary",children:[s.quantity," x ",s.item]})})}},{accessorKey:"categories",header:"",cellAlignment:"left",breakpoint:"tablet-sm",cell:({row:e})=>{let s=e.original;return(0,l.jsxs)("div",{className:"flex gap-2 items-center",children:[s.categories?.nodes?.slice(0,2).map((e,s)=>l.jsx(K.C,{type:"normal",variant:"outline",className:"font-normal",children:e.name},String(s))),s.categories?.nodes?.length>2&&(0,l.jsxs)(S.J2,{children:[l.jsx(S.CM,{asChild:!0,children:(0,l.jsxs)(a.Button,{variant:"outline",className:"!p-2 bg-transparent",children:["+"," ",s.categories.nodes.length-2," ","more"]})}),l.jsx(S.yk,{className:"w-80",children:(0,l.jsxs)("div",{className:"space-y-2",children:[l.jsx(D.P,{className:"font-medium text-sm",children:"All Categories"}),l.jsx("div",{className:"flex flex-wrap gap-2",children:s.categories.nodes.map((e,s)=>l.jsx(K.C,{type:"normal",variant:"outline",className:"font-normal",children:e.name},String(s)))})]})})]})]})}},{accessorKey:"maintenance",header:"",cellAlignment:"right",cell:({row:e})=>{let s=(e=>{let s=e.componentMaintenanceChecks?.nodes||[];if(0===s.length)return null;let t=s.filter(e=>!e?.archived).filter(e=>{let s=(0,u.AT)(e);return"Completed"!==e.status&&"Save_As_Draft"!==e.status&&"Completed"!==s.status&&"Upcoming"!==s.status});return t.length>0?{type:"overdue",count:t.length}:{type:"good"}})(e.original);return s?l.jsx("div",{className:"flex justify-end",children:l.jsx(K.C,{variant:"overdue"===s.type?"destructive":"good"===s.type?"success":"secondary",children:"overdue"===s.type?s.count:l.jsx(B.Z,{className:"h-5 w-5"})})}):null}}],data:v,showToolbar:!1,pageSize:i?v.length:5,className:"border-0 shadow-none"})}),i&&y&&l.jsx("div",{className:"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center",children:l.jsx(a.Button,{variant:"outline",className:"text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto",onClick:()=>{j?(m(d+20),p(!0)):(m(5),p(!1),g.current?.scrollIntoView({behavior:"smooth",block:"start"}))},children:j?"View More":"View Less"})})]}):(0,l.jsxs)("div",{className:"flex justify-between items-center gap-2 p-2 pt-4",children:[l.jsx("div",{children:l.jsx(z,{className:"h-12 w-12 text-muted-foreground"})}),l.jsx("div",{className:"text-center",children:l.jsx("p",{className:"text-muted-foreground",children:"No inventory items"})})]})]})})}var G=t(36895),Y=t(56937),Q=t(35024),J=t(62861);function X({label:e,value:s,className:t}){return s||0===s?(0,l.jsxs)("div",{className:(0,Y.cn)("flex flex-col sm:flex-row sm:justify-between py-2",t),children:[l.jsx("span",{className:"text-sm font-medium text-muted-foreground",children:e}),l.jsx("span",{className:"text-sm font-semibold",children:s})]}):null}function ee({title:e,description:s,icon:t,children:a,className:r}){return(0,l.jsxs)(Q.Zb,{className:(0,Y.cn)("h-fit border-dashed",r),children:[(0,l.jsxs)(Q.Ol,{className:"pb-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[t&&l.jsx(t,{className:"h-5 w-5 text-primary"}),l.jsx(Q.ll,{className:"text-lg",children:e})]}),s&&l.jsx(Q.SZ,{children:s})]}),l.jsx(Q.aY,{className:"space-y-1",children:a})]})}function es({tank:e,extraDetails:s}){return(0,l.jsxs)(Q.Zb,{className:"border-l-4 border-dashed border-l-curious-blue-600",children:[l.jsx(Q.Ol,{className:"pb-3",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[l.jsx(Q.ll,{className:"text-base",children:e.title}),e.identifier&&l.jsx(K.C,{variant:"secondary",className:"text-xs",children:e.identifier})]})}),(0,l.jsxs)(Q.aY,{className:"space-y-1",children:[l.jsx(X,{label:"Capacity",value:e.capacity}),s]})]})}function et({vessel:e,engineList:s,fuelTankList:t,waterTankList:a,sewageSystemList:r}){let i=e?.vesselSpecifics??{},n=[{label:"Authority No. (MNZ, AMSA)",value:e?.registration},{label:"Transit identifier",value:e?.transitID},{label:"MMSI",value:e?.mmsi},{label:"Call sign",value:e?.callSign},{label:"Primary harbor",value:i.primaryHarbour}],d=[{label:"Vessel beam",value:i.beam},{label:"Length overall",value:i.overallLength},{label:"Draft",value:i.draft}],o=[{label:"Date of build",value:(0,k.p6)(i.dateOfBuild)},{label:"Hull color",value:i.hullColor},{label:"Hull construction",value:i.hullConstruction}],c=[{label:"Minimum required crew",value:e?.minCrew},{label:"Max passengers allowed",value:e?.maxPax},{label:"Max people on board",value:e?.maxPOB}],h=[{label:"Port of registry",value:i.portOfRegistry},{label:"Registered length",value:i.registeredLength},{label:"Tonnage length",value:i.tonnageLength},{label:"Gross tonnage",value:i.grossTonnage},{label:"Net tonnage",value:i.netTonnage},{label:"Max cargo load",value:i.maxCargoLoad},{label:"Capacity of lifting",value:i.capacityOfLifting},{label:"Load line length",value:i.loadLineLength},{label:"Special limitations",value:i.specialLimitations},{label:"Operating area limits",value:i.operatingAreaLimits},{label:"Fishing Number",value:i.fishingNumber},{label:"Carriers dangerous goods",value:null==i.carriesDangerousGoods?void 0:0===i.carriesDangerousGoods?"No":"Yes"},{label:"Design approval number",value:i.designApprovalNumber}];return(0,l.jsxs)("div",{className:"space-y-6 p-6",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[l.jsx("h1",{className:"text-3xl font-bold tracking-tight",children:e?.title||l.jsx(J.O,{className:"h-8 w-64"})}),e?.vesselTypeDescription&&l.jsx(K.C,{type:"normal",variant:"outline",className:"text-sm",children:e.vesselTypeDescription})]}),l.jsx(ee,{title:"Basic Information",description:"Essential vessel identification details",children:n.map(({label:e,value:s})=>l.jsx(X,{label:e,value:s},e))}),(0,l.jsxs)("div",{className:"grid gap-6 lg:grid-cols-2",children:[l.jsx(ee,{title:"Dimensions",description:"Physical measurements and specifications",children:d.map(({label:e,value:s})=>l.jsx(X,{label:e,value:s},e))}),l.jsx(ee,{title:"Build Details",description:"Construction and hull information",children:o.map(({label:e,value:s})=>l.jsx(X,{label:e,value:s},e))})]}),l.jsx(ee,{title:"Crew & Passenger Limits",description:"Maximum capacity and crew requirements",children:l.jsx("div",{className:"grid gap-4 sm:grid-cols-3",children:c.map(({label:e,value:s})=>(0,l.jsxs)("div",{className:"text-center p-3 bg-muted/50 rounded-lg",children:[l.jsx("div",{className:"text-2xl font-bold text-primary",children:s||"—"}),l.jsx("div",{className:"text-xs text-muted-foreground mt-1",children:e})]},e))})}),(0,l.jsxs)("div",{className:"w-full grid gap-6 lg:grid-cols-2",children:[s?.length>0&&(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("div",{className:"flex items-center gap-2",children:(0,l.jsxs)("h2",{className:"text-xl font-semibold",children:["Engine Detail",s.length>1?"s":""]})}),l.jsx("div",{className:"grid gap-4",children:s.map(e=>(0,l.jsxs)(Q.Zb,{className:"border-l-4 border-dashed border-l-fire-bush-600",children:[l.jsx(Q.Ol,{className:"pb-3",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[l.jsx(Q.ll,{className:"text-base",children:e.title}),e.identifier&&l.jsx(K.C,{variant:"secondary",className:"text-xs",children:e.identifier})]})}),(0,l.jsxs)(Q.aY,{className:"space-y-1",children:[l.jsx(X,{label:"Position on vessel",value:e.positionOnVessel}),l.jsx(X,{label:"Engine type",value:e.type}),l.jsx(X,{label:"Current engine hours",value:e.currentHours}),l.jsx(X,{label:"Engine make & model",value:`${e.make??""} ${e.model??""}`.trim()||void 0}),l.jsx(X,{label:"Drive type",value:e.driveType}),l.jsx(X,{label:"Genset kVA",value:e.kVA}),l.jsx(X,{label:"Engine kW",value:e.kW}),l.jsx(X,{label:"Max power",value:e.maxPower})]})]},e.id))})]}),t?.length>0&&(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("div",{className:"flex items-center gap-2",children:(0,l.jsxs)("h2",{className:"text-xl font-semibold",children:["Fuel Tank",t.length>1?"s":""]})}),l.jsx("div",{className:"grid gap-4",children:t.map(e=>l.jsx(es,{tank:e,extraDetails:(0,l.jsxs)(l.Fragment,{children:[l.jsx(X,{label:"Safe fuel level",value:e.safeFuelCapacity}),l.jsx(X,{label:"Current fuel level",value:e.currentLevel}),l.jsx(X,{label:"Fuel type",value:e.fuelType})]})},e.id))})]})]}),a?.length>0&&(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("div",{className:"flex items-center gap-2",children:(0,l.jsxs)("h2",{className:"text-xl font-semibold",children:["Water Tank",a.length>1?"s":""]})}),l.jsx("div",{className:"grid gap-4 lg:grid-cols-2",children:a.map(e=>l.jsx(es,{tank:e},e.id))})]}),r?.length>0&&(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("h2",{className:"text-xl font-semibold",children:["Sullage System",r.length>1?"s":""]}),l.jsx("div",{className:"grid gap-4 lg:grid-cols-2",children:r.map(e=>l.jsx(es,{tank:e,extraDetails:l.jsx(X,{label:"Number of tanks",value:e.numberOfTanks})},e.id))})]}),l.jsx(ee,{title:"Additional Vessel Details",description:"Registry and operational specifications",children:h.map(({label:e,value:s})=>l.jsx(X,{label:e,value:s},e))})]})}var el=t(69748),ea=t(30905);let er=(e,s)=>{if(!e&&!s)return"??";let t=e?.charAt(0)?.toUpperCase()||"",l=s?.charAt(0)?.toUpperCase()||"";return`${t}${l}`||"??"},ei=(e,s)=>s?.find(s=>s.id===e),en=e=>({id:parseInt(e.id),assignedTo:{id:parseInt(e.assignedTo?.id||"0"),name:e.assignedTo?.name||""},basicComponent:{id:0,title:null},inventory:{id:parseInt(e.inventory?.id||"0"),item:e.inventory?.item||null},status:e.isOverDue?.status||"",recurringID:0,name:e.name,created:new Date().toISOString(),severity:e.severity,isOverDue:{status:e.isOverDue?.status||"",days:e.isOverDue?.days||"",ignore:!1,day:0},comments:null,workOrderNumber:null,startDate:new Date().toISOString(),expires:null,maintenanceCategoryID:0});function ed({maintenanceTasks:e,crewInfo:s}){let t=(0,c.usePathname)(),r=(0,c.useSearchParams)(),i=(0,f.d)(),[n,d]=(0,o.useState)(5),u=(0,C.wu)([{accessorKey:"title",header:({column:e})=>l.jsx(ea.u,{column:e,title:"Title"}),cell:({row:e})=>{let a=e.original;return(0,l.jsxs)(l.Fragment,{children:[l.jsx(Q.Zb,{className:"xs:hidden space-y-3 p-2.5 min-h-20 border-b shadow-none rounded-none bg-transparent",children:(0,l.jsxs)(Q.aY,{className:"p-0 space-y-3",children:[(0,l.jsxs)("div",{className:"flex gap-2.5 justify-between",children:[l.jsx(h.default,{href:`/maintenance?taskID=${a.id}&redirect_to=${t}?${r.toString()}%26tab=maintenance`,className:"text-base",children:a.name??`Task #${a.id} (No Name) - ${x()().format("DD/MM/YYYY")}`}),l.jsx("div",{children:l.jsx(A.OE,{maintenanceCheck:en(a)})})]}),a.assignedTo?.id&&"0"!==a.assignedTo.id&&(0,l.jsxs)("div",{className:"flex items-center gap-2.5",children:[l.jsx(el.Avatar,{variant:"secondary",className:"h-8 w-8",children:l.jsx(el.AvatarFallback,{className:"text-xs",children:ei(a.assignedToID,s)?er(ei(a.assignedToID,s)?.firstName,ei(a.assignedToID,s)?.surname):er(a.assignedTo.name.split(" ")[0],a.assignedTo.name.split(" ").slice(1).join(" "))})}),(0,l.jsxs)("div",{className:"flex flex-col",children:[l.jsx("span",{className:"text-xs text-muted-foreground",children:"Assigned to"}),l.jsx(h.default,{href:`/crew/info?id=${a.assignedTo.id}`,className:"hover:underline text-sm",children:a.assignedTo.name})]})]}),a.inventory?.id&&parseInt(a.inventory.id)>0&&(0,l.jsxs)("div",{className:"flex flex-col",children:[l.jsx("span",{className:"text-xs text-muted-foreground",children:"Inventory item"}),l.jsx(h.default,{href:`/inventory/view?id=${a.inventory.id}`,className:"hover:underline text-sm",children:a.inventory.item})]})]})}),l.jsx("div",{className:"hidden xs:block",children:l.jsx("div",{className:"flex items-center flex-nowrap gap-2",children:l.jsx(h.default,{href:`/maintenance?taskID=${a.id}&redirect_to=${t}?${r.toString()}%26tab=maintenance`,children:a.name??`Task #${a.id} (No Name) - ${x()().format("DD/MM/YYYY")}`})})})]})},sortingFn:(e,s)=>{let t=e?.original?.name||"",l=s?.original?.name||"";return t.localeCompare(l)}},{accessorKey:"assigned",header:({column:e})=>l.jsx(ea.u,{column:e,title:"Assigned to"}),cellAlignment:"left",breakpoint:"tablet-md",cell:({row:e})=>{let t=e.original,a=ei(t.assignedToID,s);return l.jsx(l.Fragment,{children:t.assignedTo?.id&&"0"!==t.assignedTo.id?(0,l.jsxs)("div",{className:"flex items-center gap-2.5",children:[l.jsx(el.Avatar,{variant:"secondary",className:"h-8 w-8",children:l.jsx(el.AvatarFallback,{className:"text-xs",children:a?er(a.firstName,a.surname):er(t.assignedTo.name.split(" ")[0],t.assignedTo.name.split(" ").slice(1).join(" "))})}),l.jsx(h.default,{href:`/crew/info?id=${t.assignedTo.id}`,className:"hover:underline hidden tablet-md:block",children:t.assignedTo.name})]}):l.jsx("span",{children:"-"})})},sortingFn:(e,s)=>{let t=e?.original?.assignedTo?.name||"",l=s?.original?.assignedTo?.name||"";return t.localeCompare(l)}},{accessorKey:"inventory",header:"Inventory item",cellAlignment:"left",breakpoint:"phablet",cell:({row:e})=>{let s=e.original;return l.jsx(l.Fragment,{children:s.inventory?.id&&parseInt(s.inventory.id)>0?l.jsx(h.default,{href:`/inventory/view?id=${s.inventory.id}`,className:"hover:underline",children:s.inventory.item}):l.jsx("span",{children:"-"})})}},{accessorKey:"status",header:({column:e})=>l.jsx(ea.u,{column:e,title:"Status"}),cellAlignment:"right",breakpoint:"phablet",cell:({row:e})=>{let s=e.original;return s?l.jsx(l.Fragment,{children:l.jsx(A.OE,{maintenanceCheck:en(s)})}):l.jsx("div",{children:"-"})},sortingFn:(e,s)=>{let t=e?.original?.isOverDue?.days||"",l=s?.original?.isOverDue?.days||"";return t.localeCompare(l)}}]),m=i?e?.slice(0,n)||[]:e||[];return l.jsx("div",{children:e?.length>0?(0,l.jsxs)(l.Fragment,{children:[l.jsx(C.wQ,{columns:u,data:m,showToolbar:!1}),i&&n<e.length&&l.jsx("div",{className:"flex justify-center mt-4",children:l.jsx(a.Button,{variant:"outline",onClick:()=>d(s=>Math.min(s+20,e.length)),className:"w-full max-w-sm",children:"View More"})})]}):l.jsx("div",{className:"flex justify-center items-center h-96",children:l.jsx("div",{className:"flex flex-col items-center",children:(0,l.jsxs)("div",{className:"flex justify-between items-center gap-2 p-2 pt-4",children:[l.jsx("div",{children:(0,l.jsxs)("svg",{className:"!w-[100px] h-auto",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 148.02 147.99",children:[l.jsx("path",{d:"M70.84.56c16-.53,30.66,3.59,43.98,12.35,12.12,8.24,21.1,19.09,26.92,32.55,6.14,14.85,7.38,30.11,3.74,45.78-3.92,15.59-11.95,28.57-24.1,38.96-13.11,10.9-28.24,16.66-45.39,17.28-16.75.33-31.88-4.39-45.39-14.17-13.29-9.92-22.34-22.84-27.16-38.76-4.03-14.16-3.9-28.29.39-42.38,5-15.45,14-27.97,27.01-37.6C42.77,5.97,56.1,1.31,70.84.56Z",fill:"#fefefe",fillRule:"evenodd",stroke:"#024450",strokeMiterlimit:"10",strokeWidth:"1.02px"}),l.jsx("path",{d:"M63.03,13.61c1.74.02,3.47.13,5.19.32,2.15.26,4.31.51,6.46.78,1.18.34,2.08,1.04,2.69,2.11.56,1,.85,2.06.87,3.2,1.5,2.89,2.99,5.79,4.47,8.69.09.17.19.32.32.46,1.72,1.08,3.12,2.48,4.2,4.2.42.79.72,1.63.9,2.5-.04.01-.07.04-.1.07.58,1.01.64,2.04.17,3.11-.47.88-1.1,1.62-1.92,2.21-1.17.81-2.44,1.45-3.79,1.92-.07.56-.13,1.13-.17,1.7,0,.86-.03,1.72-.1,2.57-.14.56-.42,1.04-.85,1.43-.38.3-.8.39-1.26.27-.01,1.92-.46,3.73-1.33,5.44-.59,2.66-1.36,5.27-2.33,7.82-.4,1.04-.96,1.99-1.67,2.84-.36-.12-.73-.2-1.12-.27-.28,0-.53.08-.78.22-.23.16-.45.33-.68.49-.83.87-1.67,1.73-2.52,2.57-.78.67-1.68,1.03-2.72,1.09-.09-.26-.18-.52-.27-.78-.26-.26-.58-.43-.95-.51-1.68-.23-3.27.06-4.76.87-.28.24-.56.48-.85.7-.95-1.87-2.36-3.27-4.25-4.2-.37-.14-.74-.25-1.12-.34-.42-.03-.84-.03-1.26,0-.19.06-.38.1-.58.1-.58-.66-1.04-1.39-1.38-2.21-1.11-2.73-1.98-5.53-2.62-8.4-.89-1.7-1.33-3.51-1.33-5.44-.97.14-1.64-.25-2.01-1.17-.12-.3-.2-.6-.24-.92-.01-.76-.03-1.52-.05-2.28-.02-.39-.07-.78-.15-1.17-1.41-.47-2.77-1.07-4.05-1.82-.82-.49-1.54-1.09-2.16-1.82-.66-.81-.93-1.73-.83-2.77.33-1.03.65-2.06.92-3.11.56-1.18,1.32-2.22,2.26-3.13,1.27-1.15,2.67-2.11,4.2-2.89,1.39-2.69,2.79-5.37,4.17-8.06.01-1.77.66-3.26,1.92-4.49.47-.39,1-.67,1.6-.83,3.29-.42,6.57-.79,9.85-1.09Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M63.17,14.97c2.44.07,4.86.25,7.28.56,1.3.16,2.59.33,3.88.49.85.26,1.5.78,1.92,1.58.43.87.64,1.79.63,2.77,1.18,2.31,2.37,4.62,3.57,6.92-3.88-1.88-7.97-3.04-12.28-3.5-5.82-.65-11.53-.15-17.14,1.5-1.08.33-2.13.73-3.16,1.19l-.05-.05c1.01-2.01,2.04-4.02,3.08-6.02,0-1.18.3-2.26.92-3.25.41-.57.95-.95,1.63-1.14,3.23-.44,6.47-.78,9.71-1.04Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M22.83,121.38c-.05.7-.06,1.42-.05,2.14h-1.31v-1.84c.04-6.98.54-13.92,1.48-20.82.54-4.01,1.44-7.94,2.67-11.8.83-2.63,2.05-5.06,3.64-7.28,1.23-1.49,2.67-2.74,4.32-3.74,0-.15-.03-.29-.12-.41,3.43-.91,6.85-1.76,10.29-2.55,2.46,6.94,4.9,13.88,7.33,20.82h25.63c2.42-6.97,4.87-13.93,7.35-20.87,1.78.46,3.56.91,5.34,1.36,1.34-2.25,3.04-4.21,5.1-5.87.78-4.96,2.07-9.78,3.88-14.47.65-1.62,1.43-3.17,2.33-4.66.76-1.21,1.68-2.27,2.79-3.18-1.36-.17-2.34-.88-2.94-2.11-.04-.09-.06-.19-.07-.29-2.47-.68-3.87-2.31-4.2-4.85-.2-2.64-.39-5.28-.58-7.91-.03-.54,0-1.09.07-1.63-.17-1.88.57-3.25,2.23-4.13,1.68-.73,3.36-1.46,5.05-2.18.39-.11.79-.17,1.19-.17,3.64.42,7.27.88,10.9,1.38,1.72.41,2.66,1.5,2.82,3.25-.02,1.36-.63,2.38-1.8,3.06,1.1,1.14,1.33,2.44.7,3.91-.33.64-.82,1.14-1.43,1.5,1.22,1.38,1.34,2.85.36,4.42-.31.42-.69.75-1.14,1,1.02,1.05,1.29,2.27.8,3.66-.77,1.59-2.04,2.3-3.81,2.11-.7-.09-1.39-.17-2.09-.24,1.17,1.13,2.15,2.4,2.94,3.81,1.95,3.61,3.36,7.43,4.22,11.46,2.2.83,4.31,1.85,6.33,3.03.89.53,1.66,1.2,2.31,2.01.7,1.3,1.09,2.69,1.17,4.17.08,2.03-.09,4.03-.53,6.02-.48,2.16-1.04,4.3-1.7,6.41-.79,2.37-1.56,4.75-2.33,7.14-.74.36-1.49.39-2.26.07-1.22-.53-2.31-1.25-3.28-2.16-1.78,5.28-4.16,10.26-7.14,14.95-.02.04-.03.09-.02.15,3.62.73,6.54,2.56,8.76,5.49,1.2,1.7,1.84,3.59,1.92,5.68,0,.23-.01.45-.02.68-.42.42-.93.64-1.53.66-1.25.03-2.48-.12-3.69-.44-2.04-.52-4.08-1.05-6.12-1.6-.88-.23-1.78-.37-2.69-.41-.84.03-1.68.16-2.5.36-1.96.52-3.91,1.04-5.87,1.55-.95.21-1.9.39-2.86.53-.49.03-.97.03-1.46,0-.49-.08-.9-.3-1.24-.66-.08-2.31.54-4.41,1.84-6.31,1.21-1.71,2.74-3.06,4.59-4.05.75-.38,1.51-.72,2.28-1.04-2.93-4.67-5.04-9.68-6.33-15.05-.58-2.67-.91-5.37-.97-8.11-.39.24-.79.48-1.19.7-.06.04-.1.1-.12.17-1.41,3.89-2.79,7.79-4.15,11.7h1.02c1.11,12.83,2.22,25.66,3.35,38.49h-56.89c1.1-12.83,2.22-25.66,3.35-38.49.39.01.78,0,1.17-.05-1.95-5.48-3.88-10.97-5.8-16.46-.03-.04-.08-.05-.12-.02-1.95,1.22-3.53,2.82-4.73,4.78-1.06,1.86-1.92,3.82-2.57,5.87-.84,2.72-1.51,5.49-1.99,8.3-.9,5.53-1.47,11.1-1.7,16.7-.09,2.12-.15,4.24-.17,6.36Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M60.99,25.7c4.24-.18,8.43.18,12.57,1.09,2.09.5,4.11,1.17,6.07,2.04,2.05.9,3.86,2.16,5.41,3.76.3.38.58.77.85,1.17-1.92-1.08-3.96-1.91-6.12-2.5-4.32-1.11-8.7-1.74-13.15-1.89-5.41-.23-10.78.09-16.12.97-2.72.53-5.36,1.34-7.91,2.43-.62.33-1.24.65-1.84.97.76-1.17,1.71-2.16,2.86-2.96,2.19-1.5,4.57-2.61,7.14-3.35,3.35-.98,6.76-1.56,10.24-1.72Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M103.75,26.28c1.16-.16,2.11.22,2.84,1.12.64,1.04.61,2.06-.1,3.06-.2.24-.44.44-.7.61-1.53.69-3.07,1.37-4.61,2.04-.38.15-.77.28-1.17.39-.11.09-.19.19-.27.32,0,.77.24,1.45.73,2.04.29.28.59.53.9.78-1.35,1.23-1.62,2.67-.8,4.32.28.46.65.84,1.09,1.14-.75.57-1.19,1.32-1.31,2.26-1.73-.68-2.64-1.96-2.74-3.83-.19-2.49-.37-4.98-.53-7.48.06-.89.08-1.78.05-2.67.18-.77.61-1.36,1.29-1.77,1.78-.79,3.56-1.55,5.34-2.31Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M107.73,26.67c2.3.3,4.59.6,6.89.9,1.21.16,1.87.84,1.99,2.04-.12,1.31-.83,2-2.16,2.06-2.2-.25-4.39-.54-6.58-.87.52-1.02.63-2.09.32-3.2-.13-.33-.28-.63-.46-.92Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M51.08,48.56c-.66-.05-1.32-.06-1.99-.05v-6.02c1.29-1.06,2.2-2.39,2.74-3.98.79-2.34,1.25-4.76,1.38-7.23,6.35-.8,12.71-.84,19.08-.12.66.1,1.33.2,1.99.29.15,1.96.45,3.89.9,5.8.37,1.45.98,2.79,1.8,4.03.23.32.49.61.75.9.25.22.52.42.8.61.02,1.91.05,3.82.07,5.73-.65,0-1.3,0-1.94.02-1.31,1.17-2.84,1.72-4.61,1.65-.6,0-1.11-.24-1.5-.68-4.45-.03-8.9-.03-13.35,0-.2.29-.48.47-.83.53-2.01.37-3.77-.12-5.29-1.48Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M51.62,31.57h.19v.29c-.15,2.42-.67,4.75-1.58,6.99-.28.64-.65,1.22-1.09,1.75-.05-2.84-.06-5.69-.05-8.54.83-.19,1.67-.35,2.52-.49Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M75.7,31.77c.93.14,1.85.32,2.77.53,0,2.88,0,5.76-.02,8.64-.59-.73-1.06-1.54-1.41-2.43-.77-2.18-1.21-4.43-1.33-6.75Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M106.67,32.06c2.43.31,4.85.63,7.28.95,1.17.17,1.82.84,1.94,2.01-.13,1.26-.82,1.96-2.09,2.09-3.63-.46-7.25-.92-10.87-1.38-.76-.11-1.33-.5-1.7-1.17,1.57-.72,3.16-1.42,4.76-2.09.25-.1.48-.24.68-.41Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M47.59,32.45c.06.5.1,1.02.1,1.55s-.01,1.04-.05,1.55c-1.54-.26-2.47.37-2.79,1.89-.05.4-.07.81-.07,1.21.04,1.09.13,2.17.24,3.25-.01.06-.03.13-.05.19-1.51-.5-2.9-1.22-4.17-2.16-1.83-1.54-1.81-3.06.05-4.56,1.6-1.13,3.35-1.97,5.24-2.52.5-.14,1-.28,1.5-.41Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M80.02,32.74c1.93.51,3.72,1.32,5.39,2.4.65.47,1.17,1.04,1.58,1.72.26.66.21,1.29-.15,1.89-.26.41-.58.77-.95,1.09-.99.74-2.05,1.35-3.2,1.82-.01-.07-.03-.15-.05-.22.14-1.25.2-2.5.17-3.76-.23-1.67-1.18-2.38-2.84-2.14-.01-.95,0-1.88.05-2.82Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M46.76,36.82c.28-.06.5.02.66.24.11.21.19.44.24.68.03,3.02.03,6.05,0,9.08-.02.32-.12.61-.29.87-.2.21-.36.17-.49-.1-.08-.16-.15-.32-.19-.49,0-1.69-.11-3.37-.34-5.05-.07-.92-.14-1.84-.19-2.77-.03-.52-.03-1.03,0-1.55.03-.43.24-.74.61-.92Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M80.4,36.82c.54-.08.87.15,1,.68.05.39.08.78.07,1.17-.12,2.11-.29,4.21-.51,6.31-.01.69-.03,1.39-.05,2.09-.31,1.03-.61,1.03-.92,0-.03-3.14-.03-6.28,0-9.42.04-.33.18-.6.41-.83Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M103.12,37.2c.55,0,1.1.03,1.65.12,3,.38,5.99.79,8.98,1.21,1.03.45,1.48,1.23,1.33,2.35-.34,1.04-1.06,1.57-2.16,1.6-3.32-.39-6.64-.83-9.95-1.29-1.32-.53-1.76-1.48-1.33-2.84.34-.58.84-.97,1.48-1.17Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M55.6,39.73c.69-.09,1.19.19,1.48.83.11,1.07-.36,1.6-1.43,1.58-.75-.26-1.05-.79-.9-1.58.16-.41.44-.69.85-.83Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M71.38,39.73c1.1-.05,1.6.46,1.48,1.55-.26.65-.73.93-1.43.85-.72-.26-1.01-.77-.9-1.53.16-.41.45-.7.85-.87Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M103.36,42.74c.28,0,.55,0,.83.02,2.9.37,5.8.76,8.69,1.17,1.14.43,1.61,1.25,1.43,2.45-.36,1.01-1.08,1.53-2.16,1.55-2.95-.37-5.89-.76-8.83-1.14-1.35-.44-1.86-1.35-1.53-2.74.33-.68.85-1.12,1.58-1.31Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M105.6,48.71c.77-.03,1.48.16,2.14.56,1.03.7,1.89,1.57,2.6,2.6,1.44,2.18,2.58,4.51,3.45,6.99.51,1.49.98,3,1.38,4.51-1.76,1.45-3.78,2.26-6.07,2.45-3.98.14-7.17-1.35-9.59-4.49-.36-.52-.68-1.08-.97-1.65.8-2.72,1.93-5.29,3.4-7.72.5-.78,1.07-1.5,1.72-2.16.56-.53,1.21-.89,1.94-1.09Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M48.95,49.87c.55,0,1.1,0,1.65.02,1.75,1.37,3.72,1.87,5.92,1.5.46-.12.88-.31,1.26-.58,4.06-.03,8.12-.03,12.18,0,.52.39,1.1.62,1.75.68,1.66.14,3.21-.2,4.66-1.02.28-.17.53-.36.78-.58.52-.02,1.03-.03,1.55-.02-.09,1.5-.48,2.9-1.19,4.22-.62,2.83-1.46,5.6-2.52,8.3-.2.41-.41.82-.63,1.21-.76-.1-1.48.04-2.16.41-.31.19-.6.4-.87.63-.83.87-1.66,1.73-2.52,2.57-.28.23-.58.42-.92.56-.21-.14-.41-.31-.58-.51-.8-.47-1.66-.69-2.6-.66-1.14.03-2.25.23-3.33.61-.29.12-.56.25-.83.41-1.09-1.47-2.45-2.61-4.08-3.42-.96-.41-1.96-.59-3.01-.53-.3-.48-.56-.97-.8-1.48-1.02-2.64-1.84-5.34-2.48-8.11-.69-1.33-1.11-2.73-1.24-4.22Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M56.08,52.16h15.63c.1,3.78-1.57,6.45-5,7.99-3.43,1.14-6.36.38-8.81-2.26-1.34-1.67-1.95-3.58-1.82-5.73Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M57.44,53.52h12.82c-.34,2.61-1.73,4.42-4.17,5.41-2.78.86-5.16.23-7.16-1.87-.87-1.02-1.36-2.2-1.48-3.54Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M108.07,57.98c.73-.04,1.2.28,1.43.97.07.73-.25,1.2-.95,1.43-.78.06-1.25-.28-1.43-1.04-.02-.68.3-1.14.95-1.36Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M97.93,61.43c2.16,3.27,5.21,5.17,9.13,5.7,3.08.26,5.88-.5,8.4-2.26,1.31,5.5,1.83,11.09,1.58,16.75-.43,4.08-1.4,8.03-2.91,11.84-1.9,4.73-4.25,9.21-7.04,13.45-.02.04-.03.09-.02.15,2.96.22,5.6,1.25,7.91,3.08,2.18,1.83,3.39,4.17,3.64,7.01-.91.1-1.82.04-2.72-.17-2.26-.54-4.51-1.13-6.75-1.75-1.06-.25-2.14-.42-3.23-.51-.95.04-1.87.18-2.79.41-2.31.61-4.63,1.2-6.94,1.8-.49.09-.97.17-1.46.24-.48.04-.96.03-1.43-.02.05-1.6.51-3.07,1.36-4.42,1.47-2.19,3.43-3.77,5.9-4.73.72-.26,1.45-.49,2.18-.68.02-.02.04-.04.05-.07-3.76-5.59-6.28-11.71-7.55-18.35-.46-2.83-.61-5.68-.44-8.54.33-6.44,1.37-12.75,3.13-18.93Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M117.1,65.84c1.84.71,3.6,1.58,5.29,2.6.69.4,1.3.91,1.82,1.53.56,1.06.89,2.19.97,3.4.07,1.36,0,2.72-.19,4.08-.41,2.46-1,4.89-1.75,7.28-.77,2.41-1.54,4.82-2.31,7.23-.27.02-.53-.02-.78-.12-1.2-.58-2.27-1.33-3.23-2.26.18-.88.39-1.75.63-2.62.85-3.74,1.13-7.53.83-11.36-.18-3.29-.62-6.54-1.29-9.76Z",fill:"#fefefe",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M74.34,66.33h.24c.19,1.79.56,3.53,1.09,5.24.11.25.22.5.32.75-.36.23-.74.44-1.14.61-.17-.24-.3-.5-.39-.78-.63-1.84-1-3.73-1.14-5.66.34-.05.68-.11,1.02-.17Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M53.32,66.43c.44.04.87.09,1.31.15-.18,1.61-.48,3.19-.9,4.76-.21.64-.46,1.25-.75,1.84-.4-.18-.79-.4-1.17-.63.42-.98.76-1.98,1-3.01.2-1.03.37-2.07.51-3.11Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M94.09,72.59s.05.1.05.17c-.44,2.97-.69,5.96-.75,8.96-1.2.85-2.49,1.55-3.86,2.11-.23.09-.48.15-.73.17-.14-1.48.05-2.92.56-4.32.83-2.16,2.02-4.1,3.54-5.83.39-.43.79-.85,1.19-1.26Z",fill:"#fdfdfd",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M47.25,75.84h1.31c-.01.11,0,.2.05.29.07,1.56.51,3,1.33,4.32,1.4,2.09,3.23,3.67,5.51,4.73,4.67,2.1,9.46,2.42,14.37.97,2.59-.78,4.83-2.11,6.72-4,1.37-1.45,2.23-3.16,2.57-5.15.04-.39.07-.78.07-1.17h1.36c-.09,2.63-1,4.93-2.74,6.89-2.24,2.39-4.95,4.01-8.13,4.88-4.65,1.22-9.21.98-13.69-.73-2.73-1.09-4.99-2.79-6.77-5.12-1.26-1.77-1.92-3.74-1.97-5.92Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M42.78,76.62s.09,0,.12.05c3.03,8.57,6.04,17.15,9.03,25.73.06,1.62-.66,2.74-2.16,3.37-1.72.65-3.31.43-4.76-.68-.38-.33-.66-.72-.85-1.19-2.97-8.44-5.93-16.88-8.91-25.31.02-.04.05-.08.1-.1,2.49-.59,4.97-1.21,7.43-1.87Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M84.92,76.62c1.28.33,2.55.66,3.83.97-.54,1.17-.93,2.38-1.19,3.64-.23,1.22-.22,2.45.02,3.66.28.32.63.48,1.07.46.57-.04,1.12-.17,1.65-.39.01.02.03.05.05.07-2.3,6.42-4.6,12.83-6.92,19.25-.78,1.11-1.85,1.72-3.23,1.82-1.5.11-2.75-.38-3.76-1.48-.56-.74-.74-1.57-.53-2.48,2.99-8.52,5.99-17.03,9-25.53Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M51.57,97.25c8.22-.03,16.42,0,24.61.1-.56,1.55-1.1,3.1-1.63,4.66-.25,1.9.4,3.39,1.97,4.49,1.5.93,3.13,1.19,4.85.78,1.23-.34,2.25-1.01,3.03-2.01.2-.29.36-.59.49-.92.85-2.36,1.68-4.72,2.5-7.09h.34c1.03,11.84,2.05,23.69,3.06,35.53v.24h-53.88v-.24c1-11.84,2.02-23.69,3.06-35.53.16-.01.31,0,.46.05.84,2.39,1.68,4.79,2.52,7.18.53,1.13,1.36,1.95,2.5,2.45,1.63.67,3.26.68,4.9.05,2.14-.96,3.1-2.6,2.89-4.93-.53-1.61-1.09-3.21-1.67-4.81Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M47.59,100.16c1.54-.14,2.53.52,2.99,1.99.13,1.48-.51,2.45-1.92,2.89-1.13.17-2-.21-2.65-1.14-.64-1.3-.41-2.41.7-3.33.28-.18.57-.32.87-.41Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M79.14,100.16c1.43-.15,2.4.45,2.89,1.8.26,1.42-.27,2.41-1.58,2.99-1.51.37-2.57-.16-3.18-1.58-.31-1.63.31-2.69,1.87-3.2Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M52.01,106.13h23.69c0,6.7,0,13.4-.02,20.1-.32,2.21-1.54,3.66-3.66,4.34-.28.04-.55.09-.83.15-4.92.03-9.84.03-14.76,0-2.51-.47-3.98-1.97-4.39-4.49-.02-6.7-.03-13.4-.02-20.1Z",fill:"#052350",fillRule:"evenodd",strokeWidth:"0px"}),l.jsx("path",{d:"M74.34,107.49c0,6.25,0,12.49-.02,18.74-.33,1.73-1.35,2.78-3.08,3.13-4.94.03-9.87.03-14.81,0-1.9-.43-2.92-1.62-3.06-3.57v-18.3h20.97Z",fill:"#2998e9",fillRule:"evenodd",strokeWidth:"0px"})]})}),l.jsx("p",{className:"",children:"Holy mackerel! You are up to date with all your maintenance. Only thing left to do is, to go fishing"})]})})})})}var eo=t(56950),ec=t(94440),eh=t(15806),eu=t(49581);function em({logbooks:e,vesselID:s}){let[t,a]=(0,o.useState)(),[i,n]=(0,o.useState)(!1);(0,u.__)(a),(0,u.BJ)(s,e=>{e&&e?.logBookID>0&&c({variables:{id:+e.logBookID}})});let[c]=(0,d.t)(r.Yo,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readOneCustomisedLogBookConfig;s&&n(s)},onError:e=>{console.error("queryLogBookConfig error",e)}}),m=e=>{let s="";return"Fitness"==e&&(s="crew"),"SafetyActions"==e&&(s="crew"),"WaterQuality"==e&&(s="crew"),"IMSafe"==e&&(s="crew"),"Safety"==e&&(s="dailyChecks"),"HighWaterAlarm"==e&&(s="dailyChecks"),"FirstAid"==e&&(s="dailyChecks"),"SafetyEquipment"==e&&(s="dailyChecks"),"FireExtinguisher"==e&&(s="dailyChecks"),"Hull"==e&&(s="dailyChecks"),"Hull_HullStructure"==e&&(s="dailyChecks"),"Hull_DeckEquipment"==e&&(s="dailyChecks"),"Hull_DayShapes"==e&&(s="dailyChecks"),"TenderOperationalChecks"==e&&(s="dailyChecks"),"Anchor"==e&&(s="dailyChecks"),"WindscreenCheck"==e&&(s="dailyChecks"),"NightLineDockLinesRelease"==e&&(s="dailyChecks"),"Propulsion"==e&&(s="dailyChecks"),"PreEngineAndPropulsion"==e&&(s="dailyChecks"),"EngineCheckPropellers"==e&&(s="dailyChecks"),"EngineOilWater"==e&&(s="dailyChecks"),"EngineMountsAndStabilisers"==e&&(s="dailyChecks"),"ElectricalChecks"==e&&(s="dailyChecks"),"ElectricalVisualFields"==e&&(s="dailyChecks"),"Generator"==e&&(s="dailyChecks"),"ShorePower"==e&&(s="dailyChecks"),"SteeringChecks"==e&&(s="dailyChecks"),"Navigation"==e&&(s="dailyChecks"),"NavigationCharts"==e&&(s="dailyChecks"),"NavigationChecks"==e&&(s="dailyChecks"),"Radio"==e&&(s="dailyChecks"),"OtherNavigation"==e&&(s="dailyChecks"),"LogBookSignOff"==e&&(s="signOff"),"Review"==e&&(s="signOff"),"SafetyEquipmentCheck"==e&&(s="signOff"),"ForecastAccuracy"==e&&(s="signOff"),"Power"==e&&(s="signOff"),"BatteryMaintenance"==e&&(s="signOff"),"CircuitInspections"==e&&(s="signOff"),"MooringAndAnchoring"==e&&(s="signOff"),"CargoAndAccessEquipment"==e&&(s="signOff"),"HatchesAndWatertightDoors"==e&&(s="signOff"),"GalleyAppliances"==e&&(s="signOff"),"WasteManagement"==e&&(s="signOff"),"VentilationAndAirConditioning"==e&&(s="signOff"),"EmergencyReadiness"==e&&(s="signOff"),"EnvironmentalCompliance"==e&&(s="signOff"),s},x=e=>{let s="";return"DailyCheckFuel"==e&&(s="Engine Checks"),"DailyCheckEngine"==e&&(s="Engine Checks"),"Safety"==e&&(s="Safety Checks"),"HighWaterAlarm"==e&&(s="Safety Checks"),"FirstAid"==e&&(s="Safety Checks"),"SafetyEquipment"==e&&(s="Safety Checks"),"FireExtinguisher"==e&&(s="Safety Checks"),"Hull"==e&&(s="Deck operations and exterior checks"),"Hull_HullStructure"==e&&(s="Deck operations and exterior checks"),"Hull_DeckEquipment"==e&&(s="Deck operations and exterior checks"),"Hull_DayShapes"==e&&(s="Deck operations and exterior checks"),"TenderOperationalChecks"==e&&(s="Deck operations and exterior checks"),"Anchor"==e&&(s="Deck operations and exterior checks"),"WindscreenCheck"==e&&(s="Deck operations and exterior checks"),"NightLineDockLinesRelease"==e&&(s="Deck operations and exterior checks"),"Propulsion"==e&&(s="Engine Checks"),"PreEngineAndPropulsion"==e&&(s="Engine Checks"),"EngineCheckPropellers"==e&&(s="Engine Checks"),"EngineOilWater"==e&&(s="Engine Checks"),"EngineMountsAndStabilisers"==e&&(s="Engine Checks"),"ElectricalChecks"==e&&(s="Engine Checks"),"ElectricalVisualFields"==e&&(s="Engine Checks"),"Generator"==e&&(s="Engine Checks"),"ShorePower"==e&&(s="Engine Checks"),"SteeringChecks"==e&&(s="Engine Checks"),"Navigation"==e&&(s="Navigation"),"NavigationCharts"==e&&(s="Navigation"),"NavigationChecks"==e&&(s="Navigation"),"Radio"==e&&(s="Navigation"),"OtherNavigation"==e&&(s="Navigation"),s},p=e=>"dailyChecks"===m(e)?(0,eo.N3)(e,i):"crew"===m(e)?(0,eo.N3)(e,i,"CrewWelfare_LogBookComponent"):"signOff"===m(e)?(0,eo.N3)(e,i,"LogBookSignOff_LogBookComponent"):e?.replace(/([A-Z])/g," $1").trim()+" Comments",g=(0,C.wu)([{accessorKey:"title",header:"Date",cell:({row:e})=>{let s=e.original;return l.jsx(h.default,{href:`/log-entries?vesselID=${s.logBookEntry.vehicleID}&logentryID=${s.logBookEntry.id}&firstTab=${m(s.fieldName)}&secondTab=${x(s.fieldName)}`,children:(0,eh.z)(s.lastEdited)})}},{accessorKey:"comment",header:"Comment",cellAlignment:"left",cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"p-3",children:(0,l.jsxs)(h.default,{href:`/log-entries?vesselID=${s.logBookEntry.vehicleID}&logentryID=${s.logBookEntry.id}&firstTab=${m(s.fieldName)}&secondTab=${x(s.fieldName)}`,children:[l.jsx("div",{className:"",children:p(s.fieldName)}),l.jsx(S.P,{children:s.comment})]})})}},{accessorKey:"reporter",header:"Reporter",cell:({row:e})=>{let s=e.original,t=s?.seaLogsMember?.firstName+" "+s?.seaLogsMember?.surname;return t&&s?.seaLogsMember?.firstName!==null?l.jsx("div",{className:"flex-1 flex justify-center",children:(0,l.jsxs)(S.u,{children:[l.jsx(S.aJ,{asChild:!0,children:(0,l.jsxs)(h.default,{className:"w-fit flex justify-center items-center group/crew gap-2.5",href:`/log-entries?vesselID=${s.logBookEntry.vehicleID}&logentryID=${s.logBookEntry.id}&firstTab=${m(s.fieldName)}&secondTab=${x(s.fieldName)}`,children:[l.jsx(el.Avatar,{size:"sm",variant:"secondary",children:l.jsx(el.AvatarFallback,{className:"text-sm",children:(0,el.getCrewInitials)(s?.seaLogsMember?.firstName,s?.seaLogsMember?.surname)})}),l.jsx("div",{className:"hidden lg:block group-hover/crew:underline",children:t||"Unknown Author"})]})}),l.jsx(S._v,{className:"lg:hidden",children:t||"Unknown Author"})]})}):null}}]);return l.jsx("div",{className:"overflow-x-auto w-full block ",children:e?l.jsx(ec.UQ,{type:"single",collapsible:!0,className:"w-full",children:e.map((e,a)=>(0,l.jsxs)(ec.Qd,{value:e.id.toString(),children:[l.jsx(ec.o4,{children:(0,l.jsxs)("div",{className:"flex flex-row text-card-foreground items-center w-full",children:[l.jsx("div",{className:"mr-2",children:l.jsx(h.default,{href:`/log-entries?vesselID=${s}&logentryID=${e.id}`,className:"flex flex-row flex-nowrap",children:"Locked"!==e.state?l.jsx("div",{className:"text-slorange-1000",children:e?.startDate?(0,k.p6)(e.startDate):"No Date"}):l.jsx("div",{children:e?.startDate?(0,k.p6)(e.startDate):"No Date"})})}),e.master?.firstName?(0,l.jsxs)("div",{className:"items-center text-left whitespace-nowrap border rounded-lg mr-1 p-2",children:[e.master?.firstName," ",e.master?.surname]}):l.jsx(l.Fragment,{}),l.jsx("div",{className:"p-0 items-center text-left block md:inline-block",children:e?.crew?.map((s,t)=>l.jsx("span",{children:s.crewMember?.firstName!==e.master?.firstName&&s.crewMember?.surname!==e.master?.surname?l.jsxs("span",{children:[t<4&&s.crewMember?.firstName&&l.jsx("div",{className:"inline-block border rounded-lg mr-1 p-2 outline-none",children:s.crewMember?.firstName}),4===t&&l.jsxs(S.J2,{children:[l.jsx(S.CM,{children:l.jsxs(S.zx,{variant:"primaryOutline",children:["+"," ",e.crew.length-4," ","more"]})}),l.jsx(S.yk,{className:"w-64",children:e.crew.filter(()=>e.crew.includes(s)).slice(4).map((e,s)=>l.jsx("span",{children:l.jsx("div",{className:"flex cursor-pointer items-center overflow-auto",children:l.jsx("div",{className:"ps-3 py-2",children:l.jsx("div",{className:"",children:e.crewMember?.firstName})})})},`mpre-crew-members-${s}`))})]})]},`crew-member-${s.id}`):l.jsx(l.Fragment,{})},s.id))}),t&&l.jsx("div",{className:"items-center text-left whitespace-nowrap p-2 cursor-pointer",children:1!==t.filter(s=>s.logBookEntry.id===e.id).length?(0,l.jsxs)("div",{children:[t.filter(s=>s.logBookEntry.id===e.id).length," ","Comments"]}):(0,l.jsxs)("div",{children:[t.filter(s=>s.logBookEntry.id===e.id).length," ","Comment"]})}),l.jsx("div",{className:"p-2",children:e?.signOffComment?.comment&&(0,l.jsxs)(S.J2,{children:[l.jsx(S.CM,{asChild:!0,children:l.jsx(S.zx,{size:"icon",variant:"primaryOutline",children:l.jsx(eu.Z,{className:"w-5 h-5"})})}),l.jsx(S.yk,{children:e?.signOffComment?.comment})]})})]})}),l.jsx(ec.vF,{className:"border-t border-border",children:t&&l.jsx(l.Fragment,{children:t.filter(s=>s.logBookEntry.id===e.id).length>0?l.jsx(C.wQ,{className:"p-0",columns:g,showToolbar:!1,data:t.filter(s=>s.logBookEntry.id===e.id),pageSize:20}):l.jsx("div",{className:"",children:"No comments on this trip log"})})})]},"crewlog-"+a))}):l.jsx("div",{className:"flex justify-center items-center h-32",children:l.jsx("p",{className:"",children:"Start by adding a trip log"})})})}var ex=t(43692);function ep({vesselId:e,logbooks:s,totalEntries:t,perPage:a,handlePagination:r,currentPage:i}){return(0,l.jsxs)("div",{className:"overflow-x-auto my-4 lg:my-0 lg:pt-4",children:[l.jsx(em,{logbooks:s,vesselID:e}),s.length>0&&t>a&&l.jsx(ex.Z,{page:i,limit:a,onClick:r,totalCount:t,hasNextPage:i<Math.ceil(t/a)-1,hasPreviousPage:i>0,visiblePageCount:5})]})}var eg=t(13386);function ef({crewInfo:e,vessel:s}){return l.jsx(l.Fragment,{children:e?.length>0?l.jsx(eg.x,{crewList:e,vessels:[s],showSurname:!1}):l.jsx("div",{className:"flex justify-center items-center h-96",children:(0,l.jsxs)("div",{className:"flex flex-col items-center text-3xl ",children:[l.jsx("div",{children:"Crew"}),l.jsx("div",{className:"text-2xl",children:"No data available"})]})})})}var ev=t(59445),ej=t(14608);function ey({trainingSessions:e,trainingSessionDues:s}){return(0,l.jsxs)("div",{className:"space-y-4",children:[s?.length>0&&l.jsx(ev.z,{trainingSessionDues:s,isVesselView:!0}),e?.length>0&&l.jsx(ej.X,{trainingList:e,trainingSessionDues:s,isVesselView:!0})]})}function eb({inventories:e}){let s=(0,c.usePathname)(),t=(0,c.useSearchParams)();return l.jsx("div",{className:"block",children:e?.length>0?l.jsx(C.wQ,{columns:[{accessorKey:"title",header:"Item",cellAlignment:"left",cell:({row:e})=>{let a=e.original;return l.jsx(h.default,{href:`/inventory/view/?id=${a.id}&redirect_to=${s}?${t.toString()}&tab=inventory`,className:"flex items-center",children:a.quantity+" x "+a.item})}},{accessorKey:"maintenance",header:"Maintenance",cell:({row:e})=>{let s=(e=>{let s=e.componentMaintenanceChecks?.nodes||[];if(0===s.length)return null;let t=s.filter(e=>!e?.archived).filter(e=>{let s=(0,u.AT)(e);return"Completed"!==e.status&&"Save_As_Draft"!==e.status&&"Completed"!==s.status&&"Upcoming"!==s.status});return t.length>0?{type:"overdue",count:t.length}:{type:"good"}})(e.original);return l.jsx(l.Fragment,{children:s?.type==="overdue"?l.jsx(S.Ct,{variant:"destructive",children:s.count}):s?.type==="good"?l.jsx(S.Ct,{variant:"success",children:l.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"#27AB83","aria-hidden":"true",children:l.jsx("path",{fillRule:"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z",clipRule:"evenodd"})})}):null})}},{accessorKey:"categories",header:"Categories",cellAlignment:"left",cell:({row:e})=>{let s=e.original;return(0,l.jsxs)("div",{className:"flex gap-2 items-center",children:[s.categories?.nodes?.slice(0,2).map((e,s)=>l.jsx(S.Ct,{type:"normal",variant:"outline",className:"font-normal",children:e.name},String(s))),s.categories?.nodes?.length>2&&(0,l.jsxs)(_.Popover,{children:[l.jsx(_.PopoverTrigger,{asChild:!0,children:(0,l.jsxs)(a.Button,{variant:"outline",className:"!p-2 bg-transparent",children:["+"," ",s.categories.nodes.length-2," ","more"]})}),l.jsx(_.PopoverContent,{className:"w-80",children:(0,l.jsxs)("div",{className:"space-y-2",children:[l.jsx(S.P,{className:"font-medium text-sm",children:"All Categories"}),l.jsx("div",{className:"flex flex-wrap gap-2",children:s.categories.nodes.map((e,s)=>l.jsx(S.Ct,{type:"normal",variant:"outline",className:"font-normal",children:e.name},String(s)))})]})})]})]})}},{accessorKey:"suppliers",header:"Suppliers",cellAlignment:"right",cell:({row:e})=>{let s=e.original;return l.jsx("div",{className:"flex flex-col",children:s.suppliers?.nodes?.map(e=>l.jsx("div",{children:l.jsx(h.default,{href:`/inventory/suppliers/view?id=${e.id}`,children:e.name})},String(e.id)))})}}],showToolbar:!1,data:e,pageSize:20}):l.jsx("div",{className:"flex justify-center items-center h-96",children:(0,l.jsxs)("div",{className:"flex flex-col items-center text-3xl ",children:[l.jsx("div",{children:"Inventory"}),l.jsx("div",{className:"text-2xl",children:"No data available"})]})})})}var eN=t(24894),ek=t(43926),ew=t(70996);let eC=(0,H.Z)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);var eD=t(75535);function eS({imCrew:e,edit_docs:s,setDocuments:t,documents:r,delete_docs:i,deleteFile:n}){let{toast:d}=(0,g.pm)(),o=e=>{if(!i){d({title:"Permission denied",description:"You do not have permission to delete this document",variant:"destructive"});return}n(e)};return(0,l.jsxs)(Q.Zb,{className:"space-y-6",children:[!e&&s&&l.jsx(eN.Z,{setDocuments:e=>{"function"==typeof e?t(e(r)):t(e)},text:"",subText:"Drag files here or upload",documents:r}),(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx("div",{className:"flex items-center justify-between",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[l.jsx("h2",{className:"text-lg font-semibold",children:"Documents"}),r.length>0&&(0,l.jsxs)(K.C,{type:"normal",className:"text-sm",variant:"secondary",children:[r.length," ",1===r.length?"file":"files"]})]})}),r.length>0?l.jsx("div",{className:"grid gap-3",children:r.map(e=>(0,l.jsxs)("div",{className:"flex group items-center justify-between gap-4",children:[l.jsx("div",{className:"flex-1 min-w-0",children:l.jsx(ek.Z,{document:e})}),(0,l.jsxs)("div",{className:"flex items-center gap-3 flex-shrink-0",children:[l.jsx("div",{className:"text-right",children:l.jsx("p",{className:"text-xs text-muted-foreground",children:(0,k.p6)(e.created)})}),i&&l.jsx(a.Button,{variant:"ghost",size:"sm",onClick:()=>o(e.id),className:"h-8 w-8 p-0 md:opacity-0 group-hover:md:opacity-100 transition-opacity hover:bg-destructive/10 hover:text-destructive","aria-label":`Delete ${e.title}`,children:l.jsx(eD.Z,{className:"h-4 w-4"})})]})]}))}):l.jsx(()=>l.jsx(Q.Zb,{className:"border-dashed",children:(0,l.jsxs)(Q.aY,{className:"flex flex-col items-center justify-center py-12",children:[l.jsx("div",{className:"rounded-full bg-muted p-4 mb-4",children:l.jsx(ew.Z,{className:"h-8 w-8 text-muted-foreground"})}),l.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No documents yet"}),l.jsx("p",{className:"text-sm text-muted-foreground text-center max-w-sm mb-4",children:s&&!e?"Upload your first document to get started. Drag and drop files or click to browse.":"Documents will appear here when they are uploaded."}),s&&!e&&(0,l.jsxs)(K.C,{type:"normal",variant:"secondary",className:"gap-1",children:[l.jsx(eC,{className:"h-3 w-3"}),"Ready to upload"]})]})}),{})]})]})}var eM=t(13006);function eT({taskCounter:e,trainingDueCounter:s,vessel:t,engineList:a,fuelTankList:r,waterTankList:i,sewageSystemList:n,vesselId:d,logbooks:o,totalEntries:c,perPage:h,handlePagination:u,currentPage:m,maintenanceTasks:x,crewInfo:p,trainingSessions:g,trainingSessionDues:f,inventories:v,imCrew:j,edit_docs:y,setDocuments:b,documents:N,delete_docs:k,deleteFile:w}){let[C,D]=(0,eM.v1)("tab",{defaultValue:"logEntries"});return l.jsx("div",{className:"my-4 w-full",children:(0,l.jsxs)(G.Tabs,{value:C,onValueChange:D,children:[(0,l.jsxs)(G.TabsList,{children:[l.jsx(G.TabsTrigger,{value:"info",children:"Info"}),l.jsx(G.TabsTrigger,{value:"logEntries",children:"Log Entries"}),(0,l.jsxs)(G.TabsTrigger,{value:"maintenance",children:["Maintenance",e>0&&l.jsx(S.Ct,{className:"size-6 ml-2.5",variant:"destructive",children:e})]}),l.jsx(G.TabsTrigger,{value:"crew",children:"Crew"}),(0,l.jsxs)(G.TabsTrigger,{value:"crew_training",children:["Crew Training"," ",s>0&&l.jsx(S.Ct,{className:"size-6 ml-2.5",variant:"destructive",children:s})]}),l.jsx(G.TabsTrigger,{value:"inventory",children:"Inventory"}),l.jsx(G.TabsTrigger,{value:"documents",children:"Documents"})]}),l.jsx(G.TabsContent,{value:"info",className:"bg-background rounded-lg",children:l.jsx(et,{vessel:t,engineList:a,fuelTankList:r,waterTankList:i,sewageSystemList:n})}),l.jsx(G.TabsContent,{value:"logEntries",children:l.jsx(ep,{vesselId:d,logbooks:o,totalEntries:c,perPage:h,handlePagination:u,currentPage:m})}),l.jsx(G.TabsContent,{value:"maintenance",children:l.jsx(ed,{maintenanceTasks:x,crewInfo:p})}),l.jsx(G.TabsContent,{value:"crew",children:l.jsx(ef,{crewInfo:p,vessel:t})}),l.jsx(G.TabsContent,{value:"crew_training",children:l.jsx(ey,{trainingSessions:g,trainingSessionDues:f})}),l.jsx(G.TabsContent,{value:"inventory",children:l.jsx(eb,{inventories:v})}),l.jsx(G.TabsContent,{value:"documents",children:l.jsx(eS,{imCrew:j,edit_docs:y,setDocuments:b,documents:N,delete_docs:k,deleteFile:w})})]})})}var eI=t(48755),eE=t(70413),eZ=t(73366);let eA=(e,s=!0)=>e.reduce((e,t)=>{let l=e.find(e=>e.trainingTypeID===t.trainingTypeID&&(!s||e.vesselID===t.vesselID));return l?l.members=[...l.members,...t.members]:e.push({...t,members:[...t.members]}),e},[]);var eO=t(27514),e$=t(78965),eR=t(17203),eP=t(29342),eL=t(33849);let eW=[{label:"On Voyage",value:"OnVoyage"},{label:"Ready For Voyage",value:"AvailableForVoyage"},{label:"Out Of Service",value:"OutOfService"}],eB=[{label:"Crew Unavailable",value:"CrewUnavailable"},{label:"Skipper/Master Unavailable",value:"MasterUnavailable"},{label:"Planned Maintenance",value:"PlannedMaintenance"},{label:"Breakdown",value:"Breakdown"},{label:"Other",value:"Other"}];function eF({vesselId:e,tab:s}){let t=(0,c.usePathname)(),m=(0,c.useSearchParams)(),{toast:N}=(0,g.pm)(),[k,w]=(0,o.useState)(),[C,D]=(0,o.useState)([]),[T,I]=(0,o.useState)(),[E,Z]=(0,o.useState)(),[A,$]=(0,o.useState)(0),[R,L]=(0,o.useState)(0),[W,B]=(0,o.useState)([]),[F,_]=(0,o.useState)(0),[q,H]=(0,o.useState)(),[z,K]=(0,o.useState)([]),[G,Y]=(0,o.useState)([]),[Q,J]=(0,o.useState)(),[X,ee]=(0,o.useState)(),[es,et]=(0,o.useState)([]),[el,ea]=(0,o.useState)(),[er,ei]=(0,o.useState)(),[en,ed]=(0,o.useState)(),[eo,ec]=(0,o.useState)(),[eh,eu]=(0,o.useState)([]),em=(0,c.useRouter)(),[ex,ep]=(0,eM.v1)("tab",{defaultValue:"logEntries"}),[eg,ef]=(0,o.useState)(!1),[ev,ej]=(0,o.useState)([]),[ey,eb]=(0,o.useState)(!1),[eN,ek]=(0,o.useState)(!0),[ew,eC]=(0,o.useState)(!1),[eD,eS]=(0,o.useState)(!1),[eF,e_]=(0,o.useState)(!1),[eV,eq]=(0,o.useState)(!1),[eH,ez]=(0,o.useState)(!1),[eK,eU]=(0,o.useState)(!1),[eG,eY]=(0,o.useState)(0),[eQ,eJ]=(0,o.useState)(!1),[eX,e1]=(0,o.useState)([]),e2=(0,f.d)(),e3=(0,o.useRef)(null);new eI.Z,new eE.Z,new eZ.Z;let[e0]=(0,n.D)(i.qb9,{onCompleted:e=>{e.createLogBookEntry,e1({...eX,vesselID:k?.id,date:eX?.date,status:eX?.status,comment:eX?.comment,reason:eX?.reason,otherReason:eX?.otherReason,expectedReturn:eX?.expectedReturn}),eJ(!1)},onError:e=>{N({title:"Error",description:e.message,variant:"destructive"})}});(0,u.UE)(e,e=>{e.sort((e,s)=>"Locked"===s.state?-1:"Locked"===e.state?1:0),D([...e.filter(e=>"Locked"!==e.state).sort((e,s)=>new Date(e.startDate).getTime()-new Date(s.startDate).getTime()),...e.filter(e=>"Locked"===e.state).sort((e,s)=>new Date(s.startDate).getTime()-new Date(e.startDate).getTime())]),ek(!1),e.filter(e=>"Locked"!==e.state).length>0&&I(e.filter(e=>"Locked"!==e.state)[0]),$(e.length),e.filter(e=>"Locked"!==e.state).length>0&&so(e.filter(e=>"Locked"!==e.state)[0].id)});let[e4]=(0,d.t)(r.HV,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readInventories.nodes;s&&et(s)},onError:e=>{console.error("queryInventories error",e)}});(0,u.u$)(e,s=>{let t=eA(s).slice(0,5);eY(y(e)),K(s),Y(t)}),(0,u.Ck)(e,H);let[e5]=(0,d.t)(r.l6,{onCompleted:e=>{ea(e.readEngines.nodes)},onError:e=>{console.error("getEngines error",e)}}),e6=async e=>{await e5({variables:{id:e}})},[e7]=(0,d.t)(r.u2,{onCompleted:e=>{ei(e.readFuelTanks.nodes)},onError:e=>{console.error("getFuelTanks error",e)}}),e8=async e=>{await e7({variables:{id:e}})},[e9]=(0,d.t)(r.QK,{onCompleted:e=>{ed(e.readWaterTanks.nodes)},onError:e=>{console.error("getWaterTanks error",e)}}),se=async e=>{await e9({variables:{id:e}})},[ss]=(0,d.t)(r.rO,{onCompleted:e=>{ec(e.readSewageSystems.nodes)},onError:e=>{console.error("getSewageSystems error",e)}}),st=async e=>{await ss({variables:{id:e}})},[sl,{data:sa,loading:sr,error:si}]=(0,d.t)(r.ZX,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readFiles.nodes;eb("https://api.sealogs.com/assets/"+s[0]?.fileFilename)},onError:e=>{console.error(e)}}),sn=async e=>{await sd({variables:{input:{title:e.title}}})},[sd]=(0,n.D)(i.jwd,{onCompleted:s=>{sx({variables:{input:{id:e,logBookID:s.createLogBook.id}}})},onError:e=>{console.error("createLogBook error",e)}});(0,u.BJ)(e,e=>{w(e),ej(e?.seaLogsMembers?.nodes.map(e=>e.id)),e?.seaLogsMembers&&sc(e.seaLogsMembers.nodes.filter(e=>!e.archived).map(e=>+e.id));let s=e?.parentComponent_Components?.nodes.filter(e=>"Engine"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id),t=e?.parentComponent_Components?.nodes.filter(e=>"FuelTank"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id),l=e?.parentComponent_Components?.nodes.filter(e=>"WaterTank"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id),a=e?.parentComponent_Components?.nodes.filter(e=>"SewageSystem"===e.basicComponent.componentCategory).map(e=>e.basicComponent.id);s?.length>0&&e6(s),t?.length>0&&e8(t),l?.length>0&&se(l),a?.length>0&&st(a),e?.documents?.nodes?.length>0&&eu(e.documents.nodes),e?.logBookID==0&&sn(e),e?.bannerImageID!=="0"&&e?.bannerImageID&&sl({variables:{id:[e.bannerImageID]}})}),(0,u.PN)(e,s=>{0===s.length&&B(!1);let t=[...s.filter(e=>e?.archived!==1),...es.flatMap(e=>e.componentMaintenanceChecks||[])],l=new Set,a=t.filter(e=>{let s=l.has(e.id);return l.add(e.id),!s});a.sort((e,s)=>{let t=e.isOverDue?.status,l=s.isOverDue?.status;if("High"===t&&"High"!==l)return -1;if("High"!==t&&"High"===l)return 1;if("Medium"===t&&"Medium"!==l)return -1;if("Medium"!==t&&"Medium"===l)return 1;if("Medium"===t&&"Medium"===l)return x()(s.startDate).diff(e.startDate);if("High"!==t||"High"!==l)return"1"===e.isCompleted&&"1"===s.isCompleted?"NA"===e.expires&&"NA"!==s.expires?1:"NA"!==e.expires&&"NA"===s.expires?-1:new Date(s.expires).getTime()-new Date(e.expires).getTime():"1"===e.isCompleted?1:"1"===s.isCompleted?-1:x()(e.expires).diff(s.expires);{let t=e.isOverDue?.days?parseInt(e.isOverDue.days.match(/(\d+)/)?.[0]||"0"):0;return(s.isOverDue?.days?parseInt(s.isOverDue.days.match(/(\d+)/)?.[0]||"0"):0)-t}}),B(a),sc(Array.from(new Set(a.filter(e=>e.assignedToID>0).map(e=>e.assignedToID))),!0),_(j(e))});let so=async e=>{await sh({variables:{logbookEntryId:e}})},sc=async(e,s=!1)=>{e.length>0&&(s?await sm({variables:{crewMemberIDs:e}}):await su({variables:{crewMemberIDs:e}}))},[sh]=(0,d.t)(r.MI,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readOneLogBookEntry;s&&Z(s)}}),[su]=(0,d.t)(r.rd,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readSeaLogsMembers.nodes;s&&J(s)},onError:e=>{console.error("queryCrewMemberInfo error",e)}}),[sm]=(0,d.t)(r.rd,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readSeaLogsMembers.nodes;s&&ee(s)},onError:e=>{console.error("queryTaskMembersInfo error",e)}}),[sx]=(0,n.D)(i.FxW,{onError:e=>{console.error("updateVessel error",e)}}),sp=async()=>{await sx({variables:{input:{id:e,seaLogsMembers:ev.join(",")}}}),ef(!1),sc(ev)},sg=async()=>{if(!eF){N({title:"Error",description:"You do not have permission to create a new log entry",variant:"destructive"});return}sv?N({title:"Error",description:(0,l.jsxs)("div",{children:["Please complete the open log entry"," ",l.jsx(h.default,{href:`/log-entries?vesselID=${e}&logentryID=${C.filter(e=>"Locked"!==e.state)[0].id}`,children:l.jsx("span",{className:"underline",children:"here"})})," ","before creating a new one."]}),variant:"destructive"}):k?.logBookID>0&&(ek(!0),await sf({variables:{input:{logBookID:k.logBookID,vehicleID:e}}}))},[sf]=(0,n.D)(i.jV1,{onCompleted:s=>{em.push(`/log-entries?vesselID=${e}&logentryID=${s.createLogBookEntry.id}`)},onError:e=>{console.error("createLogEntry error",e),N({title:"Error",description:e.message,variant:"destructive"})}}),sv=(0,o.useMemo)(()=>C.filter(e=>"Locked"!==e.state).length>0,[C]),sj=(0,o.useMemo)(()=>eX?.status==="OutOfService"?"Out Of Service":sv?"On Voyage":"Ready for Voyage",[sv,eX]),sy=()=>{e3.current&&e3.current.scrollIntoView({behavior:"smooth",block:"start"})},sb=(0,l.jsxs)("div",{className:"hidden md:flex flex-row justify-end",children:[sv?l.jsx("div",{className:"invisible"}):l.jsx(l.Fragment,{children:"logEntries"===ex&&l.jsx("div",{children:!ew&&l.jsx(a.Button,{disabled:eN,onClick:sg,children:"New log entry"})})}),"crew"===ex&&!ew&&l.jsx(a.Button,{onClick:()=>{ej(k?.seaLogsMembers?.nodes.map(e=>e.id)),ef(!0)},children:"Add crew"}),"maintenance"===ex&&l.jsx(a.Button,{onClick:()=>{em.push(`/maintenance/new?vesselID=${e}&redirect_to=${t}?${m.toString()}%26tab=maintenance`)},children:"Add task"}),eD&&(0,v.Fs)("RECORD_TRAINING",eD)&&l.jsx(l.Fragment,{children:"crew_training"===ex&&l.jsx(a.Button,{onClick:()=>{em.push(`/crew-training/create?vesselID=${e}`)},children:"Add Training"})}),"inventory"===ex&&!ew&&l.jsx(a.Button,{onClick:()=>{em.push(`/inventory/new?vesselID=${e}&redirectTo=${t}?${m.toString()}%26tab=inventory`)},children:"Add Inventory"})]});return(0,l.jsxs)(l.Fragment,{children:[l.jsx(b,{bannerImage:ey}),l.jsx("div",{className:"w-full flex flex-col gap-8 overflow-hidden relative px-1 phablet:px-4 -mt-20 md:-mt-24",children:(0,l.jsxs)("div",{className:"grid grid-col-1 lg:grid-cols-3 gap-8 lg:gap-6 xl:gap-8",children:[l.jsx(M,{vesselId:e,logbooks:C,imCrew:ew,handleCreateNewLogEntry:sg,isNewLogEntryDisabled:eN,setVesselTab:ep,vesselTitle:k?.title,scrollToTabs:sy}),l.jsx(O,{maintenanceTasks:W,pathname:t,setVesselTab:ep,scrollToTabs:sy}),eD&&(0,v.Fs)("VIEW_TRAINING",eD)&&l.jsx(P,{trainingSessionDuesSummary:G,setVesselTab:ep,scrollToTabs:sy}),e2&&(0,l.jsxs)(l.Fragment,{children:[l.jsx(V,{crewInfo:Q,setVesselTab:ep,vesselId:e,pathname:t}),l.jsx(U,{inventories:es,setVesselTab:ep,vesselId:e,pathname:t})]})]})}),l.jsx("div",{className:"px-1 phablet:px-4 hidden md:block",ref:e3,children:l.jsx(eT,{taskCounter:F,trainingDueCounter:eG,vessel:k,engineList:el,fuelTankList:er,waterTankList:en,sewageSystemList:eo,vesselId:e,logbooks:C,totalEntries:A,perPage:10,handlePagination:e=>{e<0||R===e||L(e)},currentPage:R,maintenanceTasks:W,crewInfo:Q,trainingSessions:q,trainingSessionDues:z,inventories:es,imCrew:ew,edit_docs:eH,setDocuments:eu,documents:eh,delete_docs:eK,deleteFile:s=>{let t=eh.filter(e=>e.id!==s);eu(t),sx({variables:{input:{id:e,documents:t.map(e=>e.id).join(",")}}})}})}),l.jsx(S.h9,{openDialog:eg,setOpenDialog:ef,handleCreate:sp,title:"Add Crew",actionText:"Add Crew",children:k&&l.jsx(p.Z,{value:ev,onChange:e=>{ej(e.map(e=>e.value))},departments:k?.departments?.nodes})}),(0,l.jsxs)(S.h9,{size:"xl",openDialog:eQ,setOpenDialog:eJ,handleCreate:()=>{eX?.status==="OutOfService"?e0({variables:{input:{vesselID:k?.id,date:eX?.date,status:eX?.status,comment:eX?.comment,reason:eX?.reason,otherReason:eX?.otherReason,expectedReturn:eX?.expectedReturn}}}):e0({variables:{input:{vesselID:k?.id,date:eX?.date,status:eX?.status}}})},title:"Update Vessel Status",actionText:"Update",className:"space-y-8",children:[l.jsx(eP.Z,{mode:"single",onChange:e=>{e1({...eX,date:e})},placeholder:"Select date",value:eX?.date}),(0,l.jsxs)("div",{className:"flex gap-2.5",children:[l.jsx(S.__,{label:"Status",className:"flex-1",children:l.jsx(S.hQ,{id:"vessel-status",options:eW,placeholder:"Status",value:eW.find(e=>eX?.status===e.value),onChange:e=>{sv?"OnVoyage"!==e.value?N({title:"Error",description:"There is an Open LogBook entry, Please complete the entry in order to update the vessel status",variant:"destructive"}):e1({...eX,status:e?.value}):e?.value==="OnVoyage"?N({title:"Error",description:"There is no Open LogBook entry, Please create a Logbook entry to set the vessel on voyage",variant:"destructive"}):e1({...eX,status:e?.value})}})}),eX?.status==="OutOfService"&&l.jsx(S.__,{label:"Reason for out of service",className:"flex-1",children:l.jsx(S.hQ,{id:"vessel-status-reason",options:eB,placeholder:"Reason",value:eB.find(e=>eX?.reason===e.value),onChange:e=>{e1({...eX,reason:e?.value})}})})]}),eX?.status==="OutOfService"&&eX?.reason==="Other"&&l.jsx(S.gx,{id:"vessel-status-other",placeholder:"Other description",value:eX?.otherReason,onChange:e=>e1({...eX,otherReason:e.target.value})}),eX?.status==="OutOfService"&&l.jsx(S.__,{label:"Comments",children:l.jsx(eL.Z,{id:"comment",placeholder:"Comment",className:"bg-background",content:eX?.comment,handleEditorChange:e=>e1({...eX,comment:e})})}),eX?.status==="OutOfService"&&l.jsx(S.__,{label:"Expected date of return",htmlFor:"expected-return-date",children:l.jsx(eP.Z,{className:"flex w-full",mode:"single",onChange:e=>{e1({...eX,expectedReturn:e})},placeholder:"Select date",value:eX?.expectedReturn})})]}),(0,l.jsxs)(e$.V,{className:" justify-between",children:[k&&k?.statusHistory?.nodes?.length>0&&(0,l.jsxs)("div",{className:"flex flex-wrap md:flex-inline items-center gap-2",children:[l.jsx("strong",{children:"Status:"}),sj,!sv&&l.jsx(a.Button,{variant:"ghost",onClick:()=>eJ(!0),children:"Edit"})]}),(0,l.jsxs)("div",{className:"hidden md:flex flex-row gap-2",children:[l.jsx(a.Button,{variant:"back",onClick:()=>em.back(),iconLeft:eR.Z,children:"Cancel"}),!ew&&(0,l.jsxs)(eO.DropdownMenu,{children:[l.jsx(eO.DropdownMenuTrigger,{asChild:!0,children:l.jsx(a.Button,{variant:"outline",children:"Edit"})}),l.jsx(eO.DropdownMenuContent,{align:"end",children:(0,l.jsxs)(eO.DropdownMenuGroup,{children:[l.jsx(eO.DropdownMenuItem,{onClick:()=>{em.push(`/vessel/edit?id=${e}`)},children:"Edit Vessel"}),k?.logBookID>0&&l.jsx(eO.DropdownMenuItem,{onClick:()=>{em.push(`/vessel/logbook-configuration?logBookID=${k.logBookID}&vesselID=${e}`)},children:"Edit Logbook Configuration"})]})})]}),sb]})]})]})}let e_=()=>{let e=(0,c.useSearchParams)(),s=e.get("id")??0,t=e.get("vesselTab")??null;return l.jsx(eF,{vesselId:+s,tab:t})}},57710:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var l=t(98768);t(60343);var a=t(64837);function r({children:e}){return l.jsx(a.Z,{children:e})}},43926:(e,s,t)=>{"use strict";t.d(s,{Z:()=>f});var l=t(98768),a=t(66263),r=t(60343),i=t(28147),n=t(12513),d=t(88557),o=t(27780),c=t(50526),h=t(7179);t(18937),t(73304);var u=t(39544),m=t(46877),x=t(13609),p=t(47634),g=t(34376);let f=({document:e,hideTitle:s=!1,showDeleteButton:t=!1,onDelete:f,canDelete:v=!0,deleteErrorMessage:j="You do not have permission to delete this document"})=>{let[y,b]=(0,r.useState)(!1),[N,k]=(0,r.useState)(!1),{toast:w}=(0,g.pm)(),C=()=>{if(!v){w({description:j,variant:"destructive"});return}f&&f(e.id)},D=()=>{let s=process.env.NEXT_PUBLIC_FILE_BASE_URL||"https://api.sealogs.com/assets/";return`${s}${e.fileFilename}`};return["jpg","jpeg","png","gif","webp","svg"].includes(e.fileFilename?.split(".").pop()?.toLowerCase()||"")&&!N?(0,l.jsxs)("div",{className:"group relative flex items-center gap-3",children:[l.jsx(u.Button,{variant:"ghost",onClick:()=>b(!0),className:"h-auto p-2 hover:bg-muted/50 transition-colors flex-1","aria-label":`View image: ${e.title}`,children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsxs)("div",{className:"relative overflow-hidden rounded-lg border border-border bg-muted/20",children:[l.jsx(i.default,{src:D()||"/placeholder.svg",alt:e.title,width:64,height:64,className:"h-16 w-16 object-cover transition-transform group-hover:scale-105",onError:()=>k(!0)}),l.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors flex items-center justify-center",children:l.jsx(m.Z,{className:"h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity"})})]}),!s&&(0,l.jsxs)("div",{className:"flex-1 text-left",children:[l.jsx("p",{className:"font-medium text-sm truncate max-w-[200px]",children:e.title}),(0,l.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Image •"," ",e.fileFilename?.split(".").pop()?.toUpperCase()]})]})]})}),t&&l.jsx(u.Button,{variant:"destructive",iconLeft:x.Z,iconOnly:!0,onClick:C,"aria-label":`Delete ${e.title}`}),l.jsx(n.ZP,{open:y,close:()=>b(!1),slides:[{src:D(),alt:e.title,description:e.title}],render:{buttonPrev:()=>null,buttonNext:()=>null},controller:{closeOnPullUp:!0,closeOnPullDown:!0,closeOnBackdropClick:!0},plugins:[d.Z,h.Z,c.Z,o.Z]})]}):(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[l.jsx(a.default,{href:D(),target:"_blank",rel:"noopener noreferrer",className:"group block flex-1",children:(0,l.jsxs)("div",{className:"flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors",children:[(0,l.jsxs)("div",{className:"relative",children:[l.jsx("div",{className:"h-16 w-16 rounded-lg border border-border border-dashed bg-muted/20 flex items-center justify-center group-hover:bg-muted/30 transition-colors",children:N?l.jsx(m.Z,{className:"h-6 w-6 text-muted-foreground"}):l.jsx(i.default,{src:{pdf:"/file-types/pdf.svg",xls:"/file-types/xls.svg",xlsx:"/file-types/xls.svg",ppt:"/file-types/ppt.svg",pptx:"/file-types/ppt.svg",txt:"/file-types/txt.svg",csv:"/file-types/csv.svg"}[e.fileFilename?.split(".").pop()?.toLowerCase()||""]||"/file-types/doc.svg",alt:`${e.fileFilename?.split(".").pop()?.toUpperCase()} file`,width:24,height:24,className:"h-6 w-6"})}),l.jsx("div",{className:"absolute -top-1 -right-1 bg-background border border-border rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity",children:l.jsx(p.Z,{className:"h-3 w-3 text-muted-foreground"})})]}),!s&&(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[l.jsx("p",{className:"font-medium text-sm truncate",children:e.title}),(0,l.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Document •"," ",e.fileFilename?.split(".").pop()?.toUpperCase()]})]})]})}),t&&l.jsx(u.Button,{variant:"destructive",iconLeft:x.Z,iconOnly:!0,onClick:C,"aria-label":`Delete ${e.title}`})]})}},24894:(e,s,t)=>{"use strict";t.d(s,{Z:()=>p});var l=t(98768),a=t(60343),r=t(28147),i=t(79418),n=t(72548),d=t(67537),o=t(94060),c=t(76342),h=t(56937),u=t(71890),m=t(60797),x=t(25394);function p({setDocuments:e,text:s="Documents and Images",subText:t,bgClass:p="",documents:g,multipleUpload:f=!0}){let[v,j]=(0,a.useState)(!1),[y,b]=(0,a.useState)([]),[N,k]=(0,a.useState)(!1),[w,C]=(0,a.useState)(!1),D=(0,a.useRef)(null),S=(0,h.cn)("relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none",v?"bg-accent border-primary":"bg-accent/50 border-border","text-foreground hover:bg-accent hover:border-primary","min-h-[10rem] cursor-pointer select-none",p),M=async e=>{let s=new FormData;s.append("FileData",e,e.name.replace(/\s/g,""));try{let e=await fetch("https://api.sealogs.com/api/v2/upload",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("sl-jwt")}`},body:s}),t=await e.json();await T({variables:{id:[t[0].id]}}),C(!1)}catch(e){console.error(e)}},[T]=(0,i.t)(o.ZX,{fetchPolicy:"cache-and-network",onCompleted:e=>{b(s=>[...s,e.readFiles.nodes[0]]),k(!0)},onError:e=>console.error(e)}),[I]=(0,n.D)(c.RgS,{onCompleted:s=>{let t=s.updateFile;e(e=>f?[...e,t]:[t])},onError:e=>console.error(e)}),E=e=>{let s=Array.from(e);C(!0),s.forEach(M)},Z=e=>s=>{s.preventDefault(),j(e)};return(0,l.jsxs)("div",{className:"w-full pt-4 lg:pt-0",children:[(0,l.jsxs)("form",{className:S,onSubmit:e=>e.preventDefault(),onDragEnter:Z(!0),onDragOver:Z(!0),onDragLeave:Z(!1),onDrop:e=>{e.preventDefault(),j(!1),e.dataTransfer.files&&E(e.dataTransfer.files)},onClick:()=>D.current?.click(),"aria-label":"File uploader drop zone",children:[l.jsx("span",{className:"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider",children:s}),l.jsx(u.I,{ref:D,type:"file",className:"hidden",multiple:f,accept:".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf",onChange:e=>{e.target.files&&E(e.target.files)}}),(0,l.jsxs)("div",{className:"flex flex-col items-center gap-2 pointer-events-none",children:[l.jsx(r.default,{src:"/sealogs-document_upload.svg",alt:"Upload illustration",width:96,height:96,className:"relative -translate-x-2.5",priority:!0}),t&&l.jsx("span",{className:"text-sm font-medium text-neutral-400",children:t})]})]}),w?(0,l.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2",children:[l.jsx(d.Z,{className:"h-5 w-5 animate-spin text-primary"}),l.jsx("span",{className:"text-sm text-muted-foreground",children:"Uploading..."})]}):l.jsx(x.h9,{openDialog:N,setOpenDialog:k,handleCreate:()=>{y.forEach((e,s)=>{let t=document.getElementById(`file-name-${s}`).value;I({variables:{input:{id:e.id,title:t}}})}),k(!1)},actionText:"Save",title:"File Name",children:l.jsx("div",{className:"space-y-4",children:y.map((e,s)=>l.jsx(m.Label,{label:`File ${s+1} Name`,htmlFor:`file-name-${s}`,children:l.jsx(u.I,{id:`file-name-${s}`,defaultValue:e.title,placeholder:"Enter file name"})},e.id))})})]})}},43692:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});var l=t(98768);let a=({page:e=0,limit:s=0,onClick:t,totalCount:a=0,hasNextPage:r=!1,hasPreviousPage:i=!1,visiblePageCount:n=0})=>{let d=s>0?Math.ceil(a/s):0,o=s>0?n:0,c=e-o,h=e;c<0&&(c=0,h=o-1);let u=d-o,m=h+1!==u;h>=u&&(c=0,h=o-1),d<o&&(c=0,h=d-1);let x=Array.from({length:h-c+1},(e,s)=>c+s).slice(-o),p=Array.from({length:(s>0?Math.floor(a/s):0)-u+1},(e,s)=>u+s).slice(0,o);return p=(p=p.filter(e=>!x.includes(e))).filter(e=>e>=0),(x[x.length-1]+1===p[0]||x[x.length-1]-1===p[0]||p.length<=0)&&(m=!1),l.jsx("div",{className:"flex items-center justify-end p-4",children:l.jsx("nav",{"aria-label":"Log Entries pagination",children:(0,l.jsxs)("ul",{className:"inline-flex -space-x-px  h-10",children:[l.jsx("li",{children:i&&e>0&&l.jsx("button",{onClick:()=>t(0),className:" rounded-s-lg",children:"First"})}),l.jsx("li",{children:i&&l.jsx("button",{onClick:()=>t(e-1),className:"",children:"Previous"})}),Array.from({length:x.length},(e,s)=>l.jsx("li",{children:l.jsx("button",{onClick:()=>t(x[s]),className:"",children:x[s]+1})},s)),m&&l.jsx("li",{children:l.jsx("button",{onClick:()=>t(h+1),className:"flex items-center justify-center px-4 h-10 leading-tight  border   ",children:"..."})}),Array.from({length:p.length},(e,s)=>l.jsx("li",{children:l.jsx("button",{onClick:()=>t(p[s]),className:"",children:p[s]+1})},s)),l.jsx("li",{children:r&&l.jsx("button",{onClick:()=>t(e+1),className:"",children:"Next"})}),l.jsx("li",{children:r&&e*s<a&&l.jsx("button",{onClick:()=>t(d-1),className:" rounded-e-lg",children:"Last"})})]})})})}},78753:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});let l=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\vessel\info\page.tsx#default`)},42832:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});let l=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\vessel\layout.tsx#default`)},70996:(e,s,t)=>{"use strict";t.d(s,{Z:()=>l});let l=(0,t(97428).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},49581:(e,s,t)=>{"use strict";t.d(s,{Z:()=>l});let l=(0,t(97428).Z)("MessageSquareText",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}],["path",{d:"M13 8H7",key:"14i4kc"}],["path",{d:"M17 12H7",key:"16if0g"}]])},51927:(e,s,t)=>{"use strict";t.d(s,{Z:()=>l});let l=(0,t(97428).Z)("SquareArrowOutUpRight",[["path",{d:"M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6",key:"y09zxi"}],["path",{d:"m21 3-9 9",key:"mpx6sq"}],["path",{d:"M15 3h6v6",key:"1q9fwt"}]])},75535:(e,s,t)=>{"use strict";t.d(s,{Z:()=>l});let l=(0,t(97428).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},53363:(e,s,t)=>{"use strict";t.d(s,{Z:()=>l});let l=(0,t(97428).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),l=s.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,9707,6250,8822,5959,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7380,7944,8410,7033,336,9445],()=>t(80331));module.exports=l})();