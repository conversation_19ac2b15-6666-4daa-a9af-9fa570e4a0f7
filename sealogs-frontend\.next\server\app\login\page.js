(()=>{var e={};e.id=2626,e.ids=[2626],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},40406:(e,t,o)=>{"use strict";o.r(t),o.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>g,pages:()=>c,routeModule:()=>u,tree:()=>d}),o(83166),o(78398),o(57757),o(48045);var i=o(40060),r=o(33581),s=o(57567),n=o.n(s),a=o(51650),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);o.d(t,l);let d=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,83166)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(o.bind(o,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(o.bind(o,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(o.bind(o,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\login\\page.tsx"],g="/login/page",m={require:o,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96755:(e,t,o)=>{Promise.resolve().then(o.t.bind(o,68472,23)),Promise.resolve().then(o.t.bind(o,28029,23)),Promise.resolve().then(o.bind(o,48008))},46776:(e,t,o)=>{"use strict";o.d(t,{E7:()=>m,Fs:()=>d,GJ:()=>s,PE:()=>r,UU:()=>a,Zu:()=>c,ay:()=>l,io:()=>g,j5:()=>n});var i=o(86708);let r=()=>{},s=()=>{let e="true"===localStorage.getItem("superAdmin"),t="true"===localStorage.getItem("admin");return e||t},n=()=>"true"===localStorage.getItem("superAdmin"),a=()=>{r()&&(window.location.href="/dashboard")},l=()=>{s()||(window.location.href="/dashboard")},d=(e,t)=>t.includes("ADMIN")||t.includes(e),c=e=>!1,g=async()=>{try{await i.Z.delete()}catch(e){console.error("[clearDB] deleting seaLogsDB failed: ",e)}try{await i.Z.open()}catch(e){console.error("[clearDB] opening seaLogsDB failed: ",e)}},m=()=>!1},86708:(e,t,o)=>{"use strict";o.d(t,{Z:()=>r});let i=new(o(69507)).ZP("seaLogsDB");i.version(2).stores({AssetReporting_LogBookEntrySection:"id, idbCRUD",BarCrossingChecklist:"id, idbCRUD, vesselID",CGEventMission:"id, idbCRUD, vesselID",Client:"id, idbCRUD",ComponentMaintenanceCheck:"id, idbCRUD",ComponentMaintenanceSchedule:"id, idbCRUD",Consequence:"id, idbCRUD",CrewDuty:"id, idbCRUD",CrewMembers_LogBookEntrySection:"id, idbCRUD",CrewTraining_LogBookEntrySection:"id, idbCRUD",CrewWelfare_LogBookEntrySection:"id, idbCRUD",CustomisedComponentField:"id, idbCRUD, customisedLogBookComponentID",CustomisedLogBookConfig:"id, idbCRUD, customisedLogBookID",DangerousGood:"id, idbCRUD",DangerousGoodsChecklist:"id, idbCRUD, vesselID",DangerousGoodsRecord:"id, idbCRUD, tripReport_LogBookEntrySectionID",Engine:"id, idbCRUD",Engine_LogBookEntrySection:"id, idbCRUD",Engine_Usage:"id, idbCRUD, maintenanceScheduleID, engineID",Engineer_LogBookEntrySection:"id, idbCRUD",EventType:"id, idbCRUD",EventType_BarCrossing:"id, idbCRUD, tripEventID, geoLocationID, geoLocationCompletedID, barCrossingChecklistID",EventType_PassengerDropFacility:"id, idbCRUD, tripEventID, geoLocationID",EventType_PersonRescue:"id, idbCRUD, missionID, tripEventID",EventType_RestrictedVisibility:"id, idbCRUD, tripEventID, startLocationID, endLocationID",EventType_Supernumerary:"id, idbCRUD",EventType_Tasking:"id, idbCRUD, currentEntryID, groupID, pausedTaskID, openTaskID, completedTaskID, tripEventID, geoLocationID, vesselRescueID, personRescueID, towingChecklistID, parentTaskingID",EventType_VesselRescue:"id, idbCRUD, missionID, vesselLocationID, tripEventID",FavoriteLocation:"id, idbCRUD, memberID",FuelLog:"id, idbCRUD, refuellingBunkeringID, eventType_TaskingID, eventType_PassengerDropFacilityID, logBookEntryID, fuelTankID",FuelTank:"id, idbCRUD",Fuel_LogBookEntrySection:"id, idbCRUD",GeoLocation:"id, idbCRUD",Inventory:"id, idbCRUD, vesselID",InfringementNotice:"id, idbCRUD",InfringementNotice_Signature:"id, idbCRUD, infringementNoticeID",Likelihood:"id, idbCRUD",LogBookEntry:"id, idbCRUD, vehicleID",LogBookEntryOldConfigs:"id, idbCRUD, logBookEntryID",LogBookEntrySection_Signature:"id, idbCRUD, logBookEntrySectionID",LogBookSignOff_LogBookEntrySection:"id, idbCRUD",MaintenanceCheck:"id, idbCRUD, maintenanceScheduleID",MaintenanceCheck_Signature:"id, idbCRUD, maintenanceCheckID",MaintenanceSchedule:"id, idbCRUD",MaintenanceScheduleSubTask:"id, idbCRUD, componentMaintenanceScheduleID",MemberTraining_Signature:"id, idbCRUD, memberID, trainingSessionID",MissionTimeline:"id, idbCRUD, missionID, vesselRescueID, personRescueID, maintenanceCheckID, subTaskID",MitigationStrategy:"id, idbCRUD",Ports_LogBookEntrySection:"id, idbCRUD",RefuellingBunkering:"id, idbCRUD, tripEventID, geoLocationID",TripUpdate:"id, idbCRUD, tripEventID, geoLocationID",RiskFactor:"id, idbCRUD, vesselID, riskRatingID, consequenceID, likelihoodID, towingChecklistID, dangerousGoodsChecklistID, barCrossingChecklistID, type",RiskRating:"id, idbCRUD",SeaLogsMember:"id, idbCRUD",SectionMemberComment:"id, idbCRUD, logBookEntrySectionID, commentType, comment, hideComment",Supernumerary_LogBookEntrySection:"id, idbCRUD, supernumeraryID",TowingChecklist:"id, idbCRUD, vesselID",TrainingLocation:"id, idbCRUD",TrainingSession:"id, idbCRUD, trainerID, logBookEntrySectionID, logBookEntryID, vesselID, trainingLocationID, geoLocationID",TrainingSessionDue:"id, idbCRUD, memberID, trainingTypeID, vesselID, trainingSessionID",TrainingType:"id, idbCRUD",TripEvent:"id, idbCRUD, logBookEntrySectionID",TripReport_LogBookEntrySection:"id, idbCRUD",TripReport_Stop:"id, idbCRUD, stopLocationID, logBookEntrySectionID, tripReportScheduleStopID, dangerousGoodsChecklistID",VehiclePosition:"id, idbCRUD, vehicleID",Vessel:"id, idbCRUD",VesselDailyCheck_LogBookEntrySection:"id, idbCRUD",VoyageSummary_LogBookEntrySection:"id, idbCRUD",WeatherForecast:"id, idbCRUD, logBookEntryID",WeatherObservation:"id, idbCRUD, logBookEntryID, forecastID",WeatherTide:"id, idbCRUD, logBookEntryID"}),i.open().catch(function(e){console.error("[seaLogsDB] Open failed: "+e)});let r=i},98771:(e,t,o)=>{"use strict";o.d(t,{Z:()=>s});var i=o(98768),r=o(28147);let s=function({color:e="default"}){return i.jsx("div",{children:"white"===e?i.jsx(r.default,{src:"/sealogs-horizontal-logo-white.png",alt:"Sealogs Logo",width:300,height:92,className:""}):i.jsx(r.default,{src:"/sealogs-horizontal-logo.png",alt:"Sealogs Logo",width:300,height:92,className:""})})}},48008:(e,t,o)=>{"use strict";o.d(t,{LoginForm:()=>S});var i=o(98768),r=o(39544),s=o(71890),n=o(60343),a=o(69424),l=o(26660),d=o(79418),c=o(72548),g=o(94060),m=o(76342);o(46776);var u=o(42106),D=o(66314),p=o(71496),h=o(57906),I=o(66263),b=o(98771),C=o(26509);let f=p.z.object({email:p.z.string(),password:p.z.string()});function S({className:e,...t}){let o=(0,a.useRouter)(),p=(0,l.x)(),[S,R]=(0,n.useState)(""),[v,x]=(0,n.useState)(!1);async function y(e){R(""),await E({variables:{email:e.email,password:e.password}})}let[U,{loading:k}]=(0,d.t)(g.jl,{fetchPolicy:"no-cache",onCompleted:e=>{let t=e.readOneSeaLogsMember;if(t){localStorage.setItem("clientId",t.clientID),localStorage.setItem("clientTitle",t.client.title),localStorage.setItem("usePilotTransfer",t.client.usePilotTransfer),localStorage.setItem("useDepartment",t.client.useDepartment),localStorage.setItem("useTripSchedule",t.client.useTripSchedule),localStorage.setItem("departmentId",t.currentDepartmentID),localStorage.setItem("departmentTitle",t.currentDepartment.title);let e=t.groups.nodes.flatMap(e=>e.permissions.nodes).map(e=>e.code);t.superAdmin&&e.push("ADMIN"),localStorage.setItem("permissions",JSON.stringify(e));let i=t.groups.nodes.some(e=>"admin"===e.code)||e.includes("ADMIN");localStorage.setItem("admin",i?"true":"false");let r=t.groups.nodes.some(e=>"crew"===e.code);localStorage.setItem("crew",r?"true":"false"),"true"===localStorage.getItem("superAdmin")?o.push("/select-client?from=login"):o.push("/")}else localStorage.setItem("admin","false"),o.push("/")},onError:e=>{console.error("readOneSeaLogsMember error",e)}}),[E,{loading:L}]=(0,c.D)(m.xUA,{onCompleted:e=>{let t=e.createToken;if(t.token){let e=t.token;localStorage.setItem("sl-jwt",e),localStorage.setItem("firstName",t.member.firstName??""),localStorage.setItem("surname",t.member.surname??""),localStorage.setItem("userId",t.member.id);let o=t.member.superAdmin;localStorage.setItem("superAdmin",o),localStorage.setItem("clientId","0"),localStorage.setItem("useDepartment","false"),localStorage.setItem("usePilotTransfer","false"),localStorage.setItem("useTripSchedule","false");let i=t.member.availableClients??[];localStorage.setItem("availableClients",JSON.stringify(i)),localStorage.setItem("admin","false"),p.defaultContext.token=localStorage?.getItem("sl-jwt")?.toString(),x(!0)}else R("Your username or password is incorrect.")},onError:e=>{console.error("loginUser error",e)}}),w=(0,D.cI)({resolver:(0,u.F)(f),defaultValues:{email:"",password:""}});return(0,i.jsxs)(h.Form,{...w,children:[i.jsx(I.default,{href:"/",className:" lg:hidden z-10",children:i.jsx(b.Z,{color:"white"})}),i.jsx("form",{onSubmit:w.handleSubmit(y),className:"flex flex-col items-center justify-center sm:w-96 z-10 px-6 py-8",children:(0,i.jsxs)("div",{className:"w-full max-w-md bg-card p-6 shadow rounded-lg",children:[i.jsx(h.FormField,{control:w.control,name:"email",render:({field:e})=>(0,i.jsxs)(h.FormItem,{children:[i.jsx(h.FormLabel,{children:"Email / Username"}),i.jsx(h.FormControl,{children:i.jsx(s.I,{type:"text",placeholder:"Enter your email address or username",...e})}),i.jsx(h.FormMessage,{})]})}),i.jsx(h.FormField,{control:w.control,name:"password",render:({field:e})=>(0,i.jsxs)(h.FormItem,{children:[i.jsx(h.FormLabel,{children:"Password"}),i.jsx(h.FormControl,{children:i.jsx(s.I,{type:"password",placeholder:"Enter your password",...e})}),i.jsx(h.FormMessage,{})]})}),S&&i.jsx("div",{className:"text-center text-red-500",children:S}),i.jsx(C.Separator,{className:"my-4"}),(0,i.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[i.jsx(r.Button,{className:"w-full",type:"submit",disabled:L||k,children:L||k?"Logging In...":"Log In"}),i.jsx(I.default,{href:"/lost-password",className:"text-blue-600  hover:underline",children:"I've lost my password"})]})]})})]})}},26509:(e,t,o)=>{"use strict";o.r(t),o.d(t,{Separator:()=>a});var i=o(98768),r=o(60343),s=o(62227),n=o(56937);let a=r.forwardRef(({className:e,orientation:t="horizontal",decorative:o=!0,...r},a)=>i.jsx(s.f,{ref:a,decorative:o,orientation:t,className:(0,n.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...r}));a.displayName=s.f.displayName},87638:(e,t,o)=>{"use strict";o.d(t,{default:()=>r.a});var i=o(9312),r=o.n(i)},9312:(e,t,o)=>{"use strict";let{createProxy:i}=o(38851);e.exports=i("C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\node_modules\\.pnpm\\next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4\\node_modules\\next\\dist\\client\\link.js")},83166:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>l});var i=o(96141);let r=(0,o(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\components\login-form.tsx#LoginForm`);o(32600);var s=o(17784),n=o(87638),a=o(82205);let l=e=>(0,i.jsxs)("div",{className:"lg:container relative lg:mx-auto w-full h-svh md:grid lg:max-w-none lg:grid-cols-2 lg:px-0",children:[(0,i.jsxs)("div",{className:"relative hidden h-full flex-col bg-muted p-10  dark:border-r lg:flex",children:[i.jsx(n.default,{href:"/",className:"hidden z-10 lg:block absolute top-4 left-4",children:i.jsx(a.Z,{color:"white"})}),i.jsx(s.default,{src:"/bg_image/SealogBG_image.avif",width:1920,height:723,alt:"Authentication",className:" absolute object-cover size-full inset-0"})]}),(0,i.jsxs)("div",{className:"size-full flex relative flex-col justify-center items-center",children:[i.jsx(s.default,{src:"/bg_image/SealogBG_image.avif",width:1920,height:723,alt:"Authentication",className:"lg:hidden absolute object-cover size-full"}),i.jsx(r,{})]})]})},82205:(e,t,o)=>{"use strict";o.d(t,{Z:()=>s});var i=o(96141),r=o(17784);let s=function({color:e="default"}){return i.jsx("div",{children:"white"===e?i.jsx(r.default,{src:"/sealogs-horizontal-logo-white.png",alt:"Sealogs Logo",width:300,height:92,className:""}):i.jsx(r.default,{src:"/sealogs-horizontal-logo.png",alt:"Sealogs Logo",width:300,height:92,className:""})})}},62227:(e,t,o)=>{"use strict";o.d(t,{f:()=>d});var i=o(60343),r=o(54928),s=o(98768),n="horizontal",a=["horizontal","vertical"],l=i.forwardRef((e,t)=>{let{decorative:o,orientation:i=n,...l}=e,d=a.includes(i)?i:n;return(0,s.jsx)(r.WV.div,{"data-orientation":d,...o?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...l,ref:t})});l.displayName="Separator";var d=l}};var t=require("../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),i=t.X(0,[864,8865,3563,6263,8189,9507,1264,6451,4234,2925,6342],()=>o(40406));module.exports=i})();