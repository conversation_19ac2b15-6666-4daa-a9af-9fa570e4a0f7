(()=>{var e={};e.id=5831,e.ids=[5831],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},65966:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),r(49050),r(9276),r(78398),r(57757),r(48045);var s=r(40060),a=r(33581),l=r(57567),i=r.n(l),o=r(51650),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d=["",{children:["trip-report-schedules",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49050)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-report-schedules\\edit\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,9276)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-report-schedules\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-report-schedules\\edit\\page.tsx"],u="/trip-report-schedules/edit/page",p={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/trip-report-schedules/edit/page",pathname:"/trip-report-schedules/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8287:(e,t,r)=>{Promise.resolve().then(r.bind(r,85075))},77293:(e,t,r)=>{Promise.resolve().then(r.bind(r,59536))},60826:e=>{e.exports=function(e){return e.split("")}},829:e=>{e.exports=function(e,t,r,s){for(var a=e.length,l=r+(s?1:-1);s?l--:++l<a;)if(t(e[l],l,e))return l;return -1}},65337:(e,t,r)=>{var s=r(829),a=r(35447),l=r(28026);e.exports=function(e,t,r){return t==t?l(e,t,r):s(e,a,r)}},35447:e=>{e.exports=function(e){return e!=e}},77420:e=>{e.exports=function(e,t,r){var s=-1,a=e.length;t<0&&(t=-t>a?0:a+t),(r=r>a?a:r)<0&&(r+=a),a=t>r?0:r-t>>>0,t>>>=0;for(var l=Array(a);++s<a;)l[s]=e[s+t];return l}},49513:(e,t,r)=>{var s=r(70458),a=/^\s+/;e.exports=function(e){return e?e.slice(0,s(e)+1).replace(a,""):e}},30482:(e,t,r)=>{var s=r(77420);e.exports=function(e,t,r){var a=e.length;return r=void 0===r?a:r,!t&&r>=a?e:s(e,t,r)}},74783:(e,t,r)=>{var s=r(65337);e.exports=function(e,t){for(var r=e.length;r--&&s(t,e[r],0)>-1;);return r}},41200:(e,t,r)=>{var s=r(65337);e.exports=function(e,t){for(var r=-1,a=e.length;++r<a&&s(t,e[r],0)>-1;);return r}},73211:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},28026:e=>{e.exports=function(e,t,r){for(var s=r-1,a=e.length;++s<a;)if(e[s]===t)return s;return -1}},66095:(e,t,r)=>{var s=r(60826),a=r(73211),l=r(92115);e.exports=function(e){return a(e)?l(e):s(e)}},70458:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},92115:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",s="\ud83c[\udffb-\udfff]",a="[^"+t+"]",l="(?:\ud83c[\udde6-\uddff]){2}",i="[\ud800-\udbff][\udc00-\udfff]",o="(?:"+r+"|"+s+")?",n="[\\ufe0e\\ufe0f]?",d="(?:\\u200d(?:"+[a,l,i].join("|")+")"+n+o+")*",c=RegExp(s+"(?="+s+")|(?:"+[a+r+"?",r,l,i,"["+t+"]"].join("|")+")"+(n+o+d),"g");e.exports=function(e){return e.match(c)||[]}},14826:(e,t,r)=>{var s=r(22060),a=r(49513),l=r(30482),i=r(74783),o=r(41200),n=r(66095),d=r(16266);e.exports=function(e,t,r){if((e=d(e))&&(r||void 0===t))return a(e);if(!e||!(t=s(t)))return e;var c=n(e),u=n(t),p=o(c,u),h=i(c,u)+1;return l(c,p,h).join("")}},85075:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(98768);r(60343);var a=r(64837);function l({children:e}){return s.jsx(a.Z,{children:e})}},33810:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(98768),a=r(83179),l=r.n(a),i=r(77866);function o({time:e=l()().format("HH:mm"),handleTimeChange:t,timeID:r,fieldName:a="Time",buttonLabel:o="Set To Now",hideButton:n=!1,disabled:d}){return s.jsx(i.j,{disabled:d,value:(()=>{if(!e||!1===e)return new Date;let t=`${l()().format("YYYY-MM-DD")} ${e}`,r=l()(t);return r.isValid()?r.toDate():new Date})(),onChange:t,use24Hour:!0,className:"tablet-sm:w-[300px] w-full",nowButton:!n,nowButtonLabel:o})}},59536:(e,t,r)=>{"use strict";r.d(t,{default:()=>P});var s=r(98768),a=r(13842),l=r(76342),i=r(94060),o=r(72548),n=r(79418),d=r(7678),c=r.n(d),u=r(14826),p=r.n(u),h=r(69424),m=r(60343),x=r(83179),f=r.n(x),v=r(47225),y=r(60797),b=r(52016),j=r(89546),g=r(25394);let S=({tripReportScheduleID:e=0})=>{let t=(0,h.useRouter)(),[r,a]=(0,m.useState)([]),[l]=(0,n.t)(i.m5,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTripReportScheduleStops;t&&a(t.nodes)},onError:e=>{console.error("ReadTripReportScheduleStops error",e)}}),o=async()=>{await l({variables:{filter:{tripReportScheduleID:{eq:e}}}})};return(0,m.useEffect)(()=>{e>0&&o()},[e]),s.jsx("div",{className:"w-full",children:(0,s.jsxs)("div",{className:"w-full",children:[s.jsx("div",{className:"flex w-full justify-end my-4",children:s.jsx(g.zx,{variant:"secondary",iconLeft:b.Z,onClick:()=>{t.push(`/trip-report-schedule-stop/create?tripReportScheduleID=${e}`)},children:"Add Trip Stop"})}),(0,s.jsxs)(j.Z,{headings:[],children:[(0,s.jsxs)("tr",{className:"font-medium border-b border-border hidden md:table-row",children:[s.jsx("td",{className:"hidden md:table-cell py-4 px-[18px] text-left",children:s.jsx(y.Label,{children:"Stop Location"})}),s.jsx("td",{className:"hidden md:table-cell py-4 px-[18px] text-left",children:s.jsx(y.Label,{children:"Arrival Time"})}),s.jsx("td",{className:"hidden md:table-cell py-4 px-[18px] text-left",children:s.jsx(y.Label,{children:"Depart Time"})}),s.jsx("td",{className:"hidden md:table-cell py-4 px-[18px] text-left",children:s.jsx(y.Label,{children:"Pick Up"})}),s.jsx("td",{className:"hidden md:table-cell py-4 px-[18px] text-left",children:s.jsx(y.Label,{children:"Drop Off"})})]}),r.map(e=>(0,s.jsxs)("tr",{className:"border-b border-border text-foreground transition-colors hover:bg-muted/50 even:bg-muted/20",children:[s.jsx("td",{className:"py-4 px-[18px] text-left",children:s.jsx(g.zx,{variant:"text",onClick:()=>t.push(`/trip-report-schedule-stop/edit/?id=${e.id}`),className:"text-foreground hover:text-primary",children:e.stopLocation.title})}),s.jsx("td",{className:"py-4 px-[18px] text-left",children:`${f()(f()().format("YYYY-MM-DD")+" "+e.arriveTime).format("HH:mma")}`}),s.jsx("td",{className:"py-4 px-[18px] text-left",children:`${f()(f()().format("YYYY-MM-DD")+" "+e.departTime).format("HH:mma")}`}),s.jsx("td",{className:"py-4 px-[18px] text-left",children:e.pickUp?"Yes":"No"}),s.jsx("td",{className:"py-4 px-[18px] text-left",children:e.dropOff?"Yes":"No"})]},e.id))]})]})})};var N=r(71890),T=r(81524),D=r(26509),C=r(74602),w=r(34376),L=r(13609),k=r(3510),R=r(33810),E=r(39544);let P=()=>{let e=(0,h.useRouter)(),t=(0,h.useSearchParams)().get("id")??0,[r,d]=(0,m.useState)({}),[u,x]=(0,m.useState)([]),[b,j]=(0,m.useState)([]),[g,P]=(0,m.useState)([]),[q,I]=(0,m.useState)(null),[M,B]=(0,m.useState)([]),[Y,H]=(0,m.useState)(null),[_,Z]=(0,m.useState)(null),V=[{value:"EveryDay",label:"Every Day"},{value:"WeekDays",label:"Days of the Week"}],[$,A]=(0,m.useState)(V[0]),[U,O]=(0,m.useState)([]),[G,W]=(0,m.useState)(!1),F=(e,t)=>{let s=[...U];O(s=t?[...U,e]:s.filter(t=>t!==e)),d({...r,daysOfWeek:s.join(",")})},[z,{loading:Q}]=(0,o.D)(l.sn,{onCompleted:t=>{t.deleteTripReportSchedules?(ei({title:"Success",description:"Trip Report Schedule deleted successfully"}),e.push("/trip-report-schedules")):ei({variant:"destructive",title:"Error",description:"Error deleting Trip Report Schedule"})},onError:e=>{ei({variant:"destructive",title:"Error",description:`Error: ${e.message}`}),console.error("deleteTripReportSchedule onError",e.message)}}),[X,{loading:J}]=(0,n.t)(i.mR,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t={...e.readOneTripReportSchedule};if(t){let e=t.vehicles.nodes.map(e=>e.id);d({...t,vehicles:e.join(",")}),I({value:t.tripScheduleService.id,label:t.tripScheduleService.title}),H({value:t.fromLocation.id,label:t.fromLocation.title}),Z({value:t.toLocation.id,label:t.toLocation.title}),A(V.find(e=>e.value===t.scheduleType))}},onError:e=>{console.error("readOneTripReportSchedule error",e)}}),[K,{loading:ee}]=(0,n.t)(i.Ss,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTripScheduleServices.nodes;t&&P(t.map(e=>({value:e.id,label:e.title})))},onError:e=>{console.error("readTripScheduleServices error",e)}}),[et,{loading:er}]=(0,n.t)(i.IR,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readGeoLocations.nodes;t&&B(t.map(e=>({value:e.id,label:e.title})))},onError:e=>{console.error("readGeoLocations error",e)}}),es=async()=>{await X({variables:{id:t}})},ea=async()=>{await K()},el=async()=>{await et()},{toast:ei}=(0,w.pm)(),eo=()=>{let e="";return c()(p()(r.title))&&(e+="\nThe title is required."),c()(b)&&(e+="\nAt least one vessel is required."),!!c()(p()(e))||(ei({variant:"destructive",title:"Validation Error",description:p()(e)}),!1)},[en,{loading:ed}]=(0,o.D)(l.T7x,{onCompleted:t=>{e.push("/trip-report-schedules")},onError:e=>{ei({variant:"destructive",title:"Error",description:e.message}),console.error("updateTripReportSchedule onError",e.message)}}),ec=async()=>{if(eo()&&+t>0){let e={...r};delete e.__typename,delete e.fromLocation,delete e.toLocation,delete e.tripReportScheduleStops,delete e.tripReport_LogBookEntrySections,delete e.tripScheduleService,await en({variables:{input:e}})}};return(0,a.sy)(e=>{x(e.map(e=>({label:e.title,value:e.id})))}),(0,m.useEffect)(()=>{+t>0&&es()},[t]),(0,m.useEffect)(()=>{c()(u)||c()(r.vehicles)||j(r.vehicles.split(",").map(e=>u.find(t=>+t.value==+e)))},[u,r]),(0,m.useEffect)(()=>{ea(),el()},[]),s.jsx("div",{className:"w-full mb-20 md:mb-0",children:(0,s.jsxs)("div",{className:"px-2 lg:px-4 mt-2 ",children:[s.jsx("div",{className:"flex md:flex-nowrap md:flex-row gap-3 flex-col-reverse flex-wrap justify-between md:items-center items-start",children:(0,s.jsxs)(C.H2,{children:[+t>0?"Edit":"New"," Trip Report Schedule"]})}),s.jsx(D.Separator,{className:"my-4"}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"Schedule Name"}),s.jsx(N.I,{defaultValue:r.title,onChange:e=>{d({...r,title:e.target.value})},type:"text",placeholder:"Title"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"Service"}),s.jsx(T.Combobox,{options:g,value:q,onChange:e=>{I(e),d({...r,tripScheduleServiceID:e.value})},placeholder:"Select Trip Schedule Service"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"Transit Route ID"}),s.jsx(N.I,{defaultValue:r.transitRouteID,onChange:e=>{d({...r,transitRouteID:e.target.value})},type:"text",placeholder:"Transit Route ID"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"Transit Trip ID"}),s.jsx(N.I,{defaultValue:r.transitTripID,onChange:e=>{d({...r,transitTripID:e.target.value})},type:"text",placeholder:"Transit Route ID"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"Vessels"}),s.jsx(T.Combobox,{options:u,value:b,onChange:e=>{j(e),d({...r,vehicles:e.map(e=>e.value).join(",")})},placeholder:"Select Vessels",multi:!0})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"Depart Time"}),s.jsx(R.Z,{time:r.departTime,handleTimeChange:e=>{d({...r,departTime:f()(e).format("HH:mm:ss")})},timeID:"trs-depart-time",fieldName:"Depart Time"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"From"}),s.jsx(T.Combobox,{options:M,value:Y,onChange:e=>{H(e),d({...r,fromLocationID:e.value})},placeholder:"Select location"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"Departure Berth"}),s.jsx(N.I,{defaultValue:r.departureBerth,onChange:e=>{d({...r,departureBerth:e.target.value})},type:"text",placeholder:"Departure Berth"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"To"}),s.jsx(T.Combobox,{options:M,value:_,onChange:e=>{Z(e),d({...r,toLocationID:e.value})},placeholder:"Select location"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"Arrival Berth"}),s.jsx(N.I,{defaultValue:r.arrivalBerth,onChange:e=>{d({...r,arrivalBerth:e.target.value})},type:"text",placeholder:"Arrival Berth"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"Arrival Time"}),s.jsx(R.Z,{time:r.arriveTime,handleTimeChange:e=>{d({...r,arriveTime:f()(e).format("HH:mm:ss")})},timeID:"trs-arrive-time",fieldName:"Arrive Time"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"Next Expected Contact Time"}),s.jsx(R.Z,{time:r.expectedContactTime,handleTimeChange:e=>{d({...r,expectedContactTime:f()(e).format("HH:mm:ss")})},timeID:"trs-expected-contact-time",fieldName:"Expected Contact Time"})]}),s.jsx(D.Separator,{className:"my-6"}),s.jsx("div",{children:s.jsx(C.H4,{className:"mb-4",children:"Schedule Occurence Details"})}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"Start of Schedule Sailed"}),s.jsx(v.Z,{dateID:"trs-start-date",fieldName:"Start Date",date:r.start,handleDateChange:e=>{d({...r,start:f()(e).format("YYYY-MM-DD")})},hideButton:!0})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"End of Schedule Sailed"}),s.jsx(v.Z,{dateID:"trs-end-date",fieldName:"End Date",date:r.end,handleDateChange:e=>{d({...r,end:f()(e).format("YYYY-MM-DD")})},hideButton:!0})]}),s.jsx(D.Separator,{className:"my-6"}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(y.Label,{children:"Schedule Occurs"}),s.jsx(T.Combobox,{options:V,value:$,onChange:e=>{A(e),d({...r,scheduleType:e.value})},placeholder:"Select Schedule Type"})]}),"WeekDays"===$.value&&[{value:"Monday",label:"Monday"},{value:"Tuesday",label:"Tuesday"},{value:"Wednesday",label:"Wednesday"},{value:"Thursday",label:"Thursday"},{value:"Friday",label:"Friday"},{value:"Saturday",label:"Saturday"},{value:"Sunday",label:"Sunday"},{value:"Public Holidays",label:"Public Holidays"}].map((e,t)=>s.jsx("div",{className:"flex items-center my-4 w-full",children:(0,s.jsxs)(y.Label,{className:"relative flex items-center pr-3 rounded-full cursor-pointer",htmlFor:`day-of-week-${t}`,"data-ripple":"true","data-ripple-color":"dark","data-ripple-dark":"true",children:[s.jsx(N.I,{type:"checkbox",id:`day-of-week-${t}`,className:"before:content[''] peer relative h-5 w-5 cursor-pointer p-3 appearance-none rounded-full border border-sky-400 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-sky-500 before:opacity-0 before:transition-opacity checked:border-sky-700 checked:bg-sky-700 before:bg-sky-700 hover:before:opacity-10",checked:U.includes(e.value),onChange:t=>{F(e.value,t.target.checked)}}),s.jsx("span",{className:"absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-1/3 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100"}),s.jsx("span",{className:"ml-3 text-sm font-semibold uppercase",children:e.value})]})},t)),s.jsx(D.Separator,{className:"my-6"}),(0,s.jsxs)("div",{children:[s.jsx(C.H4,{className:"mb-4",children:"Scheduled Stops"}),s.jsx(S,{tripReportScheduleID:+t})]}),s.jsx(D.Separator,{className:"my-6"}),(0,s.jsxs)("div",{className:"flex justify-end gap-2",children:[s.jsx(E.Button,{variant:"text",iconLeft:L.Z,onClick:()=>{e.push("/trip-report-schedules")},children:"Cancel"}),(0,s.jsxs)(E.Button,{iconLeft:k.Z,onClick:ec,isLoading:J||ed,children:[+t>0?"Update":"Save"," Changes"]})]})]})})}},47225:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(98768);r(60343);var a=r(83179),l=r.n(a),i=r(29342),o=r(39544);function n({date:e=!1,handleDateChange:t,dateID:r,fieldName:a="Select date",buttonLabel:n="Set To Now",hideButton:d=!0}){let c=e&&new Date(l()(e).toISOString()).getTime()>0?l()(e).toDate():void 0;return(0,s.jsxs)("div",{className:"flex w-full gap-2.5",children:[s.jsx(i.Z,{id:r,mode:"single",type:"date",placeholder:a,value:c,onChange:e=>{e?e instanceof Date?t(e):"startDate"in e&&e.startDate?t(e.startDate):"from"in e&&e.from&&t(e.from):t(null)},className:"flex-1",clearable:!0}),!d&&s.jsx(o.Button,{onClick:()=>t(new Date),children:n})]})}},89546:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(98768);let a=e=>(0,s.jsxs)("table",{className:" table-auto w-full ",cellPadding:"10",children:[s.jsx("thead",{children:s.jsx("tr",{className:e.showHeader?"":"hidden",children:e.headings.map((t,r)=>s.jsx("th",{scope:"col",className:`pb-3 pt-6 px-2 ${0===r?"rounded-tl-lg":" "}   ${e.headings.length===r+1?"rounded-tr-lg":" "}
                                ${t.includes(":")&&"last"===t.split(":")[1]?"rounded-tr-lg":""}
                                ${t.includes(":")&&"smhidden"===t.split(":")[1]?"hidden sm:block":""}
                                ${t.includes(":")&&"left"===t.split(":")[1]?"text-left":""}
                                ${t.includes(":")&&"firstHead"===t.split(":")[1]?"text-left text-nowrap font-thin  md: lg:text-2xl pl-6 rounded-tl-lg":""}  `,children:t.includes(":")?t.split(":")[0]:t},r))})}),s.jsx("tbody",{className:`  text-foreground ${e?.bodyClass}`,children:e.children})]})},49050:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(96141);let a=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\schedules\trip-report-schedule-form.tsx#default`),l=()=>s.jsx("div",{children:s.jsx(a,{})})},9276:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\trip-report-schedules\layout.tsx#default`)},9999:(e,t,r)=>{"use strict";r.d(t,{a:()=>a});var s=r(60343);function a(e,t,{getInitialValueInEffect:r}={getInitialValueInEffect:!0}){let[a,l]=(0,s.useState)(!!r&&t);return(0,s.useRef)(null),a||!1}},79320:(e,t,r)=>{"use strict";r.d(t,{t:()=>i});var s=r(93140),a=r(69359),l=r(79824);function i(e,t){let r=(0,l.Q)(e);return isNaN(+r)?(0,s.L)(e,NaN):(null!=t.year&&r.setFullYear(t.year),null!=t.month&&(r=(0,a.q)(r,t.month)),null!=t.date&&r.setDate(t.date),null!=t.hours&&r.setHours(t.hours),null!=t.minutes&&r.setMinutes(t.minutes),null!=t.seconds&&r.setSeconds(t.seconds),null!=t.milliseconds&&r.setMilliseconds(t.milliseconds),r)}},99491:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97428).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},72997:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97428).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,6451,4234,2925,5394,4837,6342,3842,8712],()=>r(65966));module.exports=s})();