(()=>{var e={};e.id=2274,e.ids=[2274],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},62889:(e,t,l)=>{"use strict";l.r(t),l.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),l(5350),l(2223),l(78398),l(57757),l(48045);var r=l(40060),n=l(33581),a=l(57567),s=l.n(a),i=l(51650),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);l.d(t,o);let d=["",{children:["inventory",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(l.bind(l,5350)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\new\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(l.bind(l,2223)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(l.bind(l,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(l.bind(l,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(l.bind(l,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(l.t.bind(l,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(l.bind(l,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\new\\page.tsx"],u="/inventory/new/page",p={require:l,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/inventory/new/page",pathname:"/inventory/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},85363:(e,t,l)=>{Promise.resolve().then(l.bind(l,71462))},65237:(e,t,l)=>{Promise.resolve().then(l.bind(l,22910))},47520:(e,t,l)=>{"use strict";l.d(t,{default:()=>n.a});var r=l(19821),n=l.n(r)},19821:(e,t,l)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let r=l(41034);l(98768),l(60343);let n=r._(l(40907));function a(e,t){var l;let r={loading:e=>{let{error:t,isLoading:l,pastDelay:r}=e;return null}};"function"==typeof e&&(r.loader=e);let a={...r,...t};return(0,n.default)({...a,modules:null==(l=a.loadableGenerated)?void 0:l.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96359:(e,t,l)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}});let r=l(90408);function n(e){let{reason:t,children:l}=e;throw new r.BailoutToCSRError(t)}},40907:(e,t,l)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let r=l(98768),n=l(60343),a=l(96359),s=l(58902);function i(e){return{default:e&&"default"in e?e.default:e}}let o={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},d=function(e){let t={...o,...e},l=(0,n.lazy)(()=>t.loader().then(i)),d=t.loading;function c(e){let i=d?(0,r.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,o=t.ssr?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(s.PreloadCss,{moduleIds:t.modules}),(0,r.jsx)(l,{...e})]}):(0,r.jsx)(a.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(l,{...e})});return(0,r.jsx)(n.Suspense,{fallback:i,children:o})}return c.displayName="LoadableComponent",c}},58902:(e,t,l)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return a}});let r=l(98768),n=l(54580);function a(e){let{moduleIds:t}=e,l=(0,n.getExpectedRequestStore)("next/dynamic css"),a=[];if(l.reactLoadableManifest&&t){let e=l.reactLoadableManifest;for(let l of t){if(!e[l])continue;let t=e[l].files.filter(e=>e.endsWith(".css"));a.push(...t)}}return 0===a.length?null:(0,r.jsx)(r.Fragment,{children:a.map(e=>(0,r.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:l.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},71462:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>a});var r=l(98768);l(60343);var n=l(64837);function a({children:e}){return r.jsx(n.Z,{children:e})}},22910:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>I});var r=l(98768),n=l(60343),a=l(72548),s=l(66263),i=l(33849),o=l(76342),d=l(69424),c=l(17380),u=l(13842),p=l(71890),m=l(52269),h=l(60797),v=l(26509),g=l(17203),x=l(81524),y=l(78965),f=l(25394),b=l(94446),j=l(52241),w=l(93778);function C({vesselID:e=0}){let t=(0,d.useSearchParams)(),[l,C]=(0,n.useState)(),[I,N]=(0,n.useState)(),[S,P]=(0,n.useState)(),[L,E]=(0,n.useState)(),[_,k]=(0,n.useState)(!1),[q,B]=(0,n.useState)(),[F,D]=(0,n.useState)(),[T,M]=(0,n.useState)(),[O,V]=(0,n.useState)(),{getVesselWithIcon:R}=(0,j.P)(),[U,A]=(0,n.useState)(!1),[Z,z]=(0,n.useState)(!1),[G,W]=(0,n.useState)(!1),[H,Q]=(0,n.useState)([]),[$,K]=(0,n.useState)([]),[X,Y]=(0,n.useState)([]),J=(0,d.useRouter)();var ee="";(0,u.sy)(t=>{let l=t.filter(e=>!e.archived);if(B([...l.map(e=>({...e})),{title:"Other",id:"0"}]),e>0){let t=l.find(t=>t.id===e);if(t){let l=R(t.id,t);V({label:t.title,value:e,vessel:l})}}}),(0,u.Fb)(e=>{P([{label:" ---- Create supplier ---- ",value:"newSupplier"},...e?.filter(e=>null!==e.name).map(e=>({label:e.name,value:e.id}))])}),(0,u.oA)(e=>{C([{label:" ---- Create Category ---- ",value:"newCategory"},...e?.filter(e=>null!==e.name).map(e=>({label:e.name,value:e.id}))])});let et=async()=>{let t={input:{item:document.getElementById("inventory-name").value?document.getElementById("inventory-name").value:null,description:null,content:ee,quantity:document.getElementById("inventory-qty").value?parseInt(document.getElementById("inventory-qty").value):null,productCode:document.getElementById("inventory-code").value?document.getElementById("inventory-code").value:null,costingDetails:document.getElementById("inventory-cost").value?document.getElementById("inventory-cost").value:null,documents:H.map(e=>e.id).join(","),categories:I?.map(e=>e.value).length?I.map(e=>e.value).join(","):null,suppliers:L?.map(e=>e.value).length?L.map(e=>e.value).join(","):null,location:document.getElementById("inventory-location").value?document.getElementById("inventory-location").value:null,vesselID:e>0?e:O?.value,attachmentLinks:X?X.map(e=>e.value).join(","):""}};await el({variables:t})},[el,{loading:er}]=(0,a.D)(o._Pq,{onCompleted:e=>{e.createInventory.id>0?t.get("redirect_to")?J.push(t?.get("redirect_to")+""):J.back():console.error("mutationcreateInventory error",e)},onError:e=>{console.error("mutationcreateInventory error",e)}}),en=async()=>{let e=document.getElementById("inventory-new-category").value;return await ea({variables:{input:{name:e}}})},[ea,{loading:es}]=(0,a.D)(o.CQz,{onCompleted:e=>{let t=e.createInventoryCategory;t.id>0?(C([...l,{label:t.name,value:t.id}]),N([...I,{label:t.name,value:t.id}]),W(!1)):console.error("mutationcreateInventoryCategory error",e)},onError:e=>{console.error("mutationcreateInventoryCategory error",e)}}),ei=e=>{var t={label:"",value:""};"string"==typeof e&&(t={label:e,value:e}),"object"==typeof e&&(t={label:document.getElementById("inventory-new-location").value,value:document.getElementById("inventory-new-location-id").value?document.getElementById("inventory-new-location-id").value:document.getElementById("inventory-new-location").value}),B([...q.map(e=>({...e})),{Title:t.label,ID:t.value}]),V(t),A(!1)},eo=async()=>{let e=document.getElementById("supplier-name").value,t=document.getElementById("supplier-website").value,l=document.getElementById("supplier-phone").value,r=document.getElementById("supplier-email").value,n=document.getElementById("supplier-address").value;""!==e&&await ed({variables:{input:{name:e,address:n,website:t,email:r,phone:l}}}),z(!1)},[ed,{loading:ec}]=(0,a.D)(o.aL5,{onCompleted:e=>{let t=e.createSupplier;t.id>0?(P([...S,{label:t.name,value:t.id}]),E([...L,{label:t.name,value:t.id}])):console.error("mutationcreateSupplier error",e)},onError:e=>{console.error("mutationcreateSupplier error",e)}}),[eu]=(0,a.D)(o.zfn,{onCompleted:e=>{let t=e.createSeaLogsFileLinks;t.id>0&&(K([...$,t]),X?Y([...X,{label:t.link,value:t.id}]):Y([{label:t.link,value:t.id}]))},onError:e=>{console.error("createSeaLogsFileLinksEntry error",e)}}),ep=e=>{Y(X.filter(t=>t!==e))},em=e=>e.label?(0,r.jsxs)("div",{className:"flex justify-between align-middle mr-2 w-fit",children:[r.jsx(s.default,{href:e.label,target:"_blank",className:"ml-2 ",children:e.label}),r.jsx("div",{className:"ml-2 ",children:r.jsx(f.zx,{iconLeft:"cross_icon",onClick:()=>ep(e)})})]}):null;return(0,r.jsxs)(r.Fragment,{children:[r.jsx(f.Bu,{icon:r.jsx(b.A,{className:"h-12 w-12 ring-1 p-1 rounded-full bg-background"}),title:"New inventory item"}),(0,r.jsxs)(f.Zb,{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid sm:grid-cols-2 gap-4",children:[r.jsx(h.Label,{label:"Inventory name",htmlFor:"inventory-name",children:r.jsx(p.I,{id:"inventory-name",type:"text",placeholder:"Inventory name",className:"w-full"})}),r.jsx(h.Label,{label:"Vessel",htmlFor:"inventory-vessel",children:q?r.jsx(x.Combobox,{id:"inventory-vessel",options:q?.map(e=>{let t=R(e.id,e);return{label:e.title,value:e.id,vessel:t}}),defaultValues:O,placeholder:"Select Vessel",onChange:e=>{"newLocation"===e.value&&A(!0),V(e)}}):r.jsx(c.U3,{})})]}),(0,r.jsxs)("div",{className:"grid sm:grid-cols-2 gap-4",children:[r.jsx(h.Label,{label:"Location",htmlFor:"inventory-location",children:r.jsx(p.I,{id:"inventory-location",type:"text",placeholder:"Location",className:"w-full"})}),r.jsx(h.Label,{label:"Quantity",htmlFor:"inventory-qty",children:r.jsx(p.I,{id:"inventory-qty",type:"number",placeholder:"Quantity",className:"w-full"})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(f.H5,{children:"Description"}),r.jsx(f.P,{children:"Enter details that might help with the maintenance or operation of this item."})]}),r.jsx("div",{className:"col-span-2",children:r.jsx(i.Z,{id:"inventory-Content",handleEditorChange:e=>{ee=e}})})]}),r.jsx(v.Separator,{}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(f.H5,{children:"Inventory details"}),r.jsx(f.P,{children:"In this section categorise the item and add the suppliers where you normally purchase this item and the expected cost. This will help replacing the item in the future."})]}),(0,r.jsxs)("div",{className:"col-span-2 space-y-5",children:[r.jsx(h.Label,{label:"Product code",htmlFor:"inventory-code",className:"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3",children:r.jsx(p.I,{id:"inventory-code",type:"text",placeholder:"Product code",className:"w-full"})}),r.jsx(h.Label,{label:"Categories",htmlFor:"inventory-categories",className:"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3",children:l?r.jsx(x.Combobox,{id:"inventory-categories",multi:!0,options:l,value:I,onChange:e=>{e.find(e=>"newCategory"===e.value)&&W(!0),N(e.filter(e=>"newCategory"!==e.value))}}):r.jsx(c.U3,{})}),r.jsx(h.Label,{label:"Supplier",htmlFor:"inventory-suppliers",className:"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3",children:S?r.jsx(x.Combobox,{id:"inventory-suppliers",multi:!0,value:L,onChange:e=>{e.find(e=>"newSupplier"===e.value)&&z(!0),E(e.filter(e=>"newSupplier"!==e.value))},options:S}):r.jsx(c.U3,{})}),r.jsx(h.Label,{label:"Cost",htmlFor:"inventory-cost",className:"grid grid-cols-1 md:grid-cols-[120px_1fr] items-center gap-3",children:r.jsx(p.I,{id:"inventory-cost",type:"text",placeholder:"Costing details",className:"w-full"})})]})]}),r.jsx(v.Separator,{}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(f.H5,{children:"Attachments"}),r.jsx(f.P,{children:"Upload things like photos of the item, plus warranty and guarantee documents or operating manuals. Add links to any online manuals or product descriptions."})]}),(0,r.jsxs)("div",{className:"col-span-2 space-y-5",children:[r.jsx("div",{className:"w-full flex flex-col space-y-2",children:r.jsx(w.Z,{inputId:0,sectionId:0,buttonType:"button",sectionName:"inventoryID"})}),r.jsx(h.Label,{label:"Links",htmlFor:"inventory-links",children:r.jsx(p.I,{id:"inventory-links",type:"text",placeholder:"Links to manuals or product descriptions",className:"w-full",onKeyDown:async e=>{if("Enter"===e.key){let t=e.target.value;await eu({variables:{input:{link:t}}}),e.target.value=""}}})}),r.jsx("div",{className:"flex flex-wrap gap-2",children:X?X.map(e=>r.jsx("div",{children:em(e)},e.value)):$.map(e=>r.jsx("div",{children:em(e)},e.value))})]})]})]}),(0,r.jsxs)(y.V,{className:"mt-5",children:[r.jsx(f.zx,{variant:"back",iconLeft:g.Z,onClick:()=>J.back(),children:"Cancel"}),r.jsx(f.zx,{onClick:et,children:"Create inventory"})]}),(0,r.jsxs)(f.h9,{openDialog:U,setOpenDialog:A,handleCreate:()=>ei({}),title:"Create New Location",actionText:"Create Location",children:[r.jsx(h.Label,{label:"Location",htmlFor:"inventory-new-location",className:"my-4",children:r.jsx(p.I,{id:"inventory-new-location",type:"text",placeholder:"Location"})}),r.jsx(h.Label,{label:"Location ID",htmlFor:"inventory-new-location-id",children:r.jsx(p.I,{id:"inventory-new-location-id",type:"text",placeholder:"Location ID"})})]}),r.jsx(f.h9,{openDialog:Z,setOpenDialog:z,handleCreate:eo,actionText:"Create supplier",className:"lg:max-w-lg",title:"Create new supplier",children:(0,r.jsxs)("div",{className:"mt-4 space-y-4",children:[r.jsx(h.Label,{label:"Supplier name",htmlFor:"supplier-name",children:r.jsx(p.I,{id:"supplier-name",type:"text",placeholder:"Supplier name"})}),r.jsx(h.Label,{label:"Website",htmlFor:"supplier-website",children:r.jsx(p.I,{id:"supplier-website",type:"text",placeholder:"Website"})}),r.jsx(h.Label,{label:"Phone",htmlFor:"supplier-phone",children:r.jsx(p.I,{id:"supplier-phone",type:"text",placeholder:"Phone"})}),r.jsx(h.Label,{label:"Email",htmlFor:"supplier-email",children:r.jsx(p.I,{id:"supplier-email",type:"email",placeholder:"Email"})}),r.jsx(h.Label,{label:"Address",htmlFor:"supplier-address",children:r.jsx(m.Textarea,{id:"supplier-address",rows:4,placeholder:"Supplier address"})})]})}),r.jsx(f.h9,{openDialog:G,setOpenDialog:W,handleCreate:en,actionText:"Create Category",title:"Create new category",children:r.jsx(h.Label,{label:"Category",htmlFor:"inventory-new-category",className:"my-4",children:r.jsx(p.I,{id:"inventory-new-category",type:"text",placeholder:"Category"})})})]})}function I(){let e=(0,d.useSearchParams)().get("vesselID")??0;return r.jsx(C,{vesselID:e})}},52241:(e,t,l)=>{"use strict";l.d(t,{P:()=>s});var r=l(60343),n=l(79418),a=l(94060);let s=()=>{let[e,t]=(0,r.useState)({}),[l,s]=(0,r.useState)(!0),[i]=(0,n.t)(a.N5,{fetchPolicy:"cache-and-network",onCompleted:e=>{if(e.readVessels.nodes){let l={};e.readVessels.nodes.forEach(e=>{l[e.id]={...e,vesselPosition:e.vehiclePositions?.nodes?.[0]||null}}),t(l),s(!1)}},onError:e=>{console.error("queryVesselsWithIcons error",e),s(!1)}});return(0,r.useEffect)(()=>{i({variables:{filter:{archived:{eq:!1}}}})},[]),{vesselIconData:e,loading:l,getVesselWithIcon:(t,l)=>e[t]||l||{id:t,title:"Unknown Vessel"}}}},33849:(e,t,l)=>{"use strict";l.d(t,{Z:()=>i});var r=l(98768);l(60343);var n=l(47520);l(30854);var a=l(56937);let s=(0,n.default)(async()=>{},{loadableGenerated:{modules:["app\\ui\\editor.tsx -> react-quill"]},ssr:!1});function i(e,t){return r.jsx(s,{value:e.content,placeholder:e.placeholder,onChange:e.handleEditorChange,onBlur:e.handleEditorBlur,modules:{toolbar:[[{header:[1,2,3,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"}],["link","image"],[{align:[]},{color:[]}],["clean"]]},formats:["header","bold","italic","underline","strike","blockquote","list","bullet","link","image","align","color","code-block"],className:(0,a.cn)("w-full min-h-60 mb-2 bg-card border overflow-auto text-input border-border rounded-lg",t)})}},93778:(e,t,l)=>{"use strict";l.d(t,{Z:()=>p});var r=l(98768),n=l(60343),a=l(79418),s=l(75776),i=l(94060),o=l(39544),d=l(78853),c=l(69422),u=l(34376);function p({inputId:e=0,buttonType:t="icon",sectionName:l="logBookEntryID",sectionId:p=0,editable:m=!0}){let[h,v]=(0,n.useState)([]),[g,x]=(0,n.useState)([]),[y]=(0,a.t)(i.HC,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readCaptureImages.nodes;t&&v(t)},onError:e=>{console.error("getFieldImages error",e)}}),f=async()=>{await y({variables:{filter:{[l]:{eq:p}}}})};return r.jsx(r.Fragment,{children:0===e||0===p?r.jsx("div",{className:"w-full flex",children:r.jsx(o.Button,{variant:"icon"===t?"ghost":"outline",size:"icon",iconOnly:"icon"===t,title:"Add comment",className:"icon"===t?"group":"h-10",iconLeft:r.jsx(d.Z,{className:"icon"===t?(0,c.cn)("text-curious-blue-400 group-hover:text-curious-blue-400/50","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300"):"",size:24}),onClick:()=>(0,u.Am)({title:"Please save the section first",description:"You need to save the section in order to capture or upload images.",variant:"destructive"}),children:"button"===t&&"Capture / Upload Image"})}):(0,r.jsxs)(r.Fragment,{children:[g.length>0&&r.jsx("div",{className:"flex flex-wrap mb-4",children:g.map((e,t)=>r.jsx("div",{className:"w-1/5 p-1 rounded-md relative",children:r.jsx("img",{src:e.imageData,alt:`Captured ${t}`,className:"object-cover rounded-md"},t)},t))}),m&&r.jsx("div",{className:"w-full flex",children:r.jsx(s.Z,{file:!!(h&&Array.isArray(h))&&h.filter(t=>t.fieldName===e).sort((e,t)=>t.id-e.id),setFile:f,inputId:e.toString(),buttonType:"button",sectionData:{id:p,sectionName:l}})})]})})}},50058:(e,t,l)=>{"use strict";l.d(t,{k:()=>a});var r=l(9999);let n={tiny:"320px",small:"375px",standard:"430px",phablet:"480px","tablet-sm":"600px","tablet-md":"768px","tablet-lg":"834px",landscape:"1024px",laptop:"1280px",desktop:"1536px"};function a(e){let t={...n,...e},l={};return Object.keys(t).forEach(e=>{let n=t[e];l[e]=(0,r.a)(`(min-width: ${n})`)}),l}},2223:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>r});let r=(0,l(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\inventory\layout.tsx#default`)},5350:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>r});let r=(0,l(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\inventory\new\page.tsx#default`)},30854:()=>{},9999:(e,t,l)=>{"use strict";l.d(t,{a:()=>n});var r=l(60343);function n(e,t,{getInitialValueInEffect:l}={getInitialValueInEffect:!0}){let[n,a]=(0,r.useState)(!!l&&t);return(0,r.useRef)(null),n||!1}}};var t=require("../../../webpack-runtime.js");t.C(e);var l=e=>t(t.s=e),r=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,6451,4234,2925,5394,4837,6342,3842,7380,5776],()=>l(62889));module.exports=r})();