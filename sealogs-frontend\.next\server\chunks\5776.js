"use strict";exports.id=5776,exports.ids=[5776],exports.modules={46347:(e,t,a)=>{a.d(t,{Z:()=>f});var r=a(98768),i=a(60343),n=a(28147),s=a(83048),o=a.n(s),c=a(25394),l=a(67537),d=a(71890),m=a(56937);function u({files:e,onFilesSelected:t,text:a="Documents and Images",subText:s,bgClass:o="",multipleUpload:c=!0,acceptedFileTypes:u=".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf,.csv",isLoading:p=!1,renderFileItem:g,onFileClick:f,children:v,displayFiles:x=!0}){let[h,b]=(0,i.useState)(!1),j=(0,i.useRef)(null),y=(0,m.cn)("relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none",h?"bg-accent border-primary":"bg-accent/50 border-border","text-foreground hover:bg-accent hover:border-primary","min-h-[10rem] cursor-pointer select-none",o),w=e=>{t(e)},N=e=>t=>{t.preventDefault(),b(e)},k=(e,t)=>(0,r.jsxs)("div",{onClick:()=>f?.(e),className:"flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden",children:[r.jsx(n.default,{src:"/sealogs-document_upload.svg",alt:"Document",width:48,height:48,className:"mb-2"}),r.jsx("div",{className:"text-xs text-center break-all text-muted-foreground",children:e.title})]},t);return(0,r.jsxs)("div",{className:"w-full pt-4 lg:pt-0",children:[e.length>0&&x&&r.jsx("div",{className:"flex flex-wrap gap-4 mb-4",children:e.filter(e=>e.title?.length>0).map((e,t)=>g?g(e,t):k(e,t))}),(0,r.jsxs)("form",{className:y,onSubmit:e=>e.preventDefault(),onDragEnter:N(!0),onDragOver:N(!0),onDragLeave:N(!1),onDrop:e=>{e.preventDefault(),b(!1),e.dataTransfer.files&&w(e.dataTransfer.files)},onClick:()=>{j.current&&(j.current.value="",j.current.click())},"aria-label":"File uploader drop zone",children:[r.jsx("span",{className:"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider",children:a}),r.jsx(d.I,{ref:j,type:"file",className:"hidden",multiple:c,accept:u,onChange:e=>{e.target.files&&w(e.target.files)}}),(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2 pointer-events-none",children:[r.jsx(n.default,{src:"/sealogs-document_upload.svg",alt:"Upload illustration",width:96,height:96,className:"relative -translate-x-2.5",priority:!0}),s&&r.jsx("span",{className:"text-sm font-medium text-neutral-400",children:s})]})]}),p&&(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2",children:[r.jsx(l.Z,{className:"h-5 w-5 animate-spin text-primary"}),r.jsx("span",{className:"text-sm text-muted-foreground",children:"Uploading..."})]}),v]})}var p=a(34376);let g=new(o()).S3({endpoint:"https://ddde1c1cd1aa25641691808dcbafdeb7.r2.cloudflarestorage.com",accessKeyId:"06c3e13a539f24e6fdf7075bf381bf5e",secretAccessKey:"****************************************************************",signatureVersion:"v4",region:"auto"});function f({files:e=[],setFiles:t,multipleUpload:a=!0,text:s="Documents and Images",subText:o,bgClass:l="",accept:d=".xlsx,.xls,image/*,.doc, .docx,.ppt, .pptx,.txt,.pdf,.csv",bucketName:m="sealogs",prefix:f="",displayFiles:v=!0}){let[x,h]=(0,i.useState)(!1),[b,j]=(0,i.useState)(""),[y,w]=(0,i.useState)(!1),[N,k]=(0,i.useState)(0),D=async r=>{let i=N+"-"+f+r.name;if(e?.some(e=>e.title===i)){(0,p.Am)({description:"File with same name already exists!",variant:"destructive"});return}h(!0),g.putObject({Bucket:m,Key:i,Body:r},(e,r)=>{if(h(!1),e)console.error(e),(0,p.Am)({description:"Failed to upload file",variant:"destructive"});else{let e={title:i};a?t(t=>[...t,e]):t([e])}})},C=e=>{g.getObject({Bucket:m,Key:e.title},async(t,a)=>{if(t)console.error(t),(0,p.Am)({description:"Failed to download file",variant:"destructive"});else{let t=e.title.split(".").pop()||"",r=new Blob([a?.Body]),i=URL.createObjectURL(r);if(t.match(/^(jpg|jpeg|png|gif|bmp)$/i))j(i),w(!0);else if(t.match(/^(pdf)$/i)){let e=new Blob([a?.Body],{type:"application/pdf"}),t=URL.createObjectURL(e);window.open(t,"_blank"),URL.revokeObjectURL(t)}else{(0,p.Am)({description:"File type not supported to view. Please save the file to view.",variant:"destructive"});let t=document.createElement("a");t.target="_blank",t.href=i,t.download=e.title,t.click(),URL.revokeObjectURL(i)}}})};return r.jsx(u,{files:e,onFilesSelected:e=>{Array.from(e).forEach(D)},text:s,subText:o,bgClass:l,multipleUpload:a,acceptedFileTypes:d,isLoading:x,renderFileItem:(e,t)=>(0,r.jsxs)("div",{onClick:()=>C(e),className:"flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden",children:[r.jsx(n.default,{src:"/sealogs-document_upload.svg",alt:"Document",width:48,height:48,className:"mb-2"}),r.jsx("div",{className:"text-xs text-center break-all text-muted-foreground",children:e.title.replace(N+"-","")})]},t),displayFiles:v,onFileClick:C,children:r.jsx(c.h9,{openDialog:y,setOpenDialog:w,noButton:!0,actionText:"Close",title:"Image Preview",children:r.jsx("div",{className:"flex items-center justify-center",children:r.jsx("img",{src:b,alt:"Preview",className:"max-w-full max-h-96 object-contain"})})})})}},75776:(e,t,a)=>{a.d(t,{Z:()=>h});var r=a(98768),i=a(60343),n=a(83048),s=a.n(n),o=a(25394),c=a(34376),l=a(78853),d=a(69422),m=a(69424),u=a(72548),p=a(76342),g=a(46347),f=a(93488),v=a(50058);let x=new(s()).S3({endpoint:"https://ddde1c1cd1aa25641691808dcbafdeb7.r2.cloudflarestorage.com",accessKeyId:"06c3e13a539f24e6fdf7075bf381bf5e",secretAccessKey:"****************************************************************",signatureVersion:"v4",region:"auto"});function h({file:e=!1,setFile:t,inputId:a,buttonType:n="icon",sectionData:s={id:0,sectionName:"logBookEntryID"}}){let h=(0,m.useSearchParams)().get("logentryID")??0,[b,j]=(0,i.useState)(!1),[y,w]=(0,i.useState)(!1),[N,k]=(0,i.useState)(!1),[D,C]=(0,i.useState)(0),[I,S]=(0,i.useState)([]),[B,O]=(0,i.useState)(!1),[E,U]=(0,i.useState)(!1),[F,A]=(0,i.useState)([]),[L,z]=(0,i.useState)([]),R=(0,v.k)(),T=e=>{if(!e||!e.name){console.error("No file name provided");return}x.getObject({Bucket:"captures",Key:e.name},async(t,a)=>{if(t)console.error(t),e.id&&_({variables:{ids:[+e.id]}});else{if(!e||!e.name){console.error("No file name provided");return}let t=e.name.split(".").pop()||"",i=new Blob([a?.Body]),n=URL.createObjectURL(i);if(t.match(/^(jpg|jpeg|png|gif|bmp)$/i)){w(n),k(!0);let i=Buffer.from(a?.Body).toString("base64"),s=new TextDecoder().decode(a?.Body);var r=`data:image/${t};base64,${i}`;s.startsWith("�PNG")||(r=s),w(r),void 0===F.find(t=>t.name===e.name)?A(t=>[...t,{...e,imageData:r}]):A(t=>t.map(t=>t.name===e.name?{...t,imageData:r}:t))}else{let t=new TextDecoder().decode(a?.Body);w(t),void 0===F.find(t=>t.name===e.name)?A(a=>[...a,{...e,imageData:t}]):A(a=>a.map(a=>a.name===e.name?{...a,imageData:t}:a)),k(!0)}}})},K=async(t=!1)=>{if(j(!0),U(!1),O(!1),w(null),k(!1),e&&e.length>0&&!t){e.forEach(e=>{T(e)});return}let a=await navigator.mediaDevices.enumerateDevices();if(a.some(e=>"videoinput"===e.kind))S(a.filter(e=>"videoinput"===e.kind));else{(0,c.Am)({description:"No camera found. Please connect a camera.",variant:"destructive"});return}navigator.mediaDevices.getUserMedia({video:{facingMode:"environment"},audio:!1}).then(e=>{let t=document.getElementById("camera-video");t.srcObject=e,t.play()}).catch(e=>{console.error("Error accessing camera:",e)})},P=()=>{let e=document.getElementById("camera-video");e&&e.srcObject&&(e.srcObject.getTracks().forEach(e=>e.stop()),e.srcObject=null)},[V]=(0,u.D)(p.fQS,{onCompleted:e=>{e.createSectionMemberImage,t()},onError:e=>{console.error("Error updating comment",e)}});async function Z(e){var r;e.imageData&&(r=e.name||D+"-capture-"+Date.now()),r||(r=D+"-capture-"+Date.now()),V({variables:{input:{name:r,fieldName:a,imageType:"FieldImage",[s.sectionName]:"logBookEntryID"===s.sectionName?h:s.id}}}),e.imageData&&x.putObject({Bucket:"captures",Key:r,Body:e.imageData},(e,a)=>{e?console.error(e):t()})}let $=()=>{let e=document.getElementById("camera-video");if(!e){console.error("Video element not found");return}let t=e.srcObject?e.srcObject.getVideoTracks()[0].getSettings().deviceId:null,a=I.find(e=>"videoinput"===e.kind&&e.deviceId!==t);a?navigator.mediaDevices.getUserMedia({video:{deviceId:a.deviceId},audio:!1}).then(t=>{e.srcObject=t,e.play()}).catch(e=>{console.error("Error switching camera:",e)}):(0,c.Am)({description:"No other camera found to switch.",variant:"destructive"})},M=()=>{if(0===F.length){(0,c.Am)({description:"Please capture or upload an image first.",variant:"destructive"});return}(0,c.Am)({description:"Please capture or upload an image first.",variant:"destructive"}),F.forEach(e=>{Z(e)}),A([]),z([]),w(null),k(!1),j(!1),P(),(0,c.Am)({description:"Images uploaded successfully."})},[_]=(0,u.D)(p.l9V,{onCompleted:e=>{e.deleteCaptureImage&&(t(),(0,c.Am)({description:"Image deleted successfully."}))},onError:e=>{console.error("Error deleting image",e),(0,c.Am)({description:"Failed to delete image.",variant:"destructive"})}}),W=e=>{A(t=>t.filter(t=>t.name!==e.name)),e.imageData&&(x.deleteObject({Bucket:"captures",Key:e.name||""},(e,t)=>{e?console.error("Error deleting image:",e):(0,c.Am)({description:"Image deleted successfully."})}),e.id&&_({variables:{ids:[+e.id]}}))};return(0,r.jsxs)(r.Fragment,{children:[r.jsx(o.zx,{variant:"icon"===n?"ghost":"outline",iconOnly:"icon"===n,size:"icon"===n?"icon":"default",title:"Add comment",className:"icon"===n?"group":"",iconLeft:r.jsx(l.Z,{className:"icon"===n?(0,d.cn)(e&&e.length>0?"text-curious-blue-400 group-hover:text-curious-blue-400/50":"text-neutral-400 group-hover:text-neutral-400/50","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300"):"",size:24}),onClick:()=>K(!1),children:"button"===n&&(0,f.p)(R.phablet,"Capture / Upload","Capture / Upload Image")}),(0,r.jsxs)(o.h9,{openDialog:b,setOpenDialog:j,title:B?"Files":"Camera",handleCreate:()=>{y?M():(0,c.Am)({description:"Please capture an image first.",variant:"destructive"})},handleCancel:()=>{j(!1),w(null),k(!1),A([]),P(),z([]),t()},actionText:"Save",cancelText:"Close",loading:!1,children:[(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[F.length>0&&r.jsx("div",{className:"flex flex-wrap mb-4",children:F.map((e,t)=>(0,r.jsxs)("div",{className:"w-1/4 p-1 rounded-md relative",children:[r.jsx("img",{src:e.imageData,alt:`Captured ${t}`,className:"object-cover",onClick:()=>{w(e.imageData),k(!0),P()}},t),r.jsx(o.zx,{variant:"destructive",size:"icon",className:"absolute top-1 right-1 p-0 size-5",onClick:()=>{W(e)},children:"\xd7"})]},t))}),B?r.jsx(g.Z,{files:L,setFiles:z,accept:"image/*",bucketName:"captures",multipleUpload:!0,prefix:h+"-",displayFiles:!1}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("video",{id:"camera-video",style:{display:N?"none":"block"}}),r.jsx("img",{src:y,alt:"Captured",style:{display:N?"block":"none"}})]})]}),(0,r.jsxs)("div",{className:"flex items-center mt-4 gap-2 justify-between",children:[!N&&!B&&r.jsx(o.zx,{onClick:()=>{let e=document.getElementById("camera-video");if(!e){console.error("Video element not found");return}let t=document.createElement("canvas");t.width=e.videoWidth,t.height=e.videoHeight;let a=t.getContext("2d");if(!a){console.error("Failed to get canvas context");return}a.drawImage(e,0,0,t.width,t.height);let r=t.toDataURL("image/png");e.srcObject&&(e.srcObject.getTracks().forEach(e=>e.stop()),e.srcObject=null),r&&(w(r),k(!0),A(e=>[...e,{name:D+"-capture-"+Date.now(),imageData:r}]))},className:"mt-2",children:"Capture"}),N&&!B&&r.jsx(r.Fragment,{children:r.jsx(o.zx,{onClick:()=>{w(null),k(!1),K(!0)},className:"mt-2",children:"Recapture"})}),I.length>1&&r.jsx(o.zx,{onClick:()=>{$()},className:"mt-2",children:"Switch Camera"}),B?r.jsx(o.zx,{onClick:()=>{O(!1),K()},className:"mt-2",children:"Capture Image"}):r.jsx(o.zx,{onClick:()=>{P(),O(!0)},className:"mt-2",children:"Upload Image"})]})]})]})}},69422:(e,t,a)=>{a.d(t,{cn:()=>n});var r=a(28411),i=a(5001);function n(...e){return(0,i.m6)((0,r.W)(e))}},93488:(e,t,a)=>{a.d(t,{e:()=>i,p:()=>n});var r=a(50058);function i(e="phablet"){let t=(0,r.k)();return(a,r)=>t[e]?r:a}function n(e,t,a){return e?a:t}},78853:(e,t,a)=>{a.d(t,{Z:()=>r});let r=(0,a(97428).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])}};