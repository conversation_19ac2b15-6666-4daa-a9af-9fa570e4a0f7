(()=>{var e={};e.id=2001,e.ids=[2001],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},39181:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c}),r(32294),r(36722),r(78398),r(57757),r(48045);var s=r(40060),i=r(33581),n=r(57567),a=r.n(n),o=r(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["training-matrix",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32294)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\training-matrix\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,36722)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\training-matrix\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\training-matrix\\page.tsx"],u="/training-matrix/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/training-matrix/page",pathname:"/training-matrix",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88785:(e,t,r)=>{Promise.resolve().then(r.bind(r,39716)),Promise.resolve().then(r.bind(r,73307)),Promise.resolve().then(r.bind(r,39650))},41950:(e,t,r)=>{Promise.resolve().then(r.bind(r,96497))},71241:(e,t,r)=>{var s=r(4171),i=r(96817),n=r(24436),a=Math.max,o=Math.min;e.exports=function(e,t,r){var l,c,d,u,p,x,f=0,h=!1,m=!1,v=!0;if("function"!=typeof e)throw TypeError("Expected a function");function g(t){var r=l,s=c;return l=c=void 0,f=t,u=e.apply(s,r)}function j(e){var r=e-x,s=e-f;return void 0===x||r>=t||r<0||m&&s>=d}function b(){var e,r,s,n=i();if(j(n))return y(n);p=setTimeout(b,(e=n-x,r=n-f,s=t-e,m?o(s,d-r):s))}function y(e){return(p=void 0,v&&l)?g(e):(l=c=void 0,u)}function M(){var e,r=i(),s=j(r);if(l=arguments,c=this,x=r,s){if(void 0===p)return f=e=x,p=setTimeout(b,t),h?g(e):u;if(m)return clearTimeout(p),p=setTimeout(b,t),g(x)}return void 0===p&&(p=setTimeout(b,t)),u}return t=n(t)||0,s(r)&&(h=!!r.leading,d=(m="maxWait"in r)?a(n(r.maxWait)||0,t):d,v="trailing"in r?!!r.trailing:v),M.cancel=function(){void 0!==p&&clearTimeout(p),f=0,l=x=c=p=void 0},M.flush=function(){return void 0===p?u:y(i())},M}},96817:(e,t,r)=>{var s=r(65584);e.exports=function(){return s.Date.now()}},24436:(e,t,r)=>{var s=r(49513),i=r(4171),n=r(15903),a=0/0,o=/^[-+]0x[0-9a-f]+$/i,l=/^0b[01]+$/i,c=/^0o[0-7]+$/i,d=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(n(e))return a;if(i(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=i(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=s(e);var r=l.test(e);return r||c.test(e)?d(e.slice(2),r?2:8):o.test(e)?a:+e}},52241:(e,t,r)=>{"use strict";r.d(t,{P:()=>a});var s=r(60343),i=r(79418),n=r(94060);let a=()=>{let[e,t]=(0,s.useState)({}),[r,a]=(0,s.useState)(!0),[o]=(0,i.t)(n.N5,{fetchPolicy:"cache-and-network",onCompleted:e=>{if(e.readVessels.nodes){let r={};e.readVessels.nodes.forEach(e=>{r[e.id]={...e,vesselPosition:e.vehiclePositions?.nodes?.[0]||null}}),t(r),a(!1)}},onError:e=>{console.error("queryVesselsWithIcons error",e),a(!1)}});return(0,s.useEffect)(()=>{o({variables:{filter:{archived:{eq:!1}}}})},[]),{vesselIconData:e,loading:r,getVesselWithIcon:(t,r)=>e[t]||r||{id:t,title:"Unknown Vessel"}}}},96497:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(98768);r(60343);var i=r(64837);function n({children:e}){return s.jsx(i.Z,{children:e})}},73307:(e,t,r)=>{"use strict";r.d(t,{default:()=>p});var s=r(98768),i=r(11232);r(13842);var n=r(94060),a=r(25394),o=r(79418),l=r(71241),c=r.n(l),d=r(13609),u=r(60343);function p(){let[e,t]=(0,u.useState)([]),[r,l]=(0,u.useState)([]),[d,p]=(0,u.useState)([]),f=c()(p,500),[h]=(0,o.t)(n.$0,{fetchPolicy:"cache-and-network",onCompleted:e=>{},onError:e=>{console.error("queryTrainingTypeList error",e)}}),[m]=(0,o.t)(n.qJ,{fetchPolicy:"cache-and-network",onCompleted:e=>{},onError:e=>{console.error("queryCrewMembers error",e)}});return(0,s.jsxs)(a.Zb,{children:[(0,s.jsxs)("div",{className:"mb-6 flex flex-row gap-4 items-center",children:[s.jsx("p",{children:"Filter By: "}),s.jsx(i.Z,{value:d,onChange:e=>f(e)})]}),s.jsx(a.iA,{className:"w-ful",children:s.jsx(a.xD,{children:(0,s.jsxs)(a.SC,{children:[s.jsx(a.ss,{className:"w-20",children:"\xa0"}),e.map(e=>s.jsx(a.ss,{className:"relative h-[200px] w-20",children:(0,s.jsxs)(a.u,{children:[s.jsx(a.aJ,{children:s.jsx("div",{className:"origin-bottom-left -rotate-[75deg] whitespace-nowrap absolute bottom-1 left-[18px] text-primary",title:e.title,children:e.title})}),s.jsx(a._v,{children:e.title})]})},"training-type-header-"+e.id))]})})}),s.jsx(a.xr,{className:"h-[800px]",children:s.jsx(a.iA,{children:s.jsx(a.RM,{children:r.map(t=>(0,s.jsxs)(a.SC,{children:[s.jsx(a.pj,{className:"p-1 w-20",children:(0,s.jsxs)(a.u,{children:[s.jsx(a.aJ,{children:s.jsx(a.qE,{size:"sm",variant:t.trainingStatus?.label!=="Good"?"destructive":"success",children:s.jsx(a.Q5,{className:"text-sm",children:(0,a.xE)(t.firstName,t.surname)})})}),(0,s.jsxs)(a._v,{children:[t.firstName," ",t.surname??""]})]})}),e.map(e=>s.jsx(a.pj,{className:"w-20",children:(0,s.jsxs)(a.u,{children:[s.jsx(a.aJ,{children:s.jsx(x,{trainingTypeID:e.id,crewMemberData:t})}),(0,s.jsxs)(a._v,{children:[(0,s.jsxs)("div",{className:"flex gap-1 text-sm",children:[s.jsx("span",{children:"Crew:"}),(0,s.jsxs)("span",{children:[t.firstName," ",t.surname??""]})]}),(0,s.jsxs)("div",{className:"flex gap-1 text-sm",children:[s.jsx("span",{children:"Training:"}),s.jsx("span",{children:e.title})]})]})]})},"value-type-"+e.id))]},t.id))})})})]})}let x=({trainingTypeID:e,crewMemberData:t})=>t.trainingStatus.dues.some(t=>t.trainingTypeID==e)?s.jsx("div",{className:"text-accent-foreground border bg-red-vivid-100 border-red-vivid-500 items-center justify-center p-2 rounded-full flex w-8 h-8",children:s.jsx(d.Z,{className:"text-destructive"})}):t.trainingSessions.nodes.some(t=>t.trainingTypes.nodes.some(t=>t.id==e))?s.jsx("div",{className:"text-accent-foreground border bg-bright-turquoise-100 border-bright-turquoise-600 items-center justify-center p-2 rounded-full flex w-8 h-8",children:s.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"#27AB83","aria-hidden":"true",children:s.jsx("path",{fillRule:"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z",clipRule:"evenodd"})})}):s.jsx("div",{className:"text-accent-foreground border bg-outer-space-50 border-border items-center justify-center rounded-full flex w-8 h-8",children:s.jsx("div",{className:"rounded-full size-3 bg-background0"})})},11232:(e,t,r)=>{"use strict";r.d(t,{Z:()=>c});var s=r(98768),i=r(94060),n=r(79418),a=r(60343),o=r(81524),l=r(52241);let c=({value:e,onChange:t,isClearable:r=!1,className:c="",vesselIdOptions:d=[],filterByTrainingSessionMemberId:u=0})=>{let[p,x]=(0,a.useState)(!0),[f,h]=(0,a.useState)([]),[m,v]=(0,a.useState)([]),[g,j]=(0,a.useState)([]),[b,y]=(0,a.useState)([]),{getVesselWithIcon:M,loading:w}=(0,l.P)(),[q,{loading:N}]=(0,n.t)(i.NS,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readVessels.nodes;t&&y(t.filter(e=>!e.archived&&e.title))},onError:e=>{console.error("queryVesselList error",e)}}),k=async()=>{let e={};u>0&&(e={trainingSessions:{members:{id:{contains:u}}}}),q({variables:{filter:e={...e,archived:{eq:!1}}}})};return(0,a.useEffect)(()=>{if(b.length>0&&!w){let e=b.map(e=>{let t=M(e.id,e);return{value:e.id,label:e.title,vessel:t}});e.sort((e,t)=>e.label.localeCompare(t.label)),v(e),h(e)}},[b,w]),(0,a.useEffect)(()=>{p&&(k(),x(!1))},[p]),(0,a.useEffect)(()=>{f.length>0&&j(f.find(t=>t.value===e))},[e,f]),(0,a.useEffect)(()=>{d.length>0?h(m.filter(e=>d.includes(e.value))):h(m)},[d,m]),s.jsx(o.Combobox,{options:f,defaultValues:g,onChange:e=>{j(e),t(e)},isLoading:N&&f&&!p,title:"Vessel",buttonClassName:c,labelClassName:c,placeholder:"Vessel",multi:!1})}},48331:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(39104),i=r(52040);function n(...e){return(0,i.m6)((0,s.W)(e))}},36722:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\training-matrix\layout.tsx#default`)},32294:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(96141),i=r(38811),n=r(38851);let a=(0,n.createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\crew-training\matrix.tsx#default`);(0,n.createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\lib\icons\SealogsCogIcon.tsx#SealogsCogIcon`),r(32600),r(32600),r(32600),r(32600),r(32600),r(32600),r(32600),r(32600),r(32600),r(32600),r(32600),r(32600),r(32600),r(32600),r(32600);var o=r(48331);let l=r(32600);function c({className:e,...t}){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"#f2f4f7",viewBox:"0 0 124.05 122.8",strokeMiterlimit:"10",strokeWidth:"0.25px",strokeLinecap:"round",strokeLinejoin:"round",stroke:"#022450","aria-hidden":"true","data-slot":"icon",className:(0,o.cn)("w-12 h-12 ring-1 p-0.5 shrink-0 rounded-full bg-[#fff]",e)},t),l.createElement("path",{d:"M121.11,84.65l-11.81-11.81c-2.15-2.15-5.53-2.48-8.05-.79l-9.43-16.84c.51-.36.82-.95.82-1.58v-.42h12.92v.42c0,1.07.87,1.94,1.94,1.94s1.94-.87,1.94-1.94v-16.91c0-1.07-.87-1.94-1.94-1.94s-1.94.87-1.94,1.94v.42h-12.92v-.42c0-1.07-.87-1.94-1.94-1.94h-10.32l-5.52-9.86c4.75-6.82,3.08-16.2-3.73-20.95-6.82-4.75-16.2-3.08-20.95,3.73-3.61,5.17-3.61,12.05,0,17.22l-26.39,47.13c-2.53-1.69-5.9-1.36-8.05.79l-11.81,11.81c-1.22,1.23-1.89,2.9-1.85,4.63.04,1.73.79,3.36,2.08,4.51,12,10.8,26.77,18.03,42.66,20.9l-.07.86c-.25,3,1.97,5.63,4.96,5.88.15.01.31.02.46.02h20.71c3.01,0,5.44-2.44,5.44-5.44,0-.15,0-.31-.02-.46l-.07-.86c15.89-2.87,30.66-10.1,42.66-20.9,1.29-1.15,2.04-2.78,2.08-4.51.04-1.73-.62-3.4-1.85-4.63ZM75.64,85.28c-.6-2.42-2.78-4.12-5.27-4.11h-.98v-15.33c3.79-.71,7.45-2.01,10.83-3.87l9.74,17.92c-4.51,2.42-9.33,4.23-14.32,5.38ZM49.38,85.28c-4.99-1.15-9.8-2.96-14.32-5.38l9.74-17.92c3.38,1.85,7.04,3.16,10.83,3.87v15.33h-.98c-2.5-.01-4.68,1.68-5.27,4.11ZM55.63,61.88c-3.14-.66-6.16-1.78-8.98-3.32l8.98-16.52v19.84ZM59.51,34.43s0,0,0-.01v-3.34c1.98.4,4.02.4,6,0v3.71h-1.45c-1.07,0-1.94.87-1.94,1.94v16.91c0,1.07.87,1.94,1.94,1.94h1.45v25.6h-6v-46.74ZM76.73,55.57l1.63,2.99c-2.81,1.54-5.84,2.66-8.98,3.32v-6.31h7.35ZM105.54,41.02v8.32h-12.92v-8.32h12.92ZM92.6,41.34l.04,12.41c0,.48-.38.88-.86.89l-27.73.81c-1.13.03-2.06-.88-2.05-2l.24-17.39c0-.66.55-1.2,1.21-1.19l12.22-.04h3.26l2.81-.04,9,.04c.59-.18,1.91.72,1.91,1.34l-.04,5.17ZM69.38,34.79v-5.08c.96-.5,1.87-1.09,2.7-1.78l3.84,6.86h-6.54ZM62.51,5.16c6.17,0,11.17,5,11.17,11.17s-5,11.17-11.17,11.17-11.17-5-11.17-11.17c0-6.17,5-11.16,11.17-11.17ZM74.01,117.07c-.3.32-.71.51-1.15.51h-20.71c-.87,0-1.57-.7-1.57-1.57,0-.04,0-.09,0-.13l2.39-28.09h0s.11-1.31.11-1.31c.06-.81.75-1.44,1.56-1.43h15.71c.82,0,1.5.62,1.56,1.43l.11,1.3h0s2.39,28.1,2.39,28.1c.04.44-.11.87-.41,1.19Z",fill:"none",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),l.createElement("path",{d:"M62.51,21.34c1.07,0,1.94-.87,1.94-1.94v-6.14c0-1.07-.87-1.94-1.94-1.94s-1.94.87-1.94,1.94v6.14c0,1.07.87,1.94,1.94,1.94Z",fill:"#7B879A",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),l.createElement("path",{d:"M62.51,96.3c-1.07,0-1.94.87-1.94,1.94v6.14c0,1.07.87,1.94,1.94,1.94s1.94-.87,1.94-1.94v-6.14c0-1.07-.87-1.94-1.94-1.94Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),l.createElement("path",{d:"M2.84,92.79c-.88-.79-.93-.9-1.68-2.23-.58-1.03-.6-2.23.22-3.08,1.16-1.2,2.19-2.51,2.21-2.53l12.3-12.38c1.09-1.1,5.02-2.06,6.19-1.06,9.2,8.02,15.53,11.43,27.44,14.09l-2.69,29.15c-17.84-3.3-30.51-9.83-43.99-21.97Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),l.createElement("path",{d:"M645.79,551.37l-79.77-145.99h42.28l69.4,123.65c-10.37,7.98-20.74,15.16-31.91,22.34Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}),l.createElement("path",{d:"M118.98,95.27c-9.89,9.17-24.15,16.08-40.65,19.38l-2.44-29.07c13.07-5.31,12.7-6.9,26.57-14.23,3.54-1.47,4.58-.92,7.1,1.36l12.69,13.17c1.04,1.12,1.41,4.61.37,5.74-.03.03-.63.7-1.28,1.4l-1.34,1.34c-.07.09-.93.83-1.01.9Z",fill:"#fff",stroke:"#022450",strokeMiterlimit:"10",strokeWidth:"1px"}))}r(32600),r(32600);let d=()=>(0,s.jsxs)(s.Fragment,{children:[s.jsx(i.B,{icon:s.jsx(c,{className:"h-12 w-12 ring-1 p-0.5 rounded-full bg-[#fff]"}),title:"Drills / Training Matrix"}),s.jsx(a,{})]})},38811:(e,t,r)=>{"use strict";r.d(t,{B:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\components\ui\list-header.tsx#ListHeader`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,1484,6451,4234,2925,5394,4837,3842],()=>r(39181));module.exports=s})();