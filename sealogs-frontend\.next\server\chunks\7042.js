"use strict";exports.id=7042,exports.ids=[7042],exports.modules={67468:(e,l,a)=>{a.d(l,{default:()=>N});var t=a(98768),s=a(72548),r=a(60343),n=a(71241),i=a.n(n),o=a(7678),d=a.n(o),c=a(14826),u=a.n(c),x=a(69424),m=a(76342),h=a(66263),p=a(78965),f=a(13842),g=a(46776),j=a(71890),v=a(57103),C=a(17203),b=a(81311),y=a(74602),w=a(39544);let N=({crewDutyId:e,onCancel:l,onCreate:a,isPopup:n=!1})=>{let[o,c]=(0,r.useState)({}),N=(0,x.useRouter)(),[D,S]=(0,r.useState)(!1),[I,k]=(0,r.useState)({title:"",abbreviation:""}),[Z,T]=(0,r.useState)(!1),[M,E]=(0,r.useState)(!1);(0,f.UL)(e,c);let O=i()((l,a)=>{c({...o,[l]:a,id:+e})},300),L=e=>{let{name:l,value:a}=e.target;O(l,a)},F=async()=>{let l=!1,a={title:"",abbreviation:""};if(d()(u()(o.title))&&(l=!0,a.title="Title is required"),d()(u()(o.abbreviation))&&(l=!0,a.abbreviation="Abbreviation is required"),l){S(!0),k(a);return}let t={input:{id:+o.id,title:o.title,abbreviation:o.abbreviation}};0===e?await B({variables:t}):await R({variables:t})},[B,{loading:A}]=(0,s.D)(m.fJx,{onCompleted:e=>{let l=e.createCrewDuty;l.id>0?a?a(l):N.back():console.error("mutationCreateCrewDuty error",e)},onError:e=>{console.error("mutationCreateCrewDuty error",e)}}),[R,{loading:P}]=(0,s.D)(m.Qem,{onCompleted:e=>{e.updateCrewDuty.id>0?N.back():console.error("mutationUpdateCrewDuty error",e)},onError:e=>{console.error("mutationUpdateCrewDuty error",e)}}),U=async e=>{await V({variables:{ids:[e.id]}})},[V]=(0,s.D)(m.T5T,{onCompleted:()=>{N.push("/settings/crew-duty/list")},onError:e=>{console.error("mutationDeleteCrewDuty error",e)}});return(0,r.useEffect)(()=>{(0,g.UU)()},[]),(0,t.jsxs)(t.Fragment,{children:[t.jsx("div",{className:" flex justify-between pb-4 pt-3 items-center",children:(0,t.jsxs)(y.H3,{children:[0===e?"Create":"Edit"," Crew Duty"]})}),(0,t.jsxs)("div",{className:`${n?"grid-cols-2":"grid-cols-3"} grid gap-6`,children:[(0,t.jsxs)("div",{className:`${n?"hidden":""} my-4 `,children:["Crew duty details",t.jsx("p",{className:" mt-4 max-w-[25rem] leading-loose",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Esse minima maxime enim, consectetur hic est perferendis explicabo suscipit rem reprehenderit vitae ex sunt corrupti obcaecati aliquid natus et inventore tenetur?"})]}),(0,t.jsxs)("div",{className:"w-full grid col-span-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx(j.I,{name:"title",type:"text",required:!0,defaultValue:o?.title||"",onChange:L,placeholder:"Duty Title"}),t.jsx("small",{className:"text-destructive",children:D&&I.title})]}),(0,t.jsxs)("div",{children:[t.jsx(j.I,{name:"abbreviation",type:"text",placeholder:"Abbreviation",required:!0,defaultValue:o?.abbreviation||"",onChange:L}),t.jsx("small",{className:"text-destructive",children:D&&I.abbreviation})]})]})]}),n?(0,t.jsxs)("div",{className:"flex justify-end pt-8 gap-2.5",children:[t.jsx(w.Button,{variant:"back",onClick:l,children:"Cancel"}),0!==e&&(0,t.jsxs)(t.Fragment,{children:[t.jsx(w.Button,{variant:"destructive",onClick:()=>{E(!0)},children:"Delete"}),t.jsx(v.AlertDialogNew,{openDialog:M,setOpenDialog:E,title:"Delete Crew Duty",description:`Are you sure you want to delete "${o.title}"?`,variant:"danger",actionText:"Delete",handleCreate:()=>U(o)})]}),t.jsx(w.Button,{onClick:F,disabled:A||P,children:`${0===e?"Create":"Update"} Duty`})]}):(0,t.jsxs)(p.V,{children:[t.jsx(h.default,{href:"/settings/crew-duty/list",children:t.jsx(w.Button,{variant:"back",iconLeft:C.Z,className:"mr-2",children:"Cancel"})}),0!==e&&(0,t.jsxs)(t.Fragment,{children:[t.jsx(w.Button,{variant:"destructive",onClick:()=>{T(!0)},children:"Delete"}),t.jsx(v.AlertDialogNew,{openDialog:Z,setOpenDialog:T,title:"Delete Crew Duty",description:`Are you sure you want to delete "${o.title}"?`,variant:"danger",actionText:"Delete",handleCreate:()=>U(o)})]}),t.jsx(w.Button,{iconLeft:b.Z,onClick:F,disabled:A||P,children:`${0===e?"Create":"Update"} Duty`})]})]})}},82102:(e,l,a)=>{a.d(l,{Z:()=>u});var t=a(98768),s=a(94060),r=a(79418),n=a(60343),i=a(59689),o=a(67468),d=a(57103),c=a(81524);let u=({crewDutyID:e=0,onChange:l,label:a,offline:u=!1,placeholder:x="Duty",multi:m=!1,modal:h,hideCreateOption:p=!1})=>{let f=new i.Z,[g,j]=(0,n.useState)(!0),[v,C]=(0,n.useState)([]),[b,y]=(0,n.useState)([]),[w,N]=(0,n.useState)(!1),[D]=(0,r.t)(s.HW,{fetchPolicy:"cache-and-network",onCompleted:l=>{let a=l.readCrewDuties.nodes;if(a){let l=a.filter(e=>!e.archived),t=l;if(p||(t=[{id:0,title:"-- Create New Duty --",archived:!1,abbreviation:"NEW"},...l]),C(t),e>0){let a=l.find(l=>l.id===e);y({label:a.title,value:a.id})}}},onError:e=>{console.error("queryDutiesEntry error",e)}}),S=async()=>{if(u){let l=await f.getAll();if(l){let a=l.filter(e=>!e.archived),t=a;if(p||(t=[{id:0,title:"-- Create New Duty --",archived:!1,abbreviation:"NEW"},...a]),C(t),e>0){let l=a.find(l=>l.id===e);y({label:l.title,value:l.id})}}}else await D()};return(0,n.useEffect)(()=>{g&&(S(),j(!1))},[g]),(0,n.useEffect)(()=>{if(e>0&&v.length>0){let l=v.find(l=>l.id==e);l&&y({label:l.title,value:l.id})}else 0===e&&y(null)},[e,v]),(0,t.jsxs)(t.Fragment,{children:[t.jsx(c.Combobox,{modal:h,options:v.map(e=>({label:`${e.title}`,value:e.id})),label:a,multi:m,value:b,onChange:e=>{if(e&&0===e.value){N(!0);return}y(e),l(e)},placeholder:x}),t.jsx(d.AlertDialogNew,{openDialog:w,setOpenDialog:N,size:"md",noButton:!0,noFooter:!0,className:"space-y-0",children:t.jsx(o.default,{crewDutyId:0,onCancel:()=>N(!1),onCreate:e=>{let a={label:e.title,value:e.id};y(a),l(a),S(),N(!1)},isPopup:!0})})]})}},23160:(e,l,a)=>{a.d(l,{E:()=>c});var t=a(98768),s=a(60343),r=a(27514),n=a(22995),i=a(66263),o=a(46776),d=a(9210);let c=({onChange:e,overdueList:l=!1})=>{let{isMobile:a}=(0,n.Ap)(),[c,u]=(0,s.useState)(!1),[x,m]=(0,s.useState)(l),h=()=>{m(l=>{let a=!l;return e(a),a})};return(0,s.useEffect)(()=>{u(o.Zu)},[]),(0,s.useEffect)(()=>{m(l)},[l]),(0,t.jsxs)(r.DropdownMenu,{children:[t.jsx(r.DropdownMenuTrigger,{asChild:!0,children:t.jsx(d.HV,{size:36})}),(0,t.jsxs)(r.DropdownMenuContent,{side:a?"bottom":"right",align:a?"end":"start",children:[t.jsx(r.DropdownMenuItem,{onClick:()=>h(),children:x?"Overdue Trainings":"Completed Trainings"}),t.jsx(i.default,{href:"/training-type",children:t.jsx(r.DropdownMenuItem,{children:"Training Schedules / types"})}),c&&(0,o.Fs)("RECORD_TRAINING",c)&&t.jsx(i.default,{href:"/crew-training/create",children:t.jsx(r.DropdownMenuItem,{children:"Record A Training"})})]})]})}},26034:(e,l,a)=>{a.d(l,{Z:()=>o});var t=a(98768),s=a(94060),r=a(79418),n=a(60343),i=a(81524);let o=({value:e,onChange:l,isClearable:a=!1,filterByTrainingSessionMemberId:o=0,trainingTypeIdOptions:d=[]})=>{let[c,u]=(0,n.useState)(!0),[x,m]=(0,n.useState)([]),[h,p]=(0,n.useState)([]),[f,g]=(0,n.useState)([]),[j,{loading:v}]=(0,r.t)(s.$0,{fetchPolicy:"cache-and-network",onCompleted:l=>{let a=l.readTrainingTypes.nodes;if(a){let l=a.map(e=>({value:e.id,label:e.title}));l.sort((e,l)=>e.label.localeCompare(l.label)),m(l),p(l),g(l.find(l=>l.value===e))}},onError:e=>{console.error("queryTrainingTypeList error",e)}}),C=async()=>{let e={};o>0&&(e={trainingSessions:{members:{id:{contains:o}}}}),j({variables:{filter:e}})};return(0,n.useEffect)(()=>{c&&(C(),u(!1))},[c]),(0,n.useEffect)(()=>{g(x.find(l=>l.value===e))},[e]),(0,n.useEffect)(()=>{d.length>0&&h.length>0&&m(h.filter(e=>d.includes(e.value)))},[d,h]),t.jsx(i.Combobox,{options:x,value:f,onChange:e=>{g(e),l(e)},isLoading:v,title:"Training Type",placeholder:"Training Type"})}},37042:(e,l,a)=>{a.d(l,{M:()=>A,Z:()=>F});var t=a(98768),s=a(69424),r=a(11232),n=a(26034),i=a(99303),o=a(29342),d=a(82102),c=a(60343),u=a(81524);let x=({value:e,onChange:l,isClearable:a=!1,placeholder:s="Training Status"})=>{let[r,n]=(0,c.useState)(e),[i,o]=(0,c.useState)(!0);return(0,c.useEffect)(()=>{o(!1)},[]),(0,c.useEffect)(()=>{n(e)},[e]),t.jsx("div",{className:"flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3",children:t.jsx("div",{className:"flex items-center",children:!i&&t.jsx(u.Combobox,{options:[{value:"Good",label:"Good"},{value:"Overdue",label:"Overdue"},{value:"Due Soon",label:"Due Soon"}],value:r,onChange:e=>{n(e),l(e)},title:s,placeholder:s})})})};var m=a(71241),h=a.n(m),p=a(94060),f=a(79418);let g=({value:e,onChange:l,isClearable:a=!1,className:s="",supplierIdOptions:r=[]})=>{let[n,i]=(0,c.useState)(!0),[o,d]=(0,c.useState)([]),[x,m]=(0,c.useState)([]),[h,g]=(0,c.useState)([]),[j,{loading:v}]=(0,f.t)(p.l1,{fetchPolicy:"cache-and-network",onCompleted:l=>{let a=l.readSuppliers.nodes;if(a){let l=a.filter(e=>!e.archived).map(e=>({value:e.id,label:e.name||"No Name"}));l.sort((e,l)=>e.label.localeCompare(l.label)),d(l),g(l),m(l.find(l=>l.value===e))}},onError:e=>{console.error("querySupplierList error",e)}}),C=async()=>{await j()};return(0,c.useEffect)(()=>{n&&(C(),i(!1))},[n]),(0,c.useEffect)(()=>{m(o.find(l=>l.value===e))},[e]),(0,c.useEffect)(()=>{r.length>0?d(h.filter(e=>r.includes(e.value))):d(h)},[r,h]),t.jsx("div",{className:"flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3",children:t.jsx("div",{className:"flex items-center",children:t.jsx(u.Combobox,{options:o,value:x,onChange:e=>{m(e),l(e)},className:s,isLoading:v,title:"Supplier",placeholder:"Supplier"})})})},j=({value:e,onChange:l,isClearable:a=!1,className:s="",categoryIdOptions:r=[]})=>{let[n,i]=(0,c.useState)(!0),[o,d]=(0,c.useState)([]),[x,m]=(0,c.useState)([]),[h,g]=(0,c.useState)([]),[j,{loading:v}]=(0,f.t)(p.I,{fetchPolicy:"cache-and-network",onCompleted:l=>{let a=l.readInventoryCategories.nodes;if(a){let l=a.filter(e=>!e.archived).map(e=>({value:e.id,label:e.name||"No Name"}));l.sort((e,l)=>e.label.localeCompare(l.label)),d(l),g(l),m(l.find(l=>l.value===e))}},onError:e=>{console.error("queryCategoryList error",e)}}),C=async()=>{await j()};return(0,c.useEffect)(()=>{n&&(C(),i(!1))},[n]),(0,c.useEffect)(()=>{m(o.find(l=>l.value===e))},[e]),(0,c.useEffect)(()=>{r.length>0?d(h.filter(e=>r.includes(e.value))):d(h)},[r,h]),t.jsx("div",{className:"flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3",children:t.jsx("div",{className:"flex items-center",children:!n&&t.jsx(u.Combobox,{options:o,value:x,onChange:e=>{m(e),l(e)},className:s,isLoading:v,title:"Category",placeholder:"Category"})})})};var v=a(71890);let C=({value:e,onChange:l,isClearable:a=!1})=>{let[s,r]=(0,c.useState)(!0),[n,i]=(0,c.useState)([]),[o,d]=(0,c.useState)([]),[x,{loading:m}]=(0,f.t)(p.Eo,{fetchPolicy:"cache-and-network",onCompleted:l=>{let a=l.readMaintenanceCategories.nodes;if(a){let l=a.filter(e=>!e.archived).map(e=>({value:e.id,label:e.name||"No Name"}));l.sort((e,l)=>e.label.localeCompare(l.label)),i(l),d(l.find(l=>l.value===e))}},onError:e=>{console.error("queryCategoryList error",e)}}),h=async()=>{await x({variables:{clientID:+(localStorage.getItem("clientId")??0)}})};return(0,c.useEffect)(()=>{s&&(h(),r(!1))},[s]),(0,c.useEffect)(()=>{d(n.find(l=>l.value===e))},[e]),t.jsx(t.Fragment,{children:!s&&t.jsx(u.Combobox,{options:n,value:o,onChange:e=>{d(e),l(e)},isLoading:m,title:"Category",placeholder:"Category"})})};var b=a(23160),y=a(60797),w=a(33810),N=a(83179),D=a.n(N),S=a(14692),I=a(39544),k=a(81311),Z=a(50088),T=a(25394),M=a(56937),E=a(96268),O=a(94440),L=a(50058);let F=({onChange:e,vesselIdOptions:l=[],trainingTypeIdOptions:a=[],memberId:r=0,trainerIdOptions:n=[],memberIdOptions:i=[],supplierIdOptions:o=[],categoryIdOptions:d=[],onClick:u,crewData:x,vesselData:m,tripReportFilterData:h={},table:p})=>{let f=(0,s.usePathname)(),[g,j]=(0,c.useState)({vessel:null,supplier:null,category:null}),[v,C]=(0,c.useState)({vesselIdOptions:l,supplierIdOptions:o,categoryIdOptions:d}),b=({type:l,data:a})=>{let t={...g,[l]:a};j(t),y(t),e({type:l,data:a})},y=e=>{let a=o,t=d;e.vessel&&(a=o.filter(l=>l.vesselId===e.vessel.id)),e.supplier&&(t=d.filter(l=>l.supplierId===e.supplier.id)),C({vesselIdOptions:l,supplierIdOptions:a,categoryIdOptions:t})},w=()=>{u()};return t.jsx("div",{children:(0,t.jsxs)("div",{children:["/vessel"===f&&t.jsx(B,{table:p,onChange:b}),"/crew-training"===f&&t.jsx(A,{onChange:b,vesselIdOptions:l,trainingTypeIdOptions:a,memberId:r,trainerIdOptions:n,memberIdOptions:i}),"/crew/info"===f&&t.jsx(er,{onChange:b}),"/crew"===f&&t.jsx(P,{onChange:b}),"/inventory"===f&&t.jsx(V,{onChange:b,vesselIdOptions:v.vesselIdOptions,supplierIdOptions:v.supplierIdOptions,categoryIdOptions:v.categoryIdOptions}),"/inventory/suppliers"===f&&t.jsx(q,{onChange:b}),"/key-contacts"===f&&t.jsx(H,{onChange:b}),"/maintenance"===f&&t.jsx($,{onChange:b}),"/training-type"===f&&t.jsx(z,{onChange:b}),"/reporting"===f&&t.jsx(G,{onChange:b,onClickButton:w,crewData:x,vesselData:m}),"/reporting/crew-seatime-report"===f&&t.jsx(ee,{onChange:b,onClickButton:w}),"/reporting/crew-training-completed-report"===f&&t.jsx(R,{onChange:b,vesselIdOptions:l,trainingTypeIdOptions:a,memberId:r,trainerIdOptions:n,memberIdOptions:i}),"/reporting/simple-fuel-report"===f&&t.jsx(el,{onChange:b,onClickButton:w}),"/reporting/engine-hours-report"===f&&t.jsx(el,{onChange:b,onClickButton:w}),"/reporting/service-report"===f&&t.jsx(el,{onChange:b,onClickButton:w}),"/reporting/activity-reports"===f&&t.jsx(ea,{onChange:b,onClickButton:w}),("/reporting/maintenance-status-activity"===f||"/reporting/maintenance-cost-track"===f)&&t.jsx(et,{onChange:b,onClickButton:w}),("/reporting/fuel-analysis"===f||"/reporting/fuel-tasking-analysis"===f||"/reporting/detailed-fuel-report"===f||"/reporting/fuel-summary-report"===f)&&t.jsx(W,{onChange:b}),"/document-locker"===f&&t.jsx(J,{onChange:b}),"/calendar"===f&&t.jsx(X,{onChange:b}),"/reporting/trip-report"===f&&t.jsx(es,{tripReportFilterData:h,onChange:b})]})})},B=({onChange:e,table:l})=>t.jsx("div",{className:"flex flex-1 items-center justify-between",children:t.jsx(v.I,{type:"search",placeholder:"Search",value:l.getAllColumns()?.[0]?.getFilterValue()??"",onChange:e=>l.getAllColumns()?.[0]?.setFilterValue(e.target.value),className:"h-11 w-[150px] lg:w-[250px]"})}),A=({onChange:e,vesselIdOptions:l=[],trainingTypeIdOptions:a=[],memberId:s=0,trainerIdOptions:d=[],memberIdOptions:u=[],overdueSwitcher:x=!1,excludeFilters:m=[]})=>{let[h,p]=(0,c.useState)(x);(0,c.useEffect)(()=>{p(x)},[x]);let f=(l,a)=>{e({type:l,data:a})},g=(0,L.k)(),j=(0,t.jsxs)("div",{className:"grid xs:grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2.5",children:[!0!=!h&&!m.includes("dateRange")&&t.jsx(o.Z,{onChange:e=>f("dateRange",e),clearable:!0}),!m.includes("trainingType")&&t.jsx(n.Z,{isClearable:!0,onChange:e=>f("trainingType",e),trainingTypeIdOptions:a}),!m.includes("vessel")&&t.jsx(r.Z,{isClearable:!0,onChange:e=>f("vessel",e),vesselIdOptions:l}),!0!=!h&&!m.includes("trainer")&&t.jsx(i.Z,{label:"",placeholder:"Trainer",isClearable:!0,multi:!0,controlClasses:"filter",onChange:e=>f("trainer",e),filterByTrainingSessionMemberId:s,trainerIdOptions:d}),!m.includes("crew")&&!m.includes("member")&&t.jsx(i.Z,{isClearable:!0,label:"",multi:!0,controlClasses:"filter",placeholder:"Crew",onChange:e=>f("member",e),filterByTrainingSessionMemberId:s,memberIdOptions:u})]});return t.jsx(t.Fragment,{children:g.phablet?j:t.jsx(O.UQ,{type:"single",collapsible:!0,className:"w-full mt-2.5",children:(0,t.jsxs)(O.Qd,{value:"maintenance-filters",children:[t.jsx(O.o4,{children:"Filters"}),t.jsx(O.vF,{children:j})]})})})},R=({onChange:e,vesselIdOptions:l=[],trainingTypeIdOptions:a=[],memberId:s=0,trainerIdOptions:o=[],memberIdOptions:d=[]})=>{let u=(l,a)=>{e({type:l,data:a})},[x,m]=(0,c.useState)(!0);return(0,t.jsxs)("div",{className:"flex flex-1 items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 flex-1 w-full",children:[t.jsx(r.Z,{isClearable:!0,onChange:e=>u("vessel",e),vesselIdOptions:l}),t.jsx(n.Z,{isClearable:!0,onChange:e=>u("trainingType",e),trainingTypeIdOptions:a}),t.jsx(i.Z,{isClearable:!0,controlClasses:"filter",onChange:e=>u("trainer",e),filterByTrainingSessionMemberId:s,trainerIdOptions:o}),t.jsx(i.Z,{isClearable:!0,controlClasses:"filter",placeholder:"Crew",onChange:e=>u("member",e),filterByTrainingSessionMemberId:s,memberIdOptions:d})]}),t.jsx("div",{className:"flex",children:t.jsx(b.E,{onChange:e=>{u("overdue",e)},overdueList:x})})]})},P=({onChange:e})=>{let l=(l,a)=>{e({type:l,data:a})};return t.jsx("div",{className:"flex flex-1 items-center justify-between",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 flex-1 w-full",children:[t.jsx(r.Z,{isClearable:!0,onChange:e=>l("vessel",e)}),t.jsx(d.Z,{crewDutyID:0,hideCreateOption:!0,onChange:e=>{l("crewDuty",e)}}),t.jsx(x,{isClearable:!0,onChange:e=>{l("trainingStatus",e)}}),t.jsx(U,{onChange:e=>{l("keyword",e)}})]})})},U=({onChange:e,className:l})=>{let a=h()(e,600);return t.jsx(v.I,{type:"search",className:(0,M.cn)("h-11 w-full lg:w-[250px]",l),placeholder:"Search...",onChange:e=>{a({value:e.target.value})}})},V=({onChange:e,vesselIdOptions:l,supplierIdOptions:a,categoryIdOptions:s})=>{let n=(l,a)=>{e({type:l,data:a})};return t.jsx("div",{className:"flex flex-1 items-center justify-between gap-2.5",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 flex-1 w-full",children:[t.jsx(r.Z,{isClearable:!0,vesselIdOptions:l,onChange:e=>n("vessel",e)}),t.jsx(g,{isClearable:!0,supplierIdOptions:a,onChange:e=>n("supplier",e)}),t.jsx(j,{isClearable:!0,categoryIdOptions:s,onChange:e=>n("category",e)}),t.jsx(U,{onChange:e=>{n("keyword",e)}})]})})},q=({onChange:e})=>{let l=(l,a)=>{e({type:l,data:a})};return t.jsx("div",{className:"flex flex-1 items-center justify-between",children:t.jsx("div",{className:"flex flex-col md:flex-row gap-2.5 flex-1 w-full",children:t.jsx(U,{onChange:e=>{l("keyword",e)}})})})},H=({onChange:e})=>t.jsx("div",{className:"flex flex-1 items-center justify-between",children:t.jsx("div",{className:"flex flex-col md:flex-row gap-2.5 flex-1 w-full",children:t.jsx(U,{onChange:l=>{e({type:"keyword",data:l})}})})}),$=({onChange:e})=>{let l=(0,E.ac)("(max-width: 479px)"),a=(l,a)=>{e({type:l,data:a})},s=(0,t.jsxs)("div",{className:"flex flex-1 flex-wrap items-start justify-between gap-2.5",children:[(0,t.jsxs)("div",{className:"w-full lg:w-auto grid small:grid-cols-2 tablet-sm:grid-cols-3 sm:grid-cols-4 lg:grid-cols-4 gap-2.5",children:[t.jsx("div",{className:"col-auto small:col-span-2 tablet-sm:col-auto",children:t.jsx(o.Z,{className:"border",clearable:!0,placeholder:"Due Date Range",onChange:e=>a("dateRange",e)})}),t.jsx(Q,{isClearable:!0,onChange:e=>a("status",e)}),t.jsx(C,{isClearable:!0,onChange:e=>a("category",e)}),t.jsx(_,{onChange:e=>a("recurring",e)}),t.jsx(i.Z,{isClearable:!0,controlClasses:"filter",placeholder:"Crew",onChange:e=>a("member",e)}),t.jsx(r.Z,{className:"small:col-span-2 tablet-sm:col-span-3",isClearable:!0,onChange:e=>a("vessel",e)})]}),!l&&t.jsx(U,{onChange:e=>{a("keyword",e)}})]});return l?(0,t.jsxs)(t.Fragment,{children:[t.jsx(U,{onChange:e=>{a("keyword",e)}}),t.jsx(O.UQ,{type:"single",collapsible:!0,className:"w-full mt-2.5",children:(0,t.jsxs)(O.Qd,{value:"maintenance-filters",children:[t.jsx(O.o4,{children:"Filters"}),t.jsx(O.vF,{children:s})]})})]}):s},Q=({onChange:e})=>{let[l,a]=(0,c.useState)(!0),[s,r]=(0,c.useState)(),n=[{value:"Open",label:"Open"},{value:"Save_As_Draft",label:"Save as Draft"},{value:"In_Progress",label:"In Progress"},{value:"On_Hold",label:"On Hold"},{value:"Overdue",label:"Overdue"},{value:"Completed",label:"Completed"}];return(0,c.useEffect)(()=>{a(!1)},[]),t.jsx(t.Fragment,{children:n&&!l&&t.jsx(u.Combobox,{options:n,value:s,onChange:l=>{r(l),e(l)},title:"Status",placeholder:"Status"})})},_=({onChange:e})=>{let[l,a]=(0,c.useState)();return t.jsx(u.Combobox,{options:[{value:"recurring",label:"Recurring"},{value:"one-off",label:"One-off"}],value:l,onChange:l=>{a(l),e(l)},placeholder:"Task Type"})},z=({onChange:e})=>{let l=(l,a)=>{e({type:l,data:a})};return(0,t.jsxs)("div",{className:"grid grid-cols-5 gap-2.5",children:[t.jsx(r.Z,{isClearable:!0,className:"col-span-3 sm:col-span-2",onChange:e=>l("vessel",e)}),t.jsx("div",{className:"col-span-2 sm:col-span-1 col-end-6 sm:col-end-6",children:t.jsx(U,{className:"!w-full",onChange:e=>{l("keyword",e)}})})]})},G=({onChange:e,onClickButton:l,crewData:a,vesselData:s})=>{let n=(l,a)=>{e({type:l,data:a})},[d,u]=(0,c.useState)(!0),[x,m]=(0,c.useState)(!0);return(0,c.useEffect)(()=>{a.length>1?m(!1):m(!0),s.length>1?u(!1):u(!0)},[a,s]),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 w-full",children:[t.jsx("div",{className:"mr-2",children:t.jsx(o.Z,{className:"border ",onChange:e=>n("dateRange",e)})}),t.jsx("div",{className:"mr-2",children:t.jsx(i.Z,{isClearable:!0,controlClasses:"filter",placeholder:"Crew",onChange:e=>n("member",e),isMulti:d})}),t.jsx("div",{className:"mr-2",children:t.jsx(r.Z,{isClearable:!0,onChange:e=>n("vessel",e),isMulti:x})}),t.jsx("div",{className:"mr-2",children:t.jsx(Z.Z,{text:"Report",type:"primary",color:"sky",action:()=>{l()}})})]})},W=({onChange:e})=>{let[l,a]=(0,c.useState)({from:new Date,to:new Date}),s=(l,a)=>{e({type:l,data:a})};return(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 w-full",children:[t.jsx("div",{className:"mr-2",children:t.jsx(o.Z,{type:"date",mode:"range",value:l,dateFormat:"MMM do, yyyy",onChange:e=>{a({from:e?.startDate,to:e?.endDate}),s("dateRange",e)}})}),t.jsx("div",{className:"mr-2",children:t.jsx(r.Z,{isClearable:!0,onChange:e=>s("vessel",e)})})]})},J=({onChange:e})=>{let l=(l,a)=>{e({type:l,data:a})};return(0,t.jsxs)("div",{className:"flex flex-col justify-between md:flex-row gap-2.5 flex-1",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 flex-1 w-full",children:[t.jsx(r.Z,{isClearable:!0,onChange:e=>l("vessel",e),classesName:"min-w-52"}),t.jsx(Y,{onChange:e=>{l("Module",e)},classesName:"min-w-52"})]}),t.jsx(U,{onChange:e=>{l("keyword",e)}})]})},Y=({onChange:e,multi:l=!0,className:a})=>{let[s,r]=(0,c.useState)(!0),[n,i]=(0,c.useState)([]),o=[{value:"Vessel",label:"Vessel"},{value:"Maintenance",label:"Maintenance"},{value:"Inventory",label:"Inventory"},{value:"Company",label:"Company"}];return(0,c.useEffect)(()=>{r(!1)},[]),t.jsx(t.Fragment,{children:o&&!s&&t.jsx(u.Combobox,{options:o,value:n,onChange:l=>{i(l),e(l)},title:"Module",placeholder:"Module",className:a,multi:l})})},K=({onChange:e})=>{let[l,a]=(0,c.useState)(!0),s=[{value:"Task",label:"Maintenance"},{value:"Completed Training",label:"Completed Training"},{value:"Training Due",label:"Training Due"},{value:"Log Book Entry",label:"Log Book Entry"}];return(0,c.useEffect)(()=>{a(!1)},[]),t.jsx(t.Fragment,{children:s&&!l&&t.jsx(u.Combobox,{id:"document-module-dropdown",options:s,onChange:l=>{e("Module",l)},className:"max-w-[200px]",placeholder:"Module"})})},X=({onChange:e})=>{let l=(l,a)=>{e({type:l,data:a})};return t.jsx(T.Zb,{children:(0,t.jsxs)(T.aY,{className:"flex gap-2.5",children:[t.jsx(r.Z,{isClearable:!0,onChange:e=>l("vessel",e)}),t.jsx(i.Z,{isClearable:!0,controlClasses:"filter",placeholder:"Crew",onChange:e=>l("member",e)}),t.jsx(K,{onChange:(e,a)=>{l("Module",a)}})]})})},ee=({onChange:e,onClickButton:l})=>{let[a,s]=(0,c.useState)("detailed"),[n,u]=(0,c.useState)({from:new Date,to:new Date}),x=(l,a)=>{e({type:l,data:a})};return(0,t.jsxs)("div",{className:"flex flex-col gap-2.5",children:[t.jsx("div",{className:"flex justify-start",children:t.jsx(T.mQ,{value:a,onValueChange:e=>{s(e),x("reportMode",e)},children:(0,t.jsxs)(T.dr,{children:[t.jsx(T.SP,{value:"detailed",children:"Detailed View"}),t.jsx(T.SP,{value:"summary",children:"Summarized View"})]})})}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 w-full",children:[t.jsx("div",{children:t.jsx(o.Z,{type:"date",mode:"range",value:n,dateFormat:"MMM do, yyyy",onChange:e=>{u({from:e?.startDate,to:e?.endDate}),x("dateRange",e)}})}),t.jsx("div",{children:t.jsx(i.Z,{isClearable:!0,controlClasses:"filter",placeholder:"Crew",onChange:e=>{x("members",e)},isMulti:!0})}),t.jsx("div",{children:t.jsx(r.Z,{isClearable:!0,onChange:e=>x("vessels",e),isMulti:!0})}),t.jsx("div",{children:t.jsx(d.Z,{crewDutyID:0,onChange:e=>{x("crewDuty",e)}})})]}),t.jsx("div",{children:t.jsx(I.Button,{type:"button",iconLeft:k.Z,onClick:()=>{l()},children:"Apply Filter"})})]})},el=({onChange:e,onClickButton:l})=>{let[a,s]=(0,c.useState)({from:new Date,to:new Date}),n=(l,a)=>{e({type:l,data:a})};return(0,t.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center gap-2.5 mt-2",children:[t.jsx("div",{children:t.jsx(o.Z,{type:"date",mode:"range",value:a,dateFormat:"MMM do, yyyy",onChange:e=>{s({from:e?.startDate,to:e?.endDate}),n("dateRange",e)}})}),t.jsx("div",{children:t.jsx(r.Z,{isClearable:!0,onChange:e=>n("vessels",e),isMulti:!0})}),t.jsx("div",{children:t.jsx(I.Button,{type:"button",iconLeft:k.Z,onClick:()=>{l()},children:"Apply Filter"})})]})},ea=({onChange:e,onClickButton:l})=>{let[a,s]=(0,c.useState)({from:new Date,to:new Date}),n=(l,a)=>{e({type:l,data:a})},[i,d]=(0,c.useState)();return(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 mt-2 w-full",children:[t.jsx("div",{children:t.jsx("div",{className:"flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3",children:t.jsx("div",{className:"flex items-center"})})}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 w-full",children:[t.jsx(o.Z,{type:"date",mode:"range",value:a,dateFormat:"MMM do, yyyy",onChange:e=>{s({from:e?.startDate,to:e?.endDate}),n("dateRange",e)}}),t.jsx(r.Z,{isClearable:!0,onChange:e=>n("vessels",e),isMulti:!0}),t.jsx(I.Button,{type:"button",iconLeft:k.Z,onClick:()=>{l()},children:"Apply Filter"})]})]})},et=({onChange:e,onClickButton:l})=>{let[a,s]=(0,c.useState)({from:new Date,to:new Date}),n=(l,a)=>{e({type:l,data:a})};return(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 mt-2 w-full",children:[t.jsx("div",{children:t.jsx(o.Z,{type:"date",mode:"range",value:a,dateFormat:"MMM do, yyyy",onChange:e=>{s({from:e?.startDate,to:e?.endDate}),n("dateRange",e)}})}),t.jsx("div",{children:t.jsx(r.Z,{isClearable:!0,onChange:e=>n("vessels",e),isMulti:!0})}),t.jsx("div",{children:t.jsx(C,{isClearable:!0,onChange:e=>n("category",e)})})]}),(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 mt-2 w-full",children:[t.jsx("div",{children:t.jsx(Q,{onChange:e=>n("status",e)})}),t.jsx("div",{children:t.jsx(i.Z,{isClearable:!0,controlClasses:"filter",placeholder:"Allocated Crew",onChange:e=>n("member",e)})}),t.jsx("div",{children:t.jsx(I.Button,{type:"button",iconLeft:k.Z,onClick:()=>{l()},children:"Apply Filter"})})]})]})},es=({tripReportFilterData:e,onChange:l})=>{let a=(e,a)=>{l({type:e,data:a})};return(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 w-full",children:[t.jsx("div",{className:"mr-2",children:t.jsx(o.Z,{className:"border border-slblue-200",onChange:e=>a("dateRange",e)})}),t.jsx("div",{className:"mr-2",children:t.jsx(S.Z,{handleLocationChange:e=>{if(!e){a("fromLocation",null);return}a("fromLocation",e)},setCurrentLocation:()=>{},currentEvent:{},showAddNewLocation:!1,showUseCoordinates:!1,showCurrentLocation:!1})}),t.jsx("div",{className:"mr-2",children:t.jsx(S.Z,{handleLocationChange:e=>{if(!e){a("toLocation",null);return}a("toLocation",e)},setCurrentLocation:()=>{},currentEvent:{},showAddNewLocation:!1,showUseCoordinates:!1,showCurrentLocation:!1})}),t.jsx("div",{className:"mr-2",children:t.jsx(w.Z,{time:e.fromTime??"",timeID:"from-time",fieldName:"From",buttonLabel:"Set To Now",hideButton:!0,handleTimeChange:e=>a("fromTime",D()(e).format("HH:mm"))})}),t.jsx("div",{className:"mr-2",children:t.jsx(w.Z,{time:e.toTime??"",timeID:"to-time",fieldName:"To",buttonLabel:"Set To Now",hideButton:!0,handleTimeChange:e=>a("toTime",D()(e).format("HH:mm"))})}),t.jsx("div",{className:"mr-2",children:t.jsx("div",{className:"flex items-center my-4 w-full",children:(0,t.jsxs)(y.Label,{className:"relative flex items-center pr-3 rounded-full cursor-pointer",htmlFor:"client-use-department","data-ripple":"true","data-ripple-color":"dark","data-ripple-dark":"true",children:[t.jsx(v.I,{type:"checkbox",id:"client-use-department",className:"before:content[''] peer relative h-5 w-5 cursor-pointer p-3 appearance-none rounded-full border border-sky-400 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-sky-500 before:opacity-0 before:transition-opacity checked:border-sky-700 checked:bg-sky-700 before:bg-sky-700 hover:before:opacity-10",defaultChecked:e.noPax,onChange:e=>{a("noPax",e.target.checked)}}),t.jsx("span",{className:"absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-1/3 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100"}),t.jsx("span",{className:"ml-3 text-sm font-semibold uppercase",children:"Trips with Zero Pax"})]})})}),t.jsx("div",{className:"mr-2",children:t.jsx(r.Z,{isClearable:!0,isMulti:!0,onChange:e=>a("vessels",e)})})]})},er=({onChange:e})=>{let l=(l,a)=>{e({type:l,data:a})};return t.jsx("div",{className:"flex flex-1 items-center justify-between",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row gap-2.5 flex-1 w-full",children:[t.jsx(r.Z,{isClearable:!0,onChange:e=>l("vessel",e)}),t.jsx(Q,{isClearable:!0,onChange:e=>l("status",e)}),t.jsx(U,{onChange:e=>{l("keyword",e)}})]})})}}};