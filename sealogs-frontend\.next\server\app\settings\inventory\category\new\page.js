(()=>{var e={};e.id=4108,e.ids=[4108],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},88918:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>h,originalPathname:()=>d,pages:()=>l,routeModule:()=>f,tree:()=>c}),r(37938),r(50681),r(78398),r(57757),r(48045);var n=r(40060),s=r(33581),i=r(57567),a=r.n(i),o=r(51650),u={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);r.d(t,u);let c=["",{children:["settings",{children:["inventory",{children:["category",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,37938)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\inventory\\category\\new\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50681)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\inventory\\category\\new\\page.tsx"],d="/settings/inventory/category/new/page",h={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/settings/inventory/category/new/page",pathname:"/settings/inventory/category/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},48222:(e,t,r)=>{Promise.resolve().then(r.bind(r,37111))},31941:(e,t,r)=>{Promise.resolve().then(r.bind(r,84313))},83179:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",r="minute",n="hour",s="week",i="month",a="quarter",o="year",u="date",c="Invalid Date",l=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,d=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,h=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},f="en",y={};y[f]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||"th")+"]"}};var g="$isDayjsObject",p=function(e){return e instanceof x||!(!e||!e[g])},v=function e(t,r,n){var s;if(!t)return f;if("string"==typeof t){var i=t.toLowerCase();y[i]&&(s=i),r&&(y[i]=r,s=i);var a=t.split("-");if(!s&&a.length>1)return e(a[0])}else{var o=t.name;y[o]=t,s=o}return!n&&s&&(f=s),s||!n&&f},m=function(e,t){if(p(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new x(r)},$={s:h,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+h(Math.floor(r/60),2,"0")+":"+h(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),s=t.clone().add(n,i),a=r-s<0,o=t.clone().add(n+(a?-1:1),i);return+(-(n+(r-s)/(a?s-o:o-s))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(c){return({M:i,y:o,w:s,d:"day",D:u,h:n,m:r,s:t,ms:e,Q:a})[c]||String(c||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};$.l=v,$.i=p,$.w=function(e,t){return m(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var x=function(){function h(e){this.$L=v(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[g]=!0}var f=h.prototype;return f.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if($.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(l);if(n){var s=n[2]-1||0,i=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,i)):new Date(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,i)}}return new Date(t)}(e),this.init()},f.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},f.$utils=function(){return $},f.isValid=function(){return this.$d.toString()!==c},f.isSame=function(e,t){var r=m(e);return this.startOf(t)<=r&&r<=this.endOf(t)},f.isAfter=function(e,t){return m(e)<this.startOf(t)},f.isBefore=function(e,t){return this.endOf(t)<m(e)},f.$g=function(e,t,r){return $.u(e)?this[t]:this.set(r,e)},f.unix=function(){return Math.floor(this.valueOf()/1e3)},f.valueOf=function(){return this.$d.getTime()},f.startOf=function(e,a){var c=this,l=!!$.u(a)||a,d=$.p(e),h=function(e,t){var r=$.w(c.$u?Date.UTC(c.$y,t,e):new Date(c.$y,t,e),c);return l?r:r.endOf("day")},f=function(e,t){return $.w(c.toDate()[e].apply(c.toDate("s"),(l?[0,0,0,0]:[23,59,59,999]).slice(t)),c)},y=this.$W,g=this.$M,p=this.$D,v="set"+(this.$u?"UTC":"");switch(d){case o:return l?h(1,0):h(31,11);case i:return l?h(1,g):h(0,g+1);case s:var m=this.$locale().weekStart||0,x=(y<m?y+7:y)-m;return h(l?p-x:p+(6-x),g);case"day":case u:return f(v+"Hours",0);case n:return f(v+"Minutes",1);case r:return f(v+"Seconds",2);case t:return f(v+"Milliseconds",3);default:return this.clone()}},f.endOf=function(e){return this.startOf(e,!1)},f.$set=function(s,a){var c,l=$.p(s),d="set"+(this.$u?"UTC":""),h=((c={}).day=d+"Date",c[u]=d+"Date",c[i]=d+"Month",c[o]=d+"FullYear",c[n]=d+"Hours",c[r]=d+"Minutes",c[t]=d+"Seconds",c[e]=d+"Milliseconds",c)[l],f="day"===l?this.$D+(a-this.$W):a;if(l===i||l===o){var y=this.clone().set(u,1);y.$d[h](f),y.init(),this.$d=y.set(u,Math.min(this.$D,y.daysInMonth())).$d}else h&&this.$d[h](f);return this.init(),this},f.set=function(e,t){return this.clone().$set(e,t)},f.get=function(e){return this[$.p(e)]()},f.add=function(e,a){var u,c=this;e=Number(e);var l=$.p(a),d=function(t){var r=m(c);return $.w(r.date(r.date()+Math.round(t*e)),c)};if(l===i)return this.set(i,this.$M+e);if(l===o)return this.set(o,this.$y+e);if("day"===l)return d(1);if(l===s)return d(7);var h=((u={})[r]=6e4,u[n]=36e5,u[t]=1e3,u)[l]||1,f=this.$d.getTime()+e*h;return $.w(f,this)},f.subtract=function(e,t){return this.add(-1*e,t)},f.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||c;var n=e||"YYYY-MM-DDTHH:mm:ssZ",s=$.z(this),i=this.$H,a=this.$m,o=this.$M,u=r.weekdays,l=r.months,h=r.meridiem,f=function(e,r,s,i){return e&&(e[r]||e(t,n))||s[r].slice(0,i)},y=function(e){return $.s(i%12||12,e,"0")},g=h||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n};return n.replace(d,function(e,n){return n||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return $.s(t.$y,4,"0");case"M":return o+1;case"MM":return $.s(o+1,2,"0");case"MMM":return f(r.monthsShort,o,l,3);case"MMMM":return f(l,o);case"D":return t.$D;case"DD":return $.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return f(r.weekdaysMin,t.$W,u,2);case"ddd":return f(r.weekdaysShort,t.$W,u,3);case"dddd":return u[t.$W];case"H":return String(i);case"HH":return $.s(i,2,"0");case"h":return y(1);case"hh":return y(2);case"a":return g(i,a,!0);case"A":return g(i,a,!1);case"m":return String(a);case"mm":return $.s(a,2,"0");case"s":return String(t.$s);case"ss":return $.s(t.$s,2,"0");case"SSS":return $.s(t.$ms,3,"0");case"Z":return s}return null}(e)||s.replace(":","")})},f.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},f.diff=function(e,u,c){var l,d=this,h=$.p(u),f=m(e),y=(f.utcOffset()-this.utcOffset())*6e4,g=this-f,p=function(){return $.m(d,f)};switch(h){case o:l=p()/12;break;case i:l=p();break;case a:l=p()/3;break;case s:l=(g-y)/6048e5;break;case"day":l=(g-y)/864e5;break;case n:l=g/36e5;break;case r:l=g/6e4;break;case t:l=g/1e3;break;default:l=g}return c?l:$.a(l)},f.daysInMonth=function(){return this.endOf(i).$D},f.$locale=function(){return y[this.$L]},f.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=v(e,t,!0);return n&&(r.$L=n),r},f.clone=function(){return $.w(this.$d,this)},f.toDate=function(){return new Date(this.valueOf())},f.toJSON=function(){return this.isValid()?this.toISOString():null},f.toISOString=function(){return this.$d.toISOString()},f.toString=function(){return this.$d.toUTCString()},h}(),M=x.prototype;return m.prototype=M,[["$ms",e],["$s",t],["$m",r],["$H",n],["$W","day"],["$M",i],["$y",o],["$D",u]].forEach(function(e){M[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),m.extend=function(e,t){return e.$i||(e(t,x,m),e.$i=!0),m},m.locale=v,m.isDayjs=p,m.unix=function(e){return m(1e3*e)},m.en=y[f],m.Ls=y,m.p={},m},e.exports=t()},18479:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,s=Array(n);++r<n;)s[r]=t(e[r],r,e);return s}},22060:(e,t,r)=>{var n=r(51858),s=r(18479),i=r(55813),a=r(15903),o=1/0,u=n?n.prototype:void 0,c=u?u.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(i(t))return s(t,e)+"";if(a(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-o?"-0":r}},15903:(e,t,r)=>{var n=r(55296),s=r(48377);e.exports=function(e){return"symbol"==typeof e||s(e)&&"[object Symbol]"==n(e)}},16266:(e,t,r)=>{var n=r(22060);e.exports=function(e){return null==e?"":n(e)}},3233:(e,t,r)=>{var n=r(16266),s=0;e.exports=function(e){var t=++s;return n(e)+t}},37111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(98768);r(60343);var s=r(72548),i=r(76342),a=r(69424);r(46776);var o=r(78965),u=r(71890),c=r(25394);function l(){let e=(0,a.useRouter)(),t=async()=>{let e=document.getElementById("category-name")?.value,t=document.getElementById("category-abbr")?.value;if(""!==e)return await r({variables:{input:{name:e,abbreviation:t}}})},[r,{loading:l}]=(0,s.D)(i.CQz,{onCompleted:t=>{t.createInventoryCategory.id>0?e.back():console.error("mutationcreateInventoryCategory error",t)},onError:e=>{console.error("mutationcreateInventoryCategory error",e)}});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(c.Zb,{className:"mb-4",children:[n.jsx(c.H3,{className:"text-xl",children:"Create Inventory Category"}),n.jsx("div",{className:"grid grid-cols-2",children:(0,n.jsxs)("div",{className:"flex flex-col gap-4",children:[n.jsx(c.__,{label:"Category name",children:n.jsx(u.I,{id:"category-name",type:"text",placeholder:"Category name"})}),n.jsx(c.__,{label:"Abbreviation",children:n.jsx(u.I,{id:"category-abbr",type:"text",placeholder:"Abbreviation"})})]})})]}),(0,n.jsxs)(o.V,{children:[n.jsx(c.zx,{variant:"back",onClick:()=>e.push("/settings/inventory/category"),children:"Cancel"}),n.jsx(c.zx,{onClick:t,children:"Create Category"})]})]})}function d(){return n.jsx(l,{})}},84313:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(98768);r(60343);var s=r(64837);function i({children:e}){return n.jsx(s.Z,{children:e})}},37938:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\inventory\category\new\page.tsx#default`)},50681:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\layout.tsx#default`)},84961:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},46020:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},23379:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},7671:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[864,8865,3563,6263,8189,9507,7602,6451,4234,2925,5394,4837,6342],()=>r(88918));module.exports=n})();