"use strict";exports.id=7672,exports.ids=[7672],exports.modules={17405:(e,t,r)=>{r.d(t,{Z:()=>o});var i=r(83179),a=r.n(i),n=r(86708);class l{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),r=t.id,i={...t,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},l=await this.getById(r);return l?await n.Z.TripEvent.update(r,i):await n.Z.TripEvent.add(i),l=await this.getById(r),console.log("TripEvent save",e,l),l}catch(t){console.error("TripEvent save",e,t)}}async getAll(){try{let e=await n.Z.TripEvent.toArray();return console.log("TripEvent getAll",e),e}catch(e){console.error("TripEvent getAll",e)}}async getById(e){try{let t=await n.Z.TripEvent.get(`${e}`),r=await this.addRelationships(t);return console.log("TripEvent getById",e,r),r}catch(t){console.error("TripEvent getById",e,t)}}async getByIds(e){try{let t=await n.Z.TripEvent.where("id").anyOf(e).toArray(),r=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("TripEvent getByIds",e,r),r}catch(t){console.error("TripEvent getByIds",e,t)}}async getByFieldID(e,t){try{let r=await n.Z.TripEvent.where(`${e}`).equals(`${t}`).toArray();return console.log("TripEvent getByFieldID",e,t,r),r}catch(r){console.error("TripEvent getByFieldID",e,t,r)}}async bulkAdd(e){try{return await n.Z.TripEvent.bulkAdd(e),console.log("TripEvent bulkAdd",e),e}catch(t){if("BulkError"===t.name){let r=t.failuresByPos.map(e=>e.key),i=e.filter(e=>!r.includes(e.id));return await n.Z.TripEvent.bulkAdd(i),console.log("TripEvent bulkAdd::BulkError",e,t),e}console.error("TripEvent bulkAdd",e,t)}}async addRelationships(e){return console.log("TripEvent addRelationships",e),e}async setProperty(e){try{if(e){let t=await n.Z.TripEvent.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await n.Z.TripEvent.update(e,t),console.log("TripEvent setProperty",e,t),t}}catch(t){console.error("TripEvent setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await n.Z.TripEvent.update(e.id,e)})),console.log("TripEvent multiUpdate",e)}catch(t){console.error("TripEvent multiUpdate",e,t)}}}let o=l},55361:(e,t,r)=>{r.d(t,{Z:()=>y});var i=r(98768),a=r(60343),n=r(7678),l=r.n(n),o=r(76342),s=r(79418),d=r(72548),c=r(73366),u=r(10090),m=r(81524),v=r(24794),h=r(39544),g=r(71890);function p({openDialog:e,setOpenDialog:t,handleCreate:r,actionText:a,error:n}){return i.jsx(v.Dialog,{open:e,onOpenChange:t,children:(0,i.jsxs)(v.DialogContent,{children:[i.jsx(v.DialogHeader,{children:i.jsx(v.DialogTitle,{className:"font-medium",children:"Add Crew Member"})}),(0,i.jsxs)("form",{onSubmit:e=>{e.preventDefault(),r(new FormData(e.currentTarget))},children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 gap-4 border-t pt-6",children:[(0,i.jsxs)("div",{className:"flex gap-4",children:[i.jsx(g.I,{id:"crew-firstName",name:"firstName",type:"text",placeholder:"First Name"}),i.jsx(g.I,{id:"crew-surname",name:"surname",type:"text",placeholder:"Surname"})]}),(0,i.jsxs)("div",{className:"flex gap-4",children:[i.jsx(g.I,{id:"crew-username",name:"username",type:"text",placeholder:"Username"}),i.jsx(g.I,{id:"crew-password",name:"password",type:"password",placeholder:"Password"})]}),(0,i.jsxs)("div",{className:"flex gap-4",children:[i.jsx(g.I,{id:"crew-email",name:"email",type:"email",placeholder:"Email"}),i.jsx(g.I,{id:"crew-phoneNumber",name:"phoneNumber",type:"text",placeholder:"Phone Number"})]}),n&&i.jsx("div",{className:"text-rose-600",children:n.message})]}),i.jsx(v.DialogFooter,{className:"mt-6",children:i.jsx(h.Button,{type:"submit",children:a})})]})]})})}var b=r(45519);let f=(0,b.ZP)`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                isArchived
                firstName
                surname
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
                departments {
                    nodes {
                        id
                    }
                }
                groups {
                    nodes {
                        id
                        title
                        code
                    }
                }
            }
        }
    }
`,y=({value:e=[],onChange:t,memberIdOptions:r=[],departments:n=[],filterByAdmin:v=!1,offline:h=!1,vesselID:g=0})=>{let[b,y]=(0,a.useState)(!0),[x,I]=(0,a.useState)([]),[w,j]=(0,a.useState)(!1),[D,E]=(0,a.useState)([]),[N,C]=(0,a.useState)(!1),S=new c.Z,T=e=>{let t=g>0?e.filter(e=>e.vehicles.nodes.some(e=>+e.id===g)):e,i={value:"newCrewMember",label:"--- Create Crew Member ---"},a=t.filter(e=>!v||!B(e));if(n.length>0){let e=n.flatMap(e=>e.id),t=a.filter(t=>t.departments.nodes.some(t=>e.includes(t.id))).map(e=>({value:e.id,label:`${e.firstName??""} ${e.surname??""}`}));0===r.length?I([i,...t]):I(t.filter(e=>r.includes(e.value)))}else{let e=a.map(e=>({value:e.id,label:`${e.firstName??""} ${e.surname??""}`}));0===r.length?I([i,...e]):I(e.filter(e=>r.includes(e.value)))}},[k]=(0,s.t)(f,{fetchPolicy:"cache-and-network",onError:e=>{console.error("querySeaLogsMembersList error",e)}}),R=async()=>{let e=[],t=0,r=!0;try{for(;r;){let i=await k({variables:{filter:{isArchived:{eq:!1}},limit:100,offset:t}});if(i.data?.readSeaLogsMembers){let a=i.data.readSeaLogsMembers.nodes,n=i.data.readSeaLogsMembers.pageInfo;a&&a.length>0&&(e=[...e,...a]),r=n?.hasNextPage||!1,t+=100}else r=!1}e.length>0&&T(e)}catch(e){console.error("Error loading all crew members:",e)}};(0,a.useEffect)(()=>{b&&!h&&(R(),y(!1))},[b,h]),(0,a.useEffect)(()=>{h&&S.getAll().then(e=>{T(e)})},[h]);let B=e=>e.groups.nodes?.filter(e=>"admin"===e.code).length>0,[A]=(0,d.D)(o.qK0,{fetchPolicy:"no-cache",onCompleted:r=>{let i=r.createSeaLogsMember;if(i.id>0){j(!1);let r={value:i.id,label:i.firstName+" "+i.surname};I([...x,r]),E([...D,i.id]),t([r,...e.map(e=>x.find(t=>t.value===e))]),C(!1)}},onError:e=>{console.error("createUser error",e.message),C(e)}}),$=async()=>{let r={input:{firstName:document.getElementById("crew-firstName").value?document.getElementById("crew-firstName").value:null,surname:document.getElementById("crew-surname").value?document.getElementById("crew-surname").value:null,email:document.getElementById("crew-email").value?document.getElementById("crew-email").value:null,phoneNumber:document.getElementById("crew-phoneNumber").value?document.getElementById("crew-phoneNumber").value:null,username:document.getElementById("crew-username").value?document.getElementById("crew-username").value:null,password:document.getElementById("crew-password").value?document.getElementById("crew-password").value:null}};if(h){let i=await S.save({...r.input,id:(0,u.lY)()});j(!1);let a={value:i.id,label:i.firstName+" "+i.surname};I([...x,a]),E([...D,i.id]),t([a,...e.map(e=>x.find(t=>t.value===e))]),C(!1)}else await A({variables:r})};return(0,a.useEffect)(()=>{if(l()(e)||0===e.length){E([]);return}l()(x)||E(e.map(e=>{let t=String(e);return x.find(e=>String(e.value)===t)||(console.warn("CrewMultiSelectDropdown - Could not find crew with ID:",e),{value:t,label:`Unknown (${e})`})}).filter(Boolean))},[e,x]),(0,i.jsxs)(i.Fragment,{children:[i.jsx(m.Combobox,{options:x,value:D,onChange:e=>{if(!e){E([]),t([]);return}let r=Array.isArray(e)?e:[e];if(r.find(e=>"newCrewMember"===e.value)){j(!0);return}if(0===r.length){E([]),t([]);return}let i=r.filter(e=>e&&"object"==typeof e);E(i),t(i)},placeholder:"Select Crew",multi:!0,responsiveBadges:!0,isLoading:!x}),i.jsx(p,{openDialog:w,setOpenDialog:j,handleCreate:$,actionText:"Add Crew Member",error:N})]})}},33849:(e,t,r)=>{r.d(t,{Z:()=>o});var i=r(98768);r(60343);var a=r(47520);r(30854);var n=r(56937);let l=(0,a.default)(async()=>{},{loadableGenerated:{modules:["app\\ui\\editor.tsx -> react-quill"]},ssr:!1});function o(e,t){return i.jsx(l,{value:e.content,placeholder:e.placeholder,onChange:e.handleEditorChange,onBlur:e.handleEditorBlur,modules:{toolbar:[[{header:[1,2,3,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"}],["link","image"],[{align:[]},{color:[]}],["clean"]]},formats:["header","bold","italic","underline","strike","blockquote","list","bullet","link","image","align","color","code-block"],className:(0,n.cn)("w-full min-h-60 mb-2 bg-card border overflow-auto text-input border-border rounded-lg",t)})}},94733:(e,t,r)=>{r.d(t,{c:()=>n,x:()=>a});var i=r(45519);let a=(0,i.ZP)`
    query GetIncidentRecord($id: ID!) {
        readOneIncidentRecord(filter: { id: { eq: $id } }) {
            id
            title
            startDate
            endDate
            incidentType
            personsInvolved
            description
            treatment
            contributingFactor
            riskAssessmentReviewed
            notifiable
            location {
                id
                title
                lat
                long
            }
            reportedBy {
                id
                firstName
                surname
            }
            vessel {
                id
                title
            }
            membersToNotify {
                nodes {
                    id
                    firstName
                    surname
                }
            }
            attachments {
                nodes {
                    id
                    title
                }
            }
        }
    }
`,n=(0,i.ZP)`
    query GetIncidentRecords(
        $limit: Int = 100
        $offset: Int = 0
        $filter: IncidentRecordFilterFields = {}
    ) {
        readIncidentRecords(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { startDate: DESC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                title
                startDate
                endDate
                vessel {
                    id
                    title
                }
                reportedBy {
                    id
                    firstName
                    surname
                }
                membersToNotify {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
            }
        }
    }
`},7672:(e,t,r)=>{r.d(t,{default:()=>O});var i=r(98768),a=r(60343),n=r(69424),l=r(72548),o=r(79418),s=r(83179),d=r.n(s),c=r(7678),u=r.n(c),m=r(14826),v=r.n(m),h=r(45519);let g=(0,h.ZP)`
    mutation CreateIncidentRecord($input: CreateIncidentRecordInput!) {
        createIncidentRecord(input: $input) {
            id
        }
    }
`,p=(0,h.ZP)`
    mutation UpdateIncidentRecord($input: UpdateIncidentRecordInput!) {
        updateIncidentRecord(input: $input) {
            id
        }
    }
`,b=(0,h.ZP)`
    mutation DeleteIncidentRecords($ids: [ID]!) {
        deleteIncidentRecords(ids: $ids)
    }
`;var f=r(94733),y=r(76342),x=r(17405),I=r(10090),w=r(47225),j=r(33810),D=r(14692),E=r(55361),N=r(26100),C=r(71890),S=r(60797),T=r(39544),k=r(57103),R=r(34376),B=r(81524),A=r(74602),$=r(17203),F=r(81311),Z=r(11232),L=r(99303),P=r(33849),M=r(13006),H=r(8416),q=r(93778);let O=({id:e=0,inLogbook:t=!1,closeModal:r,updateTripReport:s,currentTrip:c,selectedEvent:m,offline:h=!1,tripReport:O=[]})=>{let Y=(0,n.useRouter)(),U=(0,n.useSearchParams)(),z=U.get("id")?parseInt(U.get("id")):e,V=U.get("vesselID"),{toast:W}=(0,R.pm)(),G=new x.Z,[J,K]=(0,a.useState)(""),[Q,X]=(0,a.useState)(V||null),[_,ee]=(0,a.useState)(d()()),[et,er]=(0,a.useState)(d()().format("HH:mm")),[ei,ea]=(0,a.useState)(d()()),[en,el]=(0,a.useState)(d()().format("HH:mm")),[eo,es]=(0,a.useState)(null),[ed,ec]=(0,a.useState)("incident"),[eu,em]=(0,a.useState)(0),[ev,eh]=(0,a.useState)(null),[eg,ep]=(0,a.useState)(""),[eb,ef]=(0,a.useState)("none"),[ey,ex]=(0,a.useState)("none"),[eI,ew]=(0,a.useState)(!1),[ej,eD]=(0,a.useState)(!1),[eE,eN]=(0,a.useState)([]),[eC,eS]=(0,a.useState)(!1),[eT,ek]=(0,a.useState)(!1),[eR,eB]=(0,a.useState)([]),[eA]=(0,M.v1)("tab"),[e$,eF]=(0,a.useState)(!1),[eZ,{loading:eL}]=(0,l.D)(g,{onCompleted:async e=>{let i=e.createIncidentRecord.id;t&&c?.id&&(h?await G.save({id:(0,I.lY)(),eventCategory:"IncidentRecord",logBookEntrySectionID:c.id,incidentRecordID:i}):eU({variables:{input:{eventCategory:"IncidentRecord",logBookEntrySectionID:c.id,incidentRecordID:i}}}),s&&s({id:[...O.map(e=>e.id),c.id]})),eR.length>0&&i&&eR.forEach(e=>{e.id||eO({variables:{input:{title:e.title,incidentRecordID:i}}})}),"trip-log"==eA?e$&&(eF(!1),r?.()):Y.push("/incident-records")},onError:e=>{console.error("Error creating incident record: ",e),W({variant:"destructive",title:"Error",description:`Error creating incident record: ${e.message}`})}}),[eP,{loading:eM}]=(0,l.D)(p,{onCompleted:async()=>{t&&m?.id&&c?.id&&(h?await G.save({id:m.id,eventCategory:"IncidentRecord",logBookEntrySectionID:c.id,incidentRecordID:z}):ez({variables:{input:{id:m.id,eventCategory:"IncidentRecord",logBookEntrySectionID:c.id,incidentRecordID:z}}}),s&&s({id:[...O.map(e=>e.id),c.id]})),eR.length>0&&eR.forEach(e=>{e.id||eO({variables:{input:{title:e.title,incidentRecordID:z}}})}),"trip-log"==eA?e$&&(eF(!1),r?.()):Y.push("/incident-records")},onError:e=>{W({variant:"destructive",title:"Error",description:`Error updating incident record: ${e.message}`})}}),[eH,{loading:eq}]=(0,l.D)(b,{onCompleted:()=>{ek(!1),"trip-log"==eA?r?.():Y.push("/incident-records")},onError:e=>{ek(!1),W({variant:"destructive",title:"Error",description:`Error deleting incident record: ${e.message}`})}}),[eO,{loading:eY}]=(0,l.D)(y.q$t,{onCompleted:e=>{let t=e.createR2File;eB(eR.map(e=>e.title===t.title?{...e,id:t.id}:e))},onError:e=>{console.error("Error creating attachments",e),W({variant:"destructive",title:"Error",description:`Error creating attachments: ${e.message}`})}}),[eU]=(0,l.D)(y.Wzj,{onCompleted:e=>{},onError:e=>{console.error("Error creating trip event:",e)}}),[ez]=(0,l.D)(y.kst,{onCompleted:e=>{},onError:e=>{console.error("Error updating trip event:",e)}}),[eV]=(0,o.t)(f.x,{fetchPolicy:"network-only",onCompleted:e=>{if(e&&e.readOneIncidentRecord){let t=e.readOneIncidentRecord;t.startDate&&(ee(d()(t.startDate)),er(d()(t.startDate).format("HH:mm"))),t.endDate&&(ea(d()(t.endDate)),el(d()(t.endDate).format("HH:mm"))),K(t.title||""),t.location?.id?es(t.location.id):es(null),X(t.vessel?.id||null),ec(t.incidentType||"incident"),em(t.personsInvolved||0),eh(t.reportedBy?.id?String(t.reportedBy.id):null),ep(t.description||""),ef(t.treatment||"none"),ex(t.contributingFactor||"none"),ew(t.riskAssessmentReviewed||!1),eD(t.notifiable||!1),t.membersToNotify&&t.membersToNotify.nodes&&eN(t.membersToNotify.nodes.map(e=>e.id)),t.attachments&&t.attachments.nodes&&eB(t.attachments.nodes),eS(!1)}},onError:e=>{W({variant:"destructive",title:"Error",description:`Error fetching incident record: ${e.message}`}),eS(!1)}});(0,a.useEffect)(()=>{let e=z;0===e&&m&&(e=m.incidentRecordID||0),e>0&&(eS(!0),eV({variables:{id:e}}))},[z,m,eV]),(0,a.useEffect)(()=>{t&&c?.vesselID?X(c.vesselID.toString()):t&&V&&X(V)},[t,c,U]);let eW=()=>{if(u()(v()(J))){W({variant:"destructive",title:"Error",description:"Title is required"});return}let e=`${d()(_).format("YYYY-MM-DD")} ${et}`,r=`${d()(ei).format("YYYY-MM-DD")} ${en}`;if(t&&!Q&&c?.vesselID)X(c.vesselID.toString());else if(t&&!Q){if(V)X(V);else{console.error("Vessel ID is required");return}}let i={title:J,startDate:e,endDate:r,locationID:eo,vesselID:V&&+V>0?+V:Q,incidentType:ed,personsInvolved:eu,reportedByID:ev,description:eg,treatment:eb,contributingFactor:ey,riskAssessmentReviewed:eI,notifiable:ej,membersToNotify:eE.join(",")};z>0?eP({variables:{input:{id:z,...i}}}):eZ({variables:{input:i}})},eG=[{value:"accident",label:"Accident"},{value:"incident",label:"Incident"},{value:"nearMiss",label:"Near Miss"},{value:"safetyEvent",label:"Safety Event"},{value:"medical",label:"Illness or Injury (Medical)"},{value:"assistanceRendered",label:"Assistance Rendered"},{value:"mental",label:"Behavioural Challenge / Mental Health and Wellness"},{value:"other",label:"Other"}],eJ=[{value:"none",label:"No Treatment"},{value:"firstAidOnboard",label:"First Aid Onboard"},{value:"firstAidOnshore",label:"First Aid Onshore"},{value:"restrictedWorkInjury",label:"Restricted Work Injury"},{value:"lostTimeInjury",label:"Lost Time Injury"},{value:"doctorOrHospital",label:"Doctor or Hospital"},{value:"fatality",label:"Fatality"},{value:"other",label:"Other"}],eK=[{value:"none",label:"None"},{value:"humanError",label:"Human Error"},{value:"equipmentProcedure",label:"Equipment Procedure"},{value:"environment",label:"Environment"},{value:"other",label:"Other"}];return eC?i.jsx(N.Z,{message:"Loading incident record..."}):(0,i.jsxs)(i.Fragment,{children:[!t&&(0,i.jsxs)(A.H1,{children:[z>0?"Edit ":"New "," Incident Record"]}),(0,i.jsxs)("div",{className:"py-5 space-y-8",children:[(0,i.jsxs)("div",{children:[!t&&i.jsx(A.H3,{className:"mb-4",children:"Incident – Accident – Near Miss – Safety Event"}),i.jsx(A.P,{className:"mb-6",children:"This form is designed to be simple and practical enough to be completed during or shortly after a high-stress situation. It does not replace any official reporting forms required by Authorities, nor does it substitute your internal documentation or investigation processes."})]}),(0,i.jsxs)("div",{className:"space-y-8",children:[i.jsx(S.Label,{htmlFor:"title",label:"Title",children:i.jsx(C.I,{id:"title",type:"text",value:J,onChange:e=>K(e.target.value),placeholder:"Enter incident title"})}),V&&0==+V&&i.jsx(S.Label,{htmlFor:"vessel",label:"Vessel",children:i.jsx(Z.Z,{value:Q,onChange:e=>X(e?.value||null),placeholder:"Select Vessel",isClearable:!0})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[i.jsx(S.Label,{htmlFor:"start-date",label:"Start Date",children:i.jsx(w.Z,{date:_,handleDateChange:ee,dateID:"start-date",fieldName:"Start Date"})}),i.jsx(S.Label,{htmlFor:"start-time",label:"Start Time",children:i.jsx(j.Z,{time:et,handleTimeChange:e=>{er(d()(e).format("HH:mm"))},timeID:"start-time",fieldName:"Start Time"})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[i.jsx(S.Label,{htmlFor:"end-date",label:"End Date",children:i.jsx(w.Z,{date:ei,handleDateChange:ea,dateID:"end-date",fieldName:"End Date"})}),i.jsx(S.Label,{htmlFor:"end-time",label:"End Time",children:i.jsx(j.Z,{time:en,handleTimeChange:e=>{el(d()(e).format("HH:mm"))},timeID:"end-time",fieldName:"End Time"})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[i.jsx(S.Label,{htmlFor:"location",label:"Location",children:i.jsx(D.Z,{handleLocationChange:e=>{if(!e){es(null);return}e.value?es(e.value):void 0!==e.latitude&&void 0!==e.longitude&&es(null)},setCurrentLocation:()=>{},currentEvent:{geoLocationID:eo},showAddNewLocation:!1,showUseCoordinates:!1,showCurrentLocation:!1})}),i.jsx(S.Label,{htmlFor:"incident-type",label:"Incident Type",children:i.jsx(B.Combobox,{id:"incident-type",options:eG,value:eG.find(e=>e.value===ed)||null,onChange:e=>ec(e?.value||"incident"),placeholder:"Select Incident Type"})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[i.jsx(S.Label,{htmlFor:"persons-involved",label:"Person(s) Involved",children:i.jsx(C.I,{id:"persons-involved",type:"number",value:eu,onChange:e=>em(parseInt(e.target.value)||0),min:"0"})}),i.jsx(S.Label,{htmlFor:"reported-by",label:"Reported By",children:i.jsx(L.Z,{value:ev,onChange:e=>{"object"==typeof e&&e?.value?eh(String(e.value)):"string"==typeof e||"number"==typeof e?eh(String(e)):eh(null)},placeholder:"Select Crew Member",isClearable:!0})})]}),i.jsx(S.Label,{htmlFor:"description",label:"Description",children:i.jsx(P.Z,{id:"description",placeholder:"Provide a detailed narrative (include info on treatment refusals, police or emergency service feedback, etc.)",className:"w-full",content:eg,handleEditorChange:e=>{ep(e)},autoResize:!0})}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[i.jsx(S.Label,{htmlFor:"treatment",label:"Treatment",children:i.jsx(B.Combobox,{id:"treatment",options:eJ,value:eJ.find(e=>e.value===eb)||null,onChange:e=>ef(e?.value||"none"),placeholder:"Select Treatment"})}),i.jsx(S.Label,{htmlFor:"contributing-factor",label:"Contributing Factor",children:i.jsx(B.Combobox,{id:"contributing-factor",options:eK,value:eK.find(e=>e.value===ey)||null,onChange:e=>ex(e?.value||"none"),placeholder:"Select Contributing Factor"})})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[i.jsx(H.tz,{id:"risk-assessment",isRadioStyle:!0,variant:"warning",label:"Risk Assessment Reviewed",checked:eI,onCheckedChange:e=>ew(!0===e)}),i.jsx(H.tz,{id:"notifiable",isRadioStyle:!0,variant:"warning",label:"Notifiable to Authorities",checked:ej,onCheckedChange:e=>eD(!0===e)})]}),i.jsx(S.Label,{htmlFor:"team-members",label:"Team Members to Notify",children:i.jsx(E.Z,{value:eE,onChange:e=>{eN(e.map(e=>e.value))}})}),i.jsx(S.Label,{htmlFor:"attachments",label:"Attachments"}),i.jsx("div",{className:"w-full flex flex-col space-y-2",children:i.jsx(q.Z,{inputId:m?.id||z||0,sectionId:c.id,buttonType:"button",sectionName:"tripEventID"})}),(0,i.jsxs)("div",{className:"flex justify-end space-x-3",children:[i.jsx(T.Button,{variant:"back",iconLeft:$.Z,onClick:"trip-log"==eA?r:()=>Y.push("/incident-records"),disabled:eL||eM||eq||eY,children:"Cancel"}),z>0&&i.jsx(T.Button,{variant:"destructive",onClick:()=>ek(!0),disabled:eL||eM||eq||eY,children:"Delete"}),i.jsx(T.Button,{variant:"primary",onClick:()=>{eF(!0),eW()},iconRight:F.Z,disabled:eL||eM||eq||eY,isLoading:eL||eM,children:z>0?"Update":"Save"})]})]})]}),i.jsx(k.AlertDialogNew,{openDialog:eT,setOpenDialog:ek,title:"Delete Incident Record",description:"Are you sure you want to delete this incident record? This action cannot be undone.",cancelText:"Cancel",destructiveActionText:"Delete",handleDestructiveAction:()=>{z>0&&eH({variables:{ids:[z]}})},showDestructiveAction:!0,variant:"danger",destructiveLoading:eq,children:eq&&i.jsx(A.P,{className:"text-blue-500 mt-2",children:"Deleting incident record..."})})]})}},93778:(e,t,r)=>{r.d(t,{Z:()=>m});var i=r(98768),a=r(60343),n=r(79418),l=r(75776),o=r(94060),s=r(39544),d=r(78853),c=r(69422),u=r(34376);function m({inputId:e=0,buttonType:t="icon",sectionName:r="logBookEntryID",sectionId:m=0,editable:v=!0}){let[h,g]=(0,a.useState)([]),[p,b]=(0,a.useState)([]),[f]=(0,n.t)(o.HC,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readCaptureImages.nodes;t&&g(t)},onError:e=>{console.error("getFieldImages error",e)}}),y=async()=>{await f({variables:{filter:{[r]:{eq:m}}}})};return i.jsx(i.Fragment,{children:0===e||0===m?i.jsx("div",{className:"w-full flex",children:i.jsx(s.Button,{variant:"icon"===t?"ghost":"outline",size:"icon",iconOnly:"icon"===t,title:"Add comment",className:"icon"===t?"group":"h-10",iconLeft:i.jsx(d.Z,{className:"icon"===t?(0,c.cn)("text-curious-blue-400 group-hover:text-curious-blue-400/50","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300"):"",size:24}),onClick:()=>(0,u.Am)({title:"Please save the section first",description:"You need to save the section in order to capture or upload images.",variant:"destructive"}),children:"button"===t&&"Capture / Upload Image"})}):(0,i.jsxs)(i.Fragment,{children:[p.length>0&&i.jsx("div",{className:"flex flex-wrap mb-4",children:p.map((e,t)=>i.jsx("div",{className:"w-1/5 p-1 rounded-md relative",children:i.jsx("img",{src:e.imageData,alt:`Captured ${t}`,className:"object-cover rounded-md"},t)},t))}),v&&i.jsx("div",{className:"w-full flex",children:i.jsx(l.Z,{file:!!(h&&Array.isArray(h))&&h.filter(t=>t.fieldName===e).sort((e,t)=>t.id-e.id),setFile:y,inputId:e.toString(),buttonType:"button",sectionData:{id:m,sectionName:r}})})]})})}},8416:(e,t,r)=>{r.d(t,{tz:()=>m});var i=r(98768),a=r(60343),n=r(85745),l=r(8750),o=r(70906),s=r(56937),d=r(74602);let c=(0,n.j)("cursor-pointer w-full px-4 rounded-e-lg will-change-transform will-change-color will-change-padding transform-gpu hover:transition-all hover:ease-out hover:duration-300",{variants:{variant:{default:"hover:bg-fire-bush-100 hover:border-yellow-vivid-600",primary:"hover:bg-light-blue-vivid-50 hover:border-light-blue-vivid-600",secondary:"hover:bg-background hover:border-neutral-400",success:"hover:bg-bright-turquoise-100 hover:border-teal-600",destructive:"hover:bg-red-vivid-50 hover:border-red-vivid-600",warning:"hover:bg-fire-bush-100 hover:border-yellow-vivid-600",pink:"hover:bg-pink-vivid-50 hover:border-pink-vivid-600",outline:"hover:bg-background hover:border-neutral-400","light-blue":"hover:bg-light-blue-vivid-50 hover:border-light-blue-vivid-600"},size:{default:"py-[10.5px]",sm:"py-2",lg:"py-6"},disabled:{true:"hover:bg-transparent hover:border-border",false:""}},defaultVariants:{variant:"default",size:"default",disabled:!1}}),u=(0,n.j)("relative inset-x-0 w-12 rounded-s-lg justify-center flex items-center",{variants:{variant:{default:"bg-light-blue-vivid-50 border-light-blue-vivid-600",primary:"bg-light-blue-vivid-50 border-light-blue-vivid-600",secondary:"bg-background border-neutral-400",success:"bg-bright-turquoise-100 border-teal-600",destructive:"bg-red-vivid-50 border-red-vivid-600",warning:"bg-fire-bush-100 border-yellow-vivid-600",pink:"bg-pink-vivid-50 border-pink-vivid-600",outline:"bg-background border-neutral-400","light-blue":"bg-light-blue-vivid-50 border-light-blue-vivid-600"}},defaultVariants:{variant:"default"}}),m=a.forwardRef(({type:e="checkbox",id:t,checked:r,onCheckedChange:n,disabled:m,value:v,name:h,label:g,children:p,className:b,variant:f,size:y,radioGroupValue:x,isRadioStyle:I=!0,rightContent:w,leftContent:j,onClick:D,...E},N)=>{let C=a.useId(),S=t||`${e}-${C}`,T="radio"===e?x===v:r;return(0,i.jsxs)("div",{ref:N,className:(0,s.cn)("flex rounded-lg relative group justify-evenly border border-border shadow-sm cursor-pointer",m&&"opacity-50 cursor-not-allowed",b),onClick:t=>{!m&&("checkbox"===e&&n?n(!r):"radio"===e&&n&&!T&&n(!0),D&&D(t))},...E,children:[i.jsx("div",{className:(0,s.cn)(u({variant:f})),children:"checkbox"===e?i.jsx(l.Checkbox,{id:S,isRadioStyle:I,checked:r,onCheckedChange:e=>{"boolean"==typeof e&&n&&n(e)},disabled:m,name:h,variant:f,size:"lg",className:"pointer-events-none"}):i.jsx(o.mJ,{id:S,value:v||"",disabled:m,variant:f,size:"md",checked:T,className:"pointer-events-none"})}),i.jsx("div",{className:(0,s.cn)("flex items-center",c({variant:"secondary",size:y,disabled:m})),children:(0,i.jsxs)("div",{className:(0,s.cn)("flex flex-1 items-center",{"gap-2":j||w}),children:[j&&i.jsx("div",{className:"inline-flex items-center",children:j}),p||g&&i.jsx(d.P,{className:(0,s.cn)("text-wrap text-foreground text-base"),children:g}),w&&i.jsx("div",{className:"inline-flex items-center",children:w})]})})]})});m.displayName="CheckFieldLabel"},47225:(e,t,r)=>{r.d(t,{Z:()=>s});var i=r(98768);r(60343);var a=r(83179),n=r.n(a),l=r(29342),o=r(39544);function s({date:e=!1,handleDateChange:t,dateID:r,fieldName:a="Select date",buttonLabel:s="Set To Now",hideButton:d=!0}){let c=e&&new Date(n()(e).toISOString()).getTime()>0?n()(e).toDate():void 0;return(0,i.jsxs)("div",{className:"flex w-full gap-2.5",children:[i.jsx(l.Z,{id:r,mode:"single",type:"date",placeholder:a,value:c,onChange:e=>{e?e instanceof Date?t(e):"startDate"in e&&e.startDate?t(e.startDate):"from"in e&&e.from&&t(e.from):t(null)},className:"flex-1",clearable:!0}),!d&&i.jsx(o.Button,{onClick:()=>t(new Date),children:s})]})}}};