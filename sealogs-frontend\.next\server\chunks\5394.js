"use strict";exports.id=5394,exports.ids=[5394],exports.modules={99161:(e,a,t)=>{t.d(a,{b:()=>l});var r=t(56937);let s=t(60343);function l({className:e,...a}){return s.createElement("svg",Object.assign({className:(0,r.cn)("w-12 h-12 ring-1 p-0.5 shrink-0 rounded-full bg-[#fff]",e),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 126.43 121.34",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},a),s.createElement("path",{d:"M19.51,82.18c-.07.11-.08.24-.03.36l6.23,15.82c.06.16.21.26.38.26h92.06c.19,0,.35-.13.4-.31l7.14-27.92c.03-.13,0-.27-.09-.37-.09-.1-.23-.15-.36-.14l-9.48,1.09-6.08-24c-.05-.2-.25-.33-.46-.31l-55.68,7.88c-.13.02-.24.1-.31.21-.06.12-.06.25,0,.37l2.96,6.07c.08.16.25.25.43.23l17.4-2.46,4.21,16.32-47.55,5.46-12.93-47.06c-.06-.22-.28-.35-.51-.29-.22.06-.35.29-.29.51l.9,3.28-15.09,10.09c-.14.1-.21.27-.17.44s.18.29.35.31l18.48,2.15,8.43,30.67-10.03,1.15c-.13.01-.24.09-.31.2Z",fill:"#fff",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}),s.createElement("polygon",{points:"2.05 47.97 18.43 37.02 22.08 50.3 2.05 47.97",fill:"#2a99ea",stroke:"#0c2745",strokeMiterlimit:"10",strokeWidth:"2px"}))}},99891:(e,a,t)=>{t.d(a,{Z:()=>d});var r=t(98768),s=t(60343),l=t(79418),n=t(94060),o=t(99161);function d({vessel:e}){let[a,t]=(0,s.useState)([]),[d]=(0,l.t)(n.ZX,{fetchPolicy:"cache-and-network",onCompleted:e=>{t([e.readFiles.nodes[0]])},onError:e=>{console.error("queryFilesEntry error",e)}});return(0,r.jsxs)(r.Fragment,{children:[e?.iconMode==="Photo"&&a.length>0&&r.jsx("img",{className:"w-10 h-10 ring-1 p-0.5 bg-[#fff] rounded-full",src:`https://api.sealogs.com/assets/${a[0]?.fileFilename}`,alt:e?.title}),e?.iconMode==="Icon"&&e?.icon!=null&&r.jsx("img",{className:"w-10 h-10 ring-1 p-0.5 bg-[#fff] rounded-full",src:`/vessel-icons/${e?.icon}.svg`,alt:e?.title}),e?.icon==null&&0==a.length&&r.jsx(o.b,{className:"w-10 h-10 p-0.5"})]})}},78965:(e,a,t)=>{t.d(a,{V:()=>l});var r=t(98768),s=t(56937);function l({noBorder:e=!1,bottom:a,parentClassName:t,children:l,className:n,...o}){return r.jsx(r.Fragment,{children:r.jsx("div",{className:(0,s.cn)("sticky w-full z-50 bottom-4 md:bottom-2 inset-x-2",t),...o,children:r.jsx("div",{className:(0,s.cn)("flex w-full justify-end gap-2.5 md:gap-3 p-2.5 border bg-fire-bush-100 border-fire-bush-700 text-fire-bush-700 rounded-md shadow-md py-3 md:py-4","md:px-6 lg:px-8",n),children:l})})})}},94440:(e,a,t)=>{t.d(a,{Qd:()=>i,UQ:()=>d,o4:()=>c,vF:()=>u});var r=t(98768),s=t(60343),l=t(8971),n=t(74932),o=t(56937);let d=l.Root,i=s.forwardRef(({className:e,...a},t)=>r.jsx(l.Item,{ref:t,className:(0,o.cn)("border border-border bg-card border-dashed rounded-lg mb-2",e),...a}));i.displayName="AccordionItem";let c=s.forwardRef(({className:e,children:a,...t},s)=>r.jsx(l.Header,{className:"flex",children:(0,r.jsxs)(l.Trigger,{ref:s,className:(0,o.cn)("flex flex-1 rounded-lg items-center justify-between p-5 font-medium transition-all text-left [&[data-state=open]>svg]:rotate-180 hover:bg-light-blue-vivid-50 group hover:text-light-blue-vivid-900 hover:border-border","will-change-transform will-change-color will-change-padding transform-gpu hover:transition-all hover:ease-out hover:duration-300",e),...t,children:[a,r.jsx(n.Z,{className:"h-4 w-4 shrink-0 text-muted-foreground transition-transform duration-200"})]})}));c.displayName=l.Trigger.displayName;let u=s.forwardRef(({className:e,children:a,...t},s)=>r.jsx(l.Content,{ref:s,className:"overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...t,children:r.jsx("div",{className:(0,o.cn)("pb-4 p-2.5 sm:p-5",e),children:a})}));u.displayName=l.Content.displayName},40533:(e,a,t)=>{t.r(a),t.d(a,{ActionFooter:()=>u});var r=t(98768);t(60343);var s=t(39544),l=t(78965),n=t(17203),o=t(81311),d=t(52016),i=t(3510),c=t(56937);function u({showFooter:e=!0,showCreateTask:a=!0,showSave:t=!1,cancelText:u="Cancel",createTaskText:m="Create Task",saveText:f="Save",cancelIcon:p="arrowLeft",createTaskIcon:x="plus",saveIcon:g="save",createTaskLoading:b=!1,saveLoading:h=!1,createTaskDisabled:v=!1,saveDisabled:w=!1,onCancel:y,onCreateTask:N,onSave:j,className:k,parentClassName:C,noBorder:_,bottom:R,footerWrapperProps:z,children:S}){return e?(0,r.jsxs)(l.V,{noBorder:_,bottom:R,parentClassName:(0,c.cn)("",C),className:k,...z,children:[y&&r.jsx(s.Button,{variant:"back",iconLeft:r.jsx(n.Z,{}),onClick:()=>{y&&y()},children:u}),a&&r.jsx(s.Button,{variant:"primaryOutline",iconLeft:"check"===x?o.Z:d.Z,onClick:N,isLoading:b,disabled:v||b,children:m}),t&&r.jsx(s.Button,{iconLeft:"check"===g?o.Z:i.Z,onClick:j,isLoading:h,disabled:w||h,children:f}),S]}):null}},57103:(e,a,t)=>{t.r(a),t.d(a,{AlertDialogNew:()=>x});var r=t(98768),s=t(6834),l=t(39544),n=t(81515),o=t(28898),d=t(53294),i=t(94460),c=t(17203),u=t(56937),m=t(26509);let f={default:{icon:r.jsx(n.Z,{}),className:"",headerClassName:"",buttonVariant:"primary",iconColor:""},info:{icon:r.jsx(n.Z,{}),className:"border-blue-500 bg-blue-50",headerClassName:"bg-blue-50",buttonVariant:"default",iconColor:"text-blue-500"},warning:{icon:r.jsx(o.Z,{}),className:"border-fire-bush-700 bg-fire-bush-100 text-fire-bush-700 p-5",headerClassName:"",buttonVariant:"primary",iconColor:"text-fire-bush-700"},danger:{icon:r.jsx(d.Z,{}),className:"border-destructive bg-red-vivid-50",headerClassName:"bg-red-vivid-50",buttonVariant:"destructive",iconColor:"text-destructive"},success:{icon:r.jsx(i.Z,{}),className:"border-green-500 bg-green-50",headerClassName:"bg-green-50",buttonVariant:"success",iconColor:"text-green-500"}},p={sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl"};function x({openDialog:e,setOpenDialog:a,handleCreate:t,handleAction:n,handleCancel:o,handleDestructiveAction:d,children:i,title:x,description:g,actionText:b="Continue",secondaryActionText:h=!1,cancelText:v="Cancel",destructiveActionText:w="Delete",noButton:y=!1,noFooter:N=!1,className:j,contentClassName:k,variant:C="default",size:_="md",position:R="center",showIcon:z=!1,loading:S=!1,destructiveLoading:T=!1,showDestructiveAction:D=!1}){let{icon:Z,buttonVariant:A,className:F,headerClassName:M,iconColor:V}=f[C];return r.jsx(s.aR,{open:e,onOpenChange:a,children:(0,r.jsxs)(s._T,{className:(0,u.cn)(p[_],"side"===R&&"sm:ml-auto sm:mr-0 sm:rounded-l-xl sm:rounded-r-none sm:h-full","center"===R&&"sm:rounded-xl",k),innerClassName:(0,u.cn)(F),children:[(0,r.jsxs)(s.fY,{className:(0,u.cn)(M,{"sr-only":!x&&!g}),children:[(0,r.jsxs)("div",{className:(0,u.cn)("flex-shrink-0 flex items-center gap-2.5",V),children:[z&&Z,r.jsx(s.f$,{children:x})]}),r.jsx(s.yT,{hidden:!g,children:g})]}),r.jsx(s.iP,{className:(0,u.cn)(j),children:i}),!N&&(0,r.jsxs)(s.xo,{className:(0,u.cn)("w-full flex flex-row flex-wrap-reverse 2xs:flex-nowrap justify-end gap-2.5 sm:gap-2",{"flex-nowrap":!D}),children:[r.jsx(l.Button,{variant:"back",className:(0,u.cn)(D?"w-full 2xs:w-fit 2xs:px-3 sm:px-5":"w-full sm:w-fit px-5"),iconLeft:r.jsx(c.Z,{}),onClick:()=>{o?.(),a(!1)},children:y?"Cancel":v}),D&&r.jsx(m.Separator,{className:"my-2 2xs:hidden"}),(0,r.jsxs)("div",{className:(0,u.cn)("flex",D?"gap-2.5 w-full sm:gap-5":"w-full 2xs:max-w-[200px] sm:w-auto"),children:[D&&d&&!y&&r.jsx(l.Button,{variant:"warning"===C?"warning":"destructive",className:"w-full 2xs:px-3 sm:px-5",onClick:d,isLoading:T,children:w}),!y&&t&&r.jsx(l.Button,{variant:A,onClick:t,className:(0,u.cn)(D?"w-full 2xs:px-3 sm:px-5 ":"px-5 w-full"),isLoading:S,children:b})]}),h&&n&&r.jsx(l.Button,{variant:A,className:(0,u.cn)(D?"w-full 2xs:px-3 sm:px-5":"px-5 w-full"),onClick:n,children:h})]})]})})}},6834:(e,a,t)=>{t.d(a,{OL:()=>h,_T:()=>m,aR:()=>i,f$:()=>g,fY:()=>f,iP:()=>p,le:()=>v,xo:()=>x,yT:()=>b});var r=t(98768),s=t(60343),l=t(20412),n=t(39544),o=t(56937),d=t(29052);let i=l.Root;l.Trigger;let c=l.Portal,u=s.forwardRef(({className:e,...a},t)=>r.jsx(l.Overlay,{className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:t}));u.displayName=l.Overlay.displayName;let m=s.forwardRef(({className:e,innerClassName:a,children:t,...s},n)=>(0,r.jsxs)(c,{children:[r.jsx(u,{}),r.jsx(l.Content,{ref:n,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 w-full max-w-lg translate-x-[-50%] translate-y-[-50%] px-5 duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]",e),...s,children:r.jsx(d.x,{className:(0,o.cn)("relative xs:h-full border border-border rounded-md bg-background phablet:bg-background shadow-lg sm:rounded-lg overflow-visible",a),children:(0,r.jsxs)("div",{className:"max-h-[90svh] p-6 pt-1",children:[t,r.jsx("div",{className:"h-5"})]})})})]}));m.displayName=l.Content.displayName;let f=({className:e,...a})=>r.jsx("div",{className:(0,o.cn)("flex flex-col space-y-2 pt-6 sticky leading-tight -top-1 z-20 bg-background text-start sm:text-left",e),...a});f.displayName="AlertDialogHeader";let p=({className:e,...a})=>r.jsx("div",{className:(0,o.cn)("mt-[31px]",e),children:a.children});p.displayName="AlertDialogBody";let x=({className:e,...a})=>r.jsx("div",{className:(0,o.cn)("flex flex-col-reverse mt-[31px] sm:flex-row sm:justify-end sm:space-x-2",e),...a});x.displayName="AlertDialogFooter";let g=s.forwardRef(({className:e,...a},t)=>r.jsx(l.Title,{ref:t,className:(0,o.cn)("text-lg normal-case font-semibold",e),...a}));g.displayName=l.Title.displayName;let b=s.forwardRef(({className:e,...a},t)=>r.jsx(l.Description,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",e),...a}));b.displayName=l.Description.displayName;let h=s.forwardRef(({className:e,...a},t)=>r.jsx(l.Action,{ref:t,className:(0,o.cn)((0,n.buttonVariants)(),e),...a}));h.displayName=l.Action.displayName;let v=s.forwardRef(({className:e,...a},t)=>r.jsx(l.Cancel,{ref:t,className:(0,o.cn)((0,n.buttonVariants)({variant:"outline"}),"",e),...a}));v.displayName=l.Cancel.displayName},8943:(e,a,t)=>{t.d(a,{Cd:()=>i,X:()=>c,bZ:()=>d});var r=t(98768),s=t(60343),l=t(85745),n=t(56937);let o=(0,l.j)("relative w-full rounded-lg border px-4 py-3  [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=s.forwardRef(({className:e,variant:a,...t},s)=>r.jsx("div",{ref:s,role:"alert",className:(0,n.cn)(o({variant:a}),e),...t}));d.displayName="Alert";let i=s.forwardRef(({className:e,...a},t)=>r.jsx("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...a}));i.displayName="AlertTitle";let c=s.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,n.cn)(" [&_p]:leading-relaxed",e),...a}));c.displayName="AlertDescription"},69748:(e,a,t)=>{t.r(a),t.d(a,{Avatar:()=>c,AvatarFallback:()=>m,AvatarImage:()=>u,getCrewInitials:()=>i});var r=t(98768),s=t(60343),l=t(27762),n=t(85745),o=t(56937);let d=(0,n.j)("relative flex shrink-0 p-px leading-0 [&_[data-fallback]]:boder [&_[data-fallback]]:border-curious-blue-50 relative z-10 overflow-hidden rounded-full bg-curious-blue-50",{variants:{variant:{default:"[&_[data-fallback]]:border-none [&_[data-fallback]]:shadow-none",success:"border-2 border-bright-turquoise-600",secondary:"border-2 border-border",destructive:"border-2 border-destructive",warning:"border-2 border-fire-bush-600"},size:{xs:"size-6 [&_[data-fallback]]:text-[14px]",sm:"size-8 [&_[data-fallback]]:text-[21px]",md:"size-10 [&_[data-fallback]]:text-[18px]",lg:"size-12 [&_[data-fallback]]:text-[24px]",xl:"size-16 [&_[data-fallback]]:text-[32px]"}},defaultVariants:{variant:"default",size:"sm"}}),i=(e,a)=>{let t=e&&null!==e?String(e).charAt(0).toUpperCase():"",r=a&&null!==a?String(a).charAt(0).toUpperCase():"";return`${t}${r}`},c=s.forwardRef(({className:e,variant:a,size:t,...s},n)=>r.jsx(l.fC,{ref:n,className:(0,o.cn)(d({variant:a,size:t}),e),...s}));c.displayName=l.fC.displayName;let u=s.forwardRef(({className:e,...a},t)=>r.jsx(l.Ee,{ref:t,className:(0,o.cn)("aspect-square size-full",e),...a}));u.displayName=l.Ee.displayName;let m=s.forwardRef(({className:e,...a},t)=>r.jsx(l.NY,{ref:t,"data-fallback":!0,className:(0,o.cn)("flex size-full items-center justify-center leading-0 rounded-full overflow-hidden shadow-[inset_0_2px_4px_#0000001A] font-black text-outer-space-500 flex-col",e),...a,children:r.jsx("span",{className:"",children:a.children})}));m.displayName=l.NY.displayName},87175:(e,a,t)=>{t.d(a,{C:()=>o});var r=t(98768);t(60343);var s=t(85745),l=t(56937);let n=(0,s.j)("inline-flex items-center font-black border p-2",{variants:{variant:{default:"border-primary bg-transparent",primary:"border border-primary bg-primary-foreground text-primary",success:"text-bright-turquoise-600 border-bright-turquoise-600 bg-bright-turquoise-100",warning:"text-fire-bush-500 border-fire-bush-500 bg-fire-bush-100",destructive:"text-destructive border-destructive bg-cinnabar-200",outline:"text-muted-foreground border-border",secondary:"border bg-muted text-muted-foreground w-fit rounded-lg p-2"},type:{normal:"w-fit h-11 text-nowrap rounded-lg",circle:"rounded-full justify-center size-9"}},defaultVariants:{variant:"primary",type:"circle"}});function o({className:e,variant:a,type:t,...s}){return r.jsx("div",{className:(0,l.cn)(n({variant:a,type:t}),e),...s})}},59771:(e,a,t)=>{t.d(a,{f:()=>m});var r=t(98768),s=t(60343),l=t(41641),n=t(39303),o=t(74932),d=t(31741),i=t(86008),c=t(56937),u=t(39544);function m({className:e,classNames:a,showOutsideDays:t=!0,captionLayout:s="label",buttonVariant:m="ghost",formatters:p,components:x,...g}){let b=(0,d.U)();return r.jsx(i._,{showOutsideDays:t,className:(0,c.cn)("bg-card text-card-foreground group/calendar p-3 [--cell-size:2.5rem] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent",String.raw`rtl:**:[.rdp-button\_next>svg]:rotate-180`,String.raw`rtl:**:[.rdp-button\_previous>svg]:rotate-180`,e),captionLayout:s,formatters:{formatMonthDropdown:e=>e.toLocaleString("default",{month:"short"}),...p},classNames:{root:(0,c.cn)("w-fit",b.root),months:(0,c.cn)("relative flex flex-col gap-4 md:flex-row",b.months),month:(0,c.cn)("flex w-full flex-col gap-4",b.month),nav:(0,c.cn)("absolute inset-x-0 top-0 flex w-full items-center justify-between gap-1",b.nav),button_previous:(0,c.cn)((0,u.buttonVariants)({variant:m}),"h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50",b.button_previous),button_next:(0,c.cn)((0,u.buttonVariants)({variant:m}),"h-[--cell-size] w-[--cell-size] select-none p-0 aria-disabled:opacity-50",b.button_next),month_caption:(0,c.cn)("flex h-[--cell-size] w-full items-center justify-center px-[--cell-size]",b.month_caption),dropdowns:(0,c.cn)("flex h-[--cell-size] w-full items-center justify-center gap-1.5 text-base font-medium",b.dropdowns),dropdown_root:(0,c.cn)("has-focus:border-ring border-border bg-card shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] relative rounded-md border",b.dropdown_root),dropdown:(0,c.cn)("absolute inset-0 opacity-0",b.dropdown),caption_label:(0,c.cn)("select-none font-medium text-card-foreground","label"===s?"text-lg":"[&>svg]:text-muted-foreground flex h-8 items-center gap-1 rounded-md pl-2 pr-1 text-lg [&>svg]:size-3.5",b.caption_label),table:"w-full border-collapse text-card-foreground",weekdays:(0,c.cn)("flex",b.weekdays),weekday:(0,c.cn)("text-muted-foreground flex-1 select-none rounded-md text-base font-medium",b.weekday),week:(0,c.cn)("mt-2 flex w-full",b.week),week_number_header:(0,c.cn)("w-[--cell-size] select-none",b.week_number_header),week_number:(0,c.cn)("text-muted-foreground select-none text-base",b.week_number),day:(0,c.cn)("group/day relative aspect-square h-full w-full select-none p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md",b.day),range_start:(0,c.cn)("bg-accent text-accent-foreground rounded-l-md",b.range_start),range_middle:(0,c.cn)("bg-accent text-accent-foreground rounded-none",b.range_middle),range_end:(0,c.cn)("bg-accent text-accent-foreground rounded-r-md",b.range_end),today:(0,c.cn)("bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none font-semibold",b.today),outside:(0,c.cn)("text-muted-foreground aria-selected:text-muted-foreground opacity-50",b.outside),disabled:(0,c.cn)("text-muted-foreground opacity-30 cursor-not-allowed",b.disabled),hidden:(0,c.cn)("invisible",b.hidden),...a},components:{Root:({className:e,rootRef:a,...t})=>r.jsx("div",{"data-slot":"calendar",ref:a,className:(0,c.cn)(e),...t}),Chevron:({className:e,orientation:a,...t})=>"left"===a?r.jsx(l.Z,{className:(0,c.cn)("size-4",e),...t}):"right"===a?r.jsx(n.Z,{className:(0,c.cn)("size-4",e),...t}):r.jsx(o.Z,{className:(0,c.cn)("size-4",e),...t}),DayButton:f,WeekNumber:({children:e,...a})=>r.jsx("td",{...a,children:r.jsx("div",{className:"flex size-[--cell-size] items-center justify-center text-center text-muted-foreground",children:e})}),...x},...g})}function f({className:e,day:a,modifiers:t,...l}){let n=(0,d.U)(),o=s.useRef(null);return s.useEffect(()=>{t.focused&&o.current?.focus()},[t.focused]),r.jsx(u.Button,{ref:o,variant:"ghost",size:"icon","data-day":a.date.toLocaleDateString(),"data-selected-single":t.selected&&!t.range_start&&!t.range_end&&!t.range_middle,"data-range-start":t.range_start,"data-range-end":t.range_end,"data-range-middle":t.range_middle,className:(0,c.cn)("data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 flex aspect-square h-auto w-full min-w-[--cell-size] flex-col gap-1 font-medium leading-none text-base data-[range-end=true]:rounded-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] [&>span]:text-sm [&>span]:opacity-70 text-card-foreground hover:bg-accent hover:text-accent-foreground transition-colors",n.day,e),...l})}},35024:(e,a,t)=>{t.d(a,{Ol:()=>o,SZ:()=>i,Zb:()=>n,aY:()=>c,eW:()=>u,ll:()=>d});var r=t(98768),s=t(60343),l=t(56937);let n=s.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("p-0 phablet:p-8 space-y-8 lg:p-6 xl:p-8 shadow-none border-0 phablet:border border-border bg-card rounded-lg",e),...a}));n.displayName="Card";let o=s.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5",e),...a}));o.displayName="CardHeader";let d=s.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,l.cn)(" leading-none",e),...a}));d.displayName="CardTitle";let i=s.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,l.cn)(" text-muted-foreground",e),...a}));i.displayName="CardDescription";let c=s.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("p-0",e),...a}));c.displayName="CardContent";let u=s.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...a}));u.displayName="CardFooter"},8750:(e,a,t)=>{t.r(a),t.d(a,{Checkbox:()=>u,checkboxVariants:()=>c});var r=t(98768),s=t(60343),l=t(21023),n=t(81311),o=t(85745),d=t(56937),i=t(70906);let c=(0,o.j)("peer shrink-0 border shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"border-primary data-[state=checked]:text-foreground",destructive:"border-destructive data-[state=checked]:text-white",success:"border-bright-turquoise-600 data-[state=checked]:text-white",outline:"border-foreground data-[state=checked]:text-foreground",secondary:"border-neutral-400 data-[state=checked]:text-neutral-400","light-blue":"border-light-blue-vivid-700 data-[state=checked]:text-accent-foreground",pink:"border-pink-vivid-700 data-[state=checked]:text-pink-vivid-700",warning:"border-yellow-vivid-700 data-[state=checked]:text-fire-bush-700"},size:{sm:"size-3",default:"size-4",md:"size-5",lg:"size-7"},isRadioStyle:{true:'rounded-full aspect-square group flex items-center justify-center border-border relative shadow-[0_2px_0_#FFFFFF33] before:content-[""] before:absolute before:inset-0 before:rounded-full before:shadow-[inset_0_2px_2px_#0000001A]',false:"rounded-sm h-4 w-4"}},defaultVariants:{variant:"default",size:"default",isRadioStyle:!1}}),u=s.forwardRef(({className:e,variant:a,size:t,isRadioStyle:s,...o},u)=>{let m=t&&s?i.Qs[t]:i.Qs.default,f=a&&s?i.Eo[a]:i.Eo.default,p=a&&s?i.a8[a]:i.a8.default;return(0,r.jsxs)(l.fC,{ref:u,className:(0,d.cn)(c({variant:a,size:t,isRadioStyle:s}),e),...o,children:[r.jsx(l.z$,{className:(0,d.cn)("flex items-center justify-center text-current",s?"relative z-20":""),children:s?r.jsx("div",{className:(0,d.cn)("rounded-full",m,f)}):r.jsx(n.Z,{className:"h-4 w-4"})}),s&&r.jsx("div",{className:(0,d.cn)("rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300",m,p)})]})});u.displayName=l.fC.displayName},81524:(e,a,t)=>{t.r(a),t.d(a,{Combobox:()=>v});var r=t(98768),s=t(60343),l=t(54214),n=t(74932),o=t(13609),d=t(81311),i=t(48651),c=t(87175),u=t(15580),m=t(39544),f=t(56937),p=t(62861),x=t(69748),g=t(60797),b=t(99891);let h=s.memo(function({profile:e,vessel:a,label:t}){if(a)return r.jsx("div",{className:"size-7 flex items-center justify-center flex-shrink-0 [&_img]:!size-6 [&_svg]:!size-6",children:r.jsx(b.Z,{vessel:a})});if(e){let a=(0,x.getCrewInitials)(e.firstName,e.surname??"")??String(t).charAt(0);return(0,r.jsxs)(x.Avatar,{size:"xs",children:[r.jsx(x.AvatarImage,{src:e.avatar,alt:String(t)}),r.jsx(x.AvatarFallback,{children:a})]})}return null}),v=({options:e,title:a,value:t,defaultValues:x,onChange:b,placeholder:v="Select an option",buttonClassName:w="",multi:y=!1,isDisabled:N=!1,isLoading:j=!1,label:k,labelPosition:C="top",required:_=!1,labelClassName:R="",searchThreshold:z=5,noResultsMessage:S="No results found.",searchPlaceholder:T="Search...",groupBy:D,selectAllLabel:Z="Select all",badgeLimit:A=2,wrapBadges:F=!1,responsiveBadges:M=!0,modal:V=!1,...E})=>{let H=s.useId(),[I,L]=s.useState(!1),[Y,P]=s.useState(""),[O,B]=s.useState(null),$=s.useRef(null),[q,U]=function(e,a){let[t,r]=s.useState(a);return[void 0!==e?e:t,r]}(t,y?x||[]:x||null),G=s.useMemo(()=>{if(!Y)return e;let a=Y.toLowerCase();return e.filter(e=>{let t=String(e.label??"").toLowerCase();return t.includes(a)||t.split(" ").some(e=>e.startsWith(a))})},[e,Y]),Q=s.useMemo(()=>D?G.reduce((e,a)=>{let t=D(a)||"Other";return(e[t]=e[t]||[]).push(a),e},{}):{ungrouped:G},[G,D]),W=function({badges:e,enabled:a,fallbackLimit:t,containerRef:r,gap:l=4,reserveMoreButtonWidth:n=80}){let[o,d]=(0,s.useState)(t),[i,c]=(0,s.useState)(!1);(0,s.useRef)(null);let u=(0,s.useRef)(new Map),m=(0,s.useRef)(null);return(0,s.useCallback)(async r=>{if(!a||0===e.length||r<=0){d(Math.min(t,e.length));return}c(!0);try{m.current||(m.current=document.createElement("div"),m.current.style.position="absolute",m.current.style.visibility="hidden",m.current.style.pointerEvents="none",m.current.style.top="-9999px",m.current.style.left="-9999px",document.body.appendChild(m.current));let a=[];for(let t=0;t<e.length;t++){let r=e[t],s=`${r.value}-${r.label}`,l=u.current.get(s);if(!l&&m.current){let e=document.createElement("div");e.className="rounded-md min-w-28 font-normal px-1.5 py-0.5 flex items-center gap-1 bg-card transition-colors flex-shrink-0 h-full min-w-min border border-border",e.style.position="absolute",e.style.visibility="hidden",e.style.whiteSpace="nowrap";let a=document.createElement("div");if(a.className="flex flex-1 items-center overflow-auto gap-2.5",r.profile||r.vessel){let e=document.createElement("div");e.className="size-7 flex-shrink-0",a.appendChild(e)}let t=document.createElement("span");t.className="text-base leading-5 max-w-40 truncate text-input",t.textContent=String(r.label??""),a.appendChild(t);let n=document.createElement("div");n.className="ml-1 flex items-center justify-center",n.style.width="18px",n.style.height="18px",e.appendChild(a),e.appendChild(n),m.current.appendChild(e),l=e.getBoundingClientRect().width,u.current.set(s,l),m.current.removeChild(e)}a.push(l||120)}let t=0,s=0;e.length;for(let e=0;e<a.length;e++){let o=a[e],d=s>0?l:0,i=e<a.length-1?n:0;if(t+d+o+i<=r)t+=d+o,s++;else break}0===s&&e.length>0&&r>50&&(s=1),d(s)}catch(a){console.warn("Error calculating responsive badges:",a),d(Math.min(t,e.length))}finally{c(!1)}},[e,a,t,l,n]),{visibleBadges:e.slice(0,o),hiddenCount:Math.max(0,e.length-o),isCalculating:i}}({badges:Array.isArray(q)?q:[],enabled:M&&y&&!F,fallbackLimit:A,containerRef:$}),[J,X]=s.useMemo(()=>{if(!y||!Array.isArray(q)||0===q.length)return[[],0];if(F)return[q,0];if(M)return[W.visibleBadges,W.hiddenCount];let e=Math.max(A,0),a=q.slice(0,e);return[a,q.length-a.length]},[q,y,A,F,M,W]),K=s.useCallback(e=>y?Array.isArray(q)&&q.some(a=>a.value===e.value):q?.value===e.value,[q,y]),ee=s.useCallback(()=>{P(e=>e)},[]),ea=s.useCallback(a=>{if(y&&"select-all"===a){let e=Array.isArray(q)?[...q]:[],a=G.every(a=>e.some(e=>e.value===a.value))?e.filter(e=>!G.some(a=>a.value===e.value)):[...e.filter(e=>!G.some(a=>a.value===e.value)),...G.filter(a=>!e.some(e=>e.value===a.value))];U(a),b(a),ee();return}let t=e.find(e=>e.value===a);if(t){if(y){let e=Array.isArray(q)?[...q]:[],a=e.findIndex(e=>e.value===t.value),r=a>=0?[...e.slice(0,a),...e.slice(a+1)]:[...e,t];U(r),b(r),ee()}else{let e=q?.value===t.value?null:t;U(e),b(e),L(!1)}}},[y,q,G,e,b,ee]),et=s.useCallback(e=>{let a=q.filter(a=>a.value!==e);U(a),b(a),ee()},[q,b,ee]);s.useEffect(()=>{I||(P(""),B(null))},[I]),s.useEffect(()=>{void 0!==t&&U(t)},[t]);let er=s.useMemo(()=>y&&Array.isArray(q)&&0!==q.length?`Selected options: ${q.map(e=>e.label??"Unknown").join(", ")}`:"",[y,q]),es=()=>r.jsx(m.Button,{id:H,disabled:N,"aria-required":_,variant:"outline",asInput:!0,"data-wrap":F?"true":void 0,className:(0,f.cn)("justify-between shadow-none font-normal h-13 flex-1 px-4 bg-card text-input transition-colors","focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",F?"items-start min-h-[43px] h-fit py-3":"items-center justify-between max-h-[43px]",w),"aria-expanded":I,"aria-haspopup":"listbox","aria-describedby":`${H}-sr`,iconLeft:y&&r.jsx(l.Z,{className:"flex-shrink-0 size-3 text-neutral-400"}),iconRight:r.jsx(n.Z,{size:20,className:"text-neutral-400"}),...E,children:r.jsx("div",{className:"w-full flex flex-col overflow-hidden gap-2.5 min-w-0",children:y?Array.isArray(q)&&q.length>0?r.jsx("div",{className:"w-full grid border-l border-border ps-2.5 gap-2.5",children:(0,r.jsxs)("div",{ref:$,className:(0,f.cn)("flex gap-1",F?"flex-wrap":"flex-nowrap flex-1 overflow-hidden"),children:[J.map(e=>(0,r.jsxs)(c.C,{variant:"outline",title:String(e.label??""),className:(0,f.cn)("rounded-md min-w-28 font-normal px-1.5 py-0.5 flex items-center gap-1 bg-card transition-colors flex-shrink-0",F?"h-fit w-fit":"h-full min-w-min overflow-auto"),children:[(0,r.jsxs)("div",{className:"flex flex-1 items-center overflow-auto gap-2.5",children:[r.jsx(h,{profile:e.profile,vessel:e.vessel,label:e.label}),r.jsx("span",{className:"text-base leading-5 max-w-40 truncate text-input",children:e.label??""})]}),r.jsx("div",{className:"ml-1 flex items-center text-neutral-400/50 hover:text-neutral-400 justify-center",onClick:a=>{a.stopPropagation(),et(e.value)},"aria-label":`Remove ${e.label??""}`,children:r.jsx(o.Z,{size:18})})]},e.value)),X>0&&(0,r.jsxs)(c.C,{variant:"outline",className:"rounded-md px-1.5 py-0.5 w-fit h-full bg-card flex-shrink-0","aria-label":`${X} more selected`,children:["+",X," more"]})]})}):r.jsx("div",{className:"w-full grid",children:r.jsx("span",{className:"text-base flex-1 flex items-center truncate leading-5 text-input",children:a||r.jsx("span",{className:"text-neutral-400",children:v})})}):(0,r.jsxs)("span",{className:"flex items-center gap-2.5",children:[r.jsx(h,{profile:q?.profile,vessel:q?.vessel,label:q?.label}),r.jsx("span",{className:"text-base leading-5 text-input",children:q?.label??r.jsx("span",{className:"text-neutral-400",children:v})})]})})}),el=()=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(u.Popover,{modal:V,open:I,onOpenChange:L,children:[r.jsx(u.PopoverTrigger,{asChild:!0,children:es()}),r.jsx(u.PopoverContent,{align:"start",className:(0,f.cn)("p-0 z-[9999] sm:w-fit sm:min-w-[300px] w-[--radix-popover-trigger-width] max-h-[400px] overflow-y-auto","[&_cmdk-item][data-selected=true]:bg-transparent [&_cmdk-item][data-selected=true]:text-foreground"),children:(0,r.jsxs)(i.Command,{className:"w-full",loop:!1,shouldFilter:!1,value:"",onKeyDown:e=>{"ArrowUp"===e.key||"ArrowDown"===e.key?B("keyboard-nav"):"Escape"===e.key&&L(!1)},onMouseMove:()=>"keyboard-nav"===O&&B(null),children:[e.length>=z&&r.jsx(i.CommandInput,{placeholder:T,className:"flex h-9 w-full rounded-md bg-card/0 py-3 text-sm outline-none placeholder:text-neutral-400 disabled:cursor-not-allowed disabled:opacity-50",value:Y,onValueChange:P}),(0,r.jsxs)(i.CommandList,{className:"p-2.5 max-h-[320px] overflow-y-auto",children:[r.jsx(i.CommandEmpty,{children:S}),y&&G.length>0&&r.jsx(i.CommandGroup,{children:(0,r.jsxs)(i.CommandItem,{value:"select-all",onSelect:()=>{ea("select-all"),B(null)},"data-selected":"keyboard-nav"===O&&void 0,className:"flex items-center gap-2.5 h-[33px] py-[6px] px-5 hover:bg-background hover:text-primary",children:[r.jsx("div",{className:(0,f.cn)("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",G.every(e=>Array.isArray(q)&&q.some(a=>a.value===e.value))?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:r.jsx(d.Z,{className:"h-3 w-3"})}),r.jsx("span",{className:"text-base leading-5 text-input",children:Z})]})}),Object.entries(Q).map(([e,a])=>r.jsx(i.CommandGroup,{heading:D&&"ungrouped"!==e?e:void 0,children:a.map(e=>(0,r.jsxs)(i.CommandItem,{value:e.value,onSelect:()=>{ea(e.value),B(null),y||L(!1)},className:(0,f.cn)("flex items-center gap-2.5 h-[33px] py-[6px] px-5 my-1",!y&&K(e)?"bg-accent text-accent-foreground":"","rounded-md cursor-pointer focus:bg-accent text-input","border border-card/0 hover:bg-accent hover:border hover:border-border",e.className),"data-selected":"keyboard-nav"===O&&void 0,disabled:N,children:[y&&r.jsx("div",{className:(0,f.cn)("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",K(e)?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:r.jsx(d.Z,{className:"h-3 w-3"})}),(0,r.jsxs)("div",{className:"flex items-center gap-2.5",children:[r.jsx(h,{profile:e.profile,vessel:e.vessel,label:e.label}),r.jsx("span",{className:"text-base leading-5",children:e.label??""}),!y&&K(e)&&r.jsx(d.Z,{className:"ml-auto h-4 w-4 text-primary"})]})]},e.value))},e))]})]})})]}),er&&r.jsx("span",{id:`${H}-sr`,className:"sr-only","aria-live":"polite",children:er})]});if(j){let e=r.jsx(p.O,{className:(0,f.cn)("h-[43px] min-w-60",w)});return k?r.jsx(g.Label,{id:H,label:k,position:C,className:R,disabled:N,children:e}):e}return k?r.jsx(g.Label,{id:H,label:k,position:C,className:(0,f.cn)("w-full",R),required:_,disabled:N,children:el()}):el()}},48651:(e,a,t)=>{t.r(a),t.d(a,{Command:()=>i,CommandDialog:()=>c,CommandEmpty:()=>f,CommandGroup:()=>p,CommandInput:()=>u,CommandItem:()=>g,CommandList:()=>m,CommandSeparator:()=>x,CommandShortcut:()=>b});var r=t(98768),s=t(60343),l=t(27276),n=t(52346),o=t(56937),d=t(24794);let i=s.forwardRef(({className:e,...a},t)=>r.jsx(l.mY,{ref:t,className:(0,o.cn)("flex h-full w-full flex-col overflow-hidden rounded-md bg-card text-input",e),...a}));i.displayName=l.mY.displayName;let c=({children:e,...a})=>r.jsx(d.Dialog,{...a,children:r.jsx(d.DialogContent,{className:"overflow-hidden p-0",children:r.jsx(i,{className:"[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5",children:e})})}),u=s.forwardRef(({className:e,...a},t)=>(0,r.jsxs)("div",{className:"flex items-center border-b border-border px-3","cmdk-input-wrapper":"",children:[r.jsx(n.Z,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),r.jsx(l.mY.Input,{ref:t,className:(0,o.cn)("flex h-10 w-full rounded-md bg-transparent py-3 outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",e),...a})]}));u.displayName=l.mY.Input.displayName;let m=s.forwardRef(({className:e,...a},t)=>r.jsx(l.mY.List,{ref:t,className:(0,o.cn)("max-h-[300px] w-full overflow-y-auto overflow-x-hidden",e),...a}));m.displayName=l.mY.List.displayName;let f=s.forwardRef((e,a)=>r.jsx(l.mY.Empty,{ref:a,className:"py-6 text-center ",...e}));f.displayName=l.mY.Empty.displayName;let p=s.forwardRef(({className:e,...a},t)=>r.jsx(l.mY.Group,{ref:t,className:(0,o.cn)("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5[&_[cmdk-group-heading]]:text-muted-foreground",e),...a}));p.displayName=l.mY.Group.displayName;let x=s.forwardRef(({className:e,...a},t)=>r.jsx(l.mY.Separator,{ref:t,className:(0,o.cn)("-mx-1 h-px bg-border",e),...a}));x.displayName=l.mY.Separator.displayName;let g=s.forwardRef(({className:e,...a},t)=>r.jsx(l.mY.Item,{ref:t,className:(0,o.cn)("relative flex cursor-default gap-2 select-none items-center whitespace-nowrap rounded-sm px-2 py-1.5 outline-none data-[disabled=true]:pointer-events-none data-[selected=true]:text-primary data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",e),...a}));g.displayName=l.mY.Item.displayName;let b=({className:e,...a})=>r.jsx("span",{className:(0,o.cn)("ml-auto  tracking-widest text-muted-foreground",e),...a});b.displayName="CommandShortcut"},24794:(e,a,t)=>{t.r(a),t.d(a,{Dialog:()=>d,DialogClose:()=>u,DialogContent:()=>f,DialogDescription:()=>b,DialogFooter:()=>x,DialogHeader:()=>p,DialogOverlay:()=>m,DialogPortal:()=>c,DialogTitle:()=>g,DialogTrigger:()=>i});var r=t(98768),s=t(60343),l=t(32264),n=t(13609),o=t(56937);let d=l.fC,i=l.xz,c=l.h_,u=l.x8,m=s.forwardRef(({className:e,...a},t)=>r.jsx(l.aV,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));m.displayName=l.aV.displayName;let f=s.forwardRef(({className:e,children:a,...t},s)=>(0,r.jsxs)(c,{children:[r.jsx(m,{}),(0,r.jsxs)(l.VY,{ref:s,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[a,(0,r.jsxs)(l.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent-foreground data-[state=open]:text-muted-foreground",children:[r.jsx(n.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));f.displayName=l.VY.displayName;let p=({className:e,...a})=>r.jsx("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});p.displayName="DialogHeader";let x=({className:e,...a})=>r.jsx("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});x.displayName="DialogFooter";let g=s.forwardRef(({className:e,...a},t)=>r.jsx(l.Dx,{ref:t,className:(0,o.cn)("text-xs text-muted-foreground font-normal",e),...a}));g.displayName=l.Dx.displayName;let b=s.forwardRef(({className:e,...a},t)=>r.jsx(l.dk,{ref:t,className:(0,o.cn)(" text-muted-foreground",e),...a}));b.displayName=l.dk.displayName},27514:(e,a,t)=>{t.r(a),t.d(a,{DropdownMenu:()=>c,DropdownMenuCheckboxItem:()=>w,DropdownMenuContent:()=>h,DropdownMenuGroup:()=>m,DropdownMenuItem:()=>v,DropdownMenuLabel:()=>N,DropdownMenuPortal:()=>f,DropdownMenuRadioGroup:()=>x,DropdownMenuRadioItem:()=>y,DropdownMenuSeparator:()=>j,DropdownMenuShortcut:()=>k,DropdownMenuSub:()=>p,DropdownMenuSubContent:()=>b,DropdownMenuSubTrigger:()=>g,DropdownMenuTrigger:()=>u});var r=t(98768),s=t(60343),l=t(28969),n=t(39303),o=t(81311),d=t(74158),i=t(56937);let c=l.fC,u=l.xz,m=l.ZA,f=l.Uv,p=l.Tr,x=l.Ee,g=s.forwardRef(({className:e,inset:a,hoverEffect:t=!0,children:s,...o},d)=>(0,r.jsxs)(l.fF,{ref:d,className:(0,i.cn)("flex cursor-default gap-2 select-none items-center rounded-md px-2 py-1.5 outline-none data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a&&"pl-8",{"flex flex-row group relative m-0 py-0 gap-[11px] justify-start px-[18px] h-11 cursor-pointer w-[233px]":t},e),...o,children:[r.jsx("span",{className:"z-10",children:s}),r.jsx(n.Z,{className:"ml-auto z-10"}),t&&r.jsx("div",{className:(0,i.cn)("absolute w-full inset-0 mx-auto","group-hover:bg-accent group-hover:px-[6px] rounded-md group-hover:w-[233px] group-hover:border group-hover:border-border","will-change-transform will-change-width will-change-padding transform-gpu","transition-[width,padding] ease-out duration-600","outline-none focus:outline-none active:outline-none")})]}));g.displayName=l.fF.displayName;let b=s.forwardRef(({className:e,...a},t)=>r.jsx(l.tu,{ref:t,className:(0,i.cn)("z-50 min-w-fit overflow-hidden rounded-lg border border-border bg-card p-2 text-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a}));b.displayName=l.tu.displayName;let h=s.forwardRef(({className:e,sideOffset:a=4,...t},s)=>r.jsx(l.Uv,{children:r.jsx(l.VY,{ref:s,sideOffset:a,className:(0,i.cn)("z-50 min-w-fit rounded-[6px] shadow-[0px_4px_6px_#00000083] overflow-hidden border border-border bg-card p-2 text-foreground ","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})}));h.displayName=l.VY.displayName;let v=s.forwardRef(({className:e,inset:a,hoverEffect:t=!0,...s},n)=>(0,r.jsxs)(l.ck,{ref:n,className:(0,i.cn)("relative leading-5 flex cursor-default select-none items-center gap-2 rounded-md px-2 py-1.5","will-change-transform will-change-width will-change-padding transform-gpu","transition-[width,padding] ease-out duration-600","outline-none focus:outline-none active:outline-none","focus:bg-accent focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-6 [&>svg]:shrink-0",a&&"pl-8",t&&"flex flex-row group m-0 py-0 gap-[11px] justify-start px-[18px] h-11 cursor-pointer focus:bg-accent w-[233px]",e),...s,children:[t&&r.jsx("div",{className:(0,i.cn)("absolute scale-110 inset-0 pointer-events-none mx-auto","group-hover:bg-accent group-hover:px-[6px] -z-10 rounded-md group-hover:scale-100 group-hover:border group-hover:border-border","will-change-transform will-change-width will-change-padding transform-gpu","transition-[transform,padding] ease-out duration-600","outline-none focus:outline-none active:outline-none")}),s.children]}));v.displayName=l.ck.displayName;let w=s.forwardRef(({className:e,children:a,checked:t,...s},n)=>(0,r.jsxs)(l.oC,{ref:n,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2  outline-none transition-colors focus:bg-accent focus:text-primary data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:t,...s,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(l.wU,{children:r.jsx(o.Z,{className:"h-4 w-4"})})}),a]}));w.displayName=l.oC.displayName;let y=s.forwardRef(({className:e,children:a,...t},s)=>(0,r.jsxs)(l.Rk,{ref:s,className:(0,i.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2  outline-none transition-colors focus:bg-accent/90 focus:text-primary data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(l.wU,{children:r.jsx(d.Z,{className:"h-2 w-2 fill-current"})})}),a]}));y.displayName=l.Rk.displayName;let N=s.forwardRef(({className:e,inset:a,...t},s)=>r.jsx(l.__,{ref:s,className:(0,i.cn)("px-2 py-1.5  ",a&&"pl-8",e),...t}));N.displayName=l.__.displayName;let j=s.forwardRef(({className:e,...a},t)=>r.jsx(l.Z0,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-border",e),...a}));j.displayName=l.Z0.displayName;let k=({className:e,...a})=>r.jsx("span",{className:(0,i.cn)("ml-auto  tracking-widest opacity-60",e),...a});k.displayName="DropdownMenuShortcut"},83676:(e,a,t)=>{t.r(a),t.d(a,{FormFooter:()=>n});var r=t(98768),s=t(60343),l=t(56937);let n=s.forwardRef(({className:e,children:a,...t},s)=>r.jsx("div",{ref:s,className:(0,l.cn)("flex flex-wrap items-center rounded-lg gap-4 xs:gap-0 bg-accent border xs:h-[85px] border-border p-2.5 xs:p-5 justify-between",e),...t,children:a}));n.displayName="FormFooter"},25394:(e,a,t)=>{t.d(a,{bZ:()=>r.bZ,X:()=>r.X,iP:()=>s.iP,h9:()=>l.AlertDialogNew,qE:()=>n.Avatar,Q5:()=>n.AvatarFallback,Ct:()=>o.C,zx:()=>d.Button,Zb:()=>i.Zb,aY:()=>i.aY,SZ:()=>i.SZ,eW:()=>i.eW,Ol:()=>i.Ol,ll:()=>i.ll,XZ:()=>c.Checkbox,hQ:()=>u.Combobox,l0:()=>m.Form,NI:()=>m.FormControl,Wi:()=>m.FormField,ZL:()=>f.FormFooter,xJ:()=>m.FormItem,lX:()=>m.FormLabel,H1:()=>R.H1,H2:()=>R.H2,H3:()=>R.H3,H4:()=>R.H4,H5:()=>R.H5,II:()=>p.I,__:()=>x.Label,Bu:()=>z.ListHeader,P:()=>R.P,J2:()=>g.Popover,yk:()=>g.PopoverContent,CM:()=>g.PopoverTrigger,Ee:()=>b.Ee,mJ:()=>b.mJ,xr:()=>h.x,OC:()=>v.Select,Bw:()=>v.SelectContent,Ql:()=>v.SelectItem,i4:()=>v.SelectTrigger,ki:()=>v.SelectValue,Z0:()=>w.Separator,yo:()=>y.Sheet,Fm:()=>y.SheetBody,ue:()=>y.SheetContent,FF:()=>y.SheetFooter,Tu:()=>y.SheetHeader,bC:()=>y.SheetTitle,Od:()=>N.O,OE:()=>D,iA:()=>j.iA,RM:()=>j.RM,pj:()=>j.pj,ss:()=>j.ss,xD:()=>j.xD,SC:()=>j.SC,mQ:()=>k.Tabs,dr:()=>k.TabsList,SP:()=>k.TabsTrigger,gx:()=>C.Textarea,u:()=>_.Tooltip,_v:()=>_.TooltipContent,aJ:()=>_.TooltipTrigger,xE:()=>n.getCrewInitials}),t(94440);var r=t(8943),s=t(6834),l=t(57103),n=t(69748),o=t(87175),d=t(39544);t(59771);var i=t(35024),c=t(8750),u=t(81524);t(48651),t(24794),t(27514);var m=t(57906),f=t(83676),p=t(71890),x=t(60797),g=t(15580),b=t(70906),h=t(29052),v=t(24224),w=t(26509),y=t(69852),N=t(62861);t(79181),t(17026);var j=t(26659),k=t(36895),C=t(52269);t(26731);var _=t(70684),R=t(74602);t(40533);var z=t(39650),S=t(98768),T=t(56937);let D=({isOverdue:e,isUpcoming:a,label:t,className:r})=>e?S.jsx("span",{className:(0,T.cn)("alert w-fit inline-block text-nowrap rounded-md","text-sm xs:text-base !py-0.5 px-2 xs:px-3 xs:!py-2",r),children:t}):S.jsx("span",{className:(0,T.cn)("text-nowrap text-sm xs:text-base",r),children:t})},39650:(e,a,t)=>{t.r(a),t.d(a,{ListHeader:()=>l});var r=t(98768);t(60343);var s=t(74602);function l({icon:e,title:a,actions:t,iconClassName:l="",titleClassName:n=""}){return r.jsx("div",{className:"bg-card phablet:bg-background pb-1.5 xs:pb-[7px] z-50 sticky top-0 left-0 right-0 pt-3 tiny:px-2",children:(0,r.jsxs)("div",{className:"flex items-start justify-between gap-4 flex-nowrap",children:[(0,r.jsxs)("div",{className:"flex py-3 min-w-0 flex-1 gap-x-4",children:[e&&r.jsx("div",{className:`flex-shrink-0 hidden xs:block ${l}`,children:e}),r.jsx("div",{className:"flex items-center pe-4",children:r.jsx(s.H1,{className:`${n} text-wrap`,children:a})})]}),t&&r.jsx("div",{className:"flex-shrink-0 py-3",children:t})]})})}},15580:(e,a,t)=>{t.r(a),t.d(a,{Popover:()=>o,PopoverAnchor:()=>i,PopoverContent:()=>c,PopoverTrigger:()=>d});var r=t(98768),s=t(60343),l=t(57843),n=t(56937);let o=({children:e,triggerType:a="click",...t})=>{let[n,o]=s.useState(!1),d="hover"===a,i=d?"div":s.Fragment;return r.jsx(l.fC,{open:d?n:t.open,onOpenChange:d?o:t.onOpenChange,...t,children:r.jsx(i,{...d?{onMouseEnter:()=>o(!0),onMouseLeave:()=>o(!1)}:{},children:e})})},d=l.xz,i=l.ee,c=s.forwardRef(({className:e,align:a="center",sideOffset:t=8,...s},o)=>r.jsx(l.h_,{children:r.jsx(l.VY,{ref:o,align:a,sideOffset:t,className:(0,n.cn)("z-50 w-72 rounded-md border border-border bg-card p-4 text-foreground outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));c.displayName=l.VY.displayName},70906:(e,a,t)=>{t.d(a,{Ee:()=>c,Eo:()=>f,Qs:()=>m,a8:()=>p,mJ:()=>x});var r=t(98768),s=t(60343),l=t(93500),n=t(85745),o=t(70684),d=t(56937);let i=(0,n.j)("grid gap-2",{variants:{layout:{horizontal:"flex flex-row",vertical:"flex flex-col gap-2"},gap:{default:"",none:"gap-0",large:"gap-4"}},defaultVariants:{layout:"vertical",gap:"default"}}),c=s.forwardRef(({className:e,variant:a,layout:t,gap:s,...n},o)=>r.jsx(l.Root,{className:(0,d.cn)(i({layout:"horizontal"===a?"horizontal":"vertical"===a?"vertical":t,gap:s}),e),...n,ref:o}));c.displayName=l.Root.displayName;let u=(0,n.j)("aspect-square rounded-full group flex items-center justify-center border border-border relative shadow-[0_2px_0_#FFFFFF33] focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"text-curious-blue-600",success:"text-bright-turquoise-600",destructive:"text-cinnabar-700",outline:"text-foreground",secondary:"text-neutral-400","light-blue":"text-accent-foreground",pink:"text-pink-vivid-700",warning:"text-fire-bush-700"},size:{sm:"size-3",default:"size-4",md:"size-5",lg:"size-7"}},defaultVariants:{variant:"default",size:"default"}}),m={sm:"size-2.5",default:"size-3",md:"size-4",lg:"size-5"},f={default:"bg-curious-blue-600",success:"bg-bright-turquoise-600",destructive:"bg-destructive",outline:"bg-foreground",secondary:"bg-neutral-400","outer-space":"bg-foreground",pink:"bg-pink-vivid-700",warning:"bg-fire-bush-600"},p={default:"group-hover:bg-curious-blue-200",success:"group-hover:bg-bright-turquoise-200",destructive:"group-hover:bg-cinnabar-300",outline:"group-hover:bg-foreground/20",secondary:"group-hover:bg-border","outer-space":"group-hover:bg-curious-blue-200",pink:"group-hover:bg-pink-vivid-200",warning:"group-hover:bg-fire-bush-200"},x=s.forwardRef(({className:e,variant:a,size:t,tooltip:s,...n},i)=>{let c=t?m[t]:m.default,x=a?f[a]:f.default,g=a?p[a]:p.default,b=(0,r.jsxs)(l.Item,{ref:i,className:(0,d.cn)(u({variant:a,size:t}),e,'before:content-[""] before:absolute before:inset-0 before:rounded-full before:shadow-[inset_0_2px_2px_#0000001A]'),...n,children:[r.jsx(l.Indicator,{className:"flex relative z-20 items-center justify-center",children:r.jsx("div",{className:(0,d.cn)("rounded-full",c,x)})}),r.jsx("div",{className:(0,d.cn)("rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300",c,g)})]});return s?r.jsx(o.TooltipProvider,{children:(0,r.jsxs)(o.Tooltip,{children:[r.jsx(o.TooltipTrigger,{asChild:!0,children:b}),r.jsx(o.TooltipContent,{children:s})]})}):b});x.displayName=l.Item.displayName},29052:(e,a,t)=>{t.d(a,{x:()=>o});var r=t(98768),s=t(60343),l=t(97514),n=t(56937);let o=s.forwardRef(({className:e,children:a,...t},s)=>(0,r.jsxs)(l.Root,{ref:s,className:(0,n.cn)("relative overflow-hidden bg-card phablet:bg-muted/0",e),...t,children:[r.jsx(l.Viewport,{className:"h-full w-full rounded-[inherit]",children:a}),r.jsx(d,{}),r.jsx(l.Corner,{})]}));o.displayName=l.Root.displayName;let d=s.forwardRef(({className:e,orientation:a="vertical",...t},s)=>r.jsx(l.ScrollAreaScrollbar,{ref:s,orientation:a,className:(0,n.cn)("flex touch-none select-none transition-colors","vertical"===a&&"h-full w-2.5 border-l-transparent p-[1px]","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...t,children:r.jsx(l.ScrollAreaThumb,{className:"relative flex-1 rounded-full bg-border"})}));d.displayName=l.ScrollAreaScrollbar.displayName},24224:(e,a,t)=>{t.r(a),t.d(a,{Select:()=>c,SelectContent:()=>g,SelectGroup:()=>u,SelectItem:()=>h,SelectLabel:()=>b,SelectScrollDownButton:()=>x,SelectScrollUpButton:()=>p,SelectSeparator:()=>v,SelectTrigger:()=>f,SelectValue:()=>m});var r=t(98768),s=t(60343),l=t(51881),n=t(74932),o=t(52396),d=t(81311),i=t(56937);let c=l.fC,u=l.ZA,m=l.B4,f=s.forwardRef(({className:e,children:a,...t},s)=>(0,r.jsxs)(l.xz,{ref:s,className:(0,i.cn)("flex h-11 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2  shadow-sm placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[a,r.jsx(l.JO,{asChild:!0,children:r.jsx(n.Z,{className:"h-4 w-4 opacity-50"})})]}));f.displayName=l.xz.displayName;let p=s.forwardRef(({className:e,...a},t)=>r.jsx(l.u_,{ref:t,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:r.jsx(o.Z,{className:"h-4 w-4"})}));p.displayName=l.u_.displayName;let x=s.forwardRef(({className:e,...a},t)=>r.jsx(l.$G,{ref:t,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:r.jsx(n.Z,{className:"h-5 w-5"})}));x.displayName=l.$G.displayName;let g=s.forwardRef(({className:e,children:a,position:t="popper",...s},n)=>r.jsx(l.h_,{children:(0,r.jsxs)(l.VY,{ref:n,className:(0,i.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md bg-card text-input shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...s,children:[r.jsx(p,{}),r.jsx(l.l_,{className:(0,i.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),r.jsx(x,{})]})}));g.displayName=l.VY.displayName;let b=s.forwardRef(({className:e,...a},t)=>r.jsx(l.__,{ref:t,className:(0,i.cn)("px-2 py-1.5  ",e),...a}));b.displayName=l.__.displayName;let h=s.forwardRef(({className:e,children:a,...t},s)=>(0,r.jsxs)(l.ck,{ref:s,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8  outline-none focus:bg-accent-foreground focus:text-primary data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[r.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(l.wU,{children:r.jsx(d.Z,{className:"h-4 w-4"})})}),r.jsx(l.eT,{children:a})]}));h.displayName=l.ck.displayName;let v=s.forwardRef(({className:e,...a},t)=>r.jsx(l.Z0,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",e),...a}));v.displayName=l.Z0.displayName},26509:(e,a,t)=>{t.r(a),t.d(a,{Separator:()=>o});var r=t(98768),s=t(60343),l=t(62227),n=t(56937);let o=s.forwardRef(({className:e,orientation:a="horizontal",decorative:t=!0,...s},o)=>r.jsx(l.f,{ref:o,decorative:t,orientation:a,className:(0,n.cn)("shrink-0 bg-border","horizontal"===a?"h-[1px] w-full":"h-full w-[1px]",e),...s}));o.displayName=l.f.displayName},69852:(e,a,t)=>{t.r(a),t.d(a,{Sheet:()=>x,SheetBody:()=>k,SheetClose:()=>b,SheetContent:()=>y,SheetDescription:()=>_,SheetFooter:()=>j,SheetHeader:()=>N,SheetOverlay:()=>v,SheetPortal:()=>h,SheetTitle:()=>C,SheetTrigger:()=>g});var r=t(98768),s=t(60343),l=t(32264),n=t(85745),o=t(52396),d=t(74932),i=t(41641),c=t(39303),u=t(56937),m=t(74602),f=t(29052);let p=s.createContext("right"),x=l.fC,g=l.xz,b=l.x8,h=l.h_,v=s.forwardRef(({className:e,...a},t)=>r.jsx(l.aV,{className:(0,u.cn)("fixed inset-0 z-50 bg-muted/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a,ref:t}));v.displayName=l.aV.displayName;let w=(0,n.j)("fixed z-50 gap-4 bg-card flex flex-col shadow-lg border border-border transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",{variants:{side:{top:"inset-x-5 top-0 border-b h-[60svh] w-full data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-5 bottom-0 border-t h-[60svh] w-full data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-[31px] border-l-0 rounded-r-[6px] left-0 w-3/4 data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-fit sm:min-w-sm",right:"inset-y-[31px] border-r-0 rounded-l-[6px] right-0 w-11/12 md:w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-fit sm:min-w-sm"}},defaultVariants:{side:"right"}}),y=s.forwardRef(({side:e="right",className:a,children:t,...n},m)=>{let f=s.useMemo(()=>{switch(e){case"top":return o.Z;case"bottom":return d.Z;case"left":return i.Z;default:return c.Z}},[e]);return(0,r.jsxs)(h,{children:[r.jsx(v,{}),r.jsx(p.Provider,{value:e,children:(0,r.jsxs)(l.VY,{ref:m,className:(0,u.cn)(w({side:e}),a),...n,children:[(0,r.jsxs)(l.x8,{className:(0,u.cn)({top:"absolute left-1/2 -translate-x-1/2 -top-[21px] size-[42px]",bottom:"absolute left-1 -top-[21px] size-[42px]",left:"absolute -right-[21px] top-[34px] size-[42px]",right:"absolute -left-[21px] top-[34px] size-[42px]"}[e],"rounded-full flex items-center justify-center text-background0 bg-background border border-border transition-opacity focus:outline-none focus:ring-1 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary"),children:[r.jsx(f,{className:"size-6"}),r.jsx("span",{className:"sr-only",children:"Close"})]}),t]})})]})});y.displayName=l.VY.displayName;let N=({className:e,side:a,...t})=>{let l=s.useContext(p);return r.jsx("div",{className:(0,u.cn)("flex p4 phablet:p-12 flex-col text-foreground bg-background space-y-2",{top:"rounded-b-md",bottom:"rounded-t-md",left:"rounded-se-md",right:"rounded-ss-md"}[a||l],e),...t})};N.displayName="SheetHeader";let j=({className:e,...a})=>r.jsx("div",{className:(0,u.cn)("flex flex-col-reverse p-5 border-t border-border sm:flex-row sm:justify-end sm:space-x-2",e),...a});j.displayName="SheetFooter";let k=({className:e,...a})=>r.jsx(f.x,{className:"flex-1 h-full",children:r.jsx("div",{className:(0,u.cn)("flex flex-col px-7 py-6 space-y-4 leading-7",e),...a})});k.displayName="SheetBody";let C=s.forwardRef(({className:e,...a},t)=>r.jsx(l.Dx,{ref:t,className:(0,u.cn)("text-foreground",e),...a,children:r.jsx(m.H2,{className:"text-foreground",children:a.children})}));C.displayName=l.Dx.displayName;let _=s.forwardRef(({className:e,...a},t)=>r.jsx(l.dk,{ref:t,className:(0,u.cn)(" text-muted-foreground",e),...a}));_.displayName=l.dk.displayName},62861:(e,a,t)=>{t.d(a,{O:()=>l});var r=t(98768),s=t(56937);function l({className:e,...a}){return r.jsx("div",{className:(0,s.cn)("animate-pulse rounded-md bg-primary/10",e),...a})}},79181:(e,a,t)=>{t.d(a,{i:()=>o});var r=t(98768),s=t(60343),l=t(45305),n=t(56937);let o=s.forwardRef(({className:e,...a},t)=>(0,r.jsxs)(l.Root,{ref:t,className:(0,n.cn)("relative flex w-full touch-none select-none items-center",e),...a,children:[r.jsx(l.Track,{className:"relative h-1.5 w-full grow overflow-hidden rounded-full bg-primary/20",children:r.jsx(l.Range,{className:"absolute h-full bg-primary"})}),r.jsx(l.Thumb,{className:"block h-4 w-4 rounded-full border bg-primary/50 bg-wedgewood-50 shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"})]}));o.displayName=l.Root.displayName},17026:(e,a,t)=>{t.d(a,{r:()=>o});var r=t(98768),s=t(60343),l=t(44106),n=t(56937);let o=s.forwardRef(({className:e,...a},t)=>r.jsx(l.Root,{className:(0,n.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...a,ref:t,children:r.jsx(l.Thumb,{className:(0,n.cn)("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})}));o.displayName=l.Root.displayName},26659:(e,a,t)=>{t.d(a,{RM:()=>d,SC:()=>c,iA:()=>n,pj:()=>m,ss:()=>u,xD:()=>o,yt:()=>i});var r=t(98768),s=t(60343),l=t(56937);let n=s.forwardRef(({className:e,...a},t)=>r.jsx("div",{className:(0,l.cn)("relative w-full overflow-auto",e),children:r.jsx("table",{ref:t,cellSpacing:0,className:"w-full caption-bottom border-spacing-0",...a})}));n.displayName="Table";let o=s.forwardRef(({className:e,...a},t)=>r.jsx("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-border",e),...a}));o.displayName="TableHeader";let d=s.forwardRef(({className:e,children:a,...t},s)=>r.jsx("tbody",{ref:s,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...t,children:a}));d.displayName="TableBody";let i=s.forwardRef(({className:e,...a},t)=>r.jsx("tfoot",{ref:t,className:(0,l.cn)("border-t border-border bg-background/50 font-medium [&>tr]:last:border-b-0",e),...a}));i.displayName="TableFooter";let c=s.forwardRef(({className:e,...a},t)=>r.jsx("tr",{ref:t,className:(0,l.cn)("relative cursor-pointer border-border group data-[state=selected]:bg-accent",e),...a,children:a.children}));c.displayName="TableRow";let u=s.forwardRef(({className:e,...a},t)=>r.jsx("th",{ref:t,className:(0,l.cn)("h-8 px-0 small:px-1 phablet:px-[7px] pb-2 small:p-auto cursor-default relative text-xs tiny:text-xs small:text-sm text-neutral-400 font-normal [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] w-fit whitespace-nowrap",e),...a}));u.displayName="TableHead";let m=s.forwardRef(({className:e,noHoverEffect:a=!1,statusOverlay:t=!1,statusOverlayColor:s,...n},o)=>(0,r.jsxs)("td",{ref:o,className:(0,l.cn)("h-20 font-normal align-center text-card-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px] tiny:first:pl-1 tiny:last:pr-1 small:first:pl-1.5 small:last:pr-1.5 phablet:first:pl-2.5 phablet:last:pr-2.5",e),...n,children:[t&&r.jsx("span",{className:"absolute flex w-full rounded-md pointer-events-none overflow-hidden inset-y-1 inset-x-0",children:r.jsx("span",{className:(0,l.cn)("w-full rounded-md border bg-transparent","hidden first:block","destructive"===s&&"border-none bg-destructive/[2%]","warning"===s&&"border-warning bg-warning/[2%]")})}),!a&&r.jsx("span",{className:"absolute flex w-full rounded-md pointer-events-none overflow-hidden inset-y-1 inset-x-0",children:r.jsx("span",{className:(0,l.cn)("bg-accent","w-0","group-hover:w-full","transition-[width] ease-out duration-300","will-change-transform will-change-width","hidden first:block","destructive"===s&&"m-px rounded-md bg-destructive/[3%]","warning"===s&&"m-px rounded-md bg-warning/[3%]")})}),r.jsx("span",{className:"relative flex flex-col w-full overflow-auto z-10",children:n.children})]}));m.displayName="TableCell",s.forwardRef(({className:e,...a},t)=>r.jsx("caption",{ref:t,className:(0,l.cn)("mt-4 text-muted-foreground",e),...a})).displayName="TableCaption"},36895:(e,a,t)=>{t.r(a),t.d(a,{Tabs:()=>o,TabsContent:()=>c,TabsList:()=>d,TabsTrigger:()=>i});var r=t(98768),s=t(60343),l=t(50384),n=t(56937);let o=l.fC,d=s.forwardRef(({className:e,...a},t)=>r.jsx(l.aV,{ref:t,className:(0,n.cn)("inline-flex h-12 items-center justify-center border border-border border-dashed rounded-lg bg-card p-1 text-popover-foreground gap-2.5",e),...a}));d.displayName=l.aV.displayName;let i=s.forwardRef(({className:e,...a},t)=>r.jsx(l.xz,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap hover:bg-accent hover:text-accent-foreground rounded-md p-3 transition-all h-full focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow",e),...a}));i.displayName=l.xz.displayName;let c=s.forwardRef(({className:e,...a},t)=>r.jsx(l.VY,{ref:t,className:(0,n.cn)("mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));c.displayName=l.VY.displayName},52269:(e,a,t)=>{t.r(a),t.d(a,{Textarea:()=>n});var r=t(98768),s=t(60343),l=t(56937);let n=s.forwardRef(({className:e,autoResize:a,maxAutoHeight:t=400,...n},o)=>{let d=s.useRef(null),i=s.useMemo(()=>e=>{d.current=e,"function"==typeof o?o(e):o&&(o.current=e)},[o]),c=s.useCallback(()=>{let e=d.current;if(!e||!a)return;e.style.height="auto";let r=Math.min(e.scrollHeight,t);e.style.height=`${r}px`},[a,t]);return s.useEffect(()=>{if(a){c();let e=new ResizeObserver(c);return d.current&&e.observe(d.current),()=>e.disconnect()}},[a,c,n.value,n.defaultValue]),r.jsx("textarea",{className:(0,l.cn)("flex min-h-[172px] w-full rounded-md border border-border bg-card px-3 py-2 text-base text-input shadow-sm transition-colors","placeholder:text-neutral-400","focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-curious-blue-400","hover:border-curious-blue-400","disabled:cursor-not-allowed disabled:opacity-50","resize-y",e),ref:i,onChange:e=>{n.onChange?.(e),a&&c()},...n})});n.displayName="Textarea"},74602:(e,a,t)=>{t.d(a,{H1:()=>n,H2:()=>o,H3:()=>d,H4:()=>i,H5:()=>c,P:()=>u});var r=t(98768),s=t(56937),l=t(60343);let n=(0,l.forwardRef)(({children:e,className:a,...t},l)=>r.jsx("h1",{ref:l,className:(0,s.cn)("scroll-m-20 text-3xl phablet:text-4xl leading-[36px] my-0 font-extrabold",a),...t,children:e}));n.displayName="H1";let o=(0,l.forwardRef)(({children:e,className:a,...t},l)=>r.jsx("h2",{ref:l,className:(0,s.cn)("scroll-m-20 text-4xl leading-11 text-foreground tracking-[-0.09px] my-0 font-bold",a),...t,children:e}));o.displayName="H2";let d=(0,l.forwardRef)(({children:e,className:a,...t},l)=>r.jsx("h3",{ref:l,className:(0,s.cn)("scroll-m-20 text-2xl font-semibold text-foreground tracking-tight",a),...t,children:e}));d.displayName="H3";let i=(0,l.forwardRef)(({children:e,className:a,...t},l)=>r.jsx("h4",{ref:l,className:(0,s.cn)("scroll-m-20 text-xl font-semibold text-input tracking-tight",a),...t,children:e}));i.displayName="H4";let c=(0,l.forwardRef)(({children:e,className:a,...t},l)=>r.jsx("h5",{ref:l,className:(0,s.cn)("scroll-m-20 text-lg font-semibold text-input tracking-tight",a),...t,children:e}));c.displayName="H5";let u=(0,l.forwardRef)(({children:e,className:a,...t},l)=>r.jsx("p",{ref:l,className:(0,s.cn)("text-sm sm:text-base text-neutral-400",a),...t,children:e}));u.displayName="P"}};