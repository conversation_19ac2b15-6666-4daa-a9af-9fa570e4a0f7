(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1891],{87564:function(){},67857:function(e,t,o){Promise.resolve().then(o.bind(o,94095))},8644:function(e,t,o){"use strict";o.d(t,{E7:function(){return m},Fs:function(){return l},GJ:function(){return r},PE:function(){return n},UU:function(){return s},Zu:function(){return d},ay:function(){return c},io:function(){return u},j5:function(){return a}});var i=o(24979);let n=()=>{{let e="true"===localStorage.getItem("superAdmin"),t="true"===localStorage.getItem("admin"),o="true"===localStorage.getItem("crew");return!(e||t)&&o}},r=()=>{let e="true"===localStorage.getItem("superAdmin"),t="true"===localStorage.getItem("admin");return e||t},a=()=>"true"===localStorage.getItem("superAdmin"),s=()=>{n()&&(window.location.href="/dashboard")},c=()=>{r()||(window.location.href="/dashboard")},l=(e,t)=>t.includes("ADMIN")||t.includes(e),d=e=>JSON.parse(localStorage.getItem("permissions")||"[]"),u=async()=>{try{await i.Z.delete()}catch(e){console.error("[clearDB] deleting seaLogsDB failed: ",e)}try{await i.Z.open()}catch(e){console.error("[clearDB] opening seaLogsDB failed: ",e)}},m=()=>"true"===localStorage.getItem("useTripSchedule")},94095:function(e,t,o){"use strict";o.r(t);var i=o(12803),n=o(19011),r=o(10341),a=o(62007);t.default=()=>{var e;let t=null!==(e=(0,a.useSearchParams)().get("id"))&&void 0!==e?e:0;return(0,i.jsx)(r.Zb,{children:(0,i.jsx)(n.default,{id:+t})})}},54497:function(e,t,o){"use strict";var i=o(12803),n=o(52285);t.Z=function(e){let{message:t="Loading ...",errorMessage:o=""}=e;return(0,i.jsxs)("div",{className:"h-screen w-full flex flex-col items-center justify-center",children:[(0,i.jsx)("div",{children:(0,i.jsx)(n.default,{src:"/sealogs-loading.gif",alt:"Sealogs Logo",priority:!0,width:300,height:300,unoptimized:!0})}),o?(0,i.jsx)("div",{className:"text-destructive ",children:o}):(0,i.jsx)("div",{children:t})]})}},24979:function(e,t,o){"use strict";let i=new(o(1121)).ZP("seaLogsDB");i.version(2).stores({AssetReporting_LogBookEntrySection:"id, idbCRUD",BarCrossingChecklist:"id, idbCRUD, vesselID",CGEventMission:"id, idbCRUD, vesselID",Client:"id, idbCRUD",ComponentMaintenanceCheck:"id, idbCRUD",ComponentMaintenanceSchedule:"id, idbCRUD",Consequence:"id, idbCRUD",CrewDuty:"id, idbCRUD",CrewMembers_LogBookEntrySection:"id, idbCRUD",CrewTraining_LogBookEntrySection:"id, idbCRUD",CrewWelfare_LogBookEntrySection:"id, idbCRUD",CustomisedComponentField:"id, idbCRUD, customisedLogBookComponentID",CustomisedLogBookConfig:"id, idbCRUD, customisedLogBookID",DangerousGood:"id, idbCRUD",DangerousGoodsChecklist:"id, idbCRUD, vesselID",DangerousGoodsRecord:"id, idbCRUD, tripReport_LogBookEntrySectionID",Engine:"id, idbCRUD",Engine_LogBookEntrySection:"id, idbCRUD",Engine_Usage:"id, idbCRUD, maintenanceScheduleID, engineID",Engineer_LogBookEntrySection:"id, idbCRUD",EventType:"id, idbCRUD",EventType_BarCrossing:"id, idbCRUD, tripEventID, geoLocationID, geoLocationCompletedID, barCrossingChecklistID",EventType_PassengerDropFacility:"id, idbCRUD, tripEventID, geoLocationID",EventType_PersonRescue:"id, idbCRUD, missionID, tripEventID",EventType_RestrictedVisibility:"id, idbCRUD, tripEventID, startLocationID, endLocationID",EventType_Supernumerary:"id, idbCRUD",EventType_Tasking:"id, idbCRUD, currentEntryID, groupID, pausedTaskID, openTaskID, completedTaskID, tripEventID, geoLocationID, vesselRescueID, personRescueID, towingChecklistID, parentTaskingID",EventType_VesselRescue:"id, idbCRUD, missionID, vesselLocationID, tripEventID",FavoriteLocation:"id, idbCRUD, memberID",FuelLog:"id, idbCRUD, refuellingBunkeringID, eventType_TaskingID, eventType_PassengerDropFacilityID, logBookEntryID, fuelTankID",FuelTank:"id, idbCRUD",Fuel_LogBookEntrySection:"id, idbCRUD",GeoLocation:"id, idbCRUD",Inventory:"id, idbCRUD, vesselID",InfringementNotice:"id, idbCRUD",InfringementNotice_Signature:"id, idbCRUD, infringementNoticeID",Likelihood:"id, idbCRUD",LogBookEntry:"id, idbCRUD, vehicleID",LogBookEntryOldConfigs:"id, idbCRUD, logBookEntryID",LogBookEntrySection_Signature:"id, idbCRUD, logBookEntrySectionID",LogBookSignOff_LogBookEntrySection:"id, idbCRUD",MaintenanceCheck:"id, idbCRUD, maintenanceScheduleID",MaintenanceCheck_Signature:"id, idbCRUD, maintenanceCheckID",MaintenanceSchedule:"id, idbCRUD",MaintenanceScheduleSubTask:"id, idbCRUD, componentMaintenanceScheduleID",MemberTraining_Signature:"id, idbCRUD, memberID, trainingSessionID",MissionTimeline:"id, idbCRUD, missionID, vesselRescueID, personRescueID, maintenanceCheckID, subTaskID",MitigationStrategy:"id, idbCRUD",Ports_LogBookEntrySection:"id, idbCRUD",RefuellingBunkering:"id, idbCRUD, tripEventID, geoLocationID",TripUpdate:"id, idbCRUD, tripEventID, geoLocationID",RiskFactor:"id, idbCRUD, vesselID, riskRatingID, consequenceID, likelihoodID, towingChecklistID, dangerousGoodsChecklistID, barCrossingChecklistID, type",RiskRating:"id, idbCRUD",SeaLogsMember:"id, idbCRUD",SectionMemberComment:"id, idbCRUD, logBookEntrySectionID, commentType, comment, hideComment",Supernumerary_LogBookEntrySection:"id, idbCRUD, supernumeraryID",TowingChecklist:"id, idbCRUD, vesselID",TrainingLocation:"id, idbCRUD",TrainingSession:"id, idbCRUD, trainerID, logBookEntrySectionID, logBookEntryID, vesselID, trainingLocationID, geoLocationID",TrainingSessionDue:"id, idbCRUD, memberID, trainingTypeID, vesselID, trainingSessionID",TrainingType:"id, idbCRUD",TripEvent:"id, idbCRUD, logBookEntrySectionID",TripReport_LogBookEntrySection:"id, idbCRUD",TripReport_Stop:"id, idbCRUD, stopLocationID, logBookEntrySectionID, tripReportScheduleStopID, dangerousGoodsChecklistID",VehiclePosition:"id, idbCRUD, vehicleID",Vessel:"id, idbCRUD",VesselDailyCheck_LogBookEntrySection:"id, idbCRUD",VoyageSummary_LogBookEntrySection:"id, idbCRUD",WeatherForecast:"id, idbCRUD, logBookEntryID",WeatherObservation:"id, idbCRUD, logBookEntryID, forecastID",WeatherTide:"id, idbCRUD, logBookEntryID"}),i.open().catch(function(e){console.error("[seaLogsDB] Open failed: "+e)}),t.Z=i},11336:function(e,t,o){"use strict";var i=o(65717),n=o.n(i),r=o(24979);class a{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(e=>{let[t,o]=e;return[t,"number"==typeof o?o.toString():o]})),o=t.id,i={...t,__typename:"GeoLocation",idbCRUD:"Update",idbCRUDDate:n()().format("YYYY-MM-DD HH:mm:ss")},a=await this.getById(o);return a?await r.Z.GeoLocation.update(o,i):await r.Z.GeoLocation.add(i),a=await this.getById(o),console.log("GeoLocation save",e,a),a}catch(t){console.error("GeoLocation save",e,t)}}async getAll(){try{let e=await r.Z.GeoLocation.toArray();return console.log("GeoLocation getAll",e),e}catch(e){console.error("GeoLocation getAll",e)}}async getById(e){try{let t=await r.Z.GeoLocation.get("".concat(e));return console.log("GeoLocation getById",e,t),t}catch(t){console.error("GeoLocation getById",e,t)}}async getByIds(e){try{let t=await r.Z.GeoLocation.where("id").anyOf(e).toArray();return console.log("GeoLocation getByIds",e,t),t}catch(t){console.error("GeoLocation getByIds",e,t)}}async bulkAdd(e){try{return await r.Z.GeoLocation.bulkAdd(e),console.log("GeoLocation bulkAdd",e),e}catch(t){if("BulkError"===t.name){let o=t.failuresByPos.map(e=>e.key),i=e.filter(e=>!o.includes(e.id));return await r.Z.GeoLocation.bulkAdd(i),console.log("GeoLocation bulkAdd::BulkError",e,t),e}console.error("GeoLocation bulkAdd",e,t)}}async setProperty(e){try{if(e){let t=await r.Z.GeoLocation.get("".concat(e));return t.idbCRUD="Download",t.idbCRUDDate=n()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.GeoLocation.update(e,t),console.log("GeoLocation setProperty",e,t),t}}catch(t){console.error("GeoLocation setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.GeoLocation.update(e.id,e)})),console.log("GeoLocation multiUpdate",e)}catch(t){console.error("GeoLocation multiUpdate",e,t)}}}t.Z=a},70658:function(e,t,o){"use strict";var i=o(65717),n=o.n(i),r=o(24979);class a{async save(e){try{return await r.Z.SeaLogsMember.put({...e,idbCRUD:"Update",idbCRUDDate:n()().format("YYYY-MM-DD HH:mm:ss")}),console.log("SeaLogsMember save",e),e}catch(t){console.error("SeaLogsMember save",e,t)}}async getAll(){try{let e=await r.Z.SeaLogsMember.toArray();return console.log("SeaLogsMember getAll",e),e}catch(e){console.error("SeaLogsMember getAll",e)}}async getById(e){try{let t=await r.Z.SeaLogsMember.get("".concat(e));return console.log("SeaLogsMember getById",e,t),t}catch(t){console.error("SeaLogsMember getById",e,t)}}async getByIds(e){try{let t=await r.Z.SeaLogsMember.where("id").anyOf(e).toArray();return console.log("SeaLogsMember getByIds",e,t),t}catch(t){console.error("SeaLogsMember getByIds",e,t)}}async getByVesselId(e){try{let t=(await r.Z.SeaLogsMember.toArray()).filter(t=>t.vehicles.nodes.some(t=>t.id==="".concat(e)));return console.log("SeaLogsMember getByVesselID",e,t),t}catch(t){console.error("SeaLogsMember getByVesselID",e,t)}}async bulkAdd(e){try{return await r.Z.SeaLogsMember.bulkAdd(e),console.log("SeaLogsMember bulkAdd",e),e}catch(t){if("BulkError"===t.name){let o=t.failuresByPos.map(e=>e.key),i=e.filter(e=>!o.includes(e.id));return await r.Z.SeaLogsMember.bulkAdd(i),console.log("SeaLogsMember bulkAdd::BulkError",e,t),e}console.error("SeaLogsMember bulkAdd",e,t)}}async setProperty(e){try{if(e){let t=await r.Z.SeaLogsMember.get("".concat(e));return t.idbCRUD="Download",t.idbCRUDDate=n()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.SeaLogsMember.update(e,t),console.log("SeaLogsMember setProperty",e,t),t}}catch(t){console.error("SeaLogsMember setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.SeaLogsMember.update(e.id,e)})),console.log("SeaLogsMember multiUpdate",e)}catch(t){console.error("SeaLogsMember multiUpdate",e,t)}}async delete(e){try{return await r.Z.SeaLogsMember.delete("".concat(e)),console.log("SeaLogsMember delete",e,!0),!0}catch(t){console.error("SeaLogsMember delete",e,t)}}}t.Z=a},60469:function(e,t,o){"use strict";o.d(t,{Z:function(){return b}});var i=o(12803),n=o(60333),r=o(52285),a=o(53434),s=o.n(a),c=o(10341),l=o(75621),d=o(58140),u=o(49529);function m(e){let{files:t,onFilesSelected:o,text:a="Documents and Images",subText:s,bgClass:c="",multipleUpload:m=!0,acceptedFileTypes:g=".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf,.csv",isLoading:p=!1,renderFileItem:b,onFileClick:D,children:f,displayFiles:v=!0}=e,[y,h]=(0,n.useState)(!1),I=(0,n.useRef)(null),C=(0,u.cn)("relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none",y?"bg-accent border-primary":"bg-accent/50 border-border","text-foreground hover:bg-accent hover:border-primary","min-h-[10rem] cursor-pointer select-none",c),k=e=>{o(e)},x=e=>t=>{t.preventDefault(),h(e)},L=(e,t)=>(0,i.jsxs)("div",{onClick:()=>null==D?void 0:D(e),className:"flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden",children:[(0,i.jsx)(r.default,{src:"/sealogs-document_upload.svg",alt:"Document",width:48,height:48,className:"mb-2"}),(0,i.jsx)("div",{className:"text-xs text-center break-all text-muted-foreground",children:e.title})]},t);return(0,i.jsxs)("div",{className:"w-full pt-4 lg:pt-0",children:[t.length>0&&v&&(0,i.jsx)("div",{className:"flex flex-wrap gap-4 mb-4",children:t.filter(e=>{var t;return(null===(t=e.title)||void 0===t?void 0:t.length)>0}).map((e,t)=>b?b(e,t):L(e,t))}),(0,i.jsxs)("form",{className:C,onSubmit:e=>e.preventDefault(),onDragEnter:x(!0),onDragOver:x(!0),onDragLeave:x(!1),onDrop:e=>{e.preventDefault(),h(!1),e.dataTransfer.files&&k(e.dataTransfer.files)},onClick:()=>{I.current&&(I.current.value="",I.current.click())},"aria-label":"File uploader drop zone",children:[(0,i.jsx)("span",{className:"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider",children:a}),(0,i.jsx)(d.I,{ref:I,type:"file",className:"hidden",multiple:m,accept:g,onChange:e=>{e.target.files&&k(e.target.files)}}),(0,i.jsxs)("div",{className:"flex flex-col items-center gap-2 pointer-events-none",children:[(0,i.jsx)(r.default,{src:"/sealogs-document_upload.svg",alt:"Upload illustration",width:96,height:96,className:"relative -translate-x-2.5",priority:!0}),s&&(0,i.jsx)("span",{className:"text-sm font-medium text-neutral-400",children:s})]})]}),p&&(0,i.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2",children:[(0,i.jsx)(l.Z,{className:"h-5 w-5 animate-spin text-primary"}),(0,i.jsx)("span",{className:"text-sm text-muted-foreground",children:"Uploading..."})]}),f]})}var g=o(10927);let p=new(s()).S3({endpoint:"https://".concat("ddde1c1cd1aa25641691808dcbafdeb7",".r2.cloudflarestorage.com"),accessKeyId:"06c3e13a539f24e6fdf7075bf381bf5e",secretAccessKey:"****************************************************************",signatureVersion:"v4",region:"auto"});function b(e){let{files:t=[],setFiles:o,multipleUpload:a=!0,text:s="Documents and Images",subText:l,bgClass:d="",accept:u=".xlsx,.xls,image/*,.doc, .docx,.ppt, .pptx,.txt,.pdf,.csv",bucketName:b="sealogs",prefix:D="",displayFiles:f=!0}=e,[v,y]=(0,n.useState)(!1),[h,I]=(0,n.useState)(""),[C,k]=(0,n.useState)(!1),[x,L]=(0,n.useState)(0);(0,n.useEffect)(()=>{var e;L(+(null!==(e=localStorage.getItem("clientId"))&&void 0!==e?e:0))},[]);let S=async e=>{let i=x+"-"+D+e.name;if(null==t?void 0:t.some(e=>e.title===i)){(0,g.Am)({description:"File with same name already exists!",variant:"destructive"});return}y(!0),p.putObject({Bucket:b,Key:i,Body:e},(e,t)=>{if(y(!1),e)console.error(e),(0,g.Am)({description:"Failed to upload file",variant:"destructive"});else{let e={title:i};a?o(t=>[...t,e]):o([e])}})},U=e=>{p.getObject({Bucket:b,Key:e.title},async(t,o)=>{if(t)console.error(t),(0,g.Am)({description:"Failed to download file",variant:"destructive"});else{let t=e.title.split(".").pop()||"",i=new Blob([null==o?void 0:o.Body]),n=URL.createObjectURL(i);if(t.match(/^(jpg|jpeg|png|gif|bmp)$/i))I(n),k(!0);else if(t.match(/^(pdf)$/i)){let e=new Blob([null==o?void 0:o.Body],{type:"application/pdf"}),t=URL.createObjectURL(e);window.open(t,"_blank"),URL.revokeObjectURL(t)}else{(0,g.Am)({description:"File type not supported to view. Please save the file to view.",variant:"destructive"});let t=document.createElement("a");t.target="_blank",t.href=n,t.download=e.title,t.click(),URL.revokeObjectURL(n)}}})};return(0,i.jsx)(m,{files:t,onFilesSelected:e=>{Array.from(e).forEach(S)},text:s,subText:l,bgClass:d,multipleUpload:a,acceptedFileTypes:u,isLoading:v,renderFileItem:(e,t)=>(0,i.jsxs)("div",{onClick:()=>U(e),className:"flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden",children:[(0,i.jsx)(r.default,{src:"/sealogs-document_upload.svg",alt:"Document",width:48,height:48,className:"mb-2"}),(0,i.jsx)("div",{className:"text-xs text-center break-all text-muted-foreground",children:e.title.replace(x+"-","")})]},t),displayFiles:f,onFileClick:U,children:(0,i.jsx)(c.h9,{openDialog:C,setOpenDialog:k,noButton:!0,actionText:"Close",title:"Image Preview",children:(0,i.jsx)("div",{className:"flex items-center justify-center",children:(0,i.jsx)("img",{src:h,alt:"Preview",className:"max-w-full max-h-96 object-contain"})})})})}},74408:function(e,t,o){"use strict";o.d(t,{Z:function(){return y},_:function(){return h}});var i=o(12803),n=o(60333),r=o(53434),a=o.n(r),s=o(10341),c=o(10927),l=o(96927),d=o(53416),u=o(62007),m=o(7132),g=o(31750),p=o(60469),b=o(20820),D=o(79909),f=o(3626).lW;let v=new(a()).S3({endpoint:"https://".concat("ddde1c1cd1aa25641691808dcbafdeb7",".r2.cloudflarestorage.com"),accessKeyId:"06c3e13a539f24e6fdf7075bf381bf5e",secretAccessKey:"****************************************************************",signatureVersion:"v4",region:"auto"});function y(e){var t;let{file:o=!1,setFile:r,inputId:a,buttonType:y="icon",sectionData:h={id:0,sectionName:"logBookEntryID"}}=e,I=null!==(t=(0,u.useSearchParams)().get("logentryID"))&&void 0!==t?t:0,[C,k]=(0,n.useState)(!1),[x,L]=(0,n.useState)(!1),[S,U]=(0,n.useState)(!1),[R,w]=(0,n.useState)(0),[B,j]=(0,n.useState)([]),[E,N]=(0,n.useState)(!1),[M,T]=(0,n.useState)(!1),[A,_]=(0,n.useState)([]),[O,G]=(0,n.useState)([]),Z=(0,D.k)(),P=e=>{if(!e||!e.name){console.error("No file name provided");return}v.getObject({Bucket:"captures",Key:e.name},async(t,o)=>{if(t)console.error(t),e.id&&W({variables:{ids:[+e.id]}});else{if(!e||!e.name){console.error("No file name provided");return}let t=e.name.split(".").pop()||"",n=new Blob([null==o?void 0:o.Body]),r=URL.createObjectURL(n);if(t.match(/^(jpg|jpeg|png|gif|bmp)$/i)){L(r),U(!0);let n=f.from(null==o?void 0:o.Body).toString("base64"),a=new TextDecoder().decode(null==o?void 0:o.Body);var i="data:image/".concat(t,";base64,").concat(n);a.startsWith("�PNG")||(i=a),L(i),void 0===A.find(t=>t.name===e.name)?_(t=>[...t,{...e,imageData:i}]):_(t=>t.map(t=>t.name===e.name?{...t,imageData:i}:t))}else{let t=new TextDecoder().decode(null==o?void 0:o.Body);L(t),void 0===A.find(t=>t.name===e.name)?_(o=>[...o,{...e,imageData:t}]):_(o=>o.map(o=>o.name===e.name?{...o,imageData:t}:o)),U(!0)}}})},F=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(k(!0),T(!1),N(!1),L(null),U(!1),o&&o.length>0&&!e){o.forEach(e=>{P(e)});return}let t=await navigator.mediaDevices.enumerateDevices();if(t.some(e=>"videoinput"===e.kind))j(t.filter(e=>"videoinput"===e.kind));else{(0,c.Am)({description:"No camera found. Please connect a camera.",variant:"destructive"});return}navigator.mediaDevices.getUserMedia({video:{facingMode:"environment"},audio:!1}).then(e=>{let t=document.getElementById("camera-video");t.srcObject=e,t.play()}).catch(e=>{console.error("Error accessing camera:",e)})},Y=()=>{let e=document.getElementById("camera-video");e&&e.srcObject&&(e.srcObject.getTracks().forEach(e=>e.stop()),e.srcObject=null)};(0,n.useEffect)(()=>{var e;w(+(null!==(e=localStorage.getItem("clientId"))&&void 0!==e?e:0))},[]),(0,n.useEffect)(()=>{C||Y()},[C]),(0,n.useEffect)(()=>{O.length>0&&O.forEach(e=>{e.title&&(P({name:e.title,fieldName:a}),T(t=>Array.isArray(t)?[...t,e.title]:[e.title]))})},[O]);let[V]=(0,m.D)(g.fQS,{onCompleted:e=>{e.createSectionMemberImage,r()},onError:e=>{console.error("Error updating comment",e)}});async function z(e){var t;e.imageData&&(t=e.name||R+"-capture-"+Date.now()),t||(t=R+"-capture-"+Date.now()),V({variables:{input:{name:t,fieldName:a,imageType:"FieldImage",[h.sectionName]:"logBookEntryID"===h.sectionName?I:h.id}}}),e.imageData&&v.putObject({Bucket:"captures",Key:t,Body:e.imageData},(e,t)=>{e?console.error(e):r()})}let K=()=>{let e=document.getElementById("camera-video");if(!e){console.error("Video element not found");return}let t=e.srcObject?e.srcObject.getVideoTracks()[0].getSettings().deviceId:null,o=B.find(e=>"videoinput"===e.kind&&e.deviceId!==t);o?navigator.mediaDevices.getUserMedia({video:{deviceId:o.deviceId},audio:!1}).then(t=>{e.srcObject=t,e.play()}).catch(e=>{console.error("Error switching camera:",e)}):(0,c.Am)({description:"No other camera found to switch.",variant:"destructive"})},H=()=>{if(0===A.length){(0,c.Am)({description:"Please capture or upload an image first.",variant:"destructive"});return}(0,c.Am)({description:"Please capture or upload an image first.",variant:"destructive"}),A.forEach(e=>{z(e)}),_([]),G([]),L(null),U(!1),k(!1),Y(),(0,c.Am)({description:"Images uploaded successfully."})},[W]=(0,m.D)(g.l9V,{onCompleted:e=>{e.deleteCaptureImage&&(r(),(0,c.Am)({description:"Image deleted successfully."}))},onError:e=>{console.error("Error deleting image",e),(0,c.Am)({description:"Failed to delete image.",variant:"destructive"})}}),$=e=>{_(t=>t.filter(t=>t.name!==e.name)),e.imageData&&(v.deleteObject({Bucket:"captures",Key:e.name||""},(e,t)=>{e?console.error("Error deleting image:",e):(0,c.Am)({description:"Image deleted successfully."})}),e.id&&W({variables:{ids:[+e.id]}}))};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(s.zx,{variant:"icon"===y?"ghost":"outline",iconOnly:"icon"===y,size:"icon"===y?"icon":"default",title:"Add comment",className:"icon"===y?"group":"",iconLeft:(0,i.jsx)(l.Z,{className:"icon"===y?(0,d.cn)(o&&o.length>0?"text-curious-blue-400 group-hover:text-curious-blue-400/50":"text-neutral-400 group-hover:text-neutral-400/50","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300"):"",size:24}),onClick:()=>F(!1),children:"button"===y&&(0,b.p)(Z.phablet,"Capture / Upload","Capture / Upload Image")}),(0,i.jsxs)(s.h9,{openDialog:C,setOpenDialog:k,title:E?"Files":"Camera",handleCreate:()=>{x?H():(0,c.Am)({description:"Please capture an image first.",variant:"destructive"})},handleCancel:()=>{k(!1),L(null),U(!1),_([]),Y(),G([]),r()},actionText:"Save",cancelText:"Close",loading:!1,children:[(0,i.jsxs)("div",{className:"flex flex-col items-center",children:[A.length>0&&(0,i.jsx)("div",{className:"flex flex-wrap mb-4",children:A.map((e,t)=>(0,i.jsxs)("div",{className:"w-1/4 p-1 rounded-md relative",children:[(0,i.jsx)("img",{src:e.imageData,alt:"Captured ".concat(t),className:"object-cover",onClick:()=>{L(e.imageData),U(!0),Y()}},t),(0,i.jsx)(s.zx,{variant:"destructive",size:"icon",className:"absolute top-1 right-1 p-0 size-5",onClick:()=>{$(e)},children:"\xd7"})]},t))}),E?(0,i.jsx)(p.Z,{files:O,setFiles:G,accept:"image/*",bucketName:"captures",multipleUpload:!0,prefix:I+"-",displayFiles:!1}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("video",{id:"camera-video",style:{display:S?"none":"block"}}),(0,i.jsx)("img",{src:x,alt:"Captured",style:{display:S?"block":"none"}})]})]}),(0,i.jsxs)("div",{className:"flex items-center mt-4 gap-2 justify-between",children:[!S&&!E&&(0,i.jsx)(s.zx,{onClick:()=>{let e=document.getElementById("camera-video");if(!e){console.error("Video element not found");return}let t=document.createElement("canvas");t.width=e.videoWidth,t.height=e.videoHeight;let o=t.getContext("2d");if(!o){console.error("Failed to get canvas context");return}o.drawImage(e,0,0,t.width,t.height);let i=t.toDataURL("image/png");e.srcObject&&(e.srcObject.getTracks().forEach(e=>e.stop()),e.srcObject=null),i&&(L(i),U(!0),_(e=>[...e,{name:R+"-capture-"+Date.now(),imageData:i}]))},className:"mt-2",children:"Capture"}),S&&!E&&(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(s.zx,{onClick:()=>{L(null),U(!1),F(!0)},className:"mt-2",children:"Recapture"})}),B.length>1&&(0,i.jsx)(s.zx,{onClick:()=>{K()},className:"mt-2",children:"Switch Camera"}),E?(0,i.jsx)(s.zx,{onClick:()=>{N(!1),F()},className:"mt-2",children:"Capture Image"}):(0,i.jsx)(s.zx,{onClick:()=>{Y(),N(!0)},className:"mt-2",children:"Upload Image"})]})]})]})}let h=e=>new Promise((t,o)=>{if(!e||!e.name){console.error("No file name provided"),o("No file name provided");return}v.getObject({Bucket:"captures",Key:e.name},async(i,n)=>{if(i)console.error(i),o(i);else{if(!e||!e.name){console.error("No file name provided"),o("No file name provided");return}let i=e.name.split(".").pop()||"",a=new Blob([null==n?void 0:n.Body]);if(URL.createObjectURL(a),i.match(/^(jpg|jpeg|png|gif|bmp)$/i)){let e=f.from(null==n?void 0:n.Body).toString("base64"),o=new TextDecoder().decode(null==n?void 0:n.Body);var r="data:image/".concat(i,";base64,").concat(e);o.startsWith("�PNG")||(r=o),t(r)}else t(new TextDecoder().decode(null==n?void 0:n.Body))}})})},53416:function(e,t,o){"use strict";o.d(t,{cn:function(){return r}});var i=o(52067),n=o(39846);function r(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,n.m6)((0,i.W)(t))}},20820:function(e,t,o){"use strict";o.d(t,{e:function(){return n},p:function(){return r}});var i=o(79909);function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"phablet",t=(0,i.k)();return(o,i)=>t[e]?i:o}function r(e,t,o){return e?o:t}}},function(e){e.O(0,[6061,5290,5722,3213,6059,2444,2551,6972,9511,1098,9444,8115,3265,2447,1121,529,7225,4314,9296,4701,8539,49,6203,341,1750,7788,1243,9011,9396,6953,1744],function(){return e(e.s=67857)}),_N_E=e.O()}]);