"use strict";exports.id=3842,exports.ids=[3842],exports.modules={13842:(e,s,t)=>{t.d(s,{Vp:()=>ei,d:()=>Z,nu:()=>et,oy:()=>eu,Q0:()=>Q,PN:()=>w,yh:()=>V,Fo:()=>F,UL:()=>j,Gr:()=>er,vu:()=>E,E$:()=>ec,Ts:()=>ed,ig:()=>Y,GL:()=>M,oA:()=>W,p9:()=>z,td:()=>D,UE:()=>A,oE:()=>el,bB:()=>U,dY:()=>K,P:()=>J,UU:()=>X,s3:()=>ea,mJ:()=>H,__:()=>O,oS:()=>_,Ox:()=>I,iA:()=>ev,Fb:()=>L,qD:()=>$,MX:()=>x,u$:()=>B,Ck:()=>P,$J:()=>N,nV:()=>G,lY:()=>q,BJ:()=>k,sy:()=>f,AT:()=>es,tt:()=>ee,uC:()=>ep});var l=t(94060),a=t(79418),r=t(60343),i=t(83179),o=t.n(i),n=t(7678),u=t.n(n),d=t(83962),c=t(70413),p=t(2887),v=t(48755),g=t(98318),y=t(73366),T=t(12751),b=t(45519);let h=(0,b.Ps)`
  query GetCrewMembers($logBookEntryIDs: [ID!]) {
    readCrewMembers_LogBookEntrySections(filter: {logBookEntryID: {in: $logBookEntryIDs}}) {
      nodes {
        id
        logBookEntryID
        crewMember {
          firstName
          surname
        }
      }
    }
  }
`;var C=t(83048),m=t.n(C),S=t(88006);let R=new(m()).S3({endpoint:"https://ddde1c1cd1aa25641691808dcbafdeb7.r2.cloudflarestorage.com",accessKeyId:"06c3e13a539f24e6fdf7075bf381bf5e",secretAccessKey:"****************************************************************",signatureVersion:"v4",region:"auto"});async function k(e,s,t=!1){new c.Z;let[i,o]=(0,r.useState)(!0),[n]=(0,a.t)(l.rk,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneVessel;t&&s(t)},onError:e=>{console.error("queryVesselInfo error",e)}})}async function f(e,s=!1){let[t,i]=(0,r.useState)(!0);new c.Z;let[o]=(0,a.t)(l.N5,{fetchPolicy:"cache-and-network",onCompleted:s=>{s.readVessels.nodes&&e(s.readVessels.nodes)},onError:e=>{console.error("queryVessels error",e)}})}async function q(e){let[s,t]=(0,r.useState)(!0),[i]=(0,a.t)(l.NS,{fetchPolicy:"cache-and-network",onCompleted:s=>{s.readVessels.nodes&&e(s.readVessels.nodes)},onError:e=>{console.error("queryVessels error",e)}})}async function E(e,s=0){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.BH,{fetchPolicy:"no-cache",onCompleted:async e=>{e.readDashboardData},onError:e=>{console.error("queryVessels error",e)}}),[n]=(0,a.t)(S.j9,{fetchPolicy:"cache-and-network",onError:e=>{console.error("queryLogBookEntrySections error",e)}})}async function D(e){let[s,t]=(0,r.useState)(!0),[i]=(0,a.t)(l.HV,{fetchPolicy:"cache-and-network",onCompleted:s=>{let t=s.readInventories.nodes;t&&e(t)},onError:e=>{console.error("queryInventoriesEntry error",e)}})}async function w(e,s){let[t]=(0,a.t)(l.gQ,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readComponentMaintenanceCheckList[0].list;t&&s(t)},onError:e=>{console.error("querysetMaintenanceCheckInfo error",e)}})}async function P(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.pm,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTrainingSessions.nodes;t&&s(t)},onError:e=>{console.error("queryTrainingSessions error",e)}})}async function B(e,s){let[t,i]=(0,r.useState)(!0),[o,{loading:n}]=(0,a.t)(l.qX,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTrainingSessionDues.nodes;t&&s(Object.values(t.map(e=>({...e,status:et(e)})).reduce((e,s)=>{let t=`${s.vesselID}-${s.trainingTypeID}-${s.dueDate}`;return e[t]||(e[t]={id:s.id,vesselID:s.vesselID,vessel:s.vessel,trainingTypeID:s.trainingTypeID,trainingType:s.trainingType,dueDate:s.dueDate,status:s.status,members:[]}),e[t].members.push(s.member),e},{})).map(e=>{let s=e.members.reduce((e,s)=>(e.find(e=>e.id===s.id)||e.push(s),e),[]);return{id:e.id,vesselID:e.vesselID,vessel:e.vessel,trainingTypeID:e.trainingTypeID,trainingType:e.trainingType,status:e.status,dueDate:e.dueDate,members:s}}))},onError:e=>{console.error("readTrainingSessionDues error",e)}})}async function M(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.en,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readInventories.nodes;t&&s(t)},onError:e=>{console.error("queryInventories error",e)}})}async function F(e){let[s,t]=(0,r.useState)(!0),[i]=(0,a.t)(l.HW,{fetchPolicy:"cache-and-network",onCompleted:s=>{let t=s.readCrewDuties.nodes;t&&e(t)},onError:e=>{console.error("queryCrewDuty error",e)}})}async function L(e){let[s,t]=(0,r.useState)(!0),[i]=(0,a.t)(l.l1,{fetchPolicy:"cache-and-network",onCompleted:s=>{let t=s.readSuppliers.nodes;t&&e(t)},onError:e=>{console.error("querySupplier error",e)}})}async function A(e,s,t=!1){new v.Z,new g.Z,new y.Z;let[i,o]=(0,r.useState)(!0),n=(0,r.useRef)([]),[d]=(0,a.t)(h,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e?.readCrewMembers_LogBookEntrySections?.nodes??[];s(n.current.map(e=>{let s=t.filter(s=>s.logBookEntryID===e.id);return{...e,crew:s}}))},onError:e=>{console.error("fetchCrewMembers error",e)}}),[c]=(0,a.t)(l.cS,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e?.GetLogBookEntries?.nodes??[];n.current=t;let l=t.map(e=>e.id);u()(l)?s([]):d({variables:{logBookEntryIDs:l}})},onError:e=>{console.error("queryLogBookEntries error",e)}})}async function O(e){new p.Z;let[s,t]=(0,r.useState)(!0),[i]=(0,a.t)(l.mO,{onCompleted:s=>{let t=s.readSectionMemberComments.nodes;t&&e(t)},onError:e=>{console.error(e)}})}async function I(e,s=!1){let[t,i]=(0,r.useState)(!0);new y.Z;let[o]=(0,a.t)(l.Xp,{fetchPolicy:"cache-and-network",onCompleted:s=>{let t=s.readSeaLogsMembers.nodes;t&&e(t)},onError:e=>{console.error("querySeaLogsMembersList error",e)}})}async function _(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.rd,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readSeaLogsMembers.nodes;t&&s(t)},onError:e=>{console.error("querySeaLogsMembers error",e)}})}async function V(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.Rc,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneSeaLogsMember;t&&s(t)},onError:e=>{console.error("querySeaLogsMembers error",e)}})}async function H(e){let[s,t]=(0,r.useState)(!0),[i]=(0,a.t)(l.bA,{fetchPolicy:"cache-and-network",onCompleted:s=>{let t=s.readSeaLogsGroups.nodes;t&&e(t)},onError:e=>{console.error("querySeaLogsGroups error",e)}})}async function x(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.tZ,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneTrainingSession;t&&s(t)},onError:e=>{console.error("queryTrainingSession error",e)}})}async function N(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.e3,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneTrainingType;t&&s(t)},onError:e=>{console.error("queryTrainingType error",e)}})}async function G(e){let[s,t]=(0,r.useState)(!0),[i]=(0,a.t)(l.$0,{fetchPolicy:"cache-and-network",onCompleted:s=>{let t=s.readTrainingTypes.nodes;t&&e(t)},onError:e=>{console.error("queryTrainingTypes error",e)}})}async function W(e){let[s,t]=(0,r.useState)(!0),[i]=(0,a.t)(l.I,{fetchPolicy:"cache-and-network",onCompleted:s=>{let t=s.readInventoryCategories.nodes;t&&e(t)},onError:e=>{console.error("queryInventoryCategory error",e)}})}async function U(e){let[s,t]=(0,r.useState)(!0),[i]=(0,a.t)(l.Eo,{fetchPolicy:"cache-and-network",onCompleted:s=>{let t=s.readMaintenanceCategories.nodes;t&&e(t)},onError:e=>{console.error("queryMaintenanceCategory error",e)}})}async function Z(e,s=!1){let[t,i]=(0,r.useState)(!0);new v.Z;let[o]=(0,a.t)(l._x,{fetchPolicy:"cache-and-network",onCompleted:s=>{let t=s.readLogBookEntries;t&&e(t.nodes.filter(e=>e.vehicle.id>0).flatMap(e=>e.logBookEntrySections.nodes).flatMap(e=>+e.id))},onError:e=>{console.error("queryMemberIds error",e)}})}async function Y(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.gV,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneInventory;t&&s(t)},onError:e=>{console.error("queryInventoryByID error",e)}})}async function $(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.gW,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneSupplier;t&&s(t)},onError:e=>{console.error("querySupplierByID error",e)}})}async function J(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.Gz,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneComponentMaintenanceCheck;t&&s(t)},onError:e=>{console.error("queryMaintenanceCheckByID error",e)}})}async function j(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.qn,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneCrewDuty;t&&s(t)},onError:e=>{console.error("queryCrewDutyByID error",e)}})}async function z(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.zb,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneInventoryCategory;t&&s(t)},onError:e=>{console.error("queryInventoryCategoryByID error",e)}})}async function K(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.au,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneMaintenanceCategory;t&&s(t)},onError:e=>{console.error("queryMaintenanceCategoryByID error",e)}})}async function Q(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.t,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readComponentMaintenanceChecks.nodes;t&&s(t)},onError:e=>{console.error("queryTaskList error",e)}})}async function X(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.AJ,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readMaintenanceCheckSubTasks.nodes;t&&s(t)},onError:e=>{console.error("queryMaintenanceCheckSubTask error",e)}})}let ee=(e,s=!1)=>{let t=e?.maintenanceSchedule.__typename==="ComponentMaintenanceSchedule"&&e?.maintenanceSchedule;if(t){let l=t.occursEveryType?t.occursEveryType:"Days";if("Hours"===l||"Uses"===l)return"Uses"===l?e.equipmentUsagesAtCheck+" Equipment Uses":new Date(e.dutyHoursAtCheck).toLocaleDateString();{let a=t.occursEvery?t.occursEvery:1,r=o()(e?.dateCompleted?new Date(e.dateCompleted):new Date).startOf("day").add(a,l);return s?r.format("YYYY-MM-DD"):r.format("DD/MM/YYYY")}}return e.expires?s?o()(e.expires).format("YYYY-MM-DD"):new Date(e.expires).toLocaleDateString():"NA"},es=e=>{if(!e?.expires&&!e?.completed&&!e?.maintenanceSchedule?.occursEveryType)return{status:"Medium",days:"Open",ignore:!0};if(!e?.expires&&e?.maintenanceSchedule?.occursEveryType!=="Hours")return{status:"Completed",days:""};if("Completed"===e.status)return{status:"Completed",days:"Completed on "+new Date(e?.dateCompleted?e?.dateCompleted:e.completed).toLocaleDateString()};if("Save_As_Draft"===e.status)return{status:"Completed",days:e.status.replaceAll("_"," ")};let s=e?.maintenanceSchedule.__typename==="ComponentMaintenanceSchedule"&&e?.maintenanceSchedule,t=new Date,l=e?.expires?Math.ceil((new Date(e.expires).getTime()-t.getTime())/864e5):0;if(e?.maintenanceSchedule?.occursEveryType==="Hours"){let s=Math.min(...e.maintenanceSchedule.engineUsage.nodes.filter(e=>!0===e.isScheduled||e.lastScheduleHours>0).map(s=>s.lastScheduleHours+e.maintenanceSchedule.occursEvery-s.engine.currentHours));return s<3?{status:"High",days:s<0?"Overdue by "+-1*s+" engine hours":"Due - "+s+" engine hours"}:s<5?{status:"Medium",days:s<0?"Overdue by "+-1*s+" engine hours":"Due - "+s+" engine hours"}:s<7?{status:"Low",days:s<0?"Overdue by "+-1*s+" engine hours":"Due - "+s+" engine hours"}:{status:"Upcoming",days:"Due - "+s+" engine hours"}}if(s){let a=s.occursEveryType?s.occursEveryType:"Days",r="Months"===a?3:1,i="Months"===a?7:3,o="Months"===a?14:7,n="Days"===a?s.occursEvery:"Months"===a?30*s.occursEvery:"Weeks"===a?7*s.occursEvery:0,u=new Date(new Date(e.startDate).setDate(new Date(e.startDate).getDate()+n)),d=Math.ceil((u.getTime()-t.getTime())/864e5);if("Days"===a||"Months"===a||"Weeks"===a)return u<new Date(new Date().setDate(t.getDate()+r))?{status:"High",days:d<0?-1*d+" days ago":"Due - "+d+" days"}:u<new Date(new Date().setDate(t.getDate()+i))?{status:"Medium",days:d<0?-1*d+" days ago":"Due - "+d+" days"}:u<new Date(new Date().setDate(t.getDate()+o))?{status:"Low",days:d<0?-1*d+" days ago":"Due - "+d+" days"}:{status:"Upcoming",days:"Due - "+d+" days"};if(new Date(e.expires)<new Date(new Date().setDate(t.getDate()+r)))return{status:"High",days:l<0?-1*l+" days ago":"Due - "+l+" days"};if(new Date(e.expires)<new Date(new Date().setDate(t.getDate()+i)))return{status:"Medium",days:l<0?-1*l+" days ago":"Due - "+l+" days"};if(new Date(e.expires)<new Date(new Date().setDate(t.getDate()+o)))return{status:"Low",days:l<0?-1*l+" days ago":"Due - "+l+" days"}}else{if(new Date(e.expires)<new Date(new Date().setDate(t.getDate()+1)))return{status:"High",days:l<0?-1*l+" days ago":"Due - "+l+" days"};if(new Date(e.expires)<new Date(new Date().setDate(t.getDate()+3)))return{status:"Medium",days:l<0?-1*l+" days ago":"Due - "+l+" days"};if(new Date(e.expires)<new Date(new Date().setDate(t.getDate()+7)))return{status:"Low",days:l<0?-1*l+" days ago":"Due - "+l+" days"}}return{status:"Upcoming",days:"Due - "+l+" days"}},et=e=>{let s={low:"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center",high:"alert"},t=e.dueDate,l=new Date,a=Math.ceil((new Date(t).getTime()-l.getTime())/864e5);return{class:a<0?s.high:s.low,label:a<0?-1*a+" days ago":"Due - "+a+" days",isOverdue:a<0,dueWithinSevenDays:!(a<0)&&a<=7}};async function el(e,s){new v.Z;let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.MI,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneLogBookEntry;t&&s(t)},onError:e=>{console.error("queryLogBookEntry error",e)}})}async function ea(e,s=!1){let[t,i]=(0,r.useState)(!0);new T.Z;let[o]=(0,a.t)(l.SL,{fetchPolicy:"cache-and-network",onCompleted:s=>{let t=s.readOneClient;t&&e(t)},onError:e=>{console.error("queryClientByID error",e)}})}async function er(e,s){let[t,i]=(0,r.useState)(!0),[o]=(0,a.t)(l.Y,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readCrewMembers_LogBookEntrySections.nodes;t&&s(t)},onError:e=>{console.error("queryLogBookEntrySections error",e)}})}let ei=(e,s)=>{let t=1===s.length?+s[0].id:0;return e.map(e=>e.trainingSessionsDue&&e.trainingSessionsDue.nodes?{...e,trainingSessionsDue:{...e.trainingSessionsDue,nodes:e.trainingSessionsDue.nodes.filter(e=>!u()(e.dueDate)).map(e=>({...e,status:et(e)}))}}:e).map(e=>{if(e.trainingSessionsDue&&e.trainingSessionsDue.nodes){let s=e.trainingSessionsDue.nodes.filter(e=>t>0?!u()(e.dueDate)&&+e.vesselID==+t:!u()(e.dueDate)),l=s.map(e=>({...e,status:et(e)})),a={label:"Good",dues:[]};if(0===s.length)a={label:"Good",dues:[]};else{let e=s.filter(e=>e.status.isOverdue),t=s.filter(e=>e.status.dueWithinSevenDays);a=e.length>0&&t.length>0?{label:"Good",dues:[...e,...t]}:e.length>0?{label:"Overdue",dues:e}:t.length>0?{label:" ",dues:t}:{label:"Good",dues:[]}}let r=Object.values(a.dues.reduce((e,s)=>{let t=s.trainingTypeID;return e[t]||(e[t]=s),e},{}));return{...e,trainingSessionsDue:{...e.trainingSessionsDue,nodes:l},trainingStatus:{...a,dues:r}}}return{...e,trainingStatus:{label:"Good",dues:[]}}})},eo=e=>e.map(e=>{let s={label:"Good",tasks:[]};if(e.componentMaintenanceChecks&&e.componentMaintenanceChecks.nodes){let t=new Date,l=[],a=[];e.componentMaintenanceChecks.nodes.forEach(e=>{let s=Math.ceil((new Date(e.expires).getTime()-t.getTime())/864e5);s<0&&l.push({...e,isOverdue:es(e)}),!(s<0)&&s<=7&&a.push({...e,isOverdue:es(e)})}),l.length>0&&(s={label:"Overdue",tasks:l}),a.length>0&&(s={label:"Upcoming",tasks:a})}return{...e,taskStatus:s}}),en=e=>e.map(e=>{if(e.trainingSessionsDue&&e.trainingSessionsDue.nodes){let s=+e.id,t=e.trainingSessionsDue.nodes.filter(e=>s>0?!u()(e.dueDate)&&+e.vesselID==+s:!u()(e.dueDate)),l=t.map(e=>({...e,status:et(e)})),a={label:"Good",dues:[]};0===t.length?a={label:"Good",dues:[]}:t.some(e=>e.status.dueWithinSevenDays)?a={label:"Upcoming",dues:t.filter(e=>e.status.dueWithinSevenDays)}:t.some(e=>e.status.isOverdue)&&(a={label:"Overdue",dues:t.filter(e=>e.status.isOverdue)});let r=Object.values(a.dues.reduce((e,s)=>{let t=s.trainingTypeID;return e[t]||(e[t]=s),e},{}));return{...e,trainingSessionsDue:{...e.trainingSessionsDue,nodes:l},trainingStatus:{...a,dues:r}}}return{...e,trainingStatus:{label:"Good",dues:[]}}}),eu=e=>eo(en(e.map(e=>e.trainingSessionsDue&&e.trainingSessionsDue.nodes?{...e,trainingSessionsDue:{...e.trainingSessionsDue,nodes:e.trainingSessionsDue.nodes.filter(e=>!u()(e.dueDate)).map(e=>({...e,status:et(e)}))}}:e))),ed=e=>{if(!e?.__typename||"CustomisedComponentField"!==e.__typename)return e?.title?e.title:e.value;{let t=d.FV.map(e=>e);var s=e.fieldName;return t.forEach(t=>{t.items.forEach(t=>{e.fieldName===t.value&&(s=t?.title?t?.title:e.fieldName)})}),s}};async function ec(e){let[s,t]=(0,r.useState)(!0),[i]=(0,a.t)(l.d5,{fetchPolicy:"cache-and-network",onCompleted:s=>{let t=s.readDepartments.nodes;t&&e(t)},onError:e=>{console.error("queryDepartmentList error",e)}})}let ep=e=>{let[s,t]=(0,r.useState)(!0);(0,r.useEffect)(()=>{s&&(o(),t(!1))},[s]);let[i]=(0,a.t)(l.N5,{fetchPolicy:"cache-and-network",onCompleted:s=>{s.readVessels.nodes&&e(s.readVessels.nodes.filter(e=>"Rescue_Vessel"===e.vesselType).length>0)},onError:e=>{console.error("queryVessels error",e)}}),o=async()=>{await i({variables:{filter:{archived:{eq:!1}}}})}},ev=async(e,s=null)=>{let t="";if(!e||e<=0)return console.warn(`Invalid signature ID: ${e}`),t;try{let l=await R.getObject({Bucket:"signature",Key:"signature/"+e+".png"}).promise();if(l.Body){let e=Buffer.from(l.Body).toString("base64"),a=`data:image/png;base64,${e}`;t=a,s&&s(a)}}catch(s){console.error(`Error fetching signature ${e}:`,s)}return t}},63043:(e,s,t)=>{t.d(s,{p:()=>a});var l=t(45519);let a=(0,l.ZP)`
query Maintenance_Status_Activity_Report(
    $limit: Int = 1000
    $filter: ComponentMaintenanceCheckFilterFields = {}
) {
    readComponentMaintenanceChecks(
        limit: $limit
        filter: $filter
    ) {
    nodes {
      id
      workOrderNumber
      groupItemTo
      projected
      actual
      difference
      name
      startDate
      documents {
          nodes {
              id
          }
      }
      maintenanceCategoryID
      maintenanceCategory {
          id
          name
          abbreviation
      }
      completedBy {
          id
          firstName
          surname
      }
      dateCompleted
      completed
      expires
      dutyHoursAtCheck
      equipmentUsagesAtCheck
      comments
      severity
      status
      archived
      assignees {
          nodes {
              id
          }
      }
      maintenanceSchedule {
          id
          title
          description
          type
          occursEveryType
          occursEvery
          warnWithinType
          highWarnWithin
          mediumWarnWithin
          lowWarnWithin
          groupTo
          maintenanceChecks {
              nodes {
                  id
              }
          }
          engineUsage {
              nodes {
                  id
                  lastScheduleHours
                  isScheduled
                  engine {
                      id
                      title
                      currentHours
                  }
              }
          }
          inventoryID
          clientID
      }
      basicComponentID
      basicComponent {
          id
          title
      }
      assignedToID
      assignedTo{
        id
        firstName
        surname
      }
      assignedByID
      inventoryID
      inventory{
        id
        title
      }
      maintenanceScheduleID
      maintenanceCheck_Signature {
          id
      }
      clientID
      recurringID
    }
  }
}
`},88006:(e,s,t)=>{t.d(s,{HP:()=>a,eM:()=>r,mc:()=>i,QT:()=>o,SD:()=>n,BA:()=>u,FG:()=>d,j9:()=>p,p1:()=>c});var l=t(45519);let a=(0,l.ZP)`
  query Arrival_Departure_Report($filter: LogBookEntryFilterFields = {}){
    readLogBookEntries(sort: {
      startDate: ASC
    }, filter: $filter, limit: 1000){
    nodes{
      id
      startDate
      endDate
      lockedDate
      state
      vehicleID
      created
      vehicle{
        id
        title
      }
      logBookEntrySections{
        nodes{
          id
          tripEvents(filter: {
            eventCategory: {
              eq: PassengerDropFacility
            }
          }){
            nodes{
              id
              eventCategory
              eventType_PassengerDropFacilityID
              location
              start
              end
              eventType_PassengerDropFacility{
                id
                title
                created
                time
                paxOn
                paxOff
                lat
                long
                geoLocation{
                  id
                  lat
                  long
                  title
                }
              }
            }
          }
        }
      }
    }
  }
}
`,r=(0,l.ZP)`
query Bar_Crossing_Event_Report($filter: LogBookEntryFilterFields = {}){
  readLogBookEntries(
    sort: {
      startDate: ASC
    }, filter: $filter, limit: 1000
  ){
    nodes{
      id
      startDate
      endDate
      lockedDate
      state
      vehicleID
      created
      vehicle{
        id
        title
      }
      logBookEntrySections{
        nodes{
          id
          tripEvents(filter: {
            eventCategory: {
              eq: BarCrossing
            }
          }){
            nodes{
              id
              created
              notes
              eventType_BarCrossing{
                id
                created
                time
                timeCompleted
                report
                stopAssessPlan
                crewBriefing
                weather
                stability
                waterTightness
                lifeJackets
                lookoutPosted
                barCrossingChecklist{
                  id
                  stopAssessPlan
                  crewBriefing
                  weather
                  stability
                  waterTightness
                  lifeJackets
                  lookoutPosted
                }
                geoLocation{
                  id
                  title
                  lat
                  long
                }
                geoLocationCompleted{
                  id
                  title
                  lat
                  long
                }
              }
            }
          }
        }
      }
    }
  }
}
`,i=(0,l.ZP)`
query Simple_Fuel_Report($filter: LogBookEntryFilterFields = {}){
    readLogBookEntries(sort: {
      startDate: ASC
    }, filter: $filter, limit: 1000){
      nodes{
        id
        startDate
        endDate
        lockedDate
        vehicleID
        archived
        signOffTimestamp
        state
        created
        vehicle{
          id
          clientID
          title
        }
        engineStartStop{
          nodes{
            id
            timeStart
            timeEnd
            hoursStart
            hoursEnd
            totalHours
            created
            engine{
              id
              title
            }
          }
        }
      }
    }
  }
`,o=(0,l.ZP)`
query Infringement_Notices_Report($filter: LogBookEntryFilterFields = {}){
    readLogBookEntries(sort: {
      startDate: ASC
    }, filter: $filter, limit: 1000){
    nodes{
      id
      startDate
      endDate
      lockedDate
      state
      vehicleID
      created
      vehicle{
        id
        title
      	registration
      }
      logBookEntrySections{
        nodes{
          id
          tripEvents(filter: {
            eventCategory: {
              eq: InfringementNotice
            }
          }){
            nodes{
              id
              eventCategory
              created
              start
              end
              lastEdited
              infringementNoticeID
              notes
              infringementNotice{
                id
                created
                time
                vesselType
                vesselName
                vesselReg
                ownerFullName
                ownerDOB
                ownerOccupation
                ownerAddress
                ownerPhone
                ownerEmail
                infringementData
                otherDescription
                geoLocation{
                  id
                  title
                  lat
                  long
                }
              }
            }
          }
        }
      }
    }
  }
}
`;t(63043);let n=(0,l.ZP)`
  query Refueling_Bunkering_Report($filter: LogBookEntryFilterFields = {}){
    readLogBookEntries(sort: {
      startDate: ASC
    }, filter: $filter, limit: 1000){
    nodes{
      id
      startDate
      endDate
      lockedDate
      state
      vehicleID
      created
      vehicle{
        id
        title
      }
      logBookEntrySections{
        nodes{
          id
          sectionMemberComments(filter:{
              fieldName: {
                eq: "DailyCheckFuel"
              },
            }){
              nodes {
                  id
                  commentType
                  fieldName
                  comment
                  logBookEntrySectionID
                  hideComment
              }
          }
                    tripEvents(filter: {
            eventCategory: {
              eq: RefuellingBunkering
            }
          }){
            nodes{
              id
              eventCategory
              eventType_PassengerDropFacilityID
              location
              start
              end
             	eventType_RefuellingBunkering{
                id
                geoLocation{
                  id
                  title
                  lat
                  long
                }
                fuelLog{
                  nodes{
                    id
                    created
                    date
                    fuelAdded
                    fuelBefore
                    fuelAfter
                    fuelTank{
                      id
                      title
                    }
                  }
                }
                title
                date
              }
            }
          }
        }
      }
    }
  }
}
`,u=(0,l.ZP)`
query Restricted_Visbility_Event_Report($filter: LogBookEntryFilterFields = {}){
  readLogBookEntries(
    sort: {
      startDate: ASC
    }, filter: $filter, limit: 1000
  ){
    nodes{
      id
      startDate
      endDate
      lockedDate
      state
      vehicleID
      created
      vehicle{
        id
        title
      }
      logBookEntrySections{
        nodes{
          id
          tripEvents(filter: {
            eventCategory: {
              eq: RestrictedVisibility
            }
          }){
            nodes{
              id
              eventCategory
              created
              start
              end
              lastEdited
              eventType_RestrictedVisibility{
                id
                created
                crossingTime
                startLocation{
                  id
                  title
                  lat
                  long
                }
                crossedTime
                endLocation{
                  id
                  title
                  lat 
                  long
                }
                estSafeSpeed
                approxSafeSpeed
                crewBriefing
                navLights
                soundSignals
                lookout
                soundSignal
                soundSignals
                radarWatch
                radioWatch
                report
              }
            }
          }
        }
      }
    }
  }
}
`,d=(0,l.ZP)`
  query Simple_Fuel_Report($filter: LogBookEntryFilterFields = {}){
    readLogBookEntries(sort: {
      startDate: ASC
    }, filter: $filter, limit: 1000){
      nodes{
        id
        startDate
        endDate
        lockedDate
        vehicleID
        archived
        signOffTimestamp
        state
        created
        vehicle{
          id
          clientID
          title
        }
        fuelLog(sort: {
          created: ASC
        }){
          nodes{
            id,
            fuelTankID
            fuelTank{
              id
              title
            }
            fuelTankID
            fuelBefore
            fuelAdded
            fuelAfter
            created
            date
          }
        }
        logBookEntrySections(
          sort: {
            created: ASC
          }
        ){
          nodes{
            id
            archived
            sortOrder
            logBookEntryID
            created
            sectionMemberComments(filter:{
              fieldName: {
                eq: "DailyCheckFuel"
              },
            }){
              nodes {
                  id
                  commentType
                  fieldName
                  comment
                  logBookEntrySectionID
                  hideComment
              }
            }
            tripEvents(sort: {
              created: ASC
            }){
              nodes{
                id
                eventCategory
                created
                eventType_RefuellingBunkering{
                  id
                  fuelLog{
                    nodes{
                      id,
                      fuelTankID
                      fuelTank{
                        id
                        title
                      }
                      fuelTankID
                      fuelBefore
                      fuelAdded
                      fuelAfter
                      created
                      date
                    }
                  }
                }
                eventType_PassengerDropFacility{
                  id
                  fuelLog{
                    nodes{
                      id,
                      fuelTankID
                      fuelTank{
                        id
                        title
                      }
                      fuelTankID
                      fuelBefore
                      fuelAdded
                      fuelAfter
                      created
                      date
                    }
                  }
                }
                eventType_Tasking{
                  id
                  fuelLog{
                    nodes{
                      id,
                      fuelTankID
                      fuelTank{
                        id
                        title
                      }
                      fuelTankID
                      fuelBefore
                      fuelAdded
                      fuelAfter
                      created
                      date
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
`,c=(0,l.ZP)`
  query GET_VESSELS_WITH_STATUS_HISTORY($vesselFilter: VesselFilterFields = {}, $statusFilter: VesselStatusFilterFields = {}){
    readVessels(filter: $vesselFilter){
      nodes{
        id
        title
        archived
        statusHistory(
          filter: $statusFilter,
          sort:{
            date: ASC
          }
        ){
          nodes{
            id
            date
            status
            created
            comment
            reason
            otherReason
            expectedReturn
          }
        }
      }
    }
  }
`,p=(0,l.ZP)`
  query GET_VESSELS_WITH_LATEST_STATUS($vesselFilter: VesselFilterFields = {}, $statusFilter: VesselStatusFilterFields = {}){
    readVessels(filter: $vesselFilter){
      nodes{
        id
        title
        archived
        statusHistory(
          filter: $statusFilter,
          sort:{
            date: DESC
            created: DESC
          },
          limit: 1
        ){
          nodes{
            id
            date
            status
            created
            comment
            reason
            otherReason
            expectedReturn
          }
        }
      }
    }
  }
`},83962:(e,s,t)=>{t.d(s,{HN:()=>a,FV:()=>l});let l=[{label:"Crew Members",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"Crew",sortOrder:1,componentClass:"CrewMembers_LogBookComponent",items:[{value:"CrewMemberID",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew member",status:"Required"},{value:"DutyPerformedID",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Duty performed",status:"Required"},{value:"PunchIn",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sign in",status:"Required"},{value:"PunchOut",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sign out",status:"Required"},{value:"WorkDetails",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Work details",status:"Required"}]},{label:"Engine Log",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"Engine Room",componentClass:"Engine_LogBookComponent",items:[{value:"HoursStart",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Hours start",status:"Required"},{value:"HoursEnd",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Hours end",status:"Required"},{value:"HoursRun",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Hours run",status:"Required"},{value:"TotalHours",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Total hours",status:"Required"},{value:"FuelTankStartStops",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel records",status:"Required"},{value:"NauticalMiles",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Nautical miles",status:"Required"},{value:"FuelStart",label:"Fuel start",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"FuelAdded",label:"Fuel added",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"FuelUsed",label:"Fuel used",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"FuelTemp",label:"Fuel tempperature",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"FuelPressure",label:"Fuel pressure",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"FuelDiffPressure",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel diff pressure",status:"Required"},{value:"FuelRate",label:"Fuel rate",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"FuelDayTankLevel",label:"Fuel day tank level",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"HeaderTankLevel",label:"Header tank level",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"EngineFluidRecords",label:"Engine fluid records",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"SewageDisposalRecords",label:"Sewage disposal records",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"RPM",label:"RPM",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"Boost",label:"Boost",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"ManifoldTemp",label:"Manifold temp",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"GenSetTemp",label:"Generator tempetature",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"CoolantTemp",label:"Coolant temperature",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"CoolantLevelOK",label:"Coolant level OK",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"ThrustBearingTemp",label:"Thrust bearing temperature",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"ShaftBearingTemp",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Shaft bearing temperature",status:"Required"},{value:"OilLevelOK",label:"Oil level OK",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"OilPressure",label:"Oil pressure",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"LubeOilLevelOK",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Lube oil level OK",status:"Required"},{value:"LubeOilTemp",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Lube oil temperature",status:"Required"},{value:"LubeOilPressure",label:"Lube oil pressure",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"LubeFilterPressure",label:"Lube filter pressure",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"Volts",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Volts",status:"Required"},{value:"KWLoad",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Kilowatt load",status:"Required"},{value:"OverboardPressure",label:"Overboard pressure",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"OverboardDischarge",label:"Overboard discharge",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"Comments",label:"Comments",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"Signature",label:"Signature",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"}]},{label:"Engineering Details",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"Engine Room",componentClass:"Engineer_LogBookComponent",items:[{value:"Date",label:"Date",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"EngineerComments",label:"Comments",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"EngineerWorkOrders",label:"Work orders",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"VehicleDutySessions",label:"Vessel duty session",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"SMURecords",label:"SMU meter readings",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"ManifoldTemp",label:"Manifold temperature",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"GenSetTemp",label:"Generator temperature",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"CoolantTemp",label:"Coolant temperature",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"FuelTemp",label:"Fuel temperature",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"ThrustBearingTemp",label:"Thrust bearing temperature",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"ShaftBearingTemp",label:"Shaft bearing temperature",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"OilPressure",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Oil pressure",status:"Required"},{value:"FuelPressure",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel pressure",status:"Required"},{value:"FuelRate",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel rate",status:"Required"},{value:"Volts",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Volts",status:"Required"},{value:"KWLoad",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Kilowatt load",status:"Required"},{value:"OverboardPressure",label:"Overboard pressure",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"OverboardDischarge",label:"Overboard discharge",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"Pyros",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Pyros",status:"Required"},{value:"Boost",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Boost",status:"Required"},{value:"WaterTemp",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Water temperature",status:"Required"},{value:"AirTemp",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Air temperature",status:"Required"},{value:"RPM",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"RPM",status:"Required"},{value:"Rack",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Rack",status:"Required"},{value:"GenSetOP",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Genset O/P",status:"Required"},{value:"GenSetWT",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Genset W/T",status:"Required"},{value:"GearboxOP",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Gearbox O/P",status:"Required"},{value:"GearboxCLOP",label:"Gearbox C/L/O",vesselType:[0,1,2,3,4,5,6,7,8,9],status:"Required"},{value:"GearboxOT",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Gearbox O/T",status:"Required"},{value:"HRPOP",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"HRP O/P",status:"Required"},{value:"SeaLogsMemberID",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engineer",status:"Required"}]},{label:"Fuel",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"Engine Room",componentClass:"Fuel_LogBookComponent",items:[{value:"HazardousSubstanceRecords",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Hazardous substances",status:"Required"},{value:"FuelTankStartStops",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel records",status:"Required"},{value:"EngineFluidRecords",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine fluid records",status:"Required"},{value:"SewageDisposalRecords",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sewage disposal records",status:"Required"},{value:"FuelTankID",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel tank",status:"Required"},{value:"Start",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Start level",status:"Required"},{value:"End",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"End level",status:"Required"},{value:"Added",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel added",status:"Required"}]},{label:"Shipping Logs",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"Voyage",componentClass:"Ports_LogBookComponent",items:[{value:"LetGoTieUp",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Let Go - Tie Up",status:"Required"},{value:"ChecksCompleted",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Checks completed in accordance to vessel",status:"Required"},{value:"PilotTransfers",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Pilot transfers",status:"Required"},{value:"ShipTugs",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Tug logs",status:"Required"},{value:"OtherActivities",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Other activity",status:"Required"},{value:"PPBTransfers",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"PPB transfers",status:"Required"},{value:"MasterID",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Master",status:"Required"},{value:"TripCrewList",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew list",status:"Required"},{value:"SafetyKayakers",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engineers",status:"Required"},{value:"FuelUsed",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel used",status:"Required"},{value:"Supernumerary",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Supernumerary",status:"Required"},{value:"Comments",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Comments",status:"Required"},{value:"Signature",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Signature initial",status:"Required"}]},{label:"Supernumerary",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"Crew",sortOrder:2,componentClass:"Supernumerary_LogBookComponent",items:[{value:"FirstName",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"First name",status:"Required"},{value:"Surname",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Surname",status:"Required"},{value:"Signature",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Signature",status:"Required"},{value:"Policies",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Policies",status:"Required",fieldType:"files"}]},{label:"Trip Log",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"Voyage",componentClass:"TripReport_LogBookComponent",items:[{value:"VehicleFerryFields",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Passenger/vehicle ferries and water taxis",status:"Required",noCheck:!0},{value:"DepartFrom",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Depart from",status:"Required"},{value:"DepartTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Depart time",status:"Required"},{value:"Depart",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Depart",status:"Required"},{value:"From",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Depart location",status:"Required"},{value:"ExpectedNextContact",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Next expected contact time",status:"Required"},{value:"DepartArrive",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Depart - arrive",status:"Required"},{value:"PositionLogs",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Position logs",status:"Required"},{value:"DailyChecksCompleted",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Checks completed in accordance to vessel",status:"Required"},{value:"PaxJoinedBreakDown",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Pax joined",status:"Required",groupTo:"VehicleFerryFields"},{value:"PaxJoinedAdult",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Adult",status:"Required",groupTo:"VehicleFerryFields"},{value:"PaxJoinedChild",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Child",status:"Required",groupTo:"VehicleFerryFields"},{value:"PaxJoinedYouth",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Youth",status:"Required",groupTo:"VehicleFerryFields"},{value:"PaxJoinedVoucher",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Voucher",status:"Required"},{value:"PaxJoinedFOC",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"F.O.C.",status:"Required",groupTo:"VehicleFerryFields"},{value:"PaxJoinedStaff",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Staff",status:"Required",groupTo:"VehicleFerryFields"},{value:"PaxJoinedPrePaid",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Prepaid",status:"Required"},{value:"PaxDeparted",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Pax departed",status:"Required"},{value:"NumberPax",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Total pax carried",status:"Required"},{value:"VoucherNumber",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Voucher number",status:"Required"},{value:"SafetyBriefing",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Safety briefing",status:"Required"},{value:"SpeedExemption",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Speed exemption used",status:"Required"},{value:"SpeedExemptionCorridorID",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Corridor",status:"Required"},{value:"SpeedExemptionReasonID",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Reason",status:"Required"},{value:"VehiclesJoined",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Vehicles joined",status:"Required",groupTo:"VehicleFerryFields"},{value:"VehiclesDeparted",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Vehicles departed",status:"Required",groupTo:"VehicleFerryFields"},{value:"TotalVehiclesCarried",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Total vehicles carried",status:"Required",groupTo:"VehicleFerryFields"},{value:"VOB",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Vehicles onboard",status:"Required",groupTo:"VehicleFerryFields"},{value:"TripReport_Stops",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Trip stops",status:"Required"},{value:"VehicleDrivers",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Vehicle drivers",status:"Required"},{value:"TripEvents",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Trip events",status:"Required"},{value:"IncidentReports",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Incident report",status:"Required"},{value:"HazardReports",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Hazard report",status:"Required"},{value:"MasterID",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Master",status:"Required"},{value:"LeadGuideID",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Lead guide",status:"Required"},{value:"TripCrewList",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew list",status:"Required"},{value:"SafetyKayakers",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Safety kayakers",status:"Required"},{value:"ArriveTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Arrival time",status:"Required"},{value:"Arrive",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Arrive",status:"Required"},{value:"To",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Destination",status:"Required"},{value:"ArriveTo",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Arrived to",status:"Required"},{value:"ToLat",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Latitude",status:"Required"},{value:"ToLong",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Longitude",status:"Required"},{value:"Comments",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Comments",status:"Required"},{value:"Signature",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Signature initial",status:"Required"},{value:"POB",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Passengers onboard",status:"Required"},{value:"RadioLog",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Radio log",status:"Required",hasDynamicChildren:!0}]},{label:"Pre-Departure Checks",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"Voyage",sortOrder:3,componentClass:"VesselDailyCheck_LogBookComponent",subCategory:!0,items:[{value:"SafetyCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",fieldSet:"Safety Checks"},{value:"CheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Check time",status:"Required",groupTo:"SafetyCrewChecker"},{value:"BilgeLevels",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Bilge levels",status:"Required",fieldSet:"Checks"},{value:"BilgePumps",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Bilge pumps",status:"Required",fieldSet:"Checks"},{value:"Hull",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Hull and superstructure",status:"Required",fieldSet:"Checks"},{value:"NavEquipment",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Navigation equipment",status:"Required",fieldSet:"Checks"},{value:"OilAndWater",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Machinery oil and water",status:"Required",fieldSet:"Engine Checks",groupTo:"MainEngineChecks"},{value:"EngineRoomChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine room checks",status:"Required",fieldSet:"Engine Checks",groupTo:"EngineRoomVisualInspection"},{value:"SafetyEquipment",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Safety equipment",status:"Required",fieldSet:"Safety Checks"},{value:"DriveShafts",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Drive shafts",status:"Required",fieldSet:"Engine Checks",groupTo:"DriveShaftsChecks"},{value:"DriveShaftsChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Drive shaft checks",status:"Required",fieldSet:"Engine Checks",tab:"Other engine checks"},{value:"GearBox",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Gear box",status:"Required",fieldSet:"Engine Checks",groupTo:"DriveShaftsChecks"},{value:"Propeller",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Propeller",status:"Required",fieldSet:"Engine Checks",groupTo:"DriveShaftsChecks"},{value:"Skeg",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Skeg",status:"Required",fieldSet:"Engine Checks",groupTo:"DriveShaftsChecks"},{value:"ChecksWithManual",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Checks in accordance with the manual",status:"Required",fieldSet:"Checks"},{value:"BeltsHosesClamps",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Belts, hoses, clamps and fittings",status:"Required",fieldSet:"Engine Checks",groupTo:"OtherEngineFields"},{value:"Cabin",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Cabin",status:"Required",fieldSet:"Checks"},{value:"Floor",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Floor",status:"Required",fieldSet:"Checks"},{value:"EngineMountsAndStabilisers",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine mounts and stabilisers",status:"Required",fieldSet:"Engine Checks",tab:"Pre-startup checks"},{value:"EngineMounts",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine mounts",status:"Required",fieldSet:"Engine Checks",groupTo:"EngineMountsAndStabilisers"},{value:"Stabilizers",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Stabilizers",status:"Required",fieldSet:"Engine Checks",groupTo:"EngineMountsAndStabilisers"},{value:"EngineTellTale",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine telltale signs",status:"Required",fieldSet:"Engine Checks",groupTo:"MainEngineChecks"},{value:"EngineIsFit",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine controls tested",status:"Required",fieldSet:"Engine Checks",groupTo:"MainEngineChecks"},{value:"SteeringFluid",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Steering fluid",status:"Required",fieldSet:"Engine Checks",groupTo:"SteeringChecks"},{value:"SteeringRams",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Steering rams",status:"Required",fieldSet:"Engine Checks",groupTo:"SteeringChecks"},{value:"SteeringIsFit",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Steering checked",status:"Required",fieldSet:"Engine Checks",groupTo:"OtherEngineFields"},{value:"OperationalTestsOfHelm",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Operational tests of helm",status:"Required",fieldSet:"Engine Checks",groupTo:"OtherEngineFields"},{value:"SteeringHydraulicSystems",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Steering hydraulic systems",status:"Required",fieldSet:"Engine Checks",groupTo:"SteeringChecks"},{value:"SteeringTillers",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Steering tiller",status:"Required",fieldSet:"Engine Checks",groupTo:"OtherEngineFields"},{value:"SteeringHoses",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Steering hoses",status:"Required",fieldSet:"Engine Checks",groupTo:"SteeringChecks"},{value:"SteeringRudders",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Steering rudders",status:"Required",fieldSet:"Engine Checks",groupTo:"SteeringChecks"},{value:"EPIRB",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"EPIRB",status:"Required",fieldSet:"Safety Checks",groupTo:"SafetyEquipment"},{value:"FirstAid",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"First aid",status:"Required",fieldSet:"Safety Checks",groupTo:"SafetyEquipment"},{value:"LifeJackets",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Life jackets",status:"Required",fieldSet:"Safety Checks",groupTo:"SafetyEquipment"},{value:"FireExtinguisher",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fire essentials",status:"Required",fieldSet:"Safety Checks"},{value:"PersonOverboardRescueEquipment",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Person overboard rescue equipment",status:"Required",fieldSet:"Safety Checks",groupTo:"SafetyEquipment"},{value:"SmokeDetectors",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Smoke detectors",status:"Required",fieldSet:"Safety Checks",groupTo:"FireExtinguisher"},{value:"ForwardAndReverseBelts",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Forward & reverse belts",status:"Required",fieldSet:"Jet Specific Checks"},{value:"SteeringTiller",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Steering, tiller, arm bolts",status:"Required",fieldSet:"Jet Specific Checks"},{value:"CablesFRPullies",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Cables & pullies",status:"Required",fieldSet:"Engine Checks",groupTo:"SteeringChecks"},{value:"ThrottleAndCable",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Throttle & cable",status:"Required",fieldSet:"Engine Checks",groupTo:"SteeringChecks"},{value:"SandTraps",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sand traps",status:"Required",fieldSet:"Engine Checks",groupTo:"SteeringChecks"},{value:"UnitTransomBolts",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Unit transom bolts",status:"Required",fieldSet:"Jet Specific Checks"},{value:"CotterPins",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Cotter pins",status:"Required",fieldSet:"Jet Specific Checks"},{value:"ReverseBucketAndRam",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Reverse bucket & rams",status:"Required",fieldSet:"Jet Specific Checks"},{value:"NozzleAndBearings",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Nozzle & bearings",status:"Required",fieldSet:"Jet Specific Checks"},{value:"TailHousing",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Tail housing & bolts",status:"Required",fieldSet:"Jet Specific Checks"},{value:"WeatherSummary",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Weather forecast summary",status:"Required"},{value:"WindDirection",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Wind direction",status:"Required"},{value:"WindStrength",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Wind strength",status:"Required"},{value:"Swell",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sea swell",status:"Required"},{value:"TimeHighTide",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"High tide time",status:"Required"},{value:"LevelHighTide",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"High tide level",status:"Required"},{value:"TimeLowTide",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Low tide time",status:"Required"},{value:"LevelLowTide",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Low tide level",status:"Required"},{value:"Tides",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Tide predictions",status:"Required"},{value:"MasterID",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew member",status:"Required"},{value:"Comments",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Maintenance/corrective actions",status:"Required"},{value:"Signature",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Signature",status:"Required"},{value:"LifeRings",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Life rings",status:"Required",fieldSet:"Safety Checks",groupTo:"SafetyEquipment"},{value:"Flares",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Flares",status:"Required",fieldSet:"Safety Checks",groupTo:"SafetyEquipment"},{value:"FireHoses",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fire hoses",status:"Required",fieldSet:"Safety Checks",groupTo:"FireExtinguisher"},{value:"FireBuckets",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fire buckets",status:"Required",fieldSet:"Safety Checks",groupTo:"FireExtinguisher"},{value:"FireBlanket",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fire blanket",status:"Required",fieldSet:"Safety Checks",groupTo:"FireExtinguisher"},{value:"FireAxes",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fire axes",status:"Required",fieldSet:"Safety Checks",groupTo:"FireExtinguisher"},{value:"FirePump",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fire pump",status:"Required",fieldSet:"Safety Checks",groupTo:"FireExtinguisher"},{value:"FireFlaps",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fire flaps",status:"Required",fieldSet:"Safety Checks",groupTo:"FireExtinguisher"},{value:"LifeRaft",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Life raft",status:"Required",fieldSet:"Safety Checks",groupTo:"SafetyEquipment"},{value:"HighWaterAlarm",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"High water alarm",status:"Required",fieldSet:"Safety Checks"},{value:"CrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew responsible",status:"Required",groupTo:"SafetyCrewChecker"},{value:"Exterior",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Exterior",status:"Required",fieldSet:"Cleaning Checks"},{value:"Interior",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Interior",status:"Required",fieldSet:"Cleaning Checks"},{value:"Charts",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Charts",status:"Required",groupTo:"NavigationCharts"},{value:"NavigationCharts",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Navigation charts",fieldSet:"Navigation",status:"Required"},{value:"OtherNavigation",fieldSet:"Navigation",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Other",status:"Required"},{value:"DocumentCrewBriefings",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Document crew briefings",status:"Required",fieldSet:"Documentation"},{value:"RecordComments",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Record comments",status:"Required",fieldSet:"Documentation"},{value:"PropulsionEngineChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine checks",status:"Required",fieldSet:"Engine Checks",groupTo:"MainEngineChecks"},{value:"Batteries",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Batteries",status:"Required",fieldSet:"Engine Checks",groupTo:"OtherEngineFields"},{value:"Bilge",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Bilge",status:"Required",fieldSet:"Engine Checks",groupTo:"OtherEngineFields"},{value:"Throttle",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Throttle",status:"Required",fieldSet:"Engine Checks",groupTo:"OtherEngineFields"},{value:"JetUnit",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Jet unit",status:"Required",fieldSet:"Engine Checks",groupTo:"OtherEngineFields"},{value:"PreStartupChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Pre-startup checks",status:"Required",fieldSet:"Engine Checks",level:3},{value:"PreEngineAndPropulsion",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine and propulsion",status:"Required",fieldSet:"Engine Checks",tab:"Pre-startup checks"},{value:"PostEngineAndPropulsion",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine and propulsion",status:"Required",fieldSet:"Engine Checks",tab:"Post-startup checks"},{value:"PostElectrical",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Electrical",status:"Required",fieldSet:"Engine Checks",tab:"Post-startup checks"},{value:"OtherEngineFields",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Other engine fields",status:"Required",fieldSet:"Engine Checks",tab:"Other engine checks"},{value:"PreFuelLevelStart",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel level start",status:"Required",fieldSet:"Engine Checks",groupTo:"PreEngineAndPropulsion"},{value:"EngineHoursStart",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine hours start",status:"Required",fieldSet:"Engine Checks",groupTo:"PreEngineAndPropulsion"},{value:"EngineHoursEnd",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine hours end",status:"Required",fieldSet:"Engine Checks",groupTo:"PreEngineAndPropulsion"},{value:"CheckOilPressure",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Check oil pressure",status:"Required",fieldSet:"Engine Checks",groupTo:"PostEngineAndPropulsion"},{value:"BatteryIsCharging",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Battery is charging",status:"Required",fieldSet:"Engine Checks",groupTo:"PostElectrical"},{value:"ShorePowerIsDisconnected",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Shore power is disconnected",status:"Required",fieldSet:"Engine Checks",groupTo:"PostElectrical"},{value:"LockToLockSteering",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Lock-to-lock steering",status:"Required",fieldSet:"Engine Checks",groupTo:"Steering"},{value:"SteeringTrimTabs",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Trim tabs",status:"Required",fieldSet:"Engine Checks",groupTo:"Steering"},{value:"EngineChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine checks",status:"Required",fieldSet:"Engine Checks",groupTo:"PreStartupChecks"},{value:"EngineCheckPropellers",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Propellers",status:"Required",fieldSet:"Engine Checks",groupTo:"PreEngineAndPropulsion"},{value:"EngineOilWater",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine oil & water",status:"Required",fieldSet:"Engine Checks",tab:"Pre-startup checks"},{value:"EngineOil",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine oil",status:"Required",fieldSet:"Engine Checks",groupTo:"EngineOilWater"},{value:"OilWater",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Oil & water",status:"Required",fieldSet:"Engine Checks",groupTo:"EngineOilWater"},{value:"Electrical",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Electrical",status:"Required",fieldSet:"Engine Checks",groupTo:"PreStartupChecks"},{value:"PostStartupChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Post-startup checks",status:"Required",fieldSet:"Engine Checks",level:3},{value:"OtherEngineChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Other engine checks",status:"Required",fieldSet:"Engine Checks",level:3},{value:"PostStartupEngineChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Post-startup engine checks",status:"Required",fieldSet:"Engine Checks",groupTo:"PostStartupChecks"},{value:"Propulsion",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Propulsion",status:"Required",fieldSet:"Engine Checks",groupTo:"PostStartupChecks"},{value:"ForwardAndReverse",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Forward and reverse",status:"Required",fieldSet:"Engine Checks",groupTo:"PostStartupChecks"},{value:"Cooling",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Cooling system checks",status:"Required",fieldSet:"Engine Checks",groupTo:"PostElectricalStrainers"},{value:"CoolantLevels",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Coolant levels",status:"Required",fieldSet:"Engine Checks",groupTo:"PostEngineAndPropulsion"},{value:"PropulsionPropulsion",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Propulsion",status:"Required",fieldSet:"Engine Checks",groupTo:"PropulsionCheck"},{value:"EngineRoom",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine room",status:"Required",fieldSet:"Engine Checks",groupTo:"EngineRoomVisualInspection"},{value:"AirShutoffs",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Air shut-offs",status:"Required",fieldSet:"Engine Checks",groupTo:"EngineRoomVisualInspection"},{value:"FireDampeners",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fire dampeners",status:"Required",fieldSet:"Engine Checks",groupTo:"EngineRoomVisualInspection"},{value:"Generator",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Generator",status:"Required",fieldSet:"Engine Checks",groupTo:"ElectricalChecks"},{value:"FuelLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel level",status:"Required",fieldSet:"Engine Checks",groupTo:"FuelSystems"},{value:"FuelShutoffs",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel shut-offs",status:"Required",fieldSet:"Engine Checks",groupTo:"PreEngineAndPropulsion"},{value:"Separators",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Separators",status:"Required",fieldSet:"Engine Checks",groupTo:"PreEngineAndPropulsion"},{value:"Hull_HullStructure",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Hull structure",status:"Required",fieldSet:"Deck operations and exterior checks"},{value:"HullStructure",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Hull structure",status:"Required",fieldSet:"Deck operations and exterior checks",groupTo:"Hull_HullStructure"},{value:"PontoonPressure",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Pontoon pressure (for inflatables)",status:"Required",fieldSet:"Deck operations and exterior checks",groupTo:"Hull_HullStructure"},{value:"BungsInPlace",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Bungs in place",status:"Required",fieldSet:"Deck operations and exterior checks",groupTo:"Hull_HullStructure"},{value:"Hatches",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Hatches",status:"Required",fieldSet:"Deck operations and exterior checks",groupTo:"Hull_HullStructure"},{value:"Hull_DeckEquipment",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Deck equipment",status:"Required",fieldSet:"Deck operations and exterior checks"},{value:"DeckEquipment",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Deck equipment",status:"Required",fieldSet:"Deck operations and exterior checks",groupTo:"Hull_DeckEquipment"},{value:"SwimPlatformLadder",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Swim platform, ladder",status:"Required",fieldSet:"Deck operations and exterior checks",groupTo:"Hull_DeckEquipment"},{value:"BiminiTopsCanvasCovers",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Bimini tops, canvas covers",status:"Required",fieldSet:"Deck operations and exterior checks",groupTo:"Hull_DeckEquipment"},{value:"Hull_DayShapes",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Day shapes",status:"Required",fieldSet:"Deck operations and exterior checks"},{value:"DayShapes",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Day shapes",status:"Required",fieldSet:"Deck operations and exterior checks",groupTo:"Hull_DayShapes"},{value:"HullNavigationLights",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Navigation lights",status:"Required",fieldSet:"Deck operations and exterior checks",groupTo:"Hull_DayShapes"},{value:"TenderOperationalChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Tender operational checks",status:"Required",fieldSet:"Deck operations and exterior checks"},{value:"Anchor",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Anchor",status:"Required",fieldSet:"Deck operations and exterior checks"},{value:"WindscreenCheck",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Windscreen check",status:"Required",fieldSet:"Deck operations and exterior checks"},{value:"NightLineDockLinesRelease",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Night line/dock lines release",status:"Required",fieldSet:"Deck operations and exterior checks"},{value:"HVAC",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"HVAC",status:"Required",fieldSet:"HVAC"},{value:"TV",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"TV",status:"Required",fieldSet:"HVAC"},{value:"StabilizationSystems",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Stabilization systems",status:"Required",fieldSet:"HVAC"},{value:"Electronics",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Electronics",status:"Required",fieldSet:"HVAC"},{value:"NavigationChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Navigation checks",status:"Required",fieldSet:"Navigation"},{value:"GPS",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"GPS",status:"Required",fieldSet:"Navigation",groupTo:"NavigationChecks"},{value:"DepthSounder",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Depth sounder",status:"Required",fieldSet:"Navigation",groupTo:"NavigationChecks"},{value:"Radar",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Radar",status:"Required",fieldSet:"Navigation",groupTo:"NavigationChecks"},{value:"TracPlus",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"TracPlus",status:"Required",fieldSet:"Navigation",groupTo:"NavigationChecks"},{value:"ChartPlotter",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Chart plotter",status:"Required",fieldSet:"Navigation",groupTo:"NavigationChecks"},{value:"SART",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"SART",status:"Required",fieldSet:"Navigation",groupTo:"NavigationChecks"},{value:"AISOperational",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"AIS operational",status:"Required",fieldSet:"Navigation",groupTo:"NavigationChecks"},{value:"Radio",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Radio",status:"Required",fieldSet:"Navigation"},{value:"VHF",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"VHF",status:"Required",fieldSet:"Navigation",groupTo:"Radio"},{value:"UHF",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"UHF",status:"Required",fieldSet:"Navigation",groupTo:"Radio"},{value:"NavigationLights",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Navigation lights",status:"Required",fieldSet:"Navigation",groupTo:"OtherNavigation"},{value:"Compass",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Compass",status:"Required",fieldSet:"Navigation",groupTo:"OtherNavigation"},{value:"SoundSignallingDevices",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sound signalling devices",status:"Required",fieldSet:"Navigation",groupTo:"OtherNavigation"},{value:"NavigationHazards",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Navigation hazards",status:"Required",fieldSet:"Navigation",groupTo:"OtherNavigation"},{value:"Wheelhouse",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Wheelhouse",status:"Required",fieldSet:"Navigation",groupTo:"OtherNavigation"},{value:"BilgeCheck",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Bilge check",status:"Required",fieldSet:"Plumbing"},{value:"Sewage",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sewage",status:"Required",fieldSet:"Plumbing"},{value:"FreshWater",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fresh water",status:"Required",fieldSet:"Plumbing"},{value:"Sanitation",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sanitation",status:"Required",fieldSet:"Plumbing"},{value:"PestControl",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Pest control",status:"Required",fieldSet:"Plumbing"},{value:"MainEngineChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Main engine(s) checks",status:"Required",fieldSet:"Engine Checks",tab:"Other engine checks"},{value:"EngineRoomVisualInspection",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine room visual inspection",status:"Required",fieldSet:"Engine Checks",tab:"Other engine checks"},{value:"FuelSystems",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel systems",status:"Required",fieldSet:"Engine Checks",tab:"Other engine checks"},{value:"SteeringChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Steering checks",status:"Required",fieldSet:"Engine Checks",tab:"Pre-startup checks"},{value:"ThrottleAndCableChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Throttle and cable checks",status:"Required",fieldSet:"Engine Checks",groupTo:"OtherEngineFields"},{value:"ElectricalChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Electrical checks",status:"Required",fieldSet:"Engine Checks",tab:"Pre-startup checks"},{value:"Steering",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Steering",status:"Required",fieldSet:"Engine Checks",tab:"Post-startup checks"},{value:"FuelTanks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel tanks",status:"Required",fieldSet:"Engine Checks",groupTo:"PostElectricalStrainers"},{value:"FuelFilters",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel filters",status:"Required",fieldSet:"Engine Checks",groupTo:"PreEngineAndPropulsion"},{value:"Fuel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel",status:"Required",fieldSet:"Fuel Checks"},{value:"MainEngine",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Main engine",status:"Required",fieldSet:"Engine Checks",groupTo:"PropulsionCheck"},{value:"Transmission",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Transmission",status:"Required",fieldSet:"Engine Checks",groupTo:"PropulsionCheck"},{value:"SteeringPropulsion",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Steering propulsion",status:"Required",fieldSet:"Engine Checks",groupTo:"PropulsionCheck"},{value:"PropulsionCheck",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Propulsion check",status:"Required",fieldSet:"Engine Checks",tab:"Other engine checks"},{value:"ForwardReverse",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Forward & reverse",status:"Required",fieldSet:"Engine Checks",groupTo:"PostEngineAndPropulsion"},{value:"TrimTabs",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Trim tabs",status:"Required",fieldSet:"Engine Checks",groupTo:"PropulsionCheck"},{value:"AzimuthThruster",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Azimuth thrusters",status:"Required",fieldSet:"Engine Checks",groupTo:"PropulsionCheck"},{value:"Exhaust",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Exhaust",status:"Required",fieldSet:"Engine Checks",groupTo:"PostElectricalStrainers"},{value:"PropulsionBatteriesStatus",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Batteries status",status:"Required",fieldSet:"Engine Checks",groupTo:"ElectricalChecks"},{value:"HouseBatteriesStatus",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"House batteries status",status:"Required",fieldSet:"Engine Checks",groupTo:"ElectricalChecks"},{value:"ShorePower",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Shore power",status:"Required",fieldSet:"Engine Checks",groupTo:"ElectricalChecks"},{value:"ElectricalVisualFields",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Electrical panels",status:"Required",fieldSet:"Engine Checks",tab:"Pre-startup checks"},{value:"ElectricalPanels",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Electrical panels",status:"Required",fieldSet:"Engine Checks",groupTo:"ElectricalVisualFields"},{value:"Wiring",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Wiring",status:"Required",fieldSet:"Engine Checks",groupTo:"ElectricalVisualFields"},{value:"PostElectricalStrainers",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sea strainers",status:"Required",fieldSet:"Engine Checks",tab:"Post-startup checks"},{value:"SeaStrainers",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sea strainers",status:"Required",fieldSet:"Engine Checks",groupTo:"PostElectricalStrainers"},{value:"Sail",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sail",status:"Required",fieldSet:"Sail"},{value:"PreCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",fieldSet:"Engine Checks",tab:"Pre-startup checks"},{value:"PreCrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",fieldSet:"Engine Checks",groupTo:"PreCrewChecker"},{value:"PreCheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time Checked",status:"Required",fieldSet:"Engine Checks",groupTo:"PreCrewChecker"},{value:"PostCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",fieldSet:"Engine Checks",tab:"Post-startup checks"},{value:"PostCrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",fieldSet:"Engine Checks",groupTo:"PostCrewChecker"},{value:"PostCheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time Checked",status:"Required",fieldSet:"Engine Checks",groupTo:"PostCrewChecker"},{value:"OtherEngineCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",fieldSet:"Engine Checks",tab:"Other engine checks"},{value:"OtherEngineCrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",fieldSet:"Engine Checks",groupTo:"OtherEngineCrewChecker"},{value:"OtherEngineCheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time Checked",status:"Required",fieldSet:"Engine Checks",groupTo:"OtherEngineCrewChecker"},{value:"JetCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",fieldSet:"Jet Specific Checks"},{value:"JetCrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",groupTo:"JetCrewChecker"},{value:"JetCheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time Checked",status:"Required",groupTo:"JetCrewChecker"},{value:"CleaningCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleaningCrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",groupTo:"CleaningCrewChecker"},{value:"CleaningCheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time Checked",status:"Required",groupTo:"CleaningCrewChecker"},{value:"NavigationCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",fieldSet:"Navigation",status:"Required"},{value:"NavigationCrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",groupTo:"NavigationCrewChecker"},{value:"NavigationCheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time Checked",status:"Required",groupTo:"NavigationCrewChecker"},{value:"DeckOpsCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",fieldSet:"Deck operations and exterior checks",status:"Required"},{value:"DeckOpsCrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",groupTo:"DeckOpsCrewChecker"},{value:"DeckOpsCheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time Checked",status:"Required",groupTo:"DeckOpsCrewChecker"},{value:"HVACCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",fieldSet:"HVAC",status:"Required"},{value:"HVACCrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",groupTo:"HVACCrewChecker"},{value:"HVACCheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time Checked",status:"Required",groupTo:"HVACCrewChecker"},{value:"PlumbingCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",fieldSet:"Plumbing",status:"Required"},{value:"PlumbingCrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",groupTo:"PlumbingCrewChecker"},{value:"PlumbingCheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time Checked",status:"Required",groupTo:"PlumbingCrewChecker"},{value:"SailCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",fieldSet:"Sail",status:"Required"},{value:"SailCrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",groupTo:"SailCrewChecker"},{value:"SailCheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time Checked",status:"Required",groupTo:"SailCrewChecker"},{value:"EngineeringChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engineering",status:"Required",fieldSet:"Engine Checks",level:3},{value:"EngrMechanical",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Mechanical",status:"Required",fieldSet:"Engine Checks",tab:"Engineering"},{value:"MechCrankcaseOilLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crankcase Oil Level",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrMechanical"},{value:"MechCoolingWaterLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Cooling Water Level",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrMechanical"},{value:"MechTransmissionOilLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Transmission Oil Level",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrMechanical"},{value:"MechInspectPipework",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Inspect Pipework",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrMechanical"},{value:"MechHydraulicSteeringOilLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Hydraulic Steering Oil Level",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrMechanical"},{value:"MechGearBoxOilLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Gear Box Oil Level",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrMechanical"},{value:"MechInspectVeeBelts",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Inspect Vee Belts",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrMechanical"},{value:"EngrGenerator",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Generator",status:"Required",fieldSet:"Engine Checks",tab:"Engineering"},{value:"GenCrankcaseOilLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crankcase Oil Level",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrGenerator"},{value:"GenCoolingWaterLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Cooling Water Level",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrGenerator"},{value:"GenElectrical",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Electrical",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrGenerator"},{value:"GenPracxisSystemOperative",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Check Pracxis System Operative",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrGenerator"},{value:"GenTest24VLighting",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Test 24V Lighting",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrGenerator"},{value:"GenRunningTankFuelLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Record Running Tank Fuel Level per Shipping Movement",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrGenerator"},{value:"EngrElectronics",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Electronics",status:"Required",fieldSet:"Engine Checks",tab:"Engineering"},{value:"ElectrDeckLights",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Deck Lights",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrElectronics"},{value:"ElectrSearchLights",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Search Lights",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrElectronics"},{value:"ElectrChart",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Chart",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrElectronics"},{value:"EngrTowlineWinch",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Towline & Winch",status:"Required",fieldSet:"Engine Checks",tab:"Engineering"},{value:"TowCheckWinchCondition",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Check Winch condition",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrTowlineWinch"},{value:"TowProveWinchOperation",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Prove Winch Operation",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrTowlineWinch"},{value:"TowSelectControlStation",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Select Control Station",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrTowlineWinch"},{value:"TowCheckTowlineCondition",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Check Towline Condition",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrTowlineWinch"},{value:"EngrCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",fieldSet:"Engine Checks",tab:"Engineering"},{value:"EngrCrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrCrewChecker"},{value:"EngrCheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time Checked",status:"Required",fieldSet:"Engine Checks",groupTo:"EngrCrewChecker"},{value:"BiosecGlueBoardTraps",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Glue-board traps checked",status:"Required",fieldSet:"Biosecurity"},{value:"BiosecCrewChecker",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",fieldSet:"Biosecurity",status:"Required"},{value:"BiosecCrewResponsible",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew Responsible",status:"Required",groupTo:"BiosecCrewChecker"},{value:"BiosecCheckTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time Checked",status:"Required",groupTo:"BiosecCrewChecker"},{value:"CleanGalleyBench",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Galley Bench",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanGalleyFloor",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Galley Floor",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanTable",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Tables",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanMirrorGlass",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Mirrors / Glass",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanToilet",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Toilets",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanSink",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"All Sinks",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanDeckFloor",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Deck Floors",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanOutsideWallWindow",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Outside Walls and Windows",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanGarbageBin",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Rubbish / Garbage Bins",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanBoothSeat",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Booths / Seats",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanFridge",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fridges",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanCupboard",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Cupboards",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanOven",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Oven",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanSouvenir",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Souvenirs",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanRestockSalesItem",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Restock Sales Items",status:"Required",fieldSet:"Cleaning Checks"},{value:"CleanTill",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Till",status:"Required",fieldSet:"Cleaning Checks"}]},{label:"Weather",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"Voyage",sortOrder:9,componentClass:"Weather_LogBookComponent",items:[{value:"Tides",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Tides",status:"Required"}]},{label:"Voyage Summary",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"Voyage",sortOrder:9,componentClass:"VoyageSummary_LogBookComponent",items:[{value:"Activities",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Activities undertaken",status:"Required"},{value:"CourseSteered",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Course steered",status:"Required"},{value:"CourseOverGround",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Course over ground",status:"Required"},{value:"ChangesToPlan",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Changes to voyage",status:"Required"},{value:"SpeedOverGround",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Speed over ground",status:"Required"},{value:"VesselRPM",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Vessel RPM",status:"Required"},{value:"TypeOfSteering",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Type of steering",status:"Required"},{value:"VoyageDistance",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Voyage distance",status:"Required"},{value:"Comments",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Unusual occurrences",status:"Required"},{value:"Oktas",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Cloud cover",status:"Required"},{value:"WindStrength",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Wind strength (kts)",status:"Required"},{value:"WindDirection",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Wind direction",status:"Required"},{value:"Precipitation",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Precipitation",status:"Required"},{value:"Pressure",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Barometeric pressure",status:"Required"},{value:"Visibility",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Visibility",status:"Required"},{value:"Swell",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Swell",status:"Required"},{value:"SeaState",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sea state",status:"Required"},{value:"WeatherComments",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Comments on conditions",status:"Required"},{value:"ForecastComment",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Forecast comments",status:"Required"},{value:"WeatherReports",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Weather reports",status:"Required"},{value:"Tides",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Tide predictions",status:"Required"}]},{label:"Crew Welfare",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"Crew",componentClass:"CrewWelfare_LogBookComponent",items:[{value:"Fitness",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Safe crewing assessment",status:"Required"},{value:"SafetyActions",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Risk assessments completed",status:"Required"},{value:"WaterQuality",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Health, safety, and environment actions",status:"Required"},{value:"IMSafe",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"IMSafe",status:"Required",description:"Free of illness and symptoms, using only safe medication, managing stress well at home and work, free of alcohol, drugs, and their effects, rested, well-slept, and free of fatigue, well-fed, hydrated, and ready to go."}]},{label:"Logbook Sign-Off",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"SignOff",componentClass:"LogBookSignOff_LogBookComponent",items:[{value:"Review",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Logbook entry review",status:"Required"},{value:"SafetyEquipmentCheck",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Safety equipment check",status:"Required"},{value:"ForecastAccuracy",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Forecast accuracy",status:"Required"},{value:"FuelEnd",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel end",status:"Required"},{value:"EngineHours",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine hours",status:"Required"},{value:"EngineHoursEnd",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Hours run",status:"Required"},{value:"AIS",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Automatic Identification System (AIS)",status:"Required",groupTo:"NavigationAndBridgeEquipment"},{value:"NavigationLightsAndShapes",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Navigation lights & shapes",status:"Required",groupTo:"NavigationAndBridgeEquipment"},{value:"ElectronicNavigationalAids",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Electronic navigational aids",status:"Required",groupTo:"NavigationAndBridgeEquipment"},{value:"MainEngines",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Main engines",status:"Required",groupTo:"EngineRoomAndMachinery"},{value:"AuxiliarySystems",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Auxiliary systems",status:"Required",groupTo:"EngineRoomAndMachinery"},{value:"FuelAndOil",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel & oil",status:"Required",groupTo:"EngineRoomAndMachinery"},{value:"BilgeSystems",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Bilge systems",status:"Required",groupTo:"EngineRoomAndMachinery"},{value:"Power",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Power",status:"Required",groupTo:"ElectricalSystems"},{value:"BatteryMaintenance",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Battery maintenance",status:"Required",groupTo:"ElectricalSystems"},{value:"CircuitInspections",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Circuit inspections",status:"Required",groupTo:"ElectricalSystems"},{value:"MooringAndAnchoring",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Mooring & anchoring",status:"Required",groupTo:"DeckOperations"},{value:"CargoAndAccessEquipment",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Cargo & access equipment",status:"Required",groupTo:"DeckOperations"},{value:"HatchesAndWatertightDoors",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Hatches & watertight doors",status:"Required",groupTo:"DeckOperations"},{value:"GalleyAppliances",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Galley appliances",status:"Required",groupTo:"AccommodationAndGalley"},{value:"WasteManagement",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Waste management",status:"Required",groupTo:"AccommodationAndGalley"},{value:"VentilationAndAirConditioning",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Ventilation & air conditioning",status:"Required",groupTo:"AccommodationAndGalley"},{value:"EmergencyReadiness",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Emergency readiness",status:"Required",groupTo:"FinalChecks"},{value:"EnvironmentalCompliance",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Environmental compliance",status:"Required",groupTo:"FinalChecks"},{value:"NavigationAndBridgeEquipment",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Navigation & bridge equipment",status:"Required"},{value:"EngineRoomAndMachinery",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Engine room & machinery",status:"Required"},{value:"ElectricalSystems",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Electrical systems",status:"Required"},{value:"DeckOperations",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Deck operations",status:"Required"},{value:"AccommodationAndGalley",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Accommodation and galley",status:"Required"},{value:"FinalChecks",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Final checks",status:"Required"}]},{label:"Activity Types",vesselType:[0,1,2,3,4,5,6,7,8,9],category:"Voyage",componentClass:"EventType_LogBookComponent",items:[{value:"PilotTransfer",vesselType:[0,3],label:"Pilot Transfer",status:"Required"},{value:"RefuellingBunkering",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Refuelling/bunkering",status:"Required"},{value:"VesselRescue",vesselType:[0,1,3,4,7,8,9],label:"Vessel incident",status:"Required"},{value:"HumanRescue",vesselType:[0,1,3,4,7,8,9],label:"Human incident",status:"Required"},{value:"PassengerVehiclePickDrop",vesselType:[0,2,5,6],label:"Passenger/vehicle pickup/drop-off",status:"Required"},{value:"TripUpdate",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Trip Update",status:"Required"},{value:"PassengerVehiclePickDropLocation",vesselType:[0,2,5,6],label:"Location",status:"Required",groupTo:"PassengerVehiclePickDrop"},{value:"PassengerVehiclePickDropArrival",vesselType:[0,2,5,6],label:"Arrival",status:"Required",groupTo:"PassengerVehiclePickDrop"},{value:"PassengerVehiclePickDropDeparture",vesselType:[0,2,5,6],label:"Departure",status:"Required",groupTo:"PassengerVehiclePickDrop"},{value:"PassengerVehiclePickDropPaxPickDrop",vesselType:[0,2,5,6],label:"Pax pickup/drop off",status:"Required",groupTo:"PassengerVehiclePickDrop"},{value:"PassengerVehiclePickDropVehiclePickDrop",vesselType:[0,2,5,6],label:"Vehicle pickup/drop off",status:"Required",groupTo:"PassengerVehiclePickDrop"},{value:"PassengerVehiclePickDropOtherCargo",vesselType:[0,2,5,6],label:"Other cargo pickup/drop off",status:"Required",groupTo:"PassengerVehiclePickDrop"},{value:"DangerousGoods",vesselType:[0,2,5,6],label:"Dangerous goods",status:"Required",groupTo:"PassengerVehiclePickDrop"},{value:"DesignatedDangerousGoodsSailing",vesselType:[0,2,5,6],label:"Separate designated dangerous goods sailing",status:"Required",groupTo:"PassengerVehiclePickDrop"},{value:"VesselSecuredToWharf",vesselType:[0,2,5,6],label:"Vessel secured to wharf",status:"Required",groupTo:"DangerousGoodsSailing",classes:"dangerous-goods-sailing"},{value:"BravoFlagRaised",vesselType:[0,2,5,6],label:"Bravo flag raised",status:"Required",groupTo:"DangerousGoodsSailing",classes:"dangerous-goods-sailing"},{value:"TwoCrewLoadingVessel",vesselType:[0,2,5,6],label:"Two crew loading vessel",status:"Required",groupTo:"DangerousGoodsSailing",classes:"dangerous-goods-sailing"},{value:"FireHosesRiggedAndReady",vesselType:[0,2,5,6],label:"Fire hoses rigged and ready",status:"Required",groupTo:"DangerousGoodsSailing",classes:"dangerous-goods-sailing"},{value:"NoSmokingSignagePosted",vesselType:[0,2,5,6],label:"No smoking signage posted",status:"Required",groupTo:"DangerousGoodsSailing",classes:"dangerous-goods-sailing"},{value:"SpillKitAvailable",vesselType:[0,2,5,6],label:"Spill kit available",status:"Required",groupTo:"DangerousGoodsSailing",classes:"dangerous-goods-sailing"},{value:"FireExtinguishersAvailable",vesselType:[0,2,5,6],label:"Fire extinguishers available",status:"Required",groupTo:"DangerousGoodsSailing",classes:"dangerous-goods-sailing"},{value:"DGDeclarationReceived",vesselType:[0,2,5,6],label:"DG declaration received",status:"Required",groupTo:"DangerousGoodsSailing",classes:"dangerous-goods-sailing"},{value:"LoadPlanReceived",vesselType:[0,2,5,6],label:"Load plan received",status:"Required",groupTo:"DangerousGoodsSailing",classes:"dangerous-goods-sailing"},{value:"MSDSAvailable",vesselType:[0,2,5,6],label:"MSDS available for all dangerous goods carried",status:"Required",groupTo:"DangerousGoodsSailing"},{value:"AnyVehiclesSecureToVehicleDeck",vesselType:[0,2,5,6],label:"Any vehicles secure to vehicle deck",status:"Required",groupTo:"DangerousGoodsSailing"},{value:"SafetyAnnouncement",vesselType:[0,2,5,6],label:"Safety announcement includes reference to dangerous goods & no smoking",status:"Required",groupTo:"DangerousGoodsSailing"},{value:"VehicleStationaryAndSecure",vesselType:[0,2,5,6],label:"Vehicle stationary and secure prior to vehicle departing vessel",status:"Required",groupTo:"DangerousGoodsSailing"},{value:"IncludeTowingRiskEvaluation",vesselType:[0,1],label:"Include towing risk evaluation",status:"Required",groupTo:"HumanRescue"},{value:"RestrictedVisibility",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Restricted visibility",status:"Required"},{value:"BarCrossing",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Bar crossing",status:"Required"},{value:"PassengerArrival",vesselType:[0,1,4,7,8,9],label:"Passenger arrival",status:"Required"},{value:"PassengerDeparture",vesselType:[0,1,4,7,8,9],label:"Passenger departure",status:"Required"},{value:"CrewTraining",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew training",status:"Required"},{value:"EventSupernumerary",vesselType:[0,1,2,4,5,6,7,8,9],label:"Supernumerary",status:"Required"},{value:"InfringementNotices",vesselType:[0,1],label:"Infringement notices",status:"Required"},{value:"TaskingStartUnderway",vesselType:[0,1],label:"Tasking start/underway",status:"Required",type:"RescueSubCategory",classes:"hidden"},{value:"TaskingOnScene",vesselType:[0,1],label:"Tasking on scene",status:"Required",type:"RescueSubCategory",classes:"hidden"},{value:"TaskingOnTow",vesselType:[0,1],label:"Tasking on tow",status:"Required",type:"RescueSubCategory",classes:"hidden"},{value:"TaskingPaused",vesselType:[0,1],label:"Tasking paused",status:"Required",type:"RescueSubCategory",classes:"hidden"},{value:"TaskingResumed",vesselType:[0,1],label:"Tasking resumed",status:"Required",type:"RescueSubCategory",classes:"hidden"},{value:"TaskingComplete",vesselType:[0,1],label:"Tasking complete",status:"Required",type:"RescueSubCategory",classes:"hidden"},{value:"PassengerArrival_FuelLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel level",status:"Required",groupTo:"PassengerArrival"},{value:"PassengerArrival_Pax",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Pax",status:"Required",groupTo:"PassengerArrival"},{value:"PassengerDeparture_FuelLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel level",status:"Required",groupTo:"PassengerDeparture"},{value:"PassengerDeparture_Pax",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Pax",status:"Required",groupTo:"PassengerDeparture"},{value:"WaterTaxiService_FuelLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel level",status:"Required",groupTo:"WaterTaxiService"},{value:"WaterTaxiService_Pax",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Pax",status:"Required",groupTo:"WaterTaxiService"},{value:"ScheduledPassengerService_FuelLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel level",status:"Required",groupTo:"ScheduledPassengerService"},{value:"ScheduledPassengerService_Pax",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Pax",status:"Required",groupTo:"ScheduledPassengerService"},{value:"BarCrossing_Location",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Location",status:"Required",groupTo:"BarCrossing"},{value:"BarCrossing_EndLocation",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Location",status:"Required",groupTo:"BarCrossing"},{value:"BarCrossing_Time",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time",status:"Required",groupTo:"BarCrossing"},{value:"BarCrossing_EndTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time",status:"Required",groupTo:"BarCrossing"},{value:"BarCrossing_StopAssessPlan",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Stop assess plan",status:"Required",groupTo:"BarCrossing"},{value:"BarCrossing_CrewBriefing",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew briefing",status:"Required",groupTo:"BarCrossing"},{value:"BarCrossing_Weather",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Weather",status:"Required",groupTo:"BarCrossing"},{value:"BarCrossing_Stability",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Stability",status:"Required",groupTo:"BarCrossing"},{value:"BarCrossing_WaterTightness",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Water tightness",status:"Required",groupTo:"BarCrossing"},{value:"BarCrossing_LifeJackets",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Life jackets",status:"Required",groupTo:"BarCrossing"},{value:"BarCrossing_LookoutPosted",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Lookout posted",status:"Required",groupTo:"BarCrossing"},{value:"BarCrossing_Report",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Report",status:"Required",groupTo:"BarCrossing"},{value:"RestrictedVisibility_StartLocation",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Start location",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_EndLocation",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"End location",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_CrossingTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crossing time",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_EstSafeSpeed",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Estimated safe speed",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_StopAssessPlan",vesselType:[0,1,3,4,7,8,9],label:"Stop assess plan",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_CrewBriefing",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crew briefing",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_NavLights",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Navigational lights",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_SoundSignals",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sound signals",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_Lookout",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Lookout",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_SoundSignal",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Sound signal",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_RadarWatch",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Radar watch",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_RadioWatch",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Radio watch",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_CrossedTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Crossed time",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_ApproxSafeSpeed",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Approximate safe speed",status:"Required",groupTo:"RestrictedVisibility"},{value:"RestrictedVisibility_Report",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Report",status:"Required",groupTo:"RestrictedVisibility"},{value:"CrewTraining_FuelLevel",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Fuel level",status:"Required",groupTo:"CrewTraining"},{value:"CrewTraining_Location",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Location",status:"Required",groupTo:"CrewTraining"},{value:"CrewTraining_StartTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Start time",status:"Required",groupTo:"CrewTraining"},{value:"CrewTraining_FinishTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Finish time",status:"Required",groupTo:"CrewTraining"},{value:"Supernumerary_FirstName",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"First name",status:"Required",groupTo:"EventSupernumerary"},{value:"Supernumerary_Surname",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Surname",status:"Required",groupTo:"EventSupernumerary"},{value:"Supernumerary_Signature",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Signature",status:"Required",groupTo:"EventSupernumerary"},{value:"Supernumerary_Guests",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Number of guests",status:"Required",groupTo:"EventSupernumerary"},{value:"Supernumerary_GuestBriefing",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Guest briefing completed",status:"Required",groupTo:"EventSupernumerary"},{value:"Supernumerary_BriefingTime",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time of briefing",status:"Required",groupTo:"EventSupernumerary"},{value:"Supernumerary_Policies",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Policies",status:"Required",fieldType:"files",groupTo:"EventSupernumerary"},{value:"ConductSAP",vesselType:[0,1],label:"Conduct SAP",status:"Required",type:"TowingSubCategory",classes:"hidden"},{value:"InvestigateNatureOfIssue",vesselType:[0,1],label:"Investigate nature of issue",status:"Required",type:"TowingSubCategory",classes:"hidden"},{value:"EveryoneOnBoardOk",vesselType:[0,1],label:"Everyone on board OK",status:"Required",type:"TowingSubCategory",classes:"hidden"},{value:"RudderToMidshipsAndTrimmed",vesselType:[0,1],label:"Rudder to midships & trimmed",status:"Required",type:"TowingSubCategory",classes:"hidden"},{value:"LifejacketsOn",vesselType:[0,1],label:"Life jackets on",status:"Required",type:"TowingSubCategory",classes:"hidden"},{value:"CommunicationsEstablished",vesselType:[0,1],label:"Communications established",status:"Required",type:"TowingSubCategory",classes:"hidden"},{value:"SecureAndSafeTowing",vesselType:[0,1],label:"Secure & safe towing",status:"Required",type:"TowingSubCategory",classes:"hidden"},{value:"TripUpdate_Time",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Time",status:"Required",groupTo:"TripUpdate"},{value:"TripUpdate_Title",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Title",status:"Required",groupTo:"TripUpdate"},{value:"TripUpdate_Location",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Location",status:"Required",groupTo:"TripUpdate"},{value:"TripUpdate_Comments",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Comments",status:"Required",groupTo:"TripUpdate"},{value:"TripUpdate_Attachment",vesselType:[0,1,2,3,4,5,6,7,8,9],label:"Attachment",status:"Required",groupTo:"TripUpdate"}]}],a=["OtherActivities","CrewMemberID","DutyPerformedID","EngineID","HoursStart","HoursEnd","Date","LetGoTieUp","FirstName","Surname","DailyChecksCompleted","DepartTime","From","ArriveTime","To","ExpectedNextContact","Hull","OilAndWater","SafetyEquipment","TaskingStartUnderway","TaskingOnScene","TaskingOnTow","TaskingPaused","TaskingResumed","TaskingComplete","Supernumerary_Guests","ConductSAP","InvestigateNatureOfIssue","EveryoneOnBoardOk","RudderToMidshipsAndTrimmed","LifejacketsOn","CommunicationsEstablished","SecureAndSafeTowing","EPIRB","FireExtinguisher","EngineMountsAndStabilisers","ElectricalVisualFields","ElectricalChecks","SteeringChecks","EngineOilWater","PreEngineAndPropulsion","PostElectricalStrainers","PostElectrical","Steering","PostEngineAndPropulsion","DriveShaftsChecks","OtherEngineFields","MainEngineChecks","EngineRoomVisualInspection","FuelSystems","PropulsionCheck","Radio","NavigationChecks","OtherNavigation","NavigationCharts","Hull_DayShapes","Hull_DeckEquipment","Hull_HullStructure","NavigationAndBridgeEquipment","EngineRoomAndMachinery","ElectricalSystems","DeckOperations","AccommodationAndGalley","FinalChecks","EventSupernumerary","CrewTraining","PassengerDeparture","PassengerArrival","BarCrossing","RestrictedVisibility","HumanRescue","VehicleFerryFields","SafetyCrewChecker","PreCrewChecker","PostCrewChecker","OtherEngineCrewChecker","NavigationCrewChecker","JetCrewChecker","CleaningCrewChecker","DeckOpsCrewChecker","HVACCrewChecker","PlumbingCrewChecker","SailCrewChecker","BiosecCrewChecker","EngrMechanical","EngrGenerator","EngrElectronics","EngrTowlineWinch","EngrCrewChecker","PassengerVehiclePickDrop","TripUpdate"]},12751:(e,s,t)=>{t.d(s,{Z:()=>o});var l=t(83179),a=t.n(l),r=t(86708);class i{async save(e,s="Update"){try{return await r.Z.Client.put({...e,idbCRUD:s,idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")}),console.log("ClientModel save",e,s),e}catch(t){console.error("ClientModel save",e,s,t)}}async getById(e){try{let s=await r.Z.Client.get(`${e}`);return console.log("ClientModel getById",e,s),s}catch(s){console.error("ClientModel getById",e,s)}}async setProperty(e){try{if(e){let s=await r.Z.Client.get(`${e}`);return s.idbCRUD="Download",s.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.Client.update(e,s),console.log("ClientModel setProperty",e,s),s}}catch(s){console.error("ClientModel setProperty",e,s)}}}let o=i},59689:(e,s,t)=>{t.d(s,{Z:()=>o});var l=t(83179),a=t.n(l),r=t(86708);class i{async save(e){try{return await r.Z.CrewDuty.put({...e,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")}),console.log("CrewDutyModel save",e),e}catch(s){console.error("CrewDutyModel save",e,s)}}async getAll(){try{let e=await r.Z.CrewDuty.toArray();return console.log("CrewDutyModel getAll",e),e}catch(e){console.error("CrewDutyModel getAll",e)}}async getById(e){try{let s=await r.Z.CrewDuty.get(`${e}`);return console.log("CrewDutyModel getById",e,s),s}catch(s){console.error("CrewDutyModel getById",e,s)}}async getByIds(e){try{let s=await r.Z.CrewDuty.where("id").anyOf(e).toArray();return console.log("CrewDutyModel getByIds",e,s),s}catch(s){console.error("CrewDutyModel getByIds",e,s)}}async bulkAdd(e){try{return await r.Z.CrewDuty.bulkAdd(e),console.log("CrewDutyModel bulkAdd",e),e}catch(s){if("BulkError"===s.name){let t=s.failuresByPos.map(e=>e.key),l=e.filter(e=>!t.includes(e.id));return await r.Z.CrewDuty.bulkAdd(l),console.log("CrewDutyModel bulkAdd::BulkError",e,s),e}console.error("CrewDutyModel bulkAdd",e,s)}}async setProperty(e){try{if(e){let s=await r.Z.CrewDuty.get(`${e}`);return s.idbCRUD="Download",s.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.CrewDuty.update(e,s),console.log("CrewDutyModel setProperty",e,s),s}}catch(s){console.error("CrewDutyModel setProperty",e,s)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.CrewDuty.update(e.id,e)})),console.log("CrewDutyModel multiUpdate",e)}catch(s){console.error("CrewDutyModel multiUpdate",e,s)}}}let o=i},98318:(e,s,t)=>{t.d(s,{Z:()=>d});var l=t(83179),a=t.n(l),r=t(86708),i=t(48755),o=t(73366),n=t(59689);class u{async save(e){try{let s=Object.fromEntries(Object.entries(e).map(([e,s])=>[e,"number"==typeof s?s.toString():s])),t=s.id,l={...s,__typename:"CrewMembers_LogBookEntrySection",idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},i=await this.getById(t);i?await r.Z.CrewMembers_LogBookEntrySection.update(t,l):await r.Z.CrewMembers_LogBookEntrySection.add(l);let o=await this.logBookEntryModel.getById(s.logBookEntryID);if(o){let e=o.logBookEntrySections.nodes,t={className:"SeaLogs\\CrewMembers_LogBookEntrySection",id:s.id,__typename:"CrewMembers_LogBookEntrySection"};e=[...e,t],await this.logBookEntryModel.save({...o,logBookEntrySections:{nodes:e}})}return i=await this.getById(t),console.log("CrewMembers_LogBookEntrySectionModel save",e,i),i}catch(s){console.error("CrewMembers_LogBookEntrySectionModel save",e,s)}}async getAll(){try{let e=await r.Z.CrewMembers_LogBookEntrySection.toArray();return console.log("CrewMembers_LogBookEntrySectionModel getAll",e),e}catch(e){console.error("CrewMembers_LogBookEntrySectionModel getAll",e)}}async getById(e){try{let s=await r.Z.CrewMembers_LogBookEntrySection.get(`${e}`),t=await this.addRelationships(s);return console.log("CrewMembers_LogBookEntrySectionModel getById",e,t),t}catch(s){console.error("CrewMembers_LogBookEntrySectionModel getById",e,s)}}async getByIds(e){try{let s=await r.Z.CrewMembers_LogBookEntrySection.where("id").anyOf(e).toArray(),t=Promise.all(s.map(async e=>await this.addRelationships(e)));return console.log("CrewMembers_LogBookEntrySectionModel getByIds",e,t),t}catch(s){console.error("CrewMembers_LogBookEntrySectionModel getByIds",e,s)}}async bulkAdd(e){try{return await r.Z.CrewMembers_LogBookEntrySection.bulkAdd(e),console.log("CrewMembers_LogBookEntrySectionModel bulkAdd",e),e}catch(s){if("BulkError"===s.name){let t=s.failuresByPos.map(e=>e.key),l=e.filter(e=>!t.includes(e.id));return await r.Z.CrewMembers_LogBookEntrySection.bulkAdd(l),console.log("CrewMembers_LogBookEntrySectionModel bulkAdd::BulkError",e,s),e}console.error("CrewMembers_LogBookEntrySectionModel bulkAdd",e,s)}}async delete(e){try{await r.Z.CrewMembers_LogBookEntrySection.delete(`${e.id}`);let s=e.logBookEntryID,t=await this.logBookEntryModel.getById(s),l=t.logBookEntrySections.nodes.filter(s=>s.id!==e.id);return await this.logBookEntryModel.save({...t,logBookEntrySections:{nodes:l}}),console.log("CrewMembers_LogBookEntrySectionModel delete",e,!0),!0}catch(s){console.error("CrewMembers_LogBookEntrySectionModel delete",e,s)}}async addRelationships(e){if(!e)return console.log("CrewMembers_LogBookEntrySectionModel addRelationships",e),e;{let s=+e.crewMemberID>0?await this.seaLogsMemberModel.getById(e.crewMemberID):null,t=+e.dutyPerformedID>0?await this.crewDutyModel.getById(e.dutyPerformedID):null,l={...e,crewMember:s,dutyPerformed:t};return console.log("CrewMembers_LogBookEntrySectionModel addRelationships",e,l),l}}async setProperty(e){try{if(e){let s=await r.Z.CrewMembers_LogBookEntrySection.get(`${e}`);return s.idbCRUD="Download",s.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.CrewMembers_LogBookEntrySection.update(e,s),console.log("CrewMembers_LogBookEntrySectionModel setProperty",e,s),s}}catch(s){console.error("CrewMembers_LogBookEntrySectionModel setProperty",e,s)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.CrewMembers_LogBookEntrySection.update(e.id,e)})),console.log("CrewMembers_LogBookEntrySectionModel multiUpdate",e)}catch(s){console.error("CrewMembers_LogBookEntrySectionModel multiUpdate",e,s)}}constructor(){this.seaLogsMemberModel=new o.Z,this.crewDutyModel=new n.Z,this.logBookEntryModel=new i.Z}}let d=u},2887:(e,s,t)=>{t.d(s,{Z:()=>o});var l=t(83179),a=t.n(l),r=t(86708);t(69507);class i{async save(e){try{let s=Object.fromEntries(Object.entries(e).map(([e,s])=>[e,"number"==typeof s?s.toString():s])),t=s.id,l={...s,__typename:"SectionMemberComment",idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},i=await this.getById(t);return i?await r.Z.SectionMemberComment.update(t,l):await r.Z.SectionMemberComment.add(l),i=await this.getById(t),console.log("SectionMemberComment save",e,i),i}catch(s){console.error("SectionMemberComment save",e,s)}}async getAll(){try{let e=await r.Z.SectionMemberComment.toArray();return console.log("SectionMemberComment getAll",e),e}catch(e){console.error("SectionMemberComment getAll",e)}}async getOne(){try{let e=await r.Z.SectionMemberComment.toArray();if(e.length>0)return e?.sort((e,s)=>+s.id-+e.id),e[0];return console.log("SectionMemberComment getOne",e),e}catch(e){console.error("SectionMemberComment getOne",e)}}async getById(e){try{let s=await r.Z.SectionMemberComment.get(`${e}`);return console.log("SectionMemberComment getById",e,s),s}catch(s){console.error("SectionMemberComment getById",e,s)}}async getByLogBookEntrySectionID(e){try{let s=await r.Z.SectionMemberComment.where("logBookEntrySectionID").equals(`${e}`).toArray();return console.log("SectionMemberComment getByLogBookEntrySectionID",e,s),s}catch(s){console.error("SectionMemberComment getByLogBookEntrySectionID",e,s)}}async getPreviousComments(e=[]){try{if(0===e.length)return[];let s=await r.Z.table("SectionMemberComment").where("logBookEntrySectionID").anyOf(e).and(e=>"Section"===e.commentType).and(e=>null!==e.comment).and(e=>!1===e.hideComment).toArray();return console.log("SectionMemberComment getPreviousComments",e,s),s}catch(s){console.error("SectionMemberComment getPreviousComments",e,s)}}async bulkAdd(e){try{return await r.Z.SectionMemberComment.bulkAdd(e),console.log("SectionMemberComment bulkAdd",e),e}catch(s){if("BulkError"===s.name){let t=s.failuresByPos.map(e=>e.key),l=e.filter(e=>!t.includes(e.id));return await r.Z.SectionMemberComment.bulkAdd(l),console.log("SectionMemberComment bulkAdd::BulkError",e,s),e}console.error("SectionMemberComment bulkAdd",e,s)}}async setProperty(e){try{if(e){let s=await r.Z.SectionMemberComment.get(`${e}`);return s.idbCRUD="Download",s.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.SectionMemberComment.update(e,s),console.log("SectionMemberComment setProperty",e,s),s}}catch(s){console.error("SectionMemberComment setProperty",e,s)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.SectionMemberComment.update(e.id,e)})),console.log("SectionMemberComment multiUpdate",e)}catch(s){console.error("SectionMemberComment multiUpdate",e,s)}}}let o=i}};