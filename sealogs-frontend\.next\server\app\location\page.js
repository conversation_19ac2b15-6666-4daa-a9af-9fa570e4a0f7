(()=>{var e={};e.id=1896,e.ids=[1896],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},22901:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>u}),s(56649),s(10133),s(78398),s(57757),s(48045);var r=s(40060),o=s(33581),n=s(57567),l=s.n(n),i=s(51650),a={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>i[e]);s.d(t,a);let u=["",{children:["location",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,56649)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\location\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,10133)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\location\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\location\\page.tsx"],d="/location/page",p={require:s,loadChunk:()=>Promise.resolve()},f=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/location/page",pathname:"/location",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},45101:(e,t,s)=>{Promise.resolve().then(s.bind(s,51386))},46513:(e,t,s)=>{Promise.resolve().then(s.bind(s,37377))},47520:(e,t,s)=>{"use strict";s.d(t,{default:()=>o.a});var r=s(19821),o=s.n(r)},19821:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let r=s(41034);s(98768),s(60343);let o=r._(s(40907));function n(e,t){var s;let r={loading:e=>{let{error:t,isLoading:s,pastDelay:r}=e;return null}};"function"==typeof e&&(r.loader=e);let n={...r,...t};return(0,o.default)({...n,modules:null==(s=n.loadableGenerated)?void 0:s.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96359:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return o}});let r=s(90408);function o(e){let{reason:t,children:s}=e;throw new r.BailoutToCSRError(t)}},40907:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let r=s(98768),o=s(60343),n=s(96359),l=s(58902);function i(e){return{default:e&&"default"in e?e.default:e}}let a={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},u=function(e){let t={...a,...e},s=(0,o.lazy)(()=>t.loader().then(i)),u=t.loading;function c(e){let i=u?(0,r.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,a=t.ssr?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.PreloadCss,{moduleIds:t.modules}),(0,r.jsx)(s,{...e})]}):(0,r.jsx)(n.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(s,{...e})});return(0,r.jsx)(o.Suspense,{fallback:i,children:a})}return c.displayName="LoadableComponent",c}},58902:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return n}});let r=s(98768),o=s(54580);function n(e){let{moduleIds:t}=e,s=(0,o.getExpectedRequestStore)("next/dynamic css"),n=[];if(s.reactLoadableManifest&&t){let e=s.reactLoadableManifest;for(let s of t){if(!e[s])continue;let t=e[s].files.filter(e=>e.endsWith(".css"));n.push(...t)}}return 0===n.length?null:(0,r.jsx)(r.Fragment,{children:n.map(e=>(0,r.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:s.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},51386:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(98768);s(60343);var o=s(64837);function n({children:e}){return r.jsx(o.Z,{children:e})}},37377:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var r=s(98768),o=s(60343),n=s(94060),l=s(79418),i=s(34469),a=s(51742),u=s(30905),c=s(39650),d=s(26100),p=s(24794),f=s(39544),x=s(49008);let m=(0,s(97428).Z)("MapPinPlus",[["path",{d:"M19.914 11.105A7.298 7.298 0 0 0 20 10a8 8 0 0 0-16 0c0 4.993 5.539 10.193 7.399 11.799a1 1 0 0 0 1.202 0 32 32 0 0 0 .824-.738",key:"fcdtly"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M16 18h6",key:"987eiv"}],["path",{d:"M19 15v6",key:"10aioa"}]]);var g=s(69424),h=s(25394),v=s(9210),j=s(66263);let y=()=>{let e=(0,g.useRouter)(),[t,s]=(0,o.useState)(!0),[y,b]=(0,o.useState)([]),[P,{loading:_}]=(0,l.t)(n.IR,{fetchPolicy:"cache-and-network",onCompleted:e=>{let{nodes:t}=e.readGeoLocations;t&&b(t)},onError:e=>{console.error("queryGeoLocations error",e)}}),N=async()=>{await P({variables:{limit:1e3,offset:0}})};(0,o.useEffect)(()=>{t&&(N(),s(!1))},[t]);let M=(0,a.wu)([{accessorKey:"title",header:({column:e})=>r.jsx(u.u,{column:e,title:"Location"}),cellAlignment:"left",cell:({row:e})=>{let t=e.original;return r.jsx(j.default,{href:`/location/edit?id=${t.id}`,children:r.jsx("div",{className:" text-medium ",children:t.title||"No Title"})})}},{accessorKey:"lat",header:({column:e})=>r.jsx(u.u,{column:e,title:"Latitude"}),cell:({row:e})=>{let t=e.original;return r.jsx("div",{children:t.lat})}},{accessorKey:"long",header:({column:e})=>r.jsx(u.u,{column:e,title:"Longitude"}),cell:({row:e})=>{let t=e.original;return r.jsx("div",{children:t.long})}},{accessorKey:"map",header:()=>r.jsx("div",{className:"text-center",children:"Map"}),cell:({row:e})=>{let t=e.original;return r.jsx("div",{className:"flex justify-center",children:(0,r.jsxs)(p.Dialog,{children:[r.jsx(p.DialogTrigger,{onClick:e=>e.stopPropagation(),asChild:!0,children:r.jsx(v.r4,{})}),(0,r.jsxs)(p.DialogContent,{className:"max-w-6xl w-[95vw] p-0 overflow-hidden",children:[r.jsx(p.DialogHeader,{className:"p-4 pb-0",children:(0,r.jsxs)(p.DialogTitle,{className:"flex items-center gap-2",children:[r.jsx(x.Z,{size:18}),t.title||"Location Map",t.lat&&t.long&&(0,r.jsxs)(h.P,{className:"text-muted-foreground leading-none",children:["(",t.lat.toFixed(6),","," ",t.long.toFixed(6),")"]})]})}),r.jsx("div",{className:"h-[80vh] pt-0 grid",children:r.jsx(i.Z,{enableResize:!0,vessel:{title:t.title},position:[t.lat||0,t.long||0],zoom:15,className:"size-full"})})]})]})})},enableSorting:!1}]);return(0,r.jsxs)(r.Fragment,{children:[r.jsx(c.ListHeader,{icon:r.jsx(v.r4,{className:"size-12"}),title:"Locations",actions:r.jsx(f.Button,{iconLeft:r.jsx(m,{}),onClick:()=>e.push("/location/create"),children:"Add Location"})}),r.jsx("div",{className:"mt-16",children:t||_?r.jsx(d.Z,{}):r.jsx(a.wQ,{columns:M,data:y,showToolbar:!1,pageSize:20})})]})};var b=s(46776);let P=()=>((0,g.useRouter)(),(0,o.useEffect)(()=>{(0,b.UU)()},[]),r.jsx(y,{}))},34469:(e,t,s)=>{"use strict";s.d(t,{Z:()=>x});var r=s(98768),o=s(47520);s(47011),s(7385);var n=s(35753),l=s(60343);s(94060);var i=s(96268),a=s(71241),u=s.n(a);let c=(0,o.default)(async()=>{},{loadableGenerated:{modules:["components\\location-map.tsx -> react-leaflet"]},ssr:!1}),d=(0,o.default)(async()=>{},{loadableGenerated:{modules:["components\\location-map.tsx -> react-leaflet"]},ssr:!1}),p=(0,o.default)(async()=>{},{loadableGenerated:{modules:["components\\location-map.tsx -> react-leaflet"]},ssr:!1}),f=({debounceMs:e=150})=>{let t=(0,n.Sx)(),{run:s}=(0,i.DI)(()=>{t&&t.invalidateSize()},e);return(0,l.useEffect)(()=>{let e=t.getContainer();if(!e)return;let r=new ResizeObserver(e=>{e.some(e=>{let{width:t,height:s}=e.contentRect;return t>0&&s>0})&&s()});r.observe(e);let o=e.parentElement;o&&r.observe(o);let n=setTimeout(()=>{t&&t.invalidateSize()},100);return()=>{r.disconnect(),clearTimeout(n)}},[t,s]),null};function x({position:e,vessel:t,vessels:s,zoom:o=13,onPositionChange:l,className:i="h-full",enableResize:a=!0,scrollWheelZoom:x=!1,style:m,resizeDebounceMs:g=150,enableClickToSetPosition:h=!1}){let v=[isNaN(e[0])?0:e[0],isNaN(e[1])?0:e[1]],j=u()((e,t)=>{"number"!=typeof e||"number"!=typeof t||isNaN(e)||isNaN(t)||l?.([e,t])},100),y=e=>{if(e?.latlng?.lat!==void 0&&e?.latlng?.lng!==void 0){let{lat:t,lng:s}=e.latlng;j(t,s)}},b=e=>{let{position:t,vessel:s}=e;return(0,n.zV)({dblclick(e){h&&y(e)}}),r.jsx(d,{position:t})},P=(()=>{if(s&&s.length>0){let e=s.find(e=>e.vesselPosition?.lat&&e.vesselPosition?.long);if(e)return[e.vesselPosition.lat||0,e.vesselPosition.long||0]}return v})(),_=`map-${Math.round(1e3*P[0])}-${Math.round(1e3*P[1])}`;return(0,r.jsxs)(c,{center:P,zoom:o,scrollWheelZoom:x,className:i,style:{minHeight:"200px",height:"100%",...m},children:[r.jsx(p,{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),s&&s.length>0?s.filter(e=>e.vesselPosition?.lat!=0||e.vesselPosition?.geoLocation?.id>0).map((e,t)=>e?.vesselPosition?.id>0&&r.jsx(b,{position:[(e.vesselPosition.lat||0)+.001+.004*Math.random(),e.vesselPosition.long||0],vessel:e},t)):t&&r.jsx(b,{position:v,vessel:t}),a&&r.jsx(f,{debounceMs:g})]},_)}},10133:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\location\layout.tsx#default`)},56649:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\location\page.tsx#default`)},7385:()=>{},47011:()=>{},35753:(e,t,s)=>{"use strict";s.d(t,{Sx:()=>n,zV:()=>l});var r=s(60343);let o=(0,r.createContext)(null);function n(){return function(){let e=(0,r.useContext)(o);if(null==e)throw Error("No context provided: useLeafletContext() can only be used in a descendant of <MapContainer>");return e}().map}function l(e){let t=n();return(0,r.useEffect)(function(){return t.on(e),function(){t.off(e)}},[t,e]),t}o.Provider}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7944],()=>s(22901));module.exports=r})();