"use strict";exports.id=9507,exports.ids=[9507],exports.modules={69507:(e,t,n)=>{let r;n.d(t,{ZP:()=>nv});let i="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,a=Object.keys,s=Array.isArray;function o(e,t){return"object"!=typeof t||a(t).forEach(function(n){e[n]=t[n]}),e}"undefined"==typeof Promise||i.Promise||(i.Promise=Promise);let l=Object.getPrototypeOf,u={}.hasOwnProperty;function c(e,t){return u.call(e,t)}function h(e,t){"function"==typeof t&&(t=t(l(e))),("undefined"==typeof Reflect?a:Reflect.ownKeys)(t).forEach(n=>{f(e,n,t[n])})}let d=Object.defineProperty;function f(e,t,n,r){d(e,t,o(n&&c(n,"get")&&"function"==typeof n.get?{get:n.get,set:n.set,configurable:!0}:{value:n,configurable:!0,writable:!0},r))}function p(e){return{from:function(t){return e.prototype=Object.create(t.prototype),f(e.prototype,"constructor",e),{extend:h.bind(null,e.prototype)}}}}let y=Object.getOwnPropertyDescriptor;function m(e,t){let n;return y(e,t)||(n=l(e))&&m(n,t)}let v=[].slice;function g(e,t,n){return v.call(e,t,n)}function b(e,t){return t(e)}function _(e){if(!e)throw Error("Assertion Failed")}function w(e){i.setImmediate?setImmediate(e):setTimeout(e,0)}function x(e,t){return e.reduce((e,n,r)=>{var i=t(n,r);return i&&(e[i[0]]=i[1]),e},{})}function k(e,t){if("string"==typeof t&&c(e,t))return e[t];if(!t)return e;if("string"!=typeof t){for(var n=[],r=0,i=t.length;r<i;++r){var a=k(e,t[r]);n.push(a)}return n}var s=t.indexOf(".");if(-1!==s){var o=e[t.substr(0,s)];return null==o?void 0:k(o,t.substr(s+1))}}function E(e,t,n){if(e&&void 0!==t&&(!("isFrozen"in Object)||!Object.isFrozen(e))){if("string"!=typeof t&&"length"in t){_("string"!=typeof n&&"length"in n);for(var r=0,i=t.length;r<i;++r)E(e,t[r],n[r])}else{var a=t.indexOf(".");if(-1!==a){var o=t.substr(0,a),l=t.substr(a+1);if(""===l)void 0===n?s(e)&&!isNaN(parseInt(o))?e.splice(o,1):delete e[o]:e[o]=n;else{var u=e[o];u&&c(e,o)||(u=e[o]={}),E(u,l,n)}}else void 0===n?s(e)&&!isNaN(parseInt(t))?e.splice(t,1):delete e[t]:e[t]=n}}}function P(e){var t={};for(var n in e)c(e,n)&&(t[n]=e[n]);return t}let K=[].concat;function O(e){return K.apply([],e)}let S="BigUint64Array,BigInt64Array,Array,Boolean,String,Date,RegExp,Blob,File,FileList,FileSystemFileHandle,FileSystemDirectoryHandle,ArrayBuffer,DataView,Uint8ClampedArray,ImageBitmap,ImageData,Map,Set,CryptoKey".split(",").concat(O([8,16,32,64].map(e=>["Int","Uint","Float"].map(t=>t+e+"Array")))).filter(e=>i[e]),A=S.map(e=>i[e]);x(S,e=>[e,!0]);let C=null;function j(e){C="undefined"!=typeof WeakMap&&new WeakMap;let t=function e(t){if(!t||"object"!=typeof t)return t;let n=C&&C.get(t);if(n)return n;if(s(t)){n=[],C&&C.set(t,n);for(var r=0,i=t.length;r<i;++r)n.push(e(t[r]))}else if(A.indexOf(t.constructor)>=0)n=t;else{let r=l(t);for(var a in n=r===Object.prototype?{}:Object.create(r),C&&C.set(t,n),t)c(t,a)&&(n[a]=e(t[a]))}return n}(e);return C=null,t}let{toString:D}={};function I(e){return D.call(e).slice(8,-1)}let B="undefined"!=typeof Symbol?Symbol.iterator:"@@iterator",T="symbol"==typeof B?function(e){var t;return null!=e&&(t=e[B])&&t.apply(e)}:function(){return null},R={};function F(e){var t,n,r,i;if(1==arguments.length){if(s(e))return e.slice();if(this===R&&"string"==typeof e)return[e];if(i=T(e)){for(n=[];!(r=i.next()).done;)n.push(r.value);return n}if(null==e)return[e];if("number"==typeof(t=e.length)){for(n=Array(t);t--;)n[t]=e[t];return n}return[e]}for(t=arguments.length,n=Array(t);t--;)n[t]=arguments[t];return n}let M="undefined"!=typeof Symbol?e=>"AsyncFunction"===e[Symbol.toStringTag]:()=>!1;var N="undefined"!=typeof location&&/^(http|https):\/\/(localhost|127\.0\.0\.1)/.test(location.href),q=()=>!0;let $=!Error("").stack;function U(){if($)try{throw U.arguments,Error()}catch(e){return e}return Error()}function L(e,t){var n=e.stack;return n?(t=t||0,0===n.indexOf(e.name)&&(t+=(e.name+e.message).split("\n").length),n.split("\n").slice(t).filter(q).map(e=>"\n"+e).join("")):""}var V=["Unknown","Constraint","Data","TransactionInactive","ReadOnly","Version","NotFound","InvalidState","InvalidAccess","Abort","Timeout","QuotaExceeded","Syntax","DataClone"],W=["Modify","Bulk","OpenFailed","VersionChange","Schema","Upgrade","InvalidTable","MissingAPI","NoSuchDatabase","InvalidArgument","SubTransaction","Unsupported","Internal","DatabaseClosed","PrematureCommit","ForeignAwait"].concat(V),Y={VersionChanged:"Database version changed by other database connection",DatabaseClosed:"Database has been closed",Abort:"Transaction aborted",TransactionInactive:"Transaction has already completed or failed",MissingAPI:"IndexedDB API missing. Please visit https://tinyurl.com/y2uuvskb"};function z(e,t){this._e=U(),this.name=e,this.message=t}function G(e,t){return e+". Errors: "+Object.keys(t).map(e=>t[e].toString()).filter((e,t,n)=>n.indexOf(e)===t).join("\n")}function H(e,t,n,r){this._e=U(),this.failures=t,this.failedKeys=r,this.successCount=n,this.message=G(e,t)}function Q(e,t){this._e=U(),this.name="BulkError",this.failures=Object.keys(t).map(e=>t[e]),this.failuresByPos=t,this.message=G(e,t)}p(z).from(Error).extend({stack:{get:function(){return this._stack||(this._stack=this.name+": "+this.message+L(this._e,2))}},toString:function(){return this.name+": "+this.message}}),p(H).from(z),p(Q).from(z);var X=W.reduce((e,t)=>(e[t]=t+"Error",e),{}),J=W.reduce((e,t)=>{var n=t+"Error";function r(e,r){this._e=U(),this.name=n,e?"string"==typeof e?(this.message=`${e}${r?"\n "+r:""}`,this.inner=r||null):"object"==typeof e&&(this.message=`${e.name} ${e.message}`,this.inner=e):(this.message=Y[t]||n,this.inner=null)}return p(r).from(z),e[t]=r,e},{});J.Syntax=SyntaxError,J.Type=TypeError,J.Range=RangeError;var Z=V.reduce((e,t)=>(e[t+"Error"]=J[t],e),{}),ee=W.reduce((e,t)=>(-1===["Syntax","Type","Range"].indexOf(t)&&(e[t+"Error"]=J[t]),e),{});function et(){}function en(e){return e}function er(e,t){return null==e||e===en?t:function(n){return t(e(n))}}function ei(e,t){return function(){e.apply(this,arguments),t.apply(this,arguments)}}function ea(e,t){return e===et?t:function(){var n=e.apply(this,arguments);void 0!==n&&(arguments[0]=n);var r=this.onsuccess,i=this.onerror;this.onsuccess=null,this.onerror=null;var a=t.apply(this,arguments);return r&&(this.onsuccess=this.onsuccess?ei(r,this.onsuccess):r),i&&(this.onerror=this.onerror?ei(i,this.onerror):i),void 0!==a?a:n}}function es(e,t){return e===et?t:function(){e.apply(this,arguments);var n=this.onsuccess,r=this.onerror;this.onsuccess=this.onerror=null,t.apply(this,arguments),n&&(this.onsuccess=this.onsuccess?ei(n,this.onsuccess):n),r&&(this.onerror=this.onerror?ei(r,this.onerror):r)}}function eo(e,t){return e===et?t:function(n){var r=e.apply(this,arguments);o(n,r);var i=this.onsuccess,a=this.onerror;this.onsuccess=null,this.onerror=null;var s=t.apply(this,arguments);return i&&(this.onsuccess=this.onsuccess?ei(i,this.onsuccess):i),a&&(this.onerror=this.onerror?ei(a,this.onerror):a),void 0===r?void 0===s?void 0:s:o(r,s)}}function el(e,t){return e===et?t:function(){return!1!==t.apply(this,arguments)&&e.apply(this,arguments)}}function eu(e,t){return e===et?t:function(){var n=e.apply(this,arguments);if(n&&"function"==typeof n.then){for(var r=this,i=arguments.length,a=Array(i);i--;)a[i]=arguments[i];return n.then(function(){return t.apply(r,a)})}return t.apply(this,arguments)}}ee.ModifyError=H,ee.DexieError=z,ee.BulkError=Q;var ec={};let[eh,ed,ef]="undefined"==typeof Promise?[]:(()=>{let e=Promise.resolve();if("undefined"==typeof crypto||!crypto.subtle)return[e,l(e),e];let t=crypto.subtle.digest("SHA-512",new Uint8Array([0]));return[t,l(t),e]})(),ep=ed&&ed.then,ey=eh&&eh.constructor,em=!!ef;var ev=!1,eg=ef?()=>{ef.then(eN)}:i.setImmediate?setImmediate.bind(null,eN):i.MutationObserver?()=>{var e=document.createElement("div");new MutationObserver(()=>{eN(),e=null}).observe(e,{attributes:!0}),e.setAttribute("i","1")}:()=>{setTimeout(eN,0)},eb=function(e,t){eS.push([e,t]),ew&&(eg(),ew=!1)},e_=!0,ew=!0,ex=[],ek=[],eE=null,eP=en,eK={id:"global",global:!0,ref:0,unhandleds:[],onunhandled:e7,pgp:!1,env:{},finalize:function(){this.unhandleds.forEach(e=>{try{e7(e[0],e[1])}catch(e){}})}},eO=eK,eS=[],eA=0,eC=[];function ej(e){if("object"!=typeof this)throw TypeError("Promises must be constructed via new");this._listeners=[],this.onuncatched=et,this._lib=!1;var t=this._PSD=eO;if(N&&(this._stackHolder=U(),this._prev=null,this._numPrev=0),"function"!=typeof e){if(e!==ec)throw TypeError("Not a function");return this._state=arguments[1],this._value=arguments[2],void(!1===this._state&&eB(this,this._value))}this._state=null,this._value=null,++t.ref,function e(t,n){try{n(n=>{if(null===t._state){if(n===t)throw TypeError("A promise cannot be resolved with itself.");var r=t._lib&&eq();n&&"function"==typeof n.then?e(t,(e,t)=>{n instanceof ej?n._then(e,t):n.then(e,t)}):(t._state=!0,t._value=n,eT(t)),r&&e$()}},eB.bind(null,t))}catch(e){eB(t,e)}}(this,e)}let eD={get:function(){var e=eO,t=eH;function n(n,r){var i=!e.global&&(e!==eO||t!==eH);let a=i&&!eZ();var s=new ej((t,s)=>{eR(this,new eI(e9(n,e,i,a),e9(r,e,i,a),t,s,e))});return N&&eM(s,this),s}return n.prototype=ec,n},set:function(e){f(this,"then",e&&e.prototype===ec?eD:{get:function(){return e},set:eD.set})}};function eI(e,t,n,r,i){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.resolve=n,this.reject=r,this.psd=i}function eB(e,t){if(ek.push(t),null===e._state){var n=e._lib&&eq();t=eP(t),e._state=!1,e._value=t,N&&null!==t&&"object"==typeof t&&!t._promise&&function(e,t,n){try{e.apply(null,void 0)}catch(e){}}(()=>{var n=m(t,"stack");t._promise=e,f(t,"stack",{get:()=>ev?n&&(n.get?n.get.apply(t):n.value):e.stack})}),ex.some(t=>t._value===e._value)||ex.push(e),eT(e),n&&e$()}}function eT(e){var t=e._listeners;e._listeners=[];for(var n=0,r=t.length;n<r;++n)eR(e,t[n]);var i=e._PSD;--i.ref||i.finalize(),0===eA&&(++eA,eb(()=>{0==--eA&&eU()},[]))}function eR(e,t){if(null!==e._state){var n=e._state?t.onFulfilled:t.onRejected;if(null===n)return(e._state?t.resolve:t.reject)(e._value);++t.psd.ref,++eA,eb(eF,[n,e,t])}else e._listeners.push(t)}function eF(e,t,n){try{eE=t;var r,i=t._value;t._state?r=e(i):(ek.length&&(ek=[]),r=e(i),-1===ek.indexOf(i)&&function(e){for(var t=ex.length;t;)if(ex[--t]._value===e._value)return void ex.splice(t,1)}(t)),n.resolve(r)}catch(e){n.reject(e)}finally{eE=null,0==--eA&&eU(),--n.psd.ref||n.psd.finalize()}}function eM(e,t){var n=t?t._numPrev+1:0;n<100&&(e._prev=t,e._numPrev=n)}function eN(){eq()&&e$()}function eq(){var e=e_;return e_=!1,ew=!1,e}function e$(){var e,t,n;do for(;eS.length>0;)for(e=eS,eS=[],n=e.length,t=0;t<n;++t){var r=e[t];r[0].apply(null,r[1])}while(eS.length>0);e_=!0,ew=!0}function eU(){var e=ex;ex=[],e.forEach(e=>{e._PSD.onunhandled.call(null,e._value,e)});for(var t=eC.slice(0),n=t.length;n;)t[--n]()}function eL(e){return new ej(ec,!1,e)}function eV(e,t){var n=eO;return function(){var r=eq(),i=eO;try{return e3(n,!0),e.apply(this,arguments)}catch(e){t&&t(e)}finally{e3(i,!1),r&&e$()}}}h(ej.prototype,{then:eD,_then:function(e,t){eR(this,new eI(null,null,e,t,eO))},catch:function(e){if(1==arguments.length)return this.then(null,e);var t=arguments[0],n=arguments[1];return"function"==typeof t?this.then(null,e=>e instanceof t?n(e):eL(e)):this.then(null,e=>e&&e.name===t?n(e):eL(e))},finally:function(e){return this.then(t=>(e(),t),t=>(e(),eL(t)))},stack:{get:function(){if(this._stack)return this._stack;try{ev=!0;var e=(function e(t,n,r){if(n.length===r)return n;var i="";if(!1===t._state){var a,s,o=t._value;null!=o?(a=o.name||"Error",s=o.message||o,i=L(o,0)):(a=o,s=""),n.push(a+(s?": "+s:"")+i)}return N&&((i=L(t._stackHolder,2))&&-1===n.indexOf(i)&&n.push(i),t._prev&&e(t._prev,n,r)),n})(this,[],20).join("\nFrom previous: ");return null!==this._state&&(this._stack=e),e}finally{ev=!1}}},timeout:function(e,t){return e<1/0?new ej((n,r)=>{var i=setTimeout(()=>r(new J.Timeout(t)),e);this.then(n,r).finally(clearTimeout.bind(null,i))}):this}}),"undefined"!=typeof Symbol&&Symbol.toStringTag&&f(ej.prototype,Symbol.toStringTag,"Dexie.Promise"),eK.env=e8(),h(ej,{all:function(){var e=F.apply(null,arguments).map(e0);return new ej(function(t,n){0===e.length&&t([]);var r=e.length;e.forEach((i,a)=>ej.resolve(i).then(n=>{e[a]=n,--r||t(e)},n))})},resolve:e=>{if(e instanceof ej)return e;if(e&&"function"==typeof e.then)return new ej((t,n)=>{e.then(t,n)});var t=new ej(ec,!0,e);return eM(t,eE),t},reject:eL,race:function(){var e=F.apply(null,arguments).map(e0);return new ej((t,n)=>{e.map(e=>ej.resolve(e).then(t,n))})},PSD:{get:()=>eO,set:e=>eO=e},totalEchoes:{get:()=>eH},newPSD:eX,usePSD:e4,scheduler:{get:()=>eb,set:e=>{eb=e}},rejectionMapper:{get:()=>eP,set:e=>{eP=e}},follow:(e,t)=>new ej((n,r)=>eX((t,n)=>{var r=eO;r.unhandleds=[],r.onunhandled=n,r.finalize=ei(function(){var e;e=()=>{0===this.unhandleds.length?t():n(this.unhandleds[0])},eC.push(function t(){e(),eC.splice(eC.indexOf(t),1)}),++eA,eb(()=>{0==--eA&&eU()},[])},r.finalize),e()},t,n,r))}),ey&&(ey.allSettled&&f(ej,"allSettled",function(){let e=F.apply(null,arguments).map(e0);return new ej(t=>{0===e.length&&t([]);let n=e.length,r=Array(n);e.forEach((e,i)=>ej.resolve(e).then(e=>r[i]={status:"fulfilled",value:e},e=>r[i]={status:"rejected",reason:e}).then(()=>--n||t(r)))})}),ey.any&&"undefined"!=typeof AggregateError&&f(ej,"any",function(){let e=F.apply(null,arguments).map(e0);return new ej((t,n)=>{0===e.length&&n(AggregateError([]));let r=e.length,i=Array(r);e.forEach((e,a)=>ej.resolve(e).then(e=>t(e),e=>{i[a]=e,--r||n(AggregateError(i))}))})}));let eW={awaits:0,echoes:0,id:0};var eY=0,ez=[],eG=0,eH=0,eQ=0;function eX(e,t,n,r){var i=eO,a=Object.create(i);a.parent=i,a.ref=0,a.global=!1,a.id=++eQ;var s=eK.env;a.env=em?{Promise:ej,PromiseProp:{value:ej,configurable:!0,writable:!0},all:ej.all,race:ej.race,allSettled:ej.allSettled,any:ej.any,resolve:ej.resolve,reject:ej.reject,nthen:e6(s.nthen,a),gthen:e6(s.gthen,a)}:{},t&&o(a,t),++i.ref,a.finalize=function(){--this.parent.ref||this.parent.finalize()};var l=e4(a,e,n,r);return 0===a.ref&&a.finalize(),l}function eJ(){return eW.id||(eW.id=++eY),++eW.awaits,eW.echoes+=100,eW.id}function eZ(){return!!eW.awaits&&(0==--eW.awaits&&(eW.id=0),eW.echoes=100*eW.awaits,!0)}function e0(e){return eW.echoes&&e&&e.constructor===ey?(eJ(),e.then(e=>(eZ(),e),e=>(eZ(),te(e)))):e}function e1(e){++eH,eW.echoes&&0!=--eW.echoes||(eW.echoes=eW.id=0),ez.push(eO),e3(e,!0)}function e2(){var e=ez[ez.length-1];ez.pop(),e3(e,!1)}function e3(e,t){var n,r=eO;if((t?!eW.echoes||eG++&&e===eO:!eG||--eG&&e===eO)||(n=t?e1.bind(null,e):e2,ep.call(eh,n)),e!==eO&&(eO=e,r===eK&&(eK.env=e8()),em)){var a=eK.env.Promise,s=e.env;ed.then=s.nthen,a.prototype.then=s.gthen,(r.global||e.global)&&(Object.defineProperty(i,"Promise",s.PromiseProp),a.all=s.all,a.race=s.race,a.resolve=s.resolve,a.reject=s.reject,s.allSettled&&(a.allSettled=s.allSettled),s.any&&(a.any=s.any))}}function e8(){var e=i.Promise;return em?{Promise:e,PromiseProp:Object.getOwnPropertyDescriptor(i,"Promise"),all:e.all,race:e.race,allSettled:e.allSettled,any:e.any,resolve:e.resolve,reject:e.reject,nthen:ed.then,gthen:e.prototype.then}:{}}function e4(e,t,n,r,i){var a=eO;try{return e3(e,!0),t(n,r,i)}finally{e3(a,!1)}}function e9(e,t,n,r){return"function"!=typeof e?e:function(){var i,a=eO;n&&eJ(),e3(t,!0);try{return e.apply(this,arguments)}finally{e3(a,!1),r&&(i=eZ,ep.call(eh,i))}}}function e6(e,t){return function(n,r){return e.call(this,e9(n,t),e9(r,t))}}-1===(""+ep).indexOf("[native code]")&&(eJ=eZ=et);let e5="unhandledrejection";function e7(e,t){var n;try{n=t.onuncatched(e)}catch(e){}if(!1!==n)try{var r,a={promise:t,reason:e};if(i.document&&document.createEvent?((r=document.createEvent("Event")).initEvent(e5,!0,!0),o(r,a)):i.CustomEvent&&o(r=new CustomEvent(e5,{detail:a}),a),r&&i.dispatchEvent&&(dispatchEvent(r),!i.PromiseRejectionEvent&&i.onunhandledrejection))try{i.onunhandledrejection(r)}catch(e){}N&&r&&!r.defaultPrevented&&console.warn(`Unhandled rejection: ${e.stack||e}`)}catch(e){}}var te=ej.reject;let tt="3.2.7",tn=String.fromCharCode(65535),tr=-1/0,ti="Invalid key provided. Keys must be of type string, number, Date or Array<string | number | Date>.",ta="String expected.",ts=[],to="undefined"!=typeof navigator&&/(MSIE|Trident|Edge)/.test(navigator.userAgent),tl=e=>!/(dexie\.js|dexie\.min\.js)/.test(e),tu="__dbnames",tc="readonly",th="readwrite";function td(e,t){return e?t?function(){return e.apply(this,arguments)&&t.apply(this,arguments)}:e:t}let tf={type:3,lower:-1/0,lowerOpen:!1,upper:[[]],upperOpen:!1};function tp(e){return"string"!=typeof e||/\./.test(e)?e=>e:t=>(void 0===t[e]&&e in t&&delete(t=j(t))[e],t)}class ty{_trans(e,t,n){let r=this._tx||eO.trans,i=this.name;function a(e,n,r){if(!r.schema[i])throw new J.NotFound("Table "+i+" not part of transaction");return t(r.idbtrans,r)}let s=eq();try{return r&&r.db===this.db?r===eO.trans?r._promise(e,a,n):eX(()=>r._promise(e,a,n),{trans:r,transless:eO.transless||eO}):function e(t,n,r,i){if(t.idbdb&&(t._state.openComplete||eO.letThrough||t._vip)){var a=t._createTransaction(n,r,t._dbSchema);try{a.create(),t._state.PR1398_maxLoop=3}catch(a){return a.name===X.InvalidState&&t.isOpen()&&--t._state.PR1398_maxLoop>0?(console.warn("Dexie: Need to reopen db"),t._close(),t.open().then(()=>e(t,n,r,i))):te(a)}return a._promise(n,(e,t)=>eX(()=>(eO.trans=a,i(e,t,a)))).then(e=>a._completion.then(()=>e))}if(t._state.openComplete)return te(new J.DatabaseClosed(t._state.dbOpenError));if(!t._state.isBeingOpened){if(!t._options.autoOpen)return te(new J.DatabaseClosed);t.open().catch(et)}return t._state.dbReadyPromise.then(()=>e(t,n,r,i))}(this.db,e,[this.name],a)}finally{s&&e$()}}get(e,t){return e&&e.constructor===Object?this.where(e).first(t):this._trans("readonly",t=>this.core.get({trans:t,key:e}).then(e=>this.hook.reading.fire(e))).then(t)}where(e){if("string"==typeof e)return new this.db.WhereClause(this,e);if(s(e))return new this.db.WhereClause(this,`[${e.join("+")}]`);let t=a(e);if(1===t.length)return this.where(t[0]).equals(e[t[0]]);let n=this.schema.indexes.concat(this.schema.primKey).filter(e=>{if(e.compound&&t.every(t=>e.keyPath.indexOf(t)>=0)){for(let n=0;n<t.length;++n)if(-1===t.indexOf(e.keyPath[n]))return!1;return!0}return!1}).sort((e,t)=>e.keyPath.length-t.keyPath.length)[0];if(n&&this.db._maxKey!==tn){let r=n.keyPath.slice(0,t.length);return this.where(r).equals(r.map(t=>e[t]))}!n&&N&&console.warn(`The query ${JSON.stringify(e)} on ${this.name} would benefit of a compound index [${t.join("+")}]`);let{idxByName:r}=this.schema,i=this.db._deps.indexedDB;function o(e,t){try{return 0===i.cmp(e,t)}catch(e){return!1}}let[l,u]=t.reduce(([t,n],i)=>{let a=r[i],l=e[i];return[t||a,t||!a?td(n,a&&a.multi?e=>{let t=k(e,i);return s(t)&&t.some(e=>o(l,e))}:e=>o(l,k(e,i))):n]},[null,null]);return l?this.where(l.name).equals(e[l.keyPath]).filter(u):n?this.filter(u):this.where(t).equals("")}filter(e){return this.toCollection().and(e)}count(e){return this.toCollection().count(e)}offset(e){return this.toCollection().offset(e)}limit(e){return this.toCollection().limit(e)}each(e){return this.toCollection().each(e)}toArray(e){return this.toCollection().toArray(e)}toCollection(){return new this.db.Collection(new this.db.WhereClause(this))}orderBy(e){return new this.db.Collection(new this.db.WhereClause(this,s(e)?`[${e.join("+")}]`:e))}reverse(){return this.toCollection().reverse()}mapToClass(e){this.schema.mappedClass=e;let t=t=>{if(!t)return t;let n=Object.create(e.prototype);for(var r in t)if(c(t,r))try{n[r]=t[r]}catch(e){}return n};return this.schema.readHook&&this.hook.reading.unsubscribe(this.schema.readHook),this.schema.readHook=t,this.hook("reading",t),e}defineClass(){return this.mapToClass(function(e){o(this,e)})}add(e,t){let{auto:n,keyPath:r}=this.schema.primKey,i=e;return r&&n&&(i=tp(r)(e)),this._trans("readwrite",e=>this.core.mutate({trans:e,type:"add",keys:null!=t?[t]:null,values:[i]})).then(e=>e.numFailures?ej.reject(e.failures[0]):e.lastResult).then(t=>{if(r)try{E(e,r,t)}catch(e){}return t})}update(e,t){if("object"!=typeof e||s(e))return this.where(":id").equals(e).modify(t);{let n=k(e,this.schema.primKey.keyPath);if(void 0===n)return te(new J.InvalidArgument("Given object does not contain its primary key"));try{"function"!=typeof t?a(t).forEach(n=>{E(e,n,t[n])}):t(e,{value:e,primKey:n})}catch(e){}return this.where(":id").equals(n).modify(t)}}put(e,t){let{auto:n,keyPath:r}=this.schema.primKey,i=e;return r&&n&&(i=tp(r)(e)),this._trans("readwrite",e=>this.core.mutate({trans:e,type:"put",values:[i],keys:null!=t?[t]:null})).then(e=>e.numFailures?ej.reject(e.failures[0]):e.lastResult).then(t=>{if(r)try{E(e,r,t)}catch(e){}return t})}delete(e){return this._trans("readwrite",t=>this.core.mutate({trans:t,type:"delete",keys:[e]})).then(e=>e.numFailures?ej.reject(e.failures[0]):void 0)}clear(){return this._trans("readwrite",e=>this.core.mutate({trans:e,type:"deleteRange",range:tf})).then(e=>e.numFailures?ej.reject(e.failures[0]):void 0)}bulkGet(e){return this._trans("readonly",t=>this.core.getMany({keys:e,trans:t}).then(e=>e.map(e=>this.hook.reading.fire(e))))}bulkAdd(e,t,n){let r=Array.isArray(t)?t:void 0,i=(n=n||(r?void 0:t))?n.allKeys:void 0;return this._trans("readwrite",t=>{let{auto:n,keyPath:a}=this.schema.primKey;if(a&&r)throw new J.InvalidArgument("bulkAdd(): keys argument invalid on tables with inbound keys");if(r&&r.length!==e.length)throw new J.InvalidArgument("Arguments objects and keys must have the same length");let s=e.length,o=a&&n?e.map(tp(a)):e;return this.core.mutate({trans:t,type:"add",keys:r,values:o,wantResults:i}).then(({numFailures:e,results:t,lastResult:n,failures:r})=>{if(0===e)return i?t:n;throw new Q(`${this.name}.bulkAdd(): ${e} of ${s} operations failed`,r)})})}bulkPut(e,t,n){let r=Array.isArray(t)?t:void 0,i=(n=n||(r?void 0:t))?n.allKeys:void 0;return this._trans("readwrite",t=>{let{auto:n,keyPath:a}=this.schema.primKey;if(a&&r)throw new J.InvalidArgument("bulkPut(): keys argument invalid on tables with inbound keys");if(r&&r.length!==e.length)throw new J.InvalidArgument("Arguments objects and keys must have the same length");let s=e.length,o=a&&n?e.map(tp(a)):e;return this.core.mutate({trans:t,type:"put",keys:r,values:o,wantResults:i}).then(({numFailures:e,results:t,lastResult:n,failures:r})=>{if(0===e)return i?t:n;throw new Q(`${this.name}.bulkPut(): ${e} of ${s} operations failed`,r)})})}bulkDelete(e){let t=e.length;return this._trans("readwrite",t=>this.core.mutate({trans:t,type:"delete",keys:e})).then(({numFailures:e,lastResult:n,failures:r})=>{if(0===e)return n;throw new Q(`${this.name}.bulkDelete(): ${e} of ${t} operations failed`,r)})}}function tm(e){var t={},n=function(n,r){if(r){for(var i=arguments.length,a=Array(i-1);--i;)a[i-1]=arguments[i];return t[n].subscribe.apply(null,a),e}if("string"==typeof n)return t[n]};n.addEventType=o;for(var r=1,i=arguments.length;r<i;++r)o(arguments[r]);return n;function o(e,r,i){if("object"!=typeof e){r||(r=el),i||(i=et);var l={subscribers:[],fire:i,subscribe:function(e){-1===l.subscribers.indexOf(e)&&(l.subscribers.push(e),l.fire=r(l.fire,e))},unsubscribe:function(e){l.subscribers=l.subscribers.filter(function(t){return t!==e}),l.fire=l.subscribers.reduce(r,i)}};return t[e]=n[e]=l,l}a(e).forEach(function(t){var n=e[t];if(s(n))o(t,e[t][0],e[t][1]);else{if("asap"!==n)throw new J.InvalidArgument("Invalid event config");var r=o(t,en,function(){for(var e=arguments.length,t=Array(e);e--;)t[e]=arguments[e];r.subscribers.forEach(function(e){w(function(){e.apply(null,t)})})})}})}}function tv(e,t){return p(t).from({prototype:e}),t}function tg(e,t){return!(e.filter||e.algorithm||e.or)&&(t?e.justLimit:!e.replayFilter)}function tb(e,t){e.filter=td(e.filter,t)}function t_(e,t,n){var r=e.replayFilter;e.replayFilter=r?()=>td(r(),t()):t,e.justLimit=n&&!r}function tw(e,t){if(e.isPrimKey)return t.primaryKey;let n=t.getIndexByKeyPath(e.index);if(!n)throw new J.Schema("KeyPath "+e.index+" on object store "+t.name+" is not indexed");return n}function tx(e,t,n){let r=tw(e,t.schema);return t.openCursor({trans:n,values:!e.keysOnly,reverse:"prev"===e.dir,unique:!!e.unique,query:{index:r,range:e.range}})}function tk(e,t,n,r){let i=e.replayFilter?td(e.filter,e.replayFilter()):e.filter;if(e.or){let a={},s=(e,n,r)=>{if(!i||i(n,r,e=>n.stop(e),e=>n.fail(e))){var s=n.primaryKey,o=""+s;"[object ArrayBuffer]"===o&&(o=""+new Uint8Array(s)),c(a,o)||(a[o]=!0,t(e,n,r))}};return Promise.all([e.or._iterate(s,n),tE(tx(e,r,n),e.algorithm,s,!e.keysOnly&&e.valueMapper)])}return tE(tx(e,r,n),td(e.algorithm,i),t,!e.keysOnly&&e.valueMapper)}function tE(e,t,n,r){var i=eV(r?(e,t,i)=>n(r(e),t,i):n);return e.then(e=>{if(e)return e.start(()=>{var n=()=>e.continue();t&&!t(e,e=>n=e,t=>{e.stop(t),n=et},t=>{e.fail(t),n=et})||i(e.value,e,e=>n=e),n()})})}function tP(e,t){try{let n=tK(e),r=tK(t);if(n!==r)return"Array"===n?1:"Array"===r?-1:"binary"===n?1:"binary"===r?-1:"string"===n?1:"string"===r?-1:"Date"===n?1:"Date"!==r?NaN:-1;switch(n){case"number":case"Date":case"string":return e>t?1:e<t?-1:0;case"binary":return function(e,t){let n=e.length,r=t.length,i=n<r?n:r;for(let n=0;n<i;++n)if(e[n]!==t[n])return e[n]<t[n]?-1:1;return n===r?0:n<r?-1:1}(tO(e),tO(t));case"Array":return function(e,t){let n=e.length,r=t.length,i=n<r?n:r;for(let n=0;n<i;++n){let r=tP(e[n],t[n]);if(0!==r)return r}return n===r?0:n<r?-1:1}(e,t)}}catch(e){}return NaN}function tK(e){let t=typeof e;if("object"!==t)return t;if(ArrayBuffer.isView(e))return"binary";let n=I(e);return"ArrayBuffer"===n?"binary":n}function tO(e){return e instanceof Uint8Array?e:ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):new Uint8Array(e)}class tS{_read(e,t){var n=this._ctx;return n.error?n.table._trans(null,te.bind(null,n.error)):n.table._trans("readonly",e).then(t)}_write(e){var t=this._ctx;return t.error?t.table._trans(null,te.bind(null,t.error)):t.table._trans("readwrite",e,"locked")}_addAlgorithm(e){var t=this._ctx;t.algorithm=td(t.algorithm,e)}_iterate(e,t){return tk(this._ctx,e,t,this._ctx.table.core)}clone(e){var t=Object.create(this.constructor.prototype),n=Object.create(this._ctx);return e&&o(n,e),t._ctx=n,t}raw(){return this._ctx.valueMapper=null,this}each(e){var t=this._ctx;return this._read(n=>tk(t,e,n,t.table.core))}count(e){return this._read(e=>{let t=this._ctx,n=t.table.core;if(tg(t,!0))return n.count({trans:e,query:{index:tw(t,n.schema),range:t.range}}).then(e=>Math.min(e,t.limit));var r=0;return tk(t,()=>(++r,!1),e,n).then(()=>r)}).then(e)}sortBy(e,t){let n=e.split(".").reverse(),r=n[0],i=n.length-1;function a(e,t){return t?a(e[n[t]],t-1):e[r]}var s="next"===this._ctx.dir?1:-1;function o(e,t){var n=a(e,i),r=a(t,i);return n<r?-s:n>r?s:0}return this.toArray(function(e){return e.sort(o)}).then(t)}toArray(e){return this._read(e=>{var t=this._ctx;if("next"===t.dir&&tg(t,!0)&&t.limit>0){let{valueMapper:n}=t,r=tw(t,t.table.core.schema);return t.table.core.query({trans:e,limit:t.limit,values:!0,query:{index:r,range:t.range}}).then(({result:e})=>n?e.map(n):e)}{let n=[];return tk(t,e=>n.push(e),e,t.table.core).then(()=>n)}},e)}offset(e){var t=this._ctx;return e<=0||(t.offset+=e,tg(t)?t_(t,()=>{var t=e;return(e,n)=>0===t||(1===t?--t:n(()=>{e.advance(t),t=0}),!1)}):t_(t,()=>{var t=e;return()=>--t<0})),this}limit(e){return this._ctx.limit=Math.min(this._ctx.limit,e),t_(this._ctx,()=>{var t=e;return function(e,n,r){return--t<=0&&n(r),t>=0}},!0),this}until(e,t){return tb(this._ctx,function(n,r,i){return!e(n.value)||(r(i),t)}),this}first(e){return this.limit(1).toArray(function(e){return e[0]}).then(e)}last(e){return this.reverse().first(e)}filter(e){var t;return tb(this._ctx,function(t){return e(t.value)}),(t=this._ctx).isMatch=td(t.isMatch,e),this}and(e){return this.filter(e)}or(e){return new this.db.WhereClause(this._ctx.table,e,this)}reverse(){return this._ctx.dir="prev"===this._ctx.dir?"next":"prev",this._ondirectionchange&&this._ondirectionchange(this._ctx.dir),this}desc(){return this.reverse()}eachKey(e){var t=this._ctx;return t.keysOnly=!t.isMatch,this.each(function(t,n){e(n.key,n)})}eachUniqueKey(e){return this._ctx.unique="unique",this.eachKey(e)}eachPrimaryKey(e){var t=this._ctx;return t.keysOnly=!t.isMatch,this.each(function(t,n){e(n.primaryKey,n)})}keys(e){var t=this._ctx;t.keysOnly=!t.isMatch;var n=[];return this.each(function(e,t){n.push(t.key)}).then(function(){return n}).then(e)}primaryKeys(e){var t=this._ctx;if("next"===t.dir&&tg(t,!0)&&t.limit>0)return this._read(e=>{var n=tw(t,t.table.core.schema);return t.table.core.query({trans:e,values:!1,limit:t.limit,query:{index:n,range:t.range}})}).then(({result:e})=>e).then(e);t.keysOnly=!t.isMatch;var n=[];return this.each(function(e,t){n.push(t.primaryKey)}).then(function(){return n}).then(e)}uniqueKeys(e){return this._ctx.unique="unique",this.keys(e)}firstKey(e){return this.limit(1).keys(function(e){return e[0]}).then(e)}lastKey(e){return this.reverse().firstKey(e)}distinct(){var e=this._ctx,t=e.index&&e.table.schema.idxByName[e.index];if(!t||!t.multi)return this;var n={};return tb(this._ctx,function(e){var t=e.primaryKey.toString(),r=c(n,t);return n[t]=!0,!r}),this}modify(e){var t=this._ctx;return this._write(n=>{var r;if("function"==typeof e)r=e;else{var i=a(e),s=i.length;r=function(t){for(var n=!1,r=0;r<s;++r){var a=i[r],o=e[a];k(t,a)!==o&&(E(t,a,o),n=!0)}return n}}let o=t.table.core,{outbound:l,extractKey:u}=o.schema.primaryKey,c=this.db._options.modifyChunkSize||200,h=[],d=0,f=[],p=(e,t)=>{let{failures:n,numFailures:r}=t;for(let t of(d+=e-r,a(n)))h.push(n[t])};return this.clone().primaryKeys().then(i=>{let a=s=>{let h=Math.min(c,i.length-s);return o.getMany({trans:n,keys:i.slice(s,s+h),cache:"immutable"}).then(d=>{let f=[],y=[],m=l?[]:null,v=[];for(let e=0;e<h;++e){let t=d[e],n={value:j(t),primKey:i[s+e]};!1!==r.call(n,n.value,n)&&(null==n.value?v.push(i[s+e]):l||0===tP(u(t),u(n.value))?(y.push(n.value),l&&m.push(i[s+e])):(v.push(i[s+e]),f.push(n.value)))}let g=tg(t)&&t.limit===1/0&&("function"!=typeof e||e===tA)&&{index:t.index,range:t.range};return Promise.resolve(f.length>0&&o.mutate({trans:n,type:"add",values:f}).then(e=>{for(let t in e.failures)v.splice(parseInt(t),1);p(f.length,e)})).then(()=>(y.length>0||g&&"object"==typeof e)&&o.mutate({trans:n,type:"put",keys:m,values:y,criteria:g,changeSpec:"function"!=typeof e&&e}).then(e=>p(y.length,e))).then(()=>(v.length>0||g&&e===tA)&&o.mutate({trans:n,type:"delete",keys:v,criteria:g}).then(e=>p(v.length,e))).then(()=>i.length>s+h&&a(s+c))})};return a(0).then(()=>{if(h.length>0)throw new H("Error modifying one or more objects",h,d,f);return i.length})})})}delete(){var e=this._ctx,t=e.range;return tg(e)&&(e.isPrimKey&&!to||3===t.type)?this._write(n=>{let{primaryKey:r}=e.table.core.schema;return e.table.core.count({trans:n,query:{index:r,range:t}}).then(r=>e.table.core.mutate({trans:n,type:"deleteRange",range:t}).then(({failures:e,lastResult:t,results:n,numFailures:i})=>{if(i)throw new H("Could not delete some values",Object.keys(e).map(t=>e[t]),r-i);return r-i}))}):this.modify(tA)}}let tA=(e,t)=>t.value=null;function tC(e,t){return e<t?-1:e===t?0:1}function tj(e,t){return e>t?-1:e===t?0:1}function tD(e,t,n){var r=e instanceof tF?new e.Collection(e):e;return r._ctx.error=n?new n(t):TypeError(t),r}function tI(e){return new e.Collection(e,()=>tR("")).limit(0)}function tB(e,t,n,r){var i,a,s,o,l,u,c,h=n.length;if(!n.every(e=>"string"==typeof e))return tD(e,ta);function d(e){i="next"===e?e=>e.toUpperCase():e=>e.toLowerCase(),a="next"===e?e=>e.toLowerCase():e=>e.toUpperCase(),s="next"===e?tC:tj;var t=n.map(function(e){return{lower:a(e),upper:i(e)}}).sort(function(e,t){return s(e.lower,t.lower)});o=t.map(function(e){return e.upper}),l=t.map(function(e){return e.lower}),u=e,c="next"===e?"":r}d("next");var f=new e.Collection(e,()=>tT(o[0],l[h-1]+r));f._ondirectionchange=function(e){d(e)};var p=0;return f._addAlgorithm(function(e,n,r){var i=e.key;if("string"!=typeof i)return!1;var d=a(i);if(t(d,l,p))return!0;for(var f=null,y=p;y<h;++y){var m=function(e,t,n,r,i,a){for(var s=Math.min(e.length,r.length),o=-1,l=0;l<s;++l){var u=t[l];if(u!==r[l])return 0>i(e[l],n[l])?e.substr(0,l)+n[l]+n.substr(l+1):0>i(e[l],r[l])?e.substr(0,l)+r[l]+n.substr(l+1):o>=0?e.substr(0,o)+t[o]+n.substr(o+1):null;0>i(e[l],u)&&(o=l)}return s<r.length&&"next"===a?e+n.substr(e.length):s<e.length&&"prev"===a?e.substr(0,n.length):o<0?null:e.substr(0,o)+r[o]+n.substr(o+1)}(i,d,o[y],l[y],s,u);null===m&&null===f?p=y+1:(null===f||s(f,m)>0)&&(f=m)}return n(null!==f?function(){e.continue(f+c)}:r),!1}),f}function tT(e,t,n,r){return{type:2,lower:e,upper:t,lowerOpen:n,upperOpen:r}}function tR(e){return{type:1,lower:e,upper:e}}class tF{get Collection(){return this._ctx.table.db.Collection}between(e,t,n,r){n=!1!==n,r=!0===r;try{return this._cmp(e,t)>0||0===this._cmp(e,t)&&(n||r)&&(!n||!r)?tI(this):new this.Collection(this,()=>tT(e,t,!n,!r))}catch(e){return tD(this,ti)}}equals(e){return null==e?tD(this,ti):new this.Collection(this,()=>tR(e))}above(e){return null==e?tD(this,ti):new this.Collection(this,()=>tT(e,void 0,!0))}aboveOrEqual(e){return null==e?tD(this,ti):new this.Collection(this,()=>tT(e,void 0,!1))}below(e){return null==e?tD(this,ti):new this.Collection(this,()=>tT(void 0,e,!1,!0))}belowOrEqual(e){return null==e?tD(this,ti):new this.Collection(this,()=>tT(void 0,e))}startsWith(e){return"string"!=typeof e?tD(this,ta):this.between(e,e+tn,!0,!0)}startsWithIgnoreCase(e){return""===e?this.startsWith(e):tB(this,(e,t)=>0===e.indexOf(t[0]),[e],tn)}equalsIgnoreCase(e){return tB(this,(e,t)=>e===t[0],[e],"")}anyOfIgnoreCase(){var e=F.apply(R,arguments);return 0===e.length?tI(this):tB(this,(e,t)=>-1!==t.indexOf(e),e,"")}startsWithAnyOfIgnoreCase(){var e=F.apply(R,arguments);return 0===e.length?tI(this):tB(this,(e,t)=>t.some(t=>0===e.indexOf(t)),e,tn)}anyOf(){let e=F.apply(R,arguments),t=this._cmp;try{e.sort(t)}catch(e){return tD(this,ti)}if(0===e.length)return tI(this);let n=new this.Collection(this,()=>tT(e[0],e[e.length-1]));n._ondirectionchange=n=>{t="next"===n?this._ascending:this._descending,e.sort(t)};let r=0;return n._addAlgorithm((n,i,a)=>{let s=n.key;for(;t(s,e[r])>0;)if(++r===e.length)return i(a),!1;return 0===t(s,e[r])||(i(()=>{n.continue(e[r])}),!1)}),n}notEqual(e){return this.inAnyRange([[tr,e],[e,this.db._maxKey]],{includeLowers:!1,includeUppers:!1})}noneOf(){let e=F.apply(R,arguments);if(0===e.length)return new this.Collection(this);try{e.sort(this._ascending)}catch(e){return tD(this,ti)}let t=e.reduce((e,t)=>e?e.concat([[e[e.length-1][1],t]]):[[tr,t]],null);return t.push([e[e.length-1],this.db._maxKey]),this.inAnyRange(t,{includeLowers:!1,includeUppers:!1})}inAnyRange(e,t){let n=this._cmp,r=this._ascending,i=this._descending,a=this._min,s=this._max;if(0===e.length)return tI(this);if(!e.every(e=>void 0!==e[0]&&void 0!==e[1]&&0>=r(e[0],e[1])))return tD(this,"First argument to inAnyRange() must be an Array of two-value Arrays [lower,upper] where upper must not be lower than lower",J.InvalidArgument);let o=!t||!1!==t.includeLowers,l=t&&!0===t.includeUppers,u,c=r;function h(e,t){return c(e[0],t[0])}try{(u=e.reduce(function(e,t){let r=0,i=e.length;for(;r<i;++r){let i=e[r];if(0>n(t[0],i[1])&&n(t[1],i[0])>0){i[0]=a(i[0],t[0]),i[1]=s(i[1],t[1]);break}}return r===i&&e.push(t),e},[])).sort(h)}catch(e){return tD(this,ti)}let d=0,f=l?e=>r(e,u[d][1])>0:e=>r(e,u[d][1])>=0,p=o?e=>i(e,u[d][0])>0:e=>i(e,u[d][0])>=0,y=f,m=new this.Collection(this,()=>tT(u[0][0],u[u.length-1][1],!o,!l));return m._ondirectionchange=e=>{"next"===e?(y=f,c=r):(y=p,c=i),u.sort(h)},m._addAlgorithm((e,t,n)=>{for(var i=e.key;y(i);)if(++d===u.length)return t(n),!1;return!!(!f(i)&&!p(i))||(0===this._cmp(i,u[d][1])||0===this._cmp(i,u[d][0])||t(()=>{c===r?e.continue(u[d][0]):e.continue(u[d][1])}),!1)}),m}startsWithAnyOf(){let e=F.apply(R,arguments);return e.every(e=>"string"==typeof e)?0===e.length?tI(this):this.inAnyRange(e.map(e=>[e,e+tn])):tD(this,"startsWithAnyOf() only works with strings")}}function tM(e){return eV(function(t){return tN(t),e(t.target.error),!1})}function tN(e){e.stopPropagation&&e.stopPropagation(),e.preventDefault&&e.preventDefault()}let tq="storagemutated",t$="x-storagemutated-1",tU=tm(null,tq);class tL{_lock(){return _(!eO.global),++this._reculock,1!==this._reculock||eO.global||(eO.lockOwnerFor=this),this}_unlock(){if(_(!eO.global),0==--this._reculock)for(eO.global||(eO.lockOwnerFor=null);this._blockedFuncs.length>0&&!this._locked();){var e=this._blockedFuncs.shift();try{e4(e[1],e[0])}catch(e){}}return this}_locked(){return this._reculock&&eO.lockOwnerFor!==this}create(e){if(!this.mode)return this;let t=this.db.idbdb,n=this.db._state.dbOpenError;if(_(!this.idbtrans),!e&&!t)switch(n&&n.name){case"DatabaseClosedError":throw new J.DatabaseClosed(n);case"MissingAPIError":throw new J.MissingAPI(n.message,n);default:throw new J.OpenFailed(n)}if(!this.active)throw new J.TransactionInactive;return _(null===this._completion._state),(e=this.idbtrans=e||(this.db.core?this.db.core.transaction(this.storeNames,this.mode,{durability:this.chromeTransactionDurability}):t.transaction(this.storeNames,this.mode,{durability:this.chromeTransactionDurability}))).onerror=eV(t=>{tN(t),this._reject(e.error)}),e.onabort=eV(t=>{tN(t),this.active&&this._reject(new J.Abort(e.error)),this.active=!1,this.on("abort").fire(t)}),e.oncomplete=eV(()=>{this.active=!1,this._resolve(),"mutatedParts"in e&&tU.storagemutated.fire(e.mutatedParts)}),this}_promise(e,t,n){if("readwrite"===e&&"readwrite"!==this.mode)return te(new J.ReadOnly("Transaction is readonly"));if(!this.active)return te(new J.TransactionInactive);if(this._locked())return new ej((r,i)=>{this._blockedFuncs.push([()=>{this._promise(e,t,n).then(r,i)},eO])});if(n)return eX(()=>{var e=new ej((e,n)=>{this._lock();let r=t(e,n,this);r&&r.then&&r.then(e,n)});return e.finally(()=>this._unlock()),e._lib=!0,e});var r=new ej((e,n)=>{var r=t(e,n,this);r&&r.then&&r.then(e,n)});return r._lib=!0,r}_root(){return this.parent?this.parent._root():this}waitFor(e){var t=this._root();let n=ej.resolve(e);if(t._waitingFor)t._waitingFor=t._waitingFor.then(()=>n);else{t._waitingFor=n,t._waitingQueue=[];var r=t.idbtrans.objectStore(t.storeNames[0]);!function e(){for(++t._spinCount;t._waitingQueue.length;)t._waitingQueue.shift()();t._waitingFor&&(r.get(-1/0).onsuccess=e)}()}var i=t._waitingFor;return new ej((e,r)=>{n.then(n=>t._waitingQueue.push(eV(e.bind(null,n))),e=>t._waitingQueue.push(eV(r.bind(null,e)))).finally(()=>{t._waitingFor===i&&(t._waitingFor=null)})})}abort(){this.active&&(this.active=!1,this.idbtrans&&this.idbtrans.abort(),this._reject(new J.Abort))}table(e){let t=this._memoizedTables||(this._memoizedTables={});if(c(t,e))return t[e];let n=this.schema[e];if(!n)throw new J.NotFound("Table "+e+" not part of transaction");let r=new this.db.Table(e,n,this);return r.core=this.db.core.table(e),t[e]=r,r}}function tV(e,t,n,r,i,a,s){return{name:e,keyPath:t,unique:n,multi:r,auto:i,compound:a,src:(n&&!s?"&":"")+(r?"*":"")+(i?"++":"")+tW(t)}}function tW(e){return"string"==typeof e?e:e?"["+[].join.call(e,"+")+"]":""}function tY(e,t,n){return{name:e,primKey:t,indexes:n,mappedClass:null,idxByName:x(n,e=>[e.name,e])}}let tz=e=>{try{return e.only([[]]),tz=()=>[[]],[[]]}catch(e){return tz=()=>tn,tn}};function tG(e){return null==e?()=>{}:"string"==typeof e?1===e.split(".").length?t=>t[e]:t=>k(t,e):t=>k(t,e)}function tH(e){return[].slice.call(e)}let tQ=0;function tX(e){return null==e?":id":"string"==typeof e?e:`[${e.join("+")}]`}function tJ({_novip:e},t){let n=t.db,r=function(e,t,{IDBKeyRange:n,indexedDB:r},i){var a;return{dbcore:(a=function(e,t,n){function r(e){if(3===e.type)return null;if(4===e.type)throw Error("Cannot convert never type to IDBKeyRange");let{lower:n,upper:r,lowerOpen:i,upperOpen:a}=e;return void 0===n?void 0===r?null:t.upperBound(r,!!a):void 0===r?t.lowerBound(n,!!i):t.bound(n,r,!!i,!!a)}let{schema:i,hasGetAll:a}=function(e,t){let n=tH(e.objectStoreNames);return{schema:{name:e.name,tables:n.map(e=>t.objectStore(e)).map(e=>{let{keyPath:t,autoIncrement:n}=e,r=s(t),i=null==t,a={},o={name:e.name,primaryKey:{name:null,isPrimaryKey:!0,outbound:i,compound:r,keyPath:t,autoIncrement:n,unique:!0,extractKey:tG(t)},indexes:tH(e.indexNames).map(t=>e.index(t)).map(e=>{let{name:t,unique:n,multiEntry:r,keyPath:i}=e,o={name:t,compound:s(i),keyPath:i,unique:n,multiEntry:r,extractKey:tG(i)};return a[tX(i)]=o,o}),getIndexByKeyPath:e=>a[tX(e)]};return a[":id"]=o.primaryKey,null!=t&&(a[tX(t)]=o.primaryKey),o})},hasGetAll:n.length>0&&"getAll"in t.objectStore(n[0])&&!("undefined"!=typeof navigator&&/Safari/.test(navigator.userAgent)&&!/(Chrome\/|Edge\/)/.test(navigator.userAgent)&&[].concat(navigator.userAgent.match(/Safari\/(\d*)/))[1]<604)}}(e,n),o=i.tables.map(e=>(function(e){let t=e.name;return{name:t,schema:e,mutate:function({trans:e,type:n,keys:i,values:a,range:s}){return new Promise((o,l)=>{let u;o=eV(o);let c=e.objectStore(t),h=null==c.keyPath,d="put"===n||"add"===n;if(!d&&"delete"!==n&&"deleteRange"!==n)throw Error("Invalid operation type: "+n);let{length:f}=i||a||{length:1};if(i&&a&&i.length!==a.length)throw Error("Given keys array must have same length as given values array.");if(0===f)return o({numFailures:0,failures:{},results:[],lastResult:void 0});let p=[],y=[],m=0,v=e=>{++m,tN(e)};if("deleteRange"===n){if(4===s.type)return o({numFailures:m,failures:y,results:[],lastResult:void 0});3===s.type?p.push(u=c.clear()):p.push(u=c.delete(r(s)))}else{let[e,t]=d?h?[a,i]:[a,null]:[i,null];if(d)for(let r=0;r<f;++r)p.push(u=t&&void 0!==t[r]?c[n](e[r],t[r]):c[n](e[r])),u.onerror=v;else for(let t=0;t<f;++t)p.push(u=c[n](e[t])),u.onerror=v}let g=e=>{let t=e.target.result;p.forEach((e,t)=>null!=e.error&&(y[t]=e.error)),o({numFailures:m,failures:y,results:"delete"===n?i:p.map(e=>e.result),lastResult:t})};u.onerror=e=>{v(e),g(e)},u.onsuccess=g})},getMany:({trans:e,keys:n})=>new Promise((r,i)=>{r=eV(r);let a=e.objectStore(t),s=n.length,o=Array(s),l,u=0,c=0,h=e=>{let t=e.target;o[t._pos]=t.result,++c===u&&r(o)},d=tM(i);for(let e=0;e<s;++e)null!=n[e]&&((l=a.get(n[e]))._pos=e,l.onsuccess=h,l.onerror=d,++u);0===u&&r(o)}),get:({trans:e,key:n})=>new Promise((r,i)=>{r=eV(r);let a=e.objectStore(t).get(n);a.onsuccess=e=>r(e.target.result),a.onerror=tM(i)}),query:e=>new Promise((n,i)=>{n=eV(n);let{trans:s,values:o,limit:l,query:u}=e,c=l===1/0?void 0:l,{index:h,range:d}=u,f=s.objectStore(t),p=h.isPrimaryKey?f:f.index(h.name),y=r(d);if(0===l)return n({result:[]});if(a){let e=o?p.getAll(y,c):p.getAllKeys(y,c);e.onsuccess=e=>n({result:e.target.result}),e.onerror=tM(i)}else{let e=0,t=!o&&"openKeyCursor"in p?p.openKeyCursor(y):p.openCursor(y),r=[];t.onsuccess=i=>{let a=t.result;return a?(r.push(o?a.value:a.primaryKey),++e===l?n({result:r}):void a.continue()):n({result:r})},t.onerror=tM(i)}}),openCursor:function({trans:e,values:n,query:i,reverse:a,unique:s}){return new Promise((o,l)=>{o=eV(o);let{index:u,range:c}=i,h=e.objectStore(t),d=u.isPrimaryKey?h:h.index(u.name),f=a?s?"prevunique":"prev":s?"nextunique":"next",p=!n&&"openKeyCursor"in d?d.openKeyCursor(r(c),f):d.openCursor(r(c),f);p.onerror=tM(l),p.onsuccess=eV(t=>{let n=p.result;if(!n)return void o(null);n.___id=++tQ,n.done=!1;let r=n.continue.bind(n),i=n.continuePrimaryKey;i&&(i=i.bind(n));let a=n.advance.bind(n),s=()=>{throw Error("Cursor not stopped")};n.trans=e,n.stop=n.continue=n.continuePrimaryKey=n.advance=()=>{throw Error("Cursor not started")},n.fail=eV(l),n.next=function(){let e=1;return this.start(()=>e--?this.continue():this.stop()).then(()=>this)},n.start=e=>{let t=new Promise((e,t)=>{e=eV(e),p.onerror=tM(t),n.fail=t,n.stop=t=>{n.stop=n.continue=n.continuePrimaryKey=n.advance=s,e(t)}}),o=()=>{if(p.result)try{e()}catch(e){n.fail(e)}else n.done=!0,n.start=()=>{throw Error("Cursor behind last entry")},n.stop()};return p.onsuccess=eV(e=>{p.onsuccess=o,o()}),n.continue=r,n.continuePrimaryKey=i,n.advance=a,o(),t},o(n)},l)})},count({query:e,trans:n}){let{index:i,range:a}=e;return new Promise((e,s)=>{let o=n.objectStore(t),l=i.isPrimaryKey?o:o.index(i.name),u=r(a),c=u?l.count(u):l.count();c.onsuccess=eV(t=>e(t.target.result)),c.onerror=tM(s)})}}})(e)),l={};return o.forEach(e=>l[e.name]=e),{stack:"dbcore",transaction:e.transaction.bind(e),table(e){if(!l[e])throw Error(`Table '${e}' not found`);return l[e]},MIN_KEY:-1/0,MAX_KEY:tz(t),schema:i}}(t,n,i),e.dbcore.reduce((e,{create:t})=>({...e,...t(e)}),a))}}(e._middlewares,n,e._deps,t);e.core=r.dbcore,e.tables.forEach(t=>{let n=t.name;e.core.schema.tables.some(e=>e.name===n)&&(t.core=e.core.table(n),e[n]instanceof e.Table&&(e[n].core=t.core))})}function tZ({_novip:e},t,n,r){n.forEach(n=>{let i=r[n];t.forEach(t=>{let r=m(t,n);(!r||"value"in r&&void 0===r.value)&&(t===e.Transaction.prototype||t instanceof e.Transaction?f(t,n,{get(){return this.table(n)},set(e){d(this,n,{value:e,writable:!0,configurable:!0,enumerable:!0})}}):t[n]=new e.Table(n,i))})})}function t0({_novip:e},t){t.forEach(t=>{for(let n in t)t[n]instanceof e.Table&&delete t[n]})}function t1(e,t){return e._cfg.version-t._cfg.version}function t2(e,t){let n;let r={del:[],add:[],change:[]};for(n in e)t[n]||r.del.push(n);for(n in t){let i=e[n],a=t[n];if(i){let e={name:n,def:a,recreate:!1,del:[],add:[],change:[]};if(""+(i.primKey.keyPath||"")==""+(a.primKey.keyPath||"")&&(i.primKey.auto===a.primKey.auto||to)){let t;let n=i.idxByName,s=a.idxByName;for(t in n)s[t]||e.del.push(t);for(t in s){let r=n[t],i=s[t];r?r.src!==i.src&&e.change.push(i):e.add.push(i)}(e.del.length>0||e.add.length>0||e.change.length>0)&&r.change.push(e)}else e.recreate=!0,r.change.push(e)}else r.add.push([n,a])}return r}function t3(e,t,n,r){let i=e.db.createObjectStore(t,n.keyPath?{keyPath:n.keyPath,autoIncrement:n.auto}:{autoIncrement:n.auto});return r.forEach(e=>t8(i,e)),i}function t8(e,t){e.createIndex(t.name,t.keyPath,{unique:t.unique,multiEntry:t.multi})}function t4(e,t,n){let r={};return g(t.objectStoreNames,0).forEach(e=>{let t=n.objectStore(e),i=t.keyPath,a=tV(tW(i),i||"",!1,!1,!!t.autoIncrement,i&&"string"!=typeof i,!0),s=[];for(let e=0;e<t.indexNames.length;++e){let n=t.index(t.indexNames[e]);i=n.keyPath;var o=tV(n.name,i,!!n.unique,!!n.multiEntry,!1,i&&"string"!=typeof i,!1);s.push(o)}r[e]=tY(e,a,s)}),r}function t9({_novip:e},t,n){let r=n.db.objectStoreNames;for(let i=0;i<r.length;++i){let a=r[i],s=n.objectStore(a);e._hasGetAll="getAll"in s;for(let e=0;e<s.indexNames.length;++e){let n=s.indexNames[e],r=s.index(n).keyPath,i="string"==typeof r?r:"["+g(r).join("+")+"]";if(t[a]){let e=t[a].idxByName[i];e&&(e.name=n,delete t[a].idxByName[i],t[a].idxByName[n]=e)}}}"undefined"!=typeof navigator&&/Safari/.test(navigator.userAgent)&&!/(Chrome\/|Edge\/)/.test(navigator.userAgent)&&i.WorkerGlobalScope&&i instanceof i.WorkerGlobalScope&&[].concat(navigator.userAgent.match(/Safari\/(\d*)/))[1]<604&&(e._hasGetAll=!1)}class t6{_parseStoresSpec(e,t){a(e).forEach(n=>{if(null!==e[n]){var r=e[n].split(",").map((e,t)=>{let n=(e=e.trim()).replace(/([&*]|\+\+)/g,""),r=/^\[/.test(n)?n.match(/^\[(.*)\]$/)[1].split("+"):n;return tV(n,r||null,/\&/.test(e),/\*/.test(e),/\+\+/.test(e),s(r),0===t)}),i=r.shift();if(i.multi)throw new J.Schema("Primary key cannot be multi-valued");r.forEach(e=>{if(e.auto)throw new J.Schema("Only primary key can be marked as autoIncrement (++)");if(!e.keyPath)throw new J.Schema("Index must have a name and cannot be an empty string")}),t[n]=tY(n,i,r)}})}stores(e){let t=this.db;this._cfg.storesSource=this._cfg.storesSource?o(this._cfg.storesSource,e):e;let n=t._versions,r={},i={};return n.forEach(e=>{o(r,e._cfg.storesSource),i=e._cfg.dbschema={},e._parseStoresSpec(r,i)}),t._dbSchema=i,t0(t,[t._allTables,t,t.Transaction.prototype]),tZ(t,[t._allTables,t,t.Transaction.prototype,this._cfg.tables],a(i),i),t._storeNames=a(i),this}upgrade(e){return this._cfg.contentUpgrade=eu(this._cfg.contentUpgrade||et,e),this}}function t5(e,t){let n=e._dbNamesDB;return n||(n=e._dbNamesDB=new nv(tu,{addons:[],indexedDB:e,IDBKeyRange:t})).version(1).stores({dbnames:"name"}),n.table("dbnames")}function t7(e){return e&&"function"==typeof e.databases}function ne(e){return eX(function(){return eO.letThrough=!0,e()})}function nt(e){var t=t=>e.next(t),n=i(t),r=i(t=>e.throw(t));function i(e){return t=>{var i=e(t),a=i.value;return i.done?a:a&&"function"==typeof a.then?a.then(n,r):s(a)?Promise.all(a).then(n,r):n(a)}}return i(t)()}function nn(e,t,n){var r=arguments.length;if(r<2)throw new J.InvalidArgument("Too few arguments");for(var i=Array(r-1);--r;)i[r-1]=arguments[r];return n=i.pop(),[e,O(i),n]}function nr(e,t,n){let r=s(e)?e.slice():[e];for(let e=0;e<n;++e)r.push(t);return r}let ni={stack:"dbcore",name:"VirtualIndexMiddleware",level:1,create:function(e){return{...e,table(t){let n=e.table(t),{schema:r}=n,i={},a=[];function s(e,t,n){let r=tX(e),o=i[r]=i[r]||[],l=null==e?0:"string"==typeof e?1:e.length,u=t>0,c={...n,isVirtual:u,keyTail:t,keyLength:l,extractKey:tG(e),unique:!u&&n.unique};return o.push(c),c.isPrimaryKey||a.push(c),l>1&&s(2===l?e[0]:e.slice(0,l-1),t+1,n),o.sort((e,t)=>e.keyTail-t.keyTail),c}let o=s(r.primaryKey.keyPath,0,r.primaryKey);for(let e of(i[":id"]=[o],r.indexes))s(e.keyPath,0,e);function l(t){var n,r;let i=t.query.index;return i.isVirtual?{...t,query:{index:i,range:(n=t.query.range,r=i.keyTail,{type:1===n.type?2:n.type,lower:nr(n.lower,n.lowerOpen?e.MAX_KEY:e.MIN_KEY,r),lowerOpen:!0,upper:nr(n.upper,n.upperOpen?e.MIN_KEY:e.MAX_KEY,r),upperOpen:!0})}}:t}return{...n,schema:{...r,primaryKey:o,indexes:a,getIndexByKeyPath:function(e){let t=i[tX(e)];return t&&t[0]}},count:e=>n.count(l(e)),query:e=>n.query(l(e)),openCursor(t){let{keyTail:r,isVirtual:i,keyLength:a}=t.query.index;return i?n.openCursor(l(t)).then(n=>n&&Object.create(n,{continue:{value:function(i){null!=i?n.continue(nr(i,t.reverse?e.MAX_KEY:e.MIN_KEY,r)):t.unique?n.continue(n.key.slice(0,a).concat(t.reverse?e.MIN_KEY:e.MAX_KEY,r)):n.continue()}},continuePrimaryKey:{value(t,i){n.continuePrimaryKey(nr(t,e.MAX_KEY,r),i)}},primaryKey:{get:()=>n.primaryKey},key:{get(){let e=n.key;return 1===a?e[0]:e.slice(0,a)}},value:{get:()=>n.value}})):n.openCursor(t)}}}}}};function na(e,t,n,r){return n=n||{},r=r||"",a(e).forEach(i=>{if(c(t,i)){var a=e[i],s=t[i];if("object"==typeof a&&"object"==typeof s&&a&&s){let e=I(a);e!==I(s)?n[r+i]=t[i]:"Object"===e?na(a,s,n,r+i+"."):a!==s&&(n[r+i]=t[i])}else a!==s&&(n[r+i]=t[i])}else n[r+i]=void 0}),a(t).forEach(i=>{c(e,i)||(n[r+i]=t[i])}),n}let ns={stack:"dbcore",name:"HooksMiddleware",level:2,create:e=>({...e,table(t){let n=e.table(t),{primaryKey:r}=n.schema;return{...n,mutate(e){let i=eO.trans,{deleting:a,creating:s,updating:o}=i.table(t).hook;switch(e.type){case"add":if(s.fire===et)break;return i._promise("readwrite",()=>l(e),!0);case"put":if(s.fire===et&&o.fire===et)break;return i._promise("readwrite",()=>l(e),!0);case"delete":if(a.fire===et)break;return i._promise("readwrite",()=>l(e),!0);case"deleteRange":if(a.fire===et)break;return i._promise("readwrite",()=>(function e(t,i,a){return n.query({trans:t,values:!1,query:{index:r,range:i},limit:a}).then(({result:n})=>l({type:"delete",keys:n,trans:t}).then(r=>r.numFailures>0?Promise.reject(r.failures[0]):n.length<a?{failures:[],numFailures:0,lastResult:void 0}:e(t,{...i,lower:n[n.length-1],lowerOpen:!0},a)))})(e.trans,e.range,1e4),!0)}return n.mutate(e);function l(e){var t,i;let l=eO.trans,u=e.keys||("delete"===(t=e).type?t.keys:t.keys||t.values.map(r.extractKey));if(!u)throw Error("Keys missing");return"delete"!==(e="add"===e.type||"put"===e.type?{...e,keys:u}:{...e}).type&&(e.values=[...e.values]),e.keys&&(e.keys=[...e.keys]),("add"===(i=e).type?Promise.resolve([]):n.getMany({trans:i.trans,keys:u,cache:"immutable"})).then(t=>{let i=u.map((n,i)=>{let u=t[i],h={onerror:null,onsuccess:null};if("delete"===e.type)a.fire.call(h,n,u,l);else if("add"===e.type||void 0===u){let t=s.fire.call(h,n,e.values[i],l);null==n&&null!=t&&(n=t,e.keys[i]=n,r.outbound||E(e.values[i],r.keyPath,n))}else{let t=na(u,e.values[i]),r=o.fire.call(h,t,n,u,l);if(r){let t=e.values[i];Object.keys(r).forEach(e=>{c(t,e)?t[e]=r[e]:E(t,e,r[e])})}}return h});return n.mutate(e).then(({failures:n,results:r,numFailures:a,lastResult:s})=>{for(let a=0;a<u.length;++a){let s=r?r[a]:u[a],o=i[a];null==s?o.onerror&&o.onerror(n[a]):o.onsuccess&&o.onsuccess("put"===e.type&&t[a]?e.values[a]:s)}return{failures:n,results:r,numFailures:a,lastResult:s}}).catch(e=>(i.forEach(t=>t.onerror&&t.onerror(e)),Promise.reject(e)))})}}}}})};function no(e,t,n){try{if(!t||t.keys.length<e.length)return null;let r=[];for(let i=0,a=0;i<t.keys.length&&a<e.length;++i)0===tP(t.keys[i],e[a])&&(r.push(n?j(t.values[i]):t.values[i]),++a);return r.length===e.length?r:null}catch(e){return null}}let nl={stack:"dbcore",level:-1,create:e=>({table:t=>{let n=e.table(t);return{...n,getMany:e=>{if(!e.cache)return n.getMany(e);let t=no(e.keys,e.trans._cache,"clone"===e.cache);return t?ej.resolve(t):n.getMany(e).then(t=>(e.trans._cache={keys:e.keys,values:"clone"===e.cache?j(t):t},t))},mutate:e=>("add"!==e.type&&(e.trans._cache=null),n.mutate(e))}}})};function nu(e){return!("from"in e)}let nc=function(e,t){if(!this){let t=new nc;return e&&"d"in e&&o(t,e),t}o(this,arguments.length?{d:1,from:e,to:arguments.length>1?t:e}:{d:0})};function nh(e,t,n){let r=tP(t,n);if(isNaN(r))return;if(r>0)throw RangeError();if(nu(e))return o(e,{from:t,to:n,d:1});let i=e.l,a=e.r;if(0>tP(n,e.from))return i?nh(i,t,n):e.l={from:t,to:n,d:1,l:null,r:null},np(e);if(tP(t,e.to)>0)return a?nh(a,t,n):e.r={from:t,to:n,d:1,l:null,r:null},np(e);0>tP(t,e.from)&&(e.from=t,e.l=null,e.d=a?a.d+1:1),tP(n,e.to)>0&&(e.to=n,e.r=null,e.d=e.l?e.l.d+1:1);let s=!e.r;i&&!e.l&&nd(e,i),a&&s&&nd(e,a)}function nd(e,t){nu(t)||function e(t,{from:n,to:r,l:i,r:a}){nh(t,n,r),i&&e(t,i),a&&e(t,a)}(e,t)}function nf(e){let t=nu(e)?null:{s:0,n:e};return{next(e){let n=arguments.length>0;for(;t;)switch(t.s){case 0:if(t.s=1,n)for(;t.n.l&&0>tP(e,t.n.from);)t={up:t,n:t.n.l,s:1};else for(;t.n.l;)t={up:t,n:t.n.l,s:1};case 1:if(t.s=2,!n||0>=tP(e,t.n.to))return{value:t.n,done:!1};case 2:if(t.n.r){t.s=3,t={up:t,n:t.n.r,s:0};continue}case 3:t=t.up}return{done:!0}}}}function np(e){var t,n;let r=((null===(t=e.r)||void 0===t?void 0:t.d)||0)-((null===(n=e.l)||void 0===n?void 0:n.d)||0),i=r>1?"r":r<-1?"l":"";if(i){let t="r"===i?"l":"r",n={...e},r=e[i];e.from=r.from,e.to=r.to,e[i]=r[i],n[i]=r[t],e[t]=n,n.d=ny(n)}e.d=ny(e)}function ny({r:e,l:t}){return(e?t?Math.max(e.d,t.d):e.d:t?t.d:0)+1}h(nc.prototype,{add(e){return nd(this,e),this},addKey(e){return nh(this,e,e),this},addKeys(e){return e.forEach(e=>nh(this,e,e)),this},[B](){return nf(this)}});let nm={stack:"dbcore",level:0,create:e=>{let t=e.schema.name,n=new nc(e.MIN_KEY,e.MAX_KEY);return{...e,table:r=>{let i=e.table(r),{schema:o}=i,{primaryKey:l}=o,{extractKey:u,outbound:c}=l,h={...i,mutate:e=>{let a=e.trans,l=a.mutatedParts||(a.mutatedParts={}),u=e=>{let n=`idb://${t}/${r}/${e}`;return l[n]||(l[n]=new nc)},c=u(""),h=u(":dels"),{type:d}=e,[f,p]="deleteRange"===e.type?[e.range]:"delete"===e.type?[e.keys]:e.values.length<50?[[],e.values]:[],y=e.trans._cache;return i.mutate(e).then(e=>{if(s(f)){"delete"!==d&&(f=e.results),c.addKeys(f);let t=no(f,y);t||"add"===d||h.addKeys(f),(t||p)&&function(e,t,n,r){t.indexes.forEach(function(t){let i=e(t.name||"");function a(e){return null!=e?t.extractKey(e):null}let o=e=>t.multiEntry&&s(e)?e.forEach(e=>i.addKey(e)):i.addKey(e);(n||r).forEach((e,t)=>{let i=n&&a(n[t]),s=r&&a(r[t]);0!==tP(i,s)&&(null!=i&&o(i),null!=s&&o(s))})})}(u,o,t,p)}else if(f){let e={from:f.lower,to:f.upper};h.add(e),c.add(e)}else c.add(n),h.add(n),o.indexes.forEach(e=>u(e.name).add(n));return e})}},d=({query:{index:t,range:n}})=>{var r,i;return[t,new nc(null!==(r=n.lower)&&void 0!==r?r:e.MIN_KEY,null!==(i=n.upper)&&void 0!==i?i:e.MAX_KEY)]},f={get:e=>[l,new nc(e.key)],getMany:e=>[l,(new nc).addKeys(e.keys)],count:d,query:d,openCursor:d};return a(f).forEach(e=>{h[e]=function(a){let{subscr:s}=eO;if(s){let o=e=>{let n=`idb://${t}/${r}/${e}`;return s[n]||(s[n]=new nc)},l=o(""),h=o(":dels"),[d,p]=f[e](a);if(o(d.name||"").add(p),!d.isPrimaryKey){if("count"!==e){let t="query"===e&&c&&a.values&&i.query({...a,values:!1});return i[e].apply(this,arguments).then(n=>{if("query"===e){if(c&&a.values)return t.then(({result:e})=>(l.addKeys(e),n));let e=a.values?n.result.map(u):n.result;a.values?l.addKeys(e):h.addKeys(e)}else if("openCursor"===e){let e=a.values;return n&&Object.create(n,{key:{get:()=>(h.addKey(n.primaryKey),n.key)},primaryKey:{get(){let e=n.primaryKey;return h.addKey(e),e}},value:{get:()=>(e&&l.addKey(n.primaryKey),n.value)}})}return n})}h.add(n)}}return i[e].apply(this,arguments)}}),h}}}};class nv{constructor(e,t){var n,r,i;this._middlewares={},this.verno=0;let a=nv.dependencies;this._options=t={addons:nv.addons,autoOpen:!0,indexedDB:a.indexedDB,IDBKeyRange:a.IDBKeyRange,...t},this._deps={indexedDB:t.indexedDB,IDBKeyRange:t.IDBKeyRange};let{addons:s}=t;this._dbSchema={},this._versions=[],this._storeNames=[],this._allTables={},this.idbdb=null,this._novip=this;let o={dbOpenError:null,isBeingOpened:!1,onReadyBeingFired:null,openComplete:!1,dbReadyResolve:et,dbReadyPromise:null,cancelOpen:et,openCanceller:null,autoSchema:!0,PR1398_maxLoop:3};o.dbReadyPromise=new ej(e=>{o.dbReadyResolve=e}),o.openCanceller=new ej((e,t)=>{o.cancelOpen=t}),this._state=o,this.name=e,this.on=tm(this,"populate","blocked","versionchange","close",{ready:[eu,et]}),this.on.ready.subscribe=b(this.on.ready.subscribe,e=>(t,n)=>{nv.vip(()=>{let r=this._state;if(r.openComplete)r.dbOpenError||ej.resolve().then(t),n&&e(t);else if(r.onReadyBeingFired)r.onReadyBeingFired.push(t),n&&e(t);else{e(t);let r=this;n||e(function e(){r.on.ready.unsubscribe(t),r.on.ready.unsubscribe(e)})}})}),this.Collection=(n=this,tv(tS.prototype,function(e,t){this.db=n;let r=tf,i=null;if(t)try{r=t()}catch(e){i=e}let a=e._ctx,s=a.table,o=s.hook.reading.fire;this._ctx={table:s,index:a.index,isPrimKey:!a.index||s.schema.primKey.keyPath&&a.index===s.schema.primKey.name,range:r,keysOnly:!1,dir:"next",unique:"",algorithm:null,filter:null,replayFilter:null,justLimit:!0,isMatch:null,offset:0,limit:1/0,error:i,or:a.or,valueMapper:o!==en?o:null}})),this.Table=function(e){return tv(ty.prototype,function(t,n,r){this.db=e,this._tx=r,this.name=t,this.schema=n,this.hook=e._allTables[t]?e._allTables[t].hook:tm(null,{creating:[ea,et],reading:[er,en],updating:[eo,et],deleting:[es,et]})})}(this),this.Transaction=(r=this,tv(tL.prototype,function(e,t,n,i,a){this.db=r,this.mode=e,this.storeNames=t,this.schema=n,this.chromeTransactionDurability=i,this.idbtrans=null,this.on=tm(this,"complete","error","abort"),this.parent=a||null,this.active=!0,this._reculock=0,this._blockedFuncs=[],this._resolve=null,this._reject=null,this._waitingFor=null,this._waitingQueue=null,this._spinCount=0,this._completion=new ej((e,t)=>{this._resolve=e,this._reject=t}),this._completion.then(()=>{this.active=!1,this.on.complete.fire()},e=>{var t=this.active;return this.active=!1,this.on.error.fire(e),this.parent?this.parent._reject(e):t&&this.idbtrans&&this.idbtrans.abort(),te(e)})})),this.Version=(i=this,tv(t6.prototype,function(e){this.db=i,this._cfg={version:e,storesSource:null,dbschema:{},tables:{},contentUpgrade:null}})),this.WhereClause=function(e){return tv(tF.prototype,function(t,n,r){this.db=e,this._ctx={table:t,index:":id"===n?null:n,or:r};let i=e._deps.indexedDB;if(!i)throw new J.MissingAPI;this._cmp=this._ascending=i.cmp.bind(i),this._descending=(e,t)=>i.cmp(t,e),this._max=(e,t)=>i.cmp(e,t)>0?e:t,this._min=(e,t)=>0>i.cmp(e,t)?e:t,this._IDBKeyRange=e._deps.IDBKeyRange})}(this),this.on("versionchange",e=>{e.newVersion>0?console.warn(`Another connection wants to upgrade database '${this.name}'. Closing db now to resume the upgrade.`):console.warn(`Another connection wants to delete database '${this.name}'. Closing db now to resume the delete request.`),this.close()}),this.on("blocked",e=>{!e.newVersion||e.newVersion<e.oldVersion?console.warn(`Dexie.delete('${this.name}') was blocked`):console.warn(`Upgrade '${this.name}' blocked by other connection holding version ${e.oldVersion/10}`)}),this._maxKey=tz(t.IDBKeyRange),this._createTransaction=(e,t,n,r)=>new this.Transaction(e,t,n,this._options.chromeTransactionDurability,r),this._fireOnBlocked=e=>{this.on("blocked").fire(e),ts.filter(e=>e.name===this.name&&e!==this&&!e._state.vcFired).map(t=>t.on("versionchange").fire(e))},this.use(ni),this.use(ns),this.use(nm),this.use(nl),this.vip=Object.create(this,{_vip:{value:!0}}),s.forEach(e=>e(this))}version(e){if(isNaN(e)||e<.1)throw new J.Type("Given version is not a positive number");if(e=Math.round(10*e)/10,this.idbdb||this._state.isBeingOpened)throw new J.Schema("Cannot add version when database is open");this.verno=Math.max(this.verno,e);let t=this._versions;var n=t.filter(t=>t._cfg.version===e)[0];return n||(n=new this.Version(e),t.push(n),t.sort(t1),n.stores({}),this._state.autoSchema=!1,n)}_whenReady(e){return this.idbdb&&(this._state.openComplete||eO.letThrough||this._vip)?e():new ej((e,t)=>{if(this._state.openComplete)return t(new J.DatabaseClosed(this._state.dbOpenError));if(!this._state.isBeingOpened){if(!this._options.autoOpen)return void t(new J.DatabaseClosed);this.open().catch(et)}this._state.dbReadyPromise.then(e,t)}).then(e)}use({stack:e,create:t,level:n,name:r}){r&&this.unuse({stack:e,name:r});let i=this._middlewares[e]||(this._middlewares[e]=[]);return i.push({stack:e,create:t,level:null==n?10:n,name:r}),i.sort((e,t)=>e.level-t.level),this}unuse({stack:e,name:t,create:n}){return e&&this._middlewares[e]&&(this._middlewares[e]=this._middlewares[e].filter(e=>n?e.create!==n:!!t&&e.name!==t)),this}open(){return function(e){var t;let n=e._state,{indexedDB:r}=e._deps;if(n.isBeingOpened||e.idbdb)return n.dbReadyPromise.then(()=>n.dbOpenError?te(n.dbOpenError):e);N&&(n.openCanceller._stackHolder=U()),n.isBeingOpened=!0,n.dbOpenError=null,n.openComplete=!1;let i=n.openCanceller;function s(){if(n.openCanceller!==i)throw new J.DatabaseClosed("db.open() was cancelled")}let o=n.dbReadyResolve,l=null,u=!1,c=()=>new ej((t,i)=>{if(s(),!r)throw new J.MissingAPI;let o=e.name,c=n.autoSchema?r.open(o):r.open(o,Math.round(10*e.verno));if(!c)throw new J.MissingAPI;c.onerror=tM(i),c.onblocked=eV(e._fireOnBlocked),c.onupgradeneeded=eV(t=>{if(l=c.transaction,n.autoSchema&&!e._options.allowEmptyDB){c.onerror=tN,l.abort(),c.result.close();let e=r.deleteDatabase(o);e.onsuccess=e.onerror=eV(()=>{i(new J.NoSuchDatabase(`Database ${o} doesnt exist`))})}else{l.onerror=tM(i);var s=t.oldVersion>0x4000000000000000?0:t.oldVersion;u=s<1,e._novip.idbdb=c.result,function(e,t,n,r){let i=e._dbSchema,s=e._createTransaction("readwrite",e._storeNames,i);s.create(n),s._completion.catch(r);let o=s._reject.bind(s),l=eO.transless||eO;eX(()=>{eO.trans=s,eO.transless=l,0===t?(a(i).forEach(e=>{t3(n,e,i[e].primKey,i[e].indexes)}),tJ(e,n),ej.follow(()=>e.on.populate.fire(s)).catch(o)):(function({_novip:e},t,n,r){let i=[],s=e._versions,o=e._dbSchema=t4(e,e.idbdb,r),l=!1;return s.filter(e=>e._cfg.version>=t).forEach(s=>{i.push(()=>{let i=o,u=s._cfg.dbschema;t9(e,i,r),t9(e,u,r),o=e._dbSchema=u;let c=t2(i,u);c.add.forEach(e=>{t3(r,e[0],e[1].primKey,e[1].indexes)}),c.change.forEach(e=>{if(e.recreate)throw new J.Upgrade("Not yet support for changing primary key");{let t=r.objectStore(e.name);e.add.forEach(e=>t8(t,e)),e.change.forEach(e=>{t.deleteIndex(e.name),t8(t,e)}),e.del.forEach(e=>t.deleteIndex(e))}});let h=s._cfg.contentUpgrade;if(h&&s._cfg.version>t){let t;tJ(e,r),n._memoizedTables={},l=!0;let s=P(u);c.del.forEach(e=>{s[e]=i[e]}),t0(e,[e.Transaction.prototype]),tZ(e,[e.Transaction.prototype],a(s),s),n.schema=s;let o=M(h);o&&eJ();let d=ej.follow(()=>{if((t=h(n))&&o){var e=eZ.bind(null,null);t.then(e,e)}});return t&&"function"==typeof t.then?ej.resolve(t):d.then(()=>t)}}),i.push(t=>{l&&to||function(e,t){[].slice.call(t.db.objectStoreNames).forEach(n=>null==e[n]&&t.db.deleteObjectStore(n))}(s._cfg.dbschema,t),t0(e,[e.Transaction.prototype]),tZ(e,[e.Transaction.prototype],e._storeNames,e._dbSchema),n.schema=e._dbSchema})}),(function e(){return i.length?ej.resolve(i.shift()(n.idbtrans)).then(e):ej.resolve()})().then(()=>{var e;a(e=o).forEach(t=>{r.db.objectStoreNames.contains(t)||t3(r,t,e[t].primKey,e[t].indexes)})})})(e,t,s,n).catch(o)})}(e,s/10,l,i)}},i),c.onsuccess=eV(()=>{l=null;let r=e._novip.idbdb=c.result,i=g(r.objectStoreNames);if(i.length>0)try{let t=r.transaction(1===i.length?i[0]:i,"readonly");n.autoSchema?function({_novip:e},t,n){e.verno=t.version/10;let r=e._dbSchema=t4(0,t,n);e._storeNames=g(t.objectStoreNames,0),tZ(e,[e._allTables],a(r),r)}(e,r,t):(t9(e,e._dbSchema,t),function(e,t){let n=t2(t4(0,e.idbdb,t),e._dbSchema);return!(n.add.length||n.change.some(e=>e.add.length||e.change.length))}(e,t)||console.warn("Dexie SchemaDiff: Schema was extended without increasing the number passed to db.version(). Some queries may fail.")),tJ(e,t)}catch(e){}ts.push(e),r.onversionchange=eV(t=>{n.vcFired=!0,e.on("versionchange").fire(t)}),r.onclose=eV(t=>{e.on("close").fire(t)}),u&&function({indexedDB:e,IDBKeyRange:t},n){t7(e)||n===tu||t5(e,t).put({name:n}).catch(et)}(e._deps,o),t()},i)}).catch(e=>e&&"UnknownError"===e.name&&n.PR1398_maxLoop>0?(n.PR1398_maxLoop--,console.warn("Dexie: Workaround for Chrome UnknownError on open()"),c()):ej.reject(e));return ej.race([i,("undefined"==typeof navigator?ej.resolve():!navigator.userAgentData&&/Safari\//.test(navigator.userAgent)&&!/Chrom(e|ium)\//.test(navigator.userAgent)&&indexedDB.databases?new Promise(function(e){var n=function(){return indexedDB.databases().finally(e)};t=setInterval(n,100),n()}).finally(function(){return clearInterval(t)}):Promise.resolve()).then(c)]).then(()=>(s(),n.onReadyBeingFired=[],ej.resolve(ne(()=>e.on.ready.fire(e.vip))).then(function t(){if(n.onReadyBeingFired.length>0){let r=n.onReadyBeingFired.reduce(eu,et);return n.onReadyBeingFired=[],ej.resolve(ne(()=>r(e.vip))).then(t)}}))).finally(()=>{n.onReadyBeingFired=null,n.isBeingOpened=!1}).then(()=>e).catch(t=>{n.dbOpenError=t;try{l&&l.abort()}catch(e){}return i===n.openCanceller&&e._close(),te(t)}).finally(()=>{n.openComplete=!0,o()})}(this)}_close(){let e=this._state,t=ts.indexOf(this);if(t>=0&&ts.splice(t,1),this.idbdb){try{this.idbdb.close()}catch(e){}this._novip.idbdb=null}e.dbReadyPromise=new ej(t=>{e.dbReadyResolve=t}),e.openCanceller=new ej((t,n)=>{e.cancelOpen=n})}close(){this._close();let e=this._state;this._options.autoOpen=!1,e.dbOpenError=new J.DatabaseClosed,e.isBeingOpened&&e.cancelOpen(e.dbOpenError)}delete(){let e=arguments.length>0,t=this._state;return new ej((n,r)=>{let i=()=>{this.close();var e=this._deps.indexedDB.deleteDatabase(this.name);e.onsuccess=eV(()=>{(function({indexedDB:e,IDBKeyRange:t},n){t7(e)||n===tu||t5(e,t).delete(n).catch(et)})(this._deps,this.name),n()}),e.onerror=tM(r),e.onblocked=this._fireOnBlocked};if(e)throw new J.InvalidArgument("Arguments not allowed in db.delete()");t.isBeingOpened?t.dbReadyPromise.then(i):i()})}backendDB(){return this.idbdb}isOpen(){return null!==this.idbdb}hasBeenClosed(){let e=this._state.dbOpenError;return e&&"DatabaseClosed"===e.name}hasFailed(){return null!==this._state.dbOpenError}dynamicallyOpened(){return this._state.autoSchema}get tables(){return a(this._allTables).map(e=>this._allTables[e])}transaction(){let e=nn.apply(this,arguments);return this._transaction.apply(this,e)}_transaction(e,t,n){let r,i,a=eO.trans;a&&a.db===this&&-1===e.indexOf("!")||(a=null);let s=-1!==e.indexOf("?");e=e.replace("!","").replace("?","");try{if(i=t.map(e=>{var t=e instanceof this.Table?e.name:e;if("string"!=typeof t)throw TypeError("Invalid table argument to Dexie.transaction(). Only Table or String are allowed");return t}),"r"==e||e===tc)r=tc;else{if("rw"!=e&&e!=th)throw new J.InvalidArgument("Invalid transaction mode: "+e);r=th}if(a){if(a.mode===tc&&r===th){if(!s)throw new J.SubTransaction("Cannot enter a sub-transaction with READWRITE mode when parent transaction is READONLY");a=null}a&&i.forEach(e=>{if(a&&-1===a.storeNames.indexOf(e)){if(!s)throw new J.SubTransaction("Table "+e+" not included in parent transaction.");a=null}}),s&&a&&!a.active&&(a=null)}}catch(e){return a?a._promise(null,(t,n)=>{n(e)}):te(e)}let o=(function e(t,n,r,i,a){return ej.resolve().then(()=>{let s;let o=eO.transless||eO,l=t._createTransaction(n,r,t._dbSchema,i);if(i)l.idbtrans=i.idbtrans;else try{l.create(),t._state.PR1398_maxLoop=3}catch(i){return i.name===X.InvalidState&&t.isOpen()&&--t._state.PR1398_maxLoop>0?(console.warn("Dexie: Need to reopen db"),t._close(),t.open().then(()=>e(t,n,r,null,a))):te(i)}let u=M(a);u&&eJ();let c=ej.follow(()=>{if(s=a.call(l,l)){if(u){var e=eZ.bind(null,null);s.then(e,e)}else"function"==typeof s.next&&"function"==typeof s.throw&&(s=nt(s))}},{trans:l,transless:o});return(s&&"function"==typeof s.then?ej.resolve(s).then(e=>l.active?e:te(new J.PrematureCommit("Transaction committed too early. See http://bit.ly/2kdckMn"))):c.then(()=>s)).then(e=>(i&&l._resolve(),l._completion.then(()=>e))).catch(e=>(l._reject(e),te(e)))})}).bind(null,this,r,i,a,n);return a?a._promise(r,o,"lock"):eO.trans?e4(eO.transless,()=>this._whenReady(o)):this._whenReady(o)}table(e){if(!c(this._allTables,e))throw new J.InvalidTable(`Table ${e} does not exist`);return this._allTables[e]}}let ng="undefined"!=typeof Symbol&&"observable"in Symbol?Symbol.observable:"@@observable";class nb{constructor(e){this._subscribe=e}subscribe(e,t,n){return this._subscribe(e&&"function"!=typeof e?e:{next:e,error:t,complete:n})}[ng](){return this}}function n_(e,t){return a(t).forEach(n=>{nd(e[n]||(e[n]=new nc),t[n])}),e}try{r={indexedDB:i.indexedDB||i.mozIndexedDB||i.webkitIndexedDB||i.msIndexedDB,IDBKeyRange:i.IDBKeyRange||i.webkitIDBKeyRange}}catch(e){r={indexedDB:null,IDBKeyRange:null}}function nw(e){let t=nx;try{nx=!0,tU.storagemutated.fire(e)}finally{nx=t}}h(nv,{...ee,delete:e=>new nv(e,{addons:[]}).delete(),exists:e=>new nv(e,{addons:[]}).open().then(e=>(e.close(),!0)).catch("NoSuchDatabaseError",()=>!1),getDatabaseNames(e){try{return(function({indexedDB:e,IDBKeyRange:t}){return t7(e)?Promise.resolve(e.databases()).then(e=>e.map(e=>e.name).filter(e=>e!==tu)):t5(e,t).toCollection().primaryKeys()})(nv.dependencies).then(e)}catch(e){return te(new J.MissingAPI)}},defineClass:()=>function(e){o(this,e)},ignoreTransaction:e=>eO.trans?e4(eO.transless,e):e(),vip:ne,async:function(e){return function(){try{var t=nt(e.apply(this,arguments));return t&&"function"==typeof t.then?t:ej.resolve(t)}catch(e){return te(e)}}},spawn:function(e,t,n){try{var r=nt(e.apply(n,t||[]));return r&&"function"==typeof r.then?r:ej.resolve(r)}catch(e){return te(e)}},currentTransaction:{get:()=>eO.trans||null},waitFor:function(e,t){let n=ej.resolve("function"==typeof e?nv.ignoreTransaction(e):e).timeout(t||6e4);return eO.trans?eO.trans.waitFor(n):n},Promise:ej,debug:{get:()=>N,set:e=>{var t;t="dexie"===e?()=>!0:tl,N=e,q=t}},derive:p,extend:o,props:h,override:b,Events:tm,on:tU,liveQuery:function(e){let t,n=!1,r=new nb(r=>{let i=M(e),s=!1,o={},l={},u={get closed(){return s},unsubscribe:()=>{s=!0,tU.storagemutated.unsubscribe(f)}};r.start&&r.start(u);let c=!1,h=!1;function d(){return a(l).some(e=>o[e]&&function(e,t){let n=nf(t),r=n.next();if(r.done)return!1;let i=r.value,a=nf(e),s=a.next(i.from),o=s.value;for(;!r.done&&!s.done;){if(0>=tP(o.from,i.to)&&tP(o.to,i.from)>=0)return!0;0>tP(i.from,o.from)?i=(r=n.next(o.from)).value:o=(s=a.next(i.from)).value}return!1}(o[e],l[e]))}let f=e=>{n_(o,e),d()&&p()},p=()=>{if(c||s)return;o={};let a={},y=function(t){i&&eJ();let n=()=>eX(e,{subscr:t,trans:null}),r=eO.trans?e4(eO.transless,n):n();return i&&r.then(eZ,eZ),r}(a);h||(tU(tq,f),h=!0),c=!0,Promise.resolve(y).then(e=>{n=!0,t=e,c=!1,s||(d()?p():(o={},l=a,r.next&&r.next(e)))},e=>{c=!1,n=!1,r.error&&r.error(e),u.unsubscribe()})};return p(),u});return r.hasValue=()=>n,r.getValue=()=>t,r},extendObservabilitySet:n_,getByKeyPath:k,setByKeyPath:E,delByKeyPath:function(e,t){"string"==typeof t?E(e,t,void 0):"length"in t&&[].map.call(t,function(t){E(e,t,void 0)})},shallowClone:P,deepClone:j,getObjectDiff:na,cmp:tP,asap:w,minKey:tr,addons:[],connections:ts,errnames:X,dependencies:r,semVer:tt,version:tt.split(".").map(e=>parseInt(e)).reduce((e,t,n)=>e+t/Math.pow(10,2*n))}),nv.maxKey=tz(nv.dependencies.IDBKeyRange),"undefined"!=typeof dispatchEvent&&"undefined"!=typeof addEventListener&&(tU(tq,e=>{if(!nx){let t;to?(t=document.createEvent("CustomEvent")).initCustomEvent(t$,!0,!0,e):t=new CustomEvent(t$,{detail:e}),nx=!0,dispatchEvent(t),nx=!1}}),addEventListener(t$,({detail:e})=>{nx||nw(e)}));let nx=!1;if("undefined"!=typeof BroadcastChannel){let e=new BroadcastChannel(t$);"function"==typeof e.unref&&e.unref(),tU(tq,t=>{nx||e.postMessage(t)}),e.onmessage=e=>{e.data&&nw(e.data)}}else if("undefined"!=typeof self&&"undefined"!=typeof navigator){tU(tq,e=>{try{nx||("undefined"!=typeof localStorage&&localStorage.setItem(t$,JSON.stringify({trig:Math.random(),changedParts:e})),"object"==typeof self.clients&&[...self.clients.matchAll({includeUncontrolled:!0})].forEach(t=>t.postMessage({type:t$,changedParts:e})))}catch(e){}}),"undefined"!=typeof addEventListener&&addEventListener("storage",e=>{if(e.key===t$){let t=JSON.parse(e.newValue);t&&nw(t.changedParts)}});let e=self.document&&navigator.serviceWorker;e&&e.addEventListener("message",function({data:e}){e&&e.type===t$&&nw(e.changedParts)})}ej.rejectionMapper=function(e,t){if(!e||e instanceof z||e instanceof TypeError||e instanceof SyntaxError||!e.name||!Z[e.name])return e;var n=new Z[e.name](t||e.message,e);return"stack"in e&&f(n,"stack",{get:function(){return this.inner.stack}}),n},q=tl}};