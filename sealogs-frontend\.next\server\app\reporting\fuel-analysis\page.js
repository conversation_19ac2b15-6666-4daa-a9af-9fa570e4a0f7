(()=>{var e={};e.id=3972,e.ids=[3972],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},38814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>d,originalPathname:()=>c,pages:()=>u,routeModule:()=>g,tree:()=>p}),r(95081),r(71238),r(78398),r(57757),r(48045);var s=r(40060),a=r(33581),n=r(57567),i=r.n(n),o=r(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let p=["",{children:["reporting",{children:["fuel-analysis",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,95081)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\fuel-analysis\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\fuel-analysis\\page.tsx"],c="/reporting/fuel-analysis/page",d={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/reporting/fuel-analysis/page",pathname:"/reporting/fuel-analysis",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},13448:(e,t,r)=>{Promise.resolve().then(r.bind(r,42520))},88332:(e,t,r)=>{Promise.resolve().then(r.bind(r,44517))},30816:(e,t,r)=>{var s=r(15903);e.exports=function(e,t,r){for(var a=-1,n=e.length;++a<n;){var i=e[a],o=t(i);if(null!=o&&(void 0===l?o==o&&!s(o):r(o,l)))var l=o,p=i}return p}},24548:e=>{e.exports=function(e,t){return e>t}},52543:e=>{e.exports=function(e,t){return e<t}},14935:e=>{e.exports=function(e){return e}},23095:(e,t,r)=>{var s=r(30816),a=r(24548),n=r(14935);e.exports=function(e){return e&&e.length?s(e,n,a):void 0}},99488:(e,t,r)=>{var s=r(30816),a=r(52543),n=r(14935);e.exports=function(e){return e&&e.length?s(e,n,a):void 0}},75546:(e,t,r)=>{"use strict";r.d(t,{Br:()=>d,fU:()=>g,o0:()=>u,p6:()=>p,vq:()=>c});var s=r(83179),a=r.n(s),n=r(7678),i=r.n(n),o=r(14826),l=r.n(o);let p=(e="",t=!0)=>{let r;if(i()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,s,a]=e.split("-"),n=t?r.slice(-2):r,i=parseInt(a,10).toString().padStart(2,"0"),o=parseInt(s,10).toString().padStart(2,"0");return`${i}/${o}/${n}`}if(!(r=e&&"object"==typeof e?a()(e.toString()):a()(e)).isValid())return"";let s=r.format("DD"),n=r.format("MM"),o=t?r.format("YY"):r.format("YYYY");return`${s}/${n}/${o}`},u=(e="",t=!0)=>{let r;if(i()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,s,a]=e.split("-"),n=t?r.slice(-2):r,i=parseInt(a,10).toString().padStart(2,"0"),o=parseInt(s,10).toString().padStart(2,"0");return`${i}/${o}/${n} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[r,s]=e.split(" "),[a,n,i]=r.split("-"),o=t?a.slice(-2):a,l=s.split(":"),p=l[0].padStart(2,"0"),u=l[1].padStart(2,"0"),c=parseInt(i,10).toString().padStart(2,"0"),d=parseInt(n,10).toString().padStart(2,"0");return`${c}/${d}/${o} ${p}:${u}`}if(!(r=e&&"object"==typeof e?a()(e.toString()):a()(e)).isValid())return"";let s=r.format("DD"),n=r.format("MM"),o=t?r.format("YY"):r.format("YYYY"),p=r.format("HH:mm");return`${s}/${n}/${o} ${p}`},c=(e="")=>i()(l()(e))?"":a()(e).format("YYYY-MM-DD HH:mm:ss"),d=(e="")=>i()(l()(e))?new Date:new Date(`${e}T10:00:00Z`),g=(e,t)=>{let r=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),s=e=>e.includes(" ")?e.replace(" ","T"):e,a=e=>{if(!e||"string"!=typeof e)return null;if(r(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(s(e))},n=a(e),i=a(t);return!n||!i||isNaN(n.getTime())||isNaN(i.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):i>n}},42520:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(98768),a=r(37042),n=r(75546),i=r(13842),o=r(94060),l=r(79418),p=r(83179),u=r.n(p),c=r(23095),d=r.n(c),g=r(99488),f=r.n(g),v=r(69424),x=r(60343),m=r(25394);let h=["Date","Start","End","Fuel Start","Fuel End","Fuel used"];function y(){let e=(0,v.useRouter)(),[t,r]=(0,x.useState)(""),[p,c]=(0,x.useState)(!1),[g,y]=(0,x.useState)(!1),[S,j]=(0,x.useState)([]),[T]=(0,l.t)(o.Js,{fetchPolicy:"cache-and-network",onCompleted:e=>{var t=[];e.readOneVessel.logBookEntries.nodes?.map(e=>e.logBookEntrySections.nodes?.map(e=>{t.push(e.id)})),L({variables:{id:t}})},onError:e=>{console.error(e)}}),[L]=(0,l.t)(o.Zl,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readTripReport_LogBookEntrySections.nodes;var r=[];t.forEach(e=>{var t=0;e.tripEvents.nodes.forEach(e=>{var r=0;"PassengerDropFacility"===e.eventCategory&&(r=0!=e.eventType_PassengerDropFacility.fuelLevel?e.eventType_PassengerDropFacility.fuelLevel:r),"Tasking"===e.eventCategory&&(r=0!=e.eventType_Tasking.fuelLevel?e.eventType_Tasking.fuelLevel:r),"PassengerDropFacility"===e.eventCategory&&(r=0!=e.eventType_PassengerDropFacility.fuelLevel?e.eventType_PassengerDropFacility.fuelLevel:r),t=d()([+t,+r])});var s=t;e.tripEvents.nodes.forEach(e=>{var t=0;"PassengerDropFacility"===e.eventCategory&&(t=0!=e.eventType_PassengerDropFacility.fuelLevel?e.eventType_PassengerDropFacility.fuelLevel:t),"Tasking"===e.eventCategory&&(t=0!=e.eventType_Tasking.fuelLevel?e.eventType_Tasking.fuelLevel:t),"PassengerDropFacility"===e.eventCategory&&(t=0!=e.eventType_PassengerDropFacility.fuelLevel?e.eventType_PassengerDropFacility.fuelLevel:t),s=0===s?+t:f()([+s,+t])}),(t>0||s>0)&&r.push({id:e.id,maxFuelLevel:t,minFuelLevel:s,fromLocation:e.fromLocation,toLocation:e.toLocation,arrivalTime:e.arriveTime,departureTime:e.departTime,created:e.created})}),j(r)},onError:e=>{console.error("TripReport_LogBookEntrySection error",e)}}),P=e=>!p||u()(e.created).isAfter(u()(p.startDate))&&u()(e.created).isBefore(u()(p.endDate));return((0,i.uC)(y),g)?(0,s.jsxs)(s.Fragment,{children:[s.jsx(m.Bu,{title:"Fuel Analysis",actions:s.jsx("div",{className:"flex gap-2.5",children:s.jsx(m.zx,{variant:"back",onClick:()=>e.push("/reporting"),children:"Back"})})}),s.jsx(m.Zb,{className:"mt-8",children:(0,s.jsxs)(m.aY,{className:"flex flex-col gap-4",children:[s.jsx(a.Z,{onChange:e=>{"vessel"===e.type&&e.data?.value&&r(e.data?.value),"dateRange"===e.type&&c(e.data)}}),(0,s.jsxs)(m.iA,{children:[s.jsx(m.xD,{children:s.jsx(m.SC,{children:h.map(e=>s.jsx(m.ss,{children:e},e))})}),s.jsx(m.RM,{children:0==S.length?s.jsx(m.SC,{children:s.jsx(m.pj,{colSpan:h.length,className:"text-center h-32",children:"No Data Available"})}):S.filter(e=>P(e)).map(e=>(0,s.jsxs)(m.SC,{children:[s.jsx(m.pj,{children:(0,n.p6)(e.created)}),(0,s.jsxs)(m.pj,{children:[e.fromLocation?.title?e.fromLocation.title+" - ":"",e.departureTime]}),(0,s.jsxs)(m.pj,{children:[e.toLocation?.title?e.toLocation.title+" - ":"",e.arrivalTime]}),s.jsx(m.pj,{children:e.maxFuelLevel}),s.jsx(m.pj,{children:e.minFuelLevel}),s.jsx(m.pj,{children:e.maxFuelLevel-e.minFuelLevel})]},e.id))})]})]})})]}):s.jsx("div",{})}r(46776);var S=r(26100);function j(){let[e,t]=(0,x.useState)(!1),[r,a]=(0,x.useState)(!1);return e&&r?s.jsx(y,{}):e?s.jsx(S.Z,{errorMessage:"Oops You do not have the permission to view this section."}):s.jsx(S.Z,{})}},44517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(98768);r(60343);var a=r(32993),n=r(64837);function i({children:e}){return s.jsx(a.default,{children:s.jsx(n.Z,{children:e})})}},95081:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\fuel-analysis\page.tsx#default`)},71238:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042],()=>r(38814));module.exports=s})();