exports.id=813,exports.ids=[813],exports.modules={35242:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var u=new o(n,i||t,a),c=r?r+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],u]:t._events[c].push(u):(t._events[c]=u,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function u(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),u.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},u.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},u.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},u.prototype.emit=function(t,e,n,o,i,a){var u=r?r+t:t;if(!this._events[u])return!1;var c,l,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,c=Array(f-1);l<f;l++)c[l-1]=arguments[l];s.fn.apply(s.context,c)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!c)for(p=1,c=Array(f-1);p<f;p++)c[p-1]=arguments[p];s[l].fn.apply(s[l].context,c)}}return!0},u.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},u.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},u.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var u=this._events[i];if(u.fn)u.fn!==e||o&&!u.once||n&&u.context!==n||a(this,i);else{for(var c=0,l=[],s=u.length;c<s;c++)(u[c].fn!==e||o&&!u[c].once||n&&u[c].context!==n)&&l.push(u[c]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},u.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,t.exports=u},1936:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},38540:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},99440:(t,e,r)=>{var n=r(20539);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},85962:(t,e,r)=>{var n=r(91160),o=r(56667)(n);t.exports=o},15322:(t,e,r)=>{var n=r(85962);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},30816:(t,e,r)=>{var n=r(15903);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],u=e(a);if(null!=u&&(void 0===c?u==u&&!n(u):r(u,c)))var c=u,l=a}return l}},599:(t,e,r)=>{var n=r(47949),o=r(18438);t.exports=function t(e,r,i,a,u){var c=-1,l=e.length;for(i||(i=o),u||(u=[]);++c<l;){var s=e[c];r>0&&i(s)?r>1?t(s,r-1,i,a,u):n(u,s):a||(u[u.length]=s)}return u}},8374:(t,e,r)=>{var n=r(52800)();t.exports=n},91160:(t,e,r)=>{var n=r(8374),o=r(83077);t.exports=function(t,e){return t&&n(t,e,o)}},24548:t=>{t.exports=function(t,e){return t>e}},52543:t=>{t.exports=function(t,e){return t<e}},16475:(t,e,r)=>{var n=r(85962),o=r(57077);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},54076:(t,e,r)=>{var n=r(18479),o=r(56657),i=r(42076),a=r(16475),u=r(13326),c=r(85859),l=r(72061),s=r(14935),f=r(55813);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,c(i)),u(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},9026:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,u=r(e((n-t)/(o||1)),0),c=Array(u);u--;)c[i?u:++a]=t,t+=o;return c}},46403:(t,e,r)=>{var n=r(14935),o=r(98605),i=r(55311);t.exports=function(t,e){return i(o(t,e,n),t+"")}},44847:(t,e,r)=>{var n=r(12975),o=r(20539),i=r(14935),a=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i;t.exports=a},56044:(t,e,r)=>{var n=r(85962);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},13326:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},87581:(t,e,r)=>{var n=r(15903);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),u=void 0!==e,c=null===e,l=e==e,s=n(e);if(!c&&!s&&!a&&t>e||a&&u&&l&&!c&&!s||o&&u&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||c&&r&&i||!u&&i||!l)return -1}return 0}},72061:(t,e,r)=>{var n=r(87581);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,u=i.length,c=r.length;++o<u;){var l=n(i[o],a[o]);if(l){if(o>=c)return l;return l*("desc"==r[o]?-1:1)}}return t.index-e.index}},56667:(t,e,r)=>{var n=r(57077);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,u=Object(r);(e?a--:++a<i)&&!1!==o(u[a],a,u););return r}}},52800:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),u=a.length;u--;){var c=a[t?u:++o];if(!1===r(i[c],c,i))break}return e}}},95743:(t,e,r)=>{var n=r(30482),o=r(73211),i=r(66095),a=r(16266);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,u=r?r[0]:e.charAt(0),c=r?n(r,1).join(""):e.slice(1);return u[t]()+c}}},31648:(t,e,r)=>{var n=r(42076),o=r(57077),i=r(83077);t.exports=function(t){return function(e,r,a){var u=Object(e);if(!o(e)){var c=n(r,3);e=i(e),r=function(t){return c(u[t],t,u)}}var l=t(e,r,a);return l>-1?u[c?e[l]:l]:void 0}}},61976:(t,e,r)=>{var n=r(9026),o=r(35471),i=r(89211);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},20539:(t,e,r)=>{var n=r(25116),o=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},62767:(t,e,r)=>{var n=r(89479)(Object.getPrototypeOf,Object);t.exports=n},18438:(t,e,r)=>{var n=r(51858),o=r(62630),i=r(55813),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},35471:(t,e,r)=>{var n=r(30504),o=r(57077),i=r(60609),a=r(4171);t.exports=function(t,e,r){if(!a(r))return!1;var u=typeof e;return("number"==u?!!(o(r)&&i(e,r.length)):"string"==u&&e in r)&&n(r[e],t)}},98605:(t,e,r)=>{var n=r(1936),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,u=o(i.length-e,0),c=Array(u);++a<u;)c[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(c),n(t,this,l)}}},55311:(t,e,r)=>{var n=r(44847),o=r(78174)(n);t.exports=o},78174:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},12975:t=>{t.exports=function(t){return function(){return t}}},84535:(t,e,r)=>{var n=r(38540),o=r(15322),i=r(42076),a=r(55813),u=r(35471);t.exports=function(t,e,r){var c=a(t)?n:o;return r&&u(t,e,r)&&(e=void 0),c(t,i(e,3))}},92254:(t,e,r)=>{var n=r(31648)(r(26250));t.exports=n},26250:(t,e,r)=>{var n=r(829),o=r(42076),i=r(40160),a=Math.max;t.exports=function(t,e,r){var u=null==t?0:t.length;if(!u)return -1;var c=null==r?0:i(r);return c<0&&(c=a(u+c,0)),n(t,o(e,3),c)}},86107:(t,e,r)=>{var n=r(599),o=r(52070);t.exports=function(t,e){return n(o(t,e),1)}},26471:(t,e,r)=>{var n=r(55296),o=r(48377);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},38656:(t,e,r)=>{var n=r(84050);t.exports=function(t,e){return n(t,e)}},99470:(t,e,r)=>{var n=r(39479);t.exports=function(t){return n(t)&&t!=+t}},28288:t=>{t.exports=function(t){return null==t}},39479:(t,e,r)=>{var n=r(55296),o=r(48377);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},27673:(t,e,r)=>{var n=r(55296),o=r(62767),i=r(48377),a=Object.prototype,u=Function.prototype.toString,c=a.hasOwnProperty,l=u.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=c.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&u.call(r)==l}},37943:(t,e,r)=>{var n=r(55296),o=r(55813),i=r(48377);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},66245:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},52070:(t,e,r)=>{var n=r(18479),o=r(42076),i=r(16475),a=r(55813);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},56744:(t,e,r)=>{var n=r(99440),o=r(91160),i=r(42076);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},23095:(t,e,r)=>{var n=r(30816),o=r(24548),i=r(14935);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},27972:(t,e,r)=>{var n=r(30816),o=r(24548),i=r(42076);t.exports=function(t,e){return t&&t.length?n(t,i(e,2),o):void 0}},99488:(t,e,r)=>{var n=r(30816),o=r(52543),i=r(14935);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},11717:(t,e,r)=>{var n=r(30816),o=r(42076),i=r(52543);t.exports=function(t,e){return t&&t.length?n(t,o(e,2),i):void 0}},94022:(t,e,r)=>{var n=r(61976)();t.exports=n},59620:(t,e,r)=>{var n=r(28068),o=r(42076),i=r(56044),a=r(55813),u=r(35471);t.exports=function(t,e,r){var c=a(t)?n:i;return r&&u(t,e,r)&&(e=void 0),c(t,o(e,3))}},38701:(t,e,r)=>{var n=r(599),o=r(54076),i=r(46403),a=r(35471),u=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])});t.exports=u},37424:(t,e,r)=>{var n=r(71241),o=r(4171);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},89211:(t,e,r)=>{var n=r(24436),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},40160:(t,e,r)=>{var n=r(89211);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},30531:(t,e,r)=>{var n=r(95743)("toUpperCase");t.exports=n},86965:(t,e)=>{"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),c=Symbol.for("react.context"),l=Symbol.for("react.server_context"),s=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),d=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case r:switch(t=t.type){case o:case a:case i:case f:case p:return t;default:switch(t=t&&t.$$typeof){case l:case c:case s:case d:case h:case u:return t;default:return e}}case n:return e}}}(t)===o}},45492:(t,e,r)=>{"use strict";t.exports=r(86965)},81543:(t,e,r)=>{"use strict";r.d(e,{ZP:()=>tP});var n=r(60343),o=r.n(n),i=r(77454),a=r.n(i),u=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty;function s(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function f(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var u=t(e,r,n);return o.delete(e),o.delete(r),u}}function p(t){return u(t).concat(c(t))}var h=Object.hasOwn||function(t,e){return l.call(t,e)};function d(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var y=Object.getOwnPropertyDescriptor,v=Object.keys;function m(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function b(t,e){return d(t.getTime(),e.getTime())}function g(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function x(t,e){return t===e}function O(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),u=t.entries(),c=0;(n=u.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,h=o.value;if(r.equals(p[0],h[0],c,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;c++}return!0}function w(t,e,r){var n=v(t),o=n.length;if(v(e).length!==o)return!1;for(;o-- >0;)if(!k(t,e,r,n[o]))return!1;return!0}function j(t,e,r){var n,o,i,a=p(t),u=a.length;if(p(e).length!==u)return!1;for(;u-- >0;)if(!k(t,e,r,n=a[u])||(o=y(t,n),i=y(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function S(t,e){return d(t.valueOf(),e.valueOf())}function P(t,e){return t.source===e.source&&t.flags===e.flags}function A(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),u=t.values();(n=u.next())&&!n.done;){for(var c=e.values(),l=!1,s=0;(o=c.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function E(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function M(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function k(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||h(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var T=Array.isArray,_="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,C=Object.assign,I=Object.prototype.toString.call.bind(Object.prototype.toString),D=N();function N(t){void 0===t&&(t={});var e,r,n,o,i,a,u,c,l,p,h,y,v,k=t.circular,D=t.createInternalComparator,N=t.createState,L=t.strict,B=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?j:m,areDatesEqual:b,areErrorsEqual:g,areFunctionsEqual:x,areMapsEqual:n?s(O,j):O,areNumbersEqual:d,areObjectsEqual:n?j:w,arePrimitiveWrappersEqual:S,areRegExpsEqual:P,areSetsEqual:n?s(A,j):A,areTypedArraysEqual:n?j:E,areUrlsEqual:M};if(r&&(o=C({},o,r(o))),e){var i=f(o.areArraysEqual),a=f(o.areMapsEqual),u=f(o.areObjectsEqual),c=f(o.areSetsEqual);o=C({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:u,areSetsEqual:c})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,u=e.areNumbersEqual,c=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,p=e.areRegExpsEqual,h=e.areSetsEqual,y=e.areTypedArraysEqual,v=e.areUrlsEqual,function(t,e,s){if(t===e)return!0;if(null==t||null==e)return!1;var f=typeof t;if(f!==typeof e)return!1;if("object"!==f)return"number"===f?u(t,e,s):"function"===f&&i(t,e,s);var d=t.constructor;if(d!==e.constructor)return!1;if(d===Object)return c(t,e,s);if(T(t))return r(t,e,s);if(null!=_&&_(t))return y(t,e,s);if(d===Date)return n(t,e,s);if(d===RegExp)return p(t,e,s);if(d===Map)return a(t,e,s);if(d===Set)return h(t,e,s);var m=I(t);return"[object Date]"===m?n(t,e,s):"[object RegExp]"===m?p(t,e,s):"[object Map]"===m?a(t,e,s):"[object Set]"===m?h(t,e,s):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&c(t,e,s):"[object URL]"===m?v(t,e,s):"[object Error]"===m?o(t,e,s):"[object Arguments]"===m?c(t,e,s):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,s)}),R=D?D(B):function(t,e,r,n,o,i,a){return B(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var u=n(),c=u.cache;return r(t,a,{cache:void 0===c?e?new WeakMap:void 0:c,equals:o,meta:u.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==k&&k,comparator:B,createState:N,equals:R,strict:void 0!==L&&L})}function L(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function B(t){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function R(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function z(t){return(z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function $(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){F(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function F(t,e,r){var n;return(n=function(t,e){if("object"!==z(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==z(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===z(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}N({strict:!0}),N({circular:!0}),N({circular:!0,strict:!0}),N({createInternalComparator:function(){return d}}),N({strict:!0,createInternalComparator:function(){return d}}),N({circular:!0,createInternalComparator:function(){return d}}),N({circular:!0,createInternalComparator:function(){return d},strict:!0});var Z=function(t){return t},q=function(t,e){return Object.keys(e).reduce(function(r,n){return $($({},r),{},F({},n,t(n,e[n])))},{})},W=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},H=function(t,e,r,n,o,i,a,u){};function Y(t,e){if(t){if("string"==typeof t)return X(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return X(t,e)}}function X(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var V=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},G=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},K=function(t,e){return function(r){return G(V(t,e),r)}},J=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],u=n[2],c=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,u=1,c=1;break;case"ease":i=.25,a=.1,u=.25,c=1;break;case"ease-in":i=.42,a=0,u=1,c=1;break;case"ease-out":i=.42,a=0,u=.58,c=1;break;case"ease-in-out":i=0,a=0,u=.58,c=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),4!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(s,4)||Y(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],u=f[2],c=f[3]}else H(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}H([i,u,a,c].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=K(i,u),h=K(a,c),d=(t=i,e=u,function(r){var n;return G([].concat(function(t){if(Array.isArray(t))return X(t)}(n=V(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||Y(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=d(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return h(r)};return y.isStepper=!1,y},Q=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,u=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,u=n*a/1e3+t;return 1e-4>Math.abs(u-e)&&1e-4>Math.abs(i)?[e,0]:[u,i]};return u.isStepper=!0,u.dt=a,u},tt=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return J(n);case"spring":return Q();default:if("cubic-bezier"===n.split("(")[0])return J(n);H(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(H(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function te(t){return(te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tr(t){return function(t){if(Array.isArray(t))return tu(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ta(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function to(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tn(Object(r),!0).forEach(function(e){ti(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tn(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ti(t,e,r){var n;return(n=function(t,e){if("object"!==te(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==te(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===te(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ta(t,e){if(t){if("string"==typeof t)return tu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tu(t,e)}}function tu(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tc=function(t,e,r){return t+(e-t)*r},tl=function(t){return t.from!==t.to},ts=function t(e,r,n){var o=q(function(t,r){if(tl(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(n,2)||ta(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return to(to({},r),{},{from:i,velocity:a})}return r},r);return n<1?q(function(t,e){return tl(e)?to(to({},e),{},{velocity:tc(e.velocity,o[t].velocity,n),from:tc(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};let tf=function(t,e,r,n,o){var i,a,u=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),c=u.reduce(function(r,n){return to(to({},r),{},ti({},n,[t[n],e[n]]))},{}),l=u.reduce(function(r,n){return to(to({},r),{},ti({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=ts(r,l,a),o(to(to(to({},t),e),q(function(t,e){return e.from},l))),i=n,Object.values(l).filter(tl).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var u=(i-a)/n,l=q(function(t,e){return tc.apply(void 0,tr(e).concat([r(u)]))},c);if(o(to(to(to({},t),e),l)),u<1)s=requestAnimationFrame(f);else{var p=q(function(t,e){return tc.apply(void 0,tr(e).concat([r(1)]))},c);o(to(to(to({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function tp(t){return(tp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var th=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function td(t){return function(t){if(Array.isArray(t))return ty(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ty(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ty(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ty(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tm(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tv(Object(r),!0).forEach(function(e){tb(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tv(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tb(t,e,r){return(e=tg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tg(t){var e=function(t,e){if("object"!==tp(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tp(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===tp(e)?e:String(e)}function tx(t,e){return(tx=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tO(t,e){if(e&&("object"===tp(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return tw(t)}function tw(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function tj(t){return(tj=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var tS=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tx(t,e)}(a,t);var e,r,i=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=tj(a);return t=e?Reflect.construct(r,arguments,tj(this).constructor):r.apply(this,arguments),tO(this,t)});function a(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,a);var r,n=(r=i.call(this,t,e)).props,o=n.isActive,u=n.attributeName,c=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(tw(r)),r.changeStyle=r.changeStyle.bind(tw(r)),!o||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),tO(r);if(s&&s.length)r.state={style:s[0].style};else if(c){if("function"==typeof f)return r.state={style:c},tO(r);r.state={style:u?tb({},u,c):c}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,u=e.from,c=this.state.style;if(n){if(!r){var l={style:o?tb({},o,a):a};this.state&&c&&(o&&c[o]!==a||!o&&c!==a)&&this.setState(l);return}if(!D(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?u:t.to;if(this.state&&c){var p={style:o?tb({},o,f):f};(o&&c[o]!==f||!o&&c!==f)&&this.setState(p)}this.runAnimation(tm(tm({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,u=t.onAnimationEnd,c=t.onAnimationStart,l=tf(r,n,tt(i),o,this.changeStyle);this.manager.start([c,a,function(){e.stopJSAnimation=l()},o,u])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,u=i.duration;return this.manager.start([o].concat(td(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,u=void 0===a?"ease":a,c=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(c);if("function"==typeof u||"spring"===u)return[].concat(td(t),[e.runJSAnimation.bind(e,{from:f.style,to:c,duration:i,easing:u}),i]);var h=W(p,i,u),d=tm(tm(tm({},f.style),c),{},{transition:h});return[].concat(td(t),[d,i,s]).filter(Z)},[a,Math.max(void 0===u?0:u,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){if(!this.manager){var e,r,n;this.manager=(e=function(){return null},r=!1,n=function t(n){if(!r){if(Array.isArray(n)){if(!n.length)return;var o=function(t){if(Array.isArray(t))return t}(n)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||function(t,e){if(t){if("string"==typeof t)return R(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return R(t,void 0)}}(n)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){L(t.bind(null,a),i);return}t(i),L(t.bind(null,a));return}"object"===B(n)&&e(n),"function"==typeof n&&n()}},{stop:function(){r=!0},start:function(t){r=!1,n(t)},subscribe:function(t){return e=t,function(){e=function(){return null}}}})}var o=t.begin,i=t.duration,a=t.attributeName,u=t.to,c=t.easing,l=t.onAnimationStart,s=t.onAnimationEnd,f=t.steps,p=t.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof c||"function"==typeof p||"spring"===c){this.runJSAnimation(t);return}if(f.length>1){this.runStepAnimation(t);return}var d=a?tb({},a,u):u,y=W(Object.keys(d),i,c);h.start([l,o,tm(tm({},d),{},{transition:y}),i,s])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),i=(t.attributeName,t.easing,t.isActive),a=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,th)),u=n.Children.count(e),c=this.state.style;if("function"==typeof e)return e(c);if(!i||0===u||r<=0)return e;var l=function(t){var e=t.props,r=e.style,o=e.className;return(0,n.cloneElement)(t,tm(tm({},a),{},{style:tm(tm({},void 0===r?{}:r),c),className:o}))};return 1===u?l(n.Children.only(e)):o().createElement("div",null,n.Children.map(e,function(t){return l(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tg(n.key),n)}}(a.prototype,r),Object.defineProperty(a,"prototype",{writable:!1}),a}(n.PureComponent);tS.displayName="Animate",tS.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},tS.propTypes={from:a().oneOfType([a().object,a().string]),to:a().oneOfType([a().object,a().string]),attributeName:a().string,duration:a().number,begin:a().number,easing:a().oneOfType([a().string,a().func]),steps:a().arrayOf(a().shape({duration:a().number.isRequired,style:a().object.isRequired,easing:a().oneOfType([a().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),a().func]),properties:a().arrayOf("string"),onAnimationEnd:a().func})),children:a().oneOfType([a().node,a().func]),isActive:a().bool,canBegin:a().bool,onAnimationEnd:a().func,shouldReAnimate:a().bool,onAnimationStart:a().func,onAnimationReStart:a().func};let tP=tS},94970:(t,e,r)=>{"use strict";r.d(e,{$:()=>$});var n=r(60343),o=r.n(n),i=r(28411),a=r(81543),u=r(38656),c=r.n(u),l=r(28288),s=r.n(l),f=r(99829),p=r(31222),h=r(95949),d=r(70953),y=r(43229),v=r(80131),m=r(12307),b=r(56361),g=r(34718),x=r(56501),O=r(78445),w=["x","y"];function j(t){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function S(){return(S=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function P(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?P(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function E(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,w),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),u=parseInt("".concat(e.height||o.height),10),c=parseInt("".concat(e.width||o.width),10);return A(A(A(A(A({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:u,width:c,name:e.name,radius:e.radius})}function M(t){return o().createElement(O.bn,S({shapeType:"rectangle",propTransformer:E,activeClassName:"recharts-active-bar"},t))}var k=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o=(0,y.hj)(r)||(0,y.Rw)(r);return o?t(r,n):(o||(0,x.Z)(!1),e)}},T=["value","background"];function _(t){return(_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function C(){return(C=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function I(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function D(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?I(Object(r),!0).forEach(function(e){z(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function N(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,U(n.key),n)}}function L(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(L=function(){return!!t})()}function B(t){return(B=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function R(t,e){return(R=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function z(t,e,r){return(e=U(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function U(t){var e=function(t,e){if("object"!=_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==_(e)?e:e+""}var $=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=B(e),z(t=function(t,e){if(e&&("object"===_(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,L()?Reflect.construct(e,r||[],B(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),z(t,"id",(0,y.EL)("recharts-bar-")),z(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),z(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&R(t,e)}(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,i=r.dataKey,a=r.activeIndex,u=r.activeBar,c=(0,v.L6)(this.props,!1);return t&&t.map(function(t,r){var l=r===a,s=D(D(D({},c),t),{},{isActive:l,option:l?u:n,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return o().createElement(f.m,C({className:"recharts-bar-rectangle"},(0,g.bw)(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),o().createElement(M,s))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,i=e.isAnimationActive,u=e.animationBegin,c=e.animationDuration,l=e.animationEasing,s=e.animationId,p=this.state.prevData;return o().createElement(a.ZP,{begin:u,duration:c,isActive:i,easing:l,from:{t:0},to:{t:1},key:"bar-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,a=r.map(function(t,e){var r=p&&p[e];if(r){var o=(0,y.k4)(r.x,t.x),a=(0,y.k4)(r.y,t.y),u=(0,y.k4)(r.width,t.width),c=(0,y.k4)(r.height,t.height);return D(D({},t),{},{x:o(i),y:a(i),width:u(i),height:c(i)})}if("horizontal"===n){var l=(0,y.k4)(0,t.height)(i);return D(D({},t),{},{y:t.y+t.height-l,height:l})}var s=(0,y.k4)(0,t.width)(i);return D(D({},t),{},{width:s})});return o().createElement(f.m,null,t.renderRectanglesStatically(a))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!c()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,i=e.activeIndex,a=(0,v.L6)(this.props.background,!1);return r.map(function(e,r){e.value;var u=e.background,c=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,T);if(!u)return null;var l=D(D(D(D(D({},c),{},{fill:"#eee"},u),a),(0,g.bw)(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return o().createElement(M,C({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,i=r.xAxis,a=r.yAxis,u=r.layout,c=r.children,l=(0,v.NN)(c,p.W);if(!l)return null;var s="vertical"===u?n[0].height/2:n[0].width/2,h=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:(0,b.F$)(t,e)}};return o().createElement(f.m,{clipPath:t?"url(#clipPath-".concat(e,")"):null},l.map(function(t){return o().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:i,yAxis:a,layout:u,offset:s,dataPointFormatter:h})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,a=t.xAxis,u=t.yAxis,c=t.left,l=t.top,p=t.width,h=t.height,y=t.isAnimationActive,v=t.background,m=t.id;if(e||!r||!r.length)return null;var b=this.state.isAnimationFinished,g=(0,i.Z)("recharts-bar",n),x=a&&a.allowDataOverflow,O=u&&u.allowDataOverflow,w=x||O,j=s()(m)?this.id:m;return o().createElement(f.m,{className:g},x||O?o().createElement("defs",null,o().createElement("clipPath",{id:"clipPath-".concat(j)},o().createElement("rect",{x:x?c:c-p/2,y:O?l:l-h/2,width:x?p:2*p,height:O?h:2*h}))):null,o().createElement(f.m,{className:"recharts-bar-rectangles",clipPath:w?"url(#clipPath-".concat(j,")"):null},v?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(w,j),(!y||b)&&d.e.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&N(n.prototype,e),r&&N(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(n.PureComponent);z($,"displayName","Bar"),z($,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!m.x.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),z($,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,u=t.xAxisTicks,c=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,d=(0,b.Bu)(n,r);if(!d)return null;var m=e.layout,g=r.type.defaultProps,x=void 0!==g?D(D({},g),r.props):r.props,O=x.dataKey,w=x.children,j=x.minPointSize,S="horizontal"===m?a:i,P=l?S.scale.domain():null,A=(0,b.Yj)({numericAxis:S}),E=(0,v.NN)(w,h.b),M=f.map(function(t,e){l?f=(0,b.Vv)(l[s+e],P):Array.isArray(f=(0,b.F$)(t,O))||(f=[A,f]);var n=k(j,$.defaultProps.minPointSize)(f[1],e);if("horizontal"===m){var f,p,h,v,g,x,w,S=[a.scale(f[0]),a.scale(f[1])],M=S[0],T=S[1];p=(0,b.Fy)({axis:i,ticks:u,bandSize:o,offset:d.offset,entry:t,index:e}),h=null!==(w=null!=T?T:M)&&void 0!==w?w:void 0,v=d.size;var _=M-T;if(g=Number.isNaN(_)?0:_,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(g)<Math.abs(n)){var C=(0,y.uY)(g||n)*(Math.abs(n)-Math.abs(g));h-=C,g+=C}}else{var I=[i.scale(f[0]),i.scale(f[1])],N=I[0],L=I[1];if(p=N,h=(0,b.Fy)({axis:a,ticks:c,bandSize:o,offset:d.offset,entry:t,index:e}),v=L-N,g=d.size,x={x:i.x,y:h,width:i.width,height:g},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var B=(0,y.uY)(v||n)*(Math.abs(n)-Math.abs(v));v+=B}}return D(D(D({},t),{},{x:p,y:h,width:v,height:g,value:l?f:f[1],payload:t,background:x},E&&E[e]&&E[e].props),{},{tooltipPayload:[(0,b.Qo)(r,t)],tooltipPosition:{x:p+v/2,y:h+g/2}})});return D({data:M,layout:m},p)})},31222:(t,e,r)=>{"use strict";r.d(e,{W:()=>m});var n=r(60343),o=r.n(n),i=r(56501),a=r(99829),u=r(80131),c=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function p(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(p=function(){return!!t})()}function h(t){return(h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function d(t,e){return(d=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function y(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}var m=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=h(t),function(t,e){if(e&&("object"===l(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,p()?Reflect.construct(t,e||[],h(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&d(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,l=t.dataKey,p=t.data,h=t.dataPointFormatter,d=t.xAxis,y=t.yAxis,v=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,c),m=(0,u.L6)(v,!1);"x"===this.props.direction&&"number"!==d.type&&(0,i.Z)(!1);var b=p.map(function(t){var i,u,c=h(t,l),p=c.x,v=c.y,b=c.value,g=c.errorVal;if(!g)return null;var x=[];if(Array.isArray(g)){var O=function(t){if(Array.isArray(t))return t}(g)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(g,2)||function(t,e){if(t){if("string"==typeof t)return f(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f(t,2)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=O[0],u=O[1]}else i=u=g;if("vertical"===r){var w=d.scale,j=v+e,S=j+n,P=j-n,A=w(b-i),E=w(b+u);x.push({x1:E,y1:S,x2:E,y2:P}),x.push({x1:A,y1:j,x2:E,y2:j}),x.push({x1:A,y1:S,x2:A,y2:P})}else if("horizontal"===r){var M=y.scale,k=p+e,T=k-n,_=k+n,C=M(b-i),I=M(b+u);x.push({x1:T,y1:I,x2:_,y2:I}),x.push({x1:k,y1:C,x2:k,y2:I}),x.push({x1:T,y1:C,x2:_,y2:C})}return o().createElement(a.m,s({className:"recharts-errorBar",key:"bar-".concat(x.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},m),x.map(function(t){return o().createElement("line",s({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return o().createElement(a.m,{className:"recharts-errorBars"},b)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o().Component);y(m,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),y(m,"displayName","ErrorBar")},9626:(t,e,r)=>{"use strict";r.d(e,{z:()=>eI});var n=r(60343),o=r.n(n),i=r(28288),a=r.n(i),u=r(86830),c=r.n(u),l=r(94022),s=r.n(l),f=r(28501),p=r.n(f),h=r(38701),d=r.n(h),y=r(37424),v=r.n(y),m=r(28411),b=r(56501),g=r(27921),x=r(99829),O=r(52419),w=r(94309),j=r(35590),S=r(13663),P=r(80131),A=r(91358),E=r(821),M=r(56361),k=r(43229);function T(t){return(T="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function C(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_(Object(r),!0).forEach(function(e){I(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function I(t,e,r){var n;return(n=function(t,e){if("object"!=T(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=T(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==T(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var D=["Webkit","Moz","O","ms"],N=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=D.reduce(function(t,n){return C(C({},t),{},I({},n+r,e))},{});return n[t]=e,n};function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function B(){return(B=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function R(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function z(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?R(Object(r),!0).forEach(function(e){q(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function U(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,W(n.key),n)}}function $(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return($=function(){return!!t})()}function F(t){return(F=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Z(t,e){return(Z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function q(t,e,r){return(e=W(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function W(t){var e=function(t,e){if("object"!=L(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=L(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==L(e)?e:e+""}var H=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var u=e.length,c=(0,A.x)().domain(s()(0,u)).range([o,o+i-a]),l=c.domain().map(function(t){return c(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:l}},Y=function(t){return t.changedTouches&&!!t.changedTouches.length},X=function(t){var e,r;function i(t){var e,r,n;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i),r=i,n=[t],r=F(r),q(e=function(t,e){if(e&&("object"===L(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,$()?Reflect.construct(r,n||[],F(this).constructor):r.apply(this,n)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),q(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),q(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),q(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),q(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),q(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),q(e,"handleSlideDragStart",function(t){var r=Y(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Z(t,e)}(i,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,n=this.state.scaleValues,o=this.props,a=o.gap,u=o.data.length-1,c=i.getIndexInRange(n,Math.min(e,r)),l=i.getIndexInRange(n,Math.max(e,r));return{startIndex:c-c%a,endIndex:l===u?u:l-l%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=(0,M.F$)(r[t],o,t);return c()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,u=i.width,c=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+u-c-o,a+u-c-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=Y(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],u=this.props,c=u.x,l=u.width,s=u.travellerWidth,f=u.onChange,p=u.gap,h=u.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,c+l-s-a):y<0&&(y=Math.max(y,c-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||o<i&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||o>i&&b===t};this.setState(q(q({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,u=this.state[e],c=o.indexOf(u);if(-1!==c){var l=c+t;if(-1!==l&&!(l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(q({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,a=t.fill,u=t.stroke;return o().createElement("rect",{stroke:u,fill:a,x:e,y:r,width:n,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,i=t.width,a=t.height,u=t.data,c=t.children,l=t.padding,s=n.Children.only(c);return s?o().cloneElement(s,{x:e,y:r,width:i,height:a,margin:l,compact:!0,data:u}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,n,a=this,u=this.props,c=u.y,l=u.travellerWidth,s=u.height,f=u.traveller,p=u.ariaLabel,h=u.data,d=u.startIndex,y=u.endIndex,v=Math.max(t,this.props.x),m=z(z({},(0,P.L6)(this.props,!1)),{},{x:v,y:c,width:l,height:s}),b=p||"Min value: ".concat(null===(r=h[d])||void 0===r?void 0:r.name,", Max value: ").concat(null===(n=h[y])||void 0===n?void 0:n.name);return o().createElement(x.m,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),a.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){a.setState({isTravellerFocused:!0})},onBlur:function(){a.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},i.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,i=r.height,a=r.stroke,u=r.travellerWidth;return o().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:a,fillOpacity:.2,x:Math.min(t,e)+u,y:n,width:Math.max(Math.abs(e-t)-u,0),height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,i=t.height,a=t.travellerWidth,u=t.stroke,c=this.state,l=c.startX,s=c.endX,f={pointerEvents:"none",fill:u};return o().createElement(x.m,{className:"recharts-brush-texts"},o().createElement(E.x,B({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:n+i/2},f),this.getTextOfTick(e)),o().createElement(E.x,B({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+a+5,y:n+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,i=t.x,a=t.y,u=t.width,c=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,v=s.isTravellerFocused;if(!e||!e.length||!(0,k.hj)(i)||!(0,k.hj)(a)||!(0,k.hj)(u)||!(0,k.hj)(c)||u<=0||c<=0)return null;var b=(0,m.Z)("recharts-brush",r),g=1===o().Children.count(n),O=N("userSelect","none");return o().createElement(x.m,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:O},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||v||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,i=t.height,a=t.stroke,u=Math.floor(r+i/2)-1;return o().createElement(o().Fragment,null,o().createElement("rect",{x:e,y:r,width:n,height:i,fill:a,stroke:"none"}),o().createElement("line",{x1:e+1,y1:u,x2:e+n-1,y2:u,fill:"none",stroke:"#fff"}),o().createElement("line",{x1:e+1,y1:u+2,x2:e+n-1,y2:u+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){return o().isValidElement(t)?o().cloneElement(t,e):c()(t)?t(e):i.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,u=t.startIndex,c=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return z({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?H({data:r,width:n,x:o,travellerWidth:i,startIndex:u,endIndex:c}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&U(i.prototype,e),r&&U(i,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.PureComponent);q(X,"displayName","Brush"),q(X,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var V=r(94438),G=r(87709),K=r(60288),J=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},Q=r(81156),tt=r(60307);function te(){return(te=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tr(t){return(tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function to(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tn(Object(r),!0).forEach(function(e){tc(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tn(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ti(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ti=function(){return!!t})()}function ta(t){return(ta=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tu(t,e){return(tu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tc(t,e,r){return(e=tl(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tl(t){var e=function(t,e){if("object"!=tr(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tr(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tr(e)?e:e+""}var ts=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=(0,Q.Ky)({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return J(t,"discard")&&!i.isInRange(a)?null:a},tf=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=ta(t),function(t,e){if(e&&("object"===tr(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ti()?Reflect.construct(t,e||[],ta(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tu(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,i=t.r,a=t.alwaysShow,u=t.clipPathId,c=(0,k.P2)(e),l=(0,k.P2)(n);if((0,tt.Z)(void 0===a,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!l)return null;var s=ts(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,v=to(to({clipPath:J(this.props,"hidden")?"url(#".concat(u,")"):void 0},(0,P.L6)(this.props,!0)),{},{cx:f,cy:p});return o().createElement(x.m,{className:(0,m.Z)("recharts-reference-dot",y)},r.renderDot(d,v),K._.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tl(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o().Component);tc(tf,"displayName","ReferenceDot"),tc(tf,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),tc(tf,"renderDot",function(t,e){return o().isValidElement(t)?o().cloneElement(t,e):c()(t)?t(e):o().createElement(j.o,te({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var tp=r(59620),th=r.n(tp),td=r(31170);function ty(t){return(ty="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tv(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tv=function(){return!!t})()}function tm(t){return(tm=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tb(t,e){return(tb=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tg(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tx(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tg(Object(r),!0).forEach(function(e){tO(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tg(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tO(t,e,r){return(e=tw(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tw(t){var e=function(t,e){if("object"!=ty(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ty(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ty(e)?e:e+""}function tj(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tS(){return(tS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tP=function(t,e,r,n,o,i,a,u,c){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=c.y,d=t.y.apply(h,{position:i});if(J(c,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===u?y.reverse():y}if(e){var v=c.x,m=t.x.apply(v,{position:i});if(J(c,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=c.segment.map(function(e){return t.apply(e,{position:i})});return J(c,"discard")&&th()(g,function(e){return!t.isInRange(e)})?null:g}return null};function tA(t){var e,r,n=t.x,i=t.y,a=t.segment,u=t.xAxisId,l=t.yAxisId,s=t.shape,f=t.className,p=t.alwaysShow,h=(0,td.sp)(),d=(0,td.bH)(u),y=(0,td.Ud)(l),v=(0,td.d2)();if(!h||!v)return null;(0,tt.Z)(void 0===p,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var b=tP((0,Q.Ky)({x:d.scale,y:y.scale}),(0,k.P2)(n),(0,k.P2)(i),a&&2===a.length,v,t.position,d.orientation,y.orientation,t);if(!b)return null;var g=function(t){if(Array.isArray(t))return t}(b)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(b,2)||function(t,e){if(t){if("string"==typeof t)return tj(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tj(t,2)}}(b,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),O=g[0],w=O.x,j=O.y,S=g[1],A=S.x,E=S.y,M=tx(tx({clipPath:J(t,"hidden")?"url(#".concat(h,")"):void 0},(0,P.L6)(t,!0)),{},{x1:w,y1:j,x2:A,y2:E});return o().createElement(x.m,{className:(0,m.Z)("recharts-reference-line",f)},(e=s,r=M,o().isValidElement(e)?o().cloneElement(e,r):c()(e)?e(r):o().createElement("line",tS({},r,{className:"recharts-reference-line-line"}))),K._.renderCallByParent(t,(0,Q._b)({x1:w,y1:j,x2:A,y2:E})))}var tE=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=tm(t),function(t,e){if(e&&("object"===ty(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,tv()?Reflect.construct(t,e||[],tm(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tb(t,e)}(r,t),e=[{key:"render",value:function(){return o().createElement(tA,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tw(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o().Component);function tM(){return(tM=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tk(t){return(tk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tT(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t_(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tT(Object(r),!0).forEach(function(e){tN(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tT(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tC(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tC=function(){return!!t})()}function tI(t){return(tI=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tD(t,e){return(tD=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tN(t,e,r){return(e=tL(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tL(t){var e=function(t,e){if("object"!=tk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tk(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tk(e)?e:e+""}tO(tE,"displayName","ReferenceLine"),tO(tE,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var tB=function(t,e,r,n,o){var i=o.x1,a=o.x2,u=o.y1,c=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=(0,Q.Ky)({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(u,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!J(o,"discard")||f.isInRange(p)&&f.isInRange(h)?(0,Q.O1)(p,h):null},tR=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=tI(t),function(t,e){if(e&&("object"===tk(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,tC()?Reflect.construct(t,e||[],tI(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tD(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,i=t.y1,a=t.y2,u=t.className,c=t.alwaysShow,l=t.clipPathId;(0,tt.Z)(void 0===c,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=(0,k.P2)(e),f=(0,k.P2)(n),p=(0,k.P2)(i),h=(0,k.P2)(a),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=tB(s,f,p,h,this.props);if(!y&&!d)return null;var v=J(this.props,"hidden")?"url(#".concat(l,")"):void 0;return o().createElement(x.m,{className:(0,m.Z)("recharts-reference-area",u)},r.renderRect(d,t_(t_({clipPath:v},(0,P.L6)(this.props,!0)),y)),K._.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tL(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o().Component);function tz(t){return function(t){if(Array.isArray(t))return tU(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return tU(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tU(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tU(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}tN(tR,"displayName","ReferenceArea"),tN(tR,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),tN(tR,"renderRect",function(t,e){return o().isValidElement(t)?o().cloneElement(t,e):c()(t)?t(e):o().createElement(S.A,tM({},e,{className:"recharts-reference-area-rect"}))});var t$=function(t,e,r,n,o){var i=(0,P.NN)(t,tE),a=(0,P.NN)(t,tf),u=[].concat(tz(i),tz(a)),c=(0,P.NN)(t,tR),l="".concat(n,"Id"),s=n[0],f=e;if(u.length&&(f=u.reduce(function(t,e){if(e.props[l]===r&&J(e.props,"extendDomain")&&(0,k.hj)(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),c.length){var p="".concat(s,"1"),h="".concat(s,"2");f=c.reduce(function(t,e){if(e.props[l]===r&&J(e.props,"extendDomain")&&(0,k.hj)(e.props[p])&&(0,k.hj)(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return(0,k.hj)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},tF=r(80076),tZ=r(51128),tq=r(35242),tW=new(r.n(tq)()),tH="recharts.syncMouseEvents",tY=r(34718);function tX(t){return(tX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tV(t,e,r){return(e=tG(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tG(t){var e=function(t,e){if("object"!=tX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tX(e)?e:e+""}var tK=function(){var t,e;return t=function t(){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,t),tV(this,"activeIndex",0),tV(this,"coordinateList",[]),tV(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,u=void 0===a?null:a,c=t.offset,l=void 0===c?null:c,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=u?u:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,u=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,c=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+c;this.mouseHandlerCallback({pageX:n+a+u,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tG(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}(),tJ=r(78445),tQ=r(89620);function t0(t){return(t0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var t1=["x","y","top","left","width","height","className"];function t2(){return(t2=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function t3(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var t6=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,i=void 0===n?0:n,a=t.top,u=void 0===a?0:a,c=t.left,l=void 0===c?0:c,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t3(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=t0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t0(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t3(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:i,top:u,left:l,width:f,height:h},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,t1));return(0,k.hj)(r)&&(0,k.hj)(i)&&(0,k.hj)(f)&&(0,k.hj)(h)&&(0,k.hj)(u)&&(0,k.hj)(l)?o().createElement("path",t2({},(0,P.L6)(y,!0),{className:(0,m.Z)("recharts-cross",d),d:"M".concat(r,",").concat(u,"v").concat(h,"M").concat(l,",").concat(i,"h").concat(f)})):null};function t8(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[(0,tF.op)(e,r,n,o),(0,tF.op)(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}var t4=r(79211);function t5(t){return(t5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t7(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=t5(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t5(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t5(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function et(t){var e,r,o,i,a=t.element,u=t.tooltipEventType,c=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,v=null!==(r=a.props.cursor)&&void 0!==r?r:null===(o=a.type.defaultProps)||void 0===o?void 0:o.cursor;if(!a||!v||!c||!l||"ScatterChart"!==y&&"axis"!==u)return null;var b=tQ.H;if("ScatterChart"===y)i=l,b=t6;else if("BarChart"===y)e=h/2,i={stroke:"none",fill:"#ccc",x:"horizontal"===d?l.x-e:f.left+.5,y:"horizontal"===d?f.top+.5:l.y-e,width:"horizontal"===d?h:f.width-1,height:"horizontal"===d?f.height-1:h},b=S.A;else if("radial"===d){var g=t8(l),x=g.cx,O=g.cy,w=g.radius;i={cx:x,cy:O,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},b=t4.L}else i={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return t8(e);var u=e.cx,c=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=(0,tF.op)(u,c,l,f),h=(0,tF.op)(u,c,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}(d,l,f)},b=tQ.H;var j=t9(t9(t9(t9({stroke:"#ccc",pointerEvents:"none"},f),i),(0,P.L6)(v,!1)),{},{payload:s,payloadIndex:p,className:(0,m.Z)("recharts-tooltip-cursor",v.className)});return(0,n.isValidElement)(v)?(0,n.cloneElement)(v,j):(0,n.createElement)(b,j)}var ee=["item"],er=["children","className","width","height","style","compact","title","desc"];function en(t){return(en="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eo(){return(eo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ei(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||ef(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ea(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function eu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eu=function(){return!!t})()}function ec(t){return(ec=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function el(t,e){return(el=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function es(t){return function(t){if(Array.isArray(t))return ep(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ef(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ef(t,e){if(t){if("string"==typeof t)return ep(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ep(t,e)}}function ep(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function eh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ed(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eh(Object(r),!0).forEach(function(e){ey(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eh(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ey(t,e,r){return(e=ev(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ev(t){var e=function(t,e){if("object"!=en(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=en(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==en(e)?e:e+""}var em={xAxis:["bottom","top"],yAxis:["left","right"]},eb={width:"100%",height:"100%"},eg={x:0,y:0};function ex(t){return t}var eO=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return ed(ed(ed({},n),(0,tF.op)(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var u=o.coordinate,c=n.angle;return ed(ed(ed({},n),(0,tF.op)(n.cx,n.cy,u,c)),{},{angle:c,radius:u})}return eg},ew=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(es(t),es(r)):t},[]);return i.length>0?i:t&&t.length&&(0,k.hj)(n)&&(0,k.hj)(o)?t.slice(n,o+1):[]};function ej(t){return"number"===t?[0,"auto"]:void 0}var eS=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=ew(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,u){var c,l,s=null!==(c=u.props.data)&&void 0!==c?c:e;if(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory){var f=void 0===s?a:s;l=(0,k.Ap)(f,i.dataKey,n)}else l=s&&s[r]||a[r];return l?[].concat(es(o),[(0,M.Qo)(u,l)]):o},[])},eP=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,u=t.tooltipAxis,c=t.tooltipTicks,l=(0,M.VO)(i,a,c,u);if(l>=0&&c){var s=c[l]&&c[l].value,f=eS(t,e,l,s),p=eO(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},eA=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,u=e.stackGroups,c=e.dataStartIndex,l=e.dataEndIndex,f=t.layout,p=t.children,h=t.stackOffset,d=(0,M.NA)(f,o);return r.reduce(function(e,r){var y=void 0!==r.type.defaultProps?ed(ed({},r.type.defaultProps),r.props):r.props,v=y.type,m=y.dataKey,b=y.allowDataOverflow,g=y.allowDuplicatedCategory,x=y.scale,O=y.ticks,w=y.includeHidden,j=y[i];if(e[j])return e;var S=ew(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===j}),dataStartIndex:c,dataEndIndex:l}),P=S.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&(0,k.hj)(n)&&(0,k.hj)(o))return!0}return!1})(y.domain,b,v)&&(T=(0,M.LG)(y.domain,null,b),d&&("number"===v||"auto"!==x)&&(C=(0,M.gF)(S,m,"category")));var A=ej(v);if(!T||0===T.length){var E,T,_,C,I,D=null!==(I=y.domain)&&void 0!==I?I:A;if(m){if(T=(0,M.gF)(S,m,v),"category"===v&&d){var N=(0,k.bv)(T);g&&N?(_=T,T=s()(0,P)):g||(T=(0,M.ko)(D,T,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(es(t),[e])},[]))}else if("category"===v)T=g?T.filter(function(t){return""!==t&&!a()(t)}):(0,M.ko)(D,T,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||a()(e)?t:[].concat(es(t),[e])},[]);else if("number"===v){var L=(0,M.ZI)(S,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===j&&(w||!o)}),m,o,f);L&&(T=L)}d&&("number"===v||"auto"!==x)&&(C=(0,M.gF)(S,m,"category"))}else T=d?s()(0,P):u&&u[j]&&u[j].hasStack&&"number"===v?"expand"===h?[0,1]:(0,M.EB)(u[j].stackGroups,c,l):(0,M.s6)(S,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===j&&(w||!r)}),v,f,!0);"number"===v?(T=t$(p,T,j,o,O),D&&(T=(0,M.LG)(D,T,b))):"category"===v&&D&&T.every(function(t){return D.indexOf(t)>=0})&&(T=D)}return ed(ed({},e),{},ey({},j,ed(ed({},y),{},{axisType:o,domain:T,categoricalDomain:C,duplicateDomain:_,originalDomain:null!==(E=y.domain)&&void 0!==E?E:A,isCategorical:d,layout:f})))},{})},eE=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.layout,f=t.children,h=ew(t.data,{graphicalItems:r,dataStartIndex:u,dataEndIndex:c}),d=h.length,y=(0,M.NA)(l,o),v=-1;return r.reduce(function(t,e){var m,b=(void 0!==e.type.defaultProps?ed(ed({},e.type.defaultProps),e.props):e.props)[i],g=ej("number");return t[b]?t:(v++,m=y?s()(0,d):a&&a[b]&&a[b].hasStack?t$(f,m=(0,M.EB)(a[b].stackGroups,u,c),b,o):t$(f,m=(0,M.LG)(g,(0,M.s6)(h,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===b&&!o}),"number",l),n.defaultProps.allowDataOverflow),b,o),ed(ed({},t),{},ey({},b,ed(ed({axisType:o},n.defaultProps),{},{hide:!0,orientation:p()(em,"".concat(o,".").concat(v%2),null),domain:m,originalDomain:g,isCategorical:y,layout:l}))))},{})},eM=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=(0,P.NN)(l,o),p={};return f&&f.length?p=eA(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c}):i&&i.length&&(p=eE(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:u,dataEndIndex:c})),p},ek=function(t){var e=(0,k.Kt)(t),r=(0,M.uY)(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:d()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:(0,M.zT)(e,r)}},eT=function(t){var e=t.children,r=t.defaultShowTooltip,n=(0,P.sP)(e,X),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},e_=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},eC=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,u=void 0===a?{}:a,c=r.width,l=r.height,s=r.children,f=r.margin||{},h=(0,P.sP)(s,X),d=(0,P.sP)(s,w.D),y=Object.keys(u).reduce(function(t,e){var r=u[e],n=r.orientation;return r.mirror||r.hide?t:ed(ed({},t),{},ey({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),v=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:ed(ed({},t),{},ey({},n,p()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),m=ed(ed({},v),y),b=m.bottom;h&&(m.bottom+=h.props.height||X.defaultProps.height),d&&e&&(m=(0,M.By)(m,n,r,e));var g=c-m.left-m.right,x=l-m.top-m.bottom;return ed(ed({brushBottom:b},m),{},{width:Math.max(g,0),height:Math.max(x,0)})},eI=function(t){var e=t.chartName,r=t.GraphicalChild,i=t.defaultTooltipEventType,u=void 0===i?"axis":i,l=t.validateTooltipEventTypes,s=void 0===l?["axis"]:l,f=t.axisComponents,h=t.legendContent,d=t.formatAxisMap,y=t.defaultProps,w=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,u=e.dataStartIndex,c=e.dataEndIndex,l=t.barSize,s=t.layout,p=t.barGap,h=t.barCategoryGap,d=t.maxBarSize,y=e_(s),v=y.numericAxisName,m=y.cateAxisName,g=!!r&&!!r.length&&r.some(function(t){var e=(0,P.Gf)(t&&t.type);return e&&e.indexOf("Bar")>=0}),x=[];return r.forEach(function(r,y){var O=ew(t.data,{graphicalItems:[r],dataStartIndex:u,dataEndIndex:c}),w=void 0!==r.type.defaultProps?ed(ed({},r.type.defaultProps),r.props):r.props,j=w.dataKey,S=w.maxBarSize,A=w["".concat(v,"Id")],E=w["".concat(m,"Id")],k=f.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=w["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||(0,b.Z)(!1);var i=n[o];return ed(ed({},t),{},ey(ey({},r.axisType,i),"".concat(r.axisType,"Ticks"),(0,M.uY)(i)))},{}),T=k[m],_=k["".concat(m,"Ticks")],C=n&&n[A]&&n[A].hasStack&&(0,M.O3)(r,n[A].stackGroups),I=(0,P.Gf)(r.type).indexOf("Bar")>=0,D=(0,M.zT)(T,_),N=[],L=g&&(0,M.pt)({barSize:l,stackGroups:n,totalSize:"xAxis"===m?k[m].width:"yAxis"===m?k[m].height:void 0});if(I){var B,R,z=a()(S)?d:S,U=null!==(B=null!==(R=(0,M.zT)(T,_,!0))&&void 0!==R?R:z)&&void 0!==B?B:0;N=(0,M.qz)({barGap:p,barCategoryGap:h,bandSize:U!==D?U:D,sizeList:L[E],maxBarSize:z}),U!==D&&(N=N.map(function(t){return ed(ed({},t),{},{position:ed(ed({},t.position),{},{offset:t.position.offset-U/2})})}))}var $=r&&r.type&&r.type.getComposedData;$&&x.push({props:ed(ed({},$(ed(ed({},k),{},{displayedData:O,props:t,dataKey:j,item:r,bandSize:D,barPosition:N,offset:o,stackedData:C,layout:s,dataStartIndex:u,dataEndIndex:c}))),{},ey(ey(ey({key:r.key||"item-".concat(y)},v,k[v]),m,k[m]),"animationId",i)),childIndex:(0,P.$R)(r,t.children),item:r})}),x},A=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,u=t.updateId;if(!(0,P.TT)({props:o}))return null;var c=o.children,l=o.layout,s=o.stackOffset,p=o.data,h=o.reverseStackOrder,y=e_(l),v=y.numericAxisName,m=y.cateAxisName,b=(0,P.NN)(c,r),g=(0,M.wh)(p,b,"".concat(v,"Id"),"".concat(m,"Id"),s,h),x=f.reduce(function(t,e){var r="".concat(e.axisType,"Map");return ed(ed({},t),{},ey({},r,eM(o,ed(ed({},e),{},{graphicalItems:b,stackGroups:e.axisType===v&&g,dataStartIndex:i,dataEndIndex:a}))))},{}),O=eC(ed(ed({},x),{},{props:o,graphicalItems:b}),null==n?void 0:n.legendBBox);Object.keys(x).forEach(function(t){x[t]=d(o,x[t],O,t.replace("Map",""),e)});var j=ek(x["".concat(m,"Map")]),S=w(o,ed(ed({},x),{},{dataStartIndex:i,dataEndIndex:a,updateId:u,graphicalItems:b,stackGroups:g,offset:O}));return ed(ed({formattedGraphicalItems:S,graphicalItems:b,offset:O,stackGroups:g},j),x)},E=function(t){var r;function i(t){var r,u,l,s,f;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i),s=i,f=[t],s=ec(s),ey(l=function(t,e){if(e&&("object"===en(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eu()?Reflect.construct(s,f||[],ec(this).constructor):s.apply(this,f)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),ey(l,"accessibilityManager",new tK),ey(l,"handleLegendBBoxUpdate",function(t){if(t){var e=l.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;l.setState(ed({legendBBox:t},A({props:l.props,dataStartIndex:r,dataEndIndex:n,updateId:o},ed(ed({},l.state),{},{legendBBox:t}))))}}),ey(l,"handleReceiveSyncEvent",function(t,e,r){l.props.syncId===t&&(r!==l.eventEmitterSymbol||"function"==typeof l.props.syncMethod)&&l.applySyncEvent(e)}),ey(l,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==l.state.dataStartIndex||r!==l.state.dataEndIndex){var n=l.state.updateId;l.setState(function(){return ed({dataStartIndex:e,dataEndIndex:r},A({props:l.props,dataStartIndex:e,dataEndIndex:r,updateId:n},l.state))}),l.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),ey(l,"handleMouseEnter",function(t){var e=l.getMouseInfo(t);if(e){var r=ed(ed({},e),{},{isTooltipActive:!0});l.setState(r),l.triggerSyncEvent(r);var n=l.props.onMouseEnter;c()(n)&&n(r,t)}}),ey(l,"triggeredAfterMouseMove",function(t){var e=l.getMouseInfo(t),r=e?ed(ed({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};l.setState(r),l.triggerSyncEvent(r);var n=l.props.onMouseMove;c()(n)&&n(r,t)}),ey(l,"handleItemMouseEnter",function(t){l.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),ey(l,"handleItemMouseLeave",function(){l.setState(function(){return{isTooltipActive:!1}})}),ey(l,"handleMouseMove",function(t){t.persist(),l.throttleTriggeredAfterMouseMove(t)}),ey(l,"handleMouseLeave",function(t){l.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};l.setState(e),l.triggerSyncEvent(e);var r=l.props.onMouseLeave;c()(r)&&r(e,t)}),ey(l,"handleOuterEvent",function(t){var e,r=(0,P.Bh)(t),n=p()(l.props,"".concat(r));r&&c()(n)&&n(null!==(e=/.*touch.*/i.test(r)?l.getMouseInfo(t.changedTouches[0]):l.getMouseInfo(t))&&void 0!==e?e:{},t)}),ey(l,"handleClick",function(t){var e=l.getMouseInfo(t);if(e){var r=ed(ed({},e),{},{isTooltipActive:!0});l.setState(r),l.triggerSyncEvent(r);var n=l.props.onClick;c()(n)&&n(r,t)}}),ey(l,"handleMouseDown",function(t){var e=l.props.onMouseDown;c()(e)&&e(l.getMouseInfo(t),t)}),ey(l,"handleMouseUp",function(t){var e=l.props.onMouseUp;c()(e)&&e(l.getMouseInfo(t),t)}),ey(l,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&l.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),ey(l,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&l.handleMouseDown(t.changedTouches[0])}),ey(l,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&l.handleMouseUp(t.changedTouches[0])}),ey(l,"handleDoubleClick",function(t){var e=l.props.onDoubleClick;c()(e)&&e(l.getMouseInfo(t),t)}),ey(l,"handleContextMenu",function(t){var e=l.props.onContextMenu;c()(e)&&e(l.getMouseInfo(t),t)}),ey(l,"triggerSyncEvent",function(t){void 0!==l.props.syncId&&tW.emit(tH,l.props.syncId,t,l.eventEmitterSymbol)}),ey(l,"applySyncEvent",function(t){var e=l.props,r=e.layout,n=e.syncMethod,o=l.state.updateId,i=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)l.setState(ed({dataStartIndex:i,dataEndIndex:a},A({props:l.props,dataStartIndex:i,dataEndIndex:a,updateId:o},l.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,c=t.chartY,s=t.activeTooltipIndex,f=l.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=ed(ed({},p),{},{x:p.left,y:p.top}),v=Math.min(u,y.x+y.width),m=Math.min(c,y.y+y.height),b=h[s]&&h[s].value,g=eS(l.state,l.props.data,s),x=h[s]?{x:"horizontal"===r?h[s].coordinate:v,y:"horizontal"===r?m:h[s].coordinate}:eg;l.setState(ed(ed({},t),{},{activeLabel:b,activeCoordinate:x,activePayload:g,activeTooltipIndex:s}))}else l.setState(t)}),ey(l,"renderCursor",function(t){var r,n=l.state,i=n.isTooltipActive,a=n.activeCoordinate,u=n.activePayload,c=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=l.getTooltipEventType(),h=null!==(r=t.props.active)&&void 0!==r?r:i,d=l.props.layout,y=t.key||"_recharts-cursor";return o().createElement(et,{key:y,activeCoordinate:a,activePayload:u,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:c,tooltipAxisBandSize:f,tooltipEventType:p})}),ey(l,"renderPolarAxis",function(t,e,r){var o=p()(t,"type.axisType"),i=p()(l.state,"".concat(o,"Map")),a=t.type.defaultProps,u=void 0!==a?ed(ed({},a),t.props):t.props,c=i&&i[u["".concat(o,"Id")]];return(0,n.cloneElement)(t,ed(ed({},c),{},{className:(0,m.Z)(o,c.className),key:t.key||"".concat(e,"-").concat(r),ticks:(0,M.uY)(c,!0)}))}),ey(l,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,o=e.polarAngles,i=e.polarRadius,a=l.state,u=a.radiusAxisMap,c=a.angleAxisMap,s=(0,k.Kt)(u),f=(0,k.Kt)(c),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,n.cloneElement)(t,{polarAngles:Array.isArray(o)?o:(0,M.uY)(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:(0,M.uY)(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),ey(l,"renderLegend",function(){var t=l.state.formattedGraphicalItems,e=l.props,r=e.children,o=e.width,i=e.height,a=l.props.margin||{},u=o-(a.left||0)-(a.right||0),c=(0,G.z)({children:r,formattedGraphicalItems:t,legendWidth:u,legendContent:h});if(!c)return null;var s=c.item,f=ea(c,ee);return(0,n.cloneElement)(s,ed(ed({},f),{},{chartWidth:o,chartHeight:i,margin:a,onBBoxUpdate:l.handleLegendBBoxUpdate}))}),ey(l,"renderTooltip",function(){var t,e=l.props,r=e.children,o=e.accessibilityLayer,i=(0,P.sP)(r,O.u);if(!i)return null;var a=l.state,u=a.isTooltipActive,c=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!==(t=i.props.active)&&void 0!==t?t:u;return(0,n.cloneElement)(i,{viewBox:ed(ed({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:c,accessibilityLayer:o})}),ey(l,"renderBrush",function(t){var e=l.props,r=e.margin,o=e.data,i=l.state,a=i.offset,u=i.dataStartIndex,c=i.dataEndIndex,s=i.updateId;return(0,n.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,M.DO)(l.handleBrushChange,t.props.onChange),data:o,x:(0,k.hj)(t.props.x)?t.props.x:a.left,y:(0,k.hj)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:(0,k.hj)(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:c,updateId:"brush-".concat(s)})}),ey(l,"renderReferenceElement",function(t,e,r){if(!t)return null;var o=l.clipPathId,i=l.state,a=i.xAxisMap,u=i.yAxisMap,c=i.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,n.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:u[y],viewBox:{x:c.left,y:c.top,width:c.width,height:c.height},clipPathId:o})}),ey(l,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,n=t.basePoint,o=t.childIndex,a=t.isRange,u=[],c=e.props.key,l=void 0!==e.item.type.defaultProps?ed(ed({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=ed(ed({index:o,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:(0,M.fk)(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},(0,P.L6)(s,!1)),(0,tY.Ym)(s));return u.push(i.renderActiveDot(s,f,"".concat(c,"-activePoint-").concat(o))),n?u.push(i.renderActiveDot(s,ed(ed({},f),{},{cx:n.x,cy:n.y}),"".concat(c,"-basePoint-").concat(o))):a&&u.push(null),u}),ey(l,"renderGraphicChild",function(t,e,r){var o=l.filterFormatItem(t,e,r);if(!o)return null;var i=l.getTooltipEventType(),u=l.state,c=u.isTooltipActive,s=u.tooltipAxis,f=u.activeTooltipIndex,p=u.activeLabel,h=l.props.children,d=(0,P.sP)(h,O.u),y=o.props,v=y.points,m=y.isRange,b=y.baseLine,g=void 0!==o.item.type.defaultProps?ed(ed({},o.item.type.defaultProps),o.item.props):o.item.props,x=g.activeDot,w=g.hide,j=g.activeBar,S=g.activeShape,A={};"axis"!==i&&d&&"click"===d.props.trigger?A={onClick:(0,M.DO)(l.handleItemMouseEnter,t.props.onClick)}:"axis"!==i&&(A={onMouseLeave:(0,M.DO)(l.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,M.DO)(l.handleItemMouseEnter,t.props.onMouseEnter)});var E=(0,n.cloneElement)(t,ed(ed({},o.props),A));if(!w&&c&&d&&(x||j||S)){if(f>=0){if(s.dataKey&&!s.allowDuplicatedCategory){var T="function"==typeof s.dataKey?function(t){return"function"==typeof s.dataKey?s.dataKey(t.payload):null}:"payload.".concat(s.dataKey.toString());C=(0,k.Ap)(v,T,p),I=m&&b&&(0,k.Ap)(b,T,p)}else C=null==v?void 0:v[f],I=m&&b&&b[f];if(S||j){var _=void 0!==t.props.activeIndex?t.props.activeIndex:f;return[(0,n.cloneElement)(t,ed(ed(ed({},o.props),A),{},{activeIndex:_})),null,null]}if(!a()(C))return[E].concat(es(l.renderActivePoints({item:o,activePoint:C,basePoint:I,childIndex:f,isRange:m})))}else{var C,I,D,N=(null!==(D=l.getItemByXY(l.state.activeCoordinate))&&void 0!==D?D:{graphicalItem:E}).graphicalItem,L=N.item,B=void 0===L?t:L,R=N.childIndex,z=ed(ed(ed({},o.props),A),{},{activeIndex:R});return[(0,n.cloneElement)(B,z),null,null]}}return m?[E,null,null]:[E,null]}),ey(l,"renderCustomized",function(t,e,r){return(0,n.cloneElement)(t,ed(ed({key:"recharts-customized-".concat(r)},l.props),l.state))}),ey(l,"renderMap",{CartesianGrid:{handler:ex,once:!0},ReferenceArea:{handler:l.renderReferenceElement},ReferenceLine:{handler:ex},ReferenceDot:{handler:l.renderReferenceElement},XAxis:{handler:ex},YAxis:{handler:ex},Brush:{handler:l.renderBrush,once:!0},Bar:{handler:l.renderGraphicChild},Line:{handler:l.renderGraphicChild},Area:{handler:l.renderGraphicChild},Radar:{handler:l.renderGraphicChild},RadialBar:{handler:l.renderGraphicChild},Scatter:{handler:l.renderGraphicChild},Pie:{handler:l.renderGraphicChild},Funnel:{handler:l.renderGraphicChild},Tooltip:{handler:l.renderCursor,once:!0},PolarGrid:{handler:l.renderPolarGrid,once:!0},PolarAngleAxis:{handler:l.renderPolarAxis},PolarRadiusAxis:{handler:l.renderPolarAxis},Customized:{handler:l.renderCustomized}}),l.clipPathId="".concat(null!==(r=t.id)&&void 0!==r?r:(0,k.EL)("recharts"),"-clip"),l.throttleTriggeredAfterMouseMove=v()(l.triggeredAfterMouseMove,null!==(u=t.throttleDelay)&&void 0!==u?u:1e3/60),l.state={},l}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&el(t,e)}(i,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=(0,P.sP)(e,O.u);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var u=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,c=eS(this.state,r,a,u),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=ed(ed({},f),p.props.points[a].tooltipPosition),c=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:u,activePayload:c,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){(0,P.rL)([(0,P.sP)(t.children,O.u)],[(0,P.sP)(this.props.children,O.u)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,P.sP)(this.props.children,O.u);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return s.indexOf(e)>=0?e:u}return u}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n=(0,V.os)(r),o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var u=this.state,c=u.xAxisMap,l=u.yAxisMap,s=this.getTooltipEventType(),f=eP(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&c&&l){var p=(0,k.Kt)(c).scale,h=(0,k.Kt)(l).scale,d=p&&p.invert?p.invert(o.chartX):null,y=h&&h.invert?h.invert(o.chartY):null;return ed(ed({},o),{},{xValue:d,yValue:y},f)}return f?ed(ed({},o),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var u=this.state,c=u.angleAxisMap,l=u.radiusAxisMap;if(c&&l){var s=(0,k.Kt)(c);return(0,tF.z3)({x:o,y:i},s)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=(0,P.sP)(t,O.u),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),ed(ed({},(0,tY.Ym)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){tW.on(tH,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){tW.removeListener(tH,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===(0,P.Gf)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,i=e.height,a=e.width;return o().createElement("defs",null,o().createElement("clipPath",{id:t},o().createElement("rect",{x:r,y:n,height:i,width:a})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=ei(e,2),n=r[0],o=r[1];return ed(ed({},t),{},ey({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=ei(e,2),n=r[0],o=r[1];return ed(ed({},t),{},ey({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],u=a.props,c=a.item,l=void 0!==c.type.defaultProps?ed(ed({},c.type.defaultProps),c.props):c.props,s=(0,P.Gf)(c.type);if("Bar"===s){var f=(u.data||[]).find(function(e){return(0,S.X)(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(u.data||[]).find(function(e){return(0,tF.z3)(t,e)});if(p)return{graphicalItem:a,payload:p}}else if((0,tJ.lT)(a,n)||(0,tJ.V$)(a,n)||(0,tJ.w7)(a,n)){var h=(0,tJ.a3)({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:ed(ed({},a),{},{childIndex:d}),payload:(0,tJ.w7)(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!(0,P.TT)(this))return null;var n=this.props,i=n.children,a=n.className,u=n.width,c=n.height,l=n.style,s=n.compact,f=n.title,p=n.desc,h=ea(n,er),d=(0,P.L6)(h,!1);if(s)return o().createElement(td.br,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},o().createElement(g.T,eo({},d,{width:u,height:c,title:f,desc:p}),this.renderClipPath(),(0,P.eu)(i,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,d.role=null!==(e=this.props.role)&&void 0!==e?e:"application",d.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){r.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return o().createElement(td.br,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},o().createElement("div",eo({className:(0,m.Z)("recharts-wrapper",a),style:ed({position:"relative",cursor:"default",width:u,height:c},l)},y,{ref:function(t){r.container=t}}),o().createElement(g.T,eo({},d,{width:u,height:c,title:f,desc:p,style:eb}),this.renderClipPath(),(0,P.eu)(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ev(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.Component);ey(E,"displayName",e),ey(E,"defaultProps",ed({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},y)),ey(E,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,u=t.height,c=t.layout,l=t.stackOffset,s=t.margin,f=e.dataStartIndex,p=e.dataEndIndex;if(void 0===e.updateId){var h=eT(t);return ed(ed(ed({},h),{},{updateId:0},A(ed(ed({props:t},h),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:u,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||u!==e.prevHeight||c!==e.prevLayout||l!==e.prevStackOffset||!(0,tZ.w)(s,e.prevMargin)){var d=eT(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=ed(ed({},eP(e,n,c)),{},{updateId:e.updateId+1}),m=ed(ed(ed({},d),y),v);return ed(ed(ed({},m),A(ed({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:u,prevLayout:c,prevStackOffset:l,prevMargin:s,prevChildren:o})}if(!(0,P.rL)(o,e.prevChildren)){var b,g,x,O,w=(0,P.sP)(o,X),j=w&&null!==(b=null===(g=w.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:f,S=w&&null!==(x=null===(O=w.props)||void 0===O?void 0:O.endIndex)&&void 0!==x?x:p,E=a()(n)||j!==f||S!==p?e.updateId+1:e.updateId;return ed(ed({updateId:E},A(ed(ed({props:t},e),{},{updateId:E,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),ey(E,"renderActiveDot",function(t,e,r){var i;return i=(0,n.isValidElement)(t)?(0,n.cloneElement)(t,e):c()(t)?t(e):o().createElement(j.o,e),o().createElement(x.m,{className:"recharts-active-dot",key:r},i)});var T=(0,n.forwardRef)(function(t,e){return o().createElement(E,eo({},t,{ref:e}))});return T.displayName=E.displayName,T}},95949:(t,e,r)=>{"use strict";r.d(e,{b:()=>n});var n=function(t){return null};n.displayName="Cell"},60288:(t,e,r)=>{"use strict";r.d(e,{_:()=>A});var n=r(60343),o=r.n(n),i=r(28288),a=r.n(i),u=r(86830),c=r.n(u),l=r(4171),s=r.n(l),f=r(28411),p=r(821),h=r(80131),d=r(43229),y=r(80076);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var m=["offset"];function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function O(){return(O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var w=function(t){var e=t.value,r=t.formatter,n=a()(t.children)?e:t.children;return c()(r)?r(n):n},j=function(t,e,r){var n,i,u=t.position,c=t.viewBox,l=t.offset,s=t.className,p=c.cx,h=c.cy,v=c.innerRadius,m=c.outerRadius,b=c.startAngle,g=c.endAngle,x=c.clockWise,w=(v+m)/2,j=(0,d.uY)(g-b)*Math.min(Math.abs(g-b),360),S=j>=0?1:-1;"insideStart"===u?(n=b+S*l,i=x):"insideEnd"===u?(n=g-S*l,i=!x):"end"===u&&(n=g+S*l,i=x),i=j<=0?i:!i;var P=(0,y.op)(p,h,w,n),A=(0,y.op)(p,h,w,n+(i?1:-1)*359),E="M".concat(P.x,",").concat(P.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(i?0:1,",\n    ").concat(A.x,",").concat(A.y),M=a()(t.id)?(0,d.EL)("recharts-radial-line-"):t.id;return o().createElement("text",O({},r,{dominantBaseline:"central",className:(0,f.Z)("recharts-radial-bar-label",s)}),o().createElement("defs",null,o().createElement("path",{id:M,d:E})),o().createElement("textPath",{xlinkHref:"#".concat(M)},e))},S=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,u=e.outerRadius,c=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=(0,y.op)(o,i,u+r,c),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=(0,y.op)(o,i,(a+u)/2,c);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},P=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,u=e.width,c=e.height,l=c>=0?1:-1,f=l*n,p=l>0?"end":"start",h=l>0?"start":"end",y=u>=0?1:-1,v=y*n,m=y>0?"end":"start",b=y>0?"start":"end";if("top"===o)return x(x({},{x:i+u/2,y:a-l*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(a-r.y,0),width:u}:{});if("bottom"===o)return x(x({},{x:i+u/2,y:a+c+f,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(r.y+r.height-(a+c),0),width:u}:{});if("left"===o){var g={x:i-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"};return x(x({},g),r?{width:Math.max(g.x-r.x,0),height:c}:{})}if("right"===o){var O={x:i+u+v,y:a+c/2,textAnchor:b,verticalAnchor:"middle"};return x(x({},O),r?{width:Math.max(r.x+r.width-O.x,0),height:c}:{})}var w=r?{width:u,height:c}:{};return"insideLeft"===o?x({x:i+v,y:a+c/2,textAnchor:b,verticalAnchor:"middle"},w):"insideRight"===o?x({x:i+u-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"},w):"insideTop"===o?x({x:i+u/2,y:a+f,textAnchor:"middle",verticalAnchor:h},w):"insideBottom"===o?x({x:i+u/2,y:a+c-f,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===o?x({x:i+v,y:a+f,textAnchor:b,verticalAnchor:h},w):"insideTopRight"===o?x({x:i+u-v,y:a+f,textAnchor:m,verticalAnchor:h},w):"insideBottomLeft"===o?x({x:i+v,y:a+c-f,textAnchor:b,verticalAnchor:p},w):"insideBottomRight"===o?x({x:i+u-v,y:a+c-f,textAnchor:m,verticalAnchor:p},w):s()(o)&&((0,d.hj)(o.x)||(0,d.hU)(o.x))&&((0,d.hj)(o.y)||(0,d.hU)(o.y))?x({x:i+(0,d.h1)(o.x,u),y:a+(0,d.h1)(o.y,c),textAnchor:"end",verticalAnchor:"end"},w):x({x:i+u/2,y:a+c/2,textAnchor:"middle",verticalAnchor:"middle"},w)};function A(t){var e,r=t.offset,i=x({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,m)),u=i.viewBox,l=i.position,s=i.value,y=i.children,v=i.content,b=i.className,g=i.textBreakAll;if(!u||a()(s)&&a()(y)&&!(0,n.isValidElement)(v)&&!c()(v))return null;if((0,n.isValidElement)(v))return(0,n.cloneElement)(v,i);if(c()(v)){if(e=(0,n.createElement)(v,i),(0,n.isValidElement)(e))return e}else e=w(i);var A="cx"in u&&(0,d.hj)(u.cx),E=(0,h.L6)(i,!0);if(A&&("insideStart"===l||"insideEnd"===l||"end"===l))return j(i,e,E);var M=A?S(i):P(i);return o().createElement(p.x,O({className:(0,f.Z)("recharts-label",void 0===b?"":b)},E,M,{breakAll:g}),e)}A.displayName="Label";var E=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,u=t.radius,c=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,y=t.width,v=t.height,m=t.clockWise,b=t.labelViewBox;if(b)return b;if((0,d.hj)(y)&&(0,d.hj)(v)){if((0,d.hj)(s)&&(0,d.hj)(f))return{x:s,y:f,width:y,height:v};if((0,d.hj)(p)&&(0,d.hj)(h))return{x:p,y:h,width:y,height:v}}return(0,d.hj)(s)&&(0,d.hj)(f)?{x:s,y:f,width:0,height:0}:(0,d.hj)(e)&&(0,d.hj)(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:c||0,outerRadius:l||u||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};A.parseViewBox=E,A.renderCallByParent=function(t,e){var r,i,a=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&a&&!t.label)return null;var u=t.children,l=E(t),f=(0,h.NN)(u,A).map(function(t,r){return(0,n.cloneElement)(t,{viewBox:e||l,key:"label-".concat(r)})});return a?[(r=t.label,i=e||l,r?!0===r?o().createElement(A,{key:"label-implicit",viewBox:i}):(0,d.P2)(r)?o().createElement(A,{key:"label-implicit",viewBox:i,value:r}):(0,n.isValidElement)(r)?r.type===A?(0,n.cloneElement)(r,{key:"label-implicit",viewBox:i}):o().createElement(A,{key:"label-implicit",content:r,viewBox:i}):c()(r)?o().createElement(A,{key:"label-implicit",content:r,viewBox:i}):s()(r)?o().createElement(A,O({viewBox:i},r,{key:"label-implicit"})):null:null)].concat(function(t){if(Array.isArray(t))return b(t)}(f)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(f)||function(t,e){if(t){if("string"==typeof t)return b(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(t,void 0)}}(f)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):f}},70953:(t,e,r)=>{"use strict";r.d(e,{e:()=>A});var n=r(60343),o=r.n(n),i=r(28288),a=r.n(i),u=r(4171),c=r.n(u),l=r(86830),s=r.n(l),f=r(66245),p=r.n(f),h=r(60288),d=r(99829),y=r(80131),v=r(56361);function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var b=["valueAccessor"],g=["data","dataKey","clockWise","id","textBreakAll"];function x(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function O(){return(O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=m(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==m(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function S(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var P=function(t){return Array.isArray(t.value)?p()(t.value):t.value};function A(t){var e=t.valueAccessor,r=void 0===e?P:e,n=S(t,b),i=n.data,u=n.dataKey,c=n.clockWise,l=n.id,s=n.textBreakAll,f=S(n,g);return i&&i.length?o().createElement(d.m,{className:"recharts-label-list"},i.map(function(t,e){var n=a()(u)?r(t,e):(0,v.F$)(t&&t.payload,u),i=a()(l)?{}:{id:"".concat(l,"-").concat(e)};return o().createElement(h._,O({},(0,y.L6)(t,!0),f,i,{parentViewBox:t.parentViewBox,value:n,textBreakAll:s,viewBox:h._.parseViewBox(a()(c)?t:j(j({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}A.displayName="LabelList",A.renderCallByParent=function(t,e){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var a=t.children,u=(0,y.NN)(a,A).map(function(t,r){return(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return i?[(r=t.label)?!0===r?o().createElement(A,{key:"labelList-implicit",data:e}):o().isValidElement(r)||s()(r)?o().createElement(A,{key:"labelList-implicit",data:e,content:r}):c()(r)?o().createElement(A,O({data:e},r,{key:"labelList-implicit"})):null:null].concat(function(t){if(Array.isArray(t))return x(t)}(u)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(u)||function(t,e){if(t){if("string"==typeof t)return x(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return x(t,void 0)}}(u)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):u}},94309:(t,e,r)=>{"use strict";r.d(e,{D:()=>D});var n=r(60343),o=r.n(n),i=r(86830),a=r.n(i),u=r(28411),c=r(60307),l=r(27921),s=r(10101),f=r(34718);function p(t){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h(){return(h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function y(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(y=function(){return!!t})()}function v(t){return(v=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function m(t,e){return(m=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function b(t,e,r){return(e=g(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function g(t){var e=function(t,e){if("object"!=p(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p(e)?e:e+""}var x=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=v(t),function(t,e){if(e&&("object"===p(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,y()?Reflect.construct(t,e||[],v(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&m(t,e)}(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,i=t.inactive?e:t.color;if("plainline"===t.type)return o().createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return o().createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return o().createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(o().isValidElement(t.legendIcon)){var a=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){b(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete a.legendIcon,o().cloneElement(t.legendIcon,a)}return o().createElement(s.v,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,i=e.layout,s=e.formatter,p=e.inactiveColor,d={x:0,y:0,width:32,height:32},y={display:"horizontal"===i?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var i=e.formatter||s,m=(0,u.Z)(b(b({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var g=a()(e.value)?null:e.value;(0,c.Z)(!a()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var x=e.inactive?p:e.color;return o().createElement("li",h({className:m,style:y,key:"legend-item-".concat(r)},(0,f.bw)(t.props,e,r)),o().createElement(l.T,{width:n,height:n,viewBox:d,style:v},t.renderIcon(e)),o().createElement("span",{className:"recharts-legend-item-text",style:{color:x}},i?i(g,e,r):g))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?o().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,g(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);b(x,"displayName","Legend"),b(x,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var O=r(43229),w=r(37880);function j(t){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var S=["ref"];function P(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?P(Object(r),!0).forEach(function(e){_(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function E(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,C(n.key),n)}}function M(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(M=function(){return!!t})()}function k(t){return(k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function T(t,e){return(T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function _(t,e,r){return(e=C(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function C(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:e+""}function I(t){return t.value}var D=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=k(e),_(t=function(t,e){if(e&&("object"===j(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,M()?Reflect.construct(e,r||[],k(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&T(t,e)}(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?A({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,u=n.margin,c=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:u&&u.right||0}:{left:u&&u.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:u&&u.bottom||0}:{top:u&&u.top||0}),A(A({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,i=e.height,a=e.wrapperStyle,u=e.payloadUniqBy,c=e.payload,l=A(A({position:"absolute",width:n||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return o().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(o().isValidElement(t))return o().cloneElement(t,e);if("function"==typeof t)return o().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,S);return o().createElement(x,r)}(r,A(A({},this.props),{},{payload:(0,w.z)(c,u,I)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=A(A({},this.defaultProps),t.props).layout;return"vertical"===r&&(0,O.hj)(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&E(n.prototype,e),r&&E(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(n.PureComponent);_(D,"displayName","Legend"),_(D,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},59447:(t,e,r)=>{"use strict";r.d(e,{h:()=>y});var n=r(28411),o=r(60343),i=r.n(o),a=r(37424),u=r.n(a),c=r(43229),l=r(60307),s=r(80131);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var y=(0,o.forwardRef)(function(t,e){var r,a=t.aspect,f=t.initialDimension,p=void 0===f?{width:-1,height:-1}:f,y=t.width,v=void 0===y?"100%":y,m=t.height,b=void 0===m?"100%":m,g=t.minWidth,x=void 0===g?0:g,O=t.minHeight,w=t.maxHeight,j=t.children,S=t.debounce,P=void 0===S?0:S,A=t.id,E=t.className,M=t.onResize,k=t.style,T=(0,o.useRef)(null),_=(0,o.useRef)();_.current=M,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(T.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),T.current},configurable:!0})});var C=function(t){if(Array.isArray(t))return t}(r=(0,o.useState)({containerWidth:p.width,containerHeight:p.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(r,2)||function(t,e){if(t){if("string"==typeof t)return d(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,2)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),I=C[0],D=C[1],N=(0,o.useCallback)(function(t,e){D(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;N(n,o),null===(e=_.current)||void 0===e||e.call(_,n,o)};P>0&&(t=u()(t,P,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=T.current.getBoundingClientRect();return N(r.width,r.height),e.observe(T.current),function(){e.disconnect()}},[N,P]);var L=(0,o.useMemo)(function(){var t=I.containerWidth,e=I.containerHeight;if(t<0||e<0)return null;(0,l.Z)((0,c.hU)(v)||(0,c.hU)(b),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",v,b),(0,l.Z)(!a||a>0,"The aspect(%s) must be greater than zero.",a);var r=(0,c.hU)(v)?t:v,n=(0,c.hU)(b)?e:b;a&&a>0&&(r?n=r/a:n&&(r=n*a),w&&n>w&&(n=w)),(0,l.Z)(r>0||n>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,n,v,b,x,O,a);var u=!Array.isArray(j)&&(0,s.Gf)(j.type).endsWith("Chart");return i().Children.map(j,function(t){return i().isValidElement(t)?(0,o.cloneElement)(t,h({width:r,height:n},u?{style:h({height:"100%",width:"100%",maxHeight:n,maxWidth:r},t.props.style)}:{})):t})},[a,j,b,w,O,x,I,v]);return i().createElement("div",{id:A?"".concat(A):void 0,className:(0,n.Z)("recharts-responsive-container",E),style:h(h({},void 0===k?{}:k),{},{width:v,height:b,minWidth:x,minHeight:O,maxHeight:w}),ref:T},L)})},821:(t,e,r)=>{"use strict";r.d(e,{x:()=>R});var n=r(60343),o=r.n(n),i=r(28288),a=r.n(i),u=r(28411),c=r(43229),l=r(12307),s=r(80131),f=r(94438);function p(t){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=p(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p(e)?e:e+""}(n.key),n)}}var v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,b=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,g=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,x={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},O=Object.keys(x),w=function(){var t,e;function r(t,e){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,r),this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||b.test(e)||(this.num=NaN,this.unit=""),O.includes(e)&&(this.num=t*x[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=h(null!==(e=g.exec(t))&&void 0!==e?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&y(r.prototype,t),e&&y(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function j(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=h(null!==(r=v.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],u=w.parse(null!=o?o:""),c=w.parse(null!=a?a:""),l="*"===i?u.multiply(c):u.divide(c);if(l.isNaN())return"NaN";e=e.replace(v,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=h(null!==(s=m.exec(e))&&void 0!==s?s:[],4),p=f[1],d=f[2],y=f[3],b=w.parse(null!=p?p:""),g=w.parse(null!=y?y:""),x="+"===d?b.add(g):b.subtract(g);if(x.isNaN())return"NaN";e=e.replace(m,x.toString())}return e}var S=/\(([^()]*)\)/;function P(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=h(S.exec(e),2)[1];e=e.replace(S,j(r))}return e}(e),e=j(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var A=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],E=["dx","dy","angle","className","breakAll"];function M(){return(M=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function T(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return _(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return _(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function _(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var C=/[ \f\n\r\t\v\u2028\u2029]+/,I=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];a()(e)||(o=r?e.toString().split(""):e.toString().split(C));var i=o.map(function(t){return{word:t,width:(0,f.xE)(t,n).width}}),u=r?0:(0,f.xE)("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:u}}catch(t){return null}},D=function(t,e,r,n,o){var i,a=t.maxLines,u=t.children,l=t.style,s=t.breakAll,f=(0,c.hj)(a),p=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,u=t[t.length-1];return u&&(null==n||o||u.width+a+r<Number(n))?(u.words.push(i),u.width+=a+r):t.push({words:[i],width:a}),t},[])},h=p(e);if(!f)return h;for(var d=function(t){var e=p(I({breakAll:s,style:l,children:u.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},y=0,v=u.length-1,m=0;y<=v&&m<=u.length-1;){var b=Math.floor((y+v)/2),g=T(d(b-1),2),x=g[0],O=g[1],w=T(d(b),1)[0];if(x||w||(y=b+1),x&&w&&(v=b-1),!x&&w){i=O;break}m++}return i||h},N=function(t){return[{words:a()(t)?[]:t.toString().split(C)}]},L=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!l.x.isSsr){var u=I({breakAll:i,children:n,style:o});return u?D({breakAll:i,children:n,maxLines:a,style:o},u.wordsWithComputedWidth,u.spaceWidth,e,r):N(n)}return N(n)},B="#808080",R=function(t){var e,r=t.x,i=void 0===r?0:r,a=t.y,l=void 0===a?0:a,f=t.lineHeight,p=void 0===f?"1em":f,h=t.capHeight,d=void 0===h?"0.71em":h,y=t.scaleToFit,v=void 0!==y&&y,m=t.textAnchor,b=t.verticalAnchor,g=t.fill,x=void 0===g?B:g,O=k(t,A),w=(0,n.useMemo)(function(){return L({breakAll:O.breakAll,children:O.children,maxLines:O.maxLines,scaleToFit:v,style:O.style,width:O.width})},[O.breakAll,O.children,O.maxLines,v,O.style,O.width]),j=O.dx,S=O.dy,T=O.angle,_=O.className,C=O.breakAll,I=k(O,E);if(!(0,c.P2)(i)||!(0,c.P2)(l))return null;var D=i+((0,c.hj)(j)?j:0),N=l+((0,c.hj)(S)?S:0);switch(void 0===b?"end":b){case"start":e=P("calc(".concat(d,")"));break;case"middle":e=P("calc(".concat((w.length-1)/2," * -").concat(p," + (").concat(d," / 2))"));break;default:e=P("calc(".concat(w.length-1," * -").concat(p,")"))}var R=[];if(v){var z=w[0].width,U=O.width;R.push("scale(".concat(((0,c.hj)(U)?U/z:1)/z,")"))}return T&&R.push("rotate(".concat(T,", ").concat(D,", ").concat(N,")")),R.length&&(I.transform=R.join(" ")),o().createElement("text",M({},(0,s.L6)(I,!0),{x:D,y:N,className:(0,u.Z)("recharts-text",_),textAnchor:void 0===m?"start":m,fill:x.includes("url")?B:x}),w.map(function(t,r){var n=t.words.join(C?"":" ");return o().createElement("tspan",{x:D,dy:0===r?e:p,key:"".concat(n,"-").concat(r)},n)}))}},52419:(t,e,r)=>{"use strict";r.d(e,{u:()=>Z});var n=r(60343),o=r.n(n),i=r(38701),a=r.n(i),u=r(28288),c=r.n(u),l=r(28411),s=r(43229);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(){return(p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function y(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function v(t){return Array.isArray(t)&&(0,s.P2)(t[0])&&(0,s.P2)(t[1])?t.join(" ~ "):t}var m=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,i=t.itemStyle,u=void 0===i?{}:i,f=t.labelStyle,d=t.payload,m=t.formatter,b=t.itemSorter,g=t.wrapperClassName,x=t.labelClassName,O=t.label,w=t.labelFormatter,j=t.accessibilityLayer,S=y({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),P=y({margin:0},void 0===f?{}:f),A=!c()(O),E=A?O:"",M=(0,l.Z)("recharts-default-tooltip",g),k=(0,l.Z)("recharts-tooltip-label",x);return A&&w&&null!=d&&(E=w(O,d)),o().createElement("div",p({className:M,style:S},void 0!==j&&j?{role:"status","aria-live":"assertive"}:{}),o().createElement("p",{className:k,style:P},o().isValidElement(E)?E:"".concat(E)),function(){if(d&&d.length){var t=(b?a()(d,b):d).map(function(t,e){if("none"===t.type)return null;var n=y({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},u),i=t.formatter||m||v,a=t.value,c=t.name,l=a,f=c;if(i&&null!=l&&null!=f){var p=i(a,c,t,e,d);if(Array.isArray(p)){var b=function(t){if(Array.isArray(t))return t}(p)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(p,2)||function(t,e){if(t){if("string"==typeof t)return h(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,2)}}(p,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();l=b[0],f=b[1]}else l=p}return o().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},(0,s.P2)(f)?o().createElement("span",{className:"recharts-tooltip-item-name"},f):null,(0,s.P2)(f)?o().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,o().createElement("span",{className:"recharts-tooltip-item-value"},l),o().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return o().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function b(t){return(b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function g(t,e,r){var n;return(n=function(t,e){if("object"!=b(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=b(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==b(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var x="recharts-tooltip-wrapper",O={visibility:"hidden"};function w(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,u=t.tooltipDimension,c=t.viewBox,l=t.viewBoxDimension;if(i&&(0,s.hj)(i[n]))return i[n];var f=r[n]-u-o,p=r[n]+o;return e[n]?a[n]?f:p:a[n]?f<c[n]?Math.max(p,c[n]):Math.max(f,c[n]):p+u>c[n]+l?Math.max(f,c[n]):Math.max(p,c[n])}function j(t){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function P(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach(function(e){k(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function A(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(A=function(){return!!t})()}function E(t){return(E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function M(t,e){return(M=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function k(t,e,r){return(e=T(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function T(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:e+""}var _=function(t){var e;function r(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r);for(var t,e,n,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r,n=[].concat(i),e=E(e),k(t=function(t,e){if(e&&("object"===j(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,A()?Reflect.construct(e,n||[],E(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),k(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&M(t,e)}(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,i,a,u,c,f,p,h,d,y,v,m,b,j,S,A,E=this,M=this.props,k=M.active,T=M.allowEscapeViewBox,_=M.animationDuration,C=M.animationEasing,I=M.children,D=M.coordinate,N=M.hasPayload,L=M.isAnimationActive,B=M.offset,R=M.position,z=M.reverseDirection,U=M.useTranslate3d,$=M.viewBox,F=M.wrapperStyle,Z=(d=(t={allowEscapeViewBox:T,coordinate:D,offsetTopLeft:B,position:R,reverseDirection:z,tooltipBox:this.state.lastBoundingBox,useTranslate3d:U,viewBox:$}).allowEscapeViewBox,y=t.coordinate,v=t.offsetTopLeft,m=t.position,b=t.reverseDirection,j=t.tooltipBox,S=t.useTranslate3d,A=t.viewBox,j.height>0&&j.width>0&&y?(r=(e={translateX:p=w({allowEscapeViewBox:d,coordinate:y,key:"x",offsetTopLeft:v,position:m,reverseDirection:b,tooltipDimension:j.width,viewBox:A,viewBoxDimension:A.width}),translateY:h=w({allowEscapeViewBox:d,coordinate:y,key:"y",offsetTopLeft:v,position:m,reverseDirection:b,tooltipDimension:j.height,viewBox:A,viewBoxDimension:A.height}),useTranslate3d:S}).translateX,n=e.translateY,f={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):f=O,{cssProperties:f,cssClasses:(a=(i={translateX:p,translateY:h,coordinate:y}).coordinate,u=i.translateX,c=i.translateY,(0,l.Z)(x,g(g(g(g({},"".concat(x,"-right"),(0,s.hj)(u)&&a&&(0,s.hj)(a.x)&&u>=a.x),"".concat(x,"-left"),(0,s.hj)(u)&&a&&(0,s.hj)(a.x)&&u<a.x),"".concat(x,"-bottom"),(0,s.hj)(c)&&a&&(0,s.hj)(a.y)&&c>=a.y),"".concat(x,"-top"),(0,s.hj)(c)&&a&&(0,s.hj)(a.y)&&c<a.y)))}),q=Z.cssClasses,W=Z.cssProperties,H=P(P({transition:L&&k?"transform ".concat(_,"ms ").concat(C):void 0},W),{},{pointerEvents:"none",visibility:!this.state.dismissed&&k&&N?"visible":"hidden",position:"absolute",top:0,left:0},F);return o().createElement("div",{tabIndex:-1,className:q,style:H,ref:function(t){E.wrapperNode=t}},I)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,T(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent),C=r(12307),I=r(37880);function D(t){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function N(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function L(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?N(Object(r),!0).forEach(function(e){U(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):N(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function B(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(B=function(){return!!t})()}function R(t){return(R=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function z(t,e){return(z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function U(t,e,r){return(e=$(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function $(t){var e=function(t,e){if("object"!=D(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=D(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==D(e)?e:e+""}function F(t){return t.dataKey}var Z=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=R(t),function(t,e){if(e&&("object"===D(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,B()?Reflect.construct(t,e||[],R(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&z(t,e)}(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,i=r.allowEscapeViewBox,a=r.animationDuration,u=r.animationEasing,c=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,v=r.reverseDirection,b=r.useTranslate3d,g=r.viewBox,x=r.wrapperStyle,O=null!=h?h:[];s&&O.length&&(O=(0,I.z)(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,F));var w=O.length>0;return o().createElement(_,{allowEscapeViewBox:i,animationDuration:a,animationEasing:u,isAnimationActive:f,active:n,coordinate:l,hasPayload:w,offset:p,position:y,reverseDirection:v,useTranslate3d:b,viewBox:g,wrapperStyle:x},(t=L(L({},this.props),{},{payload:O}),o().isValidElement(c)?o().cloneElement(c,t):"function"==typeof c?o().createElement(c,t):o().createElement(m,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,$(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);U(Z,"displayName","Tooltip"),U(Z,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!C.x.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},99829:(t,e,r)=>{"use strict";r.d(e,{m:()=>l});var n=r(60343),o=r.n(n),i=r(28411),a=r(80131),u=["children","className"];function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var l=o().forwardRef(function(t,e){var r=t.children,n=t.className,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,u),s=(0,i.Z)("recharts-layer",n);return o().createElement("g",c({className:s},(0,a.L6)(l,!0),{ref:e}),r)})},27921:(t,e,r)=>{"use strict";r.d(e,{T:()=>l});var n=r(60343),o=r.n(n),i=r(28411),a=r(80131),u=["children","width","height","viewBox","className","style","title","desc"];function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function l(t){var e=t.children,r=t.width,n=t.height,l=t.viewBox,s=t.className,f=t.style,p=t.title,h=t.desc,d=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,u),y=l||{width:r,height:n,x:0,y:0},v=(0,i.Z)("recharts-surface",s);return o().createElement("svg",c({},(0,a.L6)(d,!0,"svg"),{className:v,width:r,height:n,style:f,viewBox:"".concat(y.x," ").concat(y.y," ").concat(y.width," ").concat(y.height)}),o().createElement("title",null,p),o().createElement("desc",null,h),e)}},31170:(t,e,r)=>{"use strict";r.d(e,{br:()=>y,Mw:()=>O,zn:()=>x,sp:()=>v,d2:()=>g,bH:()=>m,Ud:()=>b});var n=r(60343),o=r.n(n),i=r(56501);r(92254),r(84535);var a=r(86366),u=r.n(a)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),c=(0,n.createContext)(void 0),l=(0,n.createContext)(void 0),s=(0,n.createContext)(void 0),f=(0,n.createContext)({}),p=(0,n.createContext)(void 0),h=(0,n.createContext)(0),d=(0,n.createContext)(0),y=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,i=e.offset,a=t.clipPathId,y=t.children,v=t.width,m=t.height,b=u(i);return o().createElement(c.Provider,{value:r},o().createElement(l.Provider,{value:n},o().createElement(f.Provider,{value:i},o().createElement(s.Provider,{value:b},o().createElement(p.Provider,{value:a},o().createElement(h.Provider,{value:m},o().createElement(d.Provider,{value:v},y)))))))},v=function(){return(0,n.useContext)(p)},m=function(t){var e=(0,n.useContext)(c);null!=e||(0,i.Z)(!1);var r=e[t];return null!=r||(0,i.Z)(!1),r},b=function(t){var e=(0,n.useContext)(l);null!=e||(0,i.Z)(!1);var r=e[t];return null!=r||(0,i.Z)(!1),r},g=function(){return(0,n.useContext)(s)},x=function(){return(0,n.useContext)(d)},O=function(){return(0,n.useContext)(h)}},15559:(t,e,r)=>{"use strict";r.d(e,{I:()=>D});var n=r(60343),o=r.n(n),i=r(86830),a=r.n(i),u=r(28411),c=r(99829),l=r(35590),s=r(80131),f=["points","className","baseLinePoints","connectNulls"];function p(){return(p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function h(t){return function(t){if(Array.isArray(t))return d(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return d(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var y=function(t){return t&&t.x===+t.x&&t.y===+t.y},v=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach(function(t){y(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])}),y(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e},m=function(t,e){var r=v(t);e&&(r=[r.reduce(function(t,e){return[].concat(h(t),h(e))},[])]);var n=r.map(function(t){return t.reduce(function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},b=function(t,e,r){var n=m(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(m(e.reverse(),r).slice(1))},g=function(t){var e=t.points,r=t.className,n=t.baseLinePoints,i=t.connectNulls,a=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,f);if(!e||!e.length)return null;var c=(0,u.Z)("recharts-polygon",r);if(n&&n.length){var l=a.stroke&&"none"!==a.stroke,h=b(e,n,i);return o().createElement("g",{className:c},o().createElement("path",p({},(0,s.L6)(a,!0),{fill:"Z"===h.slice(-1)?a.fill:"none",stroke:"none",d:h})),l?o().createElement("path",p({},(0,s.L6)(a,!0),{fill:"none",d:m(e,i)})):null,l?o().createElement("path",p({},(0,s.L6)(a,!0),{fill:"none",d:m(n,i)})):null)}var d=m(e,i);return o().createElement("path",p({},(0,s.L6)(a,!0),{fill:"Z"===d.slice(-1)?a.fill:"none",className:c,d:d}))},x=r(821),O=r(34718),w=r(80076);function j(t){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function S(){return(S=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function P(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function A(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?P(Object(r),!0).forEach(function(e){_(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):P(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function E(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,C(n.key),n)}}function M(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(M=function(){return!!t})()}function k(t){return(k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function T(t,e){return(T=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function _(t,e,r){return(e=C(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function C(t){var e=function(t,e){if("object"!=j(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=j(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==j(e)?e:e+""}var I=Math.PI/180,D=function(t){var e,r;function n(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=k(t),function(t,e){if(e&&("object"===j(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,M()?Reflect.construct(t,e||[],k(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&T(t,e)}(n,t),e=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,o=e.radius,i=e.orientation,a=e.tickSize,u=(0,w.op)(r,n,o,t.coordinate),c=(0,w.op)(r,n,o+("inner"===i?-1:1)*(a||8),t.coordinate);return{x1:u.x,y1:u.y,x2:c.x,y2:c.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*I);return r>1e-5?"outer"===e?"start":"end":r<-.00001?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.radius,i=t.axisLine,a=t.axisLineType,u=A(A({},(0,s.L6)(this.props,!1)),{},{fill:"none"},(0,s.L6)(i,!1));if("circle"===a)return o().createElement(l.o,S({className:"recharts-polar-angle-axis-line"},u,{cx:e,cy:r,r:n}));var c=this.props.ticks.map(function(t){return(0,w.op)(e,r,n,t.coordinate)});return o().createElement(g,S({className:"recharts-polar-angle-axis-line"},u,{points:c}))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,i=e.tick,a=e.tickLine,l=e.tickFormatter,f=e.stroke,p=(0,s.L6)(this.props,!1),h=(0,s.L6)(i,!1),d=A(A({},p),{},{fill:"none"},(0,s.L6)(a,!1)),y=r.map(function(e,r){var s=t.getTickLineCoord(e),y=A(A(A({textAnchor:t.getTickTextAnchor(e)},p),{},{stroke:"none",fill:f},h),{},{index:r,payload:e,x:s.x2,y:s.y2});return o().createElement(c.m,S({className:(0,u.Z)("recharts-polar-angle-axis-tick",(0,w.$S)(i)),key:"tick-".concat(e.coordinate)},(0,O.bw)(t.props,e,r)),a&&o().createElement("line",S({className:"recharts-polar-angle-axis-tick-line"},d,s)),i&&n.renderTickItem(i,y,l?l(e.value,r):e.value))});return o().createElement(c.m,{className:"recharts-polar-angle-axis-ticks"},y)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.radius,n=t.axisLine;return!(r<=0)&&e&&e.length?o().createElement(c.m,{className:(0,u.Z)("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(t,e,r){return o().isValidElement(t)?o().cloneElement(t,e):a()(t)?t(e):o().createElement(x.x,S({},e,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],e&&E(n.prototype,e),r&&E(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(n.PureComponent);_(D,"displayName","PolarAngleAxis"),_(D,"axisType","angleAxis"),_(D,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0})},26445:(t,e,r)=>{"use strict";r.d(e,{S:()=>_});var n=r(60343),o=r.n(n),i=r(27972),a=r.n(i),u=r(11717),c=r.n(u),l=r(86830),s=r.n(l),f=r(28411),p=r(821),h=r(60288),d=r(99829),y=r(80076),v=r(34718),m=r(80131),b=["cx","cy","angle","ticks","axisLine"],g=["ticks","tick","angle","tickFormatter","stroke"];function x(t){return(x="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function O(){return(O=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function w(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function j(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?w(Object(r),!0).forEach(function(e){k(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):w(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function S(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function P(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,T(n.key),n)}}function A(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(A=function(){return!!t})()}function E(t){return(E=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function M(t,e){return(M=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function k(t,e,r){return(e=T(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function T(t){var e=function(t,e){if("object"!=x(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=x(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==x(e)?e:e+""}var _=function(t){var e,r;function n(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=E(t),function(t,e){if(e&&("object"===x(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,A()?Reflect.construct(t,e||[],E(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&M(t,e)}(n,t),e=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle,o=r.cx,i=r.cy;return(0,y.op)(o,i,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=a()(o,function(t){return t.coordinate||0});return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:c()(o,function(t){return t.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,i=t.ticks,a=t.axisLine,u=S(t,b),c=i.reduce(function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]},[1/0,-1/0]),l=(0,y.op)(e,r,c[0],n),s=(0,y.op)(e,r,c[1],n),f=j(j(j({},(0,m.L6)(u,!1)),{},{fill:"none"},(0,m.L6)(a,!1)),{},{x1:l.x,y1:l.y,x2:s.x,y2:s.y});return o().createElement("line",O({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,i=e.tick,a=e.angle,u=e.tickFormatter,c=e.stroke,l=S(e,g),s=this.getTickTextAnchor(),p=(0,m.L6)(l,!1),h=(0,m.L6)(i,!1),b=r.map(function(e,r){var l=t.getTickValueCoord(e),m=j(j(j(j({textAnchor:s,transform:"rotate(".concat(90-a,", ").concat(l.x,", ").concat(l.y,")")},p),{},{stroke:"none",fill:c},h),{},{index:r},l),{},{payload:e});return o().createElement(d.m,O({className:(0,f.Z)("recharts-polar-radius-axis-tick",(0,y.$S)(i)),key:"tick-".concat(e.coordinate)},(0,v.bw)(t.props,e,r)),n.renderTickItem(i,m,u?u(e.value,r):e.value))});return o().createElement(d.m,{className:"recharts-polar-radius-axis-ticks"},b)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.axisLine,n=t.tick;return e&&e.length?o().createElement(d.m,{className:(0,f.Z)("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),h._.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(t,e,r){return o().isValidElement(t)?o().cloneElement(t,e):s()(t)?t(e):o().createElement(p.x,O({},e,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],e&&P(n.prototype,e),r&&P(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(n.PureComponent);k(_,"displayName","PolarRadiusAxis"),k(_,"axisType","radiusAxis"),k(_,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0})},89620:(t,e,r)=>{"use strict";r.d(e,{H:()=>Y});var n=r(60343);function o(){}function i(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function a(t){this._context=t}function u(t){this._context=t}function c(t){this._context=t}a.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:i(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},u.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:i(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class l{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function s(t){this._context=t}function f(t){this._context=t}function p(t){return new f(t)}function h(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function d(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function y(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,u=(i-n)/3;t._context.bezierCurveTo(n+u,o+u*e,i-u,a-u*r,i,a)}function v(t){this._context=t}function m(t){this._context=new b(t)}function b(t){this._context=t}function g(t){this._context=t}function x(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function O(t,e){this._context=t,this._t=e}s.prototype={areaStart:o,areaEnd:o,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,d(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,y(this,d(this,r=h(this,t,e)),r);break;default:y(this,this._t0,r=h(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(m.prototype=Object.create(v.prototype)).point=function(t,e){v.prototype.point.call(this,e,t)},b.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},g.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r){if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=x(t),o=x(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},O.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var w=r(29853),j=r(88982),S=r(60884);function P(t){return t[0]}function A(t){return t[1]}function E(t,e){var r=(0,j.Z)(!0),n=null,o=p,i=null,a=(0,S.d)(u);function u(u){var c,l,s,f=(u=(0,w.Z)(u)).length,p=!1;for(null==n&&(i=o(s=a())),c=0;c<=f;++c)!(c<f&&r(l=u[c],c,u))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,c,u),+e(l,c,u));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?P:(0,j.Z)(t),e="function"==typeof e?e:void 0===e?A:(0,j.Z)(e),u.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),u):t},u.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),u):e},u.defined=function(t){return arguments.length?(r="function"==typeof t?t:(0,j.Z)(!!t),u):r},u.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),u):o},u.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),u):n},u}function M(t,e,r){var n=null,o=(0,j.Z)(!0),i=null,a=p,u=null,c=(0,S.d)(l);function l(l){var s,f,p,h,d,y=(l=(0,w.Z)(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(u=a(d=c())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v){if(v=!v)f=s,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),p=s-1;p>=f;--p)u.point(m[p],b[p]);u.lineEnd(),u.areaEnd()}}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),u.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return u=null,d+""||null}function s(){return E().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?P:(0,j.Z)(+t),e="function"==typeof e?e:void 0===e?(0,j.Z)(0):(0,j.Z)(+e),r="function"==typeof r?r:void 0===r?A:(0,j.Z)(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.Z)(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,j.Z)(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.Z)(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,j.Z)(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:(0,j.Z)(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(u=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=u=null:u=a(i=t),l):i},l}var k=r(30531),T=r.n(k),_=r(86830),C=r.n(_),I=r(28411),D=r(34718),N=r(80131),L=r(43229);function B(t){return(B="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function R(){return(R=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function z(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function U(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?z(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=B(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=B(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==B(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var $={curveBasisClosed:function(t){return new u(t)},curveBasisOpen:function(t){return new c(t)},curveBasis:function(t){return new a(t)},curveBumpX:function(t){return new l(t,!0)},curveBumpY:function(t){return new l(t,!1)},curveLinearClosed:function(t){return new s(t)},curveLinear:p,curveMonotoneX:function(t){return new v(t)},curveMonotoneY:function(t){return new m(t)},curveNatural:function(t){return new g(t)},curveStep:function(t){return new O(t,.5)},curveStepAfter:function(t){return new O(t,1)},curveStepBefore:function(t){return new O(t,0)}},F=function(t){return t.x===+t.x&&t.y===+t.y},Z=function(t){return t.x},q=function(t){return t.y},W=function(t,e){if(C()(t))return t;var r="curve".concat(T()(t));return("curveMonotone"===r||"curveBump"===r)&&e?$["".concat(r).concat("vertical"===e?"Y":"X")]:$[r]||p},H=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,u=t.connectNulls,c=void 0!==u&&u,l=W(void 0===r?"linear":r,a),s=c?o.filter(function(t){return F(t)}):o;if(Array.isArray(i)){var f=c?i.filter(function(t){return F(t)}):i,p=s.map(function(t,e){return U(U({},t),{},{base:f[e]})});return(e="vertical"===a?M().y(q).x1(Z).x0(function(t){return t.base.x}):M().x(Z).y1(q).y0(function(t){return t.base.y})).defined(F).curve(l),e(p)}return(e="vertical"===a&&(0,L.hj)(i)?M().y(q).x1(Z).x0(i):(0,L.hj)(i)?M().x(Z).y1(q).y0(i):E().x(Z).y(q)).defined(F).curve(l),e(s)},Y=function(t){var e=t.className,r=t.points,o=t.path,i=t.pathRef;if((!r||!r.length)&&!o)return null;var a=r&&r.length?H(t):o;return n.createElement("path",R({},(0,N.L6)(t,!1),(0,D.Ym)(t),{className:(0,I.Z)("recharts-curve",e),d:a,ref:i}))}},35590:(t,e,r)=>{"use strict";r.d(e,{o:()=>c});var n=r(60343),o=r(28411),i=r(34718),a=r(80131);function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var c=function(t){var e=t.cx,r=t.cy,c=t.r,l=t.className,s=(0,o.Z)("recharts-dot",l);return e===+e&&r===+r&&c===+c?n.createElement("circle",u({},(0,a.L6)(t,!1),(0,i.Ym)(t),{className:s,cx:e,cy:r,r:c})):null}},13663:(t,e,r)=>{"use strict";r.d(e,{A:()=>v,X:()=>d});var n=r(60343),o=r.n(n),i=r(28411),a=r(81543),u=r(80131);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(){return(l=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var h=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),u=n>=0?1:-1,c=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+u*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+c*s[0],",").concat(e)),i+="L ".concat(t+r-c*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+u*s[1])),i+="L ".concat(t+r,",").concat(e+n-u*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-c*s[2],",").concat(e+n)),i+="L ".concat(t+c*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-u*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+c*p,",").concat(e,"\n            L ").concat(t+r-c*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+u*p,"\n            L ").concat(t+r,",").concat(e+n-u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-c*p,",").concat(e+n,"\n            L ").concat(t+c*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-u*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},d=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,u=e.height;return!!(Math.abs(a)>0&&Math.abs(u)>0)&&r>=Math.min(o,o+a)&&r<=Math.max(o,o+a)&&n>=Math.min(i,i+u)&&n<=Math.max(i,i+u)},y={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},v=function(t){var e,r=p(p({},y),t),c=(0,n.useRef)(),f=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,2)||function(t,e){if(t){if("string"==typeof t)return s(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,2)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),d=f[0],v=f[1];(0,n.useEffect)(function(){if(c.current&&c.current.getTotalLength)try{var t=c.current.getTotalLength();t&&v(t)}catch(t){}},[]);var m=r.x,b=r.y,g=r.width,x=r.height,O=r.radius,w=r.className,j=r.animationEasing,S=r.animationDuration,P=r.animationBegin,A=r.isAnimationActive,E=r.isUpdateAnimationActive;if(m!==+m||b!==+b||g!==+g||x!==+x||0===g||0===x)return null;var M=(0,i.Z)("recharts-rectangle",w);return E?o().createElement(a.ZP,{canBegin:d>0,from:{width:g,height:x,x:m,y:b},to:{width:g,height:x,x:m,y:b},duration:S,animationEasing:j,isActive:E},function(t){var e=t.width,n=t.height,i=t.x,s=t.y;return o().createElement(a.ZP,{canBegin:d>0,from:"0px ".concat(-1===d?1:d,"px"),to:"".concat(d,"px 0px"),attributeName:"strokeDasharray",begin:P,duration:S,isActive:A,easing:j},o().createElement("path",l({},(0,u.L6)(r,!0),{className:M,d:h(i,s,e,n,O),ref:c})))}):o().createElement("path",l({},(0,u.L6)(r,!0),{className:M,d:h(m,b,g,x,O)}))}},79211:(t,e,r)=>{"use strict";r.d(e,{L:()=>m});var n=r(60343),o=r.n(n),i=r(28411),a=r(80131),u=r(80076),c=r(43229);function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var h=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,l=t.cornerIsExternal,s=c*(a?1:-1)+n,f=Math.asin(c/s)/u.Wk,p=l?o:o+i*f;return{center:(0,u.op)(e,r,s,p),circleTangency:(0,u.op)(e,r,n,p),lineTangency:(0,u.op)(e,r,s*Math.cos(f*u.Wk),l?o-i*f:o),theta:f}},d=function(t){var e,r=t.cx,n=t.cy,o=t.innerRadius,i=t.outerRadius,a=t.startAngle,l=(e=t.endAngle,(0,c.uY)(e-a)*Math.min(Math.abs(e-a),359.999)),s=a+l,f=(0,u.op)(r,n,i,a),p=(0,u.op)(r,n,i,s),h="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(a>s),",\n    ").concat(p.x,",").concat(p.y,"\n  ");if(o>0){var d=(0,u.op)(r,n,o,a),y=(0,u.op)(r,n,o,s);h+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(o,",").concat(o,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(a<=s),",\n            ").concat(d.x,",").concat(d.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},y=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,u=t.cornerIsExternal,l=t.startAngle,s=t.endAngle,f=(0,c.uY)(s-l),p=h({cx:e,cy:r,radius:o,angle:l,sign:f,cornerRadius:i,cornerIsExternal:u}),y=p.circleTangency,v=p.lineTangency,m=p.theta,b=h({cx:e,cy:r,radius:o,angle:s,sign:-f,cornerRadius:i,cornerIsExternal:u}),g=b.circleTangency,x=b.lineTangency,O=b.theta,w=u?Math.abs(l-s):Math.abs(l-s)-m-O;if(w<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):d({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:l,endAngle:s});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(w>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(n>0){var S=h({cx:e,cy:r,radius:n,angle:l,sign:f,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),P=S.circleTangency,A=S.lineTangency,E=S.theta,M=h({cx:e,cy:r,radius:n,angle:s,sign:-f,isExternal:!0,cornerRadius:i,cornerIsExternal:u}),k=M.circleTangency,T=M.lineTangency,_=M.theta,C=u?Math.abs(l-s):Math.abs(l-s)-E-_;if(C<0&&0===i)return"".concat(j,"L").concat(e,",").concat(r,"Z");j+="L".concat(T.x,",").concat(T.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(k.x,",").concat(k.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(C>180),",").concat(+(f>0),",").concat(P.x,",").concat(P.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(A.x,",").concat(A.y,"Z")}else j+="L".concat(e,",").concat(r,"Z");return j},v={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},m=function(t){var e,r=p(p({},v),t),n=r.cx,u=r.cy,l=r.innerRadius,f=r.outerRadius,h=r.cornerRadius,m=r.forceCornerRadius,b=r.cornerIsExternal,g=r.startAngle,x=r.endAngle,O=r.className;if(f<l||g===x)return null;var w=(0,i.Z)("recharts-sector",O),j=f-l,S=(0,c.h1)(h,j,0,!0);return e=S>0&&360>Math.abs(g-x)?y({cx:n,cy:u,innerRadius:l,outerRadius:f,cornerRadius:Math.min(S,j/2),forceCornerRadius:m,cornerIsExternal:b,startAngle:g,endAngle:x}):d({cx:n,cy:u,innerRadius:l,outerRadius:f,startAngle:g,endAngle:x}),o().createElement("path",s({},(0,a.L6)(r,!0),{className:w,d:e,role:"img"}))}},10101:(t,e,r)=>{"use strict";r.d(e,{v:()=>D});var n=r(60343),o=r.n(n),i=r(30531),a=r.n(i);let u=Math.cos,c=Math.sin,l=Math.sqrt,s=Math.PI,f=2*s,p={draw(t,e){let r=l(e/s);t.moveTo(r,0),t.arc(0,0,r,0,f)}},h=l(1/3),d=2*h,y=c(s/10)/c(7*s/10),v=c(f/10)*y,m=-u(f/10)*y,b=l(3),g=l(3)/2,x=1/l(12),O=(x/2+1)*3;var w=r(88982),j=r(60884);l(3),l(3);var S=r(28411),P=r(80131);function A(t){return(A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var E=["type","size","sizeType"];function M(){return(M=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function T(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=A(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=A(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==A(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var _={symbolCircle:p,symbolCross:{draw(t,e){let r=l(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=l(e/d),n=r*h;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=l(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=l(.8908130915292852*e),n=v*r,o=m*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=f*e/5,a=u(i),l=c(i);t.lineTo(l*r,-a*r),t.lineTo(a*n-l*o,l*n+a*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-l(e/(3*b));t.moveTo(0,2*r),t.lineTo(-b*r,-r),t.lineTo(b*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=l(e/O),n=r/2,o=r*x,i=r*x+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-g*o,g*n+-.5*o),t.lineTo(-.5*n-g*i,g*n+-.5*i),t.lineTo(-.5*a-g*i,g*a+-.5*i),t.lineTo(-.5*n+g*o,-.5*o-g*n),t.lineTo(-.5*n+g*i,-.5*i-g*n),t.lineTo(-.5*a+g*i,-.5*i-g*a),t.closePath()}}},C=Math.PI/180,I=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*C;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},D=function(t){var e,r=t.type,n=void 0===r?"circle":r,i=t.size,u=void 0===i?64:i,c=t.sizeType,l=void 0===c?"area":c,s=T(T({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,E)),{},{type:n,size:u,sizeType:l}),f=s.className,h=s.cx,d=s.cy,y=(0,P.L6)(s,!0);return h===+h&&d===+d&&u===+u?o().createElement("path",M({},y,{className:(0,S.Z)("recharts-symbols",f),transform:"translate(".concat(h,", ").concat(d,")"),d:(e=_["symbol".concat(a()(n))]||p,(function(t,e){let r=null,n=(0,j.d)(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:(0,w.Z)(t||p),e="function"==typeof e?e:(0,w.Z)(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:(0,w.Z)(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:(0,w.Z)(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(I(u,l,n))())})):null};D.registerSymbol=function(t,e){_["symbol".concat(a()(t))]=e}},78445:(t,e,r)=>{"use strict";r.d(e,{bn:()=>I,a3:()=>U,lT:()=>D,V$:()=>N,w7:()=>L});var n=r(60343),o=r.n(n),i=r(86830),a=r.n(i),u=r(27673),c=r.n(u),l=r(26471),s=r.n(l),f=r(38656),p=r.n(f),h=r(13663),d=r(28411),y=r(81543),v=r(80131);function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function b(){return(b=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function x(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function O(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?x(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=m(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==m(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var w=function(t,e,r,n,o){var i=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-i/2,",").concat(e+o)+"L ".concat(t+r-i/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},j={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},S=function(t){var e,r=O(O({},j),t),i=(0,n.useRef)(),a=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(e,2)||function(t,e){if(t){if("string"==typeof t)return g(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return g(t,2)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),u=a[0],c=a[1];(0,n.useEffect)(function(){if(i.current&&i.current.getTotalLength)try{var t=i.current.getTotalLength();t&&c(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.upperWidth,p=r.lowerWidth,h=r.height,m=r.className,x=r.animationEasing,S=r.animationDuration,P=r.animationBegin,A=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||h!==+h||0===f&&0===p||0===h)return null;var E=(0,d.Z)("recharts-trapezoid",m);return A?o().createElement(y.ZP,{canBegin:u>0,from:{upperWidth:0,lowerWidth:0,height:h,x:l,y:s},to:{upperWidth:f,lowerWidth:p,height:h,x:l,y:s},duration:S,animationEasing:x,isActive:A},function(t){var e=t.upperWidth,n=t.lowerWidth,a=t.height,c=t.x,l=t.y;return o().createElement(y.ZP,{canBegin:u>0,from:"0px ".concat(-1===u?1:u,"px"),to:"".concat(u,"px 0px"),attributeName:"strokeDasharray",begin:P,duration:S,easing:x},o().createElement("path",b({},(0,v.L6)(r,!0),{className:E,d:w(c,l,e,n,a),ref:i})))}):o().createElement("g",null,o().createElement("path",b({},(0,v.L6)(r,!0),{className:E,d:w(l,s,f,p,h)})))},P=r(79211),A=r(99829),E=r(10101),M=["option","shapeType","propTransformer","activeClassName","isActive"];function k(t){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function T(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?T(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=k(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=k(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==k(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function C(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return o().createElement(h.A,r);case"trapezoid":return o().createElement(S,r);case"sector":return o().createElement(P.L,r);case"symbols":if("symbols"===e)return o().createElement(E.v,r);break;default:return null}}function I(t){var e,r=t.option,i=t.shapeType,u=t.propTransformer,l=t.activeClassName,f=t.isActive,p=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,M);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,_(_({},p),(0,n.isValidElement)(r)?r.props:r));else if(a()(r))e=r(p);else if(c()(r)&&!s()(r)){var h=(void 0===u?function(t,e){return _(_({},e),t)}:u)(r,p);e=o().createElement(C,{shapeType:i,elementProps:h})}else e=o().createElement(C,{shapeType:i,elementProps:p});return f?o().createElement(A.m,{className:void 0===l?"recharts-active-shape":l},e):e}function D(t,e){return null!=e&&"trapezoids"in t.props}function N(t,e){return null!=e&&"sectors"in t.props}function L(t,e){return null!=e&&"points"in t.props}function B(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function R(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function z(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}function U(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,u=(D(i,o)?e="trapezoids":N(i,o)?e="sectors":L(i,o)&&(e="points"),e),c=D(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:N(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:L(i,o)?o.payload:{},l=a.filter(function(t,e){var r=p()(c,t),n=i.props[u].filter(function(t){var e;return(D(i,o)?e=B:N(i,o)?e=R:L(i,o)&&(e=z),e)(t,o)}),a=i.props[u].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}},81156:(t,e,r)=>{"use strict";r.d(e,{Ky:()=>O,O1:()=>b,_b:()=>g,t9:()=>m,xE:()=>w});var n=r(56744),o=r.n(n),i=r(84535),a=r.n(i),u=r(56361),c=r(80131),l=r(43229),s=r(94970);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){y(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(t,e,r){return(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function v(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var m=function(t,e,r,n,o){var i=t.width,a=t.height,f=t.layout,p=t.children,h=Object.keys(e),v={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},m=!!(0,c.sP)(p,s.$);return h.reduce(function(i,a){var c,s,p,h,b,g=e[a],x=g.orientation,O=g.domain,w=g.padding,j=void 0===w?{}:w,S=g.mirror,P=g.reversed,A="".concat(x).concat(S?"Mirror":"");if("number"===g.type&&("gap"===g.padding||"no-gap"===g.padding)){var E=O[1]-O[0],M=1/0,k=g.categoricalDomain.sort(l.fC);if(k.forEach(function(t,e){e>0&&(M=Math.min((t||0)-(k[e-1]||0),M))}),Number.isFinite(M)){var T=M/E,_="vertical"===g.layout?r.height:r.width;if("gap"===g.padding&&(c=T*_/2),"no-gap"===g.padding){var C=(0,l.h1)(t.barCategoryGap,T*_),I=T*_/2;c=I-C-(I-C)/_*C}}}s="xAxis"===n?[r.left+(j.left||0)+(c||0),r.left+r.width-(j.right||0)-(c||0)]:"yAxis"===n?"horizontal"===f?[r.top+r.height-(j.bottom||0),r.top+(j.top||0)]:[r.top+(j.top||0)+(c||0),r.top+r.height-(j.bottom||0)-(c||0)]:g.range,P&&(s=[s[1],s[0]]);var D=(0,u.Hq)(g,o,m),N=D.scale,L=D.realScaleType;N.domain(O).range(s),(0,u.zF)(N);var B=(0,u.g$)(N,d(d({},g),{},{realScaleType:L}));"xAxis"===n?(b="top"===x&&!S||"bottom"===x&&S,p=r.left,h=v[A]-b*g.height):"yAxis"===n&&(b="left"===x&&!S||"right"===x&&S,p=v[A]-b*g.width,h=r.top);var R=d(d(d({},g),B),{},{realScaleType:L,x:p,y:h,scale:N,width:"xAxis"===n?r.width:g.width,height:"yAxis"===n?r.height:g.height});return R.bandSize=(0,u.zT)(R,B),g.hide||"xAxis"!==n?g.hide||(v[A]+=(b?-1:1)*R.width):v[A]+=(b?-1:1)*R.height,d(d({},i),{},y({},a,R))},{})},b=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},g=function(t){return b({x:t.x1,y:t.y1},{x:t.x2,y:t.y2})},x=function(){var t,e;function r(t){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,r),this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&p(r.prototype,t),e&&p(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();y(x,"EPS",1e-4);var O=function(t){var e=Object.keys(t).reduce(function(e,r){return d(d({},e),{},y({},r,x.create(t[r])))},{});return d(d({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return o()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:i})})},isInRange:function(t){return a()(t,function(t,r){return e[r].isInRange(t)})}})},w=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))}},56361:(t,e,r)=>{"use strict";r.d(e,{By:()=>og,VO:()=>oy,zF:()=>oM,DO:()=>oA,Bu:()=>ok,zT:()=>oZ,qz:()=>ob,pt:()=>om,Yj:()=>oB,Fy:()=>oL,Hv:()=>oN,gF:()=>od,s6:()=>ow,EB:()=>oz,fk:()=>ov,wh:()=>oI,O3:()=>oR,uY:()=>oS,g$:()=>oD,Qo:()=>oW,F$:()=>oh,NA:()=>oj,ko:()=>oq,ZI:()=>oO,Hq:()=>oE,LG:()=>oF,Vv:()=>oT});var n={};r.r(n),r.d(n,{scaleBand:()=>o.Z,scaleDiverging:()=>function t(){var e=tM(rW()(tl));return e.copy=function(){return rF(e,t())},tv.O.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=tB(rW()).domain([.1,1,10]);return e.copy=function(){return rF(e,t()).base(e.base())},tv.O.apply(e,arguments)},scaleDivergingPow:()=>rH,scaleDivergingSqrt:()=>rY,scaleDivergingSymlog:()=>function t(){var e=tU(rW());return e.copy=function(){return rF(e,t()).constant(e.constant())},tv.O.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t=+t)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,tu),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,tu):[0,1],tM(n)},scaleImplicit:()=>t$.O,scaleLinear:()=>tk,scaleLog:()=>function t(){let e=tB(td()).domain([1,10]);return e.copy=()=>th(e,t()).base(e.base()),tv.o.apply(e,arguments),e},scaleOrdinal:()=>t$.Z,scalePoint:()=>o.x,scalePow:()=>tH,scaleQuantile:()=>function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=v){if(!(!(n=t.length)||isNaN(e=+e))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t=+t)?e:n[b(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e=+e)||r.push(e);return r.sort(p),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},tv.o.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function u(t){return null!=t&&t<=t?a[b(i,t,0,o)]:e}function c(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return u}return u.domain=function(t){return arguments.length?([r,n]=t,r=+r,n=+n,c()):[r,n]},u.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,c()):a.slice()},u.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},u.unknown=function(t){return arguments.length&&(e=t),u},u.thresholds=function(){return i.slice()},u.copy=function(){return t().domain([r,n]).range(a).unknown(e)},tv.o.apply(tM(u),arguments)},scaleRadial:()=>function t(){var e,r=ty(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(tX(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,tu)).map(tX)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},tv.o.apply(i,arguments),tM(i)},scaleSequential:()=>function t(){var e=tM(r$()(tl));return e.copy=function(){return rF(e,t())},tv.O.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=tB(r$()).domain([1,10]);return e.copy=function(){return rF(e,t()).base(e.base())},tv.O.apply(e,arguments)},scaleSequentialPow:()=>rZ,scaleSequentialQuantile:()=>function t(){var e=[],r=tl;function n(t){if(null!=t&&!isNaN(t=+t))return r((b(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r=+r)||e.push(r);return e.sort(p),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n=+n)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e=+e))){if(e<=0||n<2)return tG(t);if(e>=1)return tV(t);var n,o=(n-1)*e,i=Math.floor(o),a=tV((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?tK:function(t=p){if(t===p)return tK;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,u=r-n+1,c=Math.log(a),l=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*l*(a-l)/a)*(u-a/2<0?-1:1),f=Math.max(n,Math.floor(r-u*l/a+s)),p=Math.min(o,Math.floor(r+(a-u)*l/a+s));t(e,r,f,p,i)}let a=e[r],u=n,c=o;for(tJ(e,n,r),i(e[o],a)>0&&tJ(e,n,o);u<c;){for(tJ(e,u,c),++u,--c;0>i(e[u],a);)++u;for(;i(e[c],a)>0;)--c}0===i(e[n],a)?tJ(e,n,c):tJ(e,++c,o),c<=r&&(n=c+1),r<=c&&(o=c-1)}return e})(t,i).subarray(0,i+1));return a+(tG(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},tv.O.apply(n,arguments)},scaleSequentialSqrt:()=>rq,scaleSequentialSymlog:()=>function t(){var e=tU(r$());return e.copy=function(){return rF(e,t()).constant(e.constant())},tv.O.apply(e,arguments)},scaleSqrt:()=>tY,scaleSymlog:()=>function t(){var e=tU(td());return e.copy=function(){return th(e,t()).constant(e.constant())},tv.o.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[b(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},tv.o.apply(i,arguments)},scaleTime:()=>rz,scaleUtc:()=>rU,tickFormat:()=>tE});var o=r(91358);let i=Math.sqrt(50),a=Math.sqrt(10),u=Math.sqrt(2);function c(t,e,r){let n,o,l;let s=(e-t)/Math.max(0,r),f=Math.floor(Math.log10(s)),p=s/Math.pow(10,f),h=p>=i?10:p>=a?5:p>=u?2:1;return(f<0?(n=Math.round(t*(l=Math.pow(10,-f)/h)),o=Math.round(e*l),n/l<t&&++n,o/l>e&&--o,l=-l):(n=Math.round(t/(l=Math.pow(10,f)*h)),o=Math.round(e/l),n*l<t&&++n,o*l>e&&--o),o<n&&.5<=r&&r<2)?c(t,e,2*r):[n,o,l]}function l(t,e,r){if(e=+e,t=+t,!((r=+r)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?c(e,t,r):c(t,e,r);if(!(i>=o))return[];let u=i-o+1,l=Array(u);if(n){if(a<0)for(let t=0;t<u;++t)l[t]=-((i-t)/a);else for(let t=0;t<u;++t)l[t]=(i-t)*a}else if(a<0)for(let t=0;t<u;++t)l[t]=-((o+t)/a);else for(let t=0;t<u;++t)l[t]=(o+t)*a;return l}function s(t,e,r){return c(t=+t,e=+e,r=+r)[2]}function f(t,e,r){e=+e,t=+t,r=+r;let n=e<t,o=n?s(e,t,r):s(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function p(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function h(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function d(t){let e,r,n;function o(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>r(t[e],n)?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=p,r=(e,r)=>p(t(e),r),n=(e,r)=>t(e)-r):(e=t===p||t===h?t:y,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function y(){return 0}function v(t){return null===t?NaN:+t}let m=d(p),b=m.right;function g(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function x(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function O(){}m.left,d(v).center;var w="\\s*([+-]?\\d+)\\s*",j="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",S="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",P=/^#([0-9a-f]{3,8})$/,A=RegExp(`^rgb\\(${w},${w},${w}\\)$`),E=RegExp(`^rgb\\(${S},${S},${S}\\)$`),M=RegExp(`^rgba\\(${w},${w},${w},${j}\\)$`),k=RegExp(`^rgba\\(${S},${S},${S},${j}\\)$`),T=RegExp(`^hsl\\(${j},${S},${S}\\)$`),_=RegExp(`^hsla\\(${j},${S},${S},${j}\\)$`),C={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function I(){return this.rgb().formatHex()}function D(){return this.rgb().formatRgb()}function N(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=P.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?L(e):3===r?new z(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?B(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?B(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=A.exec(t))?new z(e[1],e[2],e[3],1):(e=E.exec(t))?new z(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=M.exec(t))?B(e[1],e[2],e[3],e[4]):(e=k.exec(t))?B(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=T.exec(t))?W(e[1],e[2]/100,e[3]/100,1):(e=_.exec(t))?W(e[1],e[2]/100,e[3]/100,e[4]):C.hasOwnProperty(t)?L(C[t]):"transparent"===t?new z(NaN,NaN,NaN,0):null}function L(t){return new z(t>>16&255,t>>8&255,255&t,1)}function B(t,e,r,n){return n<=0&&(t=e=r=NaN),new z(t,e,r,n)}function R(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof O||(o=N(o)),o)?new z((o=o.rgb()).r,o.g,o.b,o.opacity):new z:new z(t,e,r,null==n?1:n)}function z(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function U(){return`#${q(this.r)}${q(this.g)}${q(this.b)}`}function $(){let t=F(this.opacity);return`${1===t?"rgb(":"rgba("}${Z(this.r)}, ${Z(this.g)}, ${Z(this.b)}${1===t?")":`, ${t})`}`}function F(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function Z(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function q(t){return((t=Z(t))<16?"0":"")+t.toString(16)}function W(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new Y(t,e,r,n)}function H(t){if(t instanceof Y)return new Y(t.h,t.s,t.l,t.opacity);if(t instanceof O||(t=N(t)),!t)return new Y;if(t instanceof Y)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,u=i-o,c=(i+o)/2;return u?(a=e===i?(r-n)/u+(r<n)*6:r===i?(n-e)/u+2:(e-r)/u+4,u/=c<.5?i+o:2-i-o,a*=60):u=c>0&&c<1?0:a,new Y(a,u,c,t.opacity)}function Y(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function X(t){return(t=(t||0)%360)<0?t+360:t}function V(t){return Math.max(0,Math.min(1,t||0))}function G(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function K(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}g(O,N,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:I,formatHex:I,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return H(this).formatHsl()},formatRgb:D,toString:D}),g(z,R,x(O,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new z(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new z(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new z(Z(this.r),Z(this.g),Z(this.b),F(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:U,formatHex:U,formatHex8:function(){return`#${q(this.r)}${q(this.g)}${q(this.b)}${q((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:$,toString:$})),g(Y,function(t,e,r,n){return 1==arguments.length?H(t):new Y(t,e,r,null==n?1:n)},x(O,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new Y(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new Y(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new z(G(t>=240?t-240:t+120,o,n),G(t,o,n),G(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new Y(X(this.h),V(this.s),V(this.l),F(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=F(this.opacity);return`${1===t?"hsl(":"hsla("}${X(this.h)}, ${100*V(this.s)}%, ${100*V(this.l)}%${1===t?")":`, ${t})`}`}}));let J=t=>()=>t;function Q(t,e){var r=e-t;return r?function(e){return t+e*r}:J(isNaN(t)?e:t)}let tt=function t(e){var r,n=1==(r=+(r=e))?Q:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):J(isNaN(t)?e:t)};function o(t,e){var r=n((t=R(t)).r,(e=R(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=Q(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function te(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),u=Array(o);for(r=0;r<o;++r)n=R(e[r]),i[r]=n.r||0,a[r]=n.g||0,u[r]=n.b||0;return i=t(i),a=t(a),u=t(u),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=u(t),n+""}}}function tr(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}te(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,u=n<e-1?t[n+2]:2*i-o;return K((r-n/e)*e,a,o,i,u)}}),te(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],u=t[(n+2)%e];return K((r-n/e)*e,o,i,a,u)}});var tn=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,to=RegExp(tn.source,"g");function ti(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?J(e):("number"===o?tr:"string"===o?(n=N(e))?(e=n,tt):function(t,e){var r,n,o,i,a,u=tn.lastIndex=to.lastIndex=0,c=-1,l=[],s=[];for(t+="",e+="";(o=tn.exec(t))&&(i=to.exec(e));)(a=i.index)>u&&(a=e.slice(u,a),l[c]?l[c]+=a:l[++c]=a),(o=o[0])===(i=i[0])?l[c]?l[c]+=i:l[++c]=i:(l[++c]=null,s.push({i:c,x:tr(o,i)})),u=to.lastIndex;return u<e.length&&(a=e.slice(u),l[c]?l[c]+=a:l[++c]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof N?tt:e instanceof Date?function(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=ti(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=ti(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:tr:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function ta(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function tu(t){return+t}var tc=[0,1];function tl(t){return t}function ts(t,e){var r;return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function tf(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=ts(o,n),i=r(a,i)):(n=ts(n,o),i=r(i,a)),function(t){return i(n(t))}}function tp(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=ts(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=b(t,e,1,n)-1;return i[r](o[r](e))}}function th(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function td(){var t,e,r,n,o,i,a=tc,u=tc,c=ti,l=tl;function s(){var t,e,r,c=Math.min(a.length,u.length);return l!==tl&&(t=a[0],e=a[c-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=c>2?tp:tf,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?r:(o||(o=n(a.map(t),u,c)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(u,a.map(t),tr)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,tu),s()):a.slice()},f.range=function(t){return arguments.length?(u=Array.from(t),s()):u.slice()},f.rangeRound=function(t){return u=Array.from(t),c=ta,s()},f.clamp=function(t){return arguments.length?(l=!!t||tl,s()):l!==tl},f.interpolate=function(t){return arguments.length?(c=t,s()):c},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function ty(){return td()(tl,tl)}var tv=r(94095),tm=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function tb(t){var e;if(!(e=tm.exec(t)))throw Error("invalid format: "+t);return new tg({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function tg(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function tx(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function tO(t){return(t=tx(Math.abs(t)))?t[1]:NaN}function tw(t,e){var r=tx(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}tb.prototype=tg.prototype,tg.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let tj={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>tw(100*t,e),r:tw,s:function(t,e){var r=tx(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(r0=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+tx(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function tS(t){return t}var tP=Array.prototype.map,tA=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tE(t,e,r,n){var o,i,a=f(t,e,r);switch((n=tb(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(i=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(tO(u)/3)))-tO(Math.abs(a))))||(n.precision=i),r3(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(i=Math.max(0,tO(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=a)))-tO(o))+1)||(n.precision=i-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(i=Math.max(0,-tO(Math.abs(a))))||(n.precision=i-("%"===n.type)*2)}return r2(n)}function tM(t){var e=t.domain;return t.ticks=function(t){var r=e();return l(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return tE(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,u=i.length-1,c=i[a],l=i[u],f=10;for(l<c&&(o=c,c=l,l=o,o=a,a=u,u=o);f-- >0;){if((o=s(c,l,r))===n)return i[a]=c,i[u]=l,e(i);if(o>0)c=Math.floor(c/o)*o,l=Math.ceil(l/o)*o;else if(o<0)c=Math.ceil(c*o)/o,l=Math.floor(l*o)/o;else break;n=o}return t},t}function tk(){var t=ty();return t.copy=function(){return th(t,tk())},tv.o.apply(t,arguments),tM(t)}function tT(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function t_(t){return Math.log(t)}function tC(t){return Math.exp(t)}function tI(t){return-Math.log(-t)}function tD(t){return-Math.exp(-t)}function tN(t){return isFinite(t)?+("1e"+t):t<0?0:t}function tL(t){return(e,r)=>-t(-e,r)}function tB(t){let e,r;let n=t(t_,tC),o=n.domain,i=10;function a(){var a,u;return e=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(u=i)?tN:u===Math.E?Math.exp:t=>Math.pow(u,t),o()[0]<0?(e=tL(e),r=tL(r),t(tI,tD)):t(t_,tC),n}return n.base=function(t){return arguments.length?(i=+t,a()):i},n.domain=function(t){return arguments.length?(o(t),a()):o()},n.ticks=t=>{let n,a;let u=o(),c=u[0],s=u[u.length-1],f=s<c;f&&([c,s]=[s,c]);let p=e(c),h=e(s),d=null==t?10:+t,y=[];if(!(i%1)&&h-p<d){if(p=Math.floor(p),h=Math.ceil(h),c>0){for(;p<=h;++p)for(n=1;n<i;++n)if(!((a=p<0?n/r(-p):n*r(p))<c)){if(a>s)break;y.push(a)}}else for(;p<=h;++p)for(n=i-1;n>=1;--n)if(!((a=p>0?n/r(-p):n*r(p))<c)){if(a>s)break;y.push(a)}2*y.length<d&&(y=l(c,s,d))}else y=l(p,h,Math.min(h-p,d)).map(r);return f?y.reverse():y},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=tb(o)).precision||(o.trim=!0),o=r2(o)),t===1/0)return o;let a=Math.max(1,i*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*i<i-.5&&(n*=i),n<=a?o(t):""}},n.nice=()=>o(tT(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function tR(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function tz(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function tU(t){var e=1,r=t(tR(1),tz(e));return r.constant=function(r){return arguments.length?t(tR(e=+r),tz(e)):e},tM(r)}r2=(r1=function(t){var e,r,n,o=void 0===t.grouping||void 0===t.thousands?tS:(e=tP.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,u=e[0],c=0;o>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),i.push(t.substring(o-=u,o+u)),!((c+=u+1)>n));)u=e[a=(a+1)%e.length];return i.reverse().join(r)}),i=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",u=void 0===t.decimal?".":t.decimal+"",c=void 0===t.numerals?tS:(n=tP.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=tb(t)).fill,r=t.align,n=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):tj[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",O=tj[b],w=/[defgprs%]/.test(b);function j(t){var i,a,l,p=g,j=x;if("c"===b)j=O(t)+j,t="";else{var S=(t=+t)<0||1/t<0;if(t=isNaN(t)?f:O(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?tA[8+r0/3]:"")+j+(S&&"("===n?")":""),w){for(i=-1,a=t.length;++i<a;)if(48>(l=t.charCodeAt(i))||l>57){j=(46===l?u+t.slice(i+1):t.slice(i))+j,t=t.slice(0,i);break}}}y&&!h&&(t=o(t,1/0));var P=p.length+t.length+j.length,A=P<d?Array(d-P+1).join(e):"";switch(y&&h&&(t=o(A+t,A.length?d-j.length:1/0),A=""),r){case"<":t=p+t+j+A;break;case"=":t=p+A+t+j;break;case"^":t=A.slice(0,P=A.length>>1)+p+t+j+A.slice(P);break;default:t=A+p+t+j}return c(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=tb(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(tO(e)/3))),o=Math.pow(10,-n),i=tA[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,r3=r1.formatPrefix;var t$=r(3013);function tF(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function tZ(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function tq(t){return t<0?-t*t:t*t}function tW(t){var e=t(tl,tl),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(tl,tl):.5===r?t(tZ,tq):t(tF(r),tF(1/r)):r},tM(e)}function tH(){var t=tW(td());return t.copy=function(){return th(t,tH()).exponent(t.exponent())},tv.o.apply(t,arguments),t}function tY(){return tH.apply(null,arguments).exponent(.5)}function tX(t){return Math.sign(t)*t*t}function tV(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function tG(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function tK(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function tJ(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let tQ=new Date,t0=new Date;function t1(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a;let u=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return u;do u.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return u},o.filter=r=>t1(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t){if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}}),r&&(o.count=(e,n)=>(tQ.setTime(+e),t0.setTime(+n),t(tQ),t(t0),Math.floor(r(tQ,t0))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let t2=t1(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);t2.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?t1(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):t2:null,t2.range;let t3=t1(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());t3.range;let t6=t1(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());t6.range;let t8=t1(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());t8.range;let t4=t1(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());t4.range;let t5=t1(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());t5.range;let t7=t1(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);t7.range;let t9=t1(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);t9.range;let et=t1(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function ee(t){return t1(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}et.range;let er=ee(0),en=ee(1),eo=ee(2),ei=ee(3),ea=ee(4),eu=ee(5),ec=ee(6);function el(t){return t1(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}er.range,en.range,eo.range,ei.range,ea.range,eu.range,ec.range;let es=el(0),ef=el(1),ep=el(2),eh=el(3),ed=el(4),ey=el(5),ev=el(6);es.range,ef.range,ep.range,eh.range,ed.range,ey.range,ev.range;let em=t1(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());em.range;let eb=t1(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());eb.range;let eg=t1(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());eg.every=t=>isFinite(t=Math.floor(t))&&t>0?t1(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,eg.range;let ex=t1(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function eO(t,e,r,n,o,i){let a=[[t3,1,1e3],[t3,5,5e3],[t3,15,15e3],[t3,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function u(e,r,n){let o=Math.abs(r-e)/n,i=d(([,,t])=>t).right(a,o);if(i===a.length)return t.every(f(e/31536e6,r/31536e6,n));if(0===i)return t2.every(Math.max(f(e,r,n),1));let[u,c]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return u.every(c)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:u(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},u]}ex.every=t=>isFinite(t=Math.floor(t))&&t>0?t1(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,ex.range;let[ew,ej]=eO(ex,eb,es,et,t5,t8),[eS,eP]=eO(eg,em,er,t7,t4,t6);function eA(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function eE(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function eM(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var ek={"-":"",_:" ",0:"0"},eT=/^\s*\d+/,e_=/^%/,eC=/[\\^$*+?|[\]().{}]/g;function eI(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function eD(t){return t.replace(eC,"\\$&")}function eN(t){return RegExp("^(?:"+t.map(eD).join("|")+")","i")}function eL(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function eB(t,e,r){var n=eT.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function eR(t,e,r){var n=eT.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function ez(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function eU(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function e$(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function eF(t,e,r){var n=eT.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function eZ(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function eq(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function eW(t,e,r){var n=eT.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function eH(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function eY(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function eX(t,e,r){var n=eT.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function eV(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function eG(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function eK(t,e,r){var n=eT.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function eJ(t,e,r){var n=eT.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function eQ(t,e,r){var n=eT.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function e0(t,e,r){var n=e_.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function e1(t,e,r){var n=eT.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function e2(t,e,r){var n=eT.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function e3(t,e){return eI(t.getDate(),e,2)}function e6(t,e){return eI(t.getHours(),e,2)}function e8(t,e){return eI(t.getHours()%12||12,e,2)}function e4(t,e){return eI(1+t7.count(eg(t),t),e,3)}function e5(t,e){return eI(t.getMilliseconds(),e,3)}function e7(t,e){return e5(t,e)+"000"}function e9(t,e){return eI(t.getMonth()+1,e,2)}function rt(t,e){return eI(t.getMinutes(),e,2)}function re(t,e){return eI(t.getSeconds(),e,2)}function rr(t){var e=t.getDay();return 0===e?7:e}function rn(t,e){return eI(er.count(eg(t)-1,t),e,2)}function ro(t){var e=t.getDay();return e>=4||0===e?ea(t):ea.ceil(t)}function ri(t,e){return t=ro(t),eI(ea.count(eg(t),t)+(4===eg(t).getDay()),e,2)}function ra(t){return t.getDay()}function ru(t,e){return eI(en.count(eg(t)-1,t),e,2)}function rc(t,e){return eI(t.getFullYear()%100,e,2)}function rl(t,e){return eI((t=ro(t)).getFullYear()%100,e,2)}function rs(t,e){return eI(t.getFullYear()%1e4,e,4)}function rf(t,e){var r=t.getDay();return eI((t=r>=4||0===r?ea(t):ea.ceil(t)).getFullYear()%1e4,e,4)}function rp(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+eI(e/60|0,"0",2)+eI(e%60,"0",2)}function rh(t,e){return eI(t.getUTCDate(),e,2)}function rd(t,e){return eI(t.getUTCHours(),e,2)}function ry(t,e){return eI(t.getUTCHours()%12||12,e,2)}function rv(t,e){return eI(1+t9.count(ex(t),t),e,3)}function rm(t,e){return eI(t.getUTCMilliseconds(),e,3)}function rb(t,e){return rm(t,e)+"000"}function rg(t,e){return eI(t.getUTCMonth()+1,e,2)}function rx(t,e){return eI(t.getUTCMinutes(),e,2)}function rO(t,e){return eI(t.getUTCSeconds(),e,2)}function rw(t){var e=t.getUTCDay();return 0===e?7:e}function rj(t,e){return eI(es.count(ex(t)-1,t),e,2)}function rS(t){var e=t.getUTCDay();return e>=4||0===e?ed(t):ed.ceil(t)}function rP(t,e){return t=rS(t),eI(ed.count(ex(t),t)+(4===ex(t).getUTCDay()),e,2)}function rA(t){return t.getUTCDay()}function rE(t,e){return eI(ef.count(ex(t)-1,t),e,2)}function rM(t,e){return eI(t.getUTCFullYear()%100,e,2)}function rk(t,e){return eI((t=rS(t)).getUTCFullYear()%100,e,2)}function rT(t,e){return eI(t.getUTCFullYear()%1e4,e,4)}function r_(t,e){var r=t.getUTCDay();return eI((t=r>=4||0===r?ed(t):ed.ceil(t)).getUTCFullYear()%1e4,e,4)}function rC(){return"+0000"}function rI(){return"%"}function rD(t){return+t}function rN(t){return Math.floor(+t/1e3)}function rL(t){return new Date(t)}function rB(t){return t instanceof Date?+t:+new Date(+t)}function rR(t,e,r,n,o,i,a,u,c,l){var s=ty(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(c(t)<t?h:u(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,rB)):p().map(rL)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(tT(r,t)):s},s.copy=function(){return th(s,rR(t,e,r,n,o,i,a,u,c,l))},s}function rz(){return tv.o.apply(rR(eS,eP,eg,em,er,t7,t4,t6,t3,r8).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function rU(){return tv.o.apply(rR(ew,ej,ex,eb,es,t9,t5,t8,t3,r4).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function r$(){var t,e,r,n,o,i=0,a=1,u=tl,c=!1;function l(e){return null==e||isNaN(e=+e)?o:u(0===r?.5:(e=(n(e)-t)*r,c?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,u=t(r,n),l):[u(0),u(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i=+i),e=n(a=+a),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(c=!!t,l):c},l.interpolator=function(t){return arguments.length?(u=t,l):u},l.range=s(ti),l.rangeRound=s(ta),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function rF(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function rZ(){var t=tW(r$());return t.copy=function(){return rF(t,rZ()).exponent(t.exponent())},tv.O.apply(t,arguments)}function rq(){return rZ.apply(null,arguments).exponent(.5)}function rW(){var t,e,r,n,o,i,a,u=0,c=.5,l=1,s=1,f=tl,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=ti);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([u,c,l]=a,t=i(u=+u),e=i(c=+c),r=i(l=+l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[u,c,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(ti),h.rangeRound=d(ta),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(u),e=a(c),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function rH(){var t=tW(rW());return t.copy=function(){return rF(t,rH()).exponent(t.exponent())},tv.O.apply(t,arguments)}function rY(){return rH.apply(null,arguments).exponent(.5)}function rX(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],u=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<u;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}r8=(r6=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,l=eN(o),s=eL(o),f=eN(i),p=eL(i),h=eN(a),d=eL(a),y=eN(u),v=eL(u),m=eN(c),b=eL(c),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:e3,e:e3,f:e7,g:rl,G:rf,H:e6,I:e8,j:e4,L:e5,m:e9,M:rt,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:rD,s:rN,S:re,u:rr,U:rn,V:ri,w:ra,W:ru,x:null,X:null,y:rc,Y:rs,Z:rp,"%":rI},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:rh,e:rh,f:rb,g:rk,G:r_,H:rd,I:ry,j:rv,L:rm,m:rg,M:rx,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:rD,s:rN,S:rO,u:rw,U:rj,V:rP,w:rA,W:rE,x:null,X:null,y:rM,Y:rT,Z:rC,"%":rI},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:eY,e:eY,f:eQ,g:eZ,G:eF,H:eV,I:eV,j:eX,L:eJ,m:eH,M:eG,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:eW,Q:e1,s:e2,S:eK,u:eR,U:ez,V:eU,w:eB,W:e$,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:eZ,Y:eF,Z:eq,"%":e0};function w(t,e){return function(r){var n,o,i,a=[],u=-1,c=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++u<l;)37===t.charCodeAt(u)&&(a.push(t.slice(c,u)),null!=(o=ek[n=t.charAt(++u)])?n=t.charAt(++u):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),c=u+1);return a.push(t.slice(c,u)),a.join("")}}function j(t,e){return function(r){var n,o,i=eM(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=eE(eM(i.y,0,1))).getUTCDay())>4||0===o?ef.ceil(n):ef(n),n=t9.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=eA(eM(i.y,0,1))).getDay())>4||0===o?en.ceil(n):en(n),n=t7.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?eE(eM(i.y,0,1)).getUTCDay():eA(eM(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,eE(i)):eA(i)}}function S(t,e,r,n){for(var o,i,a=0,u=e.length,c=r.length;a<u;){if(n>=c)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=O[(o=e.charAt(a++))in ek?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,r6.parse,r4=r6.utcFormat,r6.utcParse;var rV=r(29853),rG=r(88982);function rK(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function rJ(t,e){return t[e]}function rQ(t){let e=[];return e.key=t,e}var r0,r1,r2,r3,r6,r8,r4,r5,r7,r9=r(23095),nt=r.n(r9),ne=r(99488),nr=r.n(ne),nn=r(28288),no=r.n(nn),ni=r(86830),na=r.n(ni),nu=r(37943),nc=r.n(nu),nl=r(28501),ns=r.n(nl),nf=r(86107),np=r.n(nf),nh=r(99470),nd=r.n(nh),ny=r(30531),nv=r.n(ny),nm=r(38656),nb=r.n(nm),ng=r(38701),nx=r.n(ng),nO=!0,nw="[DecimalError] ",nj=nw+"Invalid argument: ",nS=nw+"Exponent out of range: ",nP=Math.floor,nA=Math.pow,nE=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,nM=nP(1286742750677284.5),nk={};function nT(t,e){var r,n,o,i,a,u,c,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),nO?nU(e,f):e;if(c=t.d,l=e.d,a=t.e,o=e.e,c=c.slice(),i=a-o){for(i<0?(n=c,i=-i,u=l.length):(n=l,o=a,u=c.length),i>(u=(a=Math.ceil(f/7))>u?a+1:u+1)&&(i=u,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((u=c.length)-(i=l.length)<0&&(i=u,n=l,l=c,c=n),r=0;i;)r=(c[--i]=c[i]+l[i]+r)/1e7|0,c[i]%=1e7;for(r&&(c.unshift(r),++o),u=c.length;0==c[--u];)c.pop();return e.d=c,e.e=o,nO?nU(e,f):e}function n_(t,e,r){if(t!==~~t||t<e||t>r)throw Error(nj+t)}function nC(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=nB(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=nB(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}nk.absoluteValue=nk.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},nk.comparedTo=nk.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},nk.decimalPlaces=nk.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},nk.dividedBy=nk.div=function(t){return nI(this,new this.constructor(t))},nk.dividedToIntegerBy=nk.idiv=function(t){var e=this.constructor;return nU(nI(this,new e(t),0,1),e.precision)},nk.equals=nk.eq=function(t){return!this.cmp(t)},nk.exponent=function(){return nN(this)},nk.greaterThan=nk.gt=function(t){return this.cmp(t)>0},nk.greaterThanOrEqualTo=nk.gte=function(t){return this.cmp(t)>=0},nk.isInteger=nk.isint=function(){return this.e>this.d.length-2},nk.isNegative=nk.isneg=function(){return this.s<0},nk.isPositive=nk.ispos=function(){return this.s>0},nk.isZero=function(){return 0===this.s},nk.lessThan=nk.lt=function(t){return 0>this.cmp(t)},nk.lessThanOrEqualTo=nk.lte=function(t){return 1>this.cmp(t)},nk.logarithm=nk.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(r7))throw Error(nw+"NaN");if(this.s<1)throw Error(nw+(this.s?"NaN":"-Infinity"));return this.eq(r7)?new r(0):(nO=!1,e=nI(nR(this,o),nR(t,o),o),nO=!0,nU(e,n))},nk.minus=nk.sub=function(t){return t=new this.constructor(t),this.s==t.s?n$(this,t):nT(this,(t.s=-t.s,t))},nk.modulo=nk.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(nw+"NaN");return this.s?(nO=!1,e=nI(this,t,0,1).times(t),nO=!0,this.minus(e)):nU(new r(this),n)},nk.naturalExponential=nk.exp=function(){return nD(this)},nk.naturalLogarithm=nk.ln=function(){return nR(this)},nk.negated=nk.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},nk.plus=nk.add=function(t){return t=new this.constructor(t),this.s==t.s?nT(this,t):n$(this,(t.s=-t.s,t))},nk.precision=nk.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(nj+t);if(e=nN(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},nk.squareRoot=nk.sqrt=function(){var t,e,r,n,o,i,a,u=this.constructor;if(this.s<1){if(!this.s)return new u(0);throw Error(nw+"NaN")}for(t=nN(this),nO=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=nC(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=nP((t+1)/2)-(t<0||t%2),n=new u(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new u(o.toString()),o=a=(r=u.precision)+3;;)if(n=(i=n).plus(nI(this,i,a+2)).times(.5),nC(i.d).slice(0,a)===(e=nC(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(nU(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return nO=!0,nU(n,r)},nk.times=nk.mul=function(t){var e,r,n,o,i,a,u,c,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(c=f.length)<(l=p.length)&&(i=f,f=p,p=i,a=c,c=l,l=a),i=[],n=a=c+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=c+n;o>n;)u=i[o]+p[n]*f[o-n-1]+e,i[o--]=u%1e7|0,e=u/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,nO?nU(t,s.precision):t},nk.toDecimalPlaces=nk.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(n_(t,0,1e9),void 0===e?e=n.rounding:n_(e,0,8),nU(r,t+nN(r)+1,e))},nk.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=nF(n,!0):(n_(t,0,1e9),void 0===e?e=o.rounding:n_(e,0,8),r=nF(n=nU(new o(n),t+1,e),!0,t+1)),r},nk.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?nF(this):(n_(t,0,1e9),void 0===e?e=o.rounding:n_(e,0,8),r=nF((n=nU(new o(this),t+nN(this)+1,e)).abs(),!1,t+nN(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},nk.toInteger=nk.toint=function(){var t=this.constructor;return nU(new t(this),nN(this)+1,t.rounding)},nk.toNumber=function(){return+this},nk.toPower=nk.pow=function(t){var e,r,n,o,i,a,u=this,c=u.constructor,l=+(t=new c(t));if(!t.s)return new c(r7);if(!(u=new c(u)).s){if(t.s<1)throw Error(nw+"Infinity");return u}if(u.eq(r7))return u;if(n=c.precision,t.eq(r7))return nU(u,n);if(a=(e=t.e)>=(r=t.d.length-1),i=u.s,a){if((r=l<0?-l:l)<=9007199254740991){for(o=new c(r7),e=Math.ceil(n/7+4),nO=!1;r%2&&nZ((o=o.times(u)).d,e),0!==(r=nP(r/2));)nZ((u=u.times(u)).d,e);return nO=!0,t.s<0?new c(r7).div(o):nU(o,n)}}else if(i<0)throw Error(nw+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,u.s=1,nO=!1,o=t.times(nR(u,n+12)),nO=!0,(o=nD(o)).s=i,o},nk.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=nN(o),n=nF(o,r<=i.toExpNeg||r>=i.toExpPos)):(n_(t,1,1e9),void 0===e?e=i.rounding:n_(e,0,8),r=nN(o=nU(new i(o),t,e)),n=nF(o,t<=r||r<=i.toExpNeg,t)),n},nk.toSignificantDigits=nk.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(n_(t,1,1e9),void 0===e?e=r.rounding:n_(e,0,8)),nU(new r(this),t,e)},nk.toString=nk.valueOf=nk.val=nk.toJSON=nk[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=nN(this),e=this.constructor;return nF(this,t<=e.toExpNeg||t>=e.toExpPos)};var nI=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var u,c,l,s,f,p,h,d,y,v,m,b,g,x,O,w,j,S,P=n.constructor,A=n.s==o.s?1:-1,E=n.d,M=o.d;if(!n.s)return new P(n);if(!o.s)throw Error(nw+"Division by zero");for(l=0,c=n.e-o.e,j=M.length,O=E.length,d=(h=new P(A)).d=[];M[l]==(E[l]||0);)++l;if(M[l]>(E[l]||0)&&--c,(b=null==i?i=P.precision:a?i+(nN(n)-nN(o))+1:i)<0)return new P(0);if(b=b/7+2|0,l=0,1==j)for(s=0,M=M[0],b++;(l<O||s)&&b--;l++)g=1e7*s+(E[l]||0),d[l]=g/M|0,s=g%M|0;else{for((s=1e7/(M[0]+1)|0)>1&&(M=t(M,s),E=t(E,s),j=M.length,O=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(S=M.slice()).unshift(0),w=M[0],M[1]>=1e7/2&&++w;do s=0,(u=e(M,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/w|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(M,s)).length,v=y.length,1==(u=e(f,y,p,v))&&(s--,r(f,j<p?S:M,p))):(0==s&&(u=s=1),f=M.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==u&&(v=y.length,(u=e(M,y,j,v))<1&&(s++,r(y,j<v?S:M,v))),v=y.length):0===u&&(s++,y=[0]),d[l++]=s,u&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<O||void 0!==y[0])&&b--)}return d[0]||d.shift(),h.e=c,nU(h,a?i+nN(h)+1:i)}}();function nD(t,e){var r,n,o,i,a,u=0,c=0,l=t.constructor,s=l.precision;if(nN(t)>16)throw Error(nS+nN(t));if(!t.s)return new l(r7);for(null==e?(nO=!1,a=s):a=e,i=new l(.03125);t.abs().gte(.1);)t=t.times(i),c+=5;for(a+=Math.log(nA(2,c))/Math.LN10*2+5|0,r=n=o=new l(r7),l.precision=a;;){if(n=nU(n.times(t),a),r=r.times(++u),nC((i=o.plus(nI(n,r,a))).d).slice(0,a)===nC(o.d).slice(0,a)){for(;c--;)o=nU(o.times(o),a);return l.precision=s,null==e?(nO=!0,nU(o,s)):o}o=i}}function nN(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function nL(t,e,r){if(e>t.LN10.sd())throw nO=!0,r&&(t.precision=r),Error(nw+"LN10 precision limit exceeded");return nU(new t(t.LN10),e)}function nB(t){for(var e="";t--;)e+="0";return e}function nR(t,e){var r,n,o,i,a,u,c,l,s,f=1,p=t,h=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(nw+(p.s?"NaN":"-Infinity"));if(p.eq(r7))return new d(0);if(null==e?(nO=!1,l=y):l=e,p.eq(10))return null==e&&(nO=!0),nL(d,l);if(l+=10,d.precision=l,n=(r=nC(h)).charAt(0),!(15e14>Math.abs(i=nN(p))))return c=nL(d,l+2,y).times(i+""),p=nR(new d(n+"."+r.slice(1)),l-10).plus(c),d.precision=y,null==e?(nO=!0,nU(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=nC((p=p.times(t)).d)).charAt(0),f++;for(i=nN(p),n>1?(p=new d("0."+r),i++):p=new d(n+"."+r.slice(1)),u=a=p=nI(p.minus(r7),p.plus(r7),l),s=nU(p.times(p),l),o=3;;){if(a=nU(a.times(s),l),nC((c=u.plus(nI(a,new d(o),l))).d).slice(0,l)===nC(u.d).slice(0,l))return u=u.times(2),0!==i&&(u=u.plus(nL(d,l+2,y).times(i+""))),u=nI(u,new d(f),l),d.precision=y,null==e?(nO=!0,nU(u,y)):u;u=c,o+=2}}function nz(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,r=r-n-1,t.e=nP(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),nO&&(t.e>nM||t.e<-nM))throw Error(nS+r)}else t.s=0,t.e=0,t.d=[0];return t}function nU(t,e,r){var n,o,i,a,u,c,l,s,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return t;for(a=1,l=i=f[s];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(u=l/(i=nA(10,a-o-1))%10|0,c=e<0||void 0!==f[s+1]||l%i,c=r<4?(u||c)&&(0==r||r==(t.s<0?3:2)):u>5||5==u&&(4==r||c||6==r&&(n>0?o>0?l/nA(10,a-o):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return c?(i=nN(t),f.length=1,e=e-i-1,f[0]=nA(10,(7-e%7)%7),t.e=nP(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=nA(10,7-n),f[s]=o>0?(l/nA(10,a-o)%nA(10,o)|0)*i:0),c)for(;;){if(0==s){1e7==(f[0]+=i)&&(f[0]=1,++t.e);break}if(f[s]+=i,1e7!=f[s])break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(nO&&(t.e>nM||t.e<-nM))throw Error(nS+nN(t));return t}function n$(t,e){var r,n,o,i,a,u,c,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),nO?nU(e,h):e;if(c=t.d,f=e.d,n=e.e,l=t.e,c=c.slice(),a=l-n){for((s=a<0)?(r=c,a=-a,u=f.length):(r=f,n=l,u=c.length),a>(o=Math.max(Math.ceil(h/7),u)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=c.length)<(u=f.length))&&(u=o),o=0;o<u;o++)if(c[o]!=f[o]){s=c[o]<f[o];break}a=0}for(s&&(r=c,c=f,f=r,e.s=-e.s),u=c.length,o=f.length-u;o>0;--o)c[u++]=0;for(o=f.length;o>a;){if(c[--o]<f[o]){for(i=o;i&&0===c[--i];)c[i]=1e7-1;--c[i],c[o]+=1e7}c[o]-=f[o]}for(;0===c[--u];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(e.d=c,e.e=n,nO?nU(e,h):e):new p(0)}function nF(t,e,r){var n,o=nN(t),i=nC(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+nB(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+nB(-o-1)+i,r&&(n=r-a)>0&&(i+=nB(n))):o>=a?(i+=nB(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+nB(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=nB(n))),t.s<0?"-"+i:i}function nZ(t,e){if(t.length>e)return t.length=e,!0}function nq(t){if(!t||"object"!=typeof t)throw Error(nw+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(nP(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(nj+r+": "+n)}if(void 0!==(n=t[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(nj+r+": "+n)}return this}var r5=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(nj+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return nz(this,t.toString())}if("string"!=typeof t)throw Error(nj+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,nE.test(t))nz(this,t);else throw Error(nj+t)}if(i.prototype=nk,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=nq,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});r7=new r5(1);let nW=r5;function nH(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nY=function(t){return t},nX={},nV=function(t){return t===nX},nG=function(t){return function e(){return 0==arguments.length||1==arguments.length&&nV(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},nK=function(t){return function t(e,r){return 1===e?r:nG(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==nX}).length;return a>=e?r.apply(void 0,o):t(e-a,nG(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return nV(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return nH(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return nH(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nH(t,void 0)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},nJ=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},nQ=nK(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),n0=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return nY;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},n1=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},n2=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};nK(function(t,e,r){var n=+t;return n+r*(+e-n)}),nK(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),nK(function(t,e,r){var n=e-+t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let n3={rangeStep:function(t,e,r){for(var n=new nW(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new nW(t).abs().log(10).toNumber())+1}};function n6(t){return function(t){if(Array.isArray(t))return n5(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||n4(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n8(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,u=t[Symbol.iterator]();!(n=(a=u.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==u.return||u.return()}finally{if(o)throw i}}return r}}(t,e)||n4(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n4(t,e){if(t){if("string"==typeof t)return n5(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n5(t,e)}}function n5(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function n7(t){var e=n8(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function n9(t,e,r){if(t.lte(0))return new nW(0);var n=n3.getDigitCount(t.toNumber()),o=new nW(10).pow(n),i=t.div(o),a=1!==n?.05:.1,u=new nW(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?u:new nW(Math.ceil(u))}function ot(t,e,r){var n=1,o=new nW(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new nW(10).pow(n3.getDigitCount(t)-1),o=new nW(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new nW(Math.floor(t)))}else 0===t?o=new nW(Math.floor((e-1)/2)):r||(o=new nW(Math.floor(t)));var a=Math.floor((e-1)/2);return n0(nQ(function(t){return o.add(new nW(t-a).mul(n)).toNumber()}),nJ)(0,e)}var oe=n2(function(t){var e=n8(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=n8(n7([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0){var s=l===1/0?[c].concat(n6(nJ(0,o-1).map(function(){return 1/0}))):[].concat(n6(nJ(0,o-1).map(function(){return-1/0})),[l]);return r>n?n1(s):s}if(c===l)return ot(c,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new nW(0),tickMin:new nW(0),tickMax:new nW(0)};var u=n9(new nW(r).sub(e).div(n-1),o,a),c=Math.ceil((i=e<=0&&r>=0?new nW(0):(i=new nW(e).add(r).div(2)).sub(new nW(i).mod(u))).sub(e).div(u).toNumber()),l=Math.ceil(new nW(r).sub(i).div(u).toNumber()),s=c+l+1;return s>n?t(e,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,c=r>0?c:c+(n-s)),{step:u,tickMin:i.sub(new nW(c).mul(u)),tickMax:i.add(new nW(l).mul(u))})}(c,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=n3.rangeStep(h,d.add(new nW(.1).mul(p)),p);return r>n?n1(y):y});n2(function(t){var e=n8(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=n8(n7([r,n]),2),c=u[0],l=u[1];if(c===-1/0||l===1/0)return[r,n];if(c===l)return ot(c,o,i);var s=n9(new nW(l).sub(c).div(a-1),i,0),f=n0(nQ(function(t){return new nW(c).add(new nW(t).mul(s)).toNumber()}),nJ)(0,a).filter(function(t){return t>=c&&t<=l});return r>n?n1(f):f});var or=n2(function(t,e){var r=n8(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=n8(n7([n,o]),2),u=a[0],c=a[1];if(u===-1/0||c===1/0)return[n,o];if(u===c)return[u];var l=n9(new nW(c).sub(u).div(Math.max(e,2)-1),i,0),s=[].concat(n6(n3.rangeStep(new nW(u),new nW(c).sub(new nW(.99).mul(l)),l)),[c]);return n>o?n1(s):s}),on=r(31222),oo=r(43229),oi=r(80131),oa=r(87709);function ou(t){return(ou="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function oc(t){return function(t){if(Array.isArray(t))return ol(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ol(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ol(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ol(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function os(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function of(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?os(Object(r),!0).forEach(function(e){op(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):os(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function op(t,e,r){var n;return(n=function(t,e){if("object"!=ou(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ou(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==ou(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function oh(t,e,r){return no()(t)||no()(e)?r:(0,oo.P2)(e)?ns()(t,e,r):na()(e)?e(t):r}function od(t,e,r,n){var o=np()(t,function(t){return oh(t,e)});if("number"===r){var i=o.filter(function(t){return(0,oo.hj)(t)||parseFloat(t)});return i.length?[nr()(i),nt()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!no()(t)}):o).map(function(t){return(0,oo.P2)(t)||t instanceof Date?t:""})}var oy=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var u=o.range,c=0;c<a;c++){var l=c>0?n[c-1].coordinate:n[a-1].coordinate,s=n[c].coordinate,f=c>=a-1?n[0].coordinate:n[c+1].coordinate,p=void 0;if((0,oo.uY)(s-l)!==(0,oo.uY)(f-s)){var h=[];if((0,oo.uY)(f-s)===(0,oo.uY)(u[1]-u[0])){p=f;var d=s+u[1]-u[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+u[1]-u[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[c].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[c].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},ov=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?of(of({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},om=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),u=0,c=a.length;u<c;u++)for(var l=o[a[u]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return(0,oi.Gf)(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?of(of({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var O=no()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:no()(O)?void 0:(0,oo.h1)(O,r,0)})}}return i},ob=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,u=t.maxBarSize,c=a.length;if(c<1)return null;var l=(0,oo.h1)(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/c,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(c-1)*l)>=o&&(h-=(c-1)*l,l=0),h>=o&&p>0&&(f=!0,p*=.9,h=c*p);var d={offset:((o-h)/2>>0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(oc(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=(0,oo.h1)(n,o,0,!0);o-2*y-(c-1)*l<=0&&(l=0);var v=(o-2*y-(c-1)*l)/c;v>1&&(v>>=0);var m=u===+u?Math.min(v,u):v;e=a.reduce(function(t,e,r){var n=[].concat(oc(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},og=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,u=i-(a.left||0)-(a.right||0),c=(0,oa.z)({children:o,legendWidth:u});if(c){var l=n||{},s=l.width,f=l.height,p=c.align,h=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===h)&&"center"!==p&&(0,oo.hj)(t[p]))return of(of({},t),{},op({},p,t[p]+(s||0)));if(("horizontal"===d||"vertical"===d&&"center"===p)&&"middle"!==h&&(0,oo.hj)(t[h]))return of(of({},t),{},op({},h,t[h]+(f||0)))}return t},ox=function(t,e,r,n,o){var i=e.props.children,a=(0,oi.NN)(i,on.W).filter(function(t){var e;return e=t.props.direction,!!no()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(a&&a.length){var u=a.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=oh(e,r);if(no()(n))return t;var o=Array.isArray(n)?[nr()(n),nt()(n)]:[n,n],i=u.reduce(function(t,r){var n=oh(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},oO=function(t,e,r,n,o){var i=e.map(function(e){return ox(t,e,r,o,n)}).filter(function(t){return!no()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},ow=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&ox(t,e,i,n)||od(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},oj=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},oS=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,u="scaleBand"===t.realScaleType?n.bandwidth()/2:2,c=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/u:0;return(c="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*(0,oo.uY)(a[0]-a[1])*c:c,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+c,value:t,offset:c}}).filter(function(t){return!nd()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+c,value:t,index:e,offset:c}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+c,value:t,offset:c}}):n.domain().map(function(t,e){return{coordinate:n(t)+c,value:o?o[t]:t,index:e,offset:c}})},oP=new WeakMap,oA=function(t,e){if("function"!=typeof e)return t;oP.has(t)||oP.set(t,new WeakMap);var r=oP.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},oE=function(t,e,r){var i=t.scale,a=t.type,u=t.layout,c=t.axisType;if("auto"===i)return"radial"===u&&"radiusAxis"===c?{scale:o.Z(),realScaleType:"band"}:"radial"===u&&"angleAxis"===c?{scale:tk(),realScaleType:"linear"}:"category"===a&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:o.x(),realScaleType:"point"}:"category"===a?{scale:o.Z(),realScaleType:"band"}:{scale:tk(),realScaleType:"linear"};if(nc()(i)){var l="scale".concat(nv()(i));return{scale:(n[l]||o.x)(),realScaleType:n[l]?l:"point"}}return na()(i)?{scale:i}:{scale:o.x(),realScaleType:"point"}},oM=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),u=t(e[r-1]);(a<o||a>i||u<o||u>i)&&t.domain([e[0],e[r-1]])}},ok=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},oT=function(t,e){if(!e||2!==e.length||!(0,oo.hj)(e[0])||!(0,oo.hj)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!(0,oo.hj)(t[0])||t[0]<r)&&(o[0]=r),(!(0,oo.hj)(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},o_={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var u=nd()(t[a][r][1])?t[a][r][0]:t[a][r][1];u>=0?(t[a][r][0]=o,t[a][r][1]=o+u,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+u,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}rX(t,e)}},none:rX,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,u=0;a<r;++a)u+=t[a][n][1]||0;o[n][1]+=o[n][0]=-u/2}rX(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var u=0,c=0,l=0;u<o;++u){for(var s=t[e[u]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<u;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}c+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,c&&(i-=l/c)}r[a-1][1]+=r[a-1][0]=i,rX(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=nd()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},oC=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=o_[r];return(function(){var t=(0,rG.Z)([]),e=rK,r=rX,n=rJ;function o(o){var i,a,u=Array.from(t.apply(this,arguments),rQ),c=u.length,l=-1;for(let t of o)for(i=0,++l;i<c;++i)(u[i][l]=[0,+n(t,u[i].key,l,o)]).data=t;for(i=0,a=(0,rV.Z)(e(u));i<c;++i)u[a[i]].index=i;return r(u,a),u}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,rG.Z)(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:(0,rG.Z)(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?rK:"function"==typeof t?t:(0,rG.Z)(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?rX:t,o):r},o})().keys(n).value(function(t,e){return+oh(t,e,0)}).order(rK).offset(o)(t)},oI=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?of(of({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var u=i[r],c=t[u]||{hasStack:!1,stackGroups:{}};if((0,oo.P2)(a)){var l=c.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),c.hasStack=!0,c.stackGroups[a]=l}else c.stackGroups[(0,oo.EL)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return of(of({},t),{},op({},u,c))},{});return Object.keys(a).reduce(function(e,i){var u=a[i];return u.hasStack&&(u.stackGroups=Object.keys(u.stackGroups).reduce(function(e,i){var a=u.stackGroups[i];return of(of({},e),{},op({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:oC(t,a.items,o)}))},{})),of(of({},e),{},op({},i,u))},{})},oD=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,u=r||e.scale;if("auto"!==u&&"linear"!==u)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var c=t.domain();if(!c.length)return null;var l=oe(c,o,a);return t.domain([nr()(l),nt()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:or(t.domain(),o,a)}:null};function oN(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!no()(o[e.dataKey])){var u=(0,oo.Ap)(r,"value",o[e.dataKey]);if(u)return u.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var c=oh(o,no()(a)?e.dataKey:a);return no()(c)?null:e.scale(c)}var oL=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var u=oh(i,e.dataKey,e.domain[a]);return no()(u)?null:e.scale(u)-o/2+n},oB=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},oR=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?of(of({},t.type.defaultProps),t.props):t.props).stackId;if((0,oo.P2)(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},oz=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[nr()(e.concat([t[0]]).filter(oo.hj)),nt()(e.concat([t[1]]).filter(oo.hj))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},oU=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,o$=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,oF=function(t,e,r){if(na()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if((0,oo.hj)(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(oU.test(t[0])){var o=+oU.exec(t[0])[1];n[0]=e[0]-o}else na()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if((0,oo.hj)(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(o$.test(t[1])){var i=+o$.exec(t[1])[1];n[1]=e[1]+i}else na()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},oZ=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=nx()(e,function(t){return t.coordinate}),i=1/0,a=1,u=o.length;a<u;a++){var c=o[a],l=o[a-1];i=Math.min((c.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},oq=function(t,e,r){return!t||!t.length||nb()(t,ns()(r,"type.defaultProps.domain"))?e:t},oW=function(t,e){var r=t.type.defaultProps?of(of({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,u=r.tooltipType,c=r.chartType,l=r.hide;return of(of({},(0,oi.L6)(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:ov(t),value:oh(e,n),type:u,payload:e,chartType:c,hide:l})}},94438:(t,e,r)=>{"use strict";r.d(e,{os:()=>f,xE:()=>s});var n=r(12307);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=o(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var u={widthCache:{},cacheCount:0},c={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},l="recharts_measurement_span",s=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||n.x.isSsr)return{width:0,height:0};var o=(Object.keys(e=a({},r)).forEach(function(t){e[t]||delete e[t]}),e),i=JSON.stringify({text:t,copyStyle:o});if(u.widthCache[i])return u.widthCache[i];try{var s=document.getElementById(l);s||((s=document.createElement("span")).setAttribute("id",l),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var f=a(a({},c),o);Object.assign(s.style,f),s.textContent="".concat(t);var p=s.getBoundingClientRect(),h={width:p.width,height:p.height};return u.widthCache[i]=h,++u.cacheCount>2e3&&(u.cacheCount=0,u.widthCache={}),h}catch(t){return{width:0,height:0}}},f=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},43229:(t,e,r)=>{"use strict";r.d(e,{Ap:()=>S,EL:()=>g,Kt:()=>O,P2:()=>m,Rw:()=>v,bv:()=>w,fC:()=>P,h1:()=>x,hU:()=>d,hj:()=>y,k4:()=>j,uY:()=>h});var n=r(37943),o=r.n(n),i=r(99470),a=r.n(i),u=r(28501),c=r.n(u),l=r(39479),s=r.n(l),f=r(28288),p=r.n(f),h=function(t){return 0===t?0:t>0?1:-1},d=function(t){return o()(t)&&t.indexOf("%")===t.length-1},y=function(t){return s()(t)&&!a()(t)},v=function(t){return p()(t)},m=function(t){return y(t)||o()(t)},b=0,g=function(t){var e=++b;return"".concat(t||"").concat(e)},x=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!y(t)&&!o()(t))return n;if(d(t)){var u=t.indexOf("%");r=e*parseFloat(t.slice(0,u))/100}else r=+t;return a()(r)&&(r=n),i&&r>e&&(r=e),r},O=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},w=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},j=function(t,e){return y(t)&&y(e)?function(r){return t+r*(e-t)}:function(){return e}};function S(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):c()(t,e))===r}):null}var P=function(t,e){return y(t)&&y(e)?t-e:o()(t)&&o()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))}},12307:(t,e,r)=>{"use strict";r.d(e,{x:()=>n});var n={isSsr:!0,get:function(t){return n[t]},set:function(t,e){if("string"==typeof t)n[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){n[e]=t[e]})}}}},60307:(t,e,r)=>{"use strict";r.d(e,{Z:()=>n});var n=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]}},80076:(t,e,r)=>{"use strict";r.d(e,{$4:()=>m,$S:()=>j,Wk:()=>y,op:()=>v,t9:()=>b,z3:()=>w});var n=r(28288),o=r.n(n),i=r(60343),a=r(86830),u=r.n(a),c=r(43229),l=r(56361);function s(t){return(s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){h(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e,r){var n;return(n=function(t,e){if("object"!=s(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==s(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var y=Math.PI/180,v=function(t,e,r,n){return{x:t+Math.cos(-y*n)*r,y:e+Math.sin(-y*n)*r}},m=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},b=function(t,e,r,n,i){var a=t.width,u=t.height,s=t.startAngle,f=t.endAngle,y=(0,c.h1)(t.cx,a,a/2),v=(0,c.h1)(t.cy,u,u/2),b=m(a,u,r),g=(0,c.h1)(t.innerRadius,b,0),x=(0,c.h1)(t.outerRadius,b,.8*b);return Object.keys(e).reduce(function(t,r){var a,u=e[r],c=u.domain,m=u.reversed;if(o()(u.range))"angleAxis"===n?a=[s,f]:"radiusAxis"===n&&(a=[g,x]),m&&(a=[a[1],a[0]]);else{var b,O=function(t){if(Array.isArray(t))return t}(b=a=u.range)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,l=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){l=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return u}}(b,2)||function(t,e){if(t){if("string"==typeof t)return d(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return d(t,2)}}(b,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();s=O[0],f=O[1]}var w=(0,l.Hq)(u,i),j=w.realScaleType,S=w.scale;S.domain(c).range(a),(0,l.zF)(S);var P=(0,l.g$)(S,p(p({},u),{},{realScaleType:j})),A=p(p(p({},u),P),{},{range:a,radius:x,realScaleType:j,scale:S,cx:y,cy:v,innerRadius:g,outerRadius:x,startAngle:s,endAngle:f});return p(p({},t),{},h({},r,A))},{})},g=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},x=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=g({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var u=Math.acos((r-o)/a);return n>i&&(u=2*Math.PI-u),{radius:a,angle:180*u/Math.PI,angleInRadian:u}},O=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},w=function(t,e){var r,n=x({x:t.x,y:t.y},e),o=n.radius,i=n.angle,a=e.innerRadius,u=e.outerRadius;if(o<a||o>u)return!1;if(0===o)return!0;var c=O(e),l=c.startAngle,s=c.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?p(p({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null},j=function(t){return(0,i.isValidElement)(t)||u()(t)||"boolean"==typeof t?"":t.className}},80131:(t,e,r)=>{"use strict";r.d(e,{$R:()=>R,Bh:()=>B,Gf:()=>j,L6:()=>I,NN:()=>E,TT:()=>k,eu:()=>L,jf:()=>_,rL:()=>D,sP:()=>M});var n=r(28501),o=r.n(n),i=r(28288),a=r.n(i),u=r(37943),c=r.n(u),l=r(86830),s=r.n(l),f=r(4171),p=r.n(f),h=r(60343),d=r(45492),y=r(43229),v=r(51128),m=r(34718),b=["children"],g=["children"];function x(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var w={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},j=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},S=null,P=null,A=function t(e){if(e===S&&Array.isArray(P))return P;var r=[];return h.Children.forEach(e,function(e){a()(e)||((0,d.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),P=r,S=e,r};function E(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return j(t)}):[j(e)],A(t).forEach(function(t){var e=o()(t,"type.displayName")||o()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function M(t,e){var r=E(t,e);return r&&r[0]}var k=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!(0,y.hj)(r)&&!(r<=0)&&!!(0,y.hj)(n)&&!(n<=0)},T=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],_=function(t){return t&&"object"===O(t)&&"clipDot"in t},C=function(t,e,r,n){var o,i=null!==(o=null===m.ry||void 0===m.ry?void 0:m.ry[n])&&void 0!==o?o:[];return e.startsWith("data-")||!s()(t)&&(n&&i.includes(e)||m.Yh.includes(e))||r&&m.nv.includes(e)},I=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,h.isValidElement)(t)&&(n=t.props),!p()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;C(null===(i=n)||void 0===i?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},D=function t(e,r){if(e===r)return!0;var n=h.Children.count(e);if(n!==h.Children.count(r))return!1;if(0===n)return!0;if(1===n)return N(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=e[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!N(i,a))return!1}return!0},N=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var r=t.props||{},n=r.children,o=x(r,b),i=e.props||{},u=i.children,c=x(i,g);if(n&&u)return(0,v.w)(o,c)&&D(n,u);if(!n&&!u)return(0,v.w)(o,c)}return!1},L=function(t,e){var r=[],n={};return A(t).forEach(function(t,o){if(t&&t.type&&c()(t.type)&&T.indexOf(t.type)>=0)r.push(t);else if(t){var i=j(t.type),a=e[i]||{},u=a.handler,l=a.once;if(u&&(!l||!n[i])){var s=u(t,i,o);r.push(s),n[i]=!0}}}),r},B=function(t){var e=t&&t.type;return e&&w[e]?w[e]:null},R=function(t,e){return A(e).indexOf(t)}},51128:(t,e,r)=>{"use strict";function n(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}r.d(e,{w:()=>n})},87709:(t,e,r)=>{"use strict";r.d(e,{z:()=>l});var n=r(94309),o=r(56361),i=r(80131);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var l=function(t){var e,r=t.children,a=t.formattedGraphicalItems,u=t.legendWidth,l=t.legendContent,s=(0,i.sP)(r,n.D);if(!s)return null;var f=n.D.defaultProps,p=void 0!==f?c(c({},f),s.props):{};return e=s.props&&s.props.payload?s.props&&s.props.payload:"children"===l?(a||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:s.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(a||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?c(c({},r),e.props):{},i=n.dataKey,a=n.name,u=n.legendType;return{inactive:n.hide,dataKey:i,type:p.iconType||u||"square",color:(0,o.fk)(e),value:a||i,payload:n}}),c(c(c({},p),n.D.getWithHeight(s,u)),{},{payload:e,item:s})}},37880:(t,e,r)=>{"use strict";r.d(e,{z:()=>u});var n=r(35741),o=r.n(n),i=r(86830),a=r.n(i);function u(t,e,r){return!0===e?o()(t,r):a()(e)?o()(t,e):t}},34718:(t,e,r)=>{"use strict";r.d(e,{Yh:()=>u,Ym:()=>f,bw:()=>p,nv:()=>s,ry:()=>l});var n=r(60343),o=r(4171),i=r.n(o);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],c=["points","pathLength"],l={svg:["viewBox","children"],polygon:c,polyline:c},s=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),!i()(r))return null;var o={};return Object.keys(r).forEach(function(t){s.includes(t)&&(o[t]=e||function(e){return r[t](r,e)})}),o},p=function(t,e,r){if(!i()(t)||"object"!==a(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];s.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n}},91358:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i,x:()=>a});var n=r(94095),o=r(3013);function i(){var t,e,r=(0,o.Z)().unknown(void 0),a=r.domain,u=r.range,c=0,l=1,s=!1,f=0,p=0,h=.5;function d(){var r=a().length,n=l<c,o=n?l:c,i=n?c:l;t=(i-o)/Math.max(1,r-f+2*p),s&&(t=Math.floor(t)),o+=(i-o-t*(r-f))*h,e=t*(1-f),s&&(o=Math.round(o),e=Math.round(e));var d=(function(t,e,r){t=+t,e=+e,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return o+t*e});return u(n?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(a(t),d()):a()},r.range=function(t){return arguments.length?([c,l]=t,c=+c,l=+l,d()):[c,l]},r.rangeRound=function(t){return[c,l]=t,c=+c,l=+l,s=!0,d()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(s=!!t,d()):s},r.padding=function(t){return arguments.length?(f=Math.min(1,p=+t),d()):f},r.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),d()):f},r.paddingOuter=function(t){return arguments.length?(p=+t,d()):p},r.align=function(t){return arguments.length?(h=Math.max(0,Math.min(1,t)),d()):h},r.copy=function(){return i(a(),[c,l]).round(s).paddingInner(f).paddingOuter(p).align(h)},n.o.apply(d(),arguments)}function a(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(i.apply(null,arguments).paddingInner(1))}},94095:(t,e,r)=>{"use strict";function n(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function o(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}r.d(e,{O:()=>o,o:()=>n})},3013:(t,e,r)=>{"use strict";r.d(e,{Z:()=>function t(){var e=new n,r=[],o=[],i=u;function c(t){let n=e.get(t);if(void 0===n){if(i!==u)return i;e.set(t,n=r.push(t)-1)}return o[n%o.length]}return c.domain=function(t){if(!arguments.length)return r.slice();for(let o of(r=[],e=new n,t))e.has(o)||e.set(o,r.push(o)-1);return c},c.range=function(t){return arguments.length?(o=Array.from(t),c):o.slice()},c.unknown=function(t){return arguments.length?(i=t,c):i},c.copy=function(){return t(r,o).unknown(i)},a.o.apply(c,arguments),c},O:()=>u});class n extends Map{constructor(t,e=i){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(o(this,t))}has(t){return super.has(o(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function o({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function i(t){return null!==t&&"object"==typeof t?t.valueOf():t}var a=r(94095);let u=Symbol("implicit")},29853:(t,e,r)=>{"use strict";r.d(e,{Z:()=>o,t:()=>n});var n=Array.prototype.slice;function o(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}},88982:(t,e,r)=>{"use strict";function n(t){return function(){return t}}r.d(e,{Z:()=>n})},60884:(t,e,r)=>{"use strict";r.d(e,{d:()=>c});let n=Math.PI,o=2*n,i=o-1e-6;function a(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class u{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?a:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return a;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,o,i){if(t=+t,e=+e,r=+r,o=+o,(i=+i)<0)throw Error(`negative radius: ${i}`);let a=this._x1,u=this._y1,c=r-t,l=o-e,s=a-t,f=u-e,p=s*s+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(p>1e-6){if(Math.abs(f*c-l*s)>1e-6&&i){let h=r-a,d=o-u,y=c*c+l*l,v=Math.sqrt(y),m=Math.sqrt(p),b=i*Math.tan((n-Math.acos((y+p-(h*h+d*d))/(2*v*m)))/2),g=b/m,x=b/v;Math.abs(g-1)>1e-6&&this._append`L${t+g*s},${e+g*f}`,this._append`A${i},${i},0,0,${+(f*h>s*d)},${this._x1=t+x*c},${this._y1=e+x*l}`}else this._append`L${this._x1=t},${this._y1=e}`}}arc(t,e,r,a,u,c){if(t=+t,e=+e,c=!!c,(r=+r)<0)throw Error(`negative radius: ${r}`);let l=r*Math.cos(a),s=r*Math.sin(a),f=t+l,p=e+s,h=1^c,d=c?a-u:u-a;null===this._x1?this._append`M${f},${p}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-p)>1e-6)&&this._append`L${f},${p}`,r&&(d<0&&(d=d%o+o),d>i?this._append`A${r},${r},0,1,${h},${t-l},${e-s}A${r},${r},0,1,${h},${this._x1=f},${this._y1=p}`:d>1e-6&&this._append`A${r},${r},0,${+(d>=n)},${h},${this._x1=t+r*Math.cos(u)},${this._y1=e+r*Math.sin(u)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function c(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new u(e)}u.prototype},56501:(t,e,r)=>{"use strict";function n(t,e){if(!t)throw Error("Invariant failed")}r.d(e,{Z:()=>n})}};