exports.id=1298,exports.ids=[1298],exports.modules={80648:(e,t,r)=>{Promise.resolve().then(r.bind(r,83495))},60826:e=>{e.exports=function(e){return e.split("")}},829:e=>{e.exports=function(e,t,r,a){for(var i=e.length,s=r+(a?1:-1);a?s--:++s<i;)if(t(e[s],s,e))return s;return -1}},65337:(e,t,r)=>{var a=r(829),i=r(35447),s=r(28026);e.exports=function(e,t,r){return t==t?s(e,t,r):a(e,i,r)}},35447:e=>{e.exports=function(e){return e!=e}},77420:e=>{e.exports=function(e,t,r){var a=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(r=r>i?i:r)<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var s=Array(i);++a<i;)s[a]=e[a+t];return s}},49513:(e,t,r)=>{var a=r(70458),i=/^\s+/;e.exports=function(e){return e?e.slice(0,a(e)+1).replace(i,""):e}},30482:(e,t,r)=>{var a=r(77420);e.exports=function(e,t,r){var i=e.length;return r=void 0===r?i:r,!t&&r>=i?e:a(e,t,r)}},74783:(e,t,r)=>{var a=r(65337);e.exports=function(e,t){for(var r=e.length;r--&&a(t,e[r],0)>-1;);return r}},41200:(e,t,r)=>{var a=r(65337);e.exports=function(e,t){for(var r=-1,i=e.length;++r<i&&a(t,e[r],0)>-1;);return r}},73211:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},28026:e=>{e.exports=function(e,t,r){for(var a=r-1,i=e.length;++a<i;)if(e[a]===t)return a;return -1}},66095:(e,t,r)=>{var a=r(60826),i=r(73211),s=r(92115);e.exports=function(e){return i(e)?s(e):a(e)}},70458:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},92115:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",a="\ud83c[\udffb-\udfff]",i="[^"+t+"]",s="(?:\ud83c[\udde6-\uddff]){2}",l="[\ud800-\udbff][\udc00-\udfff]",n="(?:"+r+"|"+a+")?",o="[\\ufe0e\\ufe0f]?",u="(?:\\u200d(?:"+[i,s,l].join("|")+")"+o+n+")*",d=RegExp(a+"(?="+a+")|(?:"+[i+r+"?",r,s,l,"["+t+"]"].join("|")+")"+(o+n+u),"g");e.exports=function(e){return e.match(d)||[]}},71241:(e,t,r)=>{var a=r(4171),i=r(96817),s=r(24436),l=Math.max,n=Math.min;e.exports=function(e,t,r){var o,u,d,c,m,p,f=0,h=!1,v=!1,x=!0;if("function"!=typeof e)throw TypeError("Expected a function");function g(t){var r=o,a=u;return o=u=void 0,f=t,c=e.apply(a,r)}function b(e){var r=e-p,a=e-f;return void 0===p||r>=t||r<0||v&&a>=d}function j(){var e,r,a,s=i();if(b(s))return y(s);m=setTimeout(j,(e=s-p,r=s-f,a=t-e,v?n(a,d-r):a))}function y(e){return(m=void 0,x&&o)?g(e):(o=u=void 0,c)}function C(){var e,r=i(),a=b(r);if(o=arguments,u=this,p=r,a){if(void 0===m)return f=e=p,m=setTimeout(j,t),h?g(e):c;if(v)return clearTimeout(m),m=setTimeout(j,t),g(p)}return void 0===m&&(m=setTimeout(j,t)),c}return t=s(t)||0,a(r)&&(h=!!r.leading,d=(v="maxWait"in r)?l(s(r.maxWait)||0,t):d,x="trailing"in r?!!r.trailing:x),C.cancel=function(){void 0!==m&&clearTimeout(m),f=0,o=p=u=m=void 0},C.flush=function(){return void 0===m?c:y(i())},C}},96817:(e,t,r)=>{var a=r(65584);e.exports=function(){return a.Date.now()}},24436:(e,t,r)=>{var a=r(49513),i=r(4171),s=r(15903),l=0/0,n=/^[-+]0x[0-9a-f]+$/i,o=/^0b[01]+$/i,u=/^0o[0-7]+$/i,d=parseInt;e.exports=function(e){if("number"==typeof e)return e;if(s(e))return l;if(i(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=i(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=a(e);var r=o.test(e);return r||u.test(e)?d(e.slice(2),r?2:8):n.test(e)?l:+e}},14826:(e,t,r)=>{var a=r(22060),i=r(49513),s=r(30482),l=r(74783),n=r(41200),o=r(66095),u=r(16266);e.exports=function(e,t,r){if((e=u(e))&&(r||void 0===t))return i(e);if(!e||!(t=a(t)))return e;var d=o(e),c=o(t),m=n(d,c),p=l(d,c)+1;return s(d,m,p).join("")}},67468:(e,t,r)=>{"use strict";r.d(t,{default:()=>w});var a=r(98768),i=r(72548),s=r(60343),l=r(71241),n=r.n(l),o=r(7678),u=r.n(o),d=r(14826),c=r.n(d),m=r(69424),p=r(76342),f=r(66263),h=r(78965),v=r(13842),x=r(46776),g=r(71890),b=r(57103),j=r(17203),y=r(81311),C=r(74602),D=r(39544);let w=({crewDutyId:e,onCancel:t,onCreate:r,isPopup:l=!1})=>{let[o,d]=(0,s.useState)({}),w=(0,m.useRouter)(),[N,E]=(0,s.useState)(!1),[S,I]=(0,s.useState)({title:"",abbreviation:""}),[M,T]=(0,s.useState)(!1),[A,F]=(0,s.useState)(!1);(0,v.UL)(e,d);let L=n()((t,r)=>{d({...o,[t]:r,id:+e})},300),B=e=>{let{name:t,value:r}=e.target;L(t,r)},U=async()=>{let t=!1,r={title:"",abbreviation:""};if(u()(c()(o.title))&&(t=!0,r.title="Title is required"),u()(c()(o.abbreviation))&&(t=!0,r.abbreviation="Abbreviation is required"),t){E(!0),I(r);return}let a={input:{id:+o.id,title:o.title,abbreviation:o.abbreviation}};0===e?await k({variables:a}):await R({variables:a})},[k,{loading:P}]=(0,i.D)(p.fJx,{onCompleted:e=>{let t=e.createCrewDuty;t.id>0?r?r(t):w.back():console.error("mutationCreateCrewDuty error",e)},onError:e=>{console.error("mutationCreateCrewDuty error",e)}}),[R,{loading:V}]=(0,i.D)(p.Qem,{onCompleted:e=>{e.updateCrewDuty.id>0?w.back():console.error("mutationUpdateCrewDuty error",e)},onError:e=>{console.error("mutationUpdateCrewDuty error",e)}}),q=async e=>{await $({variables:{ids:[e.id]}})},[$]=(0,i.D)(p.T5T,{onCompleted:()=>{w.push("/settings/crew-duty/list")},onError:e=>{console.error("mutationDeleteCrewDuty error",e)}});return(0,s.useEffect)(()=>{(0,x.UU)()},[]),(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:" flex justify-between pb-4 pt-3 items-center",children:(0,a.jsxs)(C.H3,{children:[0===e?"Create":"Edit"," Crew Duty"]})}),(0,a.jsxs)("div",{className:`${l?"grid-cols-2":"grid-cols-3"} grid gap-6`,children:[(0,a.jsxs)("div",{className:`${l?"hidden":""} my-4 `,children:["Crew duty details",a.jsx("p",{className:" mt-4 max-w-[25rem] leading-loose",children:"Lorem ipsum dolor sit amet consectetur adipisicing elit. Esse minima maxime enim, consectetur hic est perferendis explicabo suscipit rem reprehenderit vitae ex sunt corrupti obcaecati aliquid natus et inventore tenetur?"})]}),(0,a.jsxs)("div",{className:"w-full grid col-span-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(g.I,{name:"title",type:"text",required:!0,defaultValue:o?.title||"",onChange:B,placeholder:"Duty Title"}),a.jsx("small",{className:"text-destructive",children:N&&S.title})]}),(0,a.jsxs)("div",{children:[a.jsx(g.I,{name:"abbreviation",type:"text",placeholder:"Abbreviation",required:!0,defaultValue:o?.abbreviation||"",onChange:B}),a.jsx("small",{className:"text-destructive",children:N&&S.abbreviation})]})]})]}),l?(0,a.jsxs)("div",{className:"flex justify-end pt-8 gap-2.5",children:[a.jsx(D.Button,{variant:"back",onClick:t,children:"Cancel"}),0!==e&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(D.Button,{variant:"destructive",onClick:()=>{F(!0)},children:"Delete"}),a.jsx(b.AlertDialogNew,{openDialog:A,setOpenDialog:F,title:"Delete Crew Duty",description:`Are you sure you want to delete "${o.title}"?`,variant:"danger",actionText:"Delete",handleCreate:()=>q(o)})]}),a.jsx(D.Button,{onClick:U,disabled:P||V,children:`${0===e?"Create":"Update"} Duty`})]}):(0,a.jsxs)(h.V,{children:[a.jsx(f.default,{href:"/settings/crew-duty/list",children:a.jsx(D.Button,{variant:"back",iconLeft:j.Z,className:"mr-2",children:"Cancel"})}),0!==e&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(D.Button,{variant:"destructive",onClick:()=>{T(!0)},children:"Delete"}),a.jsx(b.AlertDialogNew,{openDialog:M,setOpenDialog:T,title:"Delete Crew Duty",description:`Are you sure you want to delete "${o.title}"?`,variant:"danger",actionText:"Delete",handleCreate:()=>q(o)})]}),a.jsx(D.Button,{iconLeft:y.Z,onClick:U,disabled:P||V,children:`${0===e?"Create":"Update"} Duty`})]})]})}},64939:(e,t,r)=>{"use strict";r.d(t,{Z:()=>c});var a=r(98768),i=r(60343),s=r(7678),l=r.n(s),n=r(79418),o=r(94060),u=r(46776),d=r(81524);let c=({value:e=[],onChange:t,allDepartments:r})=>{let[s,c]=(0,i.useState)([]),[m,p]=(0,i.useState)([]),[f,h]=(0,i.useState)(!1),[v,x]=(0,i.useState)(!1),[g]=(0,n.t)(o.jl,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneSeaLogsMember;t&&x(t.departments.nodes)},onError:e=>{console.error("querySeaLogsMembers error",e)}});(0,i.useEffect)(()=>{g({variables:{filter:{id:{eq:+(localStorage.getItem("userId")??0)}}}})},[]);let b=e=>{e&&c(e?.map(e=>({value:e.id,label:e.title})))};return(0,i.useEffect)(()=>{l()(s)||l()(e)||p(e)},[e,s]),(0,i.useEffect)(()=>{b(r)},[]),a.jsx(d.Combobox,{value:m?m.map(e=>s.find(t=>t.value===e)):[],multi:!0,options:(0,u.GJ)()?s:v?s.filter(e=>v.some(t=>t.id===e.value)):s,onChange:e=>{t(e),p(e)},placeholder:"Select Department",isLoading:!s})}},79823:(e,t,r)=>{"use strict";r.d(t,{default:()=>A});var a=r(98768),i=r(60343),s=r(79418),l=r(72548),n=r(71241),o=r.n(n),u=r(7678),d=r.n(u),c=r(14826),m=r.n(c),p=r(76342),f=r(69424),h=r(35024),v=r(13842),x=r(82102),g=r(64939),b=r(34376),j=r(46776),y=r(26100),C=r(94060),D=r(73366),w=r(71890),N=r(60797),E=r(81524),S=r(8943),I=r(39544),M=r(78965),T=r(25394);let A=({userId:e})=>{let[t,r]=(0,i.useState)(-1),n=(0,f.useRouter)(),{toast:u}=(0,b.pm)(),[c,A]=(0,i.useState)(),[F,L]=(0,i.useState)(!1),[B,U]=(0,i.useState)({firstName:"",email:"",response:""}),[k,P]=(0,i.useState)([]),[R,V]=(0,i.useState)([]),[q,$]=(0,i.useState)(),[Z,O]=(0,i.useState)(),[W,_]=(0,i.useState)(!1),[J,H]=(0,i.useState)([]),[G,Y]=(0,i.useState)(!1),[X,z]=(0,i.useState)(!1),[K,Q]=(0,i.useState)(!1),ee=new D.Z,[et,er]=(0,i.useState)(null),[ea,ei]=(0,i.useState)(!1),[es]=(0,s.t)(C.jl,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneSeaLogsMember;t&&Q(t.departments.nodes)},onError:e=>{console.error("querySeaLogsMembers error",e)}});(0,i.useEffect)(()=>{es({variables:{filter:{id:{eq:+(localStorage.getItem("userId")??0)}}}})},[]),(0,i.useEffect)(()=>{Y(j.Zu),r((0,j.GJ)())},[]),(0,v.oS)([e],e=>{let t=e[0];V(t?.vehicles.nodes.map(e=>({label:e.title,value:e.id}))??[]);let r=t.groups.nodes.map(e=>{if(e){let t=q.find(t=>t.value===e.id);return{label:t.label,value:t.value}}})[0];r&&P(r),A({...t,primaryDutyID:t.primaryDuty?.id}),H(t.departments.nodes.map(e=>e.id)),localStorage.getItem("userId")===t?.id&&z(!0)}),(0,v.sy)(e=>{O(e.filter(e=>!e.archived).map(e=>({label:e.title,value:e.id,vessel:{id:e.id,title:e.title,icon:e.icon,iconMode:e.iconMode,photoID:e.photoID}})))}),(0,v.E$)(_),(0,v.mJ)(e=>{$(e.filter(e=>(0,j.GJ)()||"admin"!==e.code).map(e=>({label:e.title,value:e.id})))});let el=o()((t,r)=>{A({...c,[t]:r,id:e})},300),en=e=>{let{name:t,value:r}=e.target;el(t,r)},[eo,{loading:eu}]=(0,l.D)(p.qK0,{onCompleted:e=>{e.createSeaLogsMember.id>0?n.push("/crew"):console.error("mutationCreateUser response error",e)},onError:e=>{U({...B,response:e.message}),L(!0),console.error("mutationCreateUser catch error",e.message)}}),[ed,{loading:ec}]=(0,l.D)(p.AXh,{onCompleted:t=>{t.updateSeaLogsMember.id>0?ee.delete(e).then(()=>{n.push("/crew")}):console.error("mutationUpdateUser error",t)},onError:e=>{console.error("mutationUpdateUser error",e.message),u({variant:"destructive",title:"Error",description:e.message})}}),em=async()=>{if(K&&"true"===localStorage.getItem("useDepartment")&&0===J.length){u({variant:"destructive",title:"Validation Error",description:"Please select a department"});return}if(void 0===c||c?.groups===void 0||0===k.length){u({variant:"destructive",title:"Validation Error",description:"Please select a role"});return}let t=!1,r={firstName:"",email:"",response:""};if(d()(m()(c.firstName))&&(t=!0,r.firstName="First name is required"),d()(m()(c.email))&&(t=!0,r.email="Email is required"),t){L(!0),U(r);return}let a={input:{id:+c.id,firstName:c.firstName,surname:c.surname,username:c.username,password:c.password,email:c.email,isPilot:c.isPilot,isTransferee:c.isTransferee,phoneNumber:c.phoneNumber,...!d()(c.groups)&&{groups:c.groups.nodes.map(e=>e.id).join(",")},...!d()(R)&&{vehicles:R.map(e=>e.value).join(",")},primaryDutyID:+c.primaryDutyID,departments:J.join(",")}};0===e?await eo({variables:a}):await ed({variables:a})},ep=async e=>{if(e&&e.id>0){let t={input:{id:e.id,isArchived:!e.isArchived}};await ed({variables:t})}};return G&&(0,j.Fs)("VIEW_MEMBER",G)?(0,a.jsxs)(a.Fragment,{children:[a.jsx(T.Bu,{title:`${0===e?"Create":"Edit"} User`}),(0,a.jsxs)(h.Zb,{className:"mb-2.5",children:[(0,a.jsxs)(h.Ol,{children:[a.jsx(T.H4,{children:"User Details"}),a.jsx(T.P,{children:"Personal information and account settings for this user."}),F&&B.response&&a.jsx(S.bZ,{variant:"destructive",className:"mb-4",children:a.jsx(S.X,{children:B.response})})]}),(0,a.jsxs)(h.aY,{className:"p-0 space-y-5",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(N.Label,{htmlFor:"firstName",label:"First name",children:a.jsx(w.I,{id:"firstName",name:"firstName",placeholder:"First name",type:"text",required:!0,defaultValue:c?.firstName||"",onChange:en,className:"w-full"})}),F&&B.firstName&&a.jsx("p",{className:"text-sm text-destructive",children:B.firstName})]}),a.jsx(N.Label,{htmlFor:"surname",label:"Surname",children:a.jsx(w.I,{id:"surname",name:"surname",placeholder:"Surname",type:"text",defaultValue:c?.surname||"",onChange:en,className:"w-full"})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsx(N.Label,{htmlFor:"username",label:"Username",children:a.jsx(w.I,{id:"username",name:"username",placeholder:"Username",type:"text",defaultValue:c?.username||"",onChange:en,className:"w-full"})}),(G&&(0,j.Fs)("ADMIN",G)||X)&&a.jsx(N.Label,{htmlFor:"password",label:"Password",children:a.jsx(w.I,{id:"password",name:"password",placeholder:"Password",type:"password",required:0===e,onChange:en,autoComplete:"new-password",className:"w-full"})})]}),(G&&(0,j.Fs)(process.env.VIEW_MEMBER_CONTACT||"VIEW_MEMBER_CONTACT",G)||X)&&(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx(N.Label,{htmlFor:"email",label:"Email",children:a.jsx(w.I,{id:"email",name:"email",type:"email",placeholder:"Email",defaultValue:c?.email||"",onChange:en,className:"w-full"})}),F&&B.email&&a.jsx("p",{className:"text-sm text-destructive",children:B.email})]}),a.jsx("div",{children:a.jsx(N.Label,{htmlFor:"phoneNumber",label:"Phone Number",children:a.jsx(w.I,{id:"phoneNumber",name:"phoneNumber",placeholder:"Phone Number",type:"tel",defaultValue:c?.phoneNumber||"",onChange:en,className:"w-full"})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.jsx(N.Label,{htmlFor:"primaryDuty",label:"Primary Duty",children:a.jsx(x.Z,{onChange:e=>{A({...c,primaryDutyID:+e.value})},crewDutyID:c?.primaryDuty?.id||0})}),a.jsx(N.Label,{htmlFor:"userRole",label:"User Role",children:q&&a.jsx(E.Combobox,{placeholder:"Select roles",onChange:e=>{let t=[e];P(e),A({...c,groups:{nodes:t.map(e=>({id:e.value}))}})},value:k,options:q})}),"true"===localStorage.getItem("useDepartment")&&(0,a.jsxs)("div",{children:[a.jsx(N.Label,{htmlFor:"departments",label:"Departments",children:W&&a.jsx(g.Z,{onChange:e=>{H(e.map(e=>e.value))},value:J,allDepartments:W})}),a.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"The assigned departments will be cleared if this user is assigned the Administrator role."})]})]}),a.jsx(N.Label,{htmlFor:"vessels",label:"Vessels",children:Z&&a.jsx(E.Combobox,{multi:!0,onChange:e=>{let t=e.map(e=>e.value);V(e),A({...c,vehicles:t})},placeholder:"Select vessels",options:"true"===localStorage.getItem("useDepartment")?W&&W?.filter(t=>t.seaLogsMembers.nodes?.find(t=>t.id==e)||J.includes(t.id))?.map(e=>e.basicComponents.nodes).flat()?.map(e=>Z.find(t=>t.value===e.id))?.filter(Boolean):Z,value:R})})]})]}),a.jsx(T.h9,{openDialog:ea,setOpenDialog:ei,actionText:c?.isArchived?"Retrieve":"Archive",cancelText:"Cancel",handleCreate:()=>ep(c),title:`${c?.isArchived?"Retrieve":"Archive"} User`,children:a.jsx("div",{className:"flex justify-center flex-col px-6 py-6",children:G&&(0,j.Fs)(process.env.DELETE_MEMBER||"DELETE_MEMBER",G)?(0,a.jsxs)("div",{children:["Are you sure you want to"," ",c?.isArchived?"retrieve":"archive"," ",`${c?.firstName||"this user"} ${c?.surname||""}`,"?"]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(T.H3,{children:"Warning"}),a.jsx("p",{className:"mt-3 text-slate-500",children:"You do not have permission to archive user."}),a.jsx("hr",{className:"my-6"}),a.jsx("div",{className:"flex justify-end",children:a.jsx(I.Button,{className:"mr-8",onClick:()=>ei(!1),children:"Cancel"})})]})})}),(0,a.jsxs)(M.V,{className:"flex justify-end gap-2 p-4",children:[a.jsx(I.Button,{variant:"back",onClick:()=>n.push("/crew"),children:"Back to Crew"}),G&&(0,j.Fs)(process.env.EDIT_MEMBER||"EDIT_MEMBER",G)&&a.jsx(I.Button,{variant:"destructive",onClick:()=>{ei(!0)},children:c?.isArchived?"Retrieve":"Archive"}),a.jsx(I.Button,{onClick:em,disabled:eu||ec,children:0===e?"Create user":"Update user"})]})]}):G?a.jsx(y.Z,{errorMessage:"Oops You do not have the permission to view this section."}):a.jsx(y.Z,{})}},83495:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var a=r(98768);r(60343);var i=r(64837);function s({children:e}){return a.jsx(i.Z,{children:e})}},82102:(e,t,r)=>{"use strict";r.d(t,{Z:()=>c});var a=r(98768),i=r(94060),s=r(79418),l=r(60343),n=r(59689),o=r(67468),u=r(57103),d=r(81524);let c=({crewDutyID:e=0,onChange:t,label:r,offline:c=!1,placeholder:m="Duty",multi:p=!1,modal:f,hideCreateOption:h=!1})=>{let v=new n.Z,[x,g]=(0,l.useState)(!0),[b,j]=(0,l.useState)([]),[y,C]=(0,l.useState)([]),[D,w]=(0,l.useState)(!1),[N]=(0,s.t)(i.HW,{fetchPolicy:"cache-and-network",onCompleted:t=>{let r=t.readCrewDuties.nodes;if(r){let t=r.filter(e=>!e.archived),a=t;if(h||(a=[{id:0,title:"-- Create New Duty --",archived:!1,abbreviation:"NEW"},...t]),j(a),e>0){let r=t.find(t=>t.id===e);C({label:r.title,value:r.id})}}},onError:e=>{console.error("queryDutiesEntry error",e)}}),E=async()=>{if(c){let t=await v.getAll();if(t){let r=t.filter(e=>!e.archived),a=r;if(h||(a=[{id:0,title:"-- Create New Duty --",archived:!1,abbreviation:"NEW"},...r]),j(a),e>0){let t=r.find(t=>t.id===e);C({label:t.title,value:t.id})}}}else await N()};return(0,l.useEffect)(()=>{x&&(E(),g(!1))},[x]),(0,l.useEffect)(()=>{if(e>0&&b.length>0){let t=b.find(t=>t.id==e);t&&C({label:t.title,value:t.id})}else 0===e&&C(null)},[e,b]),(0,a.jsxs)(a.Fragment,{children:[a.jsx(d.Combobox,{modal:f,options:b.map(e=>({label:`${e.title}`,value:e.id})),label:r,multi:p,value:y,onChange:e=>{if(e&&0===e.value){w(!0);return}C(e),t(e)},placeholder:m}),a.jsx(u.AlertDialogNew,{openDialog:D,setOpenDialog:w,size:"md",noButton:!0,noFooter:!0,className:"space-y-0",children:a.jsx(o.default,{crewDutyId:0,onCancel:()=>w(!1),onCreate:e=>{let r={label:e.title,value:e.id};C(r),t(r),E(),w(!1)},isPopup:!0})})]})}},12191:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\user\layout.tsx#default`)}};