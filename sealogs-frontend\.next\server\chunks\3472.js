exports.id=3472,exports.ids=[3472],exports.modules={65653:(e,t,r)=>{Promise.resolve().then(r.bind(r,89388))},83179:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",r="minute",a="hour",s="week",n="month",i="quarter",l="year",o="date",c="Invalid Date",d=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,u=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,h=function(e,t,r){var a=String(e);return!a||a.length>=t?e:""+Array(t+1-a.length).join(r)+e},m="en",p={};p[m]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||"th")+"]"}};var f="$isDayjsObject",x=function(e){return e instanceof j||!(!e||!e[f])},v=function e(t,r,a){var s;if(!t)return m;if("string"==typeof t){var n=t.toLowerCase();p[n]&&(s=n),r&&(p[n]=r,s=n);var i=t.split("-");if(!s&&i.length>1)return e(i[0])}else{var l=t.name;p[l]=t,s=l}return!a&&s&&(m=s),s||!a&&m},g=function(e,t){if(x(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new j(r)},y={s:h,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+h(Math.floor(r/60),2,"0")+":"+h(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var a=12*(r.year()-t.year())+(r.month()-t.month()),s=t.clone().add(a,n),i=r-s<0,l=t.clone().add(a+(i?-1:1),n);return+(-(a+(r-s)/(i?s-l:l-s))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(c){return({M:n,y:l,w:s,d:"day",D:o,h:a,m:r,s:t,ms:e,Q:i})[c]||String(c||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};y.l=v,y.i=x,y.w=function(e,t){return g(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var j=function(){function h(e){this.$L=v(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[f]=!0}var m=h.prototype;return m.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(y.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var a=t.match(d);if(a){var s=a[2]-1||0,n=(a[7]||"0").substring(0,3);return r?new Date(Date.UTC(a[1],s,a[3]||1,a[4]||0,a[5]||0,a[6]||0,n)):new Date(a[1],s,a[3]||1,a[4]||0,a[5]||0,a[6]||0,n)}}return new Date(t)}(e),this.init()},m.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},m.$utils=function(){return y},m.isValid=function(){return this.$d.toString()!==c},m.isSame=function(e,t){var r=g(e);return this.startOf(t)<=r&&r<=this.endOf(t)},m.isAfter=function(e,t){return g(e)<this.startOf(t)},m.isBefore=function(e,t){return this.endOf(t)<g(e)},m.$g=function(e,t,r){return y.u(e)?this[t]:this.set(r,e)},m.unix=function(){return Math.floor(this.valueOf()/1e3)},m.valueOf=function(){return this.$d.getTime()},m.startOf=function(e,i){var c=this,d=!!y.u(i)||i,u=y.p(e),h=function(e,t){var r=y.w(c.$u?Date.UTC(c.$y,t,e):new Date(c.$y,t,e),c);return d?r:r.endOf("day")},m=function(e,t){return y.w(c.toDate()[e].apply(c.toDate("s"),(d?[0,0,0,0]:[23,59,59,999]).slice(t)),c)},p=this.$W,f=this.$M,x=this.$D,v="set"+(this.$u?"UTC":"");switch(u){case l:return d?h(1,0):h(31,11);case n:return d?h(1,f):h(0,f+1);case s:var g=this.$locale().weekStart||0,j=(p<g?p+7:p)-g;return h(d?x-j:x+(6-j),f);case"day":case o:return m(v+"Hours",0);case a:return m(v+"Minutes",1);case r:return m(v+"Seconds",2);case t:return m(v+"Milliseconds",3);default:return this.clone()}},m.endOf=function(e){return this.startOf(e,!1)},m.$set=function(s,i){var c,d=y.p(s),u="set"+(this.$u?"UTC":""),h=((c={}).day=u+"Date",c[o]=u+"Date",c[n]=u+"Month",c[l]=u+"FullYear",c[a]=u+"Hours",c[r]=u+"Minutes",c[t]=u+"Seconds",c[e]=u+"Milliseconds",c)[d],m="day"===d?this.$D+(i-this.$W):i;if(d===n||d===l){var p=this.clone().set(o,1);p.$d[h](m),p.init(),this.$d=p.set(o,Math.min(this.$D,p.daysInMonth())).$d}else h&&this.$d[h](m);return this.init(),this},m.set=function(e,t){return this.clone().$set(e,t)},m.get=function(e){return this[y.p(e)]()},m.add=function(e,i){var o,c=this;e=Number(e);var d=y.p(i),u=function(t){var r=g(c);return y.w(r.date(r.date()+Math.round(t*e)),c)};if(d===n)return this.set(n,this.$M+e);if(d===l)return this.set(l,this.$y+e);if("day"===d)return u(1);if(d===s)return u(7);var h=((o={})[r]=6e4,o[a]=36e5,o[t]=1e3,o)[d]||1,m=this.$d.getTime()+e*h;return y.w(m,this)},m.subtract=function(e,t){return this.add(-1*e,t)},m.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||c;var a=e||"YYYY-MM-DDTHH:mm:ssZ",s=y.z(this),n=this.$H,i=this.$m,l=this.$M,o=r.weekdays,d=r.months,h=r.meridiem,m=function(e,r,s,n){return e&&(e[r]||e(t,a))||s[r].slice(0,n)},p=function(e){return y.s(n%12||12,e,"0")},f=h||function(e,t,r){var a=e<12?"AM":"PM";return r?a.toLowerCase():a};return a.replace(u,function(e,a){return a||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return y.s(t.$y,4,"0");case"M":return l+1;case"MM":return y.s(l+1,2,"0");case"MMM":return m(r.monthsShort,l,d,3);case"MMMM":return m(d,l);case"D":return t.$D;case"DD":return y.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return m(r.weekdaysMin,t.$W,o,2);case"ddd":return m(r.weekdaysShort,t.$W,o,3);case"dddd":return o[t.$W];case"H":return String(n);case"HH":return y.s(n,2,"0");case"h":return p(1);case"hh":return p(2);case"a":return f(n,i,!0);case"A":return f(n,i,!1);case"m":return String(i);case"mm":return y.s(i,2,"0");case"s":return String(t.$s);case"ss":return y.s(t.$s,2,"0");case"SSS":return y.s(t.$ms,3,"0");case"Z":return s}return null}(e)||s.replace(":","")})},m.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},m.diff=function(e,o,c){var d,u=this,h=y.p(o),m=g(e),p=(m.utcOffset()-this.utcOffset())*6e4,f=this-m,x=function(){return y.m(u,m)};switch(h){case l:d=x()/12;break;case n:d=x();break;case i:d=x()/3;break;case s:d=(f-p)/6048e5;break;case"day":d=(f-p)/864e5;break;case a:d=f/36e5;break;case r:d=f/6e4;break;case t:d=f/1e3;break;default:d=f}return c?d:y.a(d)},m.daysInMonth=function(){return this.endOf(n).$D},m.$locale=function(){return p[this.$L]},m.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),a=v(e,t,!0);return a&&(r.$L=a),r},m.clone=function(){return y.w(this.$d,this)},m.toDate=function(){return new Date(this.valueOf())},m.toJSON=function(){return this.isValid()?this.toISOString():null},m.toISOString=function(){return this.$d.toISOString()},m.toString=function(){return this.$d.toUTCString()},h}(),b=j.prototype;return g.prototype=b,[["$ms",e],["$s",t],["$m",r],["$H",a],["$W","day"],["$M",n],["$y",l],["$D",o]].forEach(function(e){b[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),g.extend=function(e,t){return e.$i||(e(t,j,g),e.$i=!0),g},g.locale=v,g.isDayjs=x,g.unix=function(e){return g(1e3*e)},g.en=p[m],g.Ls=p,g.p={},g},e.exports=t()},18479:e=>{e.exports=function(e,t){for(var r=-1,a=null==e?0:e.length,s=Array(a);++r<a;)s[r]=t(e[r],r,e);return s}},22060:(e,t,r)=>{var a=r(51858),s=r(18479),n=r(55813),i=r(15903),l=1/0,o=a?a.prototype:void 0,c=o?o.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(n(t))return s(t,e)+"";if(i(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-l?"-0":r}},15903:(e,t,r)=>{var a=r(55296),s=r(48377);e.exports=function(e){return"symbol"==typeof e||s(e)&&"[object Symbol]"==a(e)}},16266:(e,t,r)=>{var a=r(22060);e.exports=function(e){return null==e?"":a(e)}},3233:(e,t,r)=>{var a=r(16266),s=0;e.exports=function(e){var t=++s;return a(e)+t}},89388:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(98768);r(60343);var s=r(64837);function n({children:e}){return a.jsx(s.Z,{children:e})}},15924:(e,t,r)=>{"use strict";r.d(t,{Z:()=>O});var a=r(98768),s=r(66263),n=r(60343),i=r(34376),l=r(24894),o=r(79418),c=r(72548),d=r(45519);let u=(0,d.ZP)`
    query readOtherCompanies(
        $limit: Int = 500
        $offset: Int = 0
        $filter: OtherCompanyFilterFields = {}
    ){
        readOtherCompanies(limit: $limit, offset: $offset, filter: $filter){
            nodes{
                id
                title
                phone
                email
                address
                email
            }
        }
    }
`;var h=r(17380),m=r(76342),p=r(25394),f=r(71890),x=r(52269);function v({isOpen:e,setIsOpen:t,onCreateSuccess:r}){let s=(0,n.useRef)(null),[i,l]=(0,n.useState)({title:"",phone:"",email:"",address:""}),[o]=(0,c.D)(m.a1J,{onCompleted:e=>{},onError:e=>{console.error("mutationCreateSupplier error",e)}}),d=async e=>{e.preventDefault();let{data:a}=await o({variables:{input:i}});a.createOtherCompany.id&&(r(a.createOtherCompany),t(!1))};return(0,a.jsxs)(p.h9,{openDialog:e,setOpenDialog:e=>t(e),handleCreate:()=>s?.current?.requestSubmit(),actionText:"Create Company",className:"lg:max-w-lg",children:[a.jsx(p.H3,{children:"Create New Company"}),a.jsx("form",{ref:s,onSubmit:d,children:(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("div",{className:"mb-4",children:a.jsx(f.I,{type:"text",placeholder:"Company Name",value:i.title,required:!0,onChange:e=>l(t=>({...t,title:e.target.value}))})}),a.jsx("div",{className:"mb-4",children:a.jsx(f.I,{type:"text",placeholder:"Phone",value:i.phone,onChange:e=>l(t=>({...t,phone:e.target.value}))})}),a.jsx("div",{className:"mb-4",children:a.jsx(f.I,{type:"email",placeholder:"Email",value:i.email,onChange:e=>l(t=>({...t,email:e.target.value}))})}),a.jsx("div",{children:a.jsx(x.Textarea,{rows:3,placeholder:"Address",value:i.address,onChange:e=>l(t=>({...t,address:e.target.value}))})})]})})]})}var g=r(94060),y=r(43926),j=r(8087),b=r(81311),$=r(69424),C=r(75535);function N({id:e,fullName:t}){let r=(0,$.useRouter)(),[s,l]=(0,n.useState)(!1),[o]=(0,c.D)(m.pip,{onCompleted:e=>{e.deleteKeyContacts&&e.deleteKeyContacts.length>0?r.push("/key-contacts"):(0,i.Am)({description:"Error deleting key contact",variant:"destructive"})},onError:e=>{console.error("mutationDeleteKeyContacts error:",e.message)}});return(0,a.jsxs)(a.Fragment,{children:[a.jsx(p.zx,{iconLeft:C.Z,variant:"destructive",color:"rose",onClick:()=>l(!0),children:"Delete"}),(0,a.jsxs)(p.h9,{openDialog:s,setOpenDialog:l,handleCreate:()=>{o({variables:{ids:[e]}})},variant:"danger",actionText:"Delete Key Contact",children:[a.jsx(p.H3,{className:"text-xl",children:"Delete Key Contact"}),(0,a.jsxs)("div",{className:"my-4 flex items-center",children:["Are you sure you want to delete ",t,"?"]})]})]})}r(46776);var D=r(60797),w=r(81524),S=r(39544),k=r(78965);function O({id:e}){let[t,r]=(0,n.useState)({EDIT_KEY_CONTACT:void 0,DELETE_KEY_CONTACT:void 0}),d=(0,n.useRef)(null),C=(0,$.useRouter)(),{toast:O}=(0,i.pm)(),[M,T]=(0,n.useState)({firstName:"",surname:"",phone:"",cellPhone:"",email:"",vhfChannel:"",address:""}),[E,Z]=(0,n.useState)([]),F=async e=>{Z(t=>t.filter(t=>t.id!==e))},L=(e,t)=>{T(r=>({...r,[e]:t}))},[_,I]=(0,n.useState)(!1),[P,A]=(0,n.useState)(null),[H,Y]=(0,n.useState)([]),[U,{called:B,loading:K}]=(0,o.t)(u,{fetchPolicy:"cache-and-network",onCompleted:e=>{Y([{value:"newOtherCompany",label:" ---- Create Company ---- "},...e.readOtherCompanies.nodes.map(e=>({value:e.id,label:e.title}))])},onError:e=>{console.error("queryGetCompanies error",e)}}),V=e=>{if("newOtherCompany"===e.value){I(!0);return}A(e)},[W]=(0,o.t)(g.Lw,{fetchPolicy:"cache-and-network",onError:e=>{console.error("getKeyContactByID error",e)}}),[z,{called:R,loading:q}]=(0,c.D)(e?m.iW9:m.R4e,{onCompleted:()=>{C.push("/key-contacts")},onError:e=>{console.error("mutationcreateSupplier error",e)}}),J=async r=>{if(r.preventDefault(),!1===t.EDIT_KEY_CONTACT){O({variant:"destructive",title:"Permission Error",description:"You don't have permission to create or update key contact!"});return}z({variables:{input:{id:e||"",...M,vhfChannel:M.vhfChannel?parseInt(`${M.vhfChannel??"0"}`):null,companyID:P?.value??"",attachments:E.map(e=>e.id).join(",")}}})};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(p.Zb,{children:[a.jsx(p.Ol,{children:a.jsx(p.ll,{children:a.jsx(p.H3,{children:e?"Edit Key Contact":"Add Key Contact"})})}),(0,a.jsxs)(p.aY,{children:[a.jsx("form",{onSubmit:J,ref:d,children:(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 pb-4 pt-3 px-4",children:[a.jsx(p.H3,{className:"text-lg",children:"Key Contact Details"}),(0,a.jsxs)("div",{className:"md:col-span-2 flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(D.Label,{htmlFor:"firstName",children:"First name"}),a.jsx(f.I,{id:"firstName",type:"text",placeholder:"First name",name:"firstName",value:M.firstName,required:!0,onChange:e=>L(e.target.name,e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(D.Label,{htmlFor:"surname",children:"Surname"}),a.jsx(f.I,{id:"surname",type:"text",placeholder:"Surname",name:"surname",value:M.surname,onChange:e=>L(e.target.name,e.target.value)})]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(D.Label,{htmlFor:"phone",children:"Phone"}),a.jsx(f.I,{id:"phone",type:"text",placeholder:"Phone",name:"phone",value:M.phone,onChange:e=>L(e.target.name,e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(D.Label,{htmlFor:"cellPhone",children:"Cell Phone"}),a.jsx(f.I,{id:"cellPhone",type:"text",placeholder:"Cell Phone",name:"cellPhone",value:M.cellPhone,onChange:e=>L(e.target.name,e.target.value)})]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(D.Label,{htmlFor:"email",children:"Email"}),a.jsx(f.I,{id:"email",type:"email",placeholder:"Email",name:"email",value:M.email,onChange:e=>L(e.target.name,e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(D.Label,{htmlFor:"vhfChannel",children:"VHF Channel"}),a.jsx(f.I,{id:"vhfChannel",type:"number",placeholder:"VHF Channel",name:"vhfChannel",value:M.vhfChannel,onChange:e=>L(e.target.name,e.target.value)})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(D.Label,{htmlFor:"address",children:"Address"}),a.jsx(x.Textarea,{id:"address",name:"address",value:M.address,onChange:e=>L(e.target.name,e.target.value),placeholder:"Address",rows:3})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(D.Label,{htmlFor:"company",children:"Company"}),B&&K?a.jsx(h.U3,{}):a.jsx(w.Combobox,{className:"w-full",id:"company",options:H.map(e=>({label:e.label,value:e.value})),value:P?{label:P.label,value:P.value}:void 0,onChange:e=>{e&&V({value:e.value,label:e.label})},placeholder:"Select Company"})]})]})]})}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 pb-4 pt-3 px-4",children:[a.jsx(p.H3,{className:"text-lg",children:"Attachment"}),(0,a.jsxs)("div",{className:"col-span-2",children:[a.jsx("div",{className:"my-4",children:a.jsx("div",{className:"grid-cols-1 md:col-span-2 lg:col-span-3",children:a.jsx(l.Z,{setDocuments:Z,text:"",subText:"Drag files here or upload",bgClass:"bg-slblue-50",documents:E})})}),a.jsx("div",{className:"block",children:E.length>0&&E.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-8 justify-between p-2.5 bg-slblue-50 rounded-lg border border-slblue-300   mb-4 hover:bg-slblue-1000 hover-text-white",children:[a.jsx(y.Z,{document:e}),a.jsx(S.Button,{variant:"text",size:"sm",onClick:()=>F(e.id),className:"text-destructive hover:text-destructive/80",iconLeft:j.Z,iconOnly:!0})]},e.id))})]})]})]})]}),(0,a.jsxs)(k.V,{className:"flex gap-2.5",children:[a.jsx(s.default,{href:"/key-contacts",children:a.jsx(S.Button,{variant:"back",children:"Cancel"})}),e&&!0===t.DELETE_KEY_CONTACT&&a.jsx(N,{id:e,fullName:`${M.firstName} ${M.surname}`}),a.jsx(S.Button,{disabled:!1===t.EDIT_KEY_CONTACT||R&&q,onClick:()=>d.current?.requestSubmit(),iconLeft:b.Z,children:"Save"})]}),a.jsx(v,{isOpen:_,setIsOpen:I,onCreateSuccess:e=>{let t={value:e.id,label:e.title};Y(e=>[...e,t]),A(t)}})]})}},43926:(e,t,r)=>{"use strict";r.d(t,{Z:()=>v});var a=r(98768),s=r(66263),n=r(60343),i=r(28147),l=r(12513),o=r(88557),c=r(27780),d=r(50526),u=r(7179);r(18937),r(73304);var h=r(39544),m=r(46877),p=r(13609),f=r(47634),x=r(34376);let v=({document:e,hideTitle:t=!1,showDeleteButton:r=!1,onDelete:v,canDelete:g=!0,deleteErrorMessage:y="You do not have permission to delete this document"})=>{let[j,b]=(0,n.useState)(!1),[$,C]=(0,n.useState)(!1),{toast:N}=(0,x.pm)(),D=()=>{if(!g){N({description:y,variant:"destructive"});return}v&&v(e.id)},w=()=>{let t=process.env.NEXT_PUBLIC_FILE_BASE_URL||"https://api.sealogs.com/assets/";return`${t}${e.fileFilename}`};return["jpg","jpeg","png","gif","webp","svg"].includes(e.fileFilename?.split(".").pop()?.toLowerCase()||"")&&!$?(0,a.jsxs)("div",{className:"group relative flex items-center gap-3",children:[a.jsx(h.Button,{variant:"ghost",onClick:()=>b(!0),className:"h-auto p-2 hover:bg-muted/50 transition-colors flex-1","aria-label":`View image: ${e.title}`,children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"relative overflow-hidden rounded-lg border border-border bg-muted/20",children:[a.jsx(i.default,{src:w()||"/placeholder.svg",alt:e.title,width:64,height:64,className:"h-16 w-16 object-cover transition-transform group-hover:scale-105",onError:()=>C(!0)}),a.jsx("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors flex items-center justify-center",children:a.jsx(m.Z,{className:"h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity"})})]}),!t&&(0,a.jsxs)("div",{className:"flex-1 text-left",children:[a.jsx("p",{className:"font-medium text-sm truncate max-w-[200px]",children:e.title}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Image •"," ",e.fileFilename?.split(".").pop()?.toUpperCase()]})]})]})}),r&&a.jsx(h.Button,{variant:"destructive",iconLeft:p.Z,iconOnly:!0,onClick:D,"aria-label":`Delete ${e.title}`}),a.jsx(l.ZP,{open:j,close:()=>b(!1),slides:[{src:w(),alt:e.title,description:e.title}],render:{buttonPrev:()=>null,buttonNext:()=>null},controller:{closeOnPullUp:!0,closeOnPullDown:!0,closeOnBackdropClick:!0},plugins:[o.Z,u.Z,d.Z,c.Z]})]}):(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx(s.default,{href:w(),target:"_blank",rel:"noopener noreferrer",className:"group block flex-1",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"h-16 w-16 rounded-lg border border-border border-dashed bg-muted/20 flex items-center justify-center group-hover:bg-muted/30 transition-colors",children:$?a.jsx(m.Z,{className:"h-6 w-6 text-muted-foreground"}):a.jsx(i.default,{src:{pdf:"/file-types/pdf.svg",xls:"/file-types/xls.svg",xlsx:"/file-types/xls.svg",ppt:"/file-types/ppt.svg",pptx:"/file-types/ppt.svg",txt:"/file-types/txt.svg",csv:"/file-types/csv.svg"}[e.fileFilename?.split(".").pop()?.toLowerCase()||""]||"/file-types/doc.svg",alt:`${e.fileFilename?.split(".").pop()?.toUpperCase()} file`,width:24,height:24,className:"h-6 w-6"})}),a.jsx("div",{className:"absolute -top-1 -right-1 bg-background border border-border rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity",children:a.jsx(f.Z,{className:"h-3 w-3 text-muted-foreground"})})]}),!t&&(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx("p",{className:"font-medium text-sm truncate",children:e.title}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Document •"," ",e.fileFilename?.split(".").pop()?.toUpperCase()]})]})]})}),r&&a.jsx(h.Button,{variant:"destructive",iconLeft:p.Z,iconOnly:!0,onClick:D,"aria-label":`Delete ${e.title}`})]})}},24894:(e,t,r)=>{"use strict";r.d(t,{Z:()=>f});var a=r(98768),s=r(60343),n=r(28147),i=r(79418),l=r(72548),o=r(67537),c=r(94060),d=r(76342),u=r(56937),h=r(71890),m=r(60797),p=r(25394);function f({setDocuments:e,text:t="Documents and Images",subText:r,bgClass:f="",documents:x,multipleUpload:v=!0}){let[g,y]=(0,s.useState)(!1),[j,b]=(0,s.useState)([]),[$,C]=(0,s.useState)(!1),[N,D]=(0,s.useState)(!1),w=(0,s.useRef)(null),S=(0,u.cn)("relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none",g?"bg-accent border-primary":"bg-accent/50 border-border","text-foreground hover:bg-accent hover:border-primary","min-h-[10rem] cursor-pointer select-none",f),k=async e=>{let t=new FormData;t.append("FileData",e,e.name.replace(/\s/g,""));try{let e=await fetch("https://api.sealogs.com/api/v2/upload",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("sl-jwt")}`},body:t}),r=await e.json();await O({variables:{id:[r[0].id]}}),D(!1)}catch(e){console.error(e)}},[O]=(0,i.t)(c.ZX,{fetchPolicy:"cache-and-network",onCompleted:e=>{b(t=>[...t,e.readFiles.nodes[0]]),C(!0)},onError:e=>console.error(e)}),[M]=(0,l.D)(d.RgS,{onCompleted:t=>{let r=t.updateFile;e(e=>v?[...e,r]:[r])},onError:e=>console.error(e)}),T=e=>{let t=Array.from(e);D(!0),t.forEach(k)},E=e=>t=>{t.preventDefault(),y(e)};return(0,a.jsxs)("div",{className:"w-full pt-4 lg:pt-0",children:[(0,a.jsxs)("form",{className:S,onSubmit:e=>e.preventDefault(),onDragEnter:E(!0),onDragOver:E(!0),onDragLeave:E(!1),onDrop:e=>{e.preventDefault(),y(!1),e.dataTransfer.files&&T(e.dataTransfer.files)},onClick:()=>w.current?.click(),"aria-label":"File uploader drop zone",children:[a.jsx("span",{className:"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider",children:t}),a.jsx(h.I,{ref:w,type:"file",className:"hidden",multiple:v,accept:".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf",onChange:e=>{e.target.files&&T(e.target.files)}}),(0,a.jsxs)("div",{className:"flex flex-col items-center gap-2 pointer-events-none",children:[a.jsx(n.default,{src:"/sealogs-document_upload.svg",alt:"Upload illustration",width:96,height:96,className:"relative -translate-x-2.5",priority:!0}),r&&a.jsx("span",{className:"text-sm font-medium text-neutral-400",children:r})]})]}),N?(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2",children:[a.jsx(o.Z,{className:"h-5 w-5 animate-spin text-primary"}),a.jsx("span",{className:"text-sm text-muted-foreground",children:"Uploading..."})]}):a.jsx(p.h9,{openDialog:$,setOpenDialog:C,handleCreate:()=>{j.forEach((e,t)=>{let r=document.getElementById(`file-name-${t}`).value;M({variables:{input:{id:e.id,title:r}}})}),C(!1)},actionText:"Save",title:"File Name",children:a.jsx("div",{className:"space-y-4",children:j.map((e,t)=>a.jsx(m.Label,{label:`File ${t+1} Name`,htmlFor:`file-name-${t}`,children:a.jsx(h.I,{id:`file-name-${t}`,defaultValue:e.title,placeholder:"Enter file name"})},e.id))})})]})}},72487:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\key-contacts\layout.tsx#default`)},84961:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(97428).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},46020:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(97428).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},23379:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(97428).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},7671:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(97428).Z)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},75535:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(97428).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};