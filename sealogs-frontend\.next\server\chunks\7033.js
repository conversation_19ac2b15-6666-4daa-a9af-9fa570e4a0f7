"use strict";exports.id=7033,exports.ids=[7033],exports.modules={75546:(e,t,o)=>{o.d(t,{Br:()=>p,fU:()=>u,o0:()=>d,p6:()=>m,vq:()=>f});var s=o(83179),n=o.n(s),l=o(7678),r=o.n(l),a=o(14826),i=o.n(a);let m=(e="",t=!0)=>{let o;if(r()(i()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[o,s,n]=e.split("-"),l=t?o.slice(-2):o,r=parseInt(n,10).toString().padStart(2,"0"),a=parseInt(s,10).toString().padStart(2,"0");return`${r}/${a}/${l}`}if(!(o=e&&"object"==typeof e?n()(e.toString()):n()(e)).isValid())return"";let s=o.format("DD"),l=o.format("MM"),a=t?o.format("YY"):o.format("YYYY");return`${s}/${l}/${a}`},d=(e="",t=!0)=>{let o;if(r()(i()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[o,s,n]=e.split("-"),l=t?o.slice(-2):o,r=parseInt(n,10).toString().padStart(2,"0"),a=parseInt(s,10).toString().padStart(2,"0");return`${r}/${a}/${l} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[o,s]=e.split(" "),[n,l,r]=o.split("-"),a=t?n.slice(-2):n,i=s.split(":"),m=i[0].padStart(2,"0"),d=i[1].padStart(2,"0"),f=parseInt(r,10).toString().padStart(2,"0"),p=parseInt(l,10).toString().padStart(2,"0");return`${f}/${p}/${a} ${m}:${d}`}if(!(o=e&&"object"==typeof e?n()(e.toString()):n()(e)).isValid())return"";let s=o.format("DD"),l=o.format("MM"),a=t?o.format("YY"):o.format("YYYY"),m=o.format("HH:mm");return`${s}/${l}/${a} ${m}`},f=(e="")=>r()(i()(e))?"":n()(e).format("YYYY-MM-DD HH:mm:ss"),p=(e="")=>r()(i()(e))?new Date:new Date(`${e}T10:00:00Z`),u=(e,t)=>{let o=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),s=e=>e.includes(" ")?e.replace(" ","T"):e,n=e=>{if(!e||"string"!=typeof e)return null;if(o(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(s(e))},l=n(e),r=n(t);return!l||!r||isNaN(l.getTime())||isNaN(r.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):r>l}},56950:(e,t,o)=>{o.d(t,{Ip:()=>r,BI:()=>l,w_:()=>a,Pr:()=>i,N3:()=>m,zY:()=>f,g:()=>d,L9:()=>u}),o(98768);var s=o(83962),n=o(93956);o(39544),o(74602),o(25394);let l=(e,t)=>{let o=t?.customisedLogBookComponents?.nodes?.filter(e=>"VesselDailyCheck_LogBookComponent"===e.componentClass);return!!(o?.length>0&&o[0]?.customisedComponentFields?.nodes.filter(t=>t.fieldName===e&&"Off"!==t.status).length>0)&&o[0]?.customisedComponentFields?.nodes.filter(t=>t.fieldName===e&&"Off"!==t.status)[0].description},r=(e,t)=>{var o=[];let s=t?.customisedLogBookComponents?.nodes?.filter(e=>"VesselDailyCheck_LogBookComponent"===e.componentClass);return!!(s?.length>0&&s[0]?.customisedComponentFields?.nodes.map(t=>t.fieldName===e?o.push({fleldID:t.id,fieldName:t.fieldName}):""))&&o},a=(e,t)=>{let o=t?.customisedLogBookComponents?.nodes?.filter(e=>"VesselDailyCheck_LogBookComponent"===e.componentClass);return!!(o?.length>0&&o[0]?.customisedComponentFields?.nodes.filter(t=>t.fieldName===e&&"Off"!==t.status).length>0)},i=(e,t)=>{let o=t?.customisedLogBookComponents?.nodes?.filter(e=>"LogBookSignOff_LogBookComponent"===e.componentClass);return!!(o?.length>0&&o[0]?.customisedComponentFields?.nodes.filter(t=>t.fieldName===e&&"Off"!==t.status).length>0)},m=(e,t,o="VesselDailyCheck_LogBookComponent")=>{let n=s.FV.map(e=>e);var l=e;n.forEach(t=>{t.items.forEach(t=>{e===t.value&&(l=t?.label?t?.label:e)})});let r=t?.customisedLogBookComponents?.nodes?.filter(e=>e.componentClass===o);if(r?.length>0){let t=r[0]?.customisedComponentFields?.nodes.find(t=>t.fieldName===e);if(t?.customisedFieldTitle)return t?.customisedFieldTitle}return l},d=(e,t,o="LogBookSignOff_LogBookComponent")=>{let s=t?.customisedLogBookComponents?.nodes?.filter(e=>e.componentClass===o);return s?.length>0&&s[0]?.customisedComponentFields?.nodes.find(t=>t.fieldName===e)?.customisedFieldTitle?s[0]?.customisedComponentFields?.nodes.find(t=>t.fieldName===e)?.customisedFieldTitle:e},f=(e,t=!0,o=!1)=>{let l=s.FV.map(e=>e);var r=[],i=[],m=[];if(l.forEach(t=>{"VesselDailyCheck_LogBookComponent"===t.componentClass&&t.items.forEach(t=>{e.forEach(e=>{e.name===t.value&&(void 0!=t.groupTo?(r.push({...e,groupTo:t.groupTo}),m.includes(t.groupTo)||m.push(t.groupTo)):i.push(e))})})}),t){let e=m.map(e=>{let t=r.filter(t=>t.groupTo===e&&a(t.name,o)).sort(n.Z_);return{name:e,field:p(e),items:t,sortOrder:o?u(e,o):0}});return e.reduce((e,t)=>e+t.items.length,0)>0?e.sort(n.Z_):null}return i.filter(e=>!m?.includes(e.name)).sort(n.Z_)},p=e=>{let t=s.FV.map(e=>e);var o=[];return t.forEach(t=>{"VesselDailyCheck_LogBookComponent"===t.componentClass&&t.items.forEach(t=>{t.value===e&&o.push(t)})}),o[0]},u=(e,t)=>{let o=(t??[]).customisedLogBookComponents?.nodes.map(e=>e);var s=0;return o?.forEach(t=>{t.customisedComponentFields.nodes.map(t=>{e===t.fieldName&&(s=t.sortOrder)})}),s}},93956:(e,t,o)=>{o.d(t,{Ar:()=>y,Bo:()=>k,EJ:()=>c,Gy:()=>L,Lj:()=>h,N0:()=>g,No:()=>C,Ts:()=>E,Up:()=>i,V6:()=>m,V9:()=>_,Vu:()=>a,ZU:()=>N,Z_:()=>d,dO:()=>v,n3:()=>p,qH:()=>u,ql:()=>F,sU:()=>f,td:()=>l});var s=o(28288),n=o.n(s);let l=(e,t,o,s)=>{if(e&&s.vesselType){let n=t.map(e=>e);return{...e,customisedLogBookComponents:{...e.customisedLogBookComponents,nodes:e.customisedLogBookComponents.nodes.filter(e=>{var t=!1;return n.forEach(n=>{e.componentClass===n.componentClass&&n.vesselType.includes(o.indexOf(s.vesselType))&&(t=!0)}),t})}}}return e},r=(e,t)=>{let o=t.map(e=>e);var s=0;return o.forEach(t=>{e.componentClass===t.componentClass&&(s=t?.sortOrder?t?.sortOrder:99)}),s},a=(e,t)=>e.sort((e,o)=>r(e,t)>r(o,t)?1:r(e,t)<r(o,t)?-1:0),i=(e,t,o)=>{if(e?.__typename&&"CustomisedComponentField"===e.__typename){let o=t.filter(t=>t.fieldID===e.id).map(e=>e.status);return o.length>0?"Off"!==o[0]:"Off"!==e.status}{let t=o.filter(t=>t.localID===e.localID).map(e=>e.status);if(t.length>0)return"Off"!==t[0]}},m=(e,t,o)=>{if(e?.__typename&&"CustomisedComponentField"===e.__typename){let o=t.filter(t=>t.fieldID===e.id).map(e=>e.status);return o.length>0?"Off"===o[0]:"Off"===e.status}{let t=o.filter(t=>t.localID===e.localID).map(e=>e.status);if(t.length>0)return"Off"===t[0]}return!0},d=(e,t)=>{let o=e.customisedFieldTitle||e.fieldName,s=t.customisedFieldTitle||t.fieldName;return e.sortOrder-t.sortOrder||(n()(o)?1:n()(s)?-1:o.localeCompare(s))},f=(e,t,o,s)=>{let n=t.map(e=>e);var l=[];return n.forEach(t=>{o.toLowerCase()===t.label.toLowerCase()&&t.items.forEach(o=>{if(o.groupTo===e){let e=s.customisedLogBookComponents.nodes.find(e=>e.componentClass===t.componentClass);if(e){let t=e.customisedComponentFields.nodes.find(e=>e.fieldName===o.value);if(t)l.push(t);else{let t=e.id;l.push({...o,customisedLogBookComponentID:t,localID:t+o.value})}}}})}),l.sort(d)},p=(e,t,o)=>{if(!e?.__typename||"CustomisedComponentField"!==e.__typename)return e.fieldSet?e.fieldSet:"Other";{let n=t.map(e=>e);var s="Other";return n.forEach(t=>{o===t.label&&t.items.forEach((t,o)=>{e.fieldName===t.value&&(s=t?.fieldSet?t.fieldSet:"Other")})}),s}},u=(e,t,o)=>{if(!e?.__typename||"CustomisedComponentField"!==e.__typename)return!!e.groupTo&&e.groupTo;{let n=t.map(e=>e);var s=!1;return n.forEach(t=>{o===t.label&&t.items.forEach((t,o)=>{e.fieldName===t.value&&(s=!!t?.groupTo&&t.groupTo)})}),s}},c=(e,t,o)=>{let s=o.customisedLogBookComponents.nodes.filter(t=>t.id==e.id).map(e=>e),n=t.map(e=>e);var l=[];return s.forEach(e=>{let t=n.find(t=>t.componentClass===e.componentClass);t&&t.items.forEach(t=>{e.customisedComponentFields.nodes.some(e=>e.fieldName===t.value)||l.push(t)})}),l},C=(e,t,o,s,n)=>{if("Daily Checks"===t)return null==e.subFields?void 0:""==e.subFields||"Enabled"==s.filter(e=>{if(e.label===n)return e}).status&&"Engine Checks"==o||e.subFields&&e.subFields.split("||").includes(o)?"":"opacity-60 pointer-events-none"},g=(e,t,o)=>{let s=o.customisedLogBookComponents.nodes.filter(t=>t.id==e.id).map(e=>e),n=t.map(e=>e);var l=!1;return s.forEach(e=>{n.forEach(t=>{e.componentClass===t.componentClass&&t?.subCategory&&(l=!0)})}),l},h=(e,t)=>{let o=t.customisedLogBookComponents.nodes,s=e.map(e=>e);var n=[];return s.forEach(e=>{o.filter(t=>t.componentClass===e.componentClass).length>0||n.push(e)}),n},v=(e,t,o)=>{if(e?.__typename&&"CustomisedComponentField"===e.__typename){let n=t.map(e=>e);var s=!1;return n.forEach(t=>{o===t.label&&t.items.forEach(t=>{e.fieldName===t.value&&"files"===t.fieldType&&(s=!0)})}),s}{let n=t.map(e=>e);var s=!1;return n.forEach(t=>{o===t.label&&t.items.forEach(t=>{e.value===t.value&&"files"===t.fieldType&&(s=!0)})}),s}},_=(e,t)=>{let o=t.map(e=>e);var s=e.title;return o.forEach(t=>{e.componentClass===t.componentClass&&(s=t?.title?t?.title:e.title)}),s},E=(e,t)=>{if(!e?.__typename||"CustomisedComponentField"!==e.__typename)return e?.label?e.label:e?.value;{let s=t.map(e=>e);var o=e.fieldName;return s.forEach(t=>{t.items.forEach(t=>{e.fieldName===t.value&&(o=t?.label?t?.label:e.fieldName)})}),o}},y=(e,t,o)=>{var s=!1;return t.customisedLogBookComponents?.nodes.filter(e=>e.title===o).map(t=>{t.customisedComponentFields.nodes.filter((e,t,o)=>o.findIndex(t=>t.fieldName===e.fieldName)===t).forEach(t=>{t.fieldName===e&&(s=!0)})}),s},N=(e,t,o)=>{let s=t.map(e=>e);var n="";return e?.__typename&&"CustomisedComponentField"===e.__typename?s.forEach(t=>{o===t.label&&t.items.forEach(t=>{e.fieldName===t.value&&(n+=t?.classes)})}):s.forEach(t=>{o===t.label&&t.items.forEach(t=>{e.value===t.value&&(n+=t?.classes)})}),n},k=(e,t,o)=>o.filter(e=>e.subCategory)[0].items.filter(t=>3===t.level&&t.fieldSet===e).length>0,F=(e,t,o)=>{if(!e?.__typename||"CustomisedComponentField"!==e.__typename)return!!e.tab&&e.tab;{let n=t.map(e=>e);var s=!1;return n.forEach(t=>{o===t.label&&t.items.forEach((t,o)=>{e.fieldName===t.value&&(s=t.tab)})}),s}},L=(e,t,o)=>{let s=t.map(e=>e);var n=!1;return s.forEach(t=>{o===t.label&&t.items.forEach(t=>{e.fieldName===t?.groupTo&&(n=!0)})}),n}}};