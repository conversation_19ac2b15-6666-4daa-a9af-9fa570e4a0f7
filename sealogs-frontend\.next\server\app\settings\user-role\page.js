(()=>{var e={};e.id=9230,e.ids=[9230],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},32906:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>c,routeModule:()=>x,tree:()=>l}),r(5872),r(50681),r(78398),r(57757),r(48045);var t=r(40060),i=r(33581),o=r(57567),n=r.n(o),a=r(51650),u={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(s,u);let l=["",{children:["settings",{children:["user-role",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5872)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\user-role\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50681)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\settings\\user-role\\page.tsx"],d="/settings/user-role/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/settings/user-role/page",pathname:"/settings/user-role",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},31941:(e,s,r)=>{Promise.resolve().then(r.bind(r,84313))},33840:(e,s,r)=>{Promise.resolve().then(r.bind(r,83180))},84313:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(98768);r(60343);var i=r(64837);function o({children:e}){return t.jsx(i.Z,{children:e})}},83180:(e,s,r)=>{"use strict";r.d(s,{default:()=>p});var t=r(98768),i=r(60343),o=r(17380),n=r(66263),a=r(13842),u=r(46776),l=r(69424),c=r(50088),d=r(25394);let p=()=>{let e=(0,l.useRouter)(),[s,r]=(0,i.useState)([]);return(0,a.mJ)(r),(0,i.useEffect)(()=>{(0,u.UU)()},[]),(0,t.jsxs)(t.Fragment,{children:[t.jsx(d.Bu,{title:"User Roles",actions:t.jsx(c.Z,{action:()=>{e.push("/settings/user-role/create")},text:"New User Role",color:"sky",type:"primary",icon:"check"})}),s?t.jsx(d.Zb,{className:"mt-8",children:t.jsx(d.aY,{children:(0,t.jsxs)(d.iA,{children:[t.jsx(d.xD,{children:(0,t.jsxs)(d.SC,{children:[t.jsx(d.ss,{children:"Role"}),t.jsx(d.ss,{children:"Code"}),t.jsx(d.ss,{children:"Description"})]})}),t.jsx(d.RM,{children:s.map(e=>(0,t.jsxs)(d.SC,{children:[t.jsx(d.pj,{children:t.jsx(n.default,{href:`/settings/user-role/edit?id=${e.id}`,className:"",children:e.title})}),t.jsx(d.pj,{children:e.code}),t.jsx(d.pj,{children:e.description})]},e.id))})]})})}):t.jsx(o.hM,{})]})}},50681:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\settings\layout.tsx#default`)},5872:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(96141);let i=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\user-role\list.tsx#default`),o=()=>t.jsx(i,{})}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[864,8865,3563,6263,8189,9507,7602,2935,6451,4234,2925,5394,4837,3842,88,7380],()=>r(32906));module.exports=t})();