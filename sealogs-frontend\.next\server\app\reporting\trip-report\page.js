(()=>{var e={};e.id=6131,e.ids=[6131],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},19946:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>d,routeModule:()=>g,tree:()=>l}),r(84390),r(71238),r(78398),r(57757),r(48045);var i=r(40060),o=r(33581),s=r(57567),a=r.n(s),n=r(51650),p={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>n[e]);r.d(t,p);let l=["",{children:["reporting",{children:["trip-report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84390)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\trip-report\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\trip-report\\page.tsx"],c="/reporting/trip-report/page",u={require:r,loadChunk:()=>Promise.resolve()},g=new i.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/reporting/trip-report/page",pathname:"/reporting/trip-report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},88332:(e,t,r)=>{Promise.resolve().then(r.bind(r,44517))},44331:(e,t,r)=>{Promise.resolve().then(r.bind(r,99851))},91973:(e,t,r)=>{"use strict";r.d(t,{u:()=>i});let i=(e,t="report.csv")=>{let r=new Blob([e.map(e=>e.join(",")).join("\n")],{type:"text/csv;charset=utf-8,"}),i=URL.createObjectURL(r);Object.assign(document.createElement("a"),{href:i,download:t}).click(),URL.revokeObjectURL(i)}},75546:(e,t,r)=>{"use strict";r.d(t,{Br:()=>u,fU:()=>g,o0:()=>d,p6:()=>l,vq:()=>c});var i=r(83179),o=r.n(i),s=r(7678),a=r.n(s),n=r(14826),p=r.n(n);let l=(e="",t=!0)=>{let r;if(a()(p()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,i,o]=e.split("-"),s=t?r.slice(-2):r,a=parseInt(o,10).toString().padStart(2,"0"),n=parseInt(i,10).toString().padStart(2,"0");return`${a}/${n}/${s}`}if(!(r=e&&"object"==typeof e?o()(e.toString()):o()(e)).isValid())return"";let i=r.format("DD"),s=r.format("MM"),n=t?r.format("YY"):r.format("YYYY");return`${i}/${s}/${n}`},d=(e="",t=!0)=>{let r;if(a()(p()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,i,o]=e.split("-"),s=t?r.slice(-2):r,a=parseInt(o,10).toString().padStart(2,"0"),n=parseInt(i,10).toString().padStart(2,"0");return`${a}/${n}/${s} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[r,i]=e.split(" "),[o,s,a]=r.split("-"),n=t?o.slice(-2):o,p=i.split(":"),l=p[0].padStart(2,"0"),d=p[1].padStart(2,"0"),c=parseInt(a,10).toString().padStart(2,"0"),u=parseInt(s,10).toString().padStart(2,"0");return`${c}/${u}/${n} ${l}:${d}`}if(!(r=e&&"object"==typeof e?o()(e.toString()):o()(e)).isValid())return"";let i=r.format("DD"),s=r.format("MM"),n=t?r.format("YY"):r.format("YYYY"),l=r.format("HH:mm");return`${i}/${s}/${n} ${l}`},c=(e="")=>a()(p()(e))?"":o()(e).format("YYYY-MM-DD HH:mm:ss"),u=(e="")=>a()(p()(e))?new Date:new Date(`${e}T10:00:00Z`),g=(e,t)=>{let r=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),i=e=>e.includes(" ")?e.replace(" ","T"):e,o=e=>{if(!e||"string"!=typeof e)return null;if(r(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(i(e))},s=o(e),a=o(t);return!s||!a||isNaN(s.getTime())||isNaN(a.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):a>s}},44517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var i=r(98768);r(60343);var o=r(32993),s=r(64837);function a({children:e}){return i.jsx(o.default,{children:i.jsx(s.Z,{children:e})})}},99851:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var i=r(98768);r(46776);var o=r(26100),s=r(79418),a=r(7678),n=r.n(a),p=r(69424),l=r(60343),d=r(45519);let c=(0,d.ZP)`
    query ReadTripReport_LogBookEntrySections(
        $filter: TripReport_LogBookEntrySectionFilterFields = {}
        $limit: Int = 100
        $offset: Int = 0
    ) {
        readTripReport_LogBookEntrySections(
            filter: $filter
            limit: $limit
            offset: $offset
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                tripScheduleDepartTime
                departTime
                tripScheduleArriveTime
                paxJoinedAdult
                arriveTime
                pob
                vob
                comment
                fromLocation {
                    id
                    title
                }
                toLocation {
                    id
                    title
                }
                logBookEntry {
                    startDate
                    vehicle {
                        title
                    }
                }
                tripReportScheduleID
                tripReportSchedule {
                    transitTripID
                }
                tripReport_Stops {
                    nodes {
                        paxJoined
                        paxDeparted
                        arriveTime
                        departTime
                        stopLocation {
                            title
                        }
                    }
                }
            }
        }
    }
`;var u=r(83179),g=r.n(u),m=r(75546),x=r(91973),f=r(37042),h=r(50088),S=r(25394);let v=["Vessel","Date","Depart Location","Total Pax Carried","Total Vehicle Carried","Destination"],T=({isLoading:e,data:t})=>e?i.jsx(S.SC,{children:i.jsx(S.pj,{colSpan:v.length,className:"text-center h-32",children:"Loading..."})}):0===t.length?i.jsx(S.SC,{children:i.jsx(S.pj,{colSpan:v.length,className:"text-center h-32",children:"No data available"})}):t.map(e=>(0,i.jsxs)(S.SC,{children:[i.jsx(S.pj,{children:e.logBookEntry.vehicle.title}),i.jsx(S.pj,{children:(0,m.p6)(e.logBookEntry.startDate)}),i.jsx(S.pj,{children:e.fromLocation.title}),i.jsx(S.pj,{children:e.tripReport_Stops?.nodes?.length>0?e.pob+e.tripReport_Stops.nodes.reduce((e,t)=>e+(t.paxJoined||0),0):e.pob}),i.jsx(S.pj,{children:e.vob>0&&e.vob}),i.jsx(S.pj,{children:e.toLocation.title})]},e.id)),D=()=>{let e=(0,p.useRouter)(),[t,r]=(0,l.useState)({}),[o,a]=(0,l.useState)([]),[d,{loading:u}]=(0,s.t)(c,{fetchPolicy:"cache-and-network",onCompleted:e=>{a(e.readTripReport_LogBookEntrySections.nodes)},onError:e=>{console.error("readTripReport_LogBookEntrySections error",e)}}),D=async()=>{let e={archived:{eq:!1}},r=t.dateRange?.startDate?g()(t.dateRange.startDate).format("YYYY-MM-DD"):null,i=t.dateRange?.endDate?g()(t.dateRange.endDate).format("YYYY-MM-DD"):null;r&&i&&(e.logBookEntry={...e.logBookEntry,startDate:{gte:r,lte:i}});let o=t.fromLocation?.value,s=t.toLocation?.value;o&&(e.fromLocationID={eq:o}),s&&(e.toLocationID={eq:s});let a=t.fromTime,n=t.toTime;a&&n?e.departTime={gte:a,lte:n}:a?e.departTime={gte:a}:n&&(e.departTime={lte:n}),t.noPax&&(e.numberPax={eq:0}),t.vessels?.length>0&&(e.logBookEntry={...e.logBookEntry,vehicleID:{in:t.vessels.map(e=>e.value)}}),await d({variables:{filter:e,limit:100,offset:0}})};return(0,l.useEffect)(()=>{n()(t)||D()},[t]),(0,i.jsxs)(i.Fragment,{children:[i.jsx(S.Bu,{title:"Trip Report",actions:i.jsx("div",{className:"flex",children:i.jsx(S.zx,{variant:"back",onClick:()=>e.push("/reporting"),children:"Back"})})}),i.jsx(S.Zb,{className:"mt-8",children:(0,i.jsxs)(S.aY,{className:"flex flex-col gap-4",children:[i.jsx(f.Z,{tripReportFilterData:t,onChange:e=>{r({...t,[e.type]:e.data})}}),i.jsx("div",{className:"flex justify-end gap-3",children:i.jsx(h.Z,{text:"Download CSV",type:"primary",action:()=>{if(0===o.length)return;let e=[["Transit Trip ID","Vessel","Date","Scheduled Depart Time","Depart Time","Depart Location","Adult (boarded at origin)","Passengers Onboard (at final destination)","Total Pax Carried","Trip Type","Scheduled Arrive Time","Arrival Time","Destination","Masters Remarks","Stop","Arrive Time","Pax Off","Pax On","Depart Time"]];o.forEach(t=>{let r=[t.tripReportSchedule?.transitTripID??"",t.logBookEntry.vehicle.title,(0,m.p6)(t.logBookEntry.startDate,!1),t.tripScheduleDepartTime??"",t.departTime??"",t.fromLocation.title??"",t.pob>0?t.pob.toString():""];if(t.tripReport_Stops?.nodes?.length>0)t.tripReport_Stops.nodes.forEach(i=>{let o=[...r];o.push(t.pob-i.paxDeparted+i.paxJoined>0?(t.pob-i.paxDeparted+i.paxJoined).toString():"",t.pob+i.paxJoined>0?(t.pob+i.paxJoined).toString():"",t.tripReportScheduleID>0?"Scheduled":"Unscheduled",t.tripScheduleArriveTime??"",t.arriveTime??"",t.toLocation?.title??"",t.comment??"",i.stopLocation?.title??"",i.arriveTime??"",i.paxDeparted?.toString()??"",i.paxJoined?.toString()??"",i.departTime??""),e.push(o)});else{let i=[...r];i.push(t.pob>0?t.pob.toString():"",t.pob>0?t.pob.toString():"",t.tripReportScheduleID>0?"Scheduled":"Unscheduled",t.tripScheduleArriveTime??"",t.arriveTime??"",t.toLocation?.title??"",t.comment??"","","","","",""),e.push(i)}});let t=`trip-report-${g()().format("YYYY-MM-DD-HHmmss")}.csv`;(0,x.u)(e,t)}})}),(0,i.jsxs)(S.iA,{children:[i.jsx(S.xD,{children:i.jsx(S.SC,{children:v.map(e=>i.jsx(S.ss,{children:e},e))})}),i.jsx(T,{isLoading:u,data:o})]})]})})]})};function j(){let[e,t]=(0,l.useState)(!1),[r,s]=(0,l.useState)(!1);return e&&r?i.jsx(D,{}):e?i.jsx(o.Z,{errorMessage:"Oops You do not have the permission to view this section."}):i.jsx(o.Z,{})}},71238:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)},84390:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\trip-report\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042],()=>r(19946));module.exports=i})();