(()=>{var e={};e.id=5158,e.ids=[5158],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},44780:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>l,originalPathname:()=>d,pages:()=>p,routeModule:()=>g,tree:()=>c}),t(27042),t(74399),t(78398),t(57757),t(48045);var s=t(40060),i=t(33581),a=t(57567),n=t.n(a),o=t(51650),u={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);t.d(r,u);let c=["",{children:["crew-training",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,27042)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew-training\\create\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,74399)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew-training\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\crew-training\\create\\page.tsx"],d="/crew-training/create/page",l={require:t,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/crew-training/create/page",pathname:"/crew-training/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},44328:(e,r,t)=>{Promise.resolve().then(t.bind(t,2876))},2876:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(98768),i=t(4687),a=t(69424);let n=()=>{let e=(0,a.useSearchParams)(),r=e.get("memberId")??0,t=e.get("vesselID")??0,n=e.get("trainingTypeId")??0;return s.jsx(i.Z,{trainingID:0,memberId:+r,vesselId:+t,trainingTypeId:+n})}},27042:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\crew-training\create\page.tsx#default`)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[864,8865,3563,6263,8189,9507,7602,2935,8594,6451,4234,2925,5394,4837,6342,3842,8712,90,7380,5776,209],()=>t(44780));module.exports=s})();