(()=>{var e={};e.id=7511,e.ids=[7511],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},24622:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>g,tree:()=>c}),s(46292),s(6537),s(78398),s(57757),s(48045);var r=s(40060),a=s(33581),i=s(57567),n=s.n(i),o=s(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["risk-strategies",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,46292)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\risk-strategies\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,6537)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\risk-strategies\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\risk-strategies\\page.tsx"],u="/risk-strategies/page",p={require:s,loadChunk:()=>Promise.resolve()},g=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/risk-strategies/page",pathname:"/risk-strategies",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53983:(e,t,s)=>{Promise.resolve().then(s.bind(s,81014))},65313:(e,t,s)=>{Promise.resolve().then(s.bind(s,37730))},81014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(98768);s(60343);var a=s(64837);function i({children:e}){return r.jsx(a.Z,{children:e})}},37730:(e,t,s)=>{"use strict";s.d(t,{default:()=>g});var r=s(98768),a=s(60343),i=s(79418),n=s(72548),o=s(94060),l=s(33849),c=s(76342),d=s(25394),u=s(74602),p=s(70701);function g(){let[e,t]=(0,a.useState)([]),[s,g]=(0,a.useState)(!1),[x,h]=(0,a.useState)(),[m,f]=(0,a.useState)(""),[j]=(0,i.t)(o.f7,{fetchPolicy:"cache-and-network",onCompleted:e=>{console.info(e.readRiskFactors.nodes),t(e.readRiskFactors.nodes)},onError:e=>{console.error("onError",e)}}),[v]=(0,n.D)(c.dxh,{onCompleted:()=>{j({variables:{filter:{type:{ne:"RiskFactor"}}}})},onError:e=>{console.error("onError",e)}}),y=(0,p.wu)([{accessorKey:"title",header:"Risk strategy",cellAlignment:"left",cell({row:e}){let t=e.original;return(0,r.jsxs)(r.Fragment,{children:[t.title||"-"," (","TowingChecklist"===t.type&&"Towing Checklist","DangerousGoods"===t.type&&"Dangerous Goods","BarCrossingChecklist"===t.type&&"Bar Crossing Checklist",")"]})}},{accessorKey:"impact",header:"Impact",cellAlignment:"left"},{accessorKey:"probability",header:"Probability",cellAlignment:"left",cell:({getValue:e})=>`${e()}/10`}]);return(0,r.jsxs)(r.Fragment,{children:[r.jsx(d.Bu,{title:"Risk Strategies"}),r.jsx("div",{className:"mt-8",children:e&&e.length>0&&r.jsx(p.c4,{data:e,columns:y,showToolbar:!1,collapsible:!0,pageSize:20,renderExpandedContent:e=>{let t=e.mitigationStrategy.nodes;return(0,r.jsxs)("div",{className:"flex flex-col gap-4",children:[r.jsx(u.H3,{className:"text-xl",children:"Mitigation Strategies"}),0==t.length&&r.jsx("div",{className:"py-4",children:"No strategies available"}),t.length>0&&r.jsx(d.iA,{children:r.jsx(d.RM,{children:t.map(e=>r.jsx(d.SC,{onClick:()=>{f(e.strategy),g(!0),h(e)},children:r.jsx(d.pj,{children:r.jsx("div",{dangerouslySetInnerHTML:{__html:e.strategy}})})},e.id))})})]})},canExpand:e=>!0,showPageSizeSelector:!1})}),r.jsx(d.h9,{openDialog:s,setOpenDialog:g,handleCreate:()=>{m&&x&&v({variables:{input:{id:x.id,strategy:m}}}),g(!1)},actionText:"Update",title:"Mitigation Strategy",size:"xl",description:"Edit the mitigation strategy content below",children:r.jsx("div",{className:"flex items-center",children:r.jsx(l.Z,{id:"strategy",placeholder:"Mitigation strategy",className:"w-full",content:m,handleEditorChange:e=>{f(e)}})})})]})}},10901:(e,t,s)=>{"use strict";s.d(t,{k:()=>p});var r=s(98768),a=s(11652),i=s(41641),n=s(39303),o=s(40712),l=s(39544),c=s(24224),d=s(25394),u=s(50058);function p({table:e,pageSizeOptions:t=[10,20,30,40,50],showPageSizeSelector:s=!0}){let p=(0,u.k)();return r.jsx("div",{className:"flex items-center justify-center px-2",children:(0,r.jsxs)("div",{className:"flex gap-2 items-end phablet:items-center phablet:space-x-6 lg:space-x-8",children:[s&&r.jsx("div",{className:"flex items-center space-x-2",children:r.jsx(d.__,{label:"Rows per page",position:p.phablet?"left":"top",htmlFor:"rows-per-page",className:"text-sm",children:(0,r.jsxs)(c.Select,{value:`${e.getState().pagination.pageSize}`,onValueChange:t=>{e.setPageSize(Number(t))},children:[r.jsx(c.SelectTrigger,{className:"h-8 w-[70px]",children:r.jsx(c.SelectValue,{placeholder:e.getState().pagination.pageSize})}),r.jsx(c.SelectContent,{side:"top",children:t.map(e=>r.jsx(c.SelectItem,{value:`${e}`,children:e},e))})]})})}),(0,r.jsxs)("div",{className:"flex w-fit items-center justify-center text-sm",children:["Page ",e.getState().pagination.pageIndex+1," of"," ",e.getPageCount()]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(l.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>e.setPageIndex(0),disabled:!e.getCanPreviousPage(),children:[r.jsx("span",{className:"sr-only",children:"Go to first page"}),r.jsx(a.Z,{})]}),(0,r.jsxs)(l.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>e.previousPage(),disabled:!e.getCanPreviousPage(),children:[r.jsx("span",{className:"sr-only",children:"Go to previous page"}),r.jsx(i.Z,{})]}),(0,r.jsxs)(l.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>e.nextPage(),disabled:!e.getCanNextPage(),children:[r.jsx("span",{className:"sr-only",children:"Go to next page"}),r.jsx(n.Z,{})]}),(0,r.jsxs)(l.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>e.setPageIndex(e.getPageCount()-1),disabled:!e.getCanNextPage(),children:[r.jsx("span",{className:"sr-only",children:"Go to last page"}),r.jsx(o.Z,{})]})]})]})})}},6958:(e,t,s)=>{"use strict";s.d(t,{n:()=>i});var r=s(98768),a=s(37042);function i({table:e,onChange:t}){return r.jsx(a.Z,{onChange:t,table:e})}},6537:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\risk-strategies\layout.tsx#default`)},46292:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(96141);let a=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\risk-evaluations\risk-strategies.tsx#default`);function i(){return r.jsx(a,{})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,8249],()=>s(24622));module.exports=r})();