"use strict";exports.id=6014,exports.ids=[6014],exports.modules={75546:(a,e,i)=>{i.d(e,{Br:()=>m,fU:()=>b,o0:()=>t,p6:()=>A,vq:()=>s});var r=i(83179),l=i.n(r),n=i(7678),c=i.n(n),o=i(14826),u=i.n(o);let A=(a="",e=!0)=>{let i;if(c()(u()(a)))return"";if("string"==typeof a&&/^\d{4}-\d{2}-\d{2}$/.test(a)){let[i,r,l]=a.split("-"),n=e?i.slice(-2):i,c=parseInt(l,10).toString().padStart(2,"0"),o=parseInt(r,10).toString().padStart(2,"0");return`${c}/${o}/${n}`}if(!(i=a&&"object"==typeof a?l()(a.toString()):l()(a)).isValid())return"";let r=i.format("DD"),n=i.format("MM"),o=e?i.format("YY"):i.format("YYYY");return`${r}/${n}/${o}`},t=(a="",e=!0)=>{let i;if(c()(u()(a)))return"";if("string"==typeof a&&/^\d{4}-\d{2}-\d{2}$/.test(a)){let[i,r,l]=a.split("-"),n=e?i.slice(-2):i,c=parseInt(l,10).toString().padStart(2,"0"),o=parseInt(r,10).toString().padStart(2,"0");return`${c}/${o}/${n} 00:00`}if("string"==typeof a&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(a)){let[i,r]=a.split(" "),[l,n,c]=i.split("-"),o=e?l.slice(-2):l,u=r.split(":"),A=u[0].padStart(2,"0"),t=u[1].padStart(2,"0"),s=parseInt(c,10).toString().padStart(2,"0"),m=parseInt(n,10).toString().padStart(2,"0");return`${s}/${m}/${o} ${A}:${t}`}if(!(i=a&&"object"==typeof a?l()(a.toString()):l()(a)).isValid())return"";let r=i.format("DD"),n=i.format("MM"),o=e?i.format("YY"):i.format("YYYY"),A=i.format("HH:mm");return`${r}/${n}/${o} ${A}`},s=(a="")=>c()(u()(a))?"":l()(a).format("YYYY-MM-DD HH:mm:ss"),m=(a="")=>c()(u()(a))?new Date:new Date(`${a}T10:00:00Z`),b=(a,e)=>{let i=a=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(a),r=a=>a.includes(" ")?a.replace(" ","T"):a,l=a=>{if(!a||"string"!=typeof a)return null;if(i(a)){let e=new Date().toISOString().split("T")[0];return new Date(`${e}T${a}`)}return new Date(r(a))},n=l(a),c=l(e);return!n||!c||isNaN(n.getTime())||isNaN(c.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:a,actualArrival:e}),!1):c>n}},44010:(a,e,i)=>{i.d(e,{$Y:()=>r,uq:()=>n,wX:()=>l});let r=[{value:"AF",label:"Afghanistan"},{value:"AL",label:"Albania"},{value:"DZ",label:"Algeria"},{value:"AS",label:"American Samoa"},{value:"AD",label:"Andorra"},{value:"AO",label:"Angola"},{value:"AI",label:"Anguilla"},{value:"AQ",label:"Antarctica"},{value:"AG",label:"Antigua and Barbuda"},{value:"AR",label:"Argentina"},{value:"AM",label:"Armenia"},{value:"AW",label:"Aruba"},{value:"AU",label:"Australia"},{value:"AT",label:"Austria"},{value:"AZ",label:"Azerbaijan"},{value:"BS",label:"Bahamas"},{value:"BH",label:"Bahrain"},{value:"BD",label:"Bangladesh"},{value:"BB",label:"Barbados"},{value:"BY",label:"Belarus"},{value:"BE",label:"Belgium"},{value:"BZ",label:"Belize"},{value:"BJ",label:"Benin"},{value:"BM",label:"Bermuda"},{value:"BT",label:"Bhutan"},{value:"BO",label:"Bolivia"},{value:"BA",label:"Bosnia and Herzegovina"},{value:"BW",label:"Botswana"},{value:"BV",label:"Bouvet Island"},{value:"BR",label:"Brazil"},{value:"IO",label:"British Indian Ocean Territory"},{value:"BN",label:"Brunei Darussalam"},{value:"BG",label:"Bulgaria"},{value:"BF",label:"Burkina Faso"},{value:"BI",label:"Burundi"},{value:"KH",label:"Cambodia"},{value:"CM",label:"Cameroon"},{value:"CA",label:"Canada"},{value:"CV",label:"Cape Verde"},{value:"KY",label:"Cayman Islands"},{value:"CF",label:"Central African Republic"},{value:"TD",label:"Chad"},{value:"CL",label:"Chile"},{value:"CN",label:"China"},{value:"CX",label:"Christmas Island"},{value:"CC",label:"Cocos (Keeling) Islands"},{value:"CO",label:"Colombia"},{value:"KM",label:"Comoros"},{value:"CG",label:"Congo"},{value:"CK",label:"Cook Islands"},{value:"CR",label:"Costa Rica"},{value:"CI",label:"Cote d'Ivoire"},{value:"HR",label:"Croatia"},{value:"CU",label:"Cuba"},{value:"CY",label:"Cyprus"},{value:"CZ",label:"Czech Republic"},{value:"DK",label:"Denmark"},{value:"DJ",label:"Djibouti"},{value:"DM",label:"Dominica"},{value:"DO",label:"Dominican Republic"},{value:"TL",label:"East Timor"},{value:"EC",label:"Ecuador"},{value:"EG",label:"Egypt"},{value:"SV",label:"El Salvador"},{value:"GQ",label:"Equatorial Guinea"},{value:"ER",label:"Eritrea"},{value:"EE",label:"Estonia"},{value:"ET",label:"Ethiopia"},{value:"EU",label:"Europe"},{value:"FK",label:"Falkland Islands (Malvinas)"},{value:"FO",label:"Faroe Islands"},{value:"FJ",label:"Fiji"},{value:"FI",label:"Finland"},{value:"FR",label:"France"},{value:"GF",label:"French Guiana"},{value:"PF",label:"French Polynesia"},{value:"TF",label:"French Southern Territories"},{value:"GA",label:"Gabon"},{value:"GM",label:"Gambia"},{value:"GE",label:"Georgia"},{value:"DE",label:"Germany"},{value:"GH",label:"Ghana"},{value:"GI",label:"Gibraltar"},{value:"GR",label:"Greece"},{value:"GL",label:"Greenland"},{value:"GD",label:"Grenada"},{value:"GP",label:"Guadeloupe"},{value:"GU",label:"Guam"},{value:"GT",label:"Guatemala"},{value:"GN",label:"Guinea"},{value:"GW",label:"Guinea-Bissau"},{value:"GY",label:"Guyana"},{value:"HT",label:"Haiti"},{value:"HM",label:"Heard Island and McDonald Islands"},{value:"VA",label:"Holy See - Vatican City State"},{value:"HN",label:"Honduras"},{value:"HK",label:"Hong Kong"},{value:"HU",label:"Hungary"},{value:"IS",label:"Iceland"},{value:"IN",label:"India"},{value:"ID",label:"Indonesia"},{value:"IR",label:"Iran"},{value:"IQ",label:"Iraq"},{value:"IE",label:"Ireland"},{value:"IL",label:"Israel"},{value:"IT",label:"Italy"},{value:"JM",label:"Jamaica"},{value:"JP",label:"Japan"},{value:"JO",label:"Jordan"},{value:"KZ",label:"Kazakhstan"},{value:"KE",label:"Kenya"},{value:"KI",label:"Kiribati"},{value:"KW",label:"Kuwait"},{value:"KG",label:"Kyrgyzstan"},{value:"LA",label:"Laos"},{value:"LV",label:"Latvia"},{value:"LB",label:"Lebanon"},{value:"LS",label:"Lesotho"},{value:"LR",label:"Liberia"},{value:"LY",label:"Libyan Arab Jamahiriya"},{value:"LI",label:"Liechtenstein"},{value:"LT",label:"Lithuania"},{value:"LU",label:"Luxembourg"},{value:"MO",label:"Macao"},{value:"MK",label:"Macedonia"},{value:"MG",label:"Madagascar"},{value:"MW",label:"Malawi"},{value:"MY",label:"Malaysia"},{value:"MV",label:"Maldives"},{value:"ML",label:"Mali"},{value:"MT",label:"Malta"},{value:"MH",label:"Marshall Islands"},{value:"MQ",label:"Martinique"},{value:"MR",label:"Mauritania"},{value:"MU",label:"Mauritius"},{value:"YT",label:"Mayotte"},{value:"MX",label:"Mexico"},{value:"FM",label:"Micronesia - Federated States of"},{value:"MD",label:"Moldova - Republic of"},{value:"MC",label:"Monaco"},{value:"MN",label:"Mongolia"},{value:"ME",label:"Montenegro"},{value:"MS",label:"Montserrat"},{value:"MA",label:"Morocco"},{value:"MZ",label:"Mozambique"},{value:"MM",label:"Myanmar"},{value:"NA",label:"Namibia"},{value:"NR",label:"Nauru"},{value:"NP",label:"Nepal"},{value:"NL",label:"Netherlands"},{value:"AN",label:"Netherlands Antilles"},{value:"NC",label:"New Caledonia"},{value:"NZ",label:"New Zealand"},{value:"NI",label:"Nicaragua"},{value:"NE",label:"Niger"},{value:"NG",label:"Nigeria"},{value:"NU",label:"Niue"},{value:"NF",label:"Norfolk Island"},{value:"KP",label:"North Korea"},{value:"MP",label:"Northern Mariana Islands"},{value:"NO",label:"Norway"},{value:"OM",label:"Oman"},{value:"PK",label:"Pakistan"},{value:"PW",label:"Palau"},{value:"PS",label:"Palestinian Territory"},{value:"PA",label:"Panama"},{value:"PG",label:"Papua New Guinea"},{value:"PY",label:"Paraguay"},{value:"PE",label:"Peru"},{value:"PH",label:"Philippines"},{value:"PN",label:"Pitcairn"},{value:"PL",label:"Poland"},{value:"PT",label:"Portugal"},{value:"PR",label:"Puerto Rico"},{value:"QA",label:"Qatar"},{value:"RE",label:"Reunion"},{value:"RO",label:"Romania"},{value:"RU",label:"Russian Federation"},{value:"RW",label:"Rwanda"},{value:"SH",label:"Saint Helena"},{value:"KN",label:"Saint Kitts and Nevis"},{value:"LC",label:"Saint Lucia"},{value:"PM",label:"Saint Pierre and Miquelon"},{value:"VC",label:"Saint Vincent and the Grenadines"},{value:"WS",label:"Samoa"},{value:"SM",label:"San Marino"},{value:"ST",label:"Sao Tome and Principe"},{value:"SA",label:"Saudi Arabia"},{value:"SN",label:"Senegal"},{value:"RS",label:"Serbia"},{value:"SC",label:"Seychelles"},{value:"SL",label:"Sierra Leone"},{value:"SG",label:"Singapore"},{value:"SK",label:"Slovakia"},{value:"SI",label:"Slovenia"},{value:"SB",label:"Solomon Islands"},{value:"SO",label:"Somalia"},{value:"ZA",label:"South Africa"},{value:"GS",label:"South Georgia and the South Sandwich Islands"},{value:"KR",label:"South Korea"},{value:"ES",label:"Spain"},{value:"LK",label:"Sri Lanka"},{value:"SD",label:"Sudan"},{value:"SR",label:"Suriname"},{value:"SJ",label:"Svalbard and Jan Mayen"},{value:"SZ",label:"Swaziland"},{value:"SE",label:"Sweden"},{value:"CH",label:"Switzerland"},{value:"SY",label:"Syrian Arab Republic"},{value:"TW",label:"Taiwan"},{value:"TJ",label:"Tajikistan"},{value:"TZ",label:"Tanzania (United Republic of)"},{value:"TH",label:"Thailand"},{value:"TG",label:"Togo"},{value:"TK",label:"Tokelau"},{value:"TO",label:"Tonga"},{value:"TT",label:"Trinidad and Tobago"},{value:"TN",label:"Tunisia"},{value:"TR",label:"Turkey"},{value:"TM",label:"Turkmenistan"},{value:"TC",label:"Turks and Caicos Islands"},{value:"TV",label:"Tuvalu"},{value:"X1",label:"UK - England"},{value:"X3",label:"UK - Northern Ireland"},{value:"X2",label:"UK - Scotland"},{value:"X4",label:"UK - Wales"},{value:"UG",label:"Uganda"},{value:"UA",label:"Ukraine"},{value:"AE",label:"United Arab Emirates"},{value:"GB",label:"United Kingdom"},{value:"US",label:"United States"},{value:"UM",label:"United States Minor Outlying Islands"},{value:"UY",label:"Uruguay"},{value:"UZ",label:"Uzbekistan"},{value:"VU",label:"Vanuatu"},{value:"VE",label:"Venezuela"},{value:"VN",label:"Vietnam"},{value:"VG",label:"Virgin Islands - British"},{value:"VI",label:"Virgin Islands - U.S."},{value:"WF",label:"Wallis and Futuna"},{value:"EH",label:"Western Sahara"},{value:"YE",label:"Yemen"},{value:"ZR",label:"Zaire"},{value:"ZM",label:"Zambia"},{value:"ZW",label:"Zimbabwe"}],l={ALL:["Africa/Abidjan","Africa/Accra","Africa/Addis_Ababa","Africa/Algiers","Africa/Asmara","Africa/Asmera","Africa/Bamako","Africa/Bangui","Africa/Banjul","Africa/Bissau","Africa/Blantyre","Africa/Brazzaville","Africa/Bujumbura","Africa/Cairo","Africa/Casablanca","Africa/Ceuta","Africa/Conakry","Africa/Dakar","Africa/Dar_es_Salaam","Africa/Djibouti","Africa/Douala","Africa/El_Aaiun","Africa/Freetown","Africa/Gaborone","Africa/Harare","Africa/Johannesburg","Africa/Juba","Africa/Kampala","Africa/Khartoum","Africa/Kigali","Africa/Kinshasa","Africa/Lagos","Africa/Libreville","Africa/Lome","Africa/Luanda","Africa/Lubumbashi","Africa/Lusaka","Africa/Malabo","Africa/Maputo","Africa/Maseru","Africa/Mbabane","Africa/Mogadishu","Africa/Monrovia","Africa/Nairobi","Africa/Ndjamena","Africa/Niamey","Africa/Nouakchott","Africa/Ouagadougou","Africa/Porto-Novo","Africa/Sao_Tome","Africa/Timbuktu","Africa/Tripoli","Africa/Tunis","Africa/Windhoek","America/Adak","America/Anchorage","America/Anguilla","America/Antigua","America/Araguaina","America/Argentina/Buenos_Aires","America/Argentina/Catamarca","America/Argentina/ComodRivadavia","America/Argentina/Cordoba","America/Argentina/Jujuy","America/Argentina/La_Rioja","America/Argentina/Mendoza","America/Argentina/Rio_Gallegos","America/Argentina/Salta","America/Argentina/San_Juan","America/Argentina/San_Luis","America/Argentina/Tucuman","America/Argentina/Ushuaia","America/Aruba","America/Asuncion","America/Atikokan","America/Atka","America/Bahia","America/Bahia_Banderas","America/Barbados","America/Belem","America/Belize","America/Blanc-Sablon","America/Boa_Vista","America/Bogota","America/Boise","America/Buenos_Aires","America/Cambridge_Bay","America/Campo_Grande","America/Cancun","America/Caracas","America/Catamarca","America/Cayenne","America/Cayman","America/Chicago","America/Chihuahua","America/Coral_Harbour","America/Cordoba","America/Costa_Rica","America/Creston","America/Cuiaba","America/Curacao","America/Danmarkshavn","America/Dawson","America/Dawson_Creek","America/Denver","America/Detroit","America/Dominica","America/Edmonton","America/Eirunepe","America/El_Salvador","America/Ensenada","America/Fort_Wayne","America/Fortaleza","America/Glace_Bay","America/Godthab","America/Goose_Bay","America/Grand_Turk","America/Grenada","America/Guadeloupe","America/Guatemala","America/Guayaquil","America/Guyana","America/Halifax","America/Havana","America/Hermosillo","America/Indiana/Indianapolis","America/Indiana/Knox","America/Indiana/Marengo","America/Indiana/Petersburg","America/Indiana/Tell_City","America/Indiana/Vevay","America/Indiana/Vincennes","America/Indiana/Winamac","America/Indianapolis","America/Inuvik","America/Iqaluit","America/Jamaica","America/Jujuy","America/Juneau","America/Kentucky/Louisville","America/Kentucky/Monticello","America/Knox_IN","America/Kralendijk","America/La_Paz","America/Lima","America/Los_Angeles","America/Louisville","America/Lower_Princes","America/Maceio","America/Managua","America/Manaus","America/Marigot","America/Martinique","America/Matamoros","America/Mazatlan","America/Mendoza","America/Menominee","America/Merida","America/Metlakatla","America/Mexico_City","America/Miquelon","America/Moncton","America/Monterrey","America/Montevideo","America/Montreal","America/Montserrat","America/Nassau","America/New_York","America/Nipigon","America/Nome","America/Noronha","America/North_Dakota/Beulah","America/North_Dakota/Center","America/North_Dakota/New_Salem","America/Ojinaga","America/Panama","America/Pangnirtung","America/Paramaribo","America/Phoenix","America/Port-au-Prince","America/Port_of_Spain","America/Porto_Acre","America/Porto_Velho","America/Puerto_Rico","America/Rainy_River","America/Rankin_Inlet","America/Recife","America/Regina","America/Resolute","America/Rio_Branco","America/Rosario","America/Santa_Isabel","America/Santarem","America/Santiago","America/Santo_Domingo","America/Sao_Paulo","America/Scoresbysund","America/Shiprock","America/Sitka","America/St_Barthelemy","America/St_Johns","America/St_Kitts","America/St_Lucia","America/St_Thomas","America/St_Vincent","America/Swift_Current","America/Tegucigalpa","America/Thule","America/Thunder_Bay","America/Tijuana","America/Toronto","America/Tortola","America/Vancouver","America/Virgin","America/Whitehorse","America/Winnipeg","America/Yakutat","America/Yellowknife","Antarctica/Casey","Antarctica/Davis","Antarctica/DumontDUrville","Antarctica/Macquarie","Antarctica/Mawson","Antarctica/McMurdo","Antarctica/Palmer","Antarctica/Rothera","Antarctica/South_Pole","Antarctica/Syowa","Antarctica/Vostok","Arctic/Longyearbyen","Asia/Aden","Asia/Almaty","Asia/Amman","Asia/Anadyr","Asia/Aqtau","Asia/Aqtobe","Asia/Ashgabat","Asia/Ashkhabad","Asia/Baghdad","Asia/Bahrain","Asia/Baku","Asia/Bangkok","Asia/Beirut","Asia/Bishkek","Asia/Brunei","Asia/Calcutta","Asia/Choibalsan","Asia/Chongqing","Asia/Chungking","Asia/Colombo","Asia/Dacca","Asia/Damascus","Asia/Dhaka","Asia/Dili","Asia/Dubai","Asia/Dushanbe","Asia/Gaza","Asia/Harbin","Asia/Hebron","Asia/Ho_Chi_Minh","Asia/Hong_Kong","Asia/Hovd","Asia/Irkutsk","Asia/Istanbul","Asia/Jakarta","Asia/Jayapura","Asia/Jerusalem","Asia/Kabul","Asia/Kamchatka","Asia/Karachi","Asia/Kashgar","Asia/Kathmandu","Asia/Katmandu","Asia/Khandyga","Asia/Kolkata","Asia/Krasnoyarsk","Asia/Kuala_Lumpur","Asia/Kuching","Asia/Kuwait","Asia/Macao","Asia/Macau","Asia/Magadan","Asia/Makassar","Asia/Manila","Asia/Muscat","Asia/Nicosia","Asia/Novokuznetsk","Asia/Novosibirsk","Asia/Omsk","Asia/Oral","Asia/Phnom_Penh","Asia/Pontianak","Asia/Pyongyang","Asia/Qatar","Asia/Qyzylorda","Asia/Rangoon","Asia/Riyadh","Asia/Saigon","Asia/Sakhalin","Asia/Samarkand","Asia/Seoul","Asia/Shanghai","Asia/Singapore","Asia/Taipei","Asia/Tashkent","Asia/Tbilisi","Asia/Tehran","Asia/Tel_Aviv","Asia/Thimbu","Asia/Thimphu","Asia/Tokyo","Asia/Ujung_Pandang","Asia/Ulaanbaatar","Asia/Ulan_Bator","Asia/Urumqi","Asia/Ust-Nera","Asia/Vientiane","Asia/Vladivostok","Asia/Yakutsk","Asia/Yekaterinburg","Asia/Yerevan","Atlantic/Azores","Atlantic/Bermuda","Atlantic/Canary","Atlantic/Cape_Verde","Atlantic/Faeroe","Atlantic/Faroe","Atlantic/Jan_Mayen","Atlantic/Madeira","Atlantic/Reykjavik","Atlantic/South_Georgia","Atlantic/St_Helena","Atlantic/Stanley","Australia/ACT","Australia/Adelaide","Australia/Brisbane","Australia/Broken_Hill","Australia/Canberra","Australia/Currie","Australia/Darwin","Australia/Eucla","Australia/Hobart","Australia/LHI","Australia/Lindeman","Australia/Lord_Howe","Australia/Melbourne","Australia/NSW","Australia/North","Australia/Perth","Australia/Queensland","Australia/South","Australia/Sydney","Australia/Tasmania","Australia/Victoria","Australia/West","Australia/Yancowinna","Brazil/Acre","Brazil/DeNoronha","Brazil/East","Brazil/West","Canada/Atlantic","Canada/Central","Canada/East-Saskatchewan","Canada/Eastern","Canada/Mountain","Canada/Newfoundland","Canada/Pacific","Canada/Saskatchewan","Canada/Yukon","Chile/Continental","Chile/EasterIsland","Europe/Amsterdam","Europe/Andorra","Europe/Athens","Europe/Belfast","Europe/Belgrade","Europe/Berlin","Europe/Bratislava","Europe/Brussels","Europe/Bucharest","Europe/Budapest","Europe/Busingen","Europe/Chisinau","Europe/Copenhagen","Europe/Dublin","Europe/Gibraltar","Europe/Guernsey","Europe/Helsinki","Europe/Isle_of_Man","Europe/Istanbul","Europe/Jersey","Europe/Kaliningrad","Europe/Kiev","Europe/Lisbon","Europe/Ljubljana","Europe/London","Europe/Luxembourg","Europe/Madrid","Europe/Malta","Europe/Mariehamn","Europe/Minsk","Europe/Monaco","Europe/Moscow","Europe/Nicosia","Europe/Oslo","Europe/Paris","Europe/Podgorica","Europe/Prague","Europe/Riga","Europe/Rome","Europe/Samara","Europe/San_Marino","Europe/Sarajevo","Europe/Simferopol","Europe/Skopje","Europe/Sofia","Europe/Stockholm","Europe/Tallinn","Europe/Tirane","Europe/Tiraspol","Europe/Uzhgorod","Europe/Vaduz","Europe/Vatican","Europe/Vienna","Europe/Vilnius","Europe/Volgograd","Europe/Warsaw","Europe/Zagreb","Europe/Zaporozhye","Europe/Zurich","Indian/Antananarivo","Indian/Chagos","Indian/Christmas","Indian/Cocos","Indian/Comoro","Indian/Kerguelen","Indian/Mahe","Indian/Maldives","Indian/Mauritius","Indian/Mayotte","Indian/Reunion","Mexico/BajaNorte","Mexico/BajaSur","Mexico/General","Pacific/Apia","Pacific/Auckland","Pacific/Chatham","Pacific/Chuuk","Pacific/Easter","Pacific/Efate","Pacific/Enderbury","Pacific/Fakaofo","Pacific/Fiji","Pacific/Funafuti","Pacific/Galapagos","Pacific/Gambier","Pacific/Guadalcanal","Pacific/Guam","Pacific/Honolulu","Pacific/Johnston","Pacific/Kiritimati","Pacific/Kosrae","Pacific/Kwajalein","Pacific/Majuro","Pacific/Marquesas","Pacific/Midway","Pacific/Nauru","Pacific/Niue","Pacific/Norfolk","Pacific/Noumea","Pacific/Pago_Pago","Pacific/Palau","Pacific/Pitcairn","Pacific/Pohnpei","Pacific/Ponape","Pacific/Port_Moresby","Pacific/Rarotonga","Pacific/Saipan","Pacific/Samoa","Pacific/Tahiti","Pacific/Tarawa","Pacific/Tongatapu","Pacific/Truk","Pacific/Wake","Pacific/Wallis","Pacific/Yap"],AF:["Asia/Kabul"],AX:["Europe/Mariehamn"],AL:["Europe/Tirane"],DZ:["Africa/Algiers"],AS:["Pacific/Pago_Pago"],AD:["Europe/Andorra"],AO:["Africa/Luanda"],AI:["America/Anguilla"],AQ:["Antarctica/McMurdo","Antarctica/South_Pole","Antarctica/Rothera","Antarctica/Palmer","Antarctica/Mawson","Antarctica/Davis","Antarctica/Casey","Antarctica/Vostok","Antarctica/DumontDUrville","Antarctica/Syowa"],AG:["America/Antigua"],AR:["America/Argentina/Buenos_Aires","America/Argentina/Cordoba","America/Argentina/Salta","America/Argentina/Jujuy","America/Argentina/Tucuman","America/Argentina/Catamarca","America/Argentina/La_Rioja","America/Argentina/San_Juan","America/Argentina/Mendoza","America/Argentina/San_Luis","America/Argentina/Rio_Gallegos","America/Argentina/Ushuaia"],AM:["Asia/Yerevan"],AW:["America/Aruba"],AU:["Australia/Lord_Howe","Antarctica/Macquarie","Australia/Hobart","Australia/Currie","Australia/Melbourne","Australia/Sydney","Australia/Broken_Hill","Australia/Brisbane","Australia/Lindeman","Australia/Adelaide","Australia/Darwin","Australia/Perth","Australia/Eucla"],AT:["Europe/Vienna"],AZ:["Asia/Baku"],BS:["America/Nassau"],BH:["Asia/Bahrain"],BD:["Asia/Dhaka"],BB:["America/Barbados"],BY:["Europe/Minsk"],BE:["Europe/Brussels"],BZ:["America/Belize"],BJ:["Africa/Porto-Novo"],BM:["Atlantic/Bermuda"],BT:["Asia/Thimphu"],BO:["America/La_Paz"],BA:["Europe/Sarajevo"],BW:["Africa/Gaborone"],BV:["None"],BR:["America/Noronha","America/Belem","America/Fortaleza","America/Recife","America/Araguaina","America/Maceio","America/Bahia","America/Sao_Paulo","America/Campo_Grande","America/Cuiaba","America/Santarem","America/Porto_Velho","America/Boa_Vista","America/Manaus","America/Eirunepe","America/Rio_Branco"],IO:["Indian/Chagos"],BN:["Asia/Brunei"],BG:["Europe/Sofia"],BF:["Africa/Ouagadougou"],BI:["Africa/Bujumbura"],KH:["Asia/Phnom_Penh"],CM:["Africa/Douala"],CA:["America/St_Johns","America/Halifax","America/Glace_Bay","America/Moncton","America/Goose_Bay","America/Blanc-Sablon","America/Montreal","America/Toronto","America/Nipigon","America/Thunder_Bay","America/Iqaluit","America/Pangnirtung","America/Resolute","America/Atikokan","America/Rankin_Inlet","America/Winnipeg","America/Rainy_River","America/Regina","America/Swift_Current","America/Edmonton","America/Cambridge_Bay","America/Yellowknife","America/Inuvik","America/Creston","America/Dawson_Creek","America/Vancouver","America/Whitehorse","America/Dawson"],CV:["Atlantic/Cape_Verde"],KY:["America/Cayman"],CF:["Africa/Bangui"],TD:["Africa/Ndjamena"],CL:["America/Santiago","Pacific/Easter"],CN:["Asia/Shanghai","Asia/Harbin","Asia/Chongqing","Asia/Urumqi","Asia/Kashgar"],CX:["Indian/Christmas"],CC:["Indian/Cocos"],CO:["America/Bogota"],KM:["Indian/Comoro"],CG:["Africa/Brazzaville"],CD:["Africa/Kinshasa","Africa/Lubumbashi"],CK:["Pacific/Rarotonga"],CR:["America/Costa_Rica"],CI:["Africa/Abidjan"],HR:["Europe/Zagreb"],CU:["America/Havana"],CY:["Asia/Nicosia"],CZ:["Europe/Prague"],DK:["Europe/Copenhagen"],DJ:["Africa/Djibouti"],DM:["America/Dominica"],DO:["America/Santo_Domingo"],EC:["America/Guayaquil","Pacific/Galapagos"],EG:["Africa/Cairo"],SV:["America/El_Salvador"],GQ:["Africa/Malabo"],ER:["Africa/Asmara"],EE:["Europe/Tallinn"],ET:["Africa/Addis_Ababa"],FK:["Atlantic/Stanley"],FO:["Atlantic/Faroe"],FJ:["Pacific/Fiji"],FI:["Europe/Helsinki"],FR:["Europe/Paris"],GF:["America/Cayenne"],PF:["Pacific/Tahiti","Pacific/Marquesas","Pacific/Gambier"],TF:["Indian/Kerguelen"],GA:["Africa/Libreville"],GM:["Africa/Banjul"],GE:["Asia/Tbilisi"],DE:["Europe/Berlin","Europe/Busingen"],GH:["Africa/Accra"],GI:["Europe/Gibraltar"],GR:["Europe/Athens"],GL:["America/Godthab","America/Danmarkshavn","America/Scoresbysund","America/Thule"],GD:["America/Grenada"],GP:["America/Guadeloupe"],GU:["Pacific/Guam"],GT:["America/Guatemala"],GG:["Europe/Guernsey"],GN:["Africa/Conakry"],GW:["Africa/Bissau"],GY:["America/Guyana"],HT:["America/Port-au-Prince"],HM:["None"],VA:["Europe/Vatican"],HN:["America/Tegucigalpa"],HK:["Asia/Hong_Kong"],HU:["Europe/Budapest"],IS:["Atlantic/Reykjavik"],IN:["Asia/Kolkata"],ID:["Asia/Jakarta","Asia/Pontianak","Asia/Makassar","Asia/Jayapura"],IR:["Asia/Tehran"],IQ:["Asia/Baghdad"],IE:["Europe/Dublin"],IM:["Europe/Isle_of_Man"],IL:["Asia/Jerusalem"],IT:["Europe/Rome"],JM:["America/Jamaica"],JP:["Asia/Tokyo"],JE:["Europe/Jersey"],JO:["Asia/Amman"],KZ:["Asia/Almaty","Asia/Qyzylorda","Asia/Aqtobe","Asia/Aqtau","Asia/Oral"],KE:["Africa/Nairobi"],KI:["Pacific/Tarawa","Pacific/Enderbury","Pacific/Kiritimati"],KP:["Asia/Pyongyang"],KR:["Asia/Seoul"],KW:["Asia/Kuwait"],KG:["Asia/Bishkek"],LA:["Asia/Vientiane"],LV:["Europe/Riga"],LB:["Asia/Beirut"],LS:["Africa/Maseru"],LR:["Africa/Monrovia"],LY:["Africa/Tripoli"],LI:["Europe/Vaduz"],LT:["Europe/Vilnius"],LU:["Europe/Luxembourg"],MO:["Asia/Macau"],MK:["Europe/Skopje"],MG:["Indian/Antananarivo"],MW:["Africa/Blantyre"],MY:["Asia/Kuala_Lumpur","Asia/Kuching"],MV:["Indian/Maldives"],ML:["Africa/Bamako"],MT:["Europe/Malta"],MH:["Pacific/Majuro","Pacific/Kwajalein"],MQ:["America/Martinique"],MR:["Africa/Nouakchott"],MU:["Indian/Mauritius"],YT:["Indian/Mayotte"],MX:["America/Mexico_City","America/Cancun","America/Merida","America/Monterrey","America/Matamoros","America/Mazatlan","America/Chihuahua","America/Ojinaga","America/Hermosillo","America/Tijuana","America/Santa_Isabel","America/Bahia_Banderas"],FM:["Pacific/Chuuk","Pacific/Pohnpei","Pacific/Kosrae"],MD:["Europe/Chisinau"],MC:["Europe/Monaco"],MN:["Asia/Ulaanbaatar","Asia/Hovd","Asia/Choibalsan"],ME:["Europe/Podgorica"],MS:["America/Montserrat"],MA:["Africa/Casablanca"],MZ:["Africa/Maputo"],MM:["Asia/Rangoon"],NA:["Africa/Windhoek"],NR:["Pacific/Nauru"],NP:["Asia/Kathmandu"],NL:["Europe/Amsterdam"],AN:["None"],NC:["Pacific/Noumea"],NZ:["Pacific/Auckland","Pacific/Chatham"],NI:["America/Managua"],NE:["Africa/Niamey"],NG:["Africa/Lagos"],NU:["Pacific/Niue"],NF:["Pacific/Norfolk"],MP:["Pacific/Saipan"],NO:["Europe/Oslo"],OM:["Asia/Muscat"],PK:["Asia/Karachi"],PW:["Pacific/Palau"],PS:["Asia/Gaza","Asia/Hebron"],PA:["America/Panama"],PG:["Pacific/Port_Moresby"],PY:["America/Asuncion"],PE:["America/Lima"],PH:["Asia/Manila"],PN:["Pacific/Pitcairn"],PL:["Europe/Warsaw"],PT:["Europe/Lisbon","Atlantic/Madeira","Atlantic/Azores"],PR:["America/Puerto_Rico"],QA:["Asia/Qatar"],RE:["Indian/Reunion"],RO:["Europe/Bucharest"],RU:["Europe/Kaliningrad","Europe/Moscow","Europe/Volgograd","Europe/Samara","Asia/Yekaterinburg","Asia/Omsk","Asia/Novosibirsk","Asia/Novokuznetsk","Asia/Krasnoyarsk","Asia/Irkutsk","Asia/Yakutsk","Asia/Khandyga","Asia/Vladivostok","Asia/Sakhalin","Asia/Ust-Nera","Asia/Magadan","Asia/Kamchatka","Asia/Anadyr"],RW:["Africa/Kigali"],BL:["America/St_Barthelemy"],SH:["Atlantic/St_Helena"],KN:["America/St_Kitts"],LC:["America/St_Lucia"],MF:["America/Marigot"],PM:["America/Miquelon"],VC:["America/St_Vincent"],WS:["Pacific/Apia"],SM:["Europe/San_Marino"],ST:["Africa/Sao_Tome"],SA:["Asia/Riyadh"],SN:["Africa/Dakar"],RS:["Europe/Belgrade"],SC:["Indian/Mahe"],SL:["Africa/Freetown"],SG:["Asia/Singapore"],SK:["Europe/Bratislava"],SI:["Europe/Ljubljana"],SB:["Pacific/Guadalcanal"],SO:["Africa/Mogadishu"],ZA:["Africa/Johannesburg"],GS:["Atlantic/South_Georgia"],ES:["Europe/Madrid","Africa/Ceuta","Atlantic/Canary"],LK:["Asia/Colombo"],SD:["Africa/Khartoum"],SR:["America/Paramaribo"],SJ:["Arctic/Longyearbyen"],SZ:["Africa/Mbabane"],SE:["Europe/Stockholm"],CH:["Europe/Zurich"],SY:["Asia/Damascus"],TW:["Asia/Taipei"],TJ:["Asia/Dushanbe"],TZ:["Africa/Dar_es_Salaam"],TH:["Asia/Bangkok"],TL:["Asia/Dili"],TG:["Africa/Lome"],TK:["Pacific/Fakaofo"],TO:["Pacific/Tongatapu"],TT:["America/Port_of_Spain"],TN:["Africa/Tunis"],TR:["Europe/Istanbul"],TM:["Asia/Ashgabat"],TC:["America/Grand_Turk"],TV:["Pacific/Funafuti"],UG:["Africa/Kampala"],UA:["Europe/Kiev","Europe/Uzhgorod","Europe/Zaporozhye","Europe/Simferopol"],AE:["Asia/Dubai"],GB:["Europe/London"],US:["America/New_York","America/Detroit","America/Kentucky/Louisville","America/Kentucky/Monticello","America/Indiana/Indianapolis","America/Indiana/Vincennes","America/Indiana/Winamac","America/Indiana/Marengo","America/Indiana/Petersburg","America/Indiana/Vevay","America/Chicago","America/Indiana/Tell_City","America/Indiana/Knox","America/Menominee","America/North_Dakota/Center","America/North_Dakota/New_Salem","America/North_Dakota/Beulah","America/Denver","America/Boise","America/Shiprock","America/Phoenix","America/Los_Angeles","America/Anchorage","America/Juneau","America/Sitka","America/Yakutat","America/Nome","America/Adak","America/Metlakatla","Pacific/Honolulu"],UM:["Pacific/Johnston","Pacific/Midway","Pacific/Wake"],UY:["America/Montevideo"],UZ:["Asia/Samarkand","Asia/Tashkent"],VU:["Pacific/Efate"],VE:["America/Caracas"],VN:["Asia/Ho_Chi_Minh"],VG:["America/Tortola"],VI:["America/St_Thomas"],WF:["Pacific/Wallis"],EH:["Africa/El_Aaiun"],YE:["Asia/Aden"],ZM:["Africa/Lusaka"],ZW:["Africa/Harare"]},n=[{icon:"sealogs-sl-boat1.svg",title:"sealogs sl boat1"},{icon:"sealogs-sl-boat2.svg",title:"sealogs sl boat2"}]},24894:(a,e,i)=>{i.d(e,{Z:()=>v});var r=i(98768),l=i(60343),n=i(28147),c=i(79418),o=i(72548),u=i(67537),A=i(94060),t=i(76342),s=i(56937),m=i(71890),b=i(60797),d=i(25394);function v({setDocuments:a,text:e="Documents and Images",subText:i,bgClass:v="",documents:f,multipleUpload:p=!0}){let[g,h]=(0,l.useState)(!1),[M,S]=(0,l.useState)([]),[E,P]=(0,l.useState)(!1),[C,B]=(0,l.useState)(!1),k=(0,l.useRef)(null),y=(0,s.cn)("relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none",g?"bg-accent border-primary":"bg-accent/50 border-border","text-foreground hover:bg-accent hover:border-primary","min-h-[10rem] cursor-pointer select-none",v),I=async a=>{let e=new FormData;e.append("FileData",a,a.name.replace(/\s/g,""));try{let a=await fetch("https://api.sealogs.com/api/v2/upload",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("sl-jwt")}`},body:e}),i=await a.json();await T({variables:{id:[i[0].id]}}),B(!1)}catch(a){console.error(a)}},[T]=(0,c.t)(A.ZX,{fetchPolicy:"cache-and-network",onCompleted:a=>{S(e=>[...e,a.readFiles.nodes[0]]),P(!0)},onError:a=>console.error(a)}),[N]=(0,o.D)(t.RgS,{onCompleted:e=>{let i=e.updateFile;a(a=>p?[...a,i]:[i])},onError:a=>console.error(a)}),_=a=>{let e=Array.from(a);B(!0),e.forEach(I)},G=a=>e=>{e.preventDefault(),h(a)};return(0,r.jsxs)("div",{className:"w-full pt-4 lg:pt-0",children:[(0,r.jsxs)("form",{className:y,onSubmit:a=>a.preventDefault(),onDragEnter:G(!0),onDragOver:G(!0),onDragLeave:G(!1),onDrop:a=>{a.preventDefault(),h(!1),a.dataTransfer.files&&_(a.dataTransfer.files)},onClick:()=>k.current?.click(),"aria-label":"File uploader drop zone",children:[r.jsx("span",{className:"absolute top-4 left-4 text-xs font-medium uppercase tracking-wider",children:e}),r.jsx(m.I,{ref:k,type:"file",className:"hidden",multiple:p,accept:".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf",onChange:a=>{a.target.files&&_(a.target.files)}}),(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2 pointer-events-none",children:[r.jsx(n.default,{src:"/sealogs-document_upload.svg",alt:"Upload illustration",width:96,height:96,className:"relative -translate-x-2.5",priority:!0}),i&&r.jsx("span",{className:"text-sm font-medium text-neutral-400",children:i})]})]}),C?(0,r.jsxs)("div",{className:"mt-4 flex items-center justify-center gap-2",children:[r.jsx(u.Z,{className:"h-5 w-5 animate-spin text-primary"}),r.jsx("span",{className:"text-sm text-muted-foreground",children:"Uploading..."})]}):r.jsx(d.h9,{openDialog:E,setOpenDialog:P,handleCreate:()=>{M.forEach((a,e)=>{let i=document.getElementById(`file-name-${e}`).value;N({variables:{input:{id:a.id,title:i}}})}),P(!1)},actionText:"Save",title:"File Name",children:r.jsx("div",{className:"space-y-4",children:M.map((a,e)=>r.jsx(b.Label,{label:`File ${e+1} Name`,htmlFor:`file-name-${e}`,children:r.jsx(m.I,{id:`file-name-${e}`,defaultValue:a.title,placeholder:"Enter file name"})},a.id))})})]})}}};