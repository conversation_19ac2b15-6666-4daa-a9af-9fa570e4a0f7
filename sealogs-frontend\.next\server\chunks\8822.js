exports.id=8822,exports.ids=[8822],exports.modules={47520:(e,t,r)=>{"use strict";r.d(t,{default:()=>n.a});var l=r(19821),n=r.n(l)},19821:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let l=r(41034);r(98768),r(60343);let n=l._(r(40907));function u(e,t){var r;let l={loading:e=>{let{error:t,isLoading:r,pastDelay:l}=e;return null}};"function"==typeof e&&(l.loader=e);let u={...l,...t};return(0,n.default)({...u,modules:null==(r=u.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96359:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}});let l=r(90408);function n(e){let{reason:t,children:r}=e;throw new l.BailoutToCSRError(t)}},40907:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let l=r(98768),n=r(60343),u=r(96359),s=r(58902);function a(e){return{default:e&&"default"in e?e.default:e}}let i={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},o=function(e){let t={...i,...e},r=(0,n.lazy)(()=>t.loader().then(a)),o=t.loading;function f(e){let a=o?(0,l.jsx)(o,{isLoading:!0,pastDelay:!0,error:null}):null,i=t.ssr?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(s.PreloadCss,{moduleIds:t.modules}),(0,l.jsx)(r,{...e})]}):(0,l.jsx)(u.BailoutToCSR,{reason:"next/dynamic",children:(0,l.jsx)(r,{...e})});return(0,l.jsx)(n.Suspense,{fallback:a,children:i})}return f.displayName="LoadableComponent",f}},58902:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return u}});let l=r(98768),n=r(54580);function u(e){let{moduleIds:t}=e,r=(0,n.getExpectedRequestStore)("next/dynamic css"),u=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files.filter(e=>e.endsWith(".css"));u.push(...t)}}return 0===u.length?null:(0,l.jsx)(l.Fragment,{children:u.map(e=>(0,l.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:r.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},30854:()=>{},13006:(e,t,r)=>{"use strict";r.d(t,{AE:()=>p,v1:()=>g});var l=r(73586);function n(e,t,r){try{return e(t)}catch(e){return(0,l.ZK)("[nuqs] Error while parsing value `%s`: %O"+(r?" (for key `%s`)":""),t,e,r),null}}var u=function(){if("undefined"==typeof window||!window.GestureEvent)return 50;try{let e=navigator.userAgent?.match(/version\/([\d\.]+) safari/i);return parseFloat(e[1])>=17?120:320}catch{return 320}}(),s=new Map,a={history:"replace",scroll:!1,shallow:!0,throttleMs:u},i=new Set,o=0,f=null,c=r(60343);function d(e){function t(t){if(void 0===t)return null;let r="";if(Array.isArray(t)){if(void 0===t[0])return null;r=t[0]}return"string"==typeof t&&(r=t),n(e.parse,r)}return{eq:(e,t)=>e===t,...e,parseServerSide:t,withDefault(e){return{...this,defaultValue:e,parseServerSide:r=>t(r)??e}},withOptions(e){return{...this,...e}}}}d({parse:e=>e,serialize:e=>`${e}`});var h=d({parse:e=>{let t=parseInt(e);return Number.isNaN(t)?null:t},serialize:e=>Math.round(e).toFixed()});d({parse:e=>{let t=h.parse(e);return null===t?null:t-1},serialize:e=>h.serialize(e+1)}),d({parse:e=>{let t=parseInt(e,16);return Number.isNaN(t)?null:t},serialize:e=>{let t=Math.round(e).toString(16);return t.padStart(t.length+t.length%2,"0")}}),d({parse:e=>{let t=parseFloat(e);return Number.isNaN(t)?null:t},serialize:e=>e.toString()});var p=d({parse:e=>"true"===e,serialize:e=>e?"true":"false"});function m(e,t){return e.valueOf()===t.valueOf()}d({parse:e=>{let t=parseInt(e);return Number.isNaN(t)?null:new Date(t)},serialize:e=>e.valueOf().toString(),eq:m}),d({parse:e=>{let t=new Date(e);return Number.isNaN(t.valueOf())?null:t},serialize:e=>e.toISOString(),eq:m}),d({parse:e=>{let t=new Date(e.slice(0,10));return Number.isNaN(t.valueOf())?null:t},serialize:e=>e.toISOString().slice(0,10),eq:m});var y=function(e){return{all:e=e||new Map,on:function(t,r){var l=e.get(t);l?l.push(r):e.set(t,[r])},off:function(t,r){var l=e.get(t);l&&(r?l.splice(l.indexOf(r)>>>0,1):e.set(t,[]))},emit:function(t,r){var l=e.get(t);l&&l.slice().map(function(e){e(r)}),(l=e.get("*"))&&l.slice().map(function(e){e(t,r)})}}}();function g(e,{history:t="replace",shallow:r=!0,scroll:d=!1,throttleMs:h=u,parse:p=e=>e,serialize:m=String,eq:g=(e,t)=>e===t,defaultValue:v,clearOnDefault:w=!0,startTransition:b}={history:"replace",scroll:!1,shallow:!0,throttleMs:u,parse:e=>e,serialize:String,eq:(e,t)=>e===t,clearOnDefault:!0,defaultValue:void 0}){let M=(0,l.YW)(),S=M.searchParams;(0,c.useRef)(S?.get(e)??null);let[O,x]=(0,c.useState)(()=>{let t=s.get(e),r=void 0===t?S?.get(e)??null:t;return null===r?null:n(p,r,e)}),j=(0,c.useRef)(O);(0,l.fF)("[nuqs `%s`] render - state: %O, iSP: %s",e,O,S?.get(e)??null);let N=(0,c.useCallback)((n,c={})=>{let p="function"==typeof n?n(j.current??v??null):n;(c.clearOnDefault??w)&&null!==p&&void 0!==v&&g(p,v)&&(p=null);let S=function(e,t,r,n){let o=null===t?null:r(t);return(0,l.fF)("[nuqs queue] Enqueueing %s=%s %O",e,o,n),s.set(e,o),"push"===n.history&&(a.history="push"),n.scroll&&(a.scroll=!0),!1===n.shallow&&(a.shallow=!1),n.startTransition&&i.add(n.startTransition),a.throttleMs=Math.max(n.throttleMs??u,Number.isFinite(a.throttleMs)?a.throttleMs:0),o}(e,p,m,{history:c.history??t,shallow:c.shallow??r,scroll:c.scroll??d,throttleMs:c.throttleMs??h,startTransition:c.startTransition??b});return y.emit(e,{state:p,query:S}),function({getSearchParamsSnapshot:e=function(){return new URLSearchParams(location.search)},updateUrl:t,rateLimitFactor:r=1}){return null===f&&(f=new Promise((n,c)=>{if(!Number.isFinite(a.throttleMs)){(0,l.fF)("[nuqs queue] Skipping flush due to throttleMs=Infinity"),n(e()),setTimeout(()=>{f=null},0);return}function d(){o=performance.now();let[r,d]=function({updateUrl:e,getSearchParamsSnapshot:t}){let r=t();if(0===s.size)return[r,null];let n=Array.from(s.entries()),o={...a},f=Array.from(i);for(let[e,t]of(s.clear(),i.clear(),a.history="replace",a.scroll=!1,a.shallow=!0,a.throttleMs=u,(0,l.fF)("[nuqs queue] Flushing queue %O with options %O",n,o),n))null===t?r.delete(e):r.set(e,t);try{return function(e,t){let r=l=>{if(l===e.length)return t();let n=e[l];if(!n)throw Error("Invalid transition function");n(()=>r(l+1))};r(0)}(f,()=>{e(r,{history:o.history,scroll:o.scroll,shallow:o.shallow})}),[r,null]}catch(e){return console.error((0,l.vU)(429),n.map(([e])=>e).join(),e),[r,e]}}({updateUrl:t,getSearchParamsSnapshot:e});null===d?n(r):c(r),f=null}setTimeout(function(){let e=performance.now()-o,t=a.throttleMs,n=r*Math.max(0,Math.min(t,t-e));(0,l.fF)("[nuqs queue] Scheduling flush in %f ms. Throttled at %f ms",n,t),0===n?d():setTimeout(d,n)},0)})),f}(M)},[e,t,r,d,h,b,M.updateUrl,M.getSearchParamsSnapshot,M.rateLimitFactor]);return[O??v??null,N]}}};