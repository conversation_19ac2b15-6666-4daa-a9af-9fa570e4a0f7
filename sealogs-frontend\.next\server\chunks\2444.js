exports.id=2444,exports.ids=[2444],exports.modules={92229:(e,t,r)=>{Promise.resolve().then(r.bind(r,40043))},60826:e=>{e.exports=function(e){return e.split("")}},829:e=>{e.exports=function(e,t,r,n){for(var i=e.length,a=r+(n?1:-1);n?a--:++a<i;)if(t(e[a],a,e))return a;return -1}},65337:(e,t,r)=>{var n=r(829),i=r(35447),a=r(28026);e.exports=function(e,t,r){return t==t?a(e,t,r):n(e,i,r)}},35447:e=>{e.exports=function(e){return e!=e}},77420:e=>{e.exports=function(e,t,r){var n=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(r=r>i?i:r)<0&&(r+=i),i=t>r?0:r-t>>>0,t>>>=0;for(var a=Array(i);++n<i;)a[n]=e[n+t];return a}},49513:(e,t,r)=>{var n=r(70458),i=/^\s+/;e.exports=function(e){return e?e.slice(0,n(e)+1).replace(i,""):e}},30482:(e,t,r)=>{var n=r(77420);e.exports=function(e,t,r){var i=e.length;return r=void 0===r?i:r,!t&&r>=i?e:n(e,t,r)}},74783:(e,t,r)=>{var n=r(65337);e.exports=function(e,t){for(var r=e.length;r--&&n(t,e[r],0)>-1;);return r}},41200:(e,t,r)=>{var n=r(65337);e.exports=function(e,t){for(var r=-1,i=e.length;++r<i&&n(t,e[r],0)>-1;);return r}},73211:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},28026:e=>{e.exports=function(e,t,r){for(var n=r-1,i=e.length;++n<i;)if(e[n]===t)return n;return -1}},66095:(e,t,r)=>{var n=r(60826),i=r(73211),a=r(92115);e.exports=function(e){return i(e)?a(e):n(e)}},70458:e=>{var t=/\s/;e.exports=function(e){for(var r=e.length;r--&&t.test(e.charAt(r)););return r}},92115:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",i="[^"+t+"]",a="(?:\ud83c[\udde6-\uddff]){2}",l="[\ud800-\udbff][\udc00-\udfff]",s="(?:"+r+"|"+n+")?",d="[\\ufe0e\\ufe0f]?",o="(?:\\u200d(?:"+[i,a,l].join("|")+")"+d+s+")*",c=RegExp(n+"(?="+n+")|(?:"+[i+r+"?",r,a,l,"["+t+"]"].join("|")+")"+(d+s+o),"g");e.exports=function(e){return e.match(c)||[]}},14826:(e,t,r)=>{var n=r(22060),i=r(49513),a=r(30482),l=r(74783),s=r(41200),d=r(66095),o=r(16266);e.exports=function(e,t,r){if((e=o(e))&&(r||void 0===t))return i(e);if(!e||!(t=n(t)))return e;var c=d(e),u=d(t),p=s(c,u),h=l(c,u)+1;return a(c,p,h).join("")}},47520:(e,t,r)=>{"use strict";r.d(t,{default:()=>i.a});var n=r(19821),i=r.n(n)},19821:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=r(41034);r(98768),r(60343);let i=n._(r(40907));function a(e,t){var r;let n={loading:e=>{let{error:t,isLoading:r,pastDelay:n}=e;return null}};"function"==typeof e&&(n.loader=e);let a={...n,...t};return(0,i.default)({...a,modules:null==(r=a.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96359:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return i}});let n=r(90408);function i(e){let{reason:t,children:r}=e;throw new n.BailoutToCSRError(t)}},40907:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(98768),i=r(60343),a=r(96359),l=r(58902);function s(e){return{default:e&&"default"in e?e.default:e}}let d={loader:()=>Promise.resolve(s(()=>null)),loading:null,ssr:!0},o=function(e){let t={...d,...e},r=(0,i.lazy)(()=>t.loader().then(s)),o=t.loading;function c(e){let s=o?(0,n.jsx)(o,{isLoading:!0,pastDelay:!0,error:null}):null,d=t.ssr?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(l.PreloadCss,{moduleIds:t.modules}),(0,n.jsx)(r,{...e})]}):(0,n.jsx)(a.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(i.Suspense,{fallback:s,children:d})}return c.displayName="LoadableComponent",c}},58902:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return a}});let n=r(98768),i=r(54580);function a(e){let{moduleIds:t}=e,r=(0,i.getExpectedRequestStore)("next/dynamic css"),a=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files.filter(e=>e.endsWith(".css"));a.push(...t)}}return 0===a.length?null:(0,n.jsx)(n.Fragment,{children:a.map(e=>(0,n.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:r.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},40043:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(98768);r(60343);var i=r(64837);function a({children:e}){return n.jsx(i.Z,{children:e})}},33849:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var n=r(98768);r(60343);var i=r(47520);r(30854);var a=r(56937);let l=(0,i.default)(async()=>{},{loadableGenerated:{modules:["app\\ui\\editor.tsx -> react-quill"]},ssr:!1});function s(e,t){return n.jsx(l,{value:e.content,placeholder:e.placeholder,onChange:e.handleEditorChange,onBlur:e.handleEditorBlur,modules:{toolbar:[[{header:[1,2,3,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"}],["link","image"],[{align:[]},{color:[]}],["clean"]]},formats:["header","bold","italic","underline","strike","blockquote","list","bullet","link","image","align","color","code-block"],className:(0,a.cn)("w-full min-h-60 mb-2 bg-card border overflow-auto text-input border-border rounded-lg",t)})}},82081:(e,t,r)=>{"use strict";r.d(t,{default:()=>w});var n=r(98768),i=r(60343),a=r(72548),l=r(79418),s=r(17380),d=r(7678),o=r.n(d),c=r(14826),u=r.n(c),p=r(69424),h=r(76342),f=r(33849),m=r(13842),x=r(46776),g=r(26100),v=r(74602),b=r(71890),j=r(81524),y=r(57103),N=r(60797),C=r(25394),T=r(75535),W=r(52016),D=r(81311),E=r(39544),O=r(35024),k=r(78965),I=r(94060),P=r(50088),S=r(70906),M=r(69422);let w=({trainingTypeId:e=0})=>{let t=(0,p.useRouter)(),[r,d]=(0,i.useState)(),[c,w]=(0,i.useState)(),[R,_]=(0,i.useState)(!1),[F,Z]=(0,i.useState)({Title:""}),[L,B]=(0,i.useState)(null),[V,q]=(0,i.useState)(),[A,H]=(0,i.useState)(!1),[U,z]=(0,i.useState)(null),[G,$]=(0,i.useState)(null),[J,Q]=(0,i.useState)(!1),[Y,K]=(0,i.useState)([]),[X,ee]=(0,i.useState)(),[et,er]=(0,i.useState)(!1);(0,m.$J)(e,e=>{d(e),H(e?.procedure),$(e.customisedComponentField.nodes)}),(0,m.sy)(e=>{w(e.filter(e=>!e.archived))});let en=t=>{let{name:n,value:i}=t.target,a=i;["HighWarnWithin","MediumWarnWithin","OccursEvery"].includes(n)&&(a=+i),d({...r,[n]:a,ID:+e})},[ei,{loading:ea}]=(0,a.D)(h.Vhv,{onCompleted:r=>{r.updateTrainingType.id>0&&(t.back(),0===e?t.push("/training-type"):t.push(`/training-type/info?id=${e}`))},onError:e=>{console.error("mutationUpdateTrainingType error",e)}}),[el,{loading:es}]=(0,a.D)(h.ZQB,{onCompleted:r=>{r.createTrainingType.id>0&&(t.back(),0===e?t.push("/training-type"):t.push(`/training-type/info?id=${e}`))},onError:e=>{console.error("mutationCreateTrainingType error",e)}}),ed=async()=>{let t=!1,n={Title:""};if(Z(n),o()(u()(document.getElementById("nature-of-training").value))&&(t=!0,n.Title="Nature of training is required"),t){_(!0),Z(n);return}let i={input:{id:e,title:document.getElementById("nature-of-training").value,occursEvery:r.OccursEvery,highWarnWithin:r.HighWarnWithin,mediumWarnWithin:r.MediumWarnWithin,procedure:A||r.procedure,vessels:r.Vessels?.map(e=>e.ID).join(",")}};0===e?await el({variables:i}):await ei({variables:i})},[eo]=(0,a.D)(h._bR,{onCompleted:()=>{t.push("/training-type")},onError:e=>{console.error("mutationDeleteTrainingType error",e)}}),ec=async()=>{if(L?.id){await eo({variables:{ids:[+L.id]}});let e=V?.map(e=>e.id===L.id?{...e,Archived:!e.Archived}:e);q(e?.filter(e=>!e.archived))}},[eu,ep]=(0,i.useState)(!1);(0,i.useEffect)(()=>{ep(x.Zu)},[]);let[eh]=(0,a.D)(h.LFI,{onCompleted:e=>{e.createCustomisedComponentField.id>0&&(ef(),K([]),Q(!1),z(""))},onError:e=>{console.error("createCustomisedLogBookComponent error",e)}}),ef=()=>{em({variables:{id:+e}})},[em]=(0,l.t)(I.e3,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneTrainingType.customisedComponentField.nodes;t&&$(t)},onError:e=>{console.error("getProcedureList error",e)}}),ex=(e,t)=>{let r=G.map(r=>r.id===e?{...r,status:t?"Required":"Off"}:r);eg({variables:{input:{id:+e,status:t?"Required":"Off"}}}),$(r)},[eg]=(0,a.D)(h.G7d,{onCompleted:e=>{e.updateCustomisedComponentField.id>0&&ef()},onError:e=>{console.error("updateCustomisedLogBookComponent error",e)}}),ev=e=>{er(!0),ee(e)},[eb]=(0,a.D)(h.Ck7,{fetchPolicy:"no-cache",onCompleted:e=>{ef(),er(!1),ee(0)},onError:e=>{}});return eu&&((0,x.Fs)("EDIT_TRAINING",eu)||(0,x.Fs)("VIEW_TRAINING",eu)||(0,x.Fs)("RECORD_TRAINING",eu))?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(v.H1,{className:"mb-4 phablet:mb-6 text-xl phablet:text-2xl landscape:text-3xl",children:[0===e?"New":"Edit"," Training Type"]}),n.jsx(O.Zb,{className:"mb-4 phablet:mb-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 landscape:grid-cols-3 gap-4 landscape:gap-6 pb-4 pt-3 px-0 phablet:px-4",children:[n.jsx("div",{className:"space-y-2",children:n.jsx(v.H4,{children:"Training type details"})}),(0,n.jsxs)("div",{className:"landscape:col-span-2 space-y-4",children:[(0,n.jsxs)(N.Label,{label:"Nature of training",htmlFor:"nature-of-training",required:!0,children:[!r&&e>0?n.jsx(s.U3,{}):n.jsx(b.I,{id:"nature-of-training",placeholder:"Nature of training",name:"Title",defaultValue:r?.title||"",onChange:en,type:"text",required:!0}),R&&F.Title&&n.jsx("p",{className:"text-sm text-destructive",children:F.Title})]}),n.jsx(N.Label,{label:"Assigned vessels",htmlFor:"vessels",children:c?n.jsx(j.Combobox,{id:"vessels",options:c?.map(e=>({value:e.id.toString(),label:e.title,vessel:{id:e.id,title:e.title,icon:e.icon,iconMode:e.iconMode,photoID:e.photoID}})),defaultValues:r?.vessels?.nodes?.map(e=>{let t=c?.find(t=>t.id===e.id);return{value:e.id.toString(),label:e.title,vessel:t?{id:t.id,title:t.title,icon:t.icon,iconMode:t.iconMode,photoID:t.photoID}:{id:e.id,title:e.title,icon:null,iconMode:"Icon",photoID:"0"}}}),onChange:e=>{d({...r,Vessels:e.map(e=>({ID:e.value,Title:e.label}))})},placeholder:"Select vessels",multi:!0}):n.jsx(s.U3,{})})]})]})}),n.jsx(O.Zb,{className:"mb-4 phablet:mb-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 landscape:grid-cols-3 gap-4 landscape:gap-6 pb-4 pt-3 px-0 phablet:px-4",children:[n.jsx("div",{className:"space-y-2",children:n.jsx(v.H4,{children:"Frequency"})}),(0,n.jsxs)("div",{className:"landscape:col-span-2 space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 phablet:grid-cols-2 landscape:grid-cols-1 gap-4",children:[n.jsx(N.Label,{label:"Occurs Every (days)",htmlFor:"OccursEvery",children:!r&&e>0?n.jsx(s.U3,{}):n.jsx(b.I,{id:"OccursEvery",name:"OccursEvery",defaultValue:r?.occursEvery||0,onChange:en,type:"number",step:1,min:0})}),n.jsx(N.Label,{label:"Medium Warning Within (e.g. 5 days)",htmlFor:"MediumWarnWithin",children:!r&&e>0?n.jsx(s.U3,{}):n.jsx(b.I,{id:"MediumWarnWithin",name:"MediumWarnWithin",defaultValue:r?.mediumWarnWithin||0,onChange:en,type:"number",step:1,min:0})})]}),n.jsx(N.Label,{label:"High Warning Within (e.g. 1 day)",htmlFor:"HighWarnWithin",children:!r&&e>0?n.jsx(s.U3,{}):n.jsx(b.I,{id:"HighWarnWithin",name:"HighWarnWithin",defaultValue:r?.highWarnWithin||0,onChange:en,type:"number",step:1,min:0})})]})]})}),n.jsx(O.Zb,{className:"mb-4 phablet:mb-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 landscape:grid-cols-3 gap-4 landscape:gap-6 pb-4 pt-3 px-0 phablet:px-4",children:[n.jsx("div",{className:"space-y-2",children:n.jsx(v.H4,{children:"Procedure added"})}),(0,n.jsxs)("div",{className:"landscape:col-span-2 space-y-4",children:[G?.length>0?n.jsx("div",{className:"flex flex-col gap-2 pb-3",children:G?.map((e,t)=>n.jsxs("div",{className:"flex justify-between items-center bg-sllightblue-50 border-b border-border",children:[n.jsxs("div",{className:"flex items-center gap-2",children:[n.jsxs(S.Ee,{variant:"horizontal",gap:"none",value:"Required"===e.status?"yes":"no",onValueChange:t=>{ex(e.id,"yes"===t)},children:[n.jsx("div",{className:M.cn("flex w-[48px] bg-cinnabar-100 justify-center py-3 standard:p-0 standard:items-center"),children:n.jsx(S.mJ,{value:"no",id:`${t}-no_radio`,variant:"destructive",size:"lg"})}),n.jsx("div",{className:M.cn("flex w-[48px] bg-bright-turquoise-100 justify-center pt-3 standard:p-0 standard:items-center"),children:n.jsx(S.mJ,{value:"yes",id:`${t}-yes_radio`,variant:"success",size:"lg"})})]}),n.jsx("div",{className:"text-sldarkblue-800 font-semibold cursor-pointer",onClick:()=>{K({...e,description:e.description}),Q(!0),z(e.description)},children:e.fieldName})]}),n.jsx(E.Button,{variant:"text",size:"icon",onClick:()=>{ev(e.id)},children:n.jsx(T.Z,{})})]},e.id))}):n.jsx("p",{children:"No procedures added"}),n.jsx(P.Z,{action:()=>{Q(!0)},text:"Add new check",type:"text",color:"sky",icon:"plus"})]})]})}),n.jsx(O.Zb,{className:"mb-4 phablet:mb-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 landscape:grid-cols-3 gap-4 landscape:gap-6 pb-4 pt-3 px-0 phablet:px-4",children:[n.jsx("div",{className:"space-y-2",children:n.jsx(v.H4,{children:"Procedure"})}),n.jsx("div",{className:"landscape:col-span-2",children:!r&&e>0?n.jsx(s.U3,{}):n.jsx(f.Z,{name:"Procedure",content:A||"",handleEditorChange:e=>{H(e)}})})]})}),(0,n.jsxs)(k.V,{children:[(0,n.jsxs)("div",{className:"flex gap-2",children:[n.jsx(E.Button,{variant:"back",onClick:()=>t.back(),children:"Cancel"}),e>0&&n.jsx(E.Button,{variant:"destructive",onClick:()=>B(r),children:"Delete"})]}),n.jsx(E.Button,{onClick:ed,isLoading:ea||es,iconLeft:0===e?W.Z:D.Z,children:0===e?"Create":"Update"})]}),n.jsx(y.AlertDialogNew,{openDialog:!!L,setOpenDialog:()=>B(null),handleCreate:ec,actionText:"Delete",variant:"danger",title:"Delete Training Type",description:`Are you sure you want to delete ${r?.title||"this training type"}?`}),n.jsx(y.AlertDialogNew,{openDialog:J,setOpenDialog:Q,handleCreate:()=>{let t={input:{fieldName:Y.title,sortOrder:+Y.sortOrder,status:"Required",description:Y.description,trainingTypeID:+e}};Y?.id>0?(eg({variables:{input:{id:+Y.id,fieldName:Y.fieldName,sortOrder:+Y.sortOrder,status:"Required",description:"<p><br></p>"==Y.description?null:Y.description}}}),K([]),Q(!1),z("")):eh({variables:t})},size:"xl",actionText:"Create Procedure",title:"Create new Procedure",children:n.jsx(C.iP,{className:"space-y-4",children:J&&(0,n.jsxs)("div",{slot:"content",className:"mb-4",children:[n.jsx(b.I,{id:"field-name",name:"field-name",type:"text",className:"w-full mb-4",placeholder:"Field Name",value:Y.fieldName,onChange:e=>{K({...Y,title:e.target.value})}}),n.jsx(b.I,{id:"field-sort-order",name:"field-sort-order",type:"number",className:"w-full mb-4",min:"0",placeholder:"Sort order",value:Y.sortOrder,onChange:e=>{K({...Y,sortOrder:e.target.value})}}),n.jsx(N.Label,{htmlFor:"field-description",label:" Procedure description",children:n.jsx(f.Z,{id:"field-description",placeholder:"Description (Optional)",content:U,handleEditorChange:e=>{z(e),K({...Y,description:e})}})})]})})}),n.jsx(y.AlertDialogNew,{openDialog:et,setOpenDialog:er,handleCreate:()=>{eb({variables:{ids:[+X]}})},size:"xl",actionText:"Delete",title:"Delete Procedure!",children:n.jsx(C.iP,{className:"space-y-4"})})]}):eu?n.jsx(g.Z,{errorMessage:"Oops You do not have the permission to view this section."}):n.jsx(g.Z,{})}},69422:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var n=r(28411),i=r(5001);function a(...e){return(0,i.m6)((0,n.W)(e))}},47908:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\training-type\layout.tsx#default`)},30854:()=>{},75535:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};