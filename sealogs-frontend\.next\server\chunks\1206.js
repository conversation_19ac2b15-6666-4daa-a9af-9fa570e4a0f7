exports.id=1206,exports.ids=[1206],exports.modules={45101:(e,t,a)=>{Promise.resolve().then(a.bind(a,51386))},51386:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var l=a(98768);a(60343);var o=a(64837);function i({children:e}){return l.jsx(o.Z,{children:e})}},95272:(e,t,a)=>{"use strict";a.d(t,{default:()=>z});var l=a(98768),o=a(60343),i=a(71241),s=a.n(i),n=a(7678),r=a.n(n),c=a(14826),d=a.n(c),u=a(78965),m=a(72548),p=a(79418),g=a(94060),x=a(76342),h=a(69424),v=a(17380),f=a(34469),j=a(71890),N=a(60797),b=a(39544),y=a(57103),L=a(35024),C=a(74602),F=a(34376),E=a(68846),w=a(17203),P=a(75535),D=a(81311),S=a(9210);let z=({id:e=0})=>{let[t,a]=(0,o.useState)(!1),[i,n]=(0,o.useState)(!0),c=(0,h.useRouter)(),{toast:z}=(0,F.pm)(),[Z,$]=(0,o.useState)({}),[k,G]=(0,o.useState)(!1),[A,M]=(0,o.useState)({title:""}),T=s()((t,a)=>{$({...Z,[t]:a,id:+e})},300),[V,{loading:I}]=(0,m.D)(x.VMX,{onCompleted:()=>{c.push(`/location/info?id=${e}`)},onError:e=>{console.error("updateGeoLocation",e),z({variant:"destructive",title:"Error",description:`Failed to update location: ${e.message}`})}}),[O,{loading:U}]=(0,m.D)(x.ovu,{onCompleted:()=>{c.push("/location")},onError:e=>{console.error("createGeoLocation",e),z({variant:"destructive",title:"Error",description:`Failed to create location: ${e.message}`})}}),[q,{loading:B}]=(0,m.D)(x.XWI,{onCompleted:()=>{a(!1),c.push("/location")},onError:e=>{console.error("deleteGeoLocation",e),a(!1),z({variant:"destructive",title:"Error",description:"Failed to delete location as this location may still be used in a trip report."})}}),H=e=>{let{name:t,value:a}=e.target;if(("lat"===t||"long"===t)&&a.includes(",")){z({variant:"destructive",title:"Error",description:`You have entered an incorrect value for ${t}`});return}T(t,a)},R=async()=>{let t=!1,a={title:""};if(r()(d()(Z.title))&&(t=!0,a.title="Title is required"),t){G(!0),M(a);return}let l={input:{id:+e,title:Z.title,lat:parseFloat(Z.lat)||0,long:parseFloat(Z.long)||0}};0===e?await O({variables:l}):await V({variables:l})},Y=async()=>{await q({variables:{ids:[+e]}})},[W,{loading:X}]=(0,p.t)(g.Nh,{fetchPolicy:"cache-and-network",onCompleted:e=>{$(e.readOneGeoLocation)},onError:e=>{console.error("readOneGeoLocation",e),z({variant:"destructive",title:"Error",description:`Failed to load location: ${e.message}`})}}),J=async()=>{await W({variables:{id:e}})},K=s()(e=>{if(e&&Array.isArray(e)&&e.length>=2){let[t,a]=e;"number"==typeof t&&"number"==typeof a&&!isNaN(t)&&!isNaN(a)&&t>=-90&&t<=90&&a>=-180&&a<=180&&$(e=>({...e,lat:t,long:a}))}},150);return(0,o.useEffect)(()=>{i&&(J(),n(!1))},[i]),(0,l.jsxs)("div",{children:[(0,l.jsxs)(L.Zb,{className:"space-y-8 mb-2.5 mx-2.5",children:[l.jsx(L.Ol,{children:(0,l.jsxs)(L.ll,{className:"flex items-center gap-2",children:[l.jsx(S.r4,{}),(0,l.jsxs)(C.H3,{children:[0===e?"Create":"Edit"," Location"]})]})}),(0,l.jsxs)(L.aY,{children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 mb-8 lg:grid-cols-3 gap-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[l.jsx(C.H2,{className:"text-lg font-medium flex items-center gap-2 text-primary",children:"Location Details"}),l.jsx(C.P,{className:"text-muted-foreground text-sm leading-relaxed",children:"Enter the location name and coordinates. You can also click on the map below to set the coordinates automatically."})]}),(0,l.jsxs)("div",{className:"col-span-2 space-y-6",children:[(0,l.jsxs)(N.Label,{label:"Location Name",htmlFor:"location-name",children:[X?l.jsx(v.U3,{}):l.jsx(j.I,{id:"location-name",name:"title",type:"text",required:!0,defaultValue:Z?.title||"",onChange:H,placeholder:"Enter location name"}),k&&A.title&&l.jsx("small",{className:"text-destructive",children:A.title})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[l.jsx(N.Label,{label:"Latitude",htmlFor:"latitude",children:X?l.jsx(v.U3,{}):l.jsx(j.I,{id:"latitude",name:"lat",type:"text",placeholder:"Latitude",required:!0,defaultValue:Z?.lat||"",onChange:H})}),l.jsx(N.Label,{label:"Longitude",htmlFor:"longitude",children:X?l.jsx(v.U3,{}):l.jsx(j.I,{id:"longitude",name:"long",type:"text",placeholder:"Longitude",required:!0,defaultValue:Z?.long||"",onChange:H})})]})]})]}),!X&&(0,l.jsxs)("div",{className:"space-y-2.5",children:[(0,l.jsxs)(C.H2,{className:"text-lg flex gap-2.5 font-medium",children:[l.jsx(E.Z,{})," Location Map"]}),l.jsx("div",{className:"h-[60svh] w-full rounded-lg overflow-hidden border",children:l.jsx(f.Z,{position:[isNaN(parseFloat(Z?.lat))?0:parseFloat(Z?.lat),isNaN(parseFloat(Z?.long))?0:parseFloat(Z?.long)],zoom:Z?.lat&&Z?.long&&!isNaN(parseFloat(Z?.lat))&&!isNaN(parseFloat(Z?.long))?19:2,onPositionChange:e=>{K(e)},className:"size-full z-0"})})]})]})]}),(0,l.jsxs)(u.V,{children:[l.jsx(b.Button,{variant:"back",onClick:()=>c.back(),iconLeft:w.Z,children:"Cancel"}),e>0&&(0,l.jsxs)(l.Fragment,{children:[l.jsx(b.Button,{variant:"destructive",iconLeft:P.Z,onClick:()=>a(!0),isLoading:B,disabled:X||I||U||B,children:"Delete"}),l.jsx(y.AlertDialogNew,{openDialog:t,setOpenDialog:a,handleDestructiveAction:Y,title:"Delete Location",description:`Are you sure you want to delete "${Z.title}"? This action cannot be undone.`,cancelText:"Cancel",destructiveActionText:"Delete",showDestructiveAction:!0,variant:"warning"})]}),l.jsx(b.Button,{iconLeft:D.Z,onClick:R,isLoading:I||U,disabled:X||I||U||B,children:`${0===e?"Create":"Update"} Location`})]})]})}},34469:(e,t,a)=>{"use strict";a.d(t,{Z:()=>g});var l=a(98768),o=a(47520);a(47011),a(7385);var i=a(35753),s=a(60343);a(94060);var n=a(96268),r=a(71241),c=a.n(r);let d=(0,o.default)(async()=>{},{loadableGenerated:{modules:["components\\location-map.tsx -> react-leaflet"]},ssr:!1}),u=(0,o.default)(async()=>{},{loadableGenerated:{modules:["components\\location-map.tsx -> react-leaflet"]},ssr:!1}),m=(0,o.default)(async()=>{},{loadableGenerated:{modules:["components\\location-map.tsx -> react-leaflet"]},ssr:!1}),p=({debounceMs:e=150})=>{let t=(0,i.Sx)(),{run:a}=(0,n.DI)(()=>{t&&t.invalidateSize()},e);return(0,s.useEffect)(()=>{let e=t.getContainer();if(!e)return;let l=new ResizeObserver(e=>{e.some(e=>{let{width:t,height:a}=e.contentRect;return t>0&&a>0})&&a()});l.observe(e);let o=e.parentElement;o&&l.observe(o);let i=setTimeout(()=>{t&&t.invalidateSize()},100);return()=>{l.disconnect(),clearTimeout(i)}},[t,a]),null};function g({position:e,vessel:t,vessels:a,zoom:o=13,onPositionChange:s,className:n="h-full",enableResize:r=!0,scrollWheelZoom:g=!1,style:x,resizeDebounceMs:h=150,enableClickToSetPosition:v=!1}){let f=[isNaN(e[0])?0:e[0],isNaN(e[1])?0:e[1]],j=c()((e,t)=>{"number"!=typeof e||"number"!=typeof t||isNaN(e)||isNaN(t)||s?.([e,t])},100),N=e=>{if(e?.latlng?.lat!==void 0&&e?.latlng?.lng!==void 0){let{lat:t,lng:a}=e.latlng;j(t,a)}},b=e=>{let{position:t,vessel:a}=e;return(0,i.zV)({dblclick(e){v&&N(e)}}),l.jsx(u,{position:t})},y=(()=>{if(a&&a.length>0){let e=a.find(e=>e.vesselPosition?.lat&&e.vesselPosition?.long);if(e)return[e.vesselPosition.lat||0,e.vesselPosition.long||0]}return f})(),L=`map-${Math.round(1e3*y[0])}-${Math.round(1e3*y[1])}`;return(0,l.jsxs)(d,{center:y,zoom:o,scrollWheelZoom:g,className:n,style:{minHeight:"200px",height:"100%",...x},children:[l.jsx(m,{url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}),a&&a.length>0?a.filter(e=>e.vesselPosition?.lat!=0||e.vesselPosition?.geoLocation?.id>0).map((e,t)=>e?.vesselPosition?.id>0&&l.jsx(b,{position:[(e.vesselPosition.lat||0)+.001+.004*Math.random(),e.vesselPosition.long||0],vessel:e},t)):t&&l.jsx(b,{position:f,vessel:t}),r&&l.jsx(p,{debounceMs:h})]},L)}},10133:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l});let l=(0,a(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\location\layout.tsx#default`)}};