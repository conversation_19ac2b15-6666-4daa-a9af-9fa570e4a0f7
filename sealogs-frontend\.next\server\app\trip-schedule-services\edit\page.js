(()=>{var e={};e.id=9342,e.ids=[9342],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},49357:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>u}),t(2868),t(31882),t(78398),t(57757),t(48045);var s=t(40060),i=t(33581),a=t(57567),n=t.n(a),o=t(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let u=["",{children:["trip-schedule-services",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2868)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-schedule-services\\edit\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,31882)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-schedule-services\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\trip-schedule-services\\edit\\page.tsx"],d="/trip-schedule-services/edit/page",p={require:t,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/trip-schedule-services/edit/page",pathname:"/trip-schedule-services/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},25504:(e,r,t)=>{Promise.resolve().then(t.bind(t,36221))},10014:(e,r,t)=>{Promise.resolve().then(t.bind(t,74999))},60826:e=>{e.exports=function(e){return e.split("")}},829:e=>{e.exports=function(e,r,t,s){for(var i=e.length,a=t+(s?1:-1);s?a--:++a<i;)if(r(e[a],a,e))return a;return -1}},65337:(e,r,t)=>{var s=t(829),i=t(35447),a=t(28026);e.exports=function(e,r,t){return r==r?a(e,r,t):s(e,i,t)}},35447:e=>{e.exports=function(e){return e!=e}},77420:e=>{e.exports=function(e,r,t){var s=-1,i=e.length;r<0&&(r=-r>i?0:i+r),(t=t>i?i:t)<0&&(t+=i),i=r>t?0:t-r>>>0,r>>>=0;for(var a=Array(i);++s<i;)a[s]=e[s+r];return a}},49513:(e,r,t)=>{var s=t(70458),i=/^\s+/;e.exports=function(e){return e?e.slice(0,s(e)+1).replace(i,""):e}},30482:(e,r,t)=>{var s=t(77420);e.exports=function(e,r,t){var i=e.length;return t=void 0===t?i:t,!r&&t>=i?e:s(e,r,t)}},74783:(e,r,t)=>{var s=t(65337);e.exports=function(e,r){for(var t=e.length;t--&&s(r,e[t],0)>-1;);return t}},41200:(e,r,t)=>{var s=t(65337);e.exports=function(e,r){for(var t=-1,i=e.length;++t<i&&s(r,e[t],0)>-1;);return t}},73211:e=>{var r=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return r.test(e)}},28026:e=>{e.exports=function(e,r,t){for(var s=t-1,i=e.length;++s<i;)if(e[s]===r)return s;return -1}},66095:(e,r,t)=>{var s=t(60826),i=t(73211),a=t(92115);e.exports=function(e){return i(e)?a(e):s(e)}},70458:e=>{var r=/\s/;e.exports=function(e){for(var t=e.length;t--&&r.test(e.charAt(t)););return t}},92115:e=>{var r="\ud800-\udfff",t="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",s="\ud83c[\udffb-\udfff]",i="[^"+r+"]",a="(?:\ud83c[\udde6-\uddff]){2}",n="[\ud800-\udbff][\udc00-\udfff]",o="(?:"+t+"|"+s+")?",l="[\\ufe0e\\ufe0f]?",u="(?:\\u200d(?:"+[i,a,n].join("|")+")"+l+o+")*",c=RegExp(s+"(?="+s+")|(?:"+[i+t+"?",t,a,n,"["+r+"]"].join("|")+")"+(l+o+u),"g");e.exports=function(e){return e.match(c)||[]}},14826:(e,r,t)=>{var s=t(22060),i=t(49513),a=t(30482),n=t(74783),o=t(41200),l=t(66095),u=t(16266);e.exports=function(e,r,t){if((e=u(e))&&(t||void 0===r))return i(e);if(!e||!(r=s(r)))return e;var c=l(e),d=l(r),p=o(c,d),f=n(c,d)+1;return a(c,p,f).join("")}},36221:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(98768);t(60343);var i=t(64837);function a({children:e}){return s.jsx(i.Z,{children:e})}},74999:(e,r,t)=>{"use strict";t.d(r,{default:()=>C});var s=t(98768),i=t(13842),a=t(76342),n=t(94060),o=t(72548),l=t(79418),u=t(7678),c=t.n(u),d=t(14826),p=t.n(d),f=t(69424),h=t(60343),x=t(71890),v=t(8750),m=t(81524),g=t(60797),j=t(26509),S=t(74602),b=t(34376),y=t(13609),T=t(3510),P=t(25394);let C=()=>{let e=(0,f.useRouter)(),r=(0,f.useSearchParams)().get("id")??0,[t,u]=(0,h.useState)({}),[d,C]=(0,h.useState)([]),[q,N]=(0,h.useState)([]),[_,E]=(0,h.useState)([]),[L,w]=(0,h.useState)([]),[R,V]=(0,h.useState)(!1),[D,{loading:k}]=(0,o.D)(a.ftk,{onCompleted:r=>{r.deleteTripScheduleServices?(V(!1),B({title:"Success",description:"Trip Schedule Service deleted successfully"}),e.push("/trip-schedule-services")):(V(!1),B({variant:"destructive",title:"Error",description:"Error deleting Trip Schedule Service"}))},onError:e=>{V(!1),B({variant:"destructive",title:"Error",description:`Error: ${e.message}`}),console.error("deleteTripScheduleService onError",e.message)}}),[I,{loading:M}]=(0,l.t)(n.uZ,{fetchPolicy:"cache-and-network",onCompleted:e=>{let r=e.readOneTripScheduleService;if(r){let e=r.tripReportSchedules.nodes;E(e.map(e=>({label:`${e.title} @ ${e.departTime}`,value:e.id})));let t=r.vehicles.nodes.map(e=>e.id),s=e.map(e=>e.id);u({...r,vehicles:t.join(","),tripReportSchedules:s.join(","),__typename:void 0})}},onError:e=>{console.error("readOneTripScheduleService error",e)}}),A=async()=>{await I({variables:{id:r}})},{toast:B}=(0,b.pm)(),U=()=>{let e="";return c()(p()(t.title))&&(e+="\nThe title is required."),c()(q)&&(e+="\nAt least one vessel is required."),!!c()(p()(e))||(B({variant:"destructive",title:"Validation Error",description:p()(e)}),!1)},[G,{loading:Z}]=(0,o.D)(a.tLr,{onCompleted:r=>{e.push("/trip-schedule-services")},onError:e=>{B({variant:"destructive",title:"Error",description:e.message}),console.error("updateTripScheduleService onError",e.message)}}),O=async()=>{U()&&+r>0&&await G({variables:{input:t}})};return(0,i.sy)(e=>{let r=["SLALL","Tug_Boat","Passenger_Ferry","Water_Taxi"];C(e.filter(e=>r.includes(e.vesselType)).map(e=>({label:e.title,value:e.id})))}),(0,h.useEffect)(()=>{+r>0&&A()},[r]),(0,h.useEffect)(()=>{c()(d)||c()(t.vehicles)||N(t.vehicles.split(",").map(e=>d.find(r=>+r.value==+e)))},[d,t]),(0,h.useEffect)(()=>{c()(_)||c()(t.tripReportSchedules)||w(t.tripReportSchedules.split(",").map(e=>_.find(r=>+r.value==+e)))},[_,t]),s.jsx("div",{className:"w-full mb-20 md:mb-0",children:(0,s.jsxs)("div",{className:"px-2 lg:px-4 mt-2 ",children:[s.jsx("div",{className:"flex md:flex-nowrap md:flex-row gap-3 flex-col-reverse flex-wrap justify-between md:items-center items-start",children:(0,s.jsxs)(S.H2,{children:[+r>0?"Edit":"New"," Trip Schedule Service"]})}),s.jsx(j.Separator,{className:"my-4"}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(g.Label,{children:"Title"}),s.jsx(x.I,{defaultValue:t.title,onChange:e=>{u({...t,title:e.target.value})},type:"text",placeholder:"Title"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(g.Label,{children:"Internal Code"}),s.jsx(x.I,{defaultValue:t.code,onChange:e=>{u({...t,code:e.target.value})},type:"text",placeholder:"Internal code"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 my-4",children:[s.jsx(v.Checkbox,{id:"share-pax-data",checked:t.sharePaxData,onCheckedChange:e=>{u({...t,sharePaxData:e})}}),s.jsx(g.Label,{htmlFor:"share-pax-data",className:"font-medium cursor-pointer",children:"Share Trip Data (Passenger Numbers)"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(g.Label,{children:"Transit ID"}),s.jsx(x.I,{defaultValue:t.transitID,onChange:e=>{u({...t,transitID:e.target.value})},type:"text",placeholder:"Transit ID"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(g.Label,{children:"Public Transit Description"}),s.jsx(x.I,{defaultValue:t.publicDescription,onChange:e=>{u({...t,publicDescription:e.target.value})},type:"text",placeholder:"Public Transit Description"})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(g.Label,{children:"Vessels"}),s.jsx(m.Combobox,{options:d,value:q,onChange:e=>{N(e),u({...t,vehicles:e.map(e=>e.value).join(",")})},placeholder:"Select Vessels",multi:!0})]}),(0,s.jsxs)("div",{className:"my-4",children:[s.jsx(g.Label,{children:"Trip Schedules"}),s.jsx(m.Combobox,{options:_,value:L,onChange:e=>{w(e),u({...t,tripReportSchedules:e.map(e=>e.value).join(",")})},placeholder:"Select Trip Schedule",multi:!0})]}),(0,s.jsxs)("div",{className:"flex justify-end gap-2",children:[s.jsx(P.zx,{variant:"text",iconLeft:y.Z,onClick:()=>{e.push("/trip-schedule-services")},children:"Cancel"}),(0,s.jsxs)(P.zx,{iconLeft:T.Z,onClick:O,isLoading:M||Z,children:[+r>0?"Update":"Save"," Changes"]})]})]})})}},2868:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(96141);let i=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\ui\schedules\trip-schedule-service-form.tsx#default`),a=()=>s.jsx("div",{children:s.jsx(i,{})})},31882:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\trip-schedule-services\layout.tsx#default`)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[864,8865,3563,6263,8189,9507,7602,2935,6451,4234,2925,5394,4837,6342,3842],()=>t(49357));module.exports=s})();