exports.id=7369,exports.ids=[7369],exports.modules={16236:(e,t,r)=>{Promise.resolve().then(r.bind(r,49980))},60826:e=>{e.exports=function(e){return e.split("")}},829:e=>{e.exports=function(e,t,r,a){for(var n=e.length,l=r+(a?1:-1);a?l--:++l<n;)if(t(e[l],l,e))return l;return -1}},65337:(e,t,r)=>{var a=r(829),n=r(35447),l=r(28026);e.exports=function(e,t,r){return t==t?l(e,t,r):a(e,n,r)}},35447:e=>{e.exports=function(e){return e!=e}},77420:e=>{e.exports=function(e,t,r){var a=-1,n=e.length;t<0&&(t=-t>n?0:n+t),(r=r>n?n:r)<0&&(r+=n),n=t>r?0:r-t>>>0,t>>>=0;for(var l=Array(n);++a<n;)l[a]=e[a+t];return l}},30482:(e,t,r)=>{var a=r(77420);e.exports=function(e,t,r){var n=e.length;return r=void 0===r?n:r,!t&&r>=n?e:a(e,t,r)}},74783:(e,t,r)=>{var a=r(65337);e.exports=function(e,t){for(var r=e.length;r--&&a(t,e[r],0)>-1;);return r}},41200:(e,t,r)=>{var a=r(65337);e.exports=function(e,t){for(var r=-1,n=e.length;++r<n&&a(t,e[r],0)>-1;);return r}},73211:e=>{var t=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return t.test(e)}},28026:e=>{e.exports=function(e,t,r){for(var a=r-1,n=e.length;++a<n;)if(e[a]===t)return a;return -1}},66095:(e,t,r)=>{var a=r(60826),n=r(73211),l=r(92115);e.exports=function(e){return n(e)?l(e):a(e)}},92115:e=>{var t="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",a="\ud83c[\udffb-\udfff]",n="[^"+t+"]",l="(?:\ud83c[\udde6-\uddff]){2}",s="[\ud800-\udbff][\udc00-\udfff]",i="(?:"+r+"|"+a+")?",o="[\\ufe0e\\ufe0f]?",d="(?:\\u200d(?:"+[n,l,s].join("|")+")"+o+i+")*",u=RegExp(a+"(?="+a+")|(?:"+[n+r+"?",r,l,s,"["+t+"]"].join("|")+")"+(o+i+d),"g");e.exports=function(e){return e.match(u)||[]}},71241:(e,t,r)=>{var a=r(4171),n=r(96817),l=r(24436),s=Math.max,i=Math.min;e.exports=function(e,t,r){var o,d,u,c,m,f,p=0,v=!1,h=!1,x=!0;if("function"!=typeof e)throw TypeError("Expected a function");function g(t){var r=o,a=d;return o=d=void 0,p=t,c=e.apply(a,r)}function b(e){var r=e-f,a=e-p;return void 0===f||r>=t||r<0||h&&a>=u}function w(){var e,r,a,l=n();if(b(l))return j(l);m=setTimeout(w,(e=l-f,r=l-p,a=t-e,h?i(a,u-r):a))}function j(e){return(m=void 0,x&&o)?g(e):(o=d=void 0,c)}function y(){var e,r=n(),a=b(r);if(o=arguments,d=this,f=r,a){if(void 0===m)return p=e=f,m=setTimeout(w,t),v?g(e):c;if(h)return clearTimeout(m),m=setTimeout(w,t),g(f)}return void 0===m&&(m=setTimeout(w,t)),c}return t=l(t)||0,a(r)&&(v=!!r.leading,u=(h="maxWait"in r)?s(l(r.maxWait)||0,t):u,x="trailing"in r?!!r.trailing:x),y.cancel=function(){void 0!==m&&clearTimeout(m),p=0,o=f=d=m=void 0},y.flush=function(){return void 0===m?c:j(n())},y}},96817:(e,t,r)=>{var a=r(65584);e.exports=function(){return a.Date.now()}},14826:(e,t,r)=>{var a=r(22060),n=r(49513),l=r(30482),s=r(74783),i=r(41200),o=r(66095),d=r(16266);e.exports=function(e,t,r){if((e=d(e))&&(r||void 0===t))return n(e);if(!e||!(t=a(t)))return e;var u=o(e),c=o(t),m=i(u,c),f=s(u,c)+1;return l(u,m,f).join("")}},49980:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var a=r(98768);r(60343);var n=r(64837);function l({children:e}){return a.jsx(n.Z,{children:e})}},55361:(e,t,r)=>{"use strict";r.d(t,{Z:()=>b});var a=r(98768),n=r(60343),l=r(7678),s=r.n(l),i=r(76342),o=r(79418),d=r(72548),u=r(73366),c=r(10090),m=r(81524),f=r(24794),p=r(39544),v=r(71890);function h({openDialog:e,setOpenDialog:t,handleCreate:r,actionText:n,error:l}){return a.jsx(f.Dialog,{open:e,onOpenChange:t,children:(0,a.jsxs)(f.DialogContent,{children:[a.jsx(f.DialogHeader,{children:a.jsx(f.DialogTitle,{className:"font-medium",children:"Add Crew Member"})}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),r(new FormData(e.currentTarget))},children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 gap-4 border-t pt-6",children:[(0,a.jsxs)("div",{className:"flex gap-4",children:[a.jsx(v.I,{id:"crew-firstName",name:"firstName",type:"text",placeholder:"First Name"}),a.jsx(v.I,{id:"crew-surname",name:"surname",type:"text",placeholder:"Surname"})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[a.jsx(v.I,{id:"crew-username",name:"username",type:"text",placeholder:"Username"}),a.jsx(v.I,{id:"crew-password",name:"password",type:"password",placeholder:"Password"})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[a.jsx(v.I,{id:"crew-email",name:"email",type:"email",placeholder:"Email"}),a.jsx(v.I,{id:"crew-phoneNumber",name:"phoneNumber",type:"text",placeholder:"Phone Number"})]}),l&&a.jsx("div",{className:"text-rose-600",children:l.message})]}),a.jsx(f.DialogFooter,{className:"mt-6",children:a.jsx(p.Button,{type:"submit",children:n})})]})]})})}var x=r(45519);let g=(0,x.ZP)`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                isArchived
                firstName
                surname
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
                departments {
                    nodes {
                        id
                    }
                }
                groups {
                    nodes {
                        id
                        title
                        code
                    }
                }
            }
        }
    }
`,b=({value:e=[],onChange:t,memberIdOptions:r=[],departments:l=[],filterByAdmin:f=!1,offline:p=!1,vesselID:v=0})=>{let[x,b]=(0,n.useState)(!0),[w,j]=(0,n.useState)([]),[y,N]=(0,n.useState)(!1),[D,S]=(0,n.useState)([]),[E,I]=(0,n.useState)(!1),C=new u.Z,M=e=>{let t=v>0?e.filter(e=>e.vehicles.nodes.some(e=>+e.id===v)):e,a={value:"newCrewMember",label:"--- Create Crew Member ---"},n=t.filter(e=>!f||!$(e));if(l.length>0){let e=l.flatMap(e=>e.id),t=n.filter(t=>t.departments.nodes.some(t=>e.includes(t.id))).map(e=>({value:e.id,label:`${e.firstName??""} ${e.surname??""}`}));0===r.length?j([a,...t]):j(t.filter(e=>r.includes(e.value)))}else{let e=n.map(e=>({value:e.id,label:`${e.firstName??""} ${e.surname??""}`}));0===r.length?j([a,...e]):j(e.filter(e=>r.includes(e.value)))}},[B]=(0,o.t)(g,{fetchPolicy:"cache-and-network",onError:e=>{console.error("querySeaLogsMembersList error",e)}}),L=async()=>{let e=[],t=0,r=!0;try{for(;r;){let a=await B({variables:{filter:{isArchived:{eq:!1}},limit:100,offset:t}});if(a.data?.readSeaLogsMembers){let n=a.data.readSeaLogsMembers.nodes,l=a.data.readSeaLogsMembers.pageInfo;n&&n.length>0&&(e=[...e,...n]),r=l?.hasNextPage||!1,t+=100}else r=!1}e.length>0&&M(e)}catch(e){console.error("Error loading all crew members:",e)}};(0,n.useEffect)(()=>{x&&!p&&(L(),b(!1))},[x,p]),(0,n.useEffect)(()=>{p&&C.getAll().then(e=>{M(e)})},[p]);let $=e=>e.groups.nodes?.filter(e=>"admin"===e.code).length>0,[P]=(0,d.D)(i.qK0,{fetchPolicy:"no-cache",onCompleted:r=>{let a=r.createSeaLogsMember;if(a.id>0){N(!1);let r={value:a.id,label:a.firstName+" "+a.surname};j([...w,r]),S([...D,a.id]),t([r,...e.map(e=>w.find(t=>t.value===e))]),I(!1)}},onError:e=>{console.error("createUser error",e.message),I(e)}}),A=async()=>{let r={input:{firstName:document.getElementById("crew-firstName").value?document.getElementById("crew-firstName").value:null,surname:document.getElementById("crew-surname").value?document.getElementById("crew-surname").value:null,email:document.getElementById("crew-email").value?document.getElementById("crew-email").value:null,phoneNumber:document.getElementById("crew-phoneNumber").value?document.getElementById("crew-phoneNumber").value:null,username:document.getElementById("crew-username").value?document.getElementById("crew-username").value:null,password:document.getElementById("crew-password").value?document.getElementById("crew-password").value:null}};if(p){let a=await C.save({...r.input,id:(0,c.lY)()});N(!1);let n={value:a.id,label:a.firstName+" "+a.surname};j([...w,n]),S([...D,a.id]),t([n,...e.map(e=>w.find(t=>t.value===e))]),I(!1)}else await P({variables:r})};return(0,n.useEffect)(()=>{if(s()(e)||0===e.length){S([]);return}s()(w)||S(e.map(e=>{let t=String(e);return w.find(e=>String(e.value)===t)||(console.warn("CrewMultiSelectDropdown - Could not find crew with ID:",e),{value:t,label:`Unknown (${e})`})}).filter(Boolean))},[e,w]),(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.Combobox,{options:w,value:D,onChange:e=>{if(!e){S([]),t([]);return}let r=Array.isArray(e)?e:[e];if(r.find(e=>"newCrewMember"===e.value)){N(!0);return}if(0===r.length){S([]),t([]);return}let a=r.filter(e=>e&&"object"==typeof e);S(a),t(a)},placeholder:"Select Crew",multi:!0,responsiveBadges:!0,isLoading:!w}),a.jsx(h,{openDialog:y,setOpenDialog:N,handleCreate:A,actionText:"Add Crew Member",error:E})]})}},49296:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var a=r(98768),n=r(60343),l=r(79418),s=r(94060),i=r(12166),o=r.n(i),d=r(81524);let u=({value:e=0,onChange:t,excludeId:r=0,parentsOnly:i=!1,disabled:u=!1})=>{let[c,m]=(0,n.useState)([]),[f,p]=(0,n.useState)(!0),[v,h]=(0,n.useState)({}),[x,g]=(0,n.useState)([]),b={value:"0",label:"All Departments",level:0},w=(e,t=0,r=0)=>e.filter(e=>+e.parentID===t).flatMap(t=>{let a=w(e,+t.id,r+1);return[{...t,level:r},...a]}),[j,{loading:y}]=(0,l.t)(s.d5,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readDepartments.nodes;i&&(t=t.filter(e=>0==+e.parentID)),m(t),g([b,...w(t).map(e=>({value:e.id,label:o()(` `,e.level)+e.title,level:e.level}))])},onError:e=>{console.error("readDepartments",e)}}),N=async()=>{await j({variables:{filter:{id:{ne:r}}}})};return(0,n.useEffect)(()=>{if(f&&(N(),p(!1)),e>0){let t=c.find(t=>t.id===e);h({value:t?.id,label:t?.title})}else h(b)},[f,e,c]),a.jsx("div",{className:"flex items-center",children:a.jsx(d.Combobox,{options:x.map(e=>({...e,value:String(e.value)})),value:v,onChange:t,placeholder:"Select department",isDisabled:u,isLoading:y})})}},51424:(e,t,r)=>{"use strict";r.d(t,{default:()=>E});var a=r(98768),n=r(60343),l=r(17380),s=r(66263),i=r(71241),o=r.n(i),d=r(7678),u=r.n(d),c=r(14826),m=r.n(c),f=r(72548),p=r(79418),v=r(76342),h=r(69424),x=r(94060),g=r(49296),b=r(55361),w=r(46776),j=r(26100),y=r(71890),N=r(60797),D=r(25394),S=r(78965);let E=({departmentId:e=0})=>{let[t,r]=(0,n.useState)(-1),i=(0,h.useRouter)(),[d,c]=(0,n.useState)({}),[E,I]=(0,n.useState)({}),[C,M]=(0,n.useState)(!1),[B,L]=(0,n.useState)([]),$=o()(e=>{let{name:t,value:r}=e.target;c({...d,[t]:m()(r)})},600),[P,{loading:A}]=(0,f.D)(v.fwu,{onCompleted:e=>{i.push(`/department/info?id=${e.createDepartment.id}`)},onError:e=>{console.error("createDepartment",e)}}),[T,{loading:Z}]=(0,f.D)(v.r_w,{onCompleted:e=>{i.push(`/department/info?id=${e.updateDepartment.id}`)},onError:e=>{console.error("updateDepartment",e)}});(0,n.useEffect)(()=>{r((0,w.GJ)())},[]);let k=async()=>{if(I({}),u()(m()(d.title))){I({...E,title:"Department name is required"});return}let t={id:e,title:d.title,parentID:d.parentID,seaLogsMembers:B.join(",")};0===e?await P({variables:{input:t}}):await T({variables:{input:t}})},[F,{loading:U}]=(0,p.t)(x.tp,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readOneDepartment;c(t),M("0"===t.parentID),L(t.seaLogsMembers.nodes.map(e=>e.id))},onError:e=>{console.error("readOneDepartment",e)}}),q=async()=>{await F({variables:{id:e}})};return((0,n.useEffect)(()=>{e>0&&q()},[e]),(0,n.useEffect)(()=>{(0,w.UU)()},[]),!1===t)?a.jsx(j.Z,{errorMessage:"Oops You do not have the permission to view this section."}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(D.Zb,{className:"mb-4",children:[a.jsx(D.Ol,{children:a.jsx(D.ll,{children:(0,a.jsxs)(D.H1,{children:[0===e?"New":"Edit"," Department"]})})}),a.jsx(D.aY,{children:!d&&e>0?a.jsx(l.WG,{}):(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[a.jsx(N.Label,{children:"Name"}),a.jsx(y.I,{name:"title",type:"text",defaultValue:d?.title,required:!0,onChange:$}),E?.title&&a.jsx("small",{className:"text-red-500 -mb-1",children:E.title})]}),!C&&(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[a.jsx(N.Label,{children:"Head Department"}),a.jsx(g.Z,{excludeId:e,value:d.parentID,onChange:e=>{c({...d,parentID:e.value})}})]}),d.children?.nodes.length>0&&a.jsx("div",{className:"md:px-4 py-4 border border-secondary rounded-lg",children:(0,a.jsxs)("div",{className:"grid grid-cols-3",children:[a.jsx(D.H3,{className:"text-lg",children:"Sub Departments"}),a.jsx("div",{className:"col-span-2 flex flex-col gap-2",children:d.children.nodes.map(e=>a.jsx("div",{children:a.jsx(s.default,{href:`/department/info?id=${e.id}`,children:e.title})},e.id))})]})}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[a.jsx(N.Label,{children:"Members"}),a.jsx("div",{className:"w-full",children:a.jsx(b.Z,{value:B,onChange:e=>{L(e.map(e=>e.value))},filterByAdmin:!0})})]})]})})]}),(0,a.jsxs)(S.V,{children:[a.jsx(s.default,{href:0==e?"/department":`/department/info?id=${e}`,children:a.jsx(D.zx,{variant:"back",children:"Back"})}),a.jsx(D.zx,{onClick:k,disabled:A||Z||U,children:0===e?"Create Department":"Update Department"})]})]})}},22496:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\department\layout.tsx#default`)}};