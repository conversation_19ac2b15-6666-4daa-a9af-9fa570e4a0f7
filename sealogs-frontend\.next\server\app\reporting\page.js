(()=>{var e={};e.id=8709,e.ids=[8709],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},69277:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>d,pages:()=>l,routeModule:()=>p,tree:()=>c}),r(95178),r(71238),r(78398),r(57757),r(48045);var n=r(40060),a=r(33581),s=r(57567),i=r.n(s),o=r(51650),u={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);r.d(t,u);let c=["",{children:["reporting",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,95178)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\page.tsx"],d="/reporting/page",h={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/reporting/page",pathname:"/reporting",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88332:(e,t,r)=>{Promise.resolve().then(r.bind(r,44517))},51767:(e,t,r)=>{Promise.resolve().then(r.bind(r,79832))},83179:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",r="minute",n="hour",a="week",s="month",i="quarter",o="year",u="date",c="Invalid Date",l=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,d=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,h=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},p="en",f={};f[p]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||"th")+"]"}};var y="$isDayjsObject",g=function(e){return e instanceof $||!(!e||!e[y])},m=function e(t,r,n){var a;if(!t)return p;if("string"==typeof t){var s=t.toLowerCase();f[s]&&(a=s),r&&(f[s]=r,a=s);var i=t.split("-");if(!a&&i.length>1)return e(i[0])}else{var o=t.name;f[o]=t,a=o}return!n&&a&&(p=a),a||!n&&p},v=function(e,t){if(g(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new $(r)},x={s:h,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+h(Math.floor(r/60),2,"0")+":"+h(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),a=t.clone().add(n,s),i=r-a<0,o=t.clone().add(n+(i?-1:1),s);return+(-(n+(r-a)/(i?a-o:o-a))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(c){return({M:s,y:o,w:a,d:"day",D:u,h:n,m:r,s:t,ms:e,Q:i})[c]||String(c||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};x.l=m,x.i=g,x.w=function(e,t){return v(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var $=function(){function h(e){this.$L=m(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[y]=!0}var p=h.prototype;return p.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if(x.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(l);if(n){var a=n[2]-1||0,s=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],a,n[3]||1,n[4]||0,n[5]||0,n[6]||0,s)):new Date(n[1],a,n[3]||1,n[4]||0,n[5]||0,n[6]||0,s)}}return new Date(t)}(e),this.init()},p.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},p.$utils=function(){return x},p.isValid=function(){return this.$d.toString()!==c},p.isSame=function(e,t){var r=v(e);return this.startOf(t)<=r&&r<=this.endOf(t)},p.isAfter=function(e,t){return v(e)<this.startOf(t)},p.isBefore=function(e,t){return this.endOf(t)<v(e)},p.$g=function(e,t,r){return x.u(e)?this[t]:this.set(r,e)},p.unix=function(){return Math.floor(this.valueOf()/1e3)},p.valueOf=function(){return this.$d.getTime()},p.startOf=function(e,i){var c=this,l=!!x.u(i)||i,d=x.p(e),h=function(e,t){var r=x.w(c.$u?Date.UTC(c.$y,t,e):new Date(c.$y,t,e),c);return l?r:r.endOf("day")},p=function(e,t){return x.w(c.toDate()[e].apply(c.toDate("s"),(l?[0,0,0,0]:[23,59,59,999]).slice(t)),c)},f=this.$W,y=this.$M,g=this.$D,m="set"+(this.$u?"UTC":"");switch(d){case o:return l?h(1,0):h(31,11);case s:return l?h(1,y):h(0,y+1);case a:var v=this.$locale().weekStart||0,$=(f<v?f+7:f)-v;return h(l?g-$:g+(6-$),y);case"day":case u:return p(m+"Hours",0);case n:return p(m+"Minutes",1);case r:return p(m+"Seconds",2);case t:return p(m+"Milliseconds",3);default:return this.clone()}},p.endOf=function(e){return this.startOf(e,!1)},p.$set=function(a,i){var c,l=x.p(a),d="set"+(this.$u?"UTC":""),h=((c={}).day=d+"Date",c[u]=d+"Date",c[s]=d+"Month",c[o]=d+"FullYear",c[n]=d+"Hours",c[r]=d+"Minutes",c[t]=d+"Seconds",c[e]=d+"Milliseconds",c)[l],p="day"===l?this.$D+(i-this.$W):i;if(l===s||l===o){var f=this.clone().set(u,1);f.$d[h](p),f.init(),this.$d=f.set(u,Math.min(this.$D,f.daysInMonth())).$d}else h&&this.$d[h](p);return this.init(),this},p.set=function(e,t){return this.clone().$set(e,t)},p.get=function(e){return this[x.p(e)]()},p.add=function(e,i){var u,c=this;e=Number(e);var l=x.p(i),d=function(t){var r=v(c);return x.w(r.date(r.date()+Math.round(t*e)),c)};if(l===s)return this.set(s,this.$M+e);if(l===o)return this.set(o,this.$y+e);if("day"===l)return d(1);if(l===a)return d(7);var h=((u={})[r]=6e4,u[n]=36e5,u[t]=1e3,u)[l]||1,p=this.$d.getTime()+e*h;return x.w(p,this)},p.subtract=function(e,t){return this.add(-1*e,t)},p.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||c;var n=e||"YYYY-MM-DDTHH:mm:ssZ",a=x.z(this),s=this.$H,i=this.$m,o=this.$M,u=r.weekdays,l=r.months,h=r.meridiem,p=function(e,r,a,s){return e&&(e[r]||e(t,n))||a[r].slice(0,s)},f=function(e){return x.s(s%12||12,e,"0")},y=h||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n};return n.replace(d,function(e,n){return n||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return x.s(t.$y,4,"0");case"M":return o+1;case"MM":return x.s(o+1,2,"0");case"MMM":return p(r.monthsShort,o,l,3);case"MMMM":return p(l,o);case"D":return t.$D;case"DD":return x.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return p(r.weekdaysMin,t.$W,u,2);case"ddd":return p(r.weekdaysShort,t.$W,u,3);case"dddd":return u[t.$W];case"H":return String(s);case"HH":return x.s(s,2,"0");case"h":return f(1);case"hh":return f(2);case"a":return y(s,i,!0);case"A":return y(s,i,!1);case"m":return String(i);case"mm":return x.s(i,2,"0");case"s":return String(t.$s);case"ss":return x.s(t.$s,2,"0");case"SSS":return x.s(t.$ms,3,"0");case"Z":return a}return null}(e)||a.replace(":","")})},p.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},p.diff=function(e,u,c){var l,d=this,h=x.p(u),p=v(e),f=(p.utcOffset()-this.utcOffset())*6e4,y=this-p,g=function(){return x.m(d,p)};switch(h){case o:l=g()/12;break;case s:l=g();break;case i:l=g()/3;break;case a:l=(y-f)/6048e5;break;case"day":l=(y-f)/864e5;break;case n:l=y/36e5;break;case r:l=y/6e4;break;case t:l=y/1e3;break;default:l=y}return c?l:x.a(l)},p.daysInMonth=function(){return this.endOf(s).$D},p.$locale=function(){return f[this.$L]},p.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=m(e,t,!0);return n&&(r.$L=n),r},p.clone=function(){return x.w(this.$d,this)},p.toDate=function(){return new Date(this.valueOf())},p.toJSON=function(){return this.isValid()?this.toISOString():null},p.toISOString=function(){return this.$d.toISOString()},p.toString=function(){return this.$d.toUTCString()},h}(),M=$.prototype;return v.prototype=M,[["$ms",e],["$s",t],["$m",r],["$H",n],["$W","day"],["$M",s],["$y",o],["$D",u]].forEach(function(e){M[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),v.extend=function(e,t){return e.$i||(e(t,$,v),e.$i=!0),v},v.locale=m,v.isDayjs=g,v.unix=function(e){return v(1e3*e)},v.en=f[p],v.Ls=f,v.p={},v},e.exports=t()},18479:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,a=Array(n);++r<n;)a[r]=t(e[r],r,e);return a}},22060:(e,t,r)=>{var n=r(51858),a=r(18479),s=r(55813),i=r(15903),o=1/0,u=n?n.prototype:void 0,c=u?u.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(s(t))return a(t,e)+"";if(i(t))return c?c.call(t):"";var r=t+"";return"0"==r&&1/t==-o?"-0":r}},15903:(e,t,r)=>{var n=r(55296),a=r(48377);e.exports=function(e){return"symbol"==typeof e||a(e)&&"[object Symbol]"==n(e)}},16266:(e,t,r)=>{var n=r(22060);e.exports=function(e){return null==e?"":n(e)}},3233:(e,t,r)=>{var n=r(16266),a=0;e.exports=function(e){var t=++a;return n(e)+t}},44517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(98768);r(60343);var a=r(32993),s=r(64837);function i({children:e}){return n.jsx(a.default,{children:n.jsx(s.Z,{children:e})})}},79832:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var n=r(98768),a=r(69424);r(46776);var s=r(26100),i=r(60343),o=r(74389);function u(){let e=(0,a.useSearchParams)();e.get("taskID"),e.get("redirectTo");let[t,r]=(0,i.useState)(!1),[u,c]=(0,i.useState)(!1);return t&&u?n.jsx(o.Z,{}):t?n.jsx(s.Z,{errorMessage:"Oops You do not have the permission to view this section."}):n.jsx(s.Z,{})}},74389:(e,t,r)=>{"use strict";r.d(t,{Z:()=>$});var n=r(98768),a=r(69424),s=r(53363),i=r(97428);let o=(0,i.Z)("Fuel",[["line",{x1:"3",x2:"15",y1:"22",y2:"22",key:"xegly4"}],["line",{x1:"4",x2:"14",y1:"9",y2:"9",key:"xcnuvu"}],["path",{d:"M14 22V4a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v18",key:"16j0yd"}],["path",{d:"M14 13h2a2 2 0 0 1 2 2v2a2 2 0 0 0 2 2a2 2 0 0 0 2-2V9.83a2 2 0 0 0-.59-1.42L18 5",key:"7cu91f"}]]),u=(0,i.Z)("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var c=r(72997);let l=(0,i.Z)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]),d=(0,i.Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),h=(0,i.Z)("Gauge",[["path",{d:"m12 14 4-4",key:"9kzdfg"}],["path",{d:"M3.34 19a10 10 0 1 1 17.32 0",key:"19p75a"}]]),p=(0,i.Z)("ListChecks",[["path",{d:"m3 17 2 2 4-4",key:"1jhpwq"}],["path",{d:"m3 7 2 2 4-4",key:"1obspn"}],["path",{d:"M13 6h8",key:"15sg57"}],["path",{d:"M13 12h8",key:"h98zly"}],["path",{d:"M13 18h8",key:"oe0vm4"}]]);var f=r(70996);let y=(0,i.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),g=(0,i.Z)("Navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]]);var m=r(25394),v=r(66263);let x=[{name:"Crew Seatime Report",icon:n.jsx(s.Z,{}),url:"/reporting/crew-seatime-report"},{name:"Simple Fuel Report",icon:n.jsx(o,{}),url:"/reporting/simple-fuel-report"},{name:"Activity Report",icon:n.jsx(u,{}),url:"/reporting/activity-reports"},{name:"Engine Hours Report",icon:n.jsx(c.Z,{}),url:"/reporting/engine-hours-report"},{name:"Maintenance Status and Activity Report",icon:n.jsx(l,{}),url:"/reporting/maintenance-status-activity"},{name:"Maintenance Cost Track Report",icon:n.jsx(d,{}),url:"/reporting/maintenance-cost-track"},{name:"Fuel Analysis Report",icon:n.jsx(h,{}),url:"/reporting/fuel-analysis"},{name:"Fuel Tasking Analysis Report",icon:n.jsx(p,{}),url:"/reporting/fuel-tasking-analysis"},{name:"Detailed Fuel Report",icon:n.jsx(f.Z,{}),url:"/reporting/detailed-fuel-report"},{name:"Summary Fuel Report",icon:n.jsx(f.Z,{}),url:"/reporting/fuel-summary-report"},{name:"Service Report",icon:n.jsx(y,{}),url:"/reporting/service-report"},{name:"Trip Report",icon:n.jsx(g,{}),url:"/reporting/trip-report"}];function $(){return(0,a.useRouter)(),(0,n.jsxs)("div",{className:"w-full grid gap-8 mt-8",children:[n.jsx(m.H1,{children:"Reporting"}),n.jsx("div",{className:"flex flex-wrap gap-2",children:x.map(e=>n.jsx(v.default,{href:e.url,children:n.jsx(m.zx,{iconLeft:e.icon,variant:"primaryOutline",children:e.name})}))})]})}},71238:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)},95178:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\page.tsx#default`)},84961:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},72997:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},46020:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},70996:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},23379:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},7671:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},53363:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[864,8865,3563,6263,8189,9507,7602,6451,4234,2925,5394,4837],()=>r(69277));module.exports=n})();