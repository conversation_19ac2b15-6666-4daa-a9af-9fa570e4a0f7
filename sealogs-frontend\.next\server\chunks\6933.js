"use strict";exports.id=6933,exports.ids=[6933],exports.modules={10769:(e,t,i)=>{i.d(t,{Z:()=>d});var s=i(83179),a=i.n(s),r=i(86708),o=i(73366),n=i(5417),l=i(92871);class c{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),i=t.id,s={...t,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},o=await this.getById(i);return o?await r.Z.BarCrossingChecklist.update(i,s):await r.Z.BarCrossingChecklist.add(s),o=await this.getById(i),console.log("BarCrossingChecklistModel save",e,o),o}catch(t){console.error("BarCrossingChecklistModel save",e,t)}}async getAll(){try{let e=await r.Z.BarCrossingChecklist.toArray();return console.log("BarCrossingChecklistModel getAll",e),e}catch(e){console.error("BarCrossingChecklistModel getAll",e)}}async getById(e){try{let t=await r.Z.BarCrossingChecklist.get(`${e}`),i=await this.addRelationships(t);return console.log("BarCrossingChecklistModel getById",i),i}catch(t){console.error("BarCrossingChecklistModel getById",e,t)}}async getByIds(e){try{let t=await r.Z.BarCrossingChecklist.where("id").anyOf(e).toArray(),i=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("BarCrossingChecklistModel getByIds",e,i),i}catch(t){console.error("BarCrossingChecklistModel getByIds",e,t)}}async getByVesselID(e){try{let t=await r.Z.BarCrossingChecklist.where("vesselID").equals(`${e}`).toArray();return console.log("BarCrossingChecklistModel getByVesselID",e,t),t}catch(t){console.error("BarCrossingChecklistModel getByVesselID",e,t)}}async getByFieldID(e,t){try{let i=await r.Z.BarCrossingChecklist.where(`${e}`).equals(`${t}`).toArray();return console.log("BarCrossingChecklistModel getByFieldID",e,t,reponse),i}catch(i){console.error("BarCrossingChecklistModel getByFieldID",e,t,i)}}async bulkAdd(e){try{return await r.Z.BarCrossingChecklist.bulkAdd(e),console.log("BarCrossingChecklistModel bulkAdd",e),e}catch(t){if("BulkError"===t.name){let i=t.failuresByPos.map(e=>e.key),s=e.filter(e=>!i.includes(e.id));return await r.Z.BarCrossingChecklist.bulkAdd(s),console.log("BarCrossingChecklistModel bulkAdd::BulkError",e,t),e}console.log("BarCrossingChecklistModel bulkAdd",e,t)}}async addRelationships(e){if(!e)return console.log("BarCrossingChecklistModel addRelationships",e),e;{let t=+e.memberID>0?await this.seaLogsMemberModel.getById(e.memberID):null,i=+e.id>0?await this.riskFactorModel.getByFieldID("barCrossingChecklistID",e.id):null;i&&(i=await Promise.all(i.map(async e=>{let t=e.mitigationStrategy;if("string"==typeof t){let i=t.split(","),s=await this.mitigationStrategyModel.getByIds(i);e.mitigationStrategy={nodes:s}}return e})));let s={...e,member:t,riskFactors:{nodes:i}};return console.log("BarCrossingChecklistModel addRelationships",e,s),s}}async setProperty(e){try{if(e){let t=await r.Z.BarCrossingChecklist.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.BarCrossingChecklist.update(e,t),console.log("BarCrossingChecklistModel setProperty",e,t),t}}catch(t){console.error("BarCrossingChecklistModel setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.BarCrossingChecklist.update(e.id,e)})),console.log("BarCrossingChecklistModel multiUpdate",e)}catch(t){console.error("BarCrossingChecklistModel multiUpdate",e,t)}}constructor(){this.seaLogsMemberModel=new o.Z,this.riskFactorModel=new n.Z,this.mitigationStrategyModel=new l.Z}}let d=c},71338:(e,t,i)=>{i.d(t,{Z:()=>d});var s=i(83179),a=i.n(s),r=i(86708),o=i(1971),n=i(46372),l=i(70413);class c{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),i=t.id,s={...t,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},o=await this.getById(i);return o?await r.Z.CGEventMission.update(i,s):await r.Z.CGEventMission.add(s),o=await this.getById(i),console.log("CGEventMissionModel save",e,o),o}catch(t){console.error("CGEventMissionModel save",e,t)}}async getAll(){try{let e=await r.Z.CGEventMission.toArray();return console.log("CGEventMissionModel getAll",e),e}catch(e){console.error("CGEventMissionModel getAll",e)}}async getById(e){try{let t=await r.Z.CGEventMission.get(`${e}`),i=await this.addRelationships(t);return console.log("CGEventMissionModel getById",e,i),i}catch(t){console.error("CGEventMissionModel getById",e,t)}}async getByIds(e){try{let t=await r.Z.CGEventMission.where("id").anyOf(e).toArray(),i=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("CGEventMissionModel getByIds",e,i),i}catch(t){console.error("CGEventMissionModel getByIds",e,t)}}async bulkAdd(e){try{return await r.Z.CGEventMission.bulkAdd(e),console.log("CGEventMissionModel bulkAdd",e),e}catch(t){if("BulkError"===t.name){let i=t.failuresByPos.map(e=>e.key),s=e.filter(e=>!i.includes(e.id));return await r.Z.CGEventMission.bulkAdd(s),console.log("CGEventMissionModel bulkAdd::BulkError",e,t),e}console.log("CGEventMissionModel bulkAdd",e,t)}}async addRelationships(e){if(!e)return console.log("CGEventMissionModel addRelationships",e),e;{let t=+e.currentLocationID>0?await this.geoLocationModel.getById(e.currentLocationID):null,i=+e.vesselPositionID>0?await this.VehiclePositionModel.getById(e.vesselPositionID):null,s=+e.vesselID>0?await this.vesselModel.getById(e.vesselID):null,a={...e,currentLocation:t,vesselPosition:i,vessel:s};return console.log("CGEventMissionModel addRelationships",e,a),a}}async setProperty(e){try{if(e){let t=await r.Z.CGEventMission.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.CGEventMission.update(e,t),console.log("CGEventMissionModel setProperty",e,t),t}}catch(t){console.error("CGEventMissionModel setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.CGEventMission.update(e.id,e)})),console.log("CGEventMissionModel multiUpdate",e)}catch(t){console.error("CGEventMissionModel multiUpdate",e,t)}}constructor(){this.geoLocationModel=new o.Z,this.VehiclePositionModel=new n.Z,this.vesselModel=new l.Z}}let d=c},7884:(e,t,i)=>{i.d(t,{Z:()=>d});var s=i(83179),a=i.n(s),r=i(86708),o=i(17405),n=i(1971),l=i(10769);class c{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),i=t.id,s={...t,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},o=await this.getById(i);return o?await r.Z.EventType_BarCrossing.update(i,s):await r.Z.EventType_BarCrossing.add(s),o=await this.getById(i),console.log("EventType_BarCrossing save",e,o),o}catch(t){console.error("EventType_BarCrossing save",e,t)}}async getAll(){try{let e=await r.Z.EventType_BarCrossing.toArray();return console.log("EventType_BarCrossing getAll",e),e}catch(e){console.error("EventType_BarCrossing getAll",e)}}async getById(e){try{let t=await r.Z.EventType_BarCrossing.get(`${e}`),i=await this.addRelationships(t);return console.log("EventType_BarCrossing getById",e,i),i}catch(t){console.error("EventType_BarCrossing getById",e,t)}}async getByIds(e){try{let t=await r.Z.EventType_BarCrossing.where("id").anyOf(e).toArray(),i=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("EventType_BarCrossing getByIds",e,i),i}catch(t){console.error("EventType_BarCrossing getByIds",e,t)}}async getByFieldID(e,t){try{let i=await r.Z.EventType_BarCrossing.where(`${e}`).equals(`${t}`).toArray();return console.log("EventType_BarCrossing getByFieldID",e,t,i),i}catch(i){console.error("EventType_BarCrossing getByFieldID",e,t,i)}}async bulkAdd(e){try{return await r.Z.EventType_BarCrossing.bulkAdd(e),console.log("EventType_BarCrossing bulkAdd",e),e}catch(t){if("BulkError"===t.name){let i=t.failuresByPos.map(e=>e.key),s=e.filter(e=>!i.includes(e.id));return await r.Z.EventType_BarCrossing.bulkAdd(s),console.log("EventType_BarCrossing bulkAdd::BulkError",e,t),e}console.error("EventType_BarCrossing bulkAdd",e,t)}}async addRelationships(e){if(!e)return console.log("EventType_BarCrossing addRelationships",e),e;{let t=+e.tripEventID>0?await this.tripEventModel.getById(e.tripEventID):null,i=+e.geoLocationID>0?await this.geoLocationModel.getById(e.geoLocationID):null,s=+e.geoLocationCompletedID>0?await this.geoLocationModel.getById(e.geoLocationCompletedID):null,a=+e.barCrossingChecklistID>0?await this.barCrossingChecklistModel.getById(e.barCrossingChecklistID):null;return console.log("EventType_BarCrossing addRelationships",e),{...e,tripEvent:t,geoLocation:i,geoLocationCompleted:s,barCrossingChecklist:a}}}async setProperty(e){try{if(e){let t=await r.Z.EventType_BarCrossing.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.EventType_BarCrossing.update(e,t),console.log("EventType_BarCrossing setProperty",e,t),t}}catch(t){console.error("EventType_BarCrossing setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.EventType_BarCrossing.update(e.id,e)})),console.log("EventType_BarCrossing multiUpdate",e)}catch(t){console.error("EventType_BarCrossing multiUpdate",e,t)}}constructor(){this.tripEventModel=new o.Z,this.geoLocationModel=new n.Z,this.barCrossingChecklistModel=new l.Z}}let d=c},87224:(e,t,i)=>{i.d(t,{Z:()=>c});var s=i(83179),a=i.n(s),r=i(86708),o=i(96197),n=i(71338);class l{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),i=t.id,s={...t,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},o=await this.getById(i);return o?await r.Z.EventType_PersonRescue.update(i,s):await r.Z.EventType_PersonRescue.add(s),o=await this.getById(i),console.log("EventType_PersonRescue save",e,o),o}catch(t){console.error("EventType_PersonRescue save",e,t)}}async getAll(){try{let e=await r.Z.EventType_PersonRescue.toArray();return console.log("EventType_PersonRescue getAll",e),e}catch(e){console.error("EventType_PersonRescue getAll",e)}}async getById(e){try{let t=await r.Z.EventType_PersonRescue.get(`${e}`),i=await this.addRelationships(t);return console.log("EventType_PersonRescue getById",e,i),i}catch(t){console.error("EventType_PersonRescue getById",e,t)}}async getByIds(e){try{let t=await r.Z.EventType_PersonRescue.where("id").anyOf(e).toArray(),i=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("EventType_PersonRescue getByIds",e,i),i}catch(t){console.error("EventType_PersonRescue getByIds",e,t)}}async getByTripEventID(e){try{let t=await r.Z.EventType_PersonRescue.where("tripEventID").equals(`${e}`).toArray(),i=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("EventType_PersonRescue getByTripEventID",e,i),i}catch(t){console.error("EventType_PersonRescue getByTripEventID",e,t)}}async getByFieldID(e,t){try{let i=await r.Z.EventType_PersonRescue.where(`${e}`).equals(`${t}`).toArray();return console.log("EventType_PersonRescue getByFieldID",e,t,i),i}catch(i){console.error("EventType_PersonRescue getByFieldID",e,t,i)}}async bulkAdd(e){try{return await r.Z.EventType_PersonRescue.bulkAdd(e),console.log("EventType_PersonRescue bulkAdd",e),e}catch(t){if("BulkError"===t.name){let i=t.failuresByPos.map(e=>e.key),s=e.filter(e=>!i.includes(e.id));return await r.Z.EventType_PersonRescue.bulkAdd(s),console.log("EventType_PersonRescue bulkAdd::BulkError",e,t),e}console.error("EventType_PersonRescue bulkAdd",e,t)}}async addRelationships(e){if(!e)return console.log("EventType_PersonRescue addRelationships",e),e;{let t=+e.id>0?await this.missionTimelineModel.getByPersonRescueID(e.id):null,i=+e.missionID>0?await this.cgEventMissionModel.getById(e.missionID):null;return console.log("EventType_PersonRescue addRelationships",e),{...e,missionTimeline:t,mission:i}}}async setProperty(e){try{if(e){let t=await r.Z.EventType_PersonRescue.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.EventType_PersonRescue.update(e,t),console.log("EventType_PersonRescue setProperty",e,t),t}}catch(t){console.error("EventType_PersonRescue setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.EventType_PersonRescue.update(e.id,e)})),console.log("EventType_PersonRescue multiUpdate",e)}catch(t){console.error("EventType_PersonRescue multiUpdate",e,t)}}constructor(){this.missionTimelineModel=new o.Z,this.cgEventMissionModel=new n.Z}}let c=l},30084:(e,t,i)=>{i.d(t,{Z:()=>u});var s=i(83179),a=i.n(s),r=i(86708),o=i(17405),n=i(1971),l=i(33022),c=i(81050),d=i(87224),g=i(18940);class y{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),i=t.id,s={...t,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},o=await this.getById(i);return o?await r.Z.EventType_Tasking.update(i,s):await r.Z.EventType_Tasking.add(s),o=await this.getById(i),console.log("EventType_Tasking save",e,o),o}catch(t){console.error("EventType_Tasking save",e,t)}}async getAll(){try{let e=await r.Z.EventType_Tasking.toArray();return console.log("EventType_Tasking getAll",e),e}catch(e){console.error("EventType_Tasking getAll",e)}}async getById(e){try{let t=await r.Z.EventType_Tasking.get(`${e}`),i=await this.addRelationships(t);return console.log("EventType_Tasking getById",e,i),i}catch(t){console.error("EventType_Tasking getById",e,t)}}async getByIds(e){try{let t=await r.Z.EventType_Tasking.where("id").anyOf(e).toArray(),i=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("EventType_Tasking getByIds",e,i),i}catch(t){console.error("EventType_Tasking getByIds",e,t)}}async getByVesselID(e){try{let t=await r.Z.EventType_Tasking.where("vesselID").equals(`${e}`).toArray();return console.log("EventType_Tasking getByVesselID",e,t),t}catch(t){console.error("EventType_Tasking getByVesselID",e,t)}}async getByFieldID(e,t){try{let i=await r.Z.EventType_Tasking.where(`${e}`).equals(`${t}`).toArray();return console.log("EventType_Tasking getByFieldID",e,t,i),i}catch(i){console.error("EventType_Tasking getByFieldID",e,t,i)}}async bulkAdd(e){try{return await r.Z.EventType_Tasking.bulkAdd(e),console.log("EventType_Tasking bulkAdd",e),e}catch(t){if("BulkError"===t.name){let i=t.failuresByPos.map(e=>e.key),s=e.filter(e=>!i.includes(e.id));return await r.Z.EventType_Tasking.bulkAdd(s),console.log("EventType_Tasking bulkAdd::BulkError",e,t),e}console.error("EventType_Tasking bulkAdd",e,t)}}async addRelationships(e){if(!e)return console.log("EventType_Tasking addRelationships",e),e;{let t=+e.geoLocationID>0?await this.geoLocationModel.getById(e.geoLocationID):null,i=+e.vesselRescueID>0?await this.eventType_VesselRescueModel.getById(e.vesselRescueID):null,s=+e.personRescueID>0?await this.eventType_PersonRescueModel.getById(e.personRescueID):null,a=+e.towingChecklistID>0?await this.towingChecklistModel.getById(e.towingChecklistID):null,r=+e.eventType_TaskingID>0?await this.fuelLogModel.getByFieldID("eventType_TaskingID",e.eventType_TaskingID):[];return console.log("EventType_Tasking addRelationships",e),{...e,geoLocation:t,vesselRescue:i,personRescue:s,towingChecklist:a,fuelLog:{nodes:r}}}}async setProperty(e){try{if(e){let t=await r.Z.EventType_Tasking.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.EventType_Tasking.update(e,t),console.log("EventType_Tasking setProperty",e,t),t}}catch(t){console.error("EventType_Tasking setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.EventType_Tasking.update(e.id,e)})),console.log("EventType_Tasking multiUpdate",e)}catch(t){console.error("EventType_Tasking multiUpdate",e,t)}}constructor(){this.tripEventModel=new o.Z,this.geoLocationModel=new n.Z,this.fuelLogModel=new l.Z,this.eventType_VesselRescueModel=new c.Z,this.eventType_PersonRescueModel=new d.Z,this.towingChecklistModel=new g.Z}}let u=y},81050:(e,t,i)=>{i.d(t,{Z:()=>d});var s=i(83179),a=i.n(s),r=i(86708),o=i(96197),n=i(71338),l=i(1971);class c{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),i=t.id,s={...t,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},o=await this.getById(i);return o?await r.Z.EventType_VesselRescue.update(i,s):await r.Z.EventType_VesselRescue.add(s),o=await this.getById(i),console.log("EventType_VesselRescue save",e,o),o}catch(t){console.error("EventType_VesselRescue save",e,t)}}async getAll(){try{let e=await r.Z.EventType_VesselRescue.toArray();return console.log("EventType_VesselRescue getAll",e),e}catch(e){console.error("EventType_VesselRescue getAll",e)}}async getById(e){try{let t=await r.Z.EventType_VesselRescue.get(`${e}`),i=await this.addRelationships(t);return console.log("EventType_VesselRescue getById",e,i),i}catch(t){console.error("EventType_VesselRescue getById",e,t)}}async getByIds(e){try{let t=await r.Z.EventType_VesselRescue.where("id").anyOf(e).toArray(),i=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("EventType_VesselRescue getByIds",e,i),i}catch(t){console.error("EventType_VesselRescue getByIds",e,t)}}async getByTripEventID(e){try{let t=await r.Z.EventType_VesselRescue.where("tripEventID").equals(`${e}`).toArray(),i=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("EventType_VesselRescue getByTripEventID",e,i),i}catch(t){console.error("EventType_VesselRescue getByTripEventID",e,t)}}async getByFieldID(e,t){try{let i=await r.Z.EventType_VesselRescue.where(`${e}`).equals(`${t}`).toArray();return console.log("EventType_VesselRescue getByFieldID",e,t,i),i}catch(i){console.error("EventType_VesselRescue getByFieldID",e,t,i)}}async bulkAdd(e){try{return await r.Z.EventType_VesselRescue.bulkAdd(e),console.log("EventType_VesselRescue bulkAdd",e),e}catch(t){if("BulkError"===t.name){let i=t.failuresByPos.map(e=>e.key),s=e.filter(e=>!i.includes(e.id));return await r.Z.EventType_VesselRescue.bulkAdd(s),console.log("EventType_VesselRescue bulkAdd::BulkError",e,t),e}console.error("EventType_VesselRescue bulkAdd",e,t)}}async addRelationships(e){if(!e)return console.log("EventType_VesselRescue addRelationships",e),e;{let t=+e.id>0?await this.missionTimelineModel.getByVesselRescueID(e.id):null,i=+e.missionID>0?await this.cgEventMissionModel.getById(e.missionID):null,s=+e.vesselLocationID>0?await this.geoLocationModel.getById(e.vesselLocationID):null;return console.log("EventType_VesselRescue addRelationships",e),{...e,missionTimeline:t,mission:i,vesselLocation:s}}}async setProperty(e){try{if(e){let t=await r.Z.EventType_VesselRescue.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.EventType_VesselRescue.update(e,t),console.log("EventType_VesselRescue setProperty",e,t),t}}catch(t){console.error("EventType_VesselRescue setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.EventType_VesselRescue.update(e.id,e)})),console.log("EventType_VesselRescue multiUpdate",e)}catch(t){console.error("EventType_VesselRescue multiUpdate",e,t)}}constructor(){this.missionTimelineModel=new o.Z,this.cgEventMissionModel=new n.Z,this.geoLocationModel=new l.Z}}let d=c},33022:(e,t,i)=>{i.d(t,{Z:()=>n});var s=i(83179),a=i.n(s),r=i(86708);class o{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),i=t.id,s={...t,__typename:"FuelLog",idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},o=await this.getById(i);return o?await r.Z.FuelLog.update(t.id,s):await r.Z.FuelLog.add(s),o=await this.getById(i),console.log("FuelLog save",e,o),o}catch(t){console.error("FuelLog save",e,t)}}async update(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t]));return await r.Z.FuelLog.update(t.id,{...t,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")}),console.log("FuelLog update",e,t),t}catch(t){console.error("FuelLog update",e,t)}}async getAll(){try{let e=await r.Z.FuelLog.toArray();return console.log("FuelLog getAll",e),e}catch(e){console.error("FuelLog getAll",e)}}async getById(e){try{let t=await r.Z.FuelLog.get(`${e}`);return console.log("FuelLog getById",e,t),t}catch(t){console.error("FuelLog getById",e,t)}}async getByFieldID(e,t){try{let i=await r.Z.FuelLog.where(`${e}`).equals(`${t}`).toArray();return console.log("FuelLog getByFieldID",e,t,i),i}catch(i){console.error("FuelLog getByFieldID",e,t,i)}}async getByIds(e){try{let t=await r.Z.FuelLog.where("id").anyOf(e).toArray();return console.log("FuelLog getByIds",e,t),t}catch(t){console.error("FuelLog getByIds",e,t)}}async bulkAdd(e){try{return await r.Z.FuelLog.bulkAdd(e),console.log("FuelLog bulkAdd",e),e}catch(t){if("BulkError"===t.name){let i=t.failuresByPos.map(e=>e.key),s=e.filter(e=>!i.includes(e.id));return await r.Z.FuelLog.bulkAdd(s),console.log("FuelLog bulkAdd::BulkError",e,t),e}console.error("FuelLog bulkAdd",e,t)}}async delete(e){try{let t=await r.Z.FuelLog.delete(e);return console.log("FuelLog delete",e,t),t}catch(t){console.error("FuelLog delete",e,t)}}async setProperty(e){try{if(e){let t=await r.Z.FuelLog.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.FuelLog.update(e,t),console.log("FuelLog setProperty",e,t),t}}catch(t){console.error("FuelLog setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.FuelLog.update(e.id,e)})),console.log("FuelLog multiUpdate",e)}catch(t){console.error("FuelLog multiUpdate",e,t)}}}let n=o},96197:(e,t,i)=>{i.d(t,{Z:()=>l});var s=i(83179),a=i.n(s),r=i(86708),o=i(73366);class n{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),i=t.id,s={...t,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},o=await this.getById(i);return o?await r.Z.MissionTimeline.update(i,s):await r.Z.MissionTimeline.add(s),o=await this.getById(i),console.log("MissionTimeline save",e,o),o}catch(t){console.error("MissionTimeline save",e,t)}}async getAll(){try{let e=await r.Z.MissionTimeline.toArray();return console.log("MissionTimeline getAll",e),e}catch(e){console.error("MissionTimeline getAll",e)}}async getById(e){try{let t=await r.Z.MissionTimeline.get(`${e}`),i=await this.addRelationships(t);return console.log("MissionTimeline getById",e,i),i}catch(t){console.error("MissionTimeline getById",e,t)}}async getByIds(e){try{let t=await r.Z.MissionTimeline.where("id").anyOf(e).toArray(),i=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("MissionTimeline getByIds",e,i),i}catch(t){console.error("MissionTimeline getByIds",e,t)}}async getByVesselRescueID(e){try{let t=await r.Z.MissionTimeline.where("vesselRescueID").equals(`${e}`).toArray();return console.log("MissionTimeline getByVesselRescueID",e,t),t}catch(t){console.error("MissionTimeline getByVesselRescueID",e,t)}}async getByPersonRescueID(e){try{let t=await r.Z.MissionTimeline.where("personRescueID").equals(`${e}`).toArray();return console.log("MissionTimeline getByPersonRescueID",e,t),t}catch(t){console.error("MissionTimeline getByPersonRescueID",e,t)}}async bulkAdd(e){try{return await r.Z.MissionTimeline.bulkAdd(e),console.log("MissionTimeline bulkAdd",e),e}catch(t){if("BulkError"===t.name){let i=t.failuresByPos.map(e=>e.key),s=e.filter(e=>!i.includes(e.id));return await r.Z.MissionTimeline.bulkAdd(s),console.log("MissionTimeline bulkAdd::BulkError",e,t),e}console.error("MissionTimeline bulkAdd",e,t)}}async addRelationships(e){if(!e)return console.log("MissionTimeline addRelationships",e),e;{let t=+e.authorID>0?await this.seaLogsMemberModel.getById(e.authorID):null;return console.log("MissionTimeline addRelationships",e),{...e,author:t}}}async setProperty(e){try{if(e){let t=await r.Z.MissionTimeline.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.MissionTimeline.update(e,t),console.log("MissionTimeline setProperty",e,t),t}}catch(t){console.error("MissionTimeline setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.MissionTimeline.update(e.id,e)})),console.log("MissionTimeline multiUpdate",e)}catch(t){console.error("MissionTimeline multiUpdate",e,t)}}constructor(){this.seaLogsMemberModel=new o.Z}}let l=n},92871:(e,t,i)=>{i.d(t,{Z:()=>n});var s=i(83179),a=i.n(s),r=i(86708);class o{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),i=t.id,s={...t,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},o=await this.getById(i);return o?await r.Z.MitigationStrategy.update(i,s):await r.Z.MitigationStrategy.add(s),o=await this.getById(i),console.log("MitigationStrategy save",e,o),o}catch(t){console.error("MitigationStrategy save",e,t)}}async getAll(){try{let e=await r.Z.MitigationStrategy.toArray();return console.log("MitigationStrategy getAll",e),e}catch(e){console.error("MitigationStrategy getAll",e)}}async getById(e){try{let t=await r.Z.MitigationStrategy.get(`${e}`),i=await this.addRelationships(t);return console.log("MitigationStrategy getById",e,i),i}catch(t){console.error("MitigationStrategy getById",e,t)}}async getByIds(e){try{let t=await r.Z.MitigationStrategy.where("id").anyOf(e).toArray(),i=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("MitigationStrategy getByIds",e,i),i}catch(t){console.error("MitigationStrategy getByIds",e,t)}}async getByVesselID(e){try{let t=await r.Z.MitigationStrategy.where("vesselID").equals(`${e}`).toArray();return console.log("MitigationStrategy getByVesselID",e,t),t}catch(t){console.error("MitigationStrategy getByVesselID",e,t)}}async bulkAdd(e){try{return await r.Z.MitigationStrategy.bulkAdd(e),console.log("MitigationStrategy bulkAdd",e),e}catch(t){if("BulkError"===t.name){let i=t.failuresByPos.map(e=>e.key),s=e.filter(e=>!i.includes(e.id));return await r.Z.MitigationStrategy.bulkAdd(s),console.log("MitigationStrategy bulkAdd::BulkError",e,t),e}console.error("MitigationStrategy bulkAdd",e,t)}}async addRelationships(e){return console.log("MitigationStrategy addRelationships",e),e}async setProperty(e){try{if(e){let t=await r.Z.MitigationStrategy.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.MitigationStrategy.update(e,t),console.log("MitigationStrategy setProperty",e,t),t}}catch(t){console.error("MitigationStrategy setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.MitigationStrategy.update(e.id,e)})),console.log("MitigationStrategy multiUpdate",e)}catch(t){console.error("MitigationStrategy multiUpdate",e,t)}}}let n=o},5417:(e,t,i)=>{i.d(t,{Z:()=>l});var s=i(83179),a=i.n(s),r=i(86708),o=i(92871);class n{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),i=t.id,s={...t,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},o=await this.getById(i);return o?await r.Z.RiskFactor.update(i,s):await r.Z.RiskFactor.add(s),o=await this.getById(i),console.log("RiskFactor save",e,o),o}catch(t){console.error("RiskFactor save",e,t)}}async getAll(){try{let e=await r.Z.RiskFactor.toArray();return console.log("RiskFactor getAll",e),e}catch(e){console.error("RiskFactor getAll",e)}}async getById(e){try{let t=await r.Z.RiskFactor.get(`${e}`),i=await this.addRelationships(t);return console.log("RiskFactor getById",e,i),i}catch(t){console.error("RiskFactor getById",e,t)}}async getByIds(e){try{let t=await r.Z.RiskFactor.where("id").anyOf(e).toArray(),i=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("RiskFactor getByIds",e,i),i}catch(t){console.error("RiskFactor getByIds",e,t)}}async getByVesselID(e){try{let t=await r.Z.RiskFactor.where("vesselID").equals(`${e}`).toArray();return console.log("RiskFactor getByVesselID",e,t),t}catch(t){console.error("RiskFactor getByVesselID",e,t)}}async getByTowingChecklistID(e){try{let t=await r.Z.RiskFactor.where("towingChecklistID").equals(`${e}`).toArray();return console.log("RiskFactor getByTowingChecklistID",e,t),t}catch(t){console.error("RiskFactor getByTowingChecklistID",e,t)}}async getByFieldID(e,t){try{let i=await r.Z.RiskFactor.where(`${e}`).equals(`${t}`).toArray();return console.log("RiskFactor getByFieldID",e,t,i),i}catch(i){console.error("RiskFactor getByFieldID",e,t,i)}}async bulkAdd(e){try{return await r.Z.RiskFactor.bulkAdd(e),console.log("RiskFactor bulkAdd",e),e}catch(t){if("BulkError"===t.name){let i=t.failuresByPos.map(e=>e.key),s=e.filter(e=>!i.includes(e.id));return await r.Z.RiskFactor.bulkAdd(s),console.log("RiskFactor bulkAdd::BulkError",e,t),e}console.error("RiskFactor bulkAdd",e,t)}}async addRelationships(e){if(!e)return console.log("RiskFactor addRelationships",e),e;{let t=e.mitigationStrategy?.nodes?.map(e=>e.id),i=[];return t&&t.length>0&&(i=await this.mitigationStrategyModel.getByIds(t)),console.log("RiskFactor addRelationships",e),{...e,mitigationStrategy:{nodes:i}}}}async setProperty(e){try{if(e){let t=await r.Z.RiskFactor.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.RiskFactor.update(e,t),console.log("RiskFactor setProperty",e,t),t}}catch(t){console.error("RiskFactor setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.RiskFactor.update(e.id,e)})),console.log("RiskFactor multiUpdate",e)}catch(t){console.error("RiskFactor multiUpdate",e,t)}}constructor(){this.mitigationStrategyModel=new o.Z}}let l=n},18940:(e,t,i)=>{i.d(t,{Z:()=>c});var s=i(83179),a=i.n(s),r=i(86708),o=i(5417),n=i(92871);class l{async save(e){try{let t=Object.fromEntries(Object.entries(e).map(([e,t])=>[e,"number"==typeof t?t.toString():t])),i=t.id,s={...t,idbCRUD:"Update",idbCRUDDate:a()().format("YYYY-MM-DD HH:mm:ss")},o=await this.getById(i);return o?await r.Z.TowingChecklist.update(i,s):await r.Z.TowingChecklist.add(s),o=await this.getById(i),console.log("TowingChecklist save",e,o),o}catch(t){console.error("TowingChecklist save",e,t)}}async getAll(){try{let e=await r.Z.TowingChecklist.toArray();return console.log("TowingChecklist getAll",e),e}catch(e){console.error("TowingChecklist getAll",e)}}async getById(e){try{let t=await r.Z.TowingChecklist.get(`${e}`),i=await this.addRelationships(t);return console.log("TowingChecklist getById",e,i),i}catch(t){console.error("TowingChecklist getById",e,t)}}async getByIds(e){try{let t=await r.Z.TowingChecklist.where("id").anyOf(e).toArray(),i=Promise.all(t.map(async e=>await this.addRelationships(e)));return console.log("TowingChecklist getByIds",e,i),i}catch(t){console.error("TowingChecklist getByIds",e,t)}}async getByVesselID(e){try{let t=await r.Z.TowingChecklist.where("vesselID").equals(`${e}`).toArray();return console.log("TowingChecklist getByVesselID",e,t),t}catch(t){console.error("TowingChecklist getByVesselID",e,t)}}async bulkAdd(e){try{return await r.Z.TowingChecklist.bulkAdd(e),console.log("TowingChecklist bulkAdd",e),e}catch(t){if("BulkError"===t.name){let i=t.failuresByPos.map(e=>e.key),s=e.filter(e=>!i.includes(e.id));return await r.Z.TowingChecklist.bulkAdd(s),console.log("TowingChecklist bulkAdd::BulkError",e,t),e}console.error("TowingChecklist bulkAdd",e,t)}}async addRelationships(e){if(!e)return console.log("TowingChecklist addRelationships",e),e;{let t=+e.id>0?await this.riskFactorModel.getByTowingChecklistID(e.id):[];return t.length>0&&(t=await Promise.all(t.map(async e=>{let t=e.mitigationStrategy;if("string"==typeof t){let i=t.split(","),s=i.length>0?await this.mitigationStrategyModel.getByIds(i):[];e.mitigationStrategy={nodes:s}}return e}))),console.log("TowingChecklist addRelationships",e),{...e,riskFactors:{nodes:t}}}}async setProperty(e){try{if(e){let t=await r.Z.TowingChecklist.get(`${e}`);return t.idbCRUD="Download",t.idbCRUDDate=a()().format("YYYY-MM-DD HH:mm:ss"),await r.Z.TowingChecklist.update(e,t),console.log("TowingChecklist setProperty",e,t),t}}catch(t){console.error("TowingChecklist setProperty",e,t)}}async multiUpdate(e){try{Promise.all(e.map(async e=>{await r.Z.TowingChecklist.update(e.id,e)})),console.log("TowingChecklist multiUpdate",e)}catch(t){console.error("TowingChecklist multiUpdate",e,t)}}constructor(){this.riskFactorModel=new o.Z,this.mitigationStrategyModel=new n.Z}}let c=l},52994:(e,t,i)=>{i.d(t,{Z:()=>f});var s=i(98768),a=i(60343),r=i(76342),o=i(94060),n=i(79418),l=i(72548),c=i(34376),d=i(69424),g=i(13842);i(46776);var y=i(48755),u=i(10769),h=i(5417),p=i(98318),m=i(10090),v=i(7884),w=i(92871),k=i(35741),C=i.n(k),b=i(6925);function f({selectedEvent:e=!1,onSidebarClose:t,barCrossingChecklistID:i=0,setBarCrossingChecklistID:k,offline:f=!1,setAllChecked:B,crewMembers:T=!1,logBookConfig:D,currentTrip:E,open:I=!1,onOpenChange:M,noSheet:R=!1}){let x=(0,d.useSearchParams)(),S=x.get("vesselID")??0,j=parseInt(x.get("logentryID")??"0"),{toast:A}=(0,c.pm)(),[Z,P]=(0,a.useState)(!1),[_,F]=(0,a.useState)({}),[Y,L]=(0,a.useState)(!1),[U,N]=(0,a.useState)({}),[O,V]=(0,a.useState)(""),[$,H]=(0,a.useState)(!1),[G,q]=(0,a.useState)([]),[z,J]=(0,a.useState)(null),[W,X]=(0,a.useState)(!1),[Q,K]=(0,a.useState)(!1),[ee,et]=(0,a.useState)(!1),[ei,es]=(0,a.useState)([]),[ea,er]=(0,a.useState)(!1),[eo,en]=(0,a.useState)(!1),[el,ec]=(0,a.useState)(!1),[ed,eg]=(0,a.useState)(!1),[ey,eu]=(0,a.useState)(!1),[eh,ep]=(0,a.useState)(!1),[em,ev]=(0,a.useState)(!1),[ew,ek]=(0,a.useState)(!1),[eC,eb]=(0,a.useState)(!1),[ef,eB]=(0,a.useState)(!1);new y.Z;let eT=new u.Z,eD=new h.Z,eE=new p.Z;new v.Z;let eI=new w.Z,[eM,eR]=(0,a.useState)(null),[ex,{loading:eS}]=(0,n.t)(o.Y,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readCrewMembers_LogBookEntrySections.nodes.map(e=>({label:`${e.crewMember.firstName??""} ${e.crewMember.surname??""}`,value:e.crewMember.id})).filter(e=>e.value!=ed.master.id);eu(C()([...ey||[],...t],"value"))},onError:e=>{console.error("CrewMembers_LogBookEntrySection error",e)}}),ej=async e=>{eg(e);let t={label:`${e.master.firstName??""} ${e.master.surname??""}`,value:e.master.id};+t.value>0&&(Array.isArray(ey)?eu(C()([...ey||[],t],"value")):eu([t]));let i=e.logBookEntrySections.nodes.filter(e=>"SeaLogs\\CrewMembers_LogBookEntrySection"===e.className);if(i&&i.length>0){let e=i.map(e=>e.id);if(f){let t=(await eE.getByIds(e)).map(e=>({label:`${e.crewMember.firstName??""} ${e.crewMember.surname??""}`,value:e.crewMember.id}));eu(Array.isArray(ey)?[...ey||[],...t]:t)}else ex({variables:{filter:{id:{in:e}}}})}};j>0&&!f&&(0,g.oE)(+j,ej);let eA=e=>async t=>{if(!ef||!ew){A({variant:"destructive",title:"Error",description:"You do not have permission to edit this section"});return}F({..._,[e]:t?"on":"off"}),+Z?.id>0&&(f?await eT.save({id:Z.id,[e]:t}):eZ({variables:{input:{id:Z.id,[e]:t}}}))},[eZ]=(0,l.D)(r.i2f,{onCompleted:()=>{},onError:e=>{console.error("onError",e)}}),eP=[{name:"StopAssessPlan",label:"Stopped, Assessed, Planned",value:"stopAssessPlan",checked:_?.stopAssessPlan?"on"===_.stopAssessPlan:Z?.stopAssessPlan,handleChange:eA("stopAssessPlan"),description:s.jsx("small",{children:s.jsx("div",{children:"Pause before crossing to evaluate conditions and create a detailed crossing plan."})})},{name:"CrewBriefing",label:"Briefed crew on crossing",value:"crewBriefing",checked:_?.crewBriefing?"on"===_.crewBriefing:Z?.crewBriefing,handleChange:eA("crewBriefing"),description:s.jsx("small",{children:s.jsx("div",{children:"Inform the crew about the crossing plan and any potential hazards."})})},{name:"Weather",label:"Weather, tide, bar conditions checked as suitable for crossing",value:"weather",checked:_?.weather?"on"===_.weather:Z?.weather,handleChange:eA("weather"),description:s.jsx("small",{children:s.jsx("div",{children:"Verify that weather, tide, and bar conditions are favorable and safe for crossing."})})},{name:"Stability",label:"Adequate stability checked",value:"stability",checked:_?.stability?"on"===_.stability:Z?.stability,handleChange:eA("stability"),description:s.jsx("small",{children:s.jsx("div",{children:"Ensure the vessel is stable enough to handle the crossing without capsizing."})})},{name:"LifeJackets",label:"Lifejackets on",value:"lifeJackets",checked:_?.lifeJackets?"on"===_.lifeJackets:Z?.lifeJackets,handleChange:eA("lifeJackets"),description:s.jsx("small",{children:s.jsx("div",{children:"Instruct crew to wear lifejackets if conditions are rough or challenging."})})},{name:"WaterTightness",label:"Water tightness checked",value:"waterTightness",checked:_?.waterTightness?"on"===_.waterTightness:Z?.waterTightness,handleChange:eA("waterTightness"),description:s.jsx("small",{children:s.jsx("div",{children:"Confirm that all hatches and doors are secured to prevent water ingress."})})},{name:"LookoutPosted",label:"Lookout posted",value:"lookoutPosted",checked:_?.lookoutPosted?"on"===_.lookoutPosted:Z?.lookoutPosted,handleChange:eA("lookoutPosted"),description:s.jsx("small",{children:s.jsx("div",{children:"Assign a crew member to watch for hazards or changes in conditions during the crossing."})})}],[e_]=(0,n.t)(o.f7,{fetchPolicy:"cache-and-network",onCompleted:e=>{H(Array.from(new Set(e.readRiskFactors.nodes?.map(e=>e.title))).map(e=>({label:e,value:e}))),q(e.readRiskFactors.nodes)},onError:e=>{console.error("onError",e)}}),[eF]=(0,n.t)(o.uF,{fetchPolicy:"cache-and-network",onCompleted:e=>{P(e.readOneBarCrossingChecklist)},onError:e=>{console.error("onError",e)}}),[eY]=(0,l.D)(r.b1D,{onCompleted:t=>{k(+t.createBarCrossingChecklist.id),(i>0||e?.eventType_BarCrossing?.id)&&eL({variables:{input:{id:i>0?i:e?.eventType_BarCrossing?.id,barCrossingChecklistID:+t.createBarCrossingChecklist.id}}}),eF({variables:{id:t.createBarCrossingChecklist.id}})},onError:e=>{console.error("onError",e)}}),[eL]=(0,l.D)(r.Icx,{onCompleted:()=>{},onError:e=>{console.error("onError",e)}}),eU=async e=>{if(!ef||!ew){A({variant:"destructive",title:"Error",description:"You do not have permission to edit this section"});return}F({..._,memberID:e}),+Z?.id>0&&(f?await eT.save({id:Z.id,memberID:e}):eZ({variables:{input:{id:Z.id,memberID:e}}}))},eN=async()=>{if(U.id>0){if(f){await eD.save({id:U.id,type:"BarCrossingChecklist",title:U.title,impact:U?.impact||"Low",probability:U?.probability||5,mitigationStrategy:ei.length>0?ei.map(e=>e.id).join(","):"",barCrossingChecklistID:Z?.id}),L(!1);let t=await eD.getByFieldID("type","BarCrossingChecklist");H(Array.from(new Set(t.map(e=>e.title))).map(e=>({label:e,value:e}))),q(t),P(await eT.getById(i>0?i:e?.eventType_BarCrossing?.barCrossingChecklist?.id))}else eH({variables:{input:{id:U.id,type:"BarCrossingChecklist",title:U.title,impact:U?.impact||"Low",probability:U?.probability||5,mitigationStrategy:ei.length>0?ei.map(e=>e.id).join(","):"",barCrossingChecklistID:Z?.id}}})}else if(f){await eD.save({id:(0,m.lY)(),type:"BarCrossingChecklist",title:U.title,impact:U?.impact||"Low",probability:U?.probability||5,mitigationStrategy:ei.length>0?ei.map(e=>e.id).join(","):"",barCrossingChecklistID:Z?.id,vesselID:S}),L(!1);let e=await eD.getByFieldID("type","BarCrossingChecklist");H(Array.from(new Set(e.map(e=>e.title))).map(e=>({label:e,value:e}))),q(e),P(await eT.getById(i>0?i:Z?.id))}else e$({variables:{input:{type:"BarCrossingChecklist",title:U.title,impact:U?.impact||"Low",probability:U?.probability||5,mitigationStrategy:ei.length>0?ei.map(e=>e.id).join(","):"",barCrossingChecklistID:Z?.id,vesselID:S}}})},[eO]=(0,l.D)(r.YMp,{onCompleted:e=>{es([...ei,{id:e.createMitigationStrategy.id,strategy:O}]),V("")},onError:e=>{console.error("onError",e)}}),[eV]=(0,l.D)(r.dxh,{onCompleted:()=>{},onError:e=>{console.error("onError",e)}}),[e$]=(0,l.D)(r.H6U,{onCompleted:t=>{L(!1),e_({variables:{filter:{type:{eq:"BarCrossingChecklist"}}}}),eF({variables:{id:i>0?i:e?.eventType_BarCrossing?.barCrossingChecklist?.id}})},onError:e=>{console.error("onError",e)}}),[eH]=(0,l.D)(r.C$E,{onCompleted:()=>{L(!1),e_({variables:{filter:{type:{eq:"BarCrossingChecklist"}}}}),eF({variables:{id:i>0?i:e?.eventType_BarCrossing?.barCrossingChecklist?.id}})},onError:e=>{console.error("onError",e)}}),eG=async()=>{if(f){await eD.save({id:eo.id,barCrossingChecklistID:0,vesselID:0}),L(!1);let t=await eD.getByFieldID("type","BarCrossingChecklist");H(Array.from(new Set(t.map(e=>e.title))).map(e=>({label:e,value:e}))),q(t),P(await eT.getById(i>0?i:e?.eventType_BarCrossing?.barCrossingChecklist?.id))}else eH({variables:{input:{id:eo.id,barCrossingChecklistID:0,vesselID:0}}});ec(!1)},eq=e=>{ei.find(t=>t.id===e.id)?es(ei.filter(t=>t.id!==e.id)):es([...ei,e])},ez=async()=>{if(O){if(f){let e=[...ei,{id:(await eI.save({id:(0,m.lY)(),strategy:O})).id,strategy:O}];N({...U,mitigationStrategy:{nodes:e}}),es(e),V("")}else eO({variables:{input:{strategy:O}}})}K(!1)},eJ=e=>{J({value:e.title,label:e.title}),e.mitigationStrategy.nodes&&es(e.mitigationStrategy.nodes),G?.filter(t=>t.title===e.title&&t.mitigationStrategy.nodes?.length>0).length>0?et(Array.from(new Set(G?.filter(t=>t.title===e.title&&t.mitigationStrategy.nodes?.length>0).map(e=>e.mitigationStrategy.nodes)[0].map(e=>({id:e.id,strategy:e.strategy}))))):et(!1)},eW={checkFields:eP,riskFactors:Z?.riskFactors?.nodes||[],crewMembers:ey?ey.map(e=>({...e,value:String(e.value)})):[],selectedAuthor:eM,onAuthorChange:e=>{eR(e),e&&eU(e.value)},canEdit:ef&&ew,canDeleteRisks:ef&&eC,onRiskClick:e=>{if(!ef||!ew){A({variant:"destructive",title:"Error",description:"You do not have permission to edit this section"});return}eJ(e),N(e),L(!0)},onAddRiskClick:()=>{if(!ef||!ew){A({variant:"destructive",title:"Error",description:"You do not have permission to edit this section"});return}N({}),V(""),J(null),L(!0)},onRiskDelete:e=>{if(!ef||!eC){A({variant:"destructive",title:"Error",description:"You do not have permission to delete risks"});return}en(e),eG()},setAllChecked:B,selectedEvent:e,currentTrip:E};return(0,s.jsxs)("div",{children:[R?s.jsx(b.YY,{...eW}):s.jsx(b.Fs,{open:I,onOpenChange:e=>{M&&M(e)},onSidebarClose:()=>{t(),M&&M(!1)},title:"Risk Analysis",subtitle:"Bar Crossing",...eW}),s.jsx(b.Tg,{open:Y,onOpenChange:L,currentRisk:U,onSave:eN,riskOptions:$,riskValue:z,onRiskValueChange:e=>{N({...U,title:e?.value}),J({value:e.value,label:e.value}),G?.filter(t=>t.title===e.value&&t.mitigationStrategy.nodes?.length>0).length>0?et(Array.from(new Set(G?.filter(t=>t.title===e.value&&t.mitigationStrategy.nodes?.length>0).map(e=>e.mitigationStrategy.nodes)[0].map(e=>({id:e.id,strategy:e.strategy}))))):et(!1)},riskImpacts:[{value:"Low",label:"Low impact"},{value:"Medium",label:"Medium impact"},{value:"High",label:"High impact"},{value:"Severe",label:"Severe impact"}],onRiskImpactChange:e=>N({...U,impact:e?.value}),onRiskProbabilityChange:e=>N({...U,probability:e}),currentStrategies:ei,content:O,onAddStrategyClick:()=>K(!0)}),s.jsx(b.Um,{open:Q,onOpenChange:K,onSave:ez,currentRisk:U,recommendedStrategies:ee,currentStrategies:ei,onStrategySelect:e=>{er(e),eq(e),N({...U,mitigationStrategy:e}),X(!1)},content:O,onEditorChange:e=>{V(e)}})]})}},40672:(e,t,i)=>{i.d(t,{Z:()=>f});var s=i(98768),a=i(60343),r=i(76342),o=i(94060),n=i(79418),l=i(72548),c=i(34376),d=i(69424),g=i(13842);i(46776);var y=i(48755),u=i(18940),h=i(5417),p=i(98318),m=i(30084),v=i(92871),w=i(10090),k=i(35741),C=i.n(k),b=i(6925);function f({selectedEvent:e=!1,onSidebarClose:t,logBookConfig:i,currentTrip:k,crewMembers:f=!1,towingChecklistID:B=0,setTowingChecklistID:T,offline:D=!1,setAllChecked:E,open:I=!1,onOpenChange:M,noSheet:R=!1}){let x=(0,d.useSearchParams)(),S=parseInt(x.get("logentryID")??"0"),j=x.get("vesselID")??0,[A,Z]=(0,a.useState)(!1),[P,_]=(0,a.useState)(!1),[F,Y]=(0,a.useState)(!1),[L,U]=(0,a.useState)(!1),[N,O]=(0,a.useState)(""),[V,$]=(0,a.useState)(!1),[H,G]=(0,a.useState)([]),[q,z]=(0,a.useState)(null),[,J]=(0,a.useState)(!1),[W,X]=(0,a.useState)(!1),[Q,K]=(0,a.useState)(!1),[ee,et]=(0,a.useState)([]),[,ei]=(0,a.useState)(!1),[es,ea]=(0,a.useState)(!1),[er,eo]=(0,a.useState)(!1),[en,el]=(0,a.useState)(!1),[ec,ed]=(0,a.useState)(!1),[eg,ey]=(0,a.useState)(!1),[eu,eh]=(0,a.useState)(!1),{toast:ep}=(0,c.pm)();new y.Z;let em=new u.Z,ev=new h.Z,ew=new p.Z;new m.Z;let ek=new v.Z,[eC,eb]=(0,a.useState)(null),[ef]=(0,n.t)(o.Y,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readCrewMembers_LogBookEntrySections.nodes.map(e=>({label:`${e.crewMember.firstName??""} ${e.crewMember.surname??""}`,value:e.crewMember.id})).filter(e=>e.value!=es.master.id);eo(C()([...er,...t],"value"))},onError:e=>{console.error("CrewMembers_LogBookEntrySection error",e)}}),eB=async e=>{ea(e);let t={label:`${e.master.firstName??""} ${e.master.surname??""}`,value:e.master.id};+t.value>0&&(Array.isArray(er)?eo(C()([...er,t],"value")):eo([t]));let i=e.logBookEntrySections.nodes.filter(e=>"SeaLogs\\CrewMembers_LogBookEntrySection"===e.className);if(i){let e=i.map(e=>e.id);if(e?.length>0){if(D){let t=(await ew.getByIds(e)).map(e=>({label:`${e.crewMember.firstName??""} ${e.crewMember.surname??""}`,value:e.crewMember.id}));Array.isArray(er)?eo(C()([...er,...t],"value")):eo(t)}else ef({variables:{filter:{id:{in:e}}}})}}};S>0&&!D&&(0,g.oE)(+S,eB);let eT=e=>async t=>{if(!eu||!ec){ep({title:"Permission Error",description:"You do not have permission to edit this section",variant:"destructive"});return}if(_({...P,[e]:t?"on":"off"}),+A?.id>0){if(D){let i=await em.save({id:A.id,[e]:!!t});Z(await em.getById(i.id))}else eD({variables:{input:{id:A.id,[e]:!!t}}})}},[eD]=(0,l.D)(r.nJw,{onCompleted:e=>{eM({variables:{id:e.updateTowingChecklist.id}})},onError:e=>{console.error("onError",e)}}),eE=[{name:"ConductSAP",label:"Conduct SAP",value:"conductSAP",checked:P?.conductSAP?"on"===P.conductSAP:A?.conductSAP,handleChange:eT("conductSAP"),description:(0,s.jsxs)("small",{children:[s.jsx("div",{children:"Conduct SAP prior to approaching the vessel."}),s.jsx("div",{children:"Check for fittings on the vessel that could damage the CRV when coming alongside."})]})},{name:"InvestigateNatureOfIssue",label:"Investigate nature of the issue",value:"investigateNatureOfIssue",checked:P?.investigateNatureOfIssue?"on"===P.investigateNatureOfIssue:A?.investigateNatureOfIssue,handleChange:eT("investigateNatureOfIssue"),description:(0,s.jsxs)("small",{children:[s.jsx("div",{children:"Ascertain the nature of the problem, any damage, or taking on water."}),s.jsx("div",{children:"Does a crew member need to go on board the other vessel to assist?"})]})},{name:"EveryoneOnBoardOk",label:"Everyone on board ok?",value:"everyoneOnBoardOk",checked:P?.everyoneOnBoardOk?"on"===P.everyoneOnBoardOk:A?.everyoneOnBoardOk,handleChange:eT("everyoneOnBoardOk"),description:(0,s.jsxs)("small",{children:[s.jsx("div",{children:"Check how many people are aboard, ensure everyone is accounted for."}),s.jsx("div",{children:"Check for injuries or medical assistance required."})]})},{name:"RudderToMidshipsAndTrimmed",label:"Rudder to midships and trimmed appropriately",value:"rudderToMidshipsAndTrimmed",checked:P?.rudderToMidshipsAndTrimmed?"on"===P.rudderToMidshipsAndTrimmed:A?.rudderToMidshipsAndTrimmed,handleChange:eT("rudderToMidshipsAndTrimmed"),description:(0,s.jsxs)("small",{children:[s.jsx("div",{children:"Check steering isn’t impaired in any way and have the rudder secured amidships or have the vessel steer for the stern of CRV."}),s.jsx("div",{children:"Check the vessel is optimally trimmed for towing."})]})},{name:"LifejacketsOn",label:"Lifejackets on",value:"lifejacketsOn",checked:P?.lifejacketsOn?"on"===P.lifejacketsOn:A?.lifejacketsOn,handleChange:eT("lifejacketsOn"),description:s.jsx("small",{children:s.jsx("div",{children:"Request that everyone wears a lifejacket."})})},{name:"CommunicationsEstablished",label:"Communications Established",value:"communicationsEstablished",checked:P?.communicationsEstablished?"on"===P.communicationsEstablished:A?.communicationsEstablished,handleChange:eT("communicationsEstablished"),description:(0,s.jsxs)("small",{children:[s.jsx("div",{children:"Ensure that communications have been established and checked prior to beginning the tow, i.e., VHF, hand signals, and/or light signals if the tow is to be conducted at night."}),s.jsx("div",{children:"Ensure there is agreement on where to tow the vessel to."})]})},{name:"SecureAndSafeTowing",label:"Secure and safe towing",value:"secureAndSafeTowing",checked:P?.secureAndSafeTowing?"on"===P.secureAndSafeTowing:A?.secureAndSafeTowing,handleChange:eT("secureAndSafeTowing"),description:(0,s.jsxs)("small",{children:[s.jsx("div",{children:"Towline securely attached"}),s.jsx("div",{children:"Ensure everything on board is stowed and secure."}),s.jsx("div",{children:"Confirm waterline length/cruising speed of the vessel (safe tow speed)."}),s.jsx("div",{children:"Confirm attachment points for the towline."}),s.jsx("div",{children:"Confirm that the towline is securely attached."}),s.jsx("div",{children:"Ensure that no one on the other vessel is in close proximity to the towline before commencing the tow."}),s.jsx("div",{children:"Turn on CRV towing lights and other vessel’s navigation lights."}),s.jsx("div",{children:"Post towline lookout with responsibility for quick release of the tow / must carry or have a knife handy."})]})}],[eI]=(0,n.t)(o.f7,{fetchPolicy:"cache-and-network",onCompleted:e=>{$(Array.from(new Set(e.readRiskFactors.nodes?.map(e=>e.title)))?.map(e=>({label:e,value:e}))),G(e.readRiskFactors.nodes)},onError:e=>{console.error("onError",e)}}),[eM]=(0,n.t)(o.XV,{fetchPolicy:"cache-and-network",onCompleted:e=>{Z(e.readOneTowingChecklist)},onError:e=>{console.error("onError",e)}}),[eR]=(0,l.D)(r.Q2n,{onCompleted:t=>{let i=+t.createTowingChecklist.id;T(i),ex({variables:{input:{id:e?.eventType_TaskingID,towingChecklistID:i}}}),eM({variables:{id:t.createTowingChecklist.id}})},onError:e=>{console.error("onError",e)}}),[ex]=(0,l.D)(r.czP,{onCompleted:()=>{},onError:e=>{console.error("onError",e)}}),eS=async e=>{if(!eu||!ec){ep({title:"Permission Error",description:"You do not have permission to edit this section",variant:"destructive"});return}if(D){let t=await em.save({id:A.id,memberID:e});Z(await em.getById(t.id))}else eD({variables:{input:{id:A.id,memberID:e}}})},ej=async()=>{if(L.id>0){if(D){await ev.save({id:L.id,type:"TowingChecklist",title:L.title,impact:L?.impact?L?.impact:"Low",probability:L?.probability?L?.probability:5,mitigationStrategy:ee.length>0?ee.map(e=>e.id).join(","):"",towingChecklistID:A?.id}),Y(!1);let t=await ev.getByFieldID("type","TowingChecklist");$(Array.from(new Set(t.map(e=>e.title)))?.map(e=>({label:e,value:e}))),G(t),Z(await em.getById(B>0?B:e?.eventType_Tasking?.towingChecklist?.id))}else eP({variables:{input:{id:L.id,type:"TowingChecklist",title:L.title,impact:L?.impact?L?.impact:"Low",probability:L?.probability?L?.probability:5,mitigationStrategy:ee.length>0?ee.map(e=>e.id).join(","):"",towingChecklistID:A?.id}}})}else if(D){await ev.save({id:(0,w.lY)(),type:"TowingChecklist",title:L.title,impact:L?.impact?L?.impact:"Low",probability:L?.probability?L?.probability:5,mitigationStrategy:ee.length>0?ee.map(e=>e.id).join(","):"",towingChecklistID:A?.id,vesselID:j}),Y(!1);let t=await ev.getByFieldID("type","TowingChecklist");$(Array.from(new Set(t.map(e=>e.title)))?.map(e=>({label:e,value:e}))),G(t),Z(await em.getById(B>0?B:e?.eventType_Tasking?.towingChecklist?.id))}else eZ({variables:{input:{type:"TowingChecklist",title:L.title,impact:L?.impact?L?.impact:"Low",probability:L?.probability?L?.probability:5,mitigationStrategy:ee.length>0?ee.map(e=>e.id).join(","):"",towingChecklistID:A?.id,vesselID:j}}})},[eA]=(0,l.D)(r.YMp,{onCompleted:e=>{et([...ee,{id:e.createMitigationStrategy.id,strategy:N}]),O("")},onError:e=>{console.error("onError",e)}}),[eZ]=(0,l.D)(r.H6U,{onCompleted:()=>{Y(!1),eI({variables:{filter:{type:{eq:"TowingChecklist"}}}}),eM({variables:{id:B>0?B:e?.eventType_Tasking?.towingChecklist?.id}})},onError:e=>{console.error("onError",e)}}),[eP]=(0,l.D)(r.C$E,{onCompleted:()=>{Y(!1),eI({variables:{filter:{type:{eq:"TowingChecklist"}}}}),eM({variables:{id:B>0?B:e?.eventType_Tasking?.towingChecklist?.id}})},onError:e=>{console.error("onError",e)}}),e_=async t=>{if(D){await ev.save({id:t.id,towingChecklistID:0,vesselID:0}),Y(!1);let i=await ev.getByFieldID("type","TowingChecklist");$(Array.from(new Set(i.map(e=>e.title)))?.map(e=>({label:e,value:e}))),G(i),Z(await em.getById(B>0?B:e?.eventType_Tasking?.towingChecklist?.id))}else eP({variables:{input:{id:t.id,towingChecklistID:0,vesselID:0}}})},eF=e=>{ee.length>0?ee.find(t=>t.id===e.id)?et(ee.filter(t=>t.id!==e.id)):et([...ee,e]):et([e])},eY=async()=>{if(N){if(D){let e=[...ee,{id:(await ek.save({id:(0,w.lY)(),strategy:N})).id,strategy:N}];U({...L,mitigationStrategy:{nodes:e}}),et(e),O("")}else eA({variables:{input:{strategy:N}}})}X(!1)},eL=e=>{z({value:e.title,label:e.title}),e.mitigationStrategy.nodes&&et(e.mitigationStrategy.nodes),H?.filter(t=>t.title===e.title&&t.mitigationStrategy.nodes?.length>0).length>0?K(Array.from(new Set(H?.filter(t=>t.title===e.title&&t.mitigationStrategy.nodes?.length>0).map(e=>e.mitigationStrategy.nodes)[0].map(e=>({id:e.id,strategy:e.strategy}))))):K(!1)},eU={checkFields:eE,riskFactors:A?.riskFactors?.nodes||[],crewMembers:er?er.map(e=>({...e,value:String(e.value)})):[],selectedAuthor:eC,onAuthorChange:e=>{eb(e),e&&eS(e.value)},canEdit:eu&&ec,canDeleteRisks:eu&&eg,onRiskClick:e=>{if(!eu||!ec){ep({title:"Permission Error",description:"You do not have permission to edit this section",variant:"destructive"});return}eL(e),U(e),Y(!0)},onAddRiskClick:()=>{if(!eu||!ec){ep({title:"Permission Error",description:"You do not have permission to edit this section",variant:"destructive"});return}U({}),O(""),z(null),Y(!0)},onRiskDelete:e=>{e_(e)},setAllChecked:E,selectedEvent:e,currentTrip:k};return(0,s.jsxs)("div",{children:[R?s.jsx(b.YY,{...eU}):s.jsx(b.Fs,{open:I,onOpenChange:e=>{M&&M(e)},onSidebarClose:()=>{t(),M&&M(!1)},title:"Risk Analysis",subtitle:"Towing",...eU}),s.jsx(b.Tg,{open:F,onOpenChange:Y,onSave:ej,currentRisk:L,riskOptions:V||[],riskValue:q,onRiskValueChange:e=>{U({...L,title:e?.value}),z({value:e.value,label:e.value}),H?.filter(t=>t.title===e.value&&t.mitigationStrategy.nodes?.length>0).length>0?K(Array.from(new Set(H?.filter(t=>t.title===e.value&&t.mitigationStrategy.nodes?.length>0).map(e=>e.mitigationStrategy.nodes)[0].map(e=>({id:e.id,strategy:e.strategy}))))):K(!1)},riskImpacts:[{value:"Low",label:"Low impact"},{value:"Medium",label:"Medium impact"},{value:"High",label:"High impact"},{value:"Severe",label:"Severe impact"}],onRiskImpactChange:e=>U({...L,impact:e?.value}),onRiskProbabilityChange:e=>U({...L,probability:e}),currentStrategies:ee,content:N,onAddStrategyClick:()=>X(!0)}),s.jsx(b.Um,{open:W,onOpenChange:X,onSave:eY,currentRisk:L,recommendedStrategies:Q,currentStrategies:ee,onStrategySelect:e=>{ei(e),eF(e),U({...L,mitigationStrategy:e}),J(!1)},content:N,onEditorChange:e=>{O(e)}})]})}},10901:(e,t,i)=>{i.d(t,{k:()=>y});var s=i(98768),a=i(11652),r=i(41641),o=i(39303),n=i(40712),l=i(39544),c=i(24224),d=i(25394),g=i(50058);function y({table:e,pageSizeOptions:t=[10,20,30,40,50],showPageSizeSelector:i=!0}){let y=(0,g.k)();return s.jsx("div",{className:"flex items-center justify-center px-2",children:(0,s.jsxs)("div",{className:"flex gap-2 items-end phablet:items-center phablet:space-x-6 lg:space-x-8",children:[i&&s.jsx("div",{className:"flex items-center space-x-2",children:s.jsx(d.__,{label:"Rows per page",position:y.phablet?"left":"top",htmlFor:"rows-per-page",className:"text-sm",children:(0,s.jsxs)(c.Select,{value:`${e.getState().pagination.pageSize}`,onValueChange:t=>{e.setPageSize(Number(t))},children:[s.jsx(c.SelectTrigger,{className:"h-8 w-[70px]",children:s.jsx(c.SelectValue,{placeholder:e.getState().pagination.pageSize})}),s.jsx(c.SelectContent,{side:"top",children:t.map(e=>s.jsx(c.SelectItem,{value:`${e}`,children:e},e))})]})})}),(0,s.jsxs)("div",{className:"flex w-fit items-center justify-center text-sm",children:["Page ",e.getState().pagination.pageIndex+1," of"," ",e.getPageCount()]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(l.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>e.setPageIndex(0),disabled:!e.getCanPreviousPage(),children:[s.jsx("span",{className:"sr-only",children:"Go to first page"}),s.jsx(a.Z,{})]}),(0,s.jsxs)(l.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>e.previousPage(),disabled:!e.getCanPreviousPage(),children:[s.jsx("span",{className:"sr-only",children:"Go to previous page"}),s.jsx(r.Z,{})]}),(0,s.jsxs)(l.Button,{variant:"outline",className:"h-8 w-8 p-0",onClick:()=>e.nextPage(),disabled:!e.getCanNextPage(),children:[s.jsx("span",{className:"sr-only",children:"Go to next page"}),s.jsx(o.Z,{})]}),(0,s.jsxs)(l.Button,{variant:"outline",className:"hidden h-8 w-8 p-0 lg:flex",onClick:()=>e.setPageIndex(e.getPageCount()-1),disabled:!e.getCanNextPage(),children:[s.jsx("span",{className:"sr-only",children:"Go to last page"}),s.jsx(n.Z,{})]})]})]})})}},6958:(e,t,i)=>{i.d(t,{n:()=>r});var s=i(98768),a=i(37042);function r({table:e,onChange:t}){return s.jsx(a.Z,{onChange:t,table:e})}},6925:(e,t,i)=>{i.d(t,{YY:()=>C,Fs:()=>b,Tg:()=>D,Um:()=>I});var s=i(98768),a=i(60343),r=i(15580),o=i(34376),n=i(81515),l=i(52016),c=i(17203),d=i(81311),g=i(39544),y=i(8416),u=i(60797),h=i(81524),p=i(74602),m=i(29052),v=i(69852),w=i(93778),k=i(25394);function C({checkFields:e,riskFactors:t=[],crewMembers:i=[],selectedAuthor:c,onAuthorChange:d,canEdit:v=!0,canDeleteRisks:C=!0,onRiskClick:b,onAddRiskClick:f,onRiskDelete:B,setAllChecked:T,selectedEvent:D,currentTrip:E}){let{toast:I}=(0,o.pm)(),[M,R]=(0,a.useState)(!1),[x,S]=(0,a.useState)(null),j=e=>{if(!v){I({variant:"destructive",title:"Error",description:"You do not have permission to edit this section"});return}b&&b(e)},A=e=>{if(!v||!C){I({variant:"destructive",title:"Error",description:"You do not have permission to delete risks"});return}S(e),R(!0)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"grid space-y-0 md:grid-cols-2 gap-2.5",children:[(0,s.jsxs)("div",{className:"h-full flex flex-col min-h-[400px] overflow-auto",children:[s.jsx(m.x,{className:"h-full mb-5 border border-dashed border-border rounded-lg",children:s.jsx("div",{className:"h-full p-2 sm:p-5 space-y-2",children:e.map((e,t)=>s.jsx("div",{className:"space-y-1",children:s.jsx(y.tz,{id:`${e.value}-onChangeComplete-${t}`,type:"checkbox",checked:e.checked,onCheckedChange:t=>{e.handleChange(!0===t)},variant:"warning",label:e.label,rightContent:e.description&&(0,s.jsxs)(k.u,{children:[s.jsx(k.aJ,{asChild:!0,children:s.jsx(g.Button,{variant:"ghost",onClick:e=>e.stopPropagation(),size:"icon",iconOnly:!0,iconLeft:s.jsx(n.Z,{className:"text-curious-blue-900 fill-curious-blue-50",size:24}),children:s.jsx("span",{className:"sr-only",children:"View description"})})}),s.jsx(k._v,{className:"w-72 p-3",children:s.jsx("ul",{children:s.jsx("li",{children:e.description})})})]})})},`${t}-${e.name}`))})}),s.jsx("div",{className:"w-full flex flex-col space-y-2",children:s.jsx(w.Z,{inputId:D?.id,sectionId:E?.id,buttonType:"button",sectionName:"tripEventID"})}),i.length>0&&s.jsx(u.Label,{label:"Who completed risk",htmlFor:"author",children:s.jsx(h.Combobox,{id:"author",options:i.map(e=>({...e,profile:e.profile||{firstName:e.label?.split(" ")[0],surname:e.label?.split(" ").slice(1).join(" "),avatar:e.avatar||e.profileImage}})),value:c,placeholder:"Select crew",onChange:e=>{d&&d(e)}})})]}),(0,s.jsxs)("div",{className:"h-full col-span-1 min-h-[400px] flex flex-col overflow-auto",children:[s.jsx(m.x,{className:"h-full border border-dashed border-border mb-4 rounded-lg",children:s.jsx("div",{className:"h-full p-2 sm:p-5",children:t.length>0&&t.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4 p-3 rounded-md border border-dashed border-border",children:[s.jsx("div",{className:"cursor-pointer",onClick:()=>j(e),children:s.jsx(u.Label,{label:e.title,className:"font-medium",children:e?.impact&&(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)(p.P,{className:"w-full leading-tight text-sm font-normal rounded-lg",children:["Impact:"," ",e.impact]}),(0,s.jsxs)(p.P,{className:"w-full leading-tight text-sm font-normal rounded-lg",children:["Probability:"," ",e.probability,"/10"]})]})})}),(0,s.jsxs)("div",{className:"flex items-center gap-2.5",children:[e?.mitigationStrategy?.nodes?.length>0&&(0,s.jsxs)(r.Popover,{children:[s.jsx(r.PopoverTrigger,{asChild:!0,children:s.jsx(g.Button,{variant:"ghost",size:"icon",iconOnly:!0,iconLeft:s.jsx(n.Z,{className:"text-light-blue-vivid-900 fill-light-blue-vivid-50",size:24}),children:s.jsx("span",{className:"sr-only",children:"View strategies"})})}),s.jsx(r.PopoverContent,{className:"w-72 p-3",children:e?.mitigationStrategy?.nodes.map(e=>s.jsx("div",{dangerouslySetInnerHTML:{__html:e.strategy}},e.id))})]}),s.jsx(g.Button,{variant:"ghost",size:"icon",onClick:()=>A(e),children:s.jsx("span",{className:"sr-only",children:"Delete risk"})})]})]},`${e.id}-risk-analysis`))})}),s.jsx(g.Button,{variant:"outline",iconLeft:l.Z,onClick:()=>{if(!v){I({variant:"destructive",title:"Error",description:"You do not have permission to edit this section"});return}f&&f()},children:"Add Risk"})]})]}),s.jsx(k.h9,{openDialog:M,setOpenDialog:R,handleCreate:()=>{B&&x&&B(x),R(!1)},actionText:"Delete",title:"Delete Risk",variant:"warning",size:"lg",position:"center",showIcon:!0,children:s.jsx(p.P,{children:"Are you sure you want to delete this risk? This action cannot be undone."})})]})}function b({open:e,onOpenChange:t,onSidebarClose:i,title:a,subtitle:r,checkFields:o,riskFactors:n=[],crewMembers:l=[],selectedAuthor:y,onAuthorChange:u,canEdit:h=!0,canDeleteRisks:p=!0,onRiskClick:m,onAddRiskClick:w,onRiskDelete:k,setAllChecked:b,selectedEvent:f,currentTrip:B}){return s.jsx(s.Fragment,{children:s.jsx(v.Sheet,{open:e,onOpenChange:t,children:(0,s.jsxs)(v.SheetContent,{side:"right",className:"w-[90vw] sm:w-[60vw]",children:[s.jsx(v.SheetHeader,{children:(0,s.jsxs)(v.SheetTitle,{children:[a," ",r&&s.jsx("span",{className:"font-thin",children:r})]})}),s.jsx(v.SheetBody,{children:s.jsx(C,{checkFields:o,riskFactors:n,crewMembers:l,selectedAuthor:y,onAuthorChange:u,canEdit:h,canDeleteRisks:p,onRiskClick:m,onAddRiskClick:w,onRiskDelete:k,setAllChecked:b,selectedEvent:f,currentTrip:B})}),s.jsx(v.SheetFooter,{children:(0,s.jsxs)("div",{className:"flex gap-2 justify-end",children:[s.jsx(g.Button,{variant:"back",iconLeft:s.jsx(c.Z,{}),onClick:()=>t(!1),children:"Cancel"}),s.jsx(g.Button,{variant:"primary",iconLeft:d.Z,onClick:i,children:"Save"})]})})]})})})}var f=i(79181),B=i(8943),T=i(53294);function D({open:e,onOpenChange:t,currentRisk:i,onSave:a,riskOptions:r,riskValue:o,onRiskValueChange:n,riskImpacts:l,onRiskImpactChange:c,onRiskProbabilityChange:d,currentStrategies:y,content:v,onAddStrategyClick:w}){return s.jsx(k.h9,{openDialog:e,setOpenDialog:t,handleCreate:a,actionText:i?.id>0?"Update":"Create Risk",title:i?.id>0?"Update Risk":"Create New Risk",variant:"default",size:"md",position:"center",cancelText:"Cancel",children:(0,s.jsxs)(k.iP,{className:"space-y-5",children:[r&&s.jsx(u.Label,{label:"Risk",htmlFor:"risk",children:s.jsx(h.Combobox,{id:"risk",options:r.filter(e=>null!==e.value&&null!==e.label),defaultValues:o,placeholder:"Select a risk",onChange:e=>{n(e)}})}),s.jsx(u.Label,{label:"Risk impact",htmlFor:"impact",children:s.jsx(h.Combobox,{id:"impact",options:l.filter(e=>null!==e.value&&null!==e.label),placeholder:"Select risk impact",defaultValues:i?.impact?l.find(e=>e.value==i?.impact):null,required:!0,onChange:e=>{c(e)}})}),s.jsx(u.Label,{label:"Risk probability",children:s.jsx(f.i,{defaultValue:[i?.probability??5],max:10,min:0,step:1,onValueChange:e=>d(e[0]),className:"w-full"})}),s.jsx("div",{className:"space-y-4",children:(0,s.jsxs)("div",{children:[s.jsx(p.H4,{children:"Mitigation strategy"}),y.length>0?s.jsx(m.x,{className:"max-h-[200px] mt-2 border border-border rounded-md p-3",children:s.jsx("div",{className:"space-y-3",children:y.map(e=>s.jsx("div",{className:"p-2 bg-muted/50 rounded-md",children:s.jsx("div",{dangerouslySetInnerHTML:{__html:e.strategy}})},e.id))})}):(0,s.jsxs)(B.bZ,{className:"mt-2 border-amber-100 bg-amber-50",children:[s.jsx(T.Z,{className:"h-4 w-4 text-amber-600"}),s.jsx(B.Cd,{children:"No strategies added"}),s.jsx(B.X,{children:"Click the button below to add mitigation strategies."})]}),v&&s.jsx("div",{className:"mt-3 p-2 bg-muted/50 rounded-md",children:s.jsx("div",{dangerouslySetInnerHTML:{__html:v}})}),s.jsx(g.Button,{variant:"primary",className:"mt-4 w-full sm:w-auto",onClick:w,children:"Add strategy"})]})})]})})}var E=i(33849);function I({open:e,onOpenChange:t,onSave:i,currentRisk:r,recommendedStrategies:o,currentStrategies:n,onStrategySelect:l,content:c,onEditorChange:d}){let g=(0,a.useCallback)(e=>{let t=n.some(t=>t.id==e.id);return console.info("statusChecked",e.id,n,t),t},[n]);return(0,s.jsxs)(k.h9,{openDialog:e,setOpenDialog:t,handleCreate:i,title:"Recommended strategy",actionText:"Save",variant:"default",size:"xl",position:"center",cancelText:"Cancel",description:"Select an existing strategy or create a new one",children:[(0,s.jsxs)("div",{className:"my-2 flex items-center gap-4 flex-wrap mb-6",children:[r?.mitigationStrategy?.nodes?.length>0&&s.jsx(s.Fragment,{children:r.mitigationStrategy.nodes.filter(e=>null!=e.strategy).map(e=>s.jsx(y.tz,{id:`strategy-${e.id}`,type:"checkbox",checked:g(e),onCheckedChange:()=>l(e),value:e.id,variant:"warning",label:s.jsx("div",{dangerouslySetInnerHTML:{__html:e.strategy}})},e.id))}),o?s.jsx(s.Fragment,{children:o?.filter(e=>e.strategy&&!r?.mitigationStrategy?.nodes?.some(t=>t.id===e.id))?.map(e=>s.jsx(y.tz,{id:`strategy-${e.id}`,type:"checkbox",value:e.id,checked:g(e),variant:"warning",onCheckedChange:()=>l(e),label:s.jsx("div",{dangerouslySetInnerHTML:{__html:e?.strategy}})},e.id))}):(0,s.jsxs)(B.bZ,{variant:"default",className:"border-blue-100 bg-blue-50",children:[s.jsx(T.Z,{className:"h-4 w-4 text-blue-600"}),s.jsx(B.Cd,{children:"No recommendations available"}),s.jsx(B.X,{children:"You can create a new mitigation strategy below."})]})]}),s.jsx(u.Label,{label:"Create a new strategy",htmlFor:"strategy",children:s.jsx(E.Z,{id:"strategy",placeholder:"Mitigation strategy",className:"w-full",content:c,handleEditorChange:d})})]})}}};