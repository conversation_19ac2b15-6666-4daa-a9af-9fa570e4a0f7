"use strict";exports.id=4234,exports.ids=[4234],exports.modules={94060:(e,i,t)=>{t.d(i,{r3:()=>ec,uF:()=>eb,qJ:()=>J,aU:()=>d,HW:()=>o,Xp:()=>s,C8:()=>a,$0:()=>l,Y:()=>em,JS:()=>eu,UU:()=>eC,BH:()=>er,Ye:()=>eI,tg:()=>ep,t1:()=>eg,SB:()=>ef,Rc:()=>m,rd:()=>c,qn:()=>r,l6:()=>H,IO:()=>eR,X4:()=>eo,ZX:()=>Z,_f:()=>y,sG:()=>h,u2:()=>U,IR:()=>et,LW:()=>eO,HV:()=>$,bK:()=>eH,gV:()=>q,en:()=>E,I:()=>B,zb:()=>N,$e:()=>P,id:()=>e6,Lw:()=>e7,cS:()=>u,Yo:()=>ee,MI:()=>g,Eo:()=>eM,au:()=>eW,DB:()=>M,Gz:()=>W,t:()=>_,gQ:()=>Q,AJ:()=>x,Se:()=>V,vf:()=>j,zF:()=>K,pf:()=>eZ,mO:()=>k,ec:()=>ei,HC:()=>en,rO:()=>X,l1:()=>R,gW:()=>w,Ah:()=>eJ,QK:()=>Y,dh:()=>eF,ld:()=>eE,To:()=>eK,_x:()=>eN,gh:()=>ew,YX:()=>eB,f7:()=>e$,nH:()=>eq,y_:()=>ea,iZ:()=>el,PI:()=>ed,Js:()=>es,$d:()=>eP,RS:()=>eA,K7:()=>eG,w$:()=>ek,sI:()=>eU,gx:()=>ey,SL:()=>f,Wu:()=>D,jl:()=>I,KX:()=>v,F5:()=>T,qX:()=>S,d5:()=>e_,tp:()=>ex,Z0:()=>ez,Nh:()=>ej,mR:()=>e4,BD:()=>e8,dH:()=>eQ,uZ:()=>e2,m5:()=>e3,Iq:()=>e1,rZ:()=>e0,Ss:()=>e5,oR:()=>eY,dr:()=>eX,bA:()=>C,Zy:()=>eh,ly:()=>A,tZ:()=>F,pm:()=>O,e3:()=>G,XV:()=>eL,$8:()=>eD,Zl:()=>ev,NS:()=>z,rk:()=>b,N5:()=>L,dY:()=>eV,Hb:()=>p,Sk:()=>eT,ui:()=>eS});var n=t(45519);let o=(0,n.ZP)`
    query {
        readCrewDuties {
            nodes {
                id
                title
                abbreviation
                archived
                clientID
                className
                lastEdited
            }
        }
    }
`,r=(0,n.ZP)`
    query GetCrewDuty($id: ID!) {
        readOneCrewDuty(filter: { id: { eq: $id } }) {
            id
            title
            abbreviation
            archived
            clientID
            className
            lastEdited
        }
    }
`,s=(0,n.ZP)`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                isArchived
                firstName
                isCrew
                departments {
                    nodes {
                        id
                    }
                }
                primaryDutyID
                primaryDuty {
                    id
                    title
                }
                surname
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
                trainingSessionsDue {
                    nodes {
                        id
                        dueDate
                        memberID
                        member {
                            id
                            firstName
                            surname
                        }
                        vesselID
                        vessel {
                            id
                            title
                        }
                        trainingTypeID
                        trainingType {
                            id
                            title
                        }
                    }
                }
                trainingSessions {
                    nodes {
                        id
                        members {
                            nodes {
                                id
                                firstName
                                surname
                            }
                        }
                    }
                }
                groups {
                    nodes {
                        id
                        title
                        code
                        permissions(limit: 1000) {
                            nodes {
                                id
                                type
                                code
                            }
                        }
                    }
                }
            }
        }
    }
`,a=(0,n.ZP)`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                isArchived
                firstName
                isCrew
                departments {
                    nodes {
                        id
                    }
                }
                primaryDutyID
                primaryDuty {
                    id
                    title
                }
                surname
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
            }
        }
    }
`,d=(0,n.ZP)`
    query GetCrewDetailWithTrainingStatus($crewMemberID: ID!) {
        readOneSeaLogsMember(filter: { id: { eq: $crewMemberID } }) {
            id
            archived
            isArchived
            firstName
            isCrew
            departments {
                nodes {
                    id
                }
            }
            primaryDutyID
            primaryDuty {
                id
                title
            }
            surname
            vehicles {
                nodes {
                    id
                    title
                }
            }
            trainingSessionsDue {
                nodes {
                    id
                    dueDate
                    memberID
                    member {
                        id
                        firstName
                        surname
                    }
                    vesselID
                    vessel {
                        id
                        title
                    }
                    trainingTypeID
                    trainingType {
                        id
                        title
                    }
                }
            }
            trainingSessions {
                nodes {
                    id
                    members {
                        nodes {
                            id
                            firstName
                            surname
                        }
                    }
                }
            }
        }
    }
`;(0,n.ZP)`
    query {
        readTrainingLocations {
            nodes {
                id
                title
            }
        }
    }
`;let l=(0,n.ZP)`
    query ReadTrainingTypes(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingTypeFilterFields = {}
    ) {
        readTrainingTypes(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                title
                occursEvery
                highWarnWithin
                procedure
                mediumWarnWithin
                vessels {
                    nodes {
                        id
                        title
                    }
                }
                customisedComponentField {
                    nodes {
                        id
                        status
                        sortOrder
                        fieldName
                        description
                    }
                }
            }
        }
    }
`;(0,n.ZP)`
    query GetClientById($clientIDs: [ID]) {
        readClients(filter: { id: { in: $clientIDs } }) {
            nodes {
                id
                title
            }
        }
    }
`;let c=(0,n.ZP)`
    query GetSeaLogsMembers($crewMemberIDs: [ID]) {
        readSeaLogsMembers(
            filter: { id: { in: $crewMemberIDs }, isArchived: { eq: false } }
        ) {
            nodes {
                id
                archived
                isArchived
                phoneNumber
                alternatePhoneNumber
                isMaster
                isCrew
                isPilot
                isTransferee
                username
                dashboardVessels
                viewArchivedMode
                requireMFA
                firstName
                surname
                email
                seaLogsTheme
                defaultRegisteredMethodID
                hasSkippedMFARegistration
                accountResetHash
                accountResetExpired
                clientID
                trainingSessionsDue {
                    nodes {
                        id
                        dueDate
                        memberID
                        member {
                            id
                            firstName
                            surname
                        }
                        vesselID
                        vessel {
                            id
                            title
                        }
                        trainingTypeID
                        trainingType {
                            id
                            title
                        }
                    }
                }
                primaryDuty {
                    id
                    archived
                    title
                    abbreviation
                }
                currentVehicleID
                className
                lastEdited
                crewTraining_LogBookEntrySections {
                    nodes {
                        id
                    }
                }
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
                logBookEntries {
                    nodes {
                        id
                    }
                }
                groups {
                    nodes {
                        id
                    }
                }
                departments {
                    nodes {
                        id
                        title
                    }
                }
            }
        }
    }
`,m=(0,n.ZP)`
    query GetSeaLogsMember($crewMemberID: [ID]!) {
        readOneSeaLogsMember(filter: { id: { in: $crewMemberID } }) {
            id
            archived
            isArchived
            phoneNumber
            alternatePhoneNumber
            isMaster
            isCrew
            isPilot
            isTransferee
            username
            dashboardVessels
            viewArchivedMode
            requireMFA
            firstName
            surname
            email
            seaLogsTheme
            defaultRegisteredMethodID
            hasSkippedMFARegistration
            accountResetHash
            accountResetExpired
            clientID
            trainingSessionsDue {
                nodes {
                    id
                    dueDate
                    memberID
                    member {
                        id
                        firstName
                        surname
                    }
                    vesselID
                    vessel {
                        id
                        title
                    }
                    trainingTypeID
                    trainingType {
                        id
                        title
                    }
                }
            }
            primaryDuty {
                id
                archived
                title
                abbreviation
            }
            currentVehicleID
            className
            lastEdited
            crewTraining_LogBookEntrySections {
                nodes {
                    id
                }
            }
            vehicles {
                nodes {
                    id
                    title
                }
            }
            logBookEntries {
                nodes {
                    id
                }
            }
            groups {
                nodes {
                    id
                }
            }
            departments {
                nodes {
                    id
                    title
                }
            }
        }
    }
`;(0,n.ZP)`
    query GetCrewMembersLogEntrySectionByLogBookEntryID($logBookEntryID: ID!) {
        readOneCrewMembers_LogBookEntrySection(
            filter: { logBookEntryID: { eq: $logBookEntryID } }
        ) {
            id
            crewMemberID
            dutyPerformedID
            logBookEntryID
            punchIn
            punchOut
        }
    }
`,(0,n.ZP)`
    query GetCrewTrainingConfig {
        getCrewTraining {
            isSuccess
            data {
                trainers {
                    id
                    name
                }
                trainingLocation
                trainingTypes {
                    id
                    type
                }
            }
        }
        getCrewMember {
            isSuccess
            data {
                ID
                FirstName
                Surname
            }
        }
    }
`,(0,n.ZP)`
    query GetCrewTrainingLists {
        getCrewTrainingLists {
            isSuccess
            data {
                id
                natureOfTraining
                trainer
                trainingLocation
                trainingSummary
            }
        }
    }
`;let u=(0,n.ZP)`
  query GetLogBookEntries($vesselId: ID!) {
    GetLogBookEntries: readLogBookEntries(filter: {vehicleID: {eq: $vesselId}}) {
      nodes {
        id
        archived
        masterID
        startDate
        endDate
        logBookID
        fuelLevel
        createdByID
        state
        vehicle {
          id
          title
        }
        logBookEntrySections {
          nodes {
            id
            className
          }
        }
      }
    }
  }
`,g=(0,n.ZP)`
    query GetLogBookEntries($logbookEntryId: ID!) {
        readOneLogBookEntry(filter: { id: { eq: $logbookEntryId } }) {
            id
            masterID
            state
            className
            startDate
            endDate
            fuelLevel
            logBookID
            createdByID
            signOffCommentID
            signOffSignatureID
            clientID
            lockedDate
            lastConfig
            fuelLog {
                nodes {
                    id
                    fuelAdded
                    fuelBefore
                    fuelAfter
                    date
                    costPerLitre
                    totalCost
                    fuelTank {
                        id
                        capacity
                        safeFuelCapacity
                        currentLevel
                        title
                    }
                }
            }
            engineStartStop {
                nodes {
                    id
                    hoursStart
                    engineID
                    engine {
                        id
                        title
                        currentHours
                    }
                }
            }
            logBook {
                id
                title
                componentConfig
            }
            master {
                id
                firstName
                surname
            }
            logBookEntrySections {
                nodes {
                    id
                    className
                }
            }
            vehicle {
                id
                seaLogsMembers {
                    nodes {
                        id
                        firstName
                        surname
                        archived
                        primaryDutyID
                    }
                }
            }
        }
    }
`,p=(0,n.ZP)`
    query GetVesselStatus($id: ID!) {
        readVesselStatuss(
            filter: { vesselID: { eq: $id } }
            sort: { created: DESC }
        ) {
            nodes {
                id
                date
                status
                comment
                created
                reason
                otherReason
                expectedReturn
                vesselID
            }
        }
    }
`;(0,n.ZP)`
    query GetLogBookConfiguration($vesselID: Int!) {
        getLogBookConfiguration(vesselID: $vesselID) {
            isSuccess
            data {
                ID
                Title
                ComponentConfig
                ClassName
                LastEdited
                LogBookConfig
            }
        }
    }
`;let f=(0,n.ZP)`
    query ReadOneClient($filter: ClientFilterFields) {
        readOneClient(filter: $filter) {
            id
            title
            phone
            adminEmail
            accountsEmail
            useDepartment
            usePilotTransfer
            useTripSchedule
            masterTerm
            maritimeTrafficFleetEmail
            logoID
            iconLogoID
            hqAddress {
                id
                streetNumber
                street
                locality
                administrative1
                postalCode
                country
                timeZone
            }
            documents {
                nodes {
                    id
                    fileFilename
                    name
                    title
                    created
                }
            }
        }
    }
`,y=(0,n.ZP)`
    query GetFuelLogs($id: [ID]!) {
        readFuelLogs(filter: { id: { in: $id } }) {
            nodes {
                id
                fuelAdded
                fuelBefore
                fuelAfter
                date
                type
                typeID
                fuelTank {
                    id
                    capacity
                    safeFuelCapacity
                    currentLevel
                    title
                }
            }
        }
    }
`,h=(0,n.ZP)`
    query GetFuelLogs($filter: FuelLogFilterFields = {}) {
        readFuelLogs(filter: $filter) {
            nodes {
                id
                fuelAdded
                fuelBefore
                fuelAfter
                date
                type
                typeID
                fuelTank {
                    id
                    capacity
                    safeFuelCapacity
                    currentLevel
                    title
                }
            }
        }
    }
`,D=(0,n.ZP)`
    query ReadOneSeaLogsGroup($id: ID!) {
        readOneSeaLogsGroup(filter: { id: { eq: $id } }) {
            id
            code
            description
            title
            permissionCodes
            permissions(limit: 1000) {
                nodes {
                    id
                    groupID
                    code
                }
            }
            members {
                nodes {
                    id
                    firstName
                    surname
                }
            }
        }
    }
`,I=(0,n.ZP)`
    query ReadOneSeaLogsMember($filter: SeaLogsMemberFilterFields) {
        readOneSeaLogsMember(filter: $filter) {
            id
            firstName
            surname
            superAdmin
            clientID
            client {
                title
                useDepartment
                useTripSchedule
                usePilotTransfer
                hqAddress {
                    timeZone
                }
            }
            currentDepartmentID
            currentDepartment {
                id
                title
            }
            groups {
                nodes {
                    id
                    title
                    code
                    permissions(limit: 1000) {
                        nodes {
                            id
                            code
                        }
                    }
                }
            }
            departments {
                nodes {
                    id
                    title
                    basicComponents {
                        nodes {
                            id
                            title
                            identifier
                        }
                    }
                }
            }
        }
    }
`,v=(0,n.ZP)`
    query ReadOneTrainingSessionDue($filter: TrainingSessionDueFilterFields) {
        readOneTrainingSessionDue(filter: $filter) {
            id
            dueDate
            lastTrainingDate
            memberID
            member {
                id
                firstName
                surname
            }
            trainingTypeID
            trainingType {
                id
                title
            }
            vesselID
            vessel {
                id
                title
            }
        }
    }
`,T=(0,n.ZP)`
    query {
        readPermissionTypes {
            code
            name
            category
            help
            sort
            default
        }
    }
`,S=(0,n.ZP)`
    query ReadTrainingSessionDues(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingSessionDueFilterFields = {}
    ) {
        readTrainingSessionDues(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: {
                dueDate: ASC
                trainingTypeID: ASC
                vesselID: ASC
                memberID: ASC
            }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                dueDate
                lastTrainingDate
                memberID
                member {
                    id
                    firstName
                    surname
                }
                vesselID
                vessel {
                    id
                    title
                    seaLogsMembers {
                        nodes {
                            id
                            firstName
                            surname
                        }
                    }
                }
                trainingTypeID
                trainingType {
                    id
                    title
                }
                trainingSession {
                    trainingLocationType
                }
            }
        }
    }
`,C=(0,n.ZP)`
    query {
        readSeaLogsGroups {
            nodes {
                id
                code
                description
                title
            }
        }
    }
`,k=(0,n.ZP)`
    query {
        readSectionMemberComments(sort: { id: DESC }) {
            nodes {
                id
                className
                lastEdited
                created
                uniqueID
                commentType
                fieldName
                workOrderNumber
                comment
                detail
                clientID
                seaLogsMemberID
                logBookEntryID
                logBookEntrySectionID
                maintenanceCheckID
                seaLogsMember {
                    firstName
                    surname
                }
                logBookEntry {
                    id
                    vehicleID
                    vehicle {
                        title
                    }
                }
                logBookEntrySection {
                    id
                    logBookEntryID
                }
                maintenanceCheck {
                    id
                    name
                }
            }
        }
    }
`,L=(0,n.ZP)`
    query ReadVessels(
        $limit: Int
        $offset: Int
        $filter: VesselFilterFields = {}
        $entryFilter: LogBookEntryFilterFields = {
            state: { in: [Editing, Reopened] }
        }
    ) {
        readVessels(filter: $filter, limit: $limit, offset: $offset) {
            nodes {
                id
                archived
                title
                registration
                callSign
                icon
                iconMode
                photoID
                minCrew
                showOnDashboard
                vesselType
                logBookID
                vehiclePositions(sort: { created: DESC }, limit: 1) {
                    nodes {
                        id
                        lat
                        long
                        geoLocation {
                            id
                            title
                            lat
                            long
                        }
                    }
                }
                parentComponent_Components {
                    nodes {
                        basicComponent {
                            id
                            title
                            componentCategory
                        }
                        parentComponent {
                            id
                            title
                        }
                    }
                }
                logBookEntries(filter: $entryFilter, limit: 1) {
                    nodes {
                        id
                    }
                }
                componentMaintenanceChecks {
                    nodes {
                        archived
                        name
                        expires
                        status
                        completed
                        maintenanceSchedule {
                            __typename
                        }
                    }
                }
            }
        }
    }
`,b=(0,n.ZP)`
    query GetVessel($id: ID!) {
        readOneVessel(filter: { id: { eq: $id } }) {
            id
            title
            registration
            logBookID
            seaLogsMembers {
                nodes {
                    id
                    archived
                    firstName
                    surname
                }
            }
            statusHistory(sort: { created: DESC }, limit: 1) {
                nodes {
                    id
                    date
                    status
                    comment
                    reason
                    otherReason
                    expectedReturn
                }
            }
            minCrew
            maxPax
            maxPOB
            activities
            numberOfEngines
            numberOfShafts
            sharedFuelTank
            documents {
                nodes {
                    id
                    fileFilename
                    name
                    title
                    created
                }
            }
            vehiclePositions(sort: { created: DESC }, limit: 1) {
                nodes {
                    id
                    lat
                    long
                    geoLocation {
                        id
                        title
                        lat
                        long
                    }
                }
            }
            icon
            metServiceObsLocation
            metServiceForecastLocation
            enableTransitMessaging
            copyCrewToOtherActivites
            templateVisible
            mmsi
            iconMode
            callSign
            countryOfOperation
            showOnDashboard
            displayLogbookComments
            transitID
            vesselSpecifics {
                id
                primaryHarbour
                overallLength
                beam
                draft
                dateOfBuild
                hullColor
                hullConstruction
                maxCargoLoad
                operatingAreaLimits
                specialLimitations
                portOfRegistry
                fishingNumber
                loadLineLength
                registeredLength
                tonnageLength
                grossTonnage
                netTonnage
                capacityOfLifting
                carriesDangerousGoods
                carriesVehicles
                designApprovalNumber
            }
            identifier
            archived
            vesselType
            vesselTypeDescription
            vesselSpecificsID
            photoID
            photo {
                id
                fileFilename
                title
                created
            }
            currentTripServiceID
            clientID
            bannerImageID
            componentMaintenanceSchedules {
                nodes {
                    id
                }
            }
            parentComponent_Components {
                nodes {
                    basicComponent {
                        id
                        title
                        componentCategory
                    }
                    parentComponent {
                        id
                        title
                    }
                }
            }
            logBookEntries(sort: { id: DESC }) {
                nodes {
                    id
                    state
                }
            }
            departments {
                nodes {
                    id
                    title
                }
            }
            defaultRadioLogs {
                nodes {
                    id
                    title
                    status
                    comment
                    time
                    vesselID
                    logBookEntryID
                }
            }
        }
    }
`,$=(0,n.ZP)`
    query ReadInventories(
        $limit: Int = 100
        $offset: Int = 0
        $filter: InventoryFilterFields = {}
    ) {
        readInventories(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                item
                location
                attachments {
                    nodes {
                        id
                    }
                }
                description
                content
                quantity
                productCode
                costingDetails
                comments
                formID
                hasMaintenanceTasksDue
                formType
                formLabel
                showForm
                title
                identifier
                componentCategory
                archived
                uniqueID
                componentMaintenanceSchedules {
                    nodes {
                        id
                    }
                }
                componentMaintenanceChecks {
                    nodes {
                        id
                        workOrderNumber
                        groupItemTo
                        projected
                        actual
                        difference
                        name
                        startDate
                        documents {
                            nodes {
                                id
                            }
                        }
                        maintenanceCategory {
                            id
                            name
                            abbreviation
                        }
                        completed
                        expires
                        dutyHoursAtCheck
                        equipmentUsagesAtCheck
                        comments
                        severity
                        status
                        archived
                        assignees {
                            nodes {
                                id
                            }
                        }
                        maintenanceSchedule {
                            id
                            title
                            description
                            type
                            occursEveryType
                            occursEvery
                            warnWithinType
                            highWarnWithin
                            mediumWarnWithin
                            lowWarnWithin
                            groupTo
                            maintenanceChecks {
                                nodes {
                                    id
                                }
                            }
                            engineUsage {
                                nodes {
                                    id
                                    lastScheduleHours
                                    isScheduled
                                    engine {
                                        id
                                        title
                                        currentHours
                                    }
                                }
                            }
                            inventoryID
                            clientID
                        }
                        basicComponentID
                        basicComponent {
                            id
                            title
                        }
                        assignedToID
                        assignedByID
                        inventoryID
                        maintenanceScheduleID
                        maintenanceCheck_Signature {
                            id
                        }
                        clientID
                        recurringID
                    }
                }
                suppliers {
                    nodes {
                        id
                        name
                    }
                }
                categories {
                    nodes {
                        id
                        name
                    }
                }
                vesselID
                vessel {
                    id
                    title
                }
                inventoryImportID
                clientID
            }
        }
    }
`,P=(0,n.ZP)`
    query ReadInventories($vesselID: Int!) {
        readInventoryList(vesselID: $vesselID) {
            list
        }
    }
`,E=(0,n.ZP)`
    query GetInventories($vesselId: ID!) {
        readInventories(filter: { vesselID: { eq: $vesselId } }) {
            nodes {
                id
                item
                location
                description
                content
                attachments {
                    nodes {
                        id
                    }
                }
                quantity
                productCode
                costingDetails
                comments
                formID
                formType
                formLabel
                showForm
                title
                identifier
                componentCategory
                archived
                uniqueID
                componentMaintenanceSchedules {
                    nodes {
                        id
                    }
                }
                componentMaintenanceChecks {
                    nodes {
                        id
                        workOrderNumber
                        groupItemTo
                        projected
                        actual
                        difference
                        name
                        startDate
                        documents {
                            nodes {
                                id
                            }
                        }
                        maintenanceCategory {
                            id
                            name
                            abbreviation
                        }
                        completed
                        expires
                        dutyHoursAtCheck
                        equipmentUsagesAtCheck
                        comments
                        severity
                        status
                        archived
                        assignees {
                            nodes {
                                id
                            }
                        }
                        maintenanceSchedule {
                            id
                            title
                            description
                            type
                            occursEveryType
                            occursEvery
                            warnWithinType
                            highWarnWithin
                            mediumWarnWithin
                            lowWarnWithin
                            groupTo
                            maintenanceChecks {
                                nodes {
                                    id
                                }
                            }
                            engineUsage {
                                nodes {
                                    id
                                    lastScheduleHours
                                    isScheduled
                                    engine {
                                        id
                                        title
                                        currentHours
                                    }
                                }
                            }
                            inventoryID
                            clientID
                        }
                        basicComponentID
                        basicComponent {
                            id
                            title
                        }
                        assignedToID
                        assignedByID
                        inventoryID
                        maintenanceScheduleID
                        maintenanceCheck_Signature {
                            id
                        }
                        clientID
                        recurringID
                    }
                }
                suppliers {
                    nodes {
                        id
                        name
                        email
                        phone
                        address
                    }
                }
                categories {
                    nodes {
                        id
                        name
                    }
                }
                vesselID
                vessel {
                    id
                    title
                }
                inventoryImportID
                clientID
                className
                lastEdited
            }
        }
    }
`,B=(0,n.ZP)`
    query {
        readInventoryCategories {
            nodes {
                id
                name
                abbreviation
                archived
                clientID
                className
                lastEdited
                lastEdited
                client {
                    id
                    title
                    phone
                }
                inventories {
                    nodes {
                        id
                        title
                    }
                }
            }
        }
    }
`,q=(0,n.ZP)`
    query GetOneInventory($id: ID!) {
        readOneInventory(filter: { id: { eq: $id } }) {
            id
            item
            location
            attachments {
                nodes {
                    id
                }
            }
            documents {
                nodes {
                    id
                    fileFilename
                    name
                    title
                    created
                }
            }
            description
            content
            quantity
            productCode
            costingDetails
            comments
            formID
            formType
            formLabel
            showForm
            title
            identifier
            componentCategory
            archived
            uniqueID
            componentMaintenanceSchedules {
                nodes {
                    id
                }
            }
            componentMaintenanceChecks {
                nodes {
                    id
                    workOrderNumber
                    groupItemTo
                    projected
                    actual
                    difference
                    name
                    startDate
                    completed
                    documents {
                        nodes {
                            id
                        }
                    }
                    assignees {
                        nodes {
                            id
                        }
                    }
                    maintenanceCategory {
                        id
                        name
                        abbreviation
                    }
                    expires
                    dutyHoursAtCheck
                    equipmentUsagesAtCheck
                    comments
                    severity
                    status
                    archived
                    basicComponentID
                    assignedToID
                    assignedByID
                    inventoryID
                    maintenanceScheduleID
                    maintenanceCheck_SignatureID
                    clientID
                }
            }
            suppliers {
                nodes {
                    id
                    name
                }
            }
            categories {
                nodes {
                    id
                    name
                }
            }
            vesselID
            vessel {
                id
                title
            }
            inventoryImportID
            clientID
            attachmentLinks {
                nodes {
                    id
                    link
                }
            }
        }
    }
`,R=(0,n.ZP)`
    query ReadSuppliers(
        $limit: Int = 500
        $offset: Int = 0
        $filter: SupplierFilterFields = {}
    ) {
        readSuppliers(limit: $limit, offset: $offset, filter: $filter) {
            nodes {
                id
                name
                address
                website
                email
                phone
                contactPerson
                archived
                inventories {
                    nodes {
                        id
                    }
                }
                clientID
                className
                lastEdited
            }
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
        }
    }
`,w=(0,n.ZP)`
    query GetSupplier($id: ID!) {
        readOneSupplier(filter: { id: { eq: $id } }) {
            id
            name
            address
            website
            email
            phone
            contactPerson
            archived
            clientID
            notes
            inventories {
                nodes {
                    id
                    title
                }
            }
            supplierContacts{
                nodes{
                    id
                    name
                    phone
                    email
                    supplierID
                }
            }
        }
    }
`,N=(0,n.ZP)`
    query GetInventoryCategoryByID($id: ID!) {
        readOneInventoryCategory(filter: { id: { eq: $id } }) {
            id
            name
            abbreviation
            archived
            clientID
            className
            lastEdited
            lastEdited
            client {
                id
                title
                phone
            }
            inventories {
                nodes {
                    id
                    title
                }
            }
        }
    }
`;(0,n.ZP)`
    query {
        readTrainingLocations {
            nodes {
                id
                title
            }
        }
    }
`;let F=(0,n.ZP)`
    query GetTrainingSession($id: ID!) {
        readOneTrainingSession(filter: { id: { eq: $id } }) {
            id
            date
            logBookEntryID
            members {
                nodes {
                    id
                    firstName
                    surname
                }
            }
            trainer {
                id
                firstName
                surname
            }
            signatures {
                nodes {
                    id
                    signatureData
                    member {
                        id
                        firstName
                        surname
                    }
                }
            }
            trainingLocation {
                id
                title
            }
            trainingLocationType
            trainingSummary
            trainingTypes {
                nodes {
                    id
                    title
                    procedure
                    occursEvery
                    highWarnWithin
                    mediumWarnWithin
                    customisedComponentField {
                        nodes {
                            id
                            status
                            sortOrder
                            fieldName
                            description
                        }
                    }
                }
            }
            vesselID
            vessel {
                id
                title
                seaLogsMembers {
                    nodes {
                        id
                    }
                }
            }
            fuelLevel
            geoLocationID
            geoLocation {
                id
                title
                lat
                long
            }
            procedureFields {
                nodes {
                    id
                    status
                    comment
                    customisedComponentFieldID
                }
            }
            startTime
            finishTime
            lat
            long
        }
    }
`,O=(0,n.ZP)`
    query GetTrainingSessions($vesselID: ID!) {
        readTrainingSessions(filter: { vesselID: { eq: $vesselID } }) {
            nodes {
                id
                date
                logBookEntrySectionID
                logBookEntryID
                members {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                signatures {
                    nodes {
                        memberID
                        signatureData
                    }
                }
                trainingLocationType
                trainer {
                    id
                    firstName
                    surname
                }
                trainingLocation {
                    id
                    title
                }
                trainingSummary
                trainingTypes {
                    nodes {
                        id
                        title
                        occursEvery
                        highWarnWithin
                        mediumWarnWithin
                    }
                }
                vessel {
                    id
                    title
                }
            }
        }
    }
`,A=(0,n.ZP)`
    query ReadTrainingSessions(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingSessionFilterFields = {}
    ) {
        readTrainingSessions(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                date
                logBookEntryID
                members {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                trainerID
                trainer {
                    id
                    firstName
                    surname
                }
                trainingLocationType
                signatures {
                    nodes {
                        id
                        signatureData
                        member {
                            id
                            firstName
                            surname
                        }
                    }
                }
                trainingLocation {
                    id
                    title
                }
                trainingSummary
                trainingTypes {
                    nodes {
                        id
                        title
                        procedure
                        occursEvery
                        highWarnWithin
                        mediumWarnWithin
                    }
                }
                vessel {
                    id
                    title
                }
            }
        }
    }
`,G=(0,n.ZP)`
    query GetOneTrainingType($id: ID!) {
        readOneTrainingType(filter: { id: { eq: $id } }) {
            id
            archived
            highWarnWithin
            mediumWarnWithin
            occursEvery
            procedure
            title
            trainingSessions {
                nodes {
                    id
                    date
                    vessel {
                        id
                        title
                    }
                }
            }
            vessels {
                nodes {
                    id
                    title
                }
            }
            customisedComponentField {
                nodes {
                    id
                    status
                    sortOrder
                    fieldName
                    description
                }
            }
        }
    }
`,Z=(0,n.ZP)`
    query GetFiles($id: [ID]!) {
        readFiles(filter: { id: { in: $id } }) {
            nodes {
                id
                title
                name
                fileFilename
                created
            }
        }
    }
`,M=(0,n.ZP)`
    query ReadComponentMaintenanceChecks(
        $limit: Int = 500
        $offset: Int = 0
        $filter: ComponentMaintenanceCheckFilterFields = {}
    ) {
        readComponentMaintenanceChecks(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            nodes {
                id
                created
                workOrderNumber
                groupItemTo
                projected
                actual
                taskType
                startHours
                hoursCompleted
                difference
                name
                startDate
                documents {
                    nodes {
                        id
                    }
                }
                maintenanceCategory {
                    id
                    name
                    abbreviation
                }
                completedBy {
                    id
                    firstName
                    surname
                }
                dateCompleted
                completed
                expires
                dutyHoursAtCheck
                equipmentUsagesAtCheck
                comments
                severity
                status
                archived
                assignees {
                    nodes {
                        id
                    }
                }
                R2File {
                    nodes {
                        id
                        title
                    }
                }
                maintenanceSchedule {
                    id
                    title
                    description
                    type
                    occursEveryType
                    occursEvery
                    warnWithinType
                    highWarnWithin
                    mediumWarnWithin
                    lowWarnWithin
                    groupTo
                    maintenanceChecks {
                        nodes {
                            id
                        }
                    }
                    engineUsage {
                        nodes {
                            id
                            lastScheduleHours
                            isScheduled
                            engine {
                                id
                                title
                                currentHours
                            }
                        }
                    }
                    inventoryID
                    clientID
                }
                basicComponentID
                basicComponent {
                    id
                    title
                }
                assignedToID
                assignedByID
                inventoryID
                maintenanceScheduleID
                maintenanceCheck_Signature {
                    id
                }
                clientID
                recurringID
            }
        }
    }
`;(0,n.ZP)`
    query GetVesselLastFuel($id: ID!) {
        readOneVessel(filter: { id: { eq: $id } }) {
            id
            title
            logBookEntries(sort: { id: DESC }) {
                nodes {
                    id
                    state
                    tripReportSections: logBookEntrySections(
                        filter: {
                            className: {
                                endswith: "TripReport_LogBookEntrySection"
                            }
                        }
                    ) {
                        nodes {
                            id
                            className
                            tripEvents {
                                nodes {
                                    id
                                    eventType_Tasking {
                                        fuelLevel
                                        fuelLog {
                                            nodes {
                                                id
                                                fuelAdded
                                                fuelBefore
                                                fuelAfter
                                                date
                                                costPerLitre
                                                totalCost
                                                fuelTank {
                                                    id
                                                    capacity
                                                    safeFuelCapacity
                                                    currentLevel
                                                    title
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
`,(0,n.ZP)`
    query GetOpenLogEntries {
        readLogBookEntries(filter: { state: { ne: Locked } }) {
            nodes {
                id
                archived
                vehicle {
                    id
                    title
                    archived
                }
            }
        }
    }
`;let W=(0,n.ZP)`
    query GetOneComponentMaintenanceCheck($id: ID!) {
        readOneComponentMaintenanceCheck(filter: { id: { eq: $id } }) {
            id
            workOrderNumber
            groupItemTo
            projected
            taskType
            startHours
            hoursCompleted
            actual
            difference
            name
            startDate
            documents {
                nodes {
                    id
                    fileFilename
                    name
                    title
                    created
                }
            }
            maintenanceCategory {
                id
                name
                abbreviation
            }
            completedBy {
                id
                firstName
                surname
            }
            R2File {
                nodes {
                    id
                    title
                }
            }
            dateCompleted
            attachmentLinks {
                nodes {
                    id
                    link
                }
            }
            records {
                nodes {
                    id
                    time
                    description
                    author {
                        id
                        firstName
                        surname
                    }
                }
            }
            completed
            expires
            dutyHoursAtCheck
            equipmentUsagesAtCheck
            comments
            severity
            status
            archived
            assignees {
                nodes {
                    id
                }
            }
            basicComponentID
            basicComponent {
                id
                title
            }
            assignedToID
            assignedTo {
                id
                firstName
                surname
            }
            assignedByID
            inventoryID
            inventory {
                id
                item
                title
                vessel {
                    id
                    title
                }
            }
            maintenanceScheduleID
            maintenanceSchedule {
                id
                title
                description
                type
                occursEveryType
                occursEvery
                warnWithinType
                highWarnWithin
                mediumWarnWithin
                lowWarnWithin
                groupTo
                maintenanceChecks {
                    nodes {
                        id
                    }
                }
                engineUsage(sort: { created: DESC }) {
                    nodes {
                        id
                        lastScheduleHours
                        isScheduled
                        engine {
                            id
                            title
                            currentHours
                        }
                    }
                }
                inventoryID
                clientID
            }
            maintenanceCheck_Signature {
                id
                signatureData
            }
            clientID
            recurringID
        }
    }
`,_=(0,n.ZP)`
    query GetComponentMaintenanceChecks($limit: Int = 500, $memberId: ID!) {
        readComponentMaintenanceChecks(
            limit: $limit
            filter: { assignedToID: { eq: $memberId } }
        ) {
            nodes {
                id
                workOrderNumber
                groupItemTo
                projected
                actual
                difference
                name
                startDate
                documents {
                    nodes {
                        id
                    }
                }
                maintenanceCategory {
                    id
                    name
                    abbreviation
                }
                completedBy {
                    id
                    firstName
                    surname
                }
                dateCompleted
                completed
                expires
                dutyHoursAtCheck
                equipmentUsagesAtCheck
                comments
                severity
                status
                archived
                assignees {
                    nodes {
                        id
                    }
                }
                basicComponentID
                basicComponent {
                    id
                    title
                }
                assignedToID
                assignedByID
                inventoryID
                maintenanceScheduleID
                maintenanceSchedule {
                    id
                    title
                    description
                    type
                    occursEveryType
                    occursEvery
                    warnWithinType
                    highWarnWithin
                    mediumWarnWithin
                    lowWarnWithin
                    groupTo
                    maintenanceChecks {
                        nodes {
                            id
                        }
                    }
                    engineUsage {
                        nodes {
                            id
                            lastScheduleHours
                            isScheduled
                            engine {
                                id
                                title
                                currentHours
                            }
                        }
                    }
                    inventoryID
                    clientID
                }
                maintenanceCheck_Signature {
                    id
                    signatureData
                }
                clientID
            }
        }
    }
`;(0,n.ZP)`
    query GetComponentMaintenanceChecks($limit: Int = 500, $vesselId: ID!) {
        readComponentMaintenanceChecks(
            limit: $limit
            filter: { basicComponentID: { eq: $vesselId } }
        ) {
            nodes {
                id
                workOrderNumber
                projected
                actual
                difference
                name
                startDate
                documents {
                    nodes {
                        id
                    }
                }
                maintenanceCategory {
                    id
                    name
                    abbreviation
                }
                completedBy {
                    id
                    firstName
                    surname
                }
                dateCompleted
                maintenanceSchedule {
                    id
                    title
                    description
                    type
                    occursEveryType
                    occursEvery
                    warnWithinType
                    highWarnWithin
                    mediumWarnWithin
                    lowWarnWithin
                    groupTo
                    maintenanceChecks {
                        nodes {
                            id
                        }
                    }
                    engineUsage {
                        nodes {
                            id
                            lastScheduleHours
                            isScheduled
                            engine {
                                id
                                title
                                currentHours
                            }
                        }
                    }
                    inventoryID
                    clientID
                }
                completed
                expires
                dutyHoursAtCheck
                equipmentUsagesAtCheck
                comments
                severity
                status
                archived
                assignees {
                    nodes {
                        id
                    }
                }
                basicComponentID
                basicComponent {
                    id
                    title
                }
                assignedToID
                assignedByID
                inventoryID
                maintenanceScheduleID
                maintenanceCheck_Signature {
                    id
                }
                clientID
            }
        }
    }
`;let x=(0,n.ZP)`
    query GetMaintenanceCheckSubTask($id: ID!) {
        readMaintenanceCheckSubTasks(
            filter: { componentMaintenanceCheckID: { eq: $id } }
        ) {
            nodes {
                id
                status
                findings
                dateCompleted
                maintenanceScheduleSubTask {
                    id
                    task
                    description
                    inventoryID
                }
                completedBy {
                    id
                    firstName
                    surname
                }
                R2File {
                    nodes {
                        id
                        title
                    }
                }
            }
        }
    }
`;(0,n.ZP)`
    query GetTrainingTypes {
        readTrainingTypes {
            nodes {
                id
                archived
                highWarnWithin
                mediumWarnWithin
                occursEvery
                procedure
                title
                trainingSessions {
                    nodes {
                        id
                        date
                        vessel {
                            id
                            title
                        }
                    }
                }
                vessels {
                    nodes {
                        id
                        title
                    }
                }
            }
        }
    }
`;let V=(0,n.ZP)`
    query GetMemberTraining_Signatures(
        $filter: MemberTraining_SignatureFilterFields = {}
    ) {
        readMemberTraining_Signatures(filter: $filter) {
            nodes {
                id
            }
        }
    }
`;(0,n.ZP)`
    query GetR2Files($id: [ID]!) {
        readR2Files(filter: { id: { in: $id } }) {
            nodes {
                id
                date
                title
            }
        }
    }
`;let H=(0,n.ZP)`
    query GetEngines($id: [ID]!, $filter: EngineStartStopFilterFields = {}) {
        readEngines(filter: { id: { in: $id } }) {
            nodes {
                id
                title
                type
                currentHours
                identifier
                isPrimary
                maxPower
                driveType
                positionOnVessel
                archived
                componentCategory
                make
                model
                kW
                kVA
                engineStartStops(
                    sort: { created: ASC }
                    filter: $filter
                ) {
                    nodes {
                        id
                        hoursStart
                        hoursEnd
                        totalHours
                        timeStart
                        timeEnd
                        logBookEntrySection {
                            logBookEntryID
                        }
                    }
                }
            }
        }
    }
`,U=(0,n.ZP)`
    query GetFuelTanks($id: [ID]!) {
        readFuelTanks(filter: { id: { in: $id } }) {
            nodes {
                id
                title
                identifier
                archived
                componentCategory
                capacity
                safeFuelCapacity
                currentLevel
                lastEdited
                fuelType
                dipType
                dipConversions
                dipImportID
                dipImportRun
                fuelTankStartStops {
                    nodes {
                        id
                        engineID
                    }
                }
            }
        }
    }
`,J=(0,n.ZP)`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                isArchived
                firstName
                isCrew
                departments {
                    nodes {
                        id
                    }
                }
                primaryDutyID
                hasTrainingSessionDue
                primaryDuty {
                    id
                    title
                }
                surname
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
                trainingSessionsDue {
                    nodes {
                        id
                        dueDate
                        memberID
                        member {
                            id
                            firstName
                            surname
                        }
                        vesselID
                        vessel {
                            id
                            title
                        }
                        trainingTypeID
                        trainingType {
                            id
                            title
                        }
                    }
                }
                trainingSessions {
                    nodes {
                        id
                        trainingTypes{
                          nodes{
                            id
                            title
                          }
                        }
                      	startTime
                      	finishTime
                        members {
                            nodes {
                                id
                                firstName
                                surname
                            }
                        }
                    }
                }
            }
        }
    }
`,K=(0,n.ZP)`
    query GetRadioLogs($filter: RadioLogFilterFields = {}) {
        readRadioLogs(filter: $filter, sort: { order: ASC }) {
            nodes {
                id
                title
                status
                comment
                time
                vesselID
                logBookEntryID
                tripScheduleServiceID
                defaultParent
                order
            }
        }
    }
`,j=(0,n.ZP)`
    query GetOtherShips {
        readOtherShips {
            nodes {
                id
                title
                registration
                details
            }
        }
    }
`;(0,n.ZP)`
    query GetPilotTransfers {
        readPilotTransfers {
            nodes {
                id
                transferType
                transferTime
                safetyBriefing
                comment
                pilots {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                transferees {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                otherShip {
                    id
                    title
                    registration
                    details
                }
            }
        }
    }
`;let z=(0,n.ZP)`
    query ReadVessels(
        $limit: Int
        $offset: Int
        $filter: VesselFilterFields = {}
    ) {
        readVessels(filter: $filter, limit: $limit, offset: $offset) {
            nodes {
                id
                title
                archived
            }
        }
    }
`,Y=(0,n.ZP)`
    query GetWaterTanks($id: [ID]!) {
        readWaterTanks(filter: { id: { in: $id } }) {
            nodes {
                id
                title
                identifier
                archived
                componentCategory
                capacity
            }
        }
    }
`,X=(0,n.ZP)`
    query GetSewageSystems($id: [ID]!) {
        readSewageSystems(filter: { id: { in: $id } }) {
            nodes {
                id
                title
                identifier
                archived
                componentCategory
                capacity
                numberOfTanks
            }
        }
    }
`;(0,n.ZP)`
    query Getlogbook($id: ID!) {
        readOneLogBook(filter: { id: { eq: $id } }) {
            id
            title
        }
    }
`;let Q=(0,n.ZP)`
    query ReadComponentMaintenanceChecks(
        $inventoryID: Int!
        $vesselID: Int!
        $archived: Int = 0
    ) {
        readComponentMaintenanceCheckList(
            inventoryID: $inventoryID
            vesselID: $vesselID
            archived: $archived
        ) {
            list
        }
    }
`,ee=(0,n.ZP)`
    query GetConfig($id: ID!) {
        readOneCustomisedLogBookConfig(
            filter: { customisedLogBookID: { eq: $id } }
        ) {
            id
            title
            lastEdited
            customisedComponentCategories
            customisedLogBook {
                id
                title
            }
            customisedLogBookComponents {
                nodes {
                    id
                    title
                    active
                    sortOrder
                    subView
                    subViewTitle
                    subFields
                    customEntryType
                    componentClass
                    category
                    disclaimer {
                        id
                        disclaimerText
                    }
                    customisedComponentFields(
                        sort: {
                            sortOrder: ASC
                            customisedFieldTitle: ASC
                            fieldName: ASC
                        }
                        limit: 500
                    ) {
                        pageInfo {
                            totalCount
                        }
                        nodes {
                            id
                            fieldName
                            status
                            sortOrder
                            description
                            customisedFieldTitle
                            customisedFieldType
                            customisedEngineTypes
                        }
                    }
                }
            }
            policies {
                nodes {
                    id
                    fileFilename
                    name
                    title
                }
            }
        }
    }
`,ei=(0,n.ZP)`
    query GetSectionMemberComments(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SectionMemberCommentFilterFields = {}
    ) {
        readSectionMemberComments(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            nodes {
                id
                commentType
                fieldName
                comment
                logBookEntrySectionID
                hideComment
            }
        }
    }
`,et=(0,n.ZP)`
    query GetGeoLocations(
        $limit: Int = 100
        $offset: Int = 0
    ) {
        readGeoLocations(
            limit: $limit
            offset: $offset
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                title
                lat
                long
            }
        }
    }
`,en=(0,n.ZP)`
    query GetCaptureImages(
        $limit: Int = 500
        $offset: Int = 0
        $filter: CaptureImageFilterFields = {}
    ) {
        readCaptureImages(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { id: DESC }
        ) {
            nodes {
                id
                imageType
                fieldName
                name
                logBookEntrySectionID
                logBookEntryID
                componentMaintenanceCheckID
                tripEventID
                inventoryID
                trainingSessionID
            }
        }
    }
`,eo=(0,n.ZP)`
    query GetEventTypes {
        readEventTypes {
            nodes {
                id
                archived
                title
            }
        }
    }
`;(0,n.ZP)`
    query GetOneTrainingType($id: [ID!]!) {
        readTrainingTypes(filter: { id: { in: $id } }) {
            nodes {
                id
                archived
                highWarnWithin
                mediumWarnWithin
                occursEvery
                procedure
                title
                trainingSessions {
                    nodes {
                        id
                        date
                        vessel {
                            id
                            title
                        }
                    }
                }
                vessels {
                    nodes {
                        id
                        title
                    }
                }
                customisedComponentField {
                    nodes {
                        id
                        status
                        sortOrder
                        fieldName
                        description
                    }
                }
            }
        }
    }
`;let er=(0,n.ZP)`
    query ReadDashboardData($archived: Int = 0) {
        readDashboardData(archived: $archived) {
            vessels {
                id
                archived
                showOnDashboard
                title
                icon
                iconMode
                photoID
                registration
                callSign
                trainingsDue
                tasksDue
                logentryID
                logBookID
                lbeStartDate
                pob
                trainingStatus {
                    id
                    title
                    dueDate
                    isOverDue
                    member
                    memberId
                }
                taskStatus {
                    id
                    title
                    dueDate
                    isOverDue
                }
                vesselPosition {
                    id
                    lat
                    long
                    time
                    geoLocationID
                    geoLocation {
                        id
                        title
                        lat
                        long
                    }
                }
                vesselCrewIds
            }
        }
    }
`,es=(0,n.ZP)`
    query GetTripsByVesselID($id: ID!) {
        readOneVessel(filter: { id: { eq: $id } }) {
            id
            title
            registration
            logBookID
            logBookEntries(sort: { id: DESC }) {
                nodes {
                    id
                    state
                    logBookEntrySections(
                        filter: {
                            className: {
                                endswith: "TripReport_LogBookEntrySection"
                            }
                        }
                    ) {
                        nodes {
                            id
                            className
                        }
                    }
                }
            }
        }
    }
`,ea=(0,n.ZP)`
    query GetTripEvent($id: ID!) {
        readOneTripEvent(filter: { id: { eq: $id } }) {
            id
            eventCategory
            vehicle
            notes
            location
            numberPax
            start
            end
            eventType_VesselRescue {
                id
                vesselName
                callSign
                pob
                latitude
                longitude
                locationDescription
                vesselLength
                vesselType
                makeAndModel
                color
                ownerName
                phone
                email
                address
                ownerOnBoard
                cgMembershipType
                cgMembership
                missionID
                vesselLocationID
                tripEventID
                operationType
                operationDescription
                vesselTypeDescription
                missionTimeline(filter: { archived: { eq: false } }) {
                    nodes {
                        id
                        commentType
                        description
                        time
                        subTaskID
                        author {
                            id
                            firstName
                            surname
                            email
                        }
                    }
                }
                mission {
                    id
                    missionType
                    description
                    operationOutcome
                    completedAt
                    operationDescription
                    eventType
                    vesselPositionID
                    currentLocation {
                        id
                        lat
                        long
                        title
                    }
                    lat
                    long
                    missionTimeline(filter: { archived: { eq: false } }) {
                        nodes {
                            id
                            commentType
                            description
                            time
                            subTaskID
                            author {
                                id
                                firstName
                                surname
                                email
                            }
                        }
                    }
                }
                vesselLocation {
                    id
                    lat
                    long
                    title
                }
            }
            eventType_PersonRescue {
                id
                personName
                gender
                age
                personDescription
                cgMembershipNumber
                personOtherDetails
                cgMembershipType
                cgMembershipNumber
                missionID
                tripEventID
                operationType
                operationDescription
                missionTimeline(filter: { archived: { eq: false } }) {
                    nodes {
                        id
                        commentType
                        description
                        time
                        subTaskID
                        author {
                            id
                            firstName
                            surname
                            email
                        }
                    }
                }
                mission {
                    id
                    missionType
                    description
                    operationOutcome
                    completedAt
                    operationDescription
                    eventType
                    vesselPositionID
                    lat
                    long
                    currentLocation {
                        id
                        lat
                        long
                        title
                    }
                    missionTimeline(filter: { archived: { eq: false } }) {
                        nodes {
                            id
                            commentType
                            description
                            time
                            subTaskID
                            author {
                                id
                                firstName
                                surname
                                email
                            }
                        }
                    }
                }
            }
            eventType_BarCrossing {
                id
                geoLocationID
                time
                stopAssessPlan
                crewBriefing
                weather
                stability
                waterTightness
                lifeJackets
                lookoutPosted
                report
                lat
                long
                timeCompleted
                latCompleted
                longCompleted
                geoLocationCompletedID
                barCrossingChecklist {
                    id
                }
            }
            tripUpdate {
                id
                date
                title
                lat
                long
                notes
                attachment {
                    nodes {
                        id
                        title
                    }
                }
                geoLocationID
                geoLocation {
                    id
                    title
                    lat
                    long
                }
            }
            eventType_RefuellingBunkering {
                id
                date
                title
                lat
                long
                notes
                geoLocationID
                fuelReceipts {
                    nodes {
                        id
                        date
                        title
                    }
                }
                geoLocation {
                    id
                    title
                    lat
                    long
                }
                fuelLog {
                    nodes {
                        id
                        fuelAdded
                        fuelBefore
                        fuelAfter
                        date
                        costPerLitre
                        totalCost
                        fuelTank {
                            id
                            capacity
                            safeFuelCapacity
                            currentLevel
                            title
                        }
                    }
                }
            }
            eventType_RestrictedVisibility {
                id
                startLocationID
                crossingTime
                estSafeSpeed
                stopAssessPlan
                crewBriefing
                navLights
                soundSignal
                lookout
                soundSignals
                radarWatch
                radioWatch
                endLocationID
                crossedTime
                approxSafeSpeed
                report
                startLat
                startLong
                endLat
                endLong
                memberID
                member {
                    firstName
                    surname
                }
            }
            eventType_PassengerDropFacility {
                id
                time
                title
                fuelLevel
                paxOn
                paxOff
                lat
                long
                type
                geoLocationID
                fuelLog {
                    nodes {
                        id
                        fuelAdded
                        fuelBefore
                        fuelAfter
                        date
                        costPerLitre
                        totalCost
                        fuelTank {
                            id
                            capacity
                            safeFuelCapacity
                            currentLevel
                            title
                        }
                    }
                }
            }
            eventType_Tasking {
                id
                time
                title
                fuelLevel
                lat
                cgop
                sarop
                long
                type
                operationType
                geoLocationID
                vesselRescueID
                personRescueID
                groupID
                comments
                status
                tripEventID
                pausedTaskID
                openTaskID
                completedTaskID
                parentTaskingID
                towingChecklist {
                    id
                }
                fuelLog {
                    nodes {
                        id
                        fuelAdded
                        fuelBefore
                        fuelAfter
                        date
                        costPerLitre
                        totalCost
                        fuelTank {
                            id
                            capacity
                            safeFuelCapacity
                            currentLevel
                            title
                        }
                    }
                }
            }
            crewTraining {
                id
            }
            supernumerary {
                id
                title
                totalGuest
                focGuest
                isBriefed
                briefingTime
                guestList {
                    nodes {
                        id
                        firstName
                        surname
                        sectionSignature {
                            id
                            signatureData
                        }
                    }
                }
            }
            infringementNotice {
                id
                time
                vesselType
                vesselName
                vesselReg
                ownerFullName
                ownerAddress
                ownerPhone
                ownerEmail
                ownerDOB
                ownerOccupation
                infringementData
                otherDescription
                waterwaysOfficerID
                geoLocationID
                signatureID
                lat
                long
                waterwaysOfficer {
                    id
                    firstName
                    surname
                }
                geoLocation {
                    id
                    title
                    lat
                    long
                }
                signature {
                    id
                    signatureData
                }
            }
            pilotTransfer {
                id
                transferType
                transferTime
                safetyBriefing
                comment
                pilots {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                transferees {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                otherShip {
                    id
                    title
                    registration
                    details
                }
            }
        }
    }
`,ed=(0,n.ZP)`
    query GetTripEvent_VesselRescue($id: ID!) {
        readOneEventType_VesselRescue(filter: { id: { eq: $id } }) {
            id
            vesselName
            callSign
            pob
            latitude
            longitude
            locationDescription
            vesselLength
            vesselType
            makeAndModel
            color
            ownerName
            phone
            email
            address
            ownerOnBoard
            cgMembershipType
            cgMembership
            missionID
            vesselLocationID
            tripEventID
            operationType
            operationDescription
            vesselTypeDescription
            mission {
                id
                missionType
                description
                operationOutcome
                completedAt
                operationDescription
                eventType
                vesselPositionID
                currentLocation {
                    id
                    lat
                    long
                    title
                }
                missionTimeline(filter: { archived: { eq: false } }) {
                    nodes {
                        id
                        commentType
                        description
                        time
                        subTaskID
                        author {
                            id
                            firstName
                            surname
                            email
                        }
                    }
                }
            }
            missionTimeline(filter: { archived: { eq: false } }) {
                nodes {
                    id
                    commentType
                    description
                    time
                    subTaskID
                    author {
                        id
                        firstName
                        surname
                        email
                    }
                }
            }
            vesselLocation {
                id
                lat
                long
                title
            }
        }
    }
`,el=(0,n.ZP)`
    query GetTripEvent_PersonRescue($id: ID!) {
        readOneEventType_PersonRescue(filter: { id: { eq: $id } }) {
            id
            personName
            gender
            age
            personDescription
            cgMembershipNumber
            personOtherDetails
            cgMembershipType
            cgMembershipNumber
            missionID
            tripEventID
            operationType
            operationDescription
            missionTimeline(filter: { archived: { eq: false } }) {
                nodes {
                    id
                    commentType
                    description
                    time
                    subTaskID
                    author {
                        id
                        firstName
                        surname
                        email
                    }
                }
            }
            mission {
                id
                missionType
                description
                operationOutcome
                completedAt
                operationDescription
                eventType
                vesselPositionID
                currentLocation {
                    id
                    lat
                    long
                    title
                }
                missionTimeline(filter: { archived: { eq: false } }) {
                    nodes {
                        id
                        commentType
                        description
                        time
                        subTaskID
                        author {
                            id
                            firstName
                            surname
                            email
                        }
                    }
                }
            }
        }
    }
`,ec=(0,n.ZP)`
    query readOneAssetReporting_LogBookEntrySection($id: ID!) {
        readOneAssetReporting_LogBookEntrySection(filter: { id: { eq: $id } }) {
            id
        }
    }
`,em=(0,n.ZP)`
    query GetCrewMembers_LogBookEntrySections(
        $filter: CrewMembers_LogBookEntrySectionFilterFields = {}
    ) {
        readCrewMembers_LogBookEntrySections(filter: $filter) {
            nodes {
                id
                punchIn
                punchOut
                archived
                dutyHours
                workDetails
                userDefinedData
                crewMemberID
                logBookEntryID
                crewMember {
                    id
                    firstName
                    surname
                    trainingSessionsDue {
                        nodes {
                            id
                            dueDate
                            memberID
                            member {
                                id
                                firstName
                                surname
                            }
                            vesselID
                            vessel {
                                id
                                title
                            }
                            trainingTypeID
                            trainingType {
                                id
                                title
                            }
                        }
                    }
                }
                dutyPerformedID
                dutyPerformed {
                    id
                    title
                    abbreviation
                }
                logBookEntry {
                    id
                    startDate
                    vehicleID
                    vehicle {
                        id
                        title
                    }
                }
            }
        }
    }
`,eu=(0,n.ZP)`
    query GetCrewTraining_LogBookEntrySections($id: [ID]!) {
        readCrewTraining_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                archived
                trainingType
                trainingLocation
                trainingSummary
                trainingTime
                migrated
                sectionSignature {
                    id
                    signatureData
                    member {
                        id
                        firstName
                        surname
                    }
                }
                createdBy {
                    id
                    firstName
                    surname
                }
                trainer {
                    id
                    firstName
                    surname
                }
                sectionMemberComments {
                    nodes {
                        id
                    }
                }
                members {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                logBookEntry {
                    id
                    vehicle {
                        id
                        title
                    }
                }
            }
        }
    }
`,eg=(0,n.ZP)`
    query GetEngineer_LogBookEntrySections($id: [ID]!) {
        readEngineer_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
            }
        }
    }
`,ep=(0,n.ZP)`
    query GetEngine_LogBookEntrySections($id: [ID]!) {
        readEngine_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                archived
                subView
                fuelStart
                nauticalMiles
                fuelEnd
                fuelAdded
                hoursRun
                engine {
                    id
                    archived
                    title
                }
                engineStartStop {
                    id
                    hoursStart
                    hoursEnd
                    totalHours
                    timeStart
                    timeEnd
                    vehicleDutySessionID
                    engineID
                }
                engineRunningCheck {
                    id
                    archived
                    entryTime
                    manifoldTemp
                    genSetTemp
                    coolantTemp
                    coolantLevelOK
                    fuelTemp
                    shaftBearingTemp
                    oilLevelOK
                    oilPressure
                    lubeOilLevelOK
                    lubeOilTemp
                    lubeOilPressure
                    lubeFilterPressure
                    fuelPressure
                    fuelDayTankLevel
                    headerTankLevel
                    fuelRate
                    volts
                    kwLoad
                    overboardPressure
                    overboardDischarge
                    maintenanceActions
                    pyros
                    boost
                    waterTemp
                    airTemp
                    rpm
                    rack
                    genSetOP
                    genSetWT
                    gearboxOP
                    gearboxCLOP
                    gearboxOT
                    hrpop
                    engine {
                        id
                        title
                    }
                    seaLogsMember {
                        id
                        firstName
                        surname
                    }
                }
            }
        }
    }
`,ef=(0,n.ZP)`
    query GetFuel_LogBookEntrySections($id: [ID]!) {
        readFuel_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                fuelTankStartStops {
                    nodes {
                        id
                        start
                        end
                        fuelType
                        comments
                        fuelTankID
                    }
                }
            }
        }
    }
`,ey=(0,n.ZP)`
    query GetPorts_LogBookEntrySections($id: [ID]!) {
        readPorts_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
            }
        }
    }
`,eh=(0,n.ZP)`
    query GetSupernumerary_LogBookEntrySections($id: [ID]!) {
        readSupernumerary_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                archived
                firstName
                surname
                disclaimer {
                    id
                    disclaimerText
                }
                sectionSignature {
                    id
                    signatureData
                    member {
                        id
                        firstName
                        surname
                    }
                }
                logBookEntry {
                    id
                    vehicle {
                        id
                        title
                    }
                }
            }
        }
    }
`,eD=(0,n.ZP)`
    query GetTripReport_LogBookEntrySections($id: [ID]!) {
        readTripReport_LogBookEntrySections(
            filter: { id: { in: $id } }
            sort: { created: ASC }
        ) {
            nodes {
                id
                archived
                sectionSignatureID
                sectionSignature {
                    id
                    signatureData
                }
                createdByID
                dailyChecksCompleted
                depart
                departFrom
                arrive
                lastEdited
                arriveTo
                departTime
                tripScheduleDepartTime
                fromFreehand
                fromLat
                fromLong
                arriveTime
                tripScheduleArriveTime
                toFreehand
                toLat
                toLong
                pob
                numberPax
                paxJoinedAdult
                paxJoinedChild
                paxJoinedYouth
                paxJoinedFOC
                paxJoinedStaff
                paxJoinedVoucher
                paxJoinedPrePaid
                paxDeparted
                safetyBriefing
                speedExemption
                expectedNextContact
                fromCreatesNewGeoLocation
                toCreatesNewGeoLocation
                voucher
                incidentReports
                hazardReports
                prevPaxState
                comment
                vob
                totalVehiclesCarried
                vehiclesJoined
                vehiclesDeparted
                observedDepart
                observedArrive
                masterID
                leadGuideID
                fromLocationID
                toLocationID
                tripReportScheduleID
                tripReportSchedule {
                    id
                    tripScheduleServiceID
                }
                lateDepartureReasonID
                tripUpdateEntityID
                speedExemptionCorridorID
                speedExemptionReasonID
                unscheduledServiceID
                designatedDangerousGoodsSailing
                dangerousGoodsRecords {
                    nodes {
                        id
                        comment
                        type
                    }
                }
                enableDGR
                dangerousGoodsChecklist {
                    id
                    vesselSecuredToWharf
                    bravoFlagRaised
                    twoCrewLoadingVessel
                    fireHosesRiggedAndReady
                    noSmokingSignagePosted
                    spillKitAvailable
                    fireExtinguishersAvailable
                    dgDeclarationReceived
                    loadPlanReceived
                    msdsAvailable
                    anyVehiclesSecureToVehicleDeck
                    safetyAnnouncement
                    vehicleStationaryAndSecure
                    riskFactors {
                        nodes {
                            id
                            type
                            title
                            impact
                            probability
                            created
                            mitigationStrategy {
                                nodes {
                                    id
                                    strategy
                                }
                            }
                        }
                    }
                }
                tripEvents(sort: { created: ASC }) {
                    nodes {
                        id
                        created
                        start
                        end
                        numberPax
                        cause
                        notes
                        location
                        vehicle
                        eventCategory
                        eventType_VesselRescueID
                        eventType_PersonRescueID
                        eventType_BarCrossingID
                        eventType_RestrictedVisibilityID
                        eventType_PassengerDropFacilityID
                        eventType_TaskingID
                        incidentRecordID
                        eventType {
                            id
                            title
                        }
                        eventType_PassengerDropFacility {
                            id
                            title
                            fuelLevel
                            time
                            type
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            fuelLog {
                                nodes {
                                    id
                                    fuelAdded
                                    fuelBefore
                                    fuelAfter
                                    date
                                    costPerLitre
                                    totalCost
                                    fuelTank {
                                        id
                                        capacity
                                        safeFuelCapacity
                                        currentLevel
                                        title
                                    }
                                }
                            }
                        }
                        eventType_Tasking {
                            id
                            title
                            time
                            type
                            status
                            cgop
                            sarop
                            fuelLevel
                            tripEventID
                            pausedTaskID
                            openTaskID
                            completedTaskID
                            vesselRescueID
                            personRescueID
                            groupID
                            parentTaskingID
                            towingChecklist {
                                id
                            }
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            fuelLog {
                                nodes {
                                    id
                                    fuelAdded
                                    fuelBefore
                                    fuelAfter
                                    date
                                    costPerLitre
                                    totalCost
                                    fuelTank {
                                        id
                                        capacity
                                        safeFuelCapacity
                                        currentLevel
                                        title
                                    }
                                }
                            }
                        }
                        eventType_BarCrossing {
                            id
                            time
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            timeCompleted
                            geoLocationCompleted {
                                id
                                title
                                lat
                                long
                            }
                            barCrossingChecklist {
                                id
                            }
                        }
                        eventType_RestrictedVisibility {
                            id
                            crossingTime
                            crossedTime
                            startLocationID
                            startLocation {
                                title
                            }
                            endLocationID
                        }
                        tripUpdate {
                            id
                            date
                            title
                            lat
                            long
                            notes
                            attachment {
                                nodes {
                                    id
                                    title
                                }
                            }
                            geoLocationID
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                        }
                        eventType_RefuellingBunkering {
                            id
                            date
                            title
                            lat
                            long
                            notes
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            geoLocationID
                            fuelLog {
                                nodes {
                                    id
                                    fuelAdded
                                    fuelBefore
                                    fuelAfter
                                    date
                                    costPerLitre
                                    totalCost
                                    fuelTank {
                                        id
                                        capacity
                                        safeFuelCapacity
                                        currentLevel
                                        title
                                    }
                                }
                            }
                        }
                        supernumerary {
                            id
                            title
                            totalGuest
                            focGuest
                            isBriefed
                            briefingTime
                            guestList {
                                nodes {
                                    id
                                    firstName
                                    surname
                                    sectionSignature {
                                        id
                                        signatureData
                                    }
                                }
                            }
                        }
                        crewTraining {
                            id
                            startTime
                            finishTime
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                        }
                        infringementNotice {
                            id
                            time
                            vesselType
                            vesselName
                            vesselReg
                            ownerFullName
                            ownerAddress
                            ownerPhone
                            ownerEmail
                            ownerDOB
                            ownerOccupation
                            infringementData
                            otherDescription
                            waterwaysOfficerID
                            geoLocationID
                            signatureID
                            lat
                            long
                            waterwaysOfficer {
                                id
                                firstName
                                surname
                            }
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            signature {
                                id
                                signatureData
                            }
                        }
                        incidentRecord {
                            id
                            title
                            startDate
                            endDate
                            incidentType
                            personsInvolved
                            description
                            treatment
                            contributingFactor
                            riskAssessmentReviewed
                            notifiable
                            location {
                                id
                                title
                                lat
                                long
                            }
                            reportedBy {
                                id
                                firstName
                                surname
                            }
                            vessel {
                                id
                                title
                            }
                        }
                    }
                }
                tripReport_Stops {
                    nodes {
                        id
                        arriveTime
                        departTime
                        paxJoined
                        paxDeparted
                        vehiclesJoined
                        vehiclesDeparted
                        stopLocationID
                        otherCargo
                        lat
                        long
                        comments
                        designatedDangerousGoodsSailing
                        dangerousGoodsChecklistID
                        stopLocation {
                            id
                            title
                            lat
                            long
                        }
                    }
                }
                fromLocation {
                    id
                    title
                    lat
                    long
                }
                toLocation {
                    id
                    title
                    lat
                    long
                }
            }
        }
    }
`,eI=(0,n.ZP)`
    query GetTripReport_LogBookEntrySections($id: [ID]!) {
        readTripReport_LogBookEntrySections(
            filter: { id: { in: $id } }
            sort: { created: ASC }
        ) {
            nodes {
                id
                arrive
                departTime
                arriveTime
                pob
                paxDeparted
                safetyBriefing
                hazardReports
                prevPaxState
                comment
                vob
                totalVehiclesCarried
                vehiclesJoined
                vehiclesDeparted
                observedDepart
                observedArrive
                masterID
                leadGuideID
                fromLocationID
                toLocationID
                tripReportScheduleID
                lateDepartureReasonID
                tripUpdateEntityID
                unscheduledServiceID
                dangerousGoodsRecords {
                    nodes {
                        id
                        comment
                        type
                    }
                }
                enableDGR
                designatedDangerousGoodsSailing
                dangerousGoodsChecklist {
                    id
                    vesselSecuredToWharf
                    bravoFlagRaised
                    twoCrewLoadingVessel
                    fireHosesRiggedAndReady
                    noSmokingSignagePosted
                    spillKitAvailable
                    fireExtinguishersAvailable
                    dgDeclarationReceived
                    loadPlanReceived
                    msdsAvailable
                    anyVehiclesSecureToVehicleDeck
                    safetyAnnouncement
                    vehicleStationaryAndSecure
                    member {
                        id
                        firstName
                        surname
                    }
                    riskFactors {
                        nodes {
                            id
                            type
                            title
                            impact
                            probability
                            created
                            mitigationStrategy {
                                nodes {
                                    id
                                    strategy
                                }
                            }
                        }
                    }
                }
                tripEvents(sort: { created: ASC }) {
                    nodes {
                        id
                        created
                        start
                        end
                        numberPax
                        cause
                        notes
                        location
                        vehicle
                        eventCategory
                        eventType {
                            id
                            title
                        }
                        eventType_PassengerDropFacility {
                            id
                            title
                            fuelLevel
                            time
                            type
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            fuelLog {
                                nodes {
                                    id
                                    fuelAdded
                                    fuelBefore
                                    fuelAfter
                                    date
                                    costPerLitre
                                    totalCost
                                    fuelTank {
                                        id
                                        capacity
                                        safeFuelCapacity
                                        currentLevel
                                        title
                                    }
                                }
                            }
                        }
                        eventType_Tasking {
                            id
                            title
                            time
                            type
                            status
                            cgop
                            sarop
                            fuelLevel
                            tripEventID
                            pausedTaskID
                            openTaskID
                            completedTaskID
                            vesselRescueID
                            personRescueID
                            groupID
                            parentTaskingID
                            operationType
                            vesselRescue {
                                vesselName
                                callSign
                                pob
                                latitude
                                longitude
                                locationDescription
                                vesselLength
                                vesselType
                                makeAndModel
                                color
                                ownerName
                                phone
                                email
                                address
                                ownerOnBoard
                                cgMembershipType
                                cgMembership
                                operationType
                                operationDescription
                                vesselTypeDescription
                                mission {
                                    id
                                    missionType
                                    description
                                    operationOutcome
                                    completedAt
                                    operationDescription
                                    eventType
                                    vesselPositionID
                                    currentLocation {
                                        id
                                        lat
                                        long
                                        title
                                    }
                                    missionTimeline(
                                        filter: { archived: { eq: false } }
                                    ) {
                                        nodes {
                                            id
                                            commentType
                                            description
                                            time
                                            subTaskID
                                            author {
                                                id
                                                firstName
                                                surname
                                                email
                                            }
                                        }
                                    }
                                }
                                vesselLocation {
                                    id
                                    title
                                    lat
                                    long
                                }
                                missionTimeline {
                                    nodes {
                                        id
                                        commentType
                                        description
                                        time
                                        subTaskID
                                        author {
                                            id
                                            firstName
                                            surname
                                            email
                                        }
                                    }
                                }
                            }
                            towingChecklist {
                                id
                                conductSAP
                                investigateNatureOfIssue
                                everyoneOnBoardOk
                                rudderToMidshipsAndTrimmed
                                lifejacketsOn
                                communicationsEstablished
                                secureAndSafeTowing
                                riskFactors {
                                    nodes {
                                        id
                                        type
                                        title
                                        impact
                                        probability
                                    }
                                }
                                member {
                                    id
                                    firstName
                                    surname
                                }
                            }
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            fuelLog {
                                nodes {
                                    id
                                    fuelAdded
                                    fuelBefore
                                    fuelAfter
                                    date
                                    costPerLitre
                                    totalCost
                                    fuelTank {
                                        id
                                        capacity
                                        safeFuelCapacity
                                        currentLevel
                                        title
                                    }
                                }
                            }
                        }
                        eventType_BarCrossing {
                            id
                            time
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            timeCompleted
                            geoLocationCompleted {
                                id
                                title
                                lat
                                long
                            }
                            barCrossingChecklist {
                                id
                                stopAssessPlan
                                crewBriefing
                                weather
                                stability
                                waterTightness
                                lifeJackets
                                lookoutPosted
                                riskFactors {
                                    nodes {
                                        id
                                        type
                                        title
                                        impact
                                        probability
                                        created
                                    }
                                }
                                member {
                                    id
                                    firstName
                                    surname
                                }
                            }
                        }
                        eventType_RestrictedVisibility {
                            id
                            crossingTime
                            crossedTime
                            startLocationID
                            startLocation {
                                id
                                title
                                lat
                                long
                            }
                            endLocationID
                            endLocation {
                                id
                                title
                                lat
                                long
                            }
                            crewBriefing
                            navLights
                            soundSignals
                            lookout
                            soundSignal
                            radarWatch
                            radioWatch
                            estSafeSpeed
                            approxSafeSpeed
                            startLat
                            startLong
                            endLat
                            endLong
                            memberID
                            member {
                                firstName
                                surname
                            }
                        }
                        eventType_RefuellingBunkering {
                            id
                            date
                            title
                            lat
                            long
                            notes
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            geoLocationID
                            fuelLog {
                                nodes {
                                    id
                                    fuelAdded
                                    fuelBefore
                                    fuelAfter
                                    date
                                    costPerLitre
                                    totalCost
                                    fuelTank {
                                        id
                                        capacity
                                        safeFuelCapacity
                                        currentLevel
                                        title
                                    }
                                }
                            }
                        }
                        tripUpdate {
                            id
                            date
                            title
                            lat
                            long
                            notes
                            attachment {
                                nodes {
                                    id
                                    title
                                }
                            }
                            geoLocationID
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                        }
                        supernumerary {
                            id
                            title
                            totalGuest
                            focGuest
                            isBriefed
                            briefingTime
                            guestList {
                                nodes {
                                    id
                                    firstName
                                    surname
                                    sectionSignature {
                                        id
                                        signatureData
                                    }
                                }
                            }
                        }
                        crewTraining {
                            id
                            startTime
                            finishTime
                            trainer {
                                id
                                firstName
                                surname
                            }
                            members {
                                nodes {
                                    id
                                    firstName
                                    surname
                                }
                            }
                            trainingTypes {
                                nodes {
                                    id
                                    title
                                }
                            }
                            trainingSummary
                            date
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                        }
                        infringementNoticeID
                    }
                }
                tripReport_Stops {
                    nodes {
                        id
                        arriveTime
                        departTime
                        paxJoined
                        paxDeparted
                        vehiclesJoined
                        vehiclesDeparted
                        stopLocationID
                        otherCargo
                        designatedDangerousGoodsSailing
                        lat
                        long
                        comments
                        dangerousGoodsChecklistID
                        stopLocation {
                            id
                            title
                            lat
                            long
                        }
                    }
                }
                fromLocation {
                    id
                    title
                    lat
                    long
                }
                toLocation {
                    id
                    title
                    lat
                    long
                }
            }
        }
    }
`,ev=(0,n.ZP)`
    query GetTripReport_LogBookEntrySections_Brief($id: [ID]!) {
        readTripReport_LogBookEntrySections(
            filter: { id: { in: $id } }
            sort: { created: DESC }
        ) {
            nodes {
                id
                archived
                created
                depart
                lastEdited
                departFrom
                arrive
                arriveTo
                departTime
                fromLat
                fromLong
                arriveTime
                totalVehiclesCarried
                toLat
                toLong
                pob
                comment
                dangerousGoodsRecords {
                    nodes {
                        id
                        comment
                        type
                    }
                }
                tripEvents(sort: { created: ASC }) {
                    nodes {
                        id
                        eventCategory
                        created
                        eventType_PassengerDropFacilityID
                        eventType_TaskingID
                        eventType_PassengerDropFacility {
                            id
                            fuelLevel
                            fuelLog {
                                nodes {
                                    id
                                    fuelAdded
                                    fuelBefore
                                    fuelAfter
                                    date
                                    costPerLitre
                                    totalCost
                                    fuelTank {
                                        id
                                        capacity
                                        safeFuelCapacity
                                        currentLevel
                                        title
                                    }
                                }
                            }
                        }
                        tripUpdate {
                            id
                            date
                            title
                            lat
                            long
                            notes
                            attachment {
                                nodes {
                                    id
                                    title
                                }
                            }
                            geoLocationID
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                        }
                        eventType_RefuellingBunkering {
                            id
                            date
                            title
                            lat
                            long
                            notes
                            geoLocationID
                            geoLocation {
                                id
                                title
                                lat
                                long
                            }
                            fuelLog {
                                nodes {
                                    id
                                    fuelAdded
                                    fuelBefore
                                    fuelAfter
                                    date
                                    costPerLitre
                                    totalCost
                                    fuelTank {
                                        id
                                        capacity
                                        safeFuelCapacity
                                        currentLevel
                                        title
                                    }
                                }
                            }
                        }
                        eventType_Tasking {
                            id
                            fuelLevel
                            type
                            cgop
                            sarop
                            pausedTaskID
                            openTaskID
                            completedTaskID
                            groupID
                            parentTaskingID
                            towingChecklist {
                                id
                            }
                            fuelLog {
                                nodes {
                                    id
                                    fuelAdded
                                    fuelBefore
                                    fuelAfter
                                    date
                                    costPerLitre
                                    totalCost
                                    fuelTank {
                                        id
                                        capacity
                                        safeFuelCapacity
                                        currentLevel
                                        title
                                    }
                                }
                            }
                        }
                    }
                }
                fromLocation {
                    id
                    title
                    lat
                    long
                }
                toLocation {
                    id
                    title
                    lat
                    long
                }
            }
        }
    }
`,eT=(0,n.ZP)`
    query GetVesselDailyCheck_LogBookEntrySections($id: [ID]!) {
        readVesselDailyCheck_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                checkTime
                bilgeLevels
                bilgePumps
                hull
                navEquipment
                oilAndWater
                engineRoomChecks
                forwardAndReverseBelts
                driveShafts
                steeringTiller
                cablesFRPullies
                throttleAndCable
                wiring
                beltsHosesClamps
                sandTraps
                batteries
                safetyEquipment
                checksWithManual
                cabin
                preStartupChecks
                engineChecks
                otherNavigation
                navigationCharts
                engineCheckPropellers
                forwardReverse
                electricalVisualFields
                postElectricalStrainers
                engineOilWater
                engineMountsAndStabilisers
                postStartupEngineChecks
                preEngineAndPropulsion
                postEngineAndPropulsion
                postElectrical
                otherEngineFields
                preFuelLevelStart
                preFuelLevelEnd
                houseBatteriesStatus
                checkOilPressure
                batteryIsCharging
                shorePowerIsDisconnected
                lockToLockSteering
                trimTabs
                steeringTrimTabs
                oilWater
                electrical
                postStartupChecks
                navigationChecks
                depthSounder
                radar
                tracPlus
                chartPlotter
                sart
                aisOperational
                vhf
                uhf
                forwardAndReverse
                hull_HullStructure
                pontoonPressure
                bungsInPlace
                hull_DeckEquipment
                swimPlatformLadder
                biminiTopsCanvasCovers
                windscreenCheck
                nightLineDockLinesRelease
                floor
                engineMounts
                engineOil
                engineTellTale
                engineIsFit
                steeringFluid
                steeringRams
                steeringIsFit
                epirb
                lifeJackets
                fireExtinguisher
                unitTransomBolts
                cotterPins
                reverseBucketAndRam
                nozzleAndBearings
                tailHousing
                weatherSummary
                windDirection
                windStrength
                swell
                lifeRings
                flares
                fireHoses
                fireBuckets
                fireBlanket
                fireAxes
                firePump
                fireFlaps
                lifeRaft
                highWaterAlarm
                firstAid
                exterior
                interior
                cleanGalleyBench
                cleanGalleyFloor
                cleanTable
                cleanMirrorGlass
                cleanToilet
                cleanSink
                cleanDeckFloor
                cleanOutsideWallWindow
                cleanGarbageBin
                cleanBoothSeat
                cleanFridge
                cleanCupboard
                cleanOven
                cleanSouvenir
                cleanRestockSalesItem
                cleanTill
                charts
                documentCrewBriefings
                recordComments
                engineChecks
                steering
                cooling
                propulsion
                bilge
                engineRoom
                throttle
                jetUnit
                generator
                fuelLevel
                fuelTanks
                fuelFilters
                fuel
                hullStructure
                deckEquipment
                anchor
                hatches
                dayShapes
                hvac
                tv
                stabilizationSystems
                electronics
                gps
                radio
                navigationLights
                compass
                soundSignallingDevices
                navigationHazards
                wheelhouse
                bilgeCheck
                sewage
                freshWater
                sanitation
                pestControl
                mainEngine
                transmission
                steeringPropultion
                propulsionCheck
                stabilizers
                exhaust
                propulsionBatteriesStatus
                personOverboardRescueEquipment
                smokeDetectors
                shorePower
                electricalPanels
                seaStrainers
                sail
                mainEngineChecks
                propulsionEngineChecks
                propulsionPropulsion
                electricalChecks
                engineRoomVisualInspection
                fuelSystems
                steeringChecks
                throttleAndCableChecks
                tenderOperationalChecks
                airShutoffs
                fireDampeners
                coolantLevels
                fuelShutoffs
                separators
                steeringRudders
                steeringHoses
                steeringTillers
                steeringHydraulicSystems
                operationalTestsOfHelms
                driveShaftsChecks
                gearBox
                propellers
                skeg
                engrMechanical
                mechCrankcaseOilLevel
                mechCoolingWaterLevel
                mechTransmissionOilLevel
                mechInspectPipework
                mechHydraulicSteeringOilLevel
                mechGearBoxOilLevel
                mechInspectVeeBelts
                engrGenerator
                genCrankcaseOilLevel
                genCoolingWaterLevel
                genElectrical
                genPracxisSystemOperative
                genTest24VLighting
                genRunningTankFuelLevel
                engrElectronics
                electrDeckLights
                electrSearchLights
                electrChart
                engrTowlineWinch
                towCheckWinchCondition
                towProveWinchOperation
                towSelectControlStation
                towCheckTowlineCondition
                biosecGlueBoardTraps
                crewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                preCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                preCheckTime
                postCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                postCheckTime
                otherEngineCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                otherEngineCheckTime
                deckOpsCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                deckOpsCheckTime
                navigationCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                navigationCheckTime
                jetCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                jetCheckTime
                cleaningCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                cleaningCheckTime
                hvacCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                hvacCheckTime
                plumbingCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                plumbingCheckTime
                sailCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                sailCheckTime
                engrCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                engrCheckTime
                biosecCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                biosecCheckTime
            }
        }
    }
`,eS=(0,n.ZP)`
    query GetVoyageSummary_LogBookEntrySections($id: [ID]!) {
        readVoyageSummary_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                archived
                logBookComponentClass
                subView
                sortOrder
                clientID
                logBookEntryID
                sectionSignatureID
                createdByID
                riverFlowID
                oktas
                windStrength
                windDirection
                precipitation
                visibility
                swell
                seaState
                courseSteered
                courseOverGround
                changesToPlan
                speedOverGround
                vesselRPM
                typeOfSteering
                voyageDistance
                weatherComments
                forecastComment
                activities
                tripEvents {
                    nodes {
                        id
                        start
                        end
                        numberPax
                        cause
                        notes
                        location
                        created
                        vehicle
                        eventType {
                            id
                            title
                        }
                        seaLogsMember {
                            id
                            firstName
                            surname
                        }
                    }
                }
            }
        }
    }
`,eC=(0,n.ZP)`
    query GetCrewWelfare_LogBookEntrySections($id: [ID]!) {
        readCrewWelfare_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                fitness
                safetyActions
                waterQuality
                imSafe
            }
        }
    }
`,ek=(0,n.ZP)`
    query GetLogBookSignOff_LogBookEntrySections($id: [ID]!) {
        readLogBookSignOff_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                review
                safetyEquipmentCheck
                forecastAccuracy
                ais
                created
                navigationLightsAndShapes
                electronicNavigationalAids
                mainEngines
                auxiliarySystems
                fuelAndOil
                bilgeSystems
                power
                batteryMaintenance
                circuitInspections
                mooringAndAnchoring
                cargoAndAccessEquipment
                hatchesAndWatertightDoors
                galleyAppliances
                wasteManagement
                ventilationAndAirConditioning
                emergencyReadiness
                environmentalCompliance
                navigationAndBridgeEquipment
                engineRoomAndMachinery
                electricalSystems
                deckOperations
                accommodationAndGalley
                finalChecks
                signOffMemberID
                fuelStart
                sectionSignatureID
                completedTime
                endLocationID
                endLocation {
                    id
                    lat
                    long
                    time
                    geoLocationID
                    geoLocation {
                        id
                        title
                        lat
                        long
                    }
                }
                sectionSignature {
                    id
                    signatureData
                }
            }
        }
    }
`,eL=(0,n.ZP)`
    query GetTowingChecklist($id: [ID]!) {
        readOneTowingChecklist(filter: { id: { in: $id } }) {
            id
            conductSAP
            investigateNatureOfIssue
            everyoneOnBoardOk
            rudderToMidshipsAndTrimmed
            lifejacketsOn
            communicationsEstablished
            secureAndSafeTowing
            member {
                id
                firstName
                surname
            }
            riskFactors {
                nodes {
                    id
                    type
                    title
                    impact
                    probability
                    riskRating {
                        id
                    }
                    likelihood {
                        id
                    }
                    mitigationStrategy {
                        nodes {
                            id
                            strategy
                        }
                    }
                }
            }
        }
    }
`,eb=(0,n.ZP)`
    query GetBarCrossingChecklist($id: [ID]!) {
        readOneBarCrossingChecklist(filter: { id: { in: $id } }) {
            id
            stopAssessPlan
            crewBriefing
            weather
            stability
            waterTightness
            lifeJackets
            lookoutPosted
            member {
                id
                firstName
                surname
            }
            riskFactors {
                nodes {
                    id
                    type
                    title
                    impact
                    probability
                    riskRating {
                        id
                    }
                    likelihood {
                        id
                    }
                    mitigationStrategy {
                        nodes {
                            id
                            strategy
                        }
                    }
                }
            }
        }
    }
`,e$=(0,n.ZP)`
    query GetRiskFactors($filter: RiskFactorFilterFields = {}) {
        readRiskFactors(filter: $filter, sort: { created: DESC }) {
            nodes {
                id
                type
                title
                impact
                probability
                dangerousGoodsChecklistID
                barCrossingChecklistID
                towingChecklistID
                eventType_RestrictedVisibilityID
                dangerousGoodsChecklist {
                    member {
                        id
                        firstName
                        surname
                    }
                }
                barCrossingChecklist {
                    member {
                        id
                        firstName
                        surname
                    }
                }
                towingChecklist {
                    member {
                        id
                        firstName
                        surname
                    }
                }
                created
                vessel {
                    id
                    title
                }
                mitigationStrategy {
                    nodes {
                        id
                        strategy
                    }
                }
            }
        }
    }
`;(0,n.ZP)`
    query Get_EventType_TaskingChecklist(
        $filter: EventType_TaskingFilterFields = {}
    ) {
        readEventType_Taskings(filter: $filter, sort: { created: DESC }) {
            nodes {
                id
                title
                time
                type
                status
                cgop
                sarop
                created
                geoLocation {
                    id
                    title
                    lat
                    long
                }
                tripEvent {
                    logBookEntrySection {
                        logBookEntryID
                        logBookEntry {
                            vehicle {
                                id
                                title
                            }
                        }
                    }
                }
                towingChecklist {
                    id
                    conductSAP
                    investigateNatureOfIssue
                    everyoneOnBoardOk
                    rudderToMidshipsAndTrimmed
                    lifejacketsOn
                    communicationsEstablished
                    secureAndSafeTowing
                    member {
                        id
                        firstName
                        surname
                    }
                    riskFactors {
                        nodes {
                            id
                            type
                            title
                            impact
                            probability
                            created
                            mitigationStrategy {
                                nodes {
                                    id
                                    strategy
                                }
                            }
                        }
                    }
                }
            }
        }
    }
`;let eP=(0,n.ZP)`
    query GetTripReport_Stop($id: ID!) {
        readOneTripReport_Stop(filter: { id: { eq: $id } }) {
            id
            arriveTime
            departTime
            paxJoined
            paxDeparted
            vehiclesJoined
            vehiclesDeparted
            stopLocationID
            otherCargo
            lat
            long
            comments
            designatedDangerousGoodsSailing
            stopLocation {
                id
                title
                lat
                long
            }
            dangerousGoodsRecords {
                nodes {
                    id
                    comment
                    type
                }
            }
            dangerousGoodsChecklist {
                id
                vesselSecuredToWharf
                bravoFlagRaised
                twoCrewLoadingVessel
                fireHosesRiggedAndReady
                noSmokingSignagePosted
                spillKitAvailable
                fireExtinguishersAvailable
                dgDeclarationReceived
                loadPlanReceived
                msdsAvailable
                anyVehiclesSecureToVehicleDeck
                safetyAnnouncement
                vehicleStationaryAndSecure
                riskFactors {
                    nodes {
                        id
                        type
                        title
                        impact
                        probability
                        created
                        mitigationStrategy {
                            nodes {
                                id
                                strategy
                            }
                        }
                    }
                }
            }
        }
    }
`;(0,n.ZP)`
    query GetOneDangerousGoodsRecord($id: ID!) {
        readOneDangerousGoodsRecord(filter: { id: { eq: $id } }) {
            id
            comment
            type
        }
    }
`;let eE=(0,n.ZP)`
    query GetDangerousGoodsRecords($ids: [ID]!) {
        readDangerousGoodsRecords(filter: { id: { in: $ids } }) {
            nodes {
                id
                comment
                type
            }
        }
    }
`,eB=(0,n.ZP)`
    query GetOneDangerousGoodsChecklist($id: ID!) {
        readOneDangerousGoodsChecklist(filter: { id: { eq: $id } }) {
            id
            vesselSecuredToWharf
            bravoFlagRaised
            twoCrewLoadingVessel
            fireHosesRiggedAndReady
            noSmokingSignagePosted
            spillKitAvailable
            fireExtinguishersAvailable
            dgDeclarationReceived
            loadPlanReceived
            msdsAvailable
            anyVehiclesSecureToVehicleDeck
            safetyAnnouncement
            vehicleStationaryAndSecure
            member {
                id
                firstName
                surname
            }
            riskFactors {
                nodes {
                    id
                    type
                    title
                    impact
                    probability
                    created
                    mitigationStrategy {
                        nodes {
                            id
                            strategy
                        }
                    }
                }
            }
        }
    }
`,eq=(0,n.ZP)`
    query GetTaskRecords($id: ID!) {
        readMissionTimelines(
            filter: { maintenanceCheckID: { eq: $id }, archived: { eq: false } }
        ) {
            nodes {
                id
                time
                description
                subTaskID
                author {
                    id
                    firstName
                    surname
                }
            }
        }
    }
`,eR=(0,n.ZP)`
    query GET_ENGINE_IDS_BY_VESSEL($id: ID!) {
        readBasicComponents(
            filter: {
                parentComponent_Components: {
                    parentComponent: { id: { eq: $id } }
                }
                componentCategory: { eq: Engine }
            }
        ) {
            nodes {
                id
                title
                componentCategory
            }
        }
    }
`,ew=(0,n.ZP)`
    query GetMaintenanceCategories($clientID: ID!) {
        readMaintenanceCategories(filter: { clientID: { eq: $clientID } }) {
            nodes {
                id
                name
                abbreviation
            }
        }
    }
`,eN=(0,n.ZP)`
    query GetLogBookEntriesMemberIds {
        readLogBookEntries(filter: { state: { in: [Editing, Reopened] } }) {
            nodes {
                id
                state
                vehicleID
                logBookEntrySections(
                    filter: {
                        logBookComponentClass: {
                            contains: "CrewMembers_LogBookComponent"
                        }
                    }
                ) {
                    nodes {
                        id
                    }
                }
                vehicle {
                    id
                }
            }
        }
    }
`,eF=(0,n.ZP)`
    query GetCrewMembersFromOpenLogBook($ids: [ID]!) {
        readCrewMembers_LogBookEntrySections(filter: { id: { in: $ids } }) {
            nodes {
                id
                crewMember {
                    id
                    firstName
                    surname
                    archived
                }
            }
        }
    }
`,eO=(0,n.ZP)`
    query GetTripReport_LogBookEntrySections($id: [ID]!) {
        readInfringementNotices(
            filter: { id: { in: $id } }
            sort: { created: ASC }
        ) {
            nodes {
                id
                time
                vesselType
                vesselName
                vesselReg
                ownerFullName
                ownerAddress
                ownerPhone
                ownerEmail
                ownerDOB
                ownerOccupation
                infringementData
                otherDescription
                waterwaysOfficerID
                geoLocationID
                signatureID
                lat
                long
                waterwaysOfficer {
                    id
                    firstName
                    surname
                }
                geoLocation {
                    id
                    title
                    lat
                    long
                }
                signature {
                    id
                    signatureData
                }
            }
        }
    }
`,eA=(0,n.ZP)`
    query Get_LogBookEntryOldConfigs($id: ID!) {
        readLogBookEntryOldConfigss(filter: { logBookEntryID: { eq: $id } }) {
            nodes {
                id
                created
            }
        }
    }
`,eG=(0,n.ZP)`
    query Get_LogBookEntryOldConfigs($id: [ID]!) {
        readOneLogBookEntryOldConfigs(filter: { id: { in: $id } }) {
            lastConfig
            lastEntry
        }
    }
`,eZ=(0,n.ZP)`
    query GET_RECURRING_TASK($id: ID!) {
        readOneMaintenanceSchedule(filter: { id: { eq: $id } }) {
            id
            title
            description
            type
            occursEveryType
            occursEvery
            warnWithinType
            highWarnWithin
            mediumWarnWithin
            lowWarnWithin
            groupTo
            maintenanceChecks {
                nodes {
                    id
                }
            }
            engineUsage {
                nodes {
                    id
                    engine {
                        id
                        title
                        currentHours
                    }
                    lastScheduleHours
                    isScheduled
                }
            }
            inventoryID
            clientID
        }
    }
`,eM=(0,n.ZP)`
    query GetMaintenanceCategories($clientID: ID!) {
        readMaintenanceCategories(filter: { clientID: { eq: $clientID } }) {
            nodes {
                id
                name
                abbreviation
                clientID
                className
                lastEdited
                client {
                    id
                    title
                    phone
                }
                componentMaintenanceCheck {
                    nodes {
                        id
                        name
                    }
                }
            }
        }
    }
`,eW=(0,n.ZP)`
    query GetMaintenanceCategoryByID($id: ID!, $clientID: ID!) {
        readOneMaintenanceCategory(
            filter: { id: { eq: $id }, clientID: { eq: $clientID } }
        ) {
            id
            name
            abbreviation
            clientID
            className
            lastEdited
            client {
                id
                title
                phone
            }
            componentMaintenanceCheck {
                nodes {
                    id
                    name
                }
            }
        }
    }
`,e_=(0,n.ZP)`
    query ReadDepartments(
        $filter: DepartmentFilterFields = {}
        $limit: Int = 100
        $offset: Int = 0
    ) {
        readDepartments(filter: $filter, limit: $limit, offset: $offset) {
            nodes {
                id
                title
                parentID
                parent {
                    id
                    title
                }
                children {
                    nodes {
                        id
                        title
                    }
                }
                basicComponents(filter: { componentCategory: { eq: Vehicle } }) {
                    nodes {
                        id
                        title
                        componentCategory
                    }
                }
                seaLogsMembers {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
            }
        }
    }
`,ex=(0,n.ZP)`
    query ReadOneDepartment($id: ID!) {
        readOneDepartment(filter: { id: { eq: $id } }) {
            id
            title
            parentID
            parent {
                id
                title
            }
            children {
                nodes {
                    id
                    title
                }
            }
            seaLogsMembers {
                nodes {
                    id
                    firstName
                    surname
                }
            }
        }
    }
`;(0,n.ZP)`
    query getSeatimeReport($vesselIds: [ID!]!) {
        readCrewMembers_LogBookEntrySections {
            nodes {
                id
                logBookEntryID
                crewMember {
                    firstName
                    surname
                }
            }
        }
        GetLogBookEntriesForVessels: readLogBookEntries(
            filter: { vehicleID: { in: $vesselIds } }
        ) {
            nodes {
                id
                archived
                masterID
                startDate
                endDate
                fuelLevel
                logBookID
                createdByID
                signOffCommentID
                signOffSignatureID
                clientID
                master {
                    id
                    firstName
                    surname
                }
                signOffComment {
                    id
                    detail
                    comment
                    fieldName
                    commentType
                }
                state
                vehicle {
                    id
                    title
                    seaLogsMembers {
                        nodes {
                            id
                        }
                    }
                }
                logBookEntryLoggers {
                    edges {
                        node {
                            id
                        }
                    }
                    nodes {
                        id
                    }
                    pageInfo {
                        totalCount
                    }
                }
            }
        }
    }
`;let eV=(0,n.ZP)`
    query getVesselListWithDocuments($filter: VesselFilterFields = {}) {
        readVessels(filter: $filter) {
            nodes {
                id
                archived
                title
                registration
                callSign
                showOnDashboard
                documents {
                    nodes {
                        id
                        fileFilename
                        name
                        title
                        created
                    }
                }
            }
        }
    }
`,eH=(0,n.ZP)`
    query getInventoriesListWithDocuments {
        readInventories {
            nodes {
                id
                item
                location
                documents {
                    nodes {
                        id
                        fileFilename
                        name
                        title
                        created
                    }
                }
                description
                title
            }
        }
    }
`,eU=(0,n.ZP)`
    query getMaintenanceListWithDocuments {
        readComponentMaintenanceChecks {
            nodes {
                id
                workOrderNumber
                groupItemTo
                projected
                actual
                difference
                name
                startDate
                documents {
                    nodes {
                        id
                        fileFilename
                        name
                        title
                        created
                    }
                }
            }
        }
    }
`,eJ=(0,n.ZP)`
    query GetVehiclePosition($id: ID!) {
        readOneVehiclePosition(
            filter: { vehicle: { id: { eq: $id } } }
            sort: { created: DESC }
        ) {
            id
            time
            lat
            long
            geoLocation {
                id
                lat
                long
                title
            }
        }
    }
`,eK=(0,n.ZP)`
    query GetFavoriteLocations($userID: ID!) {
        readFavoriteLocations(
            filter: { memberID: { eq: $userID } }
            sort: { usage: DESC }
        ) {
            nodes {
                id
                usage
                geoLocationID
            }
        }
    }
`;(0,n.ZP)`
    query {
        readLogBookEntries {
            nodes {
                id
                archived
                masterID
                startDate
                endDate
                fuelLevel
                logBookID
                createdByID
                signOffCommentID
                signOffSignatureID
                clientID
            }
        }
    }
`;let ej=(0,n.ZP)`
    query ReadOneGeoLocation($id: ID!) {
        readOneGeoLocation(filter: { id: { eq: $id } }) {
            id
            title
            lat
            long
        }
    }
`,ez=(0,n.ZP)`
    query ReadOneEventType_Supernumerary($id: ID!) {
        readOneEventType_Supernumerary(filter: { id: { eq: $id } }) {
            id
            title
            totalGuest
            focGuest
            isBriefed
            briefingTime
            guestList {
                nodes {
                    id
                    firstName
                    surname
                    sectionSignature {
                        id
                        signatureData
                    }
                }
            }
        }
    }
`,eY=(0,n.ZP)`
    query ReadWeatherForecasts($filter: WeatherForecastFilterFields) {
        readWeatherForecasts(filter: $filter) {
            nodes {
                id
                logBookEntryID
                time
                day
                lat
                long
                windSpeed
                windDirection
                swell
                visibility
                precipitation
                pressure
                cloudCover
                comment
                geoLocationID
                geoLocation {
                    id
                    lat
                    long
                    title
                }
            }
        }
    }
`,eX=(0,n.ZP)`
    query ReadWeatherTides($filter: WeatherTideFilterFields) {
        readWeatherTides(filter: $filter) {
            nodes {
                id
                tideDate
                firstHighTideTime
                firstHighTideHeight
                firstLowTideTime
                firstLowTideHeight
                secondHighTideTime
                secondHighTideHeight
                secondLowTideTime
                secondLowTideHeight
                tidalStation
                tidalStationDistance
                lat
                long
                comment
                logBookEntryID
                geoLocationID
                geoLocation {
                    id
                    title
                    lat
                    long
                }
            }
        }
    }
`;(0,n.ZP)`
    query ReadWeatherObservations($filter: WeatherObservationFilterFields) {
        readWeatherObservations(filter: $filter) {
            nodes {
                id
                logBookEntryID
                time
                lat
                long
                windSpeed
                windDirection
                swell
                visibility
                precipitation
                pressure
                cloudCover
                comment
                geoLocationID
                geoLocation {
                    id
                    lat
                    long
                    title
                }
                forecastID
                forecast {
                    id
                    time
                }
            }
        }
    }
`,(0,n.ZP)`
    query ReadOneWeatherObservation($id: ID!) {
        readOneWeatherObservation(filter: { id: { eq: $id } }) {
            id
            logBookEntryID
            time
            lat
            long
            windSpeed
            windDirection
            swell
            visibility
            precipitation
            pressure
            cloudCover
            comment
            geoLocationID
            geoLocation {
                id
                lat
                long
                title
            }
            forecastID
        }
    }
`;let eQ=(0,n.ZP)`
    query ReadOneTripScheduleImport($id: ID!) {
        readOneTripScheduleImport(filter: { id: { eq: $id } }) {
            id
            title
            scheduleType
            isRun
            tripReportSchedules {
                nodes {
                    id
                    title
                }
            }
            vehicles {
                nodes {
                    id
                    title
                }
            }
            R2FileID
            R2File {
                id
                title
            }
        }
    }
`,e0=(0,n.ZP)`
    query ReadTripScheduleImports(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TripScheduleImportFilterFields = {}
    ) {
        readTripScheduleImports(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { title: ASC, id: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                title
                scheduleType
                isRun
                tripReportSchedules {
                    nodes {
                        id
                        title
                    }
                }
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
                R2FileID
                R2File {
                    id
                    title
                }
            }
        }
    }
`,e1=(0,n.ZP)`
    query ReadTripReportSchedules(
        $filter: TripReportScheduleFilterFields
        $limit: Int = 100
        $offset: Int = 0
    ) {
        readTripReportSchedules(
            filter: $filter
            sort: { departTime: ASC }
            limit: $limit
            offset: $offset
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                title
                departTime
                departureBerth
                arriveTime
                arrivalBerth
                start
                end
                fromLocationID
                fromLocation {
                    id
                    title
                    lat
                    long
                }
                toLocationID
                toLocation {
                    id
                    title
                    lat
                    long
                }
                tripScheduleServiceID
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
                tripReportScheduleStops {
                    nodes {
                        id
                        title
                        arriveTime
                        departTime
                        tripReportScheduleID
                        stopLocationID
                    }
                }
                tripReport_LogBookEntrySections {
                    nodes {
                        id
                    }
                }
            }
        }
    }
`,e5=(0,n.ZP)`
    query ReadTripScheduleServices(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TripScheduleServiceFilterFields = {}
    ) {
        readTripScheduleServices(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { title: ASC, id: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                title
                code
                tripReportSchedules {
                    nodes {
                        id
                        title
                    }
                }
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
            }
        }
    }
`,e2=(0,n.ZP)`
    query ReadOneTripScheduleService($id: ID!) {
        readOneTripScheduleService(filter: { id: { eq: $id } }) {
            id
            title
            code
            sharePaxData
            transitID
            publicDescription
            tripReportSchedules {
                nodes {
                    id
                    title
                    departTime
                }
            }
            vehicles {
                nodes {
                    id
                    title
                }
            }
        }
    }
`,e4=(0,n.ZP)`
    query ReadOneTripReportSchedule($id: ID!) {
        readOneTripReportSchedule(filter: { id: { eq: $id } }) {
            id
            archived
            title
            departTime
            departureBerth
            arriveTime
            arrivalBerth
            expectedContactTime
            start
            end
            transitRouteID
            transitTripID
            scheduleType
            fromLocationID
            fromLocation {
                id
                title
                lat
                long
            }
            toLocationID
            toLocation {
                id
                title
                lat
                long
            }
            tripScheduleServiceID
            tripScheduleService {
                id
                title
            }
            vehicles {
                nodes {
                    id
                    title
                }
            }
            tripReportScheduleStops {
                nodes {
                    id
                    title
                    arriveTime
                    departTime
                    tripReportScheduleID
                    stopLocationID
                    stopLocation {
                        id
                        title
                    }
                }
            }
            tripReport_LogBookEntrySections {
                nodes {
                    id
                }
            }
        }
    }
`,e3=(0,n.ZP)`
    query ReadTripReportScheduleStops(
        $filter: TripReportScheduleStopFilterFields
        $limit: Int = 100
        $offset: Int = 0
    ) {
        readTripReportScheduleStops(
            filter: $filter
            limit: $limit
            offset: $offset
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                title
                arriveTime
                departTime
                pickUp
                dropOff
                stopLocationID
                stopLocation {
                    id
                    title
                }
            }
        }
    }
`,e8=(0,n.ZP)`
    query ReadOneTripReportScheduleStop($id: ID!) {
        readOneTripReportScheduleStop(filter: { id: { eq: $id } }) {
            id
            title
            arriveTime
            departTime
            transitID
            pickUp
            dropOff
            tripReportScheduleID
            stopLocationID
        }
    }
`,e6=(0,n.ZP)`
    query readKeyContacts(
        $limit: Int = 100
        $offset: Int = 0
        $filter: KeyContactFilterFields = {}
    ){
        readKeyContacts(limit: $limit, offset: $offset, filter: $filter){
            nodes{
                id
                firstName
                surname
                phone
                cellPhone
                email
                vhfChannel
                address
                companyID
                company{
                    id
                    title
                    phone
                    email
                    address
                }
                attachments{
                    nodes{
                        id
                        title
                    }
                }
            }
        }
    }
`,e7=(0,n.ZP)`
    query readOneKeyContact(
        $filter: KeyContactFilterFields = {}
    ){
        readOneKeyContact(filter: $filter){
            id
            firstName
            surname
            phone
            cellPhone
            email
            vhfChannel
            address
            company{
                id
                title
                phone
                email
                address
            }
            attachments{
                nodes{
                    id
                    title
                    fileFilename
                }
            }
        }
    }
`;(0,n.ZP)`
  query GET_VESSEL_STATUS_HISTORY($filter: VesselStatusFilterFields = {}, $offset: Int = 0, $limit: Int = 100){
    readVesselStatuss(
      sort: { date: ASC },
      filter: $filter,
      limit: $limit
      offset: $offset
    ) {
      pageInfo{
        hasNextPage
        totalCount
      }
      nodes {
          id
          date
          status
          comment
          created
          reason
          otherReason
          expectedReturn
          vesselID
      }
    }
  }
`},88570:(e,i,t)=>{t.r(i),t.d(i,{default:()=>o});var n=t(3563);let o=e=>[{type:"image/x-icon",sizes:"152x152",url:(0,n.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};