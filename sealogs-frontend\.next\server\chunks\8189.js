exports.id=8189,exports.ids=[8189],exports.modules={73084:e=>{!function(){"use strict";var t="undefined"!=typeof window&&void 0!==window.document?window.document:{},r=e.exports,n=function(){for(var e,r=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],n=0,i=r.length,o={};n<i;n++)if((e=r[n])&&e[1]in t){for(n=0;n<e.length;n++)o[r[0][n]]=e[n];return o}return!1}(),i={change:n.fullscreenchange,error:n.fullscreenerror},o={request:function(e,r){return new Promise((function(i,o){var l=(function(){this.off("change",l),i()}).bind(this);this.on("change",l);var a=(e=e||t.documentElement)[n.requestFullscreen](r);a instanceof Promise&&a.then(l).catch(o)}).bind(this))},exit:function(){return new Promise((function(e,r){if(!this.isFullscreen){e();return}var i=(function(){this.off("change",i),e()}).bind(this);this.on("change",i);var o=t[n.exitFullscreen]();o instanceof Promise&&o.then(i).catch(r)}).bind(this))},toggle:function(e,t){return this.isFullscreen?this.exit():this.request(e,t)},onchange:function(e){this.on("change",e)},onerror:function(e){this.on("error",e)},on:function(e,r){var n=i[e];n&&t.addEventListener(n,r,!1)},off:function(e,r){var n=i[e];n&&t.removeEventListener(n,r,!1)},raw:n};if(!n){r?e.exports={isEnabled:!1}:window.screenfull={isEnabled:!1};return}Object.defineProperties(o,{isFullscreen:{get:function(){return!!t[n.fullscreenElement]}},element:{enumerable:!0,get:function(){return t[n.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return!!t[n.fullscreenEnabled]}}}),r?e.exports=o:window.screenfull=o}()},84947:(e,t,r)=>{"use strict";var n=r(60343),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=n.useState,l=n.useEffect,a=n.useLayoutEffect,s=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var f="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=o({inst:{value:r,getSnapshot:t}}),i=n[0].inst,f=n[1];return a(function(){i.value=r,i.getSnapshot=t,u(i)&&f({inst:i})},[e,r,t]),l(function(){return u(i)&&f({inst:i}),e(function(){u(i)&&f({inst:i})})},[e]),s(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:f},37284:(e,t,r)=>{"use strict";e.exports=r(84947)},82542:(e,t,r)=>{"use strict";r.d(t,{x7:()=>e_,Me:()=>ew,oo:()=>eC,RR:()=>eE,Cp:()=>eF,dr:()=>eV,cv:()=>ex,uY:()=>eS,dp:()=>eA});let n=["top","right","bottom","left"],i=Math.min,o=Math.max,l=Math.round,a=Math.floor,s=e=>({x:e,y:e}),u={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function c(e,t){return"function"==typeof e?e(t):e}function d(e){return e.split("-")[0]}function p(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function h(e){return"y"===e?"height":"width"}let y=new Set(["top","bottom"]);function g(e){return y.has(d(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>f[e])}let b=["left","right"],w=["right","left"],x=["top","bottom"],S=["bottom","top"];function E(e){return e.replace(/left|right|bottom|top/g,e=>u[e])}function A(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function F(e){let{x:t,y:r,width:n,height:i}=e;return{width:n,height:i,top:r,left:t,right:t+n,bottom:r+i,x:t,y:r}}function _(e,t,r){let n,{reference:i,floating:o}=e,l=g(t),a=m(g(t)),s=h(a),u=d(t),f="y"===l,c=i.x+i.width/2-o.width/2,y=i.y+i.height/2-o.height/2,v=i[s]/2-o[s]/2;switch(u){case"top":n={x:c,y:i.y-o.height};break;case"bottom":n={x:c,y:i.y+i.height};break;case"right":n={x:i.x+i.width,y:y};break;case"left":n={x:i.x-o.width,y:y};break;default:n={x:i.x,y:i.y}}switch(p(t)){case"start":n[a]-=v*(r&&f?-1:1);break;case"end":n[a]+=v*(r&&f?-1:1)}return n}let V=async(e,t,r)=>{let{placement:n="bottom",strategy:i="absolute",middleware:o=[],platform:l}=r,a=o.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:f,y:c}=_(u,n,s),d=n,p={},m=0;for(let r=0;r<a.length;r++){let{name:o,fn:h}=a[r],{x:y,y:g,data:v,reset:b}=await h({x:f,y:c,initialPlacement:n,placement:d,strategy:i,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});f=null!=y?y:f,c=null!=g?g:c,p={...p,[o]:{...p[o],...v}},b&&m<=50&&(m++,"object"==typeof b&&(b.placement&&(d=b.placement),b.rects&&(u=!0===b.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:f,y:c}=_(u,d,s)),r=-1)}return{x:f,y:c,placement:d,strategy:i,middlewareData:p}};async function C(e,t){var r;void 0===t&&(t={});let{x:n,y:i,platform:o,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=c(t,e),h=A(m),y=a[p?"floating"===d?"reference":"floating":d],g=F(await o.getClippingRect({element:null==(r=await (null==o.isElement?void 0:o.isElement(y)))||r?y:y.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:f,strategy:s})),v="floating"===d?{x:n,y:i,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),w=await (null==o.isElement?void 0:o.isElement(b))&&await (null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},x=F(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:b,strategy:s}):v);return{top:(g.top-x.top+h.top)/w.y,bottom:(x.bottom-g.bottom+h.bottom)/w.y,left:(g.left-x.left+h.left)/w.x,right:(x.right-g.right+h.right)/w.x}}function k(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return n.some(t=>e[t]>=0)}let R=new Set(["left","top"]);async function D(e,t){let{placement:r,platform:n,elements:i}=e,o=await (null==n.isRTL?void 0:n.isRTL(i.floating)),l=d(r),a=p(r),s="y"===g(r),u=R.has(l)?-1:1,f=o&&s?-1:1,m=c(t,e),{mainAxis:h,crossAxis:y,alignmentAxis:v}="number"==typeof m?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return a&&"number"==typeof v&&(y="end"===a?-1*v:v),s?{x:y*f,y:h*u}:{x:h*u,y:y*f}}function L(){return"undefined"!=typeof window}function O(e){return M(e)?(e.nodeName||"").toLowerCase():"#document"}function N(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function j(e){var t;return null==(t=(M(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function M(e){return!!L()&&(e instanceof Node||e instanceof N(e).Node)}function P(e){return!!L()&&(e instanceof Element||e instanceof N(e).Element)}function B(e){return!!L()&&(e instanceof HTMLElement||e instanceof N(e).HTMLElement)}function I(e){return!!L()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof N(e).ShadowRoot)}let H=new Set(["inline","contents"]);function U(e){let{overflow:t,overflowX:r,overflowY:n,display:i}=Q(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!H.has(i)}let W=new Set(["table","td","th"]),z=[":popover-open",":modal"];function $(e){return z.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let q=["transform","translate","scale","rotate","perspective"],Y=["transform","translate","scale","rotate","perspective","filter"],X=["paint","layout","strict","content"];function Z(e){let t=G(),r=P(e)?Q(e):e;return q.some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||Y.some(e=>(r.willChange||"").includes(e))||X.some(e=>(r.contain||"").includes(e))}function G(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let J=new Set(["html","body","#document"]);function K(e){return J.has(O(e))}function Q(e){return N(e).getComputedStyle(e)}function ee(e){return P(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function et(e){if("html"===O(e))return e;let t=e.assignedSlot||e.parentNode||I(e)&&e.host||j(e);return I(t)?t.host:t}function er(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let i=function e(t){let r=et(t);return K(r)?t.ownerDocument?t.ownerDocument.body:t.body:B(r)&&U(r)?r:e(r)}(e),o=i===(null==(n=e.ownerDocument)?void 0:n.body),l=N(i);if(o){let e=en(l);return t.concat(l,l.visualViewport||[],U(i)?i:[],e&&r?er(e):[])}return t.concat(i,er(i,[],r))}function en(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=Q(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,i=B(e),o=i?e.offsetWidth:r,a=i?e.offsetHeight:n,s=l(r)!==o||l(n)!==a;return s&&(r=o,n=a),{width:r,height:n,$:s}}function eo(e){return P(e)?e:e.contextElement}function el(e){let t=eo(e);if(!B(t))return s(1);let r=t.getBoundingClientRect(),{width:n,height:i,$:o}=ei(t),a=(o?l(r.width):r.width)/n,u=(o?l(r.height):r.height)/i;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let ea=s(0);function es(e){let t=N(e);return G()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ea}function eu(e,t,r,n){var i;void 0===t&&(t=!1),void 0===r&&(r=!1);let o=e.getBoundingClientRect(),l=eo(e),a=s(1);t&&(n?P(n)&&(a=el(n)):a=el(e));let u=(void 0===(i=r)&&(i=!1),n&&(!i||n===N(l))&&i)?es(l):s(0),f=(o.left+u.x)/a.x,c=(o.top+u.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=N(l),t=n&&P(n)?N(n):n,r=e,i=en(r);for(;i&&n&&t!==r;){let e=el(i),t=i.getBoundingClientRect(),n=Q(i),o=t.left+(i.clientLeft+parseFloat(n.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(n.paddingTop))*e.y;f*=e.x,c*=e.y,d*=e.x,p*=e.y,f+=o,c+=l,i=en(r=N(i))}}return F({width:d,height:p,x:f,y:c})}function ef(e,t){let r=ee(e).scrollLeft;return t?t.left+r:eu(j(e)).left+r}function ec(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:ef(e,n)),y:n.top+t.scrollTop}}let ed=new Set(["absolute","fixed"]);function ep(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=N(e),n=j(e),i=r.visualViewport,o=n.clientWidth,l=n.clientHeight,a=0,s=0;if(i){o=i.width,l=i.height;let e=G();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,s=i.offsetTop)}return{width:o,height:l,x:a,y:s}}(e,r);else if("document"===t)n=function(e){let t=j(e),r=ee(e),n=e.ownerDocument.body,i=o(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),l=o(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),a=-r.scrollLeft+ef(e),s=-r.scrollTop;return"rtl"===Q(n).direction&&(a+=o(t.clientWidth,n.clientWidth)-i),{width:i,height:l,x:a,y:s}}(j(e));else if(P(t))n=function(e,t){let r=eu(e,!0,"fixed"===t),n=r.top+e.clientTop,i=r.left+e.clientLeft,o=B(e)?el(e):s(1),l=e.clientWidth*o.x;return{width:l,height:e.clientHeight*o.y,x:i*o.x,y:n*o.y}}(t,r);else{let r=es(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return F(n)}function em(e){return"static"===Q(e).position}function eh(e,t){if(!B(e)||"fixed"===Q(e).position)return null;if(t)return t(e);let r=e.offsetParent;return j(e)===r&&(r=r.ownerDocument.body),r}function ey(e,t){var r;let n=N(e);if($(e))return n;if(!B(e)){let t=et(e);for(;t&&!K(t);){if(P(t)&&!em(t))return t;t=et(t)}return n}let i=eh(e,t);for(;i&&(r=i,W.has(O(r)))&&em(i);)i=eh(i,t);return i&&K(i)&&em(i)&&!Z(i)?n:i||function(e){let t=et(e);for(;B(t)&&!K(t);){if(Z(t))return t;if($(t))break;t=et(t)}return null}(e)||n}let eg=async function(e){let t=this.getOffsetParent||ey,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=B(t),i=j(t),o="fixed"===r,l=eu(e,!0,o,t),a={scrollLeft:0,scrollTop:0},u=s(0);if(n||!n&&!o){if(("body"!==O(t)||U(i))&&(a=ee(t)),n){let e=eu(t,!0,o,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else i&&(u.x=ef(i))}o&&!n&&i&&(u.x=ef(i));let f=!i||n||o?s(0):ec(i,a);return{x:l.left+a.scrollLeft-u.x-f.x,y:l.top+a.scrollTop-u.y-f.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},ev={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:i}=e,o="fixed"===i,l=j(n),a=!!t&&$(t.floating);if(n===l||a&&o)return r;let u={scrollLeft:0,scrollTop:0},f=s(1),c=s(0),d=B(n);if((d||!d&&!o)&&(("body"!==O(n)||U(l))&&(u=ee(n)),B(n))){let e=eu(n);f=el(n),c.x=e.x+n.clientLeft,c.y=e.y+n.clientTop}let p=!l||d||o?s(0):ec(l,u,!0);return{width:r.width*f.x,height:r.height*f.y,x:r.x*f.x-u.scrollLeft*f.x+c.x+p.x,y:r.y*f.y-u.scrollTop*f.y+c.y+p.y}},getDocumentElement:j,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:l}=e,a=[..."clippingAncestors"===r?$(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=er(e,[],!1).filter(e=>P(e)&&"body"!==O(e)),i=null,o="fixed"===Q(e).position,l=o?et(e):e;for(;P(l)&&!K(l);){let t=Q(l),r=Z(l);r||"fixed"!==t.position||(i=null),(o?!r&&!i:!r&&"static"===t.position&&!!i&&ed.has(i.position)||U(l)&&!r&&function e(t,r){let n=et(t);return!(n===r||!P(n)||K(n))&&("fixed"===Q(n).position||e(n,r))}(e,l))?n=n.filter(e=>e!==l):i=t,l=et(l)}return t.set(e,n),n}(t,this._c):[].concat(r),n],s=a[0],u=a.reduce((e,r)=>{let n=ep(t,r,l);return e.top=o(n.top,e.top),e.right=i(n.right,e.right),e.bottom=i(n.bottom,e.bottom),e.left=o(n.left,e.left),e},ep(t,s,l));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:ey,getElementRects:eg,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=ei(e);return{width:t,height:r}},getScale:el,isElement:P,isRTL:function(e){return"rtl"===Q(e).direction}};function eb(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function ew(e,t,r,n){let l;void 0===n&&(n={});let{ancestorScroll:s=!0,ancestorResize:u=!0,elementResize:f="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=n,p=eo(e),m=s||u?[...p?er(p):[],...er(t)]:[];m.forEach(e=>{s&&e.addEventListener("scroll",r,{passive:!0}),u&&e.addEventListener("resize",r)});let h=p&&c?function(e,t){let r,n=null,l=j(e);function s(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function u(f,c){void 0===f&&(f=!1),void 0===c&&(c=1),s();let d=e.getBoundingClientRect(),{left:p,top:m,width:h,height:y}=d;if(f||t(),!h||!y)return;let g=a(m),v=a(l.clientWidth-(p+h)),b={rootMargin:-g+"px "+-v+"px "+-a(l.clientHeight-(m+y))+"px "+-a(p)+"px",threshold:o(0,i(1,c))||1},w=!0;function x(t){let n=t[0].intersectionRatio;if(n!==c){if(!w)return u();n?u(!1,n):r=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==n||eb(d,e.getBoundingClientRect())||u(),w=!1}try{n=new IntersectionObserver(x,{...b,root:l.ownerDocument})}catch(e){n=new IntersectionObserver(x,b)}n.observe(e)}(!0),s}(p,r):null,y=-1,g=null;f&&(g=new ResizeObserver(e=>{let[n]=e;n&&n.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(y),y=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),r()}),p&&!d&&g.observe(p),g.observe(t));let v=d?eu(e):null;return d&&function t(){let n=eu(e);v&&!eb(v,n)&&r(),v=n,l=requestAnimationFrame(t)}(),r(),()=>{var e;m.forEach(e=>{s&&e.removeEventListener("scroll",r),u&&e.removeEventListener("resize",r)}),null==h||h(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(l)}}let ex=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:i,y:o,placement:l,middlewareData:a}=t,s=await D(t,e);return l===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:i+s.x,y:o+s.y,data:{...s,placement:l}}}}},eS=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:l}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...f}=c(e,t),p={x:r,y:n},h=await C(t,f),y=g(d(l)),v=m(y),b=p[v],w=p[y];if(a){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",r=b+h[e],n=b-h[t];b=o(r,i(b,n))}if(s){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",r=w+h[e],n=w-h[t];w=o(r,i(w,n))}let x=u.fn({...t,[v]:b,[y]:w});return{...x,data:{x:x.x-r,y:x.y-n,enabled:{[v]:a,[y]:s}}}}}},eE=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,i,o,l;let{placement:a,middlewareData:s,rects:u,initialPlacement:f,platform:y,elements:A}=t,{mainAxis:F=!0,crossAxis:_=!0,fallbackPlacements:V,fallbackStrategy:k="bestFit",fallbackAxisSideDirection:T="none",flipAlignment:R=!0,...D}=c(e,t);if(null!=(r=s.arrow)&&r.alignmentOffset)return{};let L=d(a),O=g(f),N=d(f)===f,j=await (null==y.isRTL?void 0:y.isRTL(A.floating)),M=V||(N||!R?[E(f)]:function(e){let t=E(e);return[v(e),t,v(t)]}(f)),P="none"!==T;!V&&P&&M.push(...function(e,t,r,n){let i=p(e),o=function(e,t,r){switch(e){case"top":case"bottom":if(r)return t?w:b;return t?b:w;case"left":case"right":return t?x:S;default:return[]}}(d(e),"start"===r,n);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(v)))),o}(f,R,T,j));let B=[f,...M],I=await C(t,D),H=[],U=(null==(n=s.flip)?void 0:n.overflows)||[];if(F&&H.push(I[L]),_){let e=function(e,t,r){void 0===r&&(r=!1);let n=p(e),i=m(g(e)),o=h(i),l="x"===i?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=E(l)),[l,E(l)]}(a,u,j);H.push(I[e[0]],I[e[1]])}if(U=[...U,{placement:a,overflows:H}],!H.every(e=>e<=0)){let e=((null==(i=s.flip)?void 0:i.index)||0)+1,t=B[e];if(t&&(!("alignment"===_&&O!==g(t))||U.every(e=>e.overflows[0]>0&&g(e.placement)===O)))return{data:{index:e,overflows:U},reset:{placement:t}};let r=null==(o=U.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!r)switch(k){case"bestFit":{let e=null==(l=U.filter(e=>{if(P){let t=g(e.placement);return t===O||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(r=e);break}case"initialPlacement":r=f}if(a!==r)return{reset:{placement:r}}}return{}}}},eA=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let l,a;let{placement:s,rects:u,platform:f,elements:m}=t,{apply:h=()=>{},...y}=c(e,t),v=await C(t,y),b=d(s),w=p(s),x="y"===g(s),{width:S,height:E}=u.floating;"top"===b||"bottom"===b?(l=b,a=w===(await (null==f.isRTL?void 0:f.isRTL(m.floating))?"start":"end")?"left":"right"):(a=b,l="end"===w?"top":"bottom");let A=E-v.top-v.bottom,F=S-v.left-v.right,_=i(E-v[l],A),V=i(S-v[a],F),k=!t.middlewareData.shift,T=_,R=V;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(R=F),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(T=A),k&&!w){let e=o(v.left,0),t=o(v.right,0),r=o(v.top,0),n=o(v.bottom,0);x?R=S-2*(0!==e||0!==t?e+t:o(v.left,v.right)):T=E-2*(0!==r||0!==n?r+n:o(v.top,v.bottom))}await h({...t,availableWidth:R,availableHeight:T});let D=await f.getDimensions(m.floating);return S!==D.width||E!==D.height?{reset:{rects:!0}}:{}}}},eF=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...i}=c(e,t);switch(n){case"referenceHidden":{let e=k(await C(t,{...i,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{let e=k(await C(t,{...i,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}},e_=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:l,rects:a,platform:s,elements:u,middlewareData:f}=t,{element:d,padding:y=0}=c(e,t)||{};if(null==d)return{};let v=A(y),b={x:r,y:n},w=m(g(l)),x=h(w),S=await s.getDimensions(d),E="y"===w,F=E?"clientHeight":"clientWidth",_=a.reference[x]+a.reference[w]-b[w]-a.floating[x],V=b[w]-a.reference[w],C=await (null==s.getOffsetParent?void 0:s.getOffsetParent(d)),k=C?C[F]:0;k&&await (null==s.isElement?void 0:s.isElement(C))||(k=u.floating[F]||a.floating[x]);let T=k/2-S[x]/2-1,R=i(v[E?"top":"left"],T),D=i(v[E?"bottom":"right"],T),L=k-S[x]-D,O=k/2-S[x]/2+(_/2-V/2),N=o(R,i(O,L)),j=!f.arrow&&null!=p(l)&&O!==N&&a.reference[x]/2-(O<R?R:D)-S[x]/2<0,M=j?O<R?O-R:O-L:0;return{[w]:b[w]+M,data:{[w]:N,centerOffset:O-N-M,...j&&{alignmentOffset:M}},reset:j}}}),eV=function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=c(e,t),f={x:r,y:n},p=g(i),h=m(p),y=f[h],v=f[p],b=c(a,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(s){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+w.mainAxis,r=o.reference[h]+o.reference[e]-w.mainAxis;y<t?y=t:y>r&&(y=r)}if(u){var x,S;let e="y"===h?"width":"height",t=R.has(d(i)),r=o.reference[p]-o.floating[e]+(t&&(null==(x=l.offset)?void 0:x[p])||0)+(t?0:w.crossAxis),n=o.reference[p]+o.reference[e]+(t?0:(null==(S=l.offset)?void 0:S[p])||0)-(t?w.crossAxis:0);v<r?v=r:v>n&&(v=n)}return{[h]:y,[p]:v}}}},eC=(e,t,r)=>{let n=new Map,i={platform:ev,...r},o={...i.platform,_c:n};return V(e,t,{...i,platform:o})}},36183:(e,t,r)=>{"use strict";r.d(t,{M:()=>s});var n,i=r(60343),o=r(32183),l=(n||(n=r.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),a=0;function s(e){let[t,r]=i.useState(l());return(0,o.b)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},36601:(e,t,r)=>{"use strict";r.d(t,{f:()=>a});var n=r(60343),i=r(54928),o=r(98768),l=n.forwardRef((e,t)=>(0,o.jsx)(i.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},18251:(e,t,r)=>{"use strict";r.d(t,{ee:()=>q,Eh:()=>X,VY:()=>Y,fC:()=>$,D7:()=>k});var n=r(60343),i=r(82542),o=r(61222),l="undefined"!=typeof document?n.useLayoutEffect:function(){};function a(e,t){let r,n,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!a(e[n],t[n]))return!1;return!0}if((r=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,i[n]))return!1;for(n=r;0!=n--;){let r=i[n];if(("_owner"!==r||!e.$$typeof)&&!a(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function s(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function u(e,t){let r=s(e);return Math.round(t*r)/r}function f(e){let t=n.useRef(e);return l(()=>{t.current=e}),t}let c=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?(0,i.x7)({element:r.current,padding:n}).fn(t):{}:r?(0,i.x7)({element:r,padding:n}).fn(t):{}}}),d=(e,t)=>({...(0,i.cv)(e),options:[e,t]}),p=(e,t)=>({...(0,i.uY)(e),options:[e,t]}),m=(e,t)=>({...(0,i.dr)(e),options:[e,t]}),h=(e,t)=>({...(0,i.RR)(e),options:[e,t]}),y=(e,t)=>({...(0,i.dp)(e),options:[e,t]}),g=(e,t)=>({...(0,i.Cp)(e),options:[e,t]}),v=(e,t)=>({...c(e),options:[e,t]});var b=r(54928),w=r(98768),x=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:i=5,...o}=e;return(0,w.jsx)(b.WV.svg,{...o,ref:t,width:n,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,w.jsx)("polygon",{points:"0,0 30,0 15,10"})})});x.displayName="Arrow";var S=r(48367),E=r(13295),A=r(20772),F=r(32183),_=r(22595),V="Popper",[C,k]=(0,E.b)(V),[T,R]=C(V),D=e=>{let{__scopePopper:t,children:r}=e,[i,o]=n.useState(null);return(0,w.jsx)(T,{scope:t,anchor:i,onAnchorChange:o,children:r})};D.displayName=V;var L="PopperAnchor",O=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:i,...o}=e,l=R(L,r),a=n.useRef(null),s=(0,S.e)(t,a);return n.useEffect(()=>{l.onAnchorChange(i?.current||a.current)}),i?null:(0,w.jsx)(b.WV.div,{...o,ref:s})});O.displayName=L;var N="PopperContent",[j,M]=C(N),P=n.forwardRef((e,t)=>{let{__scopePopper:r,side:c="bottom",sideOffset:x=0,align:E="center",alignOffset:V=0,arrowPadding:C=0,avoidCollisions:k=!0,collisionBoundary:T=[],collisionPadding:D=0,sticky:L="partial",hideWhenDetached:O=!1,updatePositionStrategy:M="optimized",onPlaced:P,...B}=e,I=R(N,r),[H,$]=n.useState(null),q=(0,S.e)(t,e=>$(e)),[Y,X]=n.useState(null),Z=(0,_.t)(Y),G=Z?.width??0,J=Z?.height??0,K="number"==typeof D?D:{top:0,right:0,bottom:0,left:0,...D},Q=Array.isArray(T)?T:[T],ee=Q.length>0,et={padding:K,boundary:Q.filter(U),altBoundary:ee},{refs:er,floatingStyles:en,placement:ei,isPositioned:eo,middlewareData:el}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:c=[],platform:d,elements:{reference:p,floating:m}={},transform:h=!0,whileElementsMounted:y,open:g}=e,[v,b]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[w,x]=n.useState(c);a(w,c)||x(c);let[S,E]=n.useState(null),[A,F]=n.useState(null),_=n.useCallback(e=>{e!==T.current&&(T.current=e,E(e))},[]),V=n.useCallback(e=>{e!==R.current&&(R.current=e,F(e))},[]),C=p||S,k=m||A,T=n.useRef(null),R=n.useRef(null),D=n.useRef(v),L=null!=y,O=f(y),N=f(d),j=f(g),M=n.useCallback(()=>{if(!T.current||!R.current)return;let e={placement:t,strategy:r,middleware:w};N.current&&(e.platform=N.current),(0,i.oo)(T.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==j.current};P.current&&!a(D.current,t)&&(D.current=t,o.flushSync(()=>{b(t)}))})},[w,t,r,N,j]);l(()=>{!1===g&&D.current.isPositioned&&(D.current.isPositioned=!1,b(e=>({...e,isPositioned:!1})))},[g]);let P=n.useRef(!1);l(()=>(P.current=!0,()=>{P.current=!1}),[]),l(()=>{if(C&&(T.current=C),k&&(R.current=k),C&&k){if(O.current)return O.current(C,k,M);M()}},[C,k,M,O,L]);let B=n.useMemo(()=>({reference:T,floating:R,setReference:_,setFloating:V}),[_,V]),I=n.useMemo(()=>({reference:C,floating:k}),[C,k]),H=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!I.floating)return e;let t=u(I.floating,v.x),n=u(I.floating,v.y);return h?{...e,transform:"translate("+t+"px, "+n+"px)",...s(I.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,h,I.floating,v.x,v.y]);return n.useMemo(()=>({...v,update:M,refs:B,elements:I,floatingStyles:H}),[v,M,B,I,H])}({strategy:"fixed",placement:c+("center"!==E?"-"+E:""),whileElementsMounted:(...e)=>(0,i.Me)(...e,{animationFrame:"always"===M}),elements:{reference:I.anchor},middleware:[d({mainAxis:x+J,alignmentAxis:V}),k&&p({mainAxis:!0,crossAxis:!1,limiter:"partial"===L?m():void 0,...et}),k&&h({...et}),y({...et,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:i,height:o}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${r}px`),l.setProperty("--radix-popper-available-height",`${n}px`),l.setProperty("--radix-popper-anchor-width",`${i}px`),l.setProperty("--radix-popper-anchor-height",`${o}px`)}}),Y&&v({element:Y,padding:C}),W({arrowWidth:G,arrowHeight:J}),O&&g({strategy:"referenceHidden",...et})]}),[ea,es]=z(ei),eu=(0,A.W)(P);(0,F.b)(()=>{eo&&eu?.()},[eo,eu]);let ef=el.arrow?.x,ec=el.arrow?.y,ed=el.arrow?.centerOffset!==0,[ep,em]=n.useState();return(0,F.b)(()=>{H&&em(window.getComputedStyle(H).zIndex)},[H]),(0,w.jsx)("div",{ref:er.setFloating,"data-radix-popper-content-wrapper":"",style:{...en,transform:eo?en.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ep,"--radix-popper-transform-origin":[el.transformOrigin?.x,el.transformOrigin?.y].join(" "),...el.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,w.jsx)(j,{scope:r,placedSide:ea,onArrowChange:X,arrowX:ef,arrowY:ec,shouldHideArrow:ed,children:(0,w.jsx)(b.WV.div,{"data-side":ea,"data-align":es,...B,ref:q,style:{...B.style,animation:eo?void 0:"none"}})})})});P.displayName=N;var B="PopperArrow",I={top:"bottom",right:"left",bottom:"top",left:"right"},H=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,i=M(B,r),o=I[i.placedSide];return(0,w.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,w.jsx)(x,{...n,ref:t,style:{...n.style,display:"block"}})})});function U(e){return null!==e}H.displayName=B;var W=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:i}=t,o=i.arrow?.centerOffset!==0,l=o?0:e.arrowWidth,a=o?0:e.arrowHeight,[s,u]=z(r),f={start:"0%",center:"50%",end:"100%"}[u],c=(i.arrow?.x??0)+l/2,d=(i.arrow?.y??0)+a/2,p="",m="";return"bottom"===s?(p=o?f:`${c}px`,m=`${-a}px`):"top"===s?(p=o?f:`${c}px`,m=`${n.floating.height+a}px`):"right"===s?(p=`${-a}px`,m=o?f:`${d}px`):"left"===s&&(p=`${n.floating.width+a}px`,m=o?f:`${d}px`),{data:{x:p,y:m}}}});function z(e){let[t,r="center"]=e.split("-");return[t,r]}var $=D,q=O,Y=P,X=H},146:(e,t,r)=>{"use strict";r.d(t,{VY:()=>Y,fC:()=>z,h_:()=>q,xz:()=>$,zt:()=>W});var n=r(60343),i=r(70112),o=r(48367),l=r(13295),a=r(97938),s=r(36183),u=r(18251),f=r(54054),c=r(26983),d=r(54928),p=r(73404),m=r(21110),h=r(50750),y=r(98768),[g,v]=(0,l.b)("Tooltip",[u.D7]),b=(0,u.D7)(),w="TooltipProvider",x="tooltip.open",[S,E]=g(w),A=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:l}=e,a=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,y.jsx)(S,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,i)},[i]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:o,children:l})};A.displayName=w;var F="Tooltip",[_,V]=g(F),C=e=>{let{__scopeTooltip:t,children:r,open:i,defaultOpen:o,onOpenChange:l,disableHoverableContent:a,delayDuration:f}=e,c=E(F,e.__scopeTooltip),d=b(t),[p,h]=n.useState(null),g=(0,s.M)(),v=n.useRef(0),w=a??c.disableHoverableContent,S=f??c.delayDuration,A=n.useRef(!1),[V,C]=(0,m.T)({prop:i,defaultProp:o??!1,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(x))):c.onClose(),l?.(e)},caller:F}),k=n.useMemo(()=>V?A.current?"delayed-open":"instant-open":"closed",[V]),T=n.useCallback(()=>{window.clearTimeout(v.current),v.current=0,A.current=!1,C(!0)},[C]),R=n.useCallback(()=>{window.clearTimeout(v.current),v.current=0,C(!1)},[C]),D=n.useCallback(()=>{window.clearTimeout(v.current),v.current=window.setTimeout(()=>{A.current=!0,C(!0),v.current=0},S)},[S,C]);return n.useEffect(()=>()=>{v.current&&(window.clearTimeout(v.current),v.current=0)},[]),(0,y.jsx)(u.fC,{...d,children:(0,y.jsx)(_,{scope:t,contentId:g,open:V,stateAttribute:k,trigger:p,onTriggerChange:h,onTriggerEnter:n.useCallback(()=>{c.isOpenDelayedRef.current?D():T()},[c.isOpenDelayedRef,D,T]),onTriggerLeave:n.useCallback(()=>{w?R():(window.clearTimeout(v.current),v.current=0)},[R,w]),onOpen:T,onClose:R,disableHoverableContent:w,children:r})})};C.displayName=F;var k="TooltipTrigger",T=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...l}=e,a=V(k,r),s=E(k,r),f=b(r),c=n.useRef(null),p=(0,o.e)(t,c,a.onTriggerChange),m=n.useRef(!1),h=n.useRef(!1),g=n.useCallback(()=>m.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,y.jsx)(u.ee,{asChild:!0,...f,children:(0,y.jsx)(d.WV.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...l,ref:p,onPointerMove:(0,i.M)(e.onPointerMove,e=>{"touch"===e.pointerType||h.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),h.current=!0)}),onPointerLeave:(0,i.M)(e.onPointerLeave,()=>{a.onTriggerLeave(),h.current=!1}),onPointerDown:(0,i.M)(e.onPointerDown,()=>{a.open&&a.onClose(),m.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,i.M)(e.onFocus,()=>{m.current||a.onOpen()}),onBlur:(0,i.M)(e.onBlur,a.onClose),onClick:(0,i.M)(e.onClick,a.onClose)})})});T.displayName=k;var R="TooltipPortal",[D,L]=g(R,{forceMount:void 0}),O=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:i}=e,o=V(R,t);return(0,y.jsx)(D,{scope:t,forceMount:r,children:(0,y.jsx)(c.z,{present:r||o.open,children:(0,y.jsx)(f.h,{asChild:!0,container:i,children:n})})})};O.displayName=R;var N="TooltipContent",j=n.forwardRef((e,t)=>{let r=L(N,e.__scopeTooltip),{forceMount:n=r.forceMount,side:i="top",...o}=e,l=V(N,e.__scopeTooltip);return(0,y.jsx)(c.z,{present:n||l.open,children:l.disableHoverableContent?(0,y.jsx)(H,{side:i,...o,ref:t}):(0,y.jsx)(M,{side:i,...o,ref:t})})}),M=n.forwardRef((e,t)=>{let r=V(N,e.__scopeTooltip),i=E(N,e.__scopeTooltip),l=n.useRef(null),a=(0,o.e)(t,l),[s,u]=n.useState(null),{trigger:f,onClose:c}=r,d=l.current,{onPointerInTransitChange:p}=i,m=n.useCallback(()=>{u(null),p(!1)},[p]),h=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},i=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(r,n,i,o)){case o:return"left";case i:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,i),...function(e){let{top:t,right:r,bottom:n,left:i}=e;return[{x:i,y:t},{x:r,y:t},{x:r,y:n},{x:i,y:n}]}(t.getBoundingClientRect())])),p(!0)},[p]);return n.useEffect(()=>()=>m(),[m]),n.useEffect(()=>{if(f&&d){let e=e=>h(e,d),t=e=>h(e,f);return f.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{f.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[f,d,h,m]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=f?.contains(t)||d?.contains(t),i=!function(e,t){let{x:r,y:n}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let l=t[e],a=t[o],s=l.x,u=l.y,f=a.x,c=a.y;u>n!=c>n&&r<(f-s)*(n-u)/(c-u)+s&&(i=!i)}return i}(r,s);n?m():i&&(m(),c())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[f,d,s,c,m]),(0,y.jsx)(H,{...e,ref:a})}),[P,B]=g(F,{isInside:!1}),I=(0,p.sA)("TooltipContent"),H=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:i,"aria-label":o,onEscapeKeyDown:l,onPointerDownOutside:s,...f}=e,c=V(N,r),d=b(r),{onClose:p}=c;return n.useEffect(()=>(document.addEventListener(x,p),()=>document.removeEventListener(x,p)),[p]),n.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;t?.contains(c.trigger)&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,p]),(0,y.jsx)(a.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,y.jsxs)(u.VY,{"data-state":c.stateAttribute,...d,...f,ref:t,style:{...f.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,y.jsx)(I,{children:i}),(0,y.jsx)(P,{scope:r,isInside:!0,children:(0,y.jsx)(h.fC,{id:c.contentId,role:"tooltip",children:o||i})})]})})});j.displayName=N;var U="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,i=b(r);return B(U,r).isInside?null:(0,y.jsx)(u.Eh,{...i,...n,ref:t})}).displayName=U;var W=A,z=C,$=T,q=O,Y=j},22595:(e,t,r)=>{"use strict";r.d(t,{t:()=>o});var n=r(60343),i=r(32183);function o(e){let[t,r]=n.useState(void 0);return(0,i.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,i=t.blockSize}else n=e.offsetWidth,i=e.offsetHeight;r({width:n,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},96268:(e,t,r)=>{"use strict";r.d(t,{DI:()=>B,ac:()=>U,rN:()=>G,sd:()=>Z});var n,i,o=r(60343);let l=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};var a="object"==typeof global&&global&&global.Object===Object&&global,s="object"==typeof self&&self&&self.Object===Object&&self,u=a||s||Function("return this")();let f=function(){return u.Date.now()};var c=/\s/;let d=function(e){for(var t=e.length;t--&&c.test(e.charAt(t)););return t};var p=/^\s+/,m=u.Symbol,h=Object.prototype,y=h.hasOwnProperty,g=h.toString,v=m?m.toStringTag:void 0;let b=function(e){var t=y.call(e,v),r=e[v];try{e[v]=void 0;var n=!0}catch(e){}var i=g.call(e);return n&&(t?e[v]=r:delete e[v]),i};var w=Object.prototype.toString,x=m?m.toStringTag:void 0;let S=function(e){var t;return"symbol"==typeof e||null!=e&&"object"==typeof e&&"[object Symbol]"==(null==(t=e)?void 0===t?"[object Undefined]":"[object Null]":x&&x in Object(t)?b(t):w.call(t))};var E=0/0,A=/^[-+]0x[0-9a-f]+$/i,F=/^0b[01]+$/i,_=/^0o[0-7]+$/i,V=parseInt;let C=function(e){if("number"==typeof e)return e;if(S(e))return E;if(l(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=l(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=(t=e)?t.slice(0,d(t)+1).replace(p,""):t;var n=F.test(e);return n||_.test(e)?V(e.slice(2),n?2:8):A.test(e)?E:+e};var k=Math.max,T=Math.min;let R=function(e,t,r){var n,i,o,a,s,u,c=0,d=!1,p=!1,m=!0;if("function"!=typeof e)throw TypeError("Expected a function");function h(t){var r=n,o=i;return n=i=void 0,c=t,a=e.apply(o,r)}function y(e){var r=e-u,n=e-c;return void 0===u||r>=t||r<0||p&&n>=o}function g(){var e,r,n,i=f();if(y(i))return v(i);s=setTimeout(g,(e=i-u,r=i-c,n=t-e,p?T(n,o-r):n))}function v(e){return(s=void 0,m&&n)?h(e):(n=i=void 0,a)}function b(){var e,r=f(),o=y(r);if(n=arguments,i=this,u=r,o){if(void 0===s)return c=e=u,s=setTimeout(g,t),d?h(e):a;if(p)return clearTimeout(s),s=setTimeout(g,t),h(u)}return void 0===s&&(s=setTimeout(g,t)),a}return t=C(t)||0,l(r)&&(d=!!r.leading,o=(p="maxWait"in r)?k(C(r.maxWait)||0,t):o,m="trailing"in r?!!r.trailing:m),b.cancel=function(){void 0!==s&&clearTimeout(s),c=0,n=u=i=s=void 0},b.flush=function(){return void 0===s?a:v(f())},b};function D(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)e[n]=r[n]}return e}(function e(t,r){function n(e,n,i){if("undefined"!=typeof document){"number"==typeof(i=D({},r,i)).expires&&(i.expires=new Date(Date.now()+864e5*i.expires)),i.expires&&(i.expires=i.expires.toUTCString()),e=encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var o="";for(var l in i)i[l]&&(o+="; "+l,!0!==i[l])&&(o+="="+i[l].split(";")[0]);return document.cookie=e+"="+t.write(n,e)+o}}return Object.create({set:n,get:function(e){if("undefined"!=typeof document&&(!arguments.length||e)){for(var r=document.cookie?document.cookie.split("; "):[],n={},i=0;i<r.length;i++){var o=r[i].split("="),l=o.slice(1).join("=");try{var a=decodeURIComponent(o[0]);if(n[a]=t.read(l,a),e===a)break}catch(e){}}return e?n[e]:n}},remove:function(e,t){n(e,"",D({},t,{expires:-1}))},withAttributes:function(t){return e(this.converter,D({},this.attributes,t))},withConverter:function(t){return e(D({},this.converter,t),this.attributes)}},{attributes:{value:Object.freeze(r)},converter:{value:Object.freeze(t)}})})({read:function(e){return'"'===e[0]&&(e=e.slice(1,-1)),e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(e){return encodeURIComponent(e).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"}),r(73084),r(37284);let L=e=>{let t=(0,o.useRef)(e);return t.current=e,t},O="undefined"!=typeof window,N="undefined"!=typeof navigator;function j(e,...t){e&&e.addEventListener&&e.addEventListener(...t)}function M(e,...t){e&&e.removeEventListener&&e.removeEventListener(...t)}O&&(null==(i=window)?void 0:null==(n=i.navigator)?void 0:n.userAgent)&&/iP(?:ad|hone|od)/.test(window.navigator.userAgent);let P=(e,t,r)=>{let n=useRef(void 0);n.current&&r(t,n.current)||(n.current=t),useEffect(e,n.current)},B=(e,t,r)=>{let n=L(e),i=(0,o.useMemo)(()=>R((...e)=>n.current(...e),t,r),[JSON.stringify(r),t]);return function(e){let t=L(e);(0,o.useEffect)(()=>()=>{t.current()},[t])}(()=>{i.cancel()}),{run:i,cancel:i.cancel,flush:i.flush}},I=()=>{let e=(0,o.useRef)(!0);return e.current?(e.current=!1,!0):e.current};Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY;let H=e=>(t,r)=>{let n=I();e(()=>{if(!n)return t()},r)};H(o.useEffect);let U=(e,t)=>{let[r,n]=(0,o.useState)(function(e,t){return void 0!==t?t:!!O&&window.matchMedia(e).matches}(e,t));return(0,o.useEffect)(()=>{let t=!0,r=window.matchMedia(e),i=()=>{t&&n(!!r.matches)};return"addEventListener"in r?r.addEventListener("change",i):null==r.addListener||r.addListener.call(r,i),n(r.matches),()=>{t=!1,"removeEventListener"in r?r.removeEventListener("change",i):null==r.removeListener||r.removeListener.call(r,i)}},[e]),r};Number.NaN,Number.NaN,Number.NaN,Number.NaN,Number.NaN,Number.NaN,Number.NaN,Number.NaN,Number.NaN,Number.NaN,Number.NaN,Number.NaN;let W=N?navigator:void 0,z=W&&(W.connection||W.mozConnection||W.webkitConnection);function $(e){let t=null==W?void 0:W.onLine,r=null==e?void 0:e.online;return{online:t,previous:r,since:t!==r?new Date:null==e?void 0:e.since,downlink:null==z?void 0:z.downlink,downlinkMax:null==z?void 0:z.downlinkMax,effectiveType:null==z?void 0:z.effectiveType,rtt:null==z?void 0:z.rtt,saveData:null==z?void 0:z.saveData,type:null==z?void 0:z.type}}let q=()=>{let[e,t]=(0,o.useState)($);return(0,o.useEffect)(()=>{let e=()=>{t($)};return j(window,"online",e,{passive:!0}),j(window,"offline",e,{passive:!0}),z&&j(z,"change",e,{passive:!0}),()=>{M(window,"online",e),M(window,"offline",e),z&&M(z,"change",e)}},[]),e},Y=new WeakSet,X=e=>(t,r)=>{let n=()=>{if(!Y.has(t))return Y.add(t),t()};e(()=>n(),r)};X(o.useEffect),X(o.useLayoutEffect);let Z=()=>{let{online:e}=q();return e};function G(...e){return(0,o.useMemo)(()=>(function(...e){return t=>{e.forEach(e=>{!function(e,t){if(null!=e){if("function"==typeof e){e(t);return}try{e.current=t}catch(r){throw Error(`Cannot assign value '${t}' to ref '${e}'`)}}}(e,t)})}})(...e),e)}H(o.useLayoutEffect),o.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}})},17203:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},67537:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},66314:(e,t,r)=>{"use strict";r.d(t,{Gc:()=>_,KN:()=>L,Qr:()=>D,RV:()=>V,U2:()=>b,cI:()=>eE,t8:()=>x});var n=r(60343),i=e=>"checkbox"===e.type,o=e=>e instanceof Date,l=e=>null==e;let a=e=>"object"==typeof e;var s=e=>!l(e)&&!Array.isArray(e)&&a(e)&&!o(e),u=e=>s(e)&&e.target?i(e.target)?e.target.checked:e.target.value:e,f=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,c=(e,t)=>e.has(f(t)),d=e=>{let t=e.constructor&&e.constructor.prototype;return s(t)&&t.hasOwnProperty("isPrototypeOf")},p="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function m(e){let t;let r=Array.isArray(e),n="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(!(p&&(e instanceof Blob||n))&&(r||s(e))))return e;else if(t=r?[]:{},r||d(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=m(e[r]));else t=e;return t}var h=e=>/^\w*$/.test(e),y=e=>void 0===e,g=e=>Array.isArray(e)?e.filter(Boolean):[],v=e=>g(e.replace(/["|']|\]/g,"").split(/\.|\[/)),b=(e,t,r)=>{if(!t||!s(e))return r;let n=(h(t)?[t]:v(t)).reduce((e,t)=>l(e)?e:e[t],e);return y(n)||n===e?y(e[t])?r:e[t]:n},w=e=>"boolean"==typeof e,x=(e,t,r)=>{let n=-1,i=h(t)?[t]:v(t),o=i.length,l=o-1;for(;++n<o;){let t=i[n],o=r;if(n!==l){let r=e[t];o=s(r)||Array.isArray(r)?r:isNaN(+i[n+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=o,e=e[t]}};let S={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},E={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},A={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},F=n.createContext(null);F.displayName="HookFormContext";let _=()=>n.useContext(F),V=e=>{let{children:t,...r}=e;return n.createElement(F.Provider,{value:r},t)};var C=(e,t,r,n=!0)=>{let i={defaultValues:t._defaultValues};for(let o in e)Object.defineProperty(i,o,{get:()=>(t._proxyFormState[o]!==E.all&&(t._proxyFormState[o]=!n||E.all),r&&(r[o]=!0),e[o])});return i};let k="undefined"!=typeof window?n.useLayoutEffect:n.useEffect;var T=e=>"string"==typeof e,R=(e,t,r,n,i)=>T(e)?(n&&t.watch.add(e),b(r,e,i)):Array.isArray(e)?e.map(e=>(n&&t.watch.add(e),b(r,e))):(n&&(t.watchAll=!0),r);let D=e=>e.render(function(e){let t=_(),{name:r,disabled:i,control:o=t.control,shouldUnregister:l}=e,a=c(o._names.array,r),s=function(e){let t=_(),{control:r=t.control,name:i,defaultValue:o,disabled:l,exact:a}=e||{},s=n.useRef(o),[u,f]=n.useState(r._getWatch(i,s.current));return k(()=>r._subscribe({name:i,formState:{values:!0},exact:a,callback:e=>!l&&f(R(i,r._names,e.values||r._formValues,!1,s.current))}),[i,r,l,a]),n.useEffect(()=>r._removeUnmounted()),u}({control:o,name:r,defaultValue:b(o._formValues,r,b(o._defaultValues,r,e.defaultValue)),exact:!0}),f=function(e){let t=_(),{control:r=t.control,disabled:i,name:o,exact:l}=e||{},[a,s]=n.useState(r._formState),u=n.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return k(()=>r._subscribe({name:o,formState:u.current,exact:l,callback:e=>{i||s({...r._formState,...e})}}),[o,i,l]),n.useEffect(()=>{u.current.isValid&&r._setValid(!0)},[r]),n.useMemo(()=>C(a,r,u.current,!1),[a,r])}({control:o,name:r,exact:!0}),d=n.useRef(e),p=n.useRef(o.register(r,{...e.rules,value:s,...w(e.disabled)?{disabled:e.disabled}:{}})),h=n.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!b(f.errors,r)},isDirty:{enumerable:!0,get:()=>!!b(f.dirtyFields,r)},isTouched:{enumerable:!0,get:()=>!!b(f.touchedFields,r)},isValidating:{enumerable:!0,get:()=>!!b(f.validatingFields,r)},error:{enumerable:!0,get:()=>b(f.errors,r)}}),[f,r]),g=n.useCallback(e=>p.current.onChange({target:{value:u(e),name:r},type:S.CHANGE}),[r]),v=n.useCallback(()=>p.current.onBlur({target:{value:b(o._formValues,r),name:r},type:S.BLUR}),[r,o._formValues]),E=n.useCallback(e=>{let t=b(o._fields,r);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})},[o._fields,r]),A=n.useMemo(()=>({name:r,value:s,...w(i)||f.disabled?{disabled:f.disabled||i}:{},onChange:g,onBlur:v,ref:E}),[r,i,f.disabled,g,v,E,s]);return n.useEffect(()=>{let e=o._options.shouldUnregister||l;o.register(r,{...d.current.rules,...w(d.current.disabled)?{disabled:d.current.disabled}:{}});let t=(e,t)=>{let r=b(o._fields,e);r&&r._f&&(r._f.mount=t)};if(t(r,!0),e){let e=m(b(o._options.defaultValues,r));x(o._defaultValues,r,e),y(b(o._formValues,r))&&x(o._formValues,r,e)}return a||o.register(r),()=>{(a?e&&!o._state.action:e)?o.unregister(r):t(r,!1)}},[r,o,a,l]),n.useEffect(()=>{o._setDisabledField({disabled:i,name:r})},[i,r,o]),n.useMemo(()=>({field:A,formState:f,fieldState:h}),[A,f,h])}(e));var L=(e,t,r,n,i)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[n]:i||!0}}:{},O=e=>Array.isArray(e)?e:[e],N=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},j=e=>l(e)||!a(e);function M(e,t,r=new WeakSet){if(j(e)||j(t))return e===t;if(o(e)&&o(t))return e.getTime()===t.getTime();let n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;if(r.has(e)||r.has(t))return!0;for(let l of(r.add(e),r.add(t),n)){let n=e[l];if(!i.includes(l))return!1;if("ref"!==l){let e=t[l];if(o(n)&&o(e)||s(n)&&s(e)||Array.isArray(n)&&Array.isArray(e)?!M(n,e,r):n!==e)return!1}}return!0}var P=e=>s(e)&&!Object.keys(e).length,B=e=>"file"===e.type,I=e=>"function"==typeof e,H=e=>{if(!p)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},U=e=>"select-multiple"===e.type,W=e=>"radio"===e.type,z=e=>W(e)||i(e),$=e=>H(e)&&e.isConnected;function q(e,t){let r=Array.isArray(t)?t:h(t)?[t]:v(t),n=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,n=0;for(;n<r;)e=y(e)?n++:e[t[n++]];return e}(e,r),i=r.length-1,o=r[i];return n&&delete n[o],0!==i&&(s(n)&&P(n)||Array.isArray(n)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!y(e[t]))return!1;return!0}(n))&&q(e,r.slice(0,-1)),e}var Y=e=>{for(let t in e)if(I(e[t]))return!0;return!1};function X(e,t={}){let r=Array.isArray(e);if(s(e)||r)for(let r in e)Array.isArray(e[r])||s(e[r])&&!Y(e[r])?(t[r]=Array.isArray(e[r])?[]:{},X(e[r],t[r])):l(e[r])||(t[r]=!0);return t}var Z=(e,t)=>(function e(t,r,n){let i=Array.isArray(t);if(s(t)||i)for(let i in t)Array.isArray(t[i])||s(t[i])&&!Y(t[i])?y(r)||j(n[i])?n[i]=Array.isArray(t[i])?X(t[i],[]):{...X(t[i])}:e(t[i],l(r)?{}:r[i],n[i]):n[i]=!M(t[i],r[i]);return n})(e,t,X(t));let G={value:!1,isValid:!1},J={value:!0,isValid:!0};var K=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!y(e[0].attributes.value)?y(e[0].value)||""===e[0].value?J:{value:e[0].value,isValid:!0}:J:G}return G},Q=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:n})=>y(e)?e:t?""===e?NaN:e?+e:e:r&&T(e)?new Date(e):n?n(e):e;let ee={isValid:!1,value:null};var et=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,ee):ee;function er(e){let t=e.ref;return B(t)?t.files:W(t)?et(e.refs).value:U(t)?[...t.selectedOptions].map(({value:e})=>e):i(t)?K(e.refs).value:Q(y(t.value)?e.ref.value:t.value,e)}var en=(e,t,r,n)=>{let i={};for(let r of e){let e=b(t,r);e&&x(i,r,e._f)}return{criteriaMode:r,names:[...e],fields:i,shouldUseNativeValidation:n}},ei=e=>e instanceof RegExp,eo=e=>y(e)?e:ei(e)?e.source:s(e)?ei(e.value)?e.value.source:e.value:e,el=e=>({isOnSubmit:!e||e===E.onSubmit,isOnBlur:e===E.onBlur,isOnChange:e===E.onChange,isOnAll:e===E.all,isOnTouch:e===E.onTouched});let ea="AsyncFunction";var es=e=>!!e&&!!e.validate&&!!(I(e.validate)&&e.validate.constructor.name===ea||s(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===ea)),eu=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),ef=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let ec=(e,t,r,n)=>{for(let i of r||Object.keys(e)){let r=b(e,i);if(r){let{_f:e,...o}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],i)&&!n||e.ref&&t(e.ref,e.name)&&!n)return!0;if(ec(o,t))break}else if(s(o)&&ec(o,t))break}}};function ed(e,t,r){let n=b(e,r);if(n||h(r))return{error:n,name:r};let i=r.split(".");for(;i.length;){let n=i.join("."),o=b(t,n),l=b(e,n);if(o&&!Array.isArray(o)&&r!==n)break;if(l&&l.type)return{name:n,error:l};if(l&&l.root&&l.root.type)return{name:`${n}.root`,error:l.root};i.pop()}return{name:r}}var ep=(e,t,r,n)=>{r(e);let{name:i,...o}=e;return P(o)||Object.keys(o).length>=Object.keys(t).length||Object.keys(o).find(e=>t[e]===(!n||E.all))},em=(e,t,r)=>!e||!t||e===t||O(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),eh=(e,t,r,n,i)=>!i.isOnAll&&(!r&&i.isOnTouch?!(t||e):(r?n.isOnBlur:i.isOnBlur)?!e:(r?!n.isOnChange:!i.isOnChange)||e),ey=(e,t)=>!g(b(e,t)).length&&q(e,t),eg=(e,t,r)=>{let n=O(b(e,r));return x(n,"root",t[r]),x(e,r,n),e},ev=e=>T(e);function eb(e,t,r="validate"){if(ev(e)||Array.isArray(e)&&e.every(ev)||w(e)&&!e)return{type:r,message:ev(e)?e:"",ref:t}}var ew=e=>s(e)&&!ei(e)?e:{value:e,message:""},ex=async(e,t,r,n,o,a)=>{let{ref:u,refs:f,required:c,maxLength:d,minLength:p,min:m,max:h,pattern:g,validate:v,name:x,valueAsNumber:S,mount:E}=e._f,F=b(r,x);if(!E||t.has(x))return{};let _=f?f[0]:u,V=e=>{o&&_.reportValidity&&(_.setCustomValidity(w(e)?"":e||""),_.reportValidity())},C={},k=W(u),R=i(u),D=(S||B(u))&&y(u.value)&&y(F)||H(u)&&""===u.value||""===F||Array.isArray(F)&&!F.length,O=L.bind(null,x,n,C),N=(e,t,r,n=A.maxLength,i=A.minLength)=>{let o=e?t:r;C[x]={type:e?n:i,message:o,ref:u,...O(e?n:i,o)}};if(a?!Array.isArray(F)||!F.length:c&&(!(k||R)&&(D||l(F))||w(F)&&!F||R&&!K(f).isValid||k&&!et(f).isValid)){let{value:e,message:t}=ev(c)?{value:!!c,message:c}:ew(c);if(e&&(C[x]={type:A.required,message:t,ref:_,...O(A.required,t)},!n))return V(t),C}if(!D&&(!l(m)||!l(h))){let e,t;let r=ew(h),i=ew(m);if(l(F)||isNaN(F)){let n=u.valueAsDate||new Date(F),o=e=>new Date(new Date().toDateString()+" "+e),l="time"==u.type,a="week"==u.type;T(r.value)&&F&&(e=l?o(F)>o(r.value):a?F>r.value:n>new Date(r.value)),T(i.value)&&F&&(t=l?o(F)<o(i.value):a?F<i.value:n<new Date(i.value))}else{let n=u.valueAsNumber||(F?+F:F);l(r.value)||(e=n>r.value),l(i.value)||(t=n<i.value)}if((e||t)&&(N(!!e,r.message,i.message,A.max,A.min),!n))return V(C[x].message),C}if((d||p)&&!D&&(T(F)||a&&Array.isArray(F))){let e=ew(d),t=ew(p),r=!l(e.value)&&F.length>+e.value,i=!l(t.value)&&F.length<+t.value;if((r||i)&&(N(r,e.message,t.message),!n))return V(C[x].message),C}if(g&&!D&&T(F)){let{value:e,message:t}=ew(g);if(ei(e)&&!F.match(e)&&(C[x]={type:A.pattern,message:t,ref:u,...O(A.pattern,t)},!n))return V(t),C}if(v){if(I(v)){let e=eb(await v(F,r),_);if(e&&(C[x]={...e,...O(A.validate,e.message)},!n))return V(e.message),C}else if(s(v)){let e={};for(let t in v){if(!P(e)&&!n)break;let i=eb(await v[t](F,r),_,t);i&&(e={...i,...O(t,i.message)},V(i.message),n&&(C[x]=e))}if(!P(e)&&(C[x]={ref:_,...e},!n))return C}}return V(!0),C};let eS={mode:E.onSubmit,reValidateMode:E.onChange,shouldFocusError:!0};function eE(e={}){let t=n.useRef(void 0),r=n.useRef(void 0),[a,f]=n.useState({isDirty:!1,isValidating:!1,isLoading:I(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:I(e.defaultValues)?void 0:e.defaultValues});if(!t.current){if(e.formControl)t.current={...e.formControl,formState:a},e.defaultValues&&!I(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{let{formControl:r,...n}=function(e={}){let t,r={...eS,...e},n={submitCount:0,isDirty:!1,isReady:!1,isLoading:I(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},a={},f=(s(r.defaultValues)||s(r.values))&&m(r.defaultValues||r.values)||{},d=r.shouldUnregister?{}:m(f),h={action:!1,mount:!1,watch:!1},v={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},A=0,F={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},_={...F},V={array:N(),state:N()},C=r.criteriaMode===E.all,k=e=>t=>{clearTimeout(A),A=setTimeout(e,t)},D=async e=>{if(!r.disabled&&(F.isValid||_.isValid||e)){let e=r.resolver?P((await G()).errors):await K(a,!0);e!==n.isValid&&V.state.next({isValid:e})}},L=(e,t)=>{!r.disabled&&(F.isValidating||F.validatingFields||_.isValidating||_.validatingFields)&&((e||Array.from(v.mount)).forEach(e=>{e&&(t?x(n.validatingFields,e,t):q(n.validatingFields,e))}),V.state.next({validatingFields:n.validatingFields,isValidating:!P(n.validatingFields)}))},j=(e,t)=>{x(n.errors,e,t),V.state.next({errors:n.errors})},W=(e,t,r,n)=>{let i=b(a,e);if(i){let o=b(d,e,y(r)?b(f,e):r);y(o)||n&&n.defaultChecked||t?x(d,e,t?o:er(i._f)):ei(e,o),h.mount&&D()}},Y=(e,t,i,o,l)=>{let a=!1,s=!1,u={name:e};if(!r.disabled){if(!i||o){(F.isDirty||_.isDirty)&&(s=n.isDirty,n.isDirty=u.isDirty=ee(),a=s!==u.isDirty);let r=M(b(f,e),t);s=!!b(n.dirtyFields,e),r?q(n.dirtyFields,e):x(n.dirtyFields,e,!0),u.dirtyFields=n.dirtyFields,a=a||(F.dirtyFields||_.dirtyFields)&&!r!==s}if(i){let t=b(n.touchedFields,e);t||(x(n.touchedFields,e,i),u.touchedFields=n.touchedFields,a=a||(F.touchedFields||_.touchedFields)&&t!==i)}a&&l&&V.state.next(u)}return a?u:{}},X=(e,i,o,l)=>{let a=b(n.errors,e),s=(F.isValid||_.isValid)&&w(i)&&n.isValid!==i;if(r.delayError&&o?(t=k(()=>j(e,o)))(r.delayError):(clearTimeout(A),t=null,o?x(n.errors,e,o):q(n.errors,e)),(o?!M(a,o):a)||!P(l)||s){let t={...l,...s&&w(i)?{isValid:i}:{},errors:n.errors,name:e};n={...n,...t},V.state.next(t)}},G=async e=>{L(e,!0);let t=await r.resolver(d,r.context,en(e||v.mount,a,r.criteriaMode,r.shouldUseNativeValidation));return L(e),t},J=async e=>{let{errors:t}=await G(e);if(e)for(let r of e){let e=b(t,r);e?x(n.errors,r,e):q(n.errors,r)}else n.errors=t;return t},K=async(e,t,i={valid:!0})=>{for(let o in e){let l=e[o];if(l){let{_f:e,...a}=l;if(e){let a=v.array.has(e.name),s=l._f&&es(l._f);s&&F.validatingFields&&L([o],!0);let u=await ex(l,v.disabled,d,C,r.shouldUseNativeValidation&&!t,a);if(s&&F.validatingFields&&L([o]),u[e.name]&&(i.valid=!1,t))break;t||(b(u,e.name)?a?eg(n.errors,u,e.name):x(n.errors,e.name,u[e.name]):q(n.errors,e.name))}P(a)||await K(a,t,i)}}return i.valid},ee=(e,t)=>!r.disabled&&(e&&t&&x(d,e,t),!M(eA(),f)),et=(e,t,r)=>R(e,v,{...h.mount?d:y(t)?f:T(e)?{[e]:t}:t},r,t),ei=(e,t,r={})=>{let n=b(a,e),o=t;if(n){let r=n._f;r&&(r.disabled||x(d,e,Q(t,r)),o=H(r.ref)&&l(t)?"":t,U(r.ref)?[...r.ref.options].forEach(e=>e.selected=o.includes(e.value)):r.refs?i(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(o)?e.checked=!!o.find(t=>t===e.value):e.checked=o===e.value||!!o)}):r.refs.forEach(e=>e.checked=e.value===o):B(r.ref)?r.ref.value="":(r.ref.value=o,r.ref.type||V.state.next({name:e,values:m(d)})))}(r.shouldDirty||r.shouldTouch)&&Y(e,o,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&eE(e)},ea=(e,t,r)=>{for(let n in t){if(!t.hasOwnProperty(n))return;let i=t[n],l=e+"."+n,u=b(a,l);(v.array.has(e)||s(i)||u&&!u._f)&&!o(i)?ea(l,i,r):ei(l,i,r)}},ev=(e,t,r={})=>{let i=b(a,e),o=v.array.has(e),s=m(t);x(d,e,s),o?(V.array.next({name:e,values:m(d)}),(F.isDirty||F.dirtyFields||_.isDirty||_.dirtyFields)&&r.shouldDirty&&V.state.next({name:e,dirtyFields:Z(f,d),isDirty:ee(e,s)})):!i||i._f||l(s)?ei(e,s,r):ea(e,s,r),ef(e,v)&&V.state.next({...n}),V.state.next({name:h.mount?e:void 0,values:m(d)})},eb=async e=>{h.mount=!0;let i=e.target,l=i.name,s=!0,f=b(a,l),c=e=>{s=Number.isNaN(e)||o(e)&&isNaN(e.getTime())||M(e,b(d,l,e))},p=el(r.mode),y=el(r.reValidateMode);if(f){let o,h;let g=i.type?er(f._f):u(e),w=e.type===S.BLUR||e.type===S.FOCUS_OUT,E=!eu(f._f)&&!r.resolver&&!b(n.errors,l)&&!f._f.deps||eh(w,b(n.touchedFields,l),n.isSubmitted,y,p),A=ef(l,v,w);x(d,l,g),w?(f._f.onBlur&&f._f.onBlur(e),t&&t(0)):f._f.onChange&&f._f.onChange(e);let k=Y(l,g,w),T=!P(k)||A;if(w||V.state.next({name:l,type:e.type,values:m(d)}),E)return(F.isValid||_.isValid)&&("onBlur"===r.mode?w&&D():w||D()),T&&V.state.next({name:l,...A?{}:k});if(!w&&A&&V.state.next({...n}),r.resolver){let{errors:e}=await G([l]);if(c(g),s){let t=ed(n.errors,a,l),r=ed(e,a,t.name||l);o=r.error,l=r.name,h=P(e)}}else L([l],!0),o=(await ex(f,v.disabled,d,C,r.shouldUseNativeValidation))[l],L([l]),c(g),s&&(o?h=!1:(F.isValid||_.isValid)&&(h=await K(a,!0)));s&&(f._f.deps&&eE(f._f.deps),X(l,h,o,k))}},ew=(e,t)=>{if(b(n.errors,t)&&e.focus)return e.focus(),1},eE=async(e,t={})=>{let i,o;let l=O(e);if(r.resolver){let t=await J(y(e)?e:l);i=P(t),o=e?!l.some(e=>b(t,e)):i}else e?((o=(await Promise.all(l.map(async e=>{let t=b(a,e);return await K(t&&t._f?{[e]:t}:t)}))).every(Boolean))||n.isValid)&&D():o=i=await K(a);return V.state.next({...!T(e)||(F.isValid||_.isValid)&&i!==n.isValid?{}:{name:e},...r.resolver||!e?{isValid:i}:{},errors:n.errors}),t.shouldFocus&&!o&&ec(a,ew,e?l:v.mount),o},eA=e=>{let t={...h.mount?d:f};return y(e)?t:T(e)?b(t,e):e.map(e=>b(t,e))},eF=(e,t)=>({invalid:!!b((t||n).errors,e),isDirty:!!b((t||n).dirtyFields,e),error:b((t||n).errors,e),isValidating:!!b(n.validatingFields,e),isTouched:!!b((t||n).touchedFields,e)}),e_=(e,t,r)=>{let i=(b(a,e,{_f:{}})._f||{}).ref,{ref:o,message:l,type:s,...u}=b(n.errors,e)||{};x(n.errors,e,{...u,...t,ref:i}),V.state.next({name:e,errors:n.errors,isValid:!1}),r&&r.shouldFocus&&i&&i.focus&&i.focus()},eV=e=>V.state.subscribe({next:t=>{em(e.name,t.name,e.exact)&&ep(t,e.formState||F,eN,e.reRenderRoot)&&e.callback({values:{...d},...n,...t})}}).unsubscribe,eC=(e,t={})=>{for(let i of e?O(e):v.mount)v.mount.delete(i),v.array.delete(i),t.keepValue||(q(a,i),q(d,i)),t.keepError||q(n.errors,i),t.keepDirty||q(n.dirtyFields,i),t.keepTouched||q(n.touchedFields,i),t.keepIsValidating||q(n.validatingFields,i),r.shouldUnregister||t.keepDefaultValue||q(f,i);V.state.next({values:m(d)}),V.state.next({...n,...t.keepDirty?{isDirty:ee()}:{}}),t.keepIsValid||D()},ek=({disabled:e,name:t})=>{(w(e)&&h.mount||e||v.disabled.has(t))&&(e?v.disabled.add(t):v.disabled.delete(t))},eT=(e,t={})=>{let n=b(a,e),i=w(t.disabled)||w(r.disabled);return x(a,e,{...n||{},_f:{...n&&n._f?n._f:{ref:{name:e}},name:e,mount:!0,...t}}),v.mount.add(e),n?ek({disabled:w(t.disabled)?t.disabled:r.disabled,name:e}):W(e,!0,t.value),{...i?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:eo(t.min),max:eo(t.max),minLength:eo(t.minLength),maxLength:eo(t.maxLength),pattern:eo(t.pattern)}:{},name:e,onChange:eb,onBlur:eb,ref:i=>{if(i){eT(e,t),n=b(a,e);let r=y(i.value)&&i.querySelectorAll&&i.querySelectorAll("input,select,textarea")[0]||i,o=z(r),l=n._f.refs||[];(o?l.find(e=>e===r):r===n._f.ref)||(x(a,e,{_f:{...n._f,...o?{refs:[...l.filter($),r,...Array.isArray(b(f,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),W(e,!1,void 0,r))}else(n=b(a,e,{}))._f&&(n._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(c(v.array,e)&&h.action)&&v.unMount.add(e)}}},eR=()=>r.shouldFocusError&&ec(a,ew,v.mount),eD=(e,t)=>async i=>{let o;i&&(i.preventDefault&&i.preventDefault(),i.persist&&i.persist());let l=m(d);if(V.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await G();n.errors=e,l=m(t)}else await K(a);if(v.disabled.size)for(let e of v.disabled)q(l,e);if(q(n.errors,"root"),P(n.errors)){V.state.next({errors:{}});try{await e(l,i)}catch(e){o=e}}else t&&await t({...n.errors},i),eR(),setTimeout(eR);if(V.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:P(n.errors)&&!o,submitCount:n.submitCount+1,errors:n.errors}),o)throw o},eL=(e,t={})=>{let i=e?m(e):f,o=m(i),l=P(e),s=l?f:o;if(t.keepDefaultValues||(f=i),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...v.mount,...Object.keys(Z(f,d))])))b(n.dirtyFields,e)?x(s,e,b(d,e)):ev(e,b(s,e));else{if(p&&y(e))for(let e of v.mount){let t=b(a,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(H(e)){let t=e.closest("form");if(t){t.reset();break}}}}for(let e of v.mount){let t=b(s,e,b(f,e));y(t)||(x(s,e,t),ev(e,b(s,e)))}}d=m(s),V.array.next({values:{...s}}),V.state.next({values:{...s}})}v={mount:t.keepDirtyValues?v.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},h.mount=!F.isValid||!!t.keepIsValid||!!t.keepDirtyValues,h.watch=!!r.shouldUnregister,V.state.next({submitCount:t.keepSubmitCount?n.submitCount:0,isDirty:!l&&(t.keepDirty?n.isDirty:!!(t.keepDefaultValues&&!M(e,f))),isSubmitted:!!t.keepIsSubmitted&&n.isSubmitted,dirtyFields:l?{}:t.keepDirtyValues?t.keepDefaultValues&&d?Z(f,d):n.dirtyFields:t.keepDefaultValues&&e?Z(f,e):t.keepDirty?n.dirtyFields:{},touchedFields:t.keepTouched?n.touchedFields:{},errors:t.keepErrors?n.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&n.isSubmitSuccessful,isSubmitting:!1})},eO=(e,t)=>eL(I(e)?e(d):e,t),eN=e=>{n={...n,...e}},ej={control:{register:eT,unregister:eC,getFieldState:eF,handleSubmit:eD,setError:e_,_subscribe:eV,_runSchema:G,_focusError:eR,_getWatch:et,_getDirty:ee,_setValid:D,_setFieldArray:(e,t=[],i,o,l=!0,s=!0)=>{if(o&&i&&!r.disabled){if(h.action=!0,s&&Array.isArray(b(a,e))){let t=i(b(a,e),o.argA,o.argB);l&&x(a,e,t)}if(s&&Array.isArray(b(n.errors,e))){let t=i(b(n.errors,e),o.argA,o.argB);l&&x(n.errors,e,t),ey(n.errors,e)}if((F.touchedFields||_.touchedFields)&&s&&Array.isArray(b(n.touchedFields,e))){let t=i(b(n.touchedFields,e),o.argA,o.argB);l&&x(n.touchedFields,e,t)}(F.dirtyFields||_.dirtyFields)&&(n.dirtyFields=Z(f,d)),V.state.next({name:e,isDirty:ee(e,t),dirtyFields:n.dirtyFields,errors:n.errors,isValid:n.isValid})}else x(d,e,t)},_setDisabledField:ek,_setErrors:e=>{n.errors=e,V.state.next({errors:n.errors,isValid:!1})},_getFieldArray:e=>g(b(h.mount?d:f,e,r.shouldUnregister?b(f,e,[]):[])),_reset:eL,_resetDefaultValues:()=>I(r.defaultValues)&&r.defaultValues().then(e=>{eO(e,r.resetOptions),V.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of v.unMount){let t=b(a,e);t&&(t._f.refs?t._f.refs.every(e=>!$(e)):!$(t._f.ref))&&eC(e)}v.unMount=new Set},_disableForm:e=>{w(e)&&(V.state.next({disabled:e}),ec(a,(t,r)=>{let n=b(a,r);n&&(t.disabled=n._f.disabled||e,Array.isArray(n._f.refs)&&n._f.refs.forEach(t=>{t.disabled=n._f.disabled||e}))},0,!1))},_subjects:V,_proxyFormState:F,get _fields(){return a},get _formValues(){return d},get _state(){return h},set _state(value){h=value},get _defaultValues(){return f},get _names(){return v},set _names(value){v=value},get _formState(){return n},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(h.mount=!0,_={..._,...e.formState},eV({...e,formState:_})),trigger:eE,register:eT,handleSubmit:eD,watch:(e,t)=>I(e)?V.state.subscribe({next:r=>e(et(void 0,t),r)}):et(e,t,!0),setValue:ev,getValues:eA,reset:eO,resetField:(e,t={})=>{b(a,e)&&(y(t.defaultValue)?ev(e,m(b(f,e))):(ev(e,t.defaultValue),x(f,e,m(t.defaultValue))),t.keepTouched||q(n.touchedFields,e),t.keepDirty||(q(n.dirtyFields,e),n.isDirty=t.defaultValue?ee(e,m(b(f,e))):ee()),!t.keepError&&(q(n.errors,e),F.isValid&&D()),V.state.next({...n}))},clearErrors:e=>{e&&O(e).forEach(e=>q(n.errors,e)),V.state.next({errors:e?n.errors:{}})},unregister:eC,setError:e_,setFocus:(e,t={})=>{let r=b(a,e),n=r&&r._f;if(n){let e=n.refs?n.refs[0]:n.ref;e.focus&&(e.focus(),t.shouldSelect&&I(e.select)&&e.select())}},getFieldState:eF};return{...ej,formControl:ej}}(e);t.current={...n,formState:a}}}let d=t.current.control;return d._options=e,k(()=>{let e=d._subscribe({formState:d._proxyFormState,callback:()=>f({...d._formState}),reRenderRoot:!0});return f(e=>({...e,isReady:!0})),d._formState.isReady=!0,e},[d]),n.useEffect(()=>d._disableForm(e.disabled),[d,e.disabled]),n.useEffect(()=>{e.mode&&(d._options.mode=e.mode),e.reValidateMode&&(d._options.reValidateMode=e.reValidateMode)},[d,e.mode,e.reValidateMode]),n.useEffect(()=>{e.errors&&(d._setErrors(e.errors),d._focusError())},[d,e.errors]),n.useEffect(()=>{e.shouldUnregister&&d._subjects.state.next({values:d._getWatch()})},[d,e.shouldUnregister]),n.useEffect(()=>{if(d._proxyFormState.isDirty){let e=d._getDirty();e!==a.isDirty&&d._subjects.state.next({isDirty:e})}},[d,a.isDirty]),n.useEffect(()=>{e.values&&!M(e.values,r.current)?(d._reset(e.values,d._options.resetOptions),r.current=e.values,f(e=>({...e}))):d._resetDefaultValues()},[d,e.values]),n.useEffect(()=>{d._state.mount||(d._setValid(),d._state.mount=!0),d._state.watch&&(d._state.watch=!1,d._subjects.state.next({...d._formState})),d._removeUnmounted()}),t.current.formState=C(a,d),t.current}}};