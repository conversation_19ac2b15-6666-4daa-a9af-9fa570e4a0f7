"use strict";exports.id=9445,exports.ids=[9445],exports.modules={93791:(e,s,t)=>{t.d(s,{q:()=>i});var r=t(60343);function i(e){let{initialFilter:s,loadList:t,loadDues:i,toggleOverdue:a}=e,[l,n]=(0,r.useState)(s),d=(0,r.useCallback)(({type:e,data:s})=>{let r={...l};"vessel"===e&&(Array.isArray(s)&&s.length?r.vesselID={in:s.map(e=>+e.value)}:s&&!Array.isArray(s)?r.vesselID={eq:+s.value}:delete r.vesselID),"trainingType"===e&&(Array.isArray(s)&&s.length?r.trainingTypes={id:{in:s.map(e=>+e.value)}}:s&&!Array.isArray(s)?r.trainingTypes={id:{contains:+s.value}}:delete r.trainingTypes),"trainer"===e&&(Array.isArray(s)&&s.length?r.trainer={id:{in:s.map(e=>+e.value)}}:s&&!Array.isArray(s)?r.trainer={id:{eq:+s.value}}:delete r.trainer),"member"===e&&(Array.isArray(s)&&s.length?r.members={id:{in:s.map(e=>+e.value)}}:s&&!Array.isArray(s)?r.members={id:{eq:+s.value}}:delete r.members),"dateRange"===e&&(s?.startDate&&s?.endDate?r.date={gte:s.startDate,lte:s.endDate}:delete r.date),"overdue"===e&&void 0!==s&&a(e=>!s),n(r),i(r),t(0,r)},[l,i,t,a]);return{filter:l,setFilter:n,handleFilterChange:d}}},59445:(e,s,t)=>{t.d(s,{z:()=>W,Z:()=>k});var r=t(98768),i=t(94060),a=t(79418),l=t(60343),n=t(37042),d=t(13842),c=t(46776),o=t(26100),m=t(69424),h=t(51742),u=t(30905),p=t(67537),x=t(25394),g=t(96268),v=t(56937),f=t(93791),j=t(13006),y=t(60336),b=t(52241),N=t(66263),w=t(10706),M=t(60797),Z=t(50058);let T=e=>{if(!e)return"";try{let s=new Date(e);return(0,w.WU)(s,"dd/MM/yy")}catch{return""}},D=({data:e,memberId:s,type:t="completed"})=>{(0,Z.k)();let i="completed"===t,a=i?e.members?.nodes||[]:e.members||[],l=i?e.trainingTypes?.nodes?.map(e=>e.title).join(", ")||"":e.trainingType?.title||"";return(0,r.jsxs)("div",{className:"w-full space-y-3 tablet-md:border-none border-b border-border py-3 small:pe-4",children:[(0,r.jsxs)("div",{className:"flex flex-wrap justify-between items-center",children:[i?r.jsx(N.default,{href:`/crew-training/info?id=${e.id}`,className:"font-semibold text-base hover:text-primary",children:T(e.date)}):r.jsx("div",{className:"font-semibold text-base",children:l}),(0,r.jsxs)("div",{className:"flex gap-2 items-center landscape:hidden",children:[r.jsx(M.Label,{className:"text-sm m-0 text-muted-foreground",children:i?"Trainer:":"Status:"}),i?r.jsx("div",{className:"text-sm font-medium",children:s&&e.trainer?.id===+s?"You":`${e.trainer&&e.trainer.firstName||""} ${e.trainer&&e.trainer.surname||""}`}):r.jsx("div",{className:`text-sm font-medium px-2 py-1 rounded-md ${e.status?.isOverdue?"bg-destructive/10 text-destructive":"bg-warning/10 text-warning"}`,children:e.status?.label||"Unknown"})]})]}),(0,r.jsxs)("div",{className:(0,v.cn)("tablet-md:hidden",i?"space-y-[7px]":"flex items-center gap-1"),children:[r.jsx(M.Label,{className:"text-sm m-0 text-muted-foreground",children:i?"Training Details:":"Due Date:"}),r.jsx("div",{className:"text-sm",children:i?l:e.dueDate?T(e.dueDate):"Not specified"})]}),r.jsx(M.Label,{position:"left",className:"text-sm laptop:hidden text-muted-foreground",label:i?"Team:":"Crew Members:",children:(0,r.jsxs)("div",{className:"flex gap-1",children:[a.slice(0,6).map(s=>(0,r.jsxs)(x.u,{children:[r.jsx(x.aJ,{children:r.jsx(x.qE,{size:"sm",variant:!i&&e.status?.isOverdue?"destructive":"secondary",children:r.jsx(x.Q5,{className:"text-sm",children:(0,x.xE)(s.firstName,s.surname)})})}),(0,r.jsxs)(x._v,{children:[s.firstName," ",s.surname??""]})]},s.id)),a.length>6&&r.jsx("div",{children:(0,r.jsxs)(x.J2,{children:[r.jsx(x.CM,{className:"w-fit",asChild:!0,children:(0,r.jsxs)(x.zx,{variant:"outline",size:"sm",className:"w-fit",children:["+",a.length-6," more"]})}),r.jsx(x.yk,{children:r.jsx("div",{className:"p-3 w-64 max-h-64 overflow-auto",children:r.jsx("div",{className:"space-y-2",children:a.slice(6).map(e=>r.jsx("div",{className:"text-sm",children:`${e.firstName??""} ${e.surname??""}`},e.id))})})})]})})]})}),(0,r.jsxs)("div",{className:"flex justify-between landscape:hidden items-center",children:[r.jsx(M.Label,{className:"text-sm m-0 text-muted-foreground",children:i?"Location:":"Vessel:"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("span",{className:"text-sm text-nowrap",children:e.vessel?.title||""}),i&&r.jsx(y.F,{vessel:e.vessel,iconClassName:"size-8"})]})]})]})},k=({memberId:e=0,vesselId:s=0,applyFilterRef:t,excludeFilters:v=[]})=>{(0,m.useRouter)();let[N,w]=(0,l.useState)(!0),[M,Z]=(0,l.useState)({totalCount:0,hasNextPage:!1,hasPreviousPage:!1}),[T,k]=(0,l.useState)([]),[C,I]=(0,l.useState)([]),[A,S]=(0,l.useState)(0),[E,$]=(0,l.useState)([]),[F,L]=(0,l.useState)([]),[q,O]=(0,l.useState)([]),[z,R]=(0,l.useState)([]),[K]=(0,l.useState)(!1),[_,P]=(0,j.v1)("overdue");(0,g.ac)("(min-width: 720px)");let{getVesselWithIcon:G,loading:Q}=(0,b.P)(),[V,J]=(0,l.useState)(!1);(0,l.useEffect)(()=>{J("true"===_)},[_]);let U=(0,l.useCallback)(e=>{"function"==typeof e?J(s=>{let t=e(s);return P(t?"true":"false"),t}):(J(e),P(e?"true":"false"))},[P]),[B,{loading:Y}]=(0,a.t)(i.ly,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readTrainingSessions.nodes,t=s.map(e=>{let s=G(e.vessel.id,e.vessel);return{...e,vessel:s}}),r=Array.from(new Set(s.map(e=>e.vessel.id))).filter(e=>0!=+e),i=Array.from(new Set(s.flatMap(e=>e.trainingTypes.nodes.map(e=>e.id)))),a=Array.from(new Set(s.map(e=>e.trainerID))).filter(e=>0!=+e),l=Array.from(new Set(s.flatMap(e=>e.members.nodes.map(e=>e.id))));t&&(k(t),$(r),L(i),O(a),R(l)),Z(e.readTrainingSessions.pageInfo)},onError:e=>{console.error("queryTrainingList error",e)}}),X=async(e=0,s={...ee})=>{await B({variables:{filter:s,offset:100*e,limit:100}})},H=async t=>{let r={};e>0&&(r.memberID={eq:+e}),s>0&&(r.vesselID={eq:+s}),t.vesselID&&(r.vesselID=t.vesselID),t.trainingTypes&&(r.trainingTypeID={eq:t.trainingTypes.id.contains}),t.members&&(r.memberID={eq:t.members.id.contains}),t.date?r.dueDate=t.date:r.dueDate={ne:null},await er({variables:{filter:r}})},{filter:ee,setFilter:es,handleFilterChange:et}=(0,f.q)({initialFilter:{},loadList:X,loadDues:H,toggleOverdue:U});(0,l.useEffect)(()=>{t&&(t.current={apply:et,overdue:V,setOverdue:U})},[et,V,U]);let[er,{loading:ei}]=(0,a.t)(i.qX,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readTrainingSessionDues.nodes;s&&I(Object.values(s.filter(e=>e.vessel.seaLogsMembers.nodes.some(s=>s.id===e.memberID)).map(e=>({...e,status:(0,d.nu)(e)})).reduce((e,s)=>{let t=`${s.vesselID}-${s.trainingTypeID}-${s.dueDate}`;return e[t]||(e[t]={id:s.id,vesselID:s.vesselID,vessel:s.vessel,trainingTypeID:s.trainingTypeID,trainingType:s.trainingType,dueDate:s.dueDate,status:s.status,trainingLocationType:s.trainingSession.trainingLocationType,members:[]}),e[t].members.push(s.member),e},{})).map(e=>{let s=e.members.reduce((e,s)=>{let t=e.find(e=>e.id===s.id);return t?(t.firstName=s.firstName,t.surname=s.surname):e.push(s),e},[]);return{id:e.id,vesselID:e.vesselID,vessel:e.vessel,trainingTypeID:e.trainingTypeID,trainingType:e.trainingType,status:e.status,dueDate:e.dueDate,trainingLocationType:e.trainingLocationType,members:s}}))},onError:e=>{console.error("readTrainingSessionDues error",e)}});(0,l.useEffect)(()=>{if(N){let s={...ee};+e>0&&(s.members={id:{contains:+e}}),es(s),H(s),X(0,s),w(!1)}},[N]);let[ea,el]=(0,l.useState)(!1);(0,l.useEffect)(()=>{el(c.Zu)},[]);let en=!!v.includes("overdueToggle")&&(Y||ei);if(!ea||!(0,c.Fs)("EDIT_TRAINING",ea)&&!(0,c.Fs)("VIEW_TRAINING",ea)&&!(0,c.Fs)("RECORD_TRAINING",ea)&&!(0,c.Fs)("VIEW_MEMBER_TRAINING",ea))return ea?r.jsx(o.Z,{errorMessage:"Oops You do not have the permission to view this section."}):r.jsx(o.Z,{});let ed=(0,h.wu)([{accessorKey:"title",header:({column:e})=>r.jsx(u.u,{column:e,title:"Completed"}),cell:({row:s})=>{let t=s.original;return r.jsx(D,{data:t,memberId:e,type:"completed"})},sortingFn:(e,s)=>{let t=new Date(e?.original?.date||0).getTime();return new Date(s?.original?.date||0).getTime()-t}},{accessorKey:"trainingDrillsCompleted",cellAlignment:"left",header:({column:e})=>r.jsx(u.u,{column:e,title:"Training/drills completed"}),breakpoint:"tablet-md",cell:({row:e})=>{let s=e.original;return r.jsx(x.P,{children:s.trainingTypes.nodes?s.trainingTypes.nodes.map(e=>(0,r.jsxs)("span",{children:[e.title,",\xa0"]},e.id)):""})},sortingFn:(e,s)=>{let t=e?.original?.trainingTypes?.nodes?.[0]?.title||"",r=s?.original?.trainingTypes?.nodes?.[0]?.title||"";return t.localeCompare(r)}},{accessorKey:"where",cellAlignment:"left",header:({column:e})=>r.jsx(u.u,{column:e,title:"Where"}),breakpoint:"landscape",cell:({row:e})=>{let s=e.original;return(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("span",{className:"text-sm text-nowrap",children:s.vessel?.title||s.trainingLocationType||""}),r.jsx(y.F,{vessel:s.vessel,iconClassName:"size-8"})]})},sortingFn:(e,s)=>{let t=e?.original?.vessel?.title||"",r=s?.original?.vessel?.title||"";return t.localeCompare(r)}},{accessorKey:"trainer",cellAlignment:"center",header:({column:e})=>r.jsx(u.u,{column:e,title:"Trainer"}),breakpoint:"landscape",cell:({row:e})=>{let s=e.original;return r.jsx("div",{className:"text-nowrap",children:(0,r.jsxs)(x.u,{children:[r.jsx(x.aJ,{children:r.jsx(x.qE,{size:"sm",variant:"secondary",children:r.jsx(x.Q5,{className:"text-sm",children:(0,x.xE)(s.trainer.firstName,s.trainer.surname)})})}),(0,r.jsxs)(x._v,{children:[s.trainer.firstName," ",s.trainer.surname??""]})]})})},sortingFn:(e,s)=>{let t=`${e?.original?.trainer&&e?.original?.trainer?.firstName} ${e?.original?.trainer&&e?.original?.trainer?.surname}`||"",r=`${s?.original?.trainer&&s?.original?.trainer?.firstName} ${s?.original?.trainer&&s?.original?.trainer?.surname}`||"";return t.localeCompare(r)}},{accessorKey:"who",cellAlignment:"right",header:({column:e})=>r.jsx(u.u,{column:e,title:"Who"}),breakpoint:"laptop",cell:({row:e})=>{let s=e.original;return r.jsx("div",{className:"w-full flex items-end gap-1",children:s.members.nodes.map((e,s)=>(0,r.jsxs)(x.u,{children:[r.jsx(x.aJ,{children:r.jsx(x.qE,{size:"sm",variant:"secondary",children:r.jsx(x.Q5,{className:"text-sm",children:(0,x.xE)(e.firstName,e.surname)})})}),(0,r.jsxs)(x._v,{children:[e.firstName," ",e.surname??""]})]},s))})},sortingFn:(e,s)=>{let t=`${e?.original?.members?.nodes?.[0]?.firstName??""} ${e?.original?.members?.nodes?.[0]?.surname??""}`||"",r=`${s?.original?.members?.nodes?.[0]?.firstName??""} ${s?.original?.members?.nodes?.[0]?.surname??""}`||"";return t.localeCompare(r)}}]);return(0,r.jsxs)("div",{className:"w-full",children:[r.jsx("div",{className:"mb-5",children:r.jsx(x.Zb,{children:r.jsx(n.M,{memberId:e,onChange:et,overdueSwitcher:!V,excludeFilters:v})})}),v.includes("overdueToggle")?en?(0,r.jsxs)("div",{className:"flex items-center justify-center p-8 text-muted-foreground",children:[r.jsx(p.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading training data..."]}):r.jsx(W,{trainingSessionDues:v.includes("overdueToggle")?[...C||[],...(T||[]).map(e=>{let s=G(e.vessel?.id,e.vessel);return{id:e.id,dueDate:e.date,vesselID:e.vessel?.id,vessel:s,trainingTypeID:e.trainingTypes?.nodes?.[0]?.id,trainingType:e.trainingTypes?.nodes?.[0]||{title:""},members:e.members?.nodes||[],status:{label:"Completed",isOverdue:!1,class:"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center",dueWithinSevenDays:!1}}})]:C||[],hideCrewColumn:!0,pageSize:20}):V?ei?(0,r.jsxs)("div",{className:"flex items-center justify-center p-8 text-muted-foreground",children:[r.jsx(p.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading overdue training..."]}):r.jsx(W,{trainingSessionDues:C}):r.jsx(r.Fragment,{children:T?.length>0?r.jsx(h.wQ,{columns:ed,data:T,pageSize:20,onChange:et,showToolbar:!1}):r.jsx("div",{className:"group border-b hover: ",children:r.jsx("div",{className:"p-4 col-span-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-center gap-2 p-2 pt-4",children:[(0,r.jsxs)("svg",{className:"!w-[75px] h-auto",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 147 147.01",children:[r.jsx("path",{d:"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z",fill:"#ffffff",strokeWidth:"0px"}),r.jsx("path",{d:"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z",fill:"#052350",fillRule:"evenodd",opacity:".97",strokeWidth:"0px"}),r.jsx("path",{d:"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z",fill:"#2998e9",strokeWidth:"0px"}),r.jsx("path",{d:"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z",fill:"#2998e9",strokeWidth:"0px"}),r.jsx("path",{d:"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z",fill:"#024450",strokeWidth:"0px"}),r.jsx("path",{d:"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z",fill:"#052451",strokeWidth:"0px"}),r.jsx("path",{d:"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z",fill:"#052250",strokeWidth:"0px"}),r.jsx("path",{d:"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z",fill:"#2998e9",strokeWidth:"0px"}),r.jsx("path",{d:"M45.34,125.8h23.84v6.63h-23.84v-6.63Z",fill:"#052350",strokeWidth:"0"}),r.jsx("path",{d:"M70.17,125.8h6.58v6.63h-6.58v-6.63Z",fill:"#052250",strokeWidth:"0"}),r.jsx("path",{d:"M77.77,125.8h23.84v6.63h-23.84v-6.63Z",fill:"#052350",strokeWidth:"0"}),r.jsx("path",{d:"M67.98,127.01v4.2h-21.42v-4.2h21.42Z",fill:"#2a99ea",strokeWidth:"0"}),r.jsx("path",{d:"M75.58,127.01v4.2h-4.2v-4.2h4.2Z",fill:"#2a99ea",strokeWidth:"0"}),r.jsx("path",{d:"M78.99,127.01h21.42v4.2h-21.42v-4.2Z",fill:"#2a99ea",strokeWidth:"0"}),r.jsx("path",{d:"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z",fill:"#ffffff",strokeWidth:"0"})]}),r.jsx("p",{className:"  ",children:"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!"})]})})})}),!v.includes("overdueToggle")&&r.jsx("button",{className:"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-background border border-border p-5 text-center hover:text-light-blue-vivid-900 w-full",onClick:()=>U(e=>!e),children:V?"View all completed trainings":"View overdue trainings"})]})},W=({trainingSessionDues:e,isVesselView:s=!1,hideCrewColumn:t=!1,pageSize:i=20})=>{let a=(0,g.ac)("(min-width: 720px)"),l=(0,h.wu)([{accessorKey:"title",header:"Training",cell:({row:e})=>{let s=e.original;return r.jsx(D,{data:s,type:"overdue"})}},{accessorKey:"vessel",cellAlignment:"left",header:"Vessel",breakpoint:"landscape",cell:({row:e})=>{let t=e.original;return r.jsx(r.Fragment,{children:!1==s&&r.jsx("div",{className:"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left",children:t.vessel?.title||""})})},sortingFn:(e,s)=>{let t=e?.original?.vessel?.title||"",r=s?.original?.vessel?.title||"";return t.localeCompare(r)}},{accessorKey:"crew",cellAlignment:"right",header:"Crew",breakpoint:"laptop",cell:({row:e})=>{let s=e.original,t=s.members||[];return a?r.jsx("div",{className:"flex gap-1",children:t.map(e=>(0,r.jsxs)(x.u,{children:[r.jsx(x.aJ,{children:r.jsx(x.qE,{size:"sm",variant:s.status.isOverdue?"destructive":"secondary",children:r.jsx(x.Q5,{className:"text-sm",children:(0,x.xE)(e.firstName,e.surname)})})}),(0,r.jsxs)(x._v,{children:[e.firstName," ",e.surname??""]})]},e.id))}):r.jsx("div",{className:(0,v.cn)("!rounded-full size-10",s.status?.class),children:t.length})},sortingFn:(e,s)=>{let t=`${e?.original?.members?.nodes?.[0]?.firstName??""} ${e?.original?.members?.nodes?.[0]?.surname??""}`||"",r=`${s?.original?.members?.nodes?.[0]?.firstName??""} ${s?.original?.members?.nodes?.[0]?.surname??""}`||"";return t.localeCompare(r)}},{accessorKey:"status",cellAlignment:"right",header:"Status",breakpoint:"landscape",cell:({row:e})=>{let s=e.original;return r.jsx(x.OE,{isOverdue:s.status?.isOverdue,isUpcoming:s.status?.dueWithinSevenDays,label:s.status?.label||"Unknown Status"})}}]),n=t?l.filter(e=>"crew"!==e.accessorKey):l;return r.jsx(r.Fragment,{children:e?.length>0?r.jsx(h.wQ,{columns:n,data:e,pageSize:i,showToolbar:!1}):r.jsx("div",{className:"group border-b hover: ",children:r.jsx("div",{className:"p-4 col-span-4",children:(0,r.jsxs)("div",{className:"flex justify-between items-center gap-2 p-2 pt-4",children:[(0,r.jsxs)("svg",{className:"!w-[75px] h-auto",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 147 147.01",children:[r.jsx("path",{d:"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z",fill:"#ffffff",strokeWidth:"0px"}),r.jsx("path",{d:"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z",fill:"#052350",fillRule:"evenodd",opacity:".97",strokeWidth:"0px"}),r.jsx("path",{d:"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z",fill:"#2998e9",strokeWidth:"0px"}),r.jsx("path",{d:"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z",fill:"#2998e9",strokeWidth:"0px"}),r.jsx("path",{d:"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z",fill:"#024450",strokeWidth:"0px"}),r.jsx("path",{d:"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z",fill:"#052451",strokeWidth:"0px"}),r.jsx("path",{d:"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z",fill:"#052250",strokeWidth:"0px"}),r.jsx("path",{d:"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z",fill:"#2998e9",strokeWidth:"0px"}),r.jsx("path",{d:"M45.34,125.8h23.84v6.63h-23.84v-6.63Z",fill:"#052350",strokeWidth:"0"}),r.jsx("path",{d:"M70.17,125.8h6.58v6.63h-6.58v-6.63Z",fill:"#052250",strokeWidth:"0"}),r.jsx("path",{d:"M77.77,125.8h23.84v6.63h-23.84v-6.63Z",fill:"#052350",strokeWidth:"0"}),r.jsx("path",{d:"M67.98,127.01v4.2h-21.42v-4.2h21.42Z",fill:"#2a99ea",strokeWidth:"0"}),r.jsx("path",{d:"M75.58,127.01v4.2h-4.2v-4.2h4.2Z",fill:"#2a99ea",strokeWidth:"0"}),r.jsx("path",{d:"M78.99,127.01h21.42v4.2h-21.42v-4.2Z",fill:"#2a99ea",strokeWidth:"0"}),r.jsx("path",{d:"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z",fill:"#ffffff",strokeWidth:"0"})]}),r.jsx("p",{className:"  ",children:"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!"})]})})})})}}};