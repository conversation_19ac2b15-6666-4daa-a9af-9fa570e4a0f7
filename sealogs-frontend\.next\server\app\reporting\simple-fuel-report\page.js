(()=>{var e={};e.id=7180,e.ids=[7180],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},50183:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>f,tree:()=>c}),s(74580),s(71238),s(78398),s(57757),s(48045);var r=s(40060),n=s(33581),l=s(57567),o=s.n(l),a=s(51650),i={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);s.d(t,i);let c=["",{children:["reporting",{children:["simple-fuel-report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,74580)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\simple-fuel-report\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\simple-fuel-report\\page.tsx"],d="/reporting/simple-fuel-report/page",p={require:s,loadChunk:()=>Promise.resolve()},f=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/reporting/simple-fuel-report/page",pathname:"/reporting/simple-fuel-report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88332:(e,t,s)=>{Promise.resolve().then(s.bind(s,44517))},11243:(e,t,s)=>{Promise.resolve().then(s.bind(s,44115))},91973:(e,t,s)=>{"use strict";s.d(t,{u:()=>r});let r=(e,t="report.csv")=>{let s=new Blob([e.map(e=>e.join(",")).join("\n")],{type:"text/csv;charset=utf-8,"}),r=URL.createObjectURL(s);Object.assign(document.createElement("a"),{href:r,download:t}).click(),URL.revokeObjectURL(r)}},2604:(e,t,s)=>{"use strict";s.d(t,{S:()=>o});var r=s(9707),n=s(58774),l=s.n(n);function o(e,t){let s=new r.default(t);l()(s,{head:e.headers,body:e.body,foot:e.footers,margin:5,...e.userOptions}),s.save(e.fileName||"report.pdf")}},44517:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(98768);s(60343);var n=s(32993),l=s(64837);function o({children:e}){return r.jsx(n.default,{children:r.jsx(l.Z,{children:e})})}},44115:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(98768);s(46776);var n=s(26100),l=s(37042),o=s(60343),a=s(79418),i=s(88006),c=s(83179),u=s.n(c),d=s(91973),p=s(2604),f=s(49517),x=s(69424),m=s(26659),g=s(39544),h=s(25394);let j=["Vessel","Log Entry","Fuel Tank","Fuel Start","Fuel Added","Fuel End","Fuel Used","Comments"];function v(){let e=(0,x.useRouter)(),[t,s]=(0,o.useState)([]),[n,c]=(0,o.useState)({startDate:new Date,endDate:new Date}),[v,{called:b,loading:N,data:S}]=(0,a.t)(i.FG,{fetchPolicy:"cache-and-network",onError:e=>{console.error("queryLogBookEntrySections error",e)}}),D=(0,o.useMemo)(()=>{let e=S?.readLogBookEntries?.nodes??[];if(0===e.length)return[];let t=e.filter(function(e){return e.fuelLog.nodes.length>0&&"0"!==e.vehicle.id});if(0===t.length)return[];let s=[];return t.forEach(e=>{if("Locked"!==e.state)return;let t=new Date(e.startDate),r=e.vehicle,n=e.fuelLog.nodes.filter(e=>0!=e.fuelTank.id),l=n.reduce((e,t)=>({...e,[t.fuelTank.id]:t.fuelTank.title}),{}),o=e.logBookEntrySections.nodes,a=o.reduce((e,t)=>[...e,...t.tripEvents.nodes],[]),i=o.reduce((e,t)=>[...e,...t.sectionMemberComments.nodes],[]).map(e=>e?.comment).filter(e=>null!=e||""!=e);for(let o in l)if(Object.prototype.hasOwnProperty.call(l,o)){let c=l[o],u=n.filter(e=>e.fuelTankID==o),d=u[0],p=u[u.length-1],f=d?.fuelBefore??0,x=function(e,t){return e.map(function(e){return"RefuellingBunkering"===e.eventCategory?k(e.eventType_RefuellingBunkering.fuelLog.nodes,t):"Tasking"===e.eventCategory?k(e.eventType_Tasking.fuelLog.nodes,t):"PassengerDropFacility"===e.eventCategory?k(e.eventType_PassengerDropFacility.fuelLog.nodes,t):0}).reduce((e,t)=>e+t,0)}(a,o),m=p?.fuelAfter??0,g=f+x-m,h={logBookEntryID:e.id,vesselID:r.id,logbookDate:t,vesselName:r.title,fuelTankID:Number(o),fuelTankName:c,fuelStart:f,fuelAdded:x,fuelEnd:m,fuelUsed:g,comments:i.join(", ")};s.push(h)}}),s},[S,b,N]);return(0,r.jsxs)(r.Fragment,{children:[r.jsx(h.Bu,{title:"Simple Fuel Report",actions:r.jsx("div",{className:"flex",children:r.jsx(g.Button,{variant:"back",onClick:()=>e.push("/reporting"),children:"Back"})})}),(0,r.jsxs)(h.Zb,{className:"mt-8",children:[(0,r.jsxs)(h.Ol,{className:"gap-4",children:[r.jsx(l.Z,{onChange:({type:e,data:t})=>{switch(e){case"dateRange":c(t);break;case"vessels":s(t)}},onClick:()=>{let e={};null!==n.startDate&&null!==n.endDate&&(e.startDate={gte:n.startDate,lte:n.endDate}),t.length>0&&(e.vehicleID={in:t.map(e=>e.value)}),v({variables:{filter:e}})}}),r.jsx(f.Z,{onDownloadPdf:()=>{if(0===D.length)return;let e=D.map(function(e){return[e.vesselName+"",u()(e.logbookDate).format("DD/MM/YY")+"",e.fuelTankName+"",e.fuelStart.toLocaleString(),e.fuelAdded.toLocaleString(),e.fuelEnd.toLocaleString(),e.fuelUsed.toLocaleString(),`${e.comments??""} `]}),t=D.reduce((e,t)=>e+t.fuelUsed,0);(0,p.S)({headers:[[{content:"Vessel"},{content:"Log Entry"},{content:"Fuel Tank"},{content:"Fuel Start"},{content:"Fuel Added"},{content:"Fuel End"},{content:"Fuel Used"},{content:"Comments",styles:{cellWidth:60}}]],body:e,footers:[[{colSpan:6,content:"Total Fuel Used"},{content:t.toLocaleString()},{content:""}]],userOptions:{showFoot:"lastPage"}})},onDownloadCsv:()=>{if(0===D.length)return;let e=[];e.push(["vessel","log entry","fuel tank","fuel start","fuel added","fuel end","fuel used","comments"]),D.forEach(t=>{e.push([t.vesselName,t.logbookDate.toISOString(),t.fuelTankName,t.fuelStart,t.fuelAdded,t.fuelEnd,t.fuelUsed,t.comments??""])}),(0,d.u)(e)}})]}),r.jsx(h.aY,{children:(0,r.jsxs)(m.iA,{children:[r.jsx(m.xD,{children:r.jsx(m.SC,{children:j.map(e=>r.jsx(m.ss,{children:e},e))})}),r.jsx(y,{isLoading:b&&N,reportData:D})]})})]})]})}function y({reportData:e,isLoading:t}){let s=(0,o.useMemo)(()=>e.reduce((e,t)=>t.fuelUsed+e,0),[e]);return t?r.jsx(m.SC,{children:r.jsx(m.pj,{colSpan:j.length,className:"text-center  h-32",children:"Loading..."})}):0==e.length?r.jsx(m.SC,{children:r.jsx(m.pj,{colSpan:j.length,className:"text-center  h-32",children:"No Data Available"})}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(m.RM,{children:e.map((e,t)=>(0,r.jsxs)(m.SC,{className:"group border-b  hover: ",children:[r.jsx(m.pj,{className:"px-2 py-3 text-left w-[15%]",children:r.jsx("div",{className:" inline-block ml-3",children:e.vesselName})}),r.jsx(m.pj,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:" inline-block ",children:u()(e.logbookDate).format("DD/M/YY")})}),r.jsx(m.pj,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:" inline-block ",children:e.fuelTankName})}),r.jsx(m.pj,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:" inline-block ",children:e.fuelStart.toLocaleString()})}),r.jsx(m.pj,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:" inline-block ",children:e.fuelAdded.toLocaleString()})}),r.jsx(m.pj,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:" inline-block ",children:e.fuelEnd.toLocaleString()})}),r.jsx(m.pj,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:" inline-block ",children:e.fuelUsed.toLocaleString()})}),r.jsx(m.pj,{className:"px-2 py-3 text-left w-[25%]",children:r.jsx("div",{className:" inline-block ",children:e.comments})})]},`${e.logBookEntryID}-${e.fuelTankID}-${e.vesselID}`))}),r.jsx(m.yt,{children:(0,r.jsxs)(m.SC,{className:"group border-b",children:[r.jsx(m.pj,{className:"px-2 py-3 text-left",scope:"col",colSpan:6,children:r.jsx("div",{className:"inline-block ml-3",children:"Total Fuel Used"})}),r.jsx(m.pj,{className:"px-2 py-3 text-left w-[10%]",children:r.jsx("div",{className:"inline-block ",children:s.toLocaleString()})}),r.jsx(m.pj,{className:"px-2 py-3 text-left w-[25%]"})]})})]})}function k(e,t){return 0===e.length?0:e.filter(e=>e.fuelTankID==t).reduce((e,t)=>e+t.fuelAdded,0)}function b(){let[e,t]=(0,o.useState)(!1),[s,l]=(0,o.useState)(!1);return e&&s?r.jsx(v,{}):e?r.jsx(n.Z,{errorMessage:"Oops You do not have the permission to view this section."}):r.jsx(n.Z,{})}},49517:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var r=s(98768),n=s(39544),l=s(76915);function o({onDownloadCsv:e,onDownloadPdf:t}){return(0,r.jsxs)("div",{className:"flex gap-3 mb-3",children:[e&&r.jsx(n.Button,{iconLeft:l.Z,type:"button",onClick:e,children:"Download CSV"}),t&&r.jsx(n.Button,{iconLeft:l.Z,type:"button",onClick:t,children:"Download PDF"})]})}},71238:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)},74580:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\simple-fuel-report\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,9707,6250,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042],()=>s(50183));module.exports=r})();