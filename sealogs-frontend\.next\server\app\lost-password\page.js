(()=>{var e={};e.id=978,e.ids=[978],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},16281:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d}),t(18003),t(78398),t(57757),t(48045);var a=t(40060),l=t(33581),r=t(57567),i=t.n(r),o=t(51650),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(s,n);let d=["",{children:["lost-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,18003)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\lost-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\lost-password\\page.tsx"],u="/lost-password/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/lost-password/page",pathname:"/lost-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},28702:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,68472,23)),Promise.resolve().then(t.t.bind(t,28029,23)),Promise.resolve().then(t.bind(t,36330))},36330:(e,s,t)=>{"use strict";t.d(s,{default:()=>p});var a=t(98768),l=t(98771),r=t(66263),i=t(60343),o=t(72548),n=t(76342),d=t(71496),c=t(66314),u=t(42106),m=t(57906),h=t(39544),x=t(71890);let g=d.z.object({email:d.z.string()}),p=()=>{let[e,s]=(0,i.useState)(""),[t,d]=(0,i.useState)(!1),[p,f]=(0,i.useState)(""),[j,{loading:v}]=(0,o.D)(n.rd7,{onCompleted:()=>{s(""),d(!0)},onError:e=>{s(e.message)}});async function w(e){s(""),f(e.email),await j({variables:{email:e.email}})}let b=(0,c.cI)({resolver:(0,u.F)(g),defaultValues:{email:""}});return(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center z-10",children:[a.jsx(r.default,{href:"/",className:" lg:hidden",children:a.jsx(l.Z,{color:"white"})}),!t&&a.jsx(m.Form,{...b,children:a.jsx("form",{onSubmit:b.handleSubmit(w),className:"flex flex-col items-center justify-center lg:w-96 px-6 py-8",children:(0,a.jsxs)("div",{className:"w-full max-w-md bg-card p-6 shadow rounded-lg",children:[(0,a.jsxs)("div",{className:"flex flex-col space-y-2 text-center mb-4",children:[a.jsx("h1",{className:"text-2xl  tracking-tight",children:"Lost Password"}),a.jsx("p",{className:" text-muted-foreground",children:"Enter your e-mail address and we will send you a link with which you can reset your password"})]}),a.jsx(m.FormField,{control:b.control,name:"email",render:({field:e})=>(0,a.jsxs)(m.FormItem,{children:[a.jsx(m.FormControl,{children:a.jsx(x.I,{type:"email",placeholder:"Enter your email address",...e})}),a.jsx(m.FormMessage,{})]})}),e&&a.jsx("div",{className:"text-center text-red-500",children:e}),a.jsx("div",{className:"flex flex-col items-center space-y-4",children:a.jsx(h.Button,{className:"w-full",type:"submit",disabled:v,children:v?"Sending email...":(0,a.jsxs)("p",{children:["Send me the",a.jsx("span",{className:"hidden sm:inline",children:"password"})," ",a.jsx("span",{className:"hidden xs:inline",children:"reset"})," ","link"]})})})]})})}),t&&(0,a.jsxs)("div",{className:" shadow rounded py-8 px-4 w-80 md:w-96 ",children:[(0,a.jsxs)("h1",{className:" text-bold mb-4 ",children:["Password reset link sent to '",p,"'"]}),(0,a.jsxs)("div",{className:" mb-4",children:["Thank you! A reset link has been sent to '",p,"', provided an account exists for this email address."]})]})]})}},98771:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});var a=t(98768),l=t(28147);let r=function({color:e="default"}){return a.jsx("div",{children:"white"===e?a.jsx(l.default,{src:"/sealogs-horizontal-logo-white.png",alt:"Sealogs Logo",width:300,height:92,className:""}):a.jsx(l.default,{src:"/sealogs-horizontal-logo.png",alt:"Sealogs Logo",width:300,height:92,className:""})})}},87638:(e,s,t)=>{"use strict";t.d(s,{default:()=>l.a});var a=t(9312),l=t.n(a)},9312:(e,s,t)=>{"use strict";let{createProxy:a}=t(38851);e.exports=a("C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\node_modules\\.pnpm\\next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4\\node_modules\\next\\dist\\client\\link.js")},18003:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(96141),l=t(87638);let r=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\lost-password\form.tsx#default`);var i=t(17784),o=t(82205);let n=()=>(0,a.jsxs)("div",{className:"lg:container relative lg:mx-auto w-full h-svh md:grid lg:max-w-none lg:grid-cols-2 lg:px-0",children:[(0,a.jsxs)("div",{className:"relative hidden h-full flex-col bg-muted p-10  dark:border-r lg:flex",children:[a.jsx(l.default,{href:"/",className:"hidden z-10 lg:block absolute top-4 left-4",children:a.jsx(o.Z,{color:"white"})}),a.jsx(i.default,{src:"/bg_image/SealogBG_image.avif",width:1920,height:723,alt:"Authentication",className:" absolute object-cover size-full inset-0"})]}),(0,a.jsxs)("div",{className:"size-full flex relative flex-col justify-center items-center",children:[a.jsx(i.default,{src:"/bg_image/SealogBG_image.avif",width:1920,height:723,alt:"Authentication",className:"lg:hidden absolute object-cover size-full"}),a.jsx(r,{})]})]})},82205:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});var a=t(96141),l=t(17784);let r=function({color:e="default"}){return a.jsx("div",{children:"white"===e?a.jsx(l.default,{src:"/sealogs-horizontal-logo-white.png",alt:"Sealogs Logo",width:300,height:92,className:""}):a.jsx(l.default,{src:"/sealogs-horizontal-logo.png",alt:"Sealogs Logo",width:300,height:92,className:""})})}},88570:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var a=t(3563);let l=e=>[{type:"image/x-icon",sizes:"152x152",url:(0,a.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[864,8865,3563,6263,8189,1264,6451,2925,6342],()=>t(16281));module.exports=a})();