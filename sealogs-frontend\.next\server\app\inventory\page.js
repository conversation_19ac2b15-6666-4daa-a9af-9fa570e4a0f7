(()=>{var e={};e.id=1268,e.ids=[1268],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},1805:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>g,tree:()=>c}),t(77603),t(2223),t(78398),t(57757),t(48045);var s=t(40060),n=t(33581),i=t(57567),a=t.n(i),o=t(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let c=["",{children:["inventory",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,77603)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,2223)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\page.tsx"],d="/inventory/page",p={require:t,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/inventory/page",pathname:"/inventory",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},85363:(e,r,t)=>{Promise.resolve().then(t.bind(t,71462))},22580:(e,r,t)=>{Promise.resolve().then(t.bind(t,85630))},71462:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(98768);t(60343);var n=t(64837);function i({children:e}){return s.jsx(n.Z,{children:e})}},85630:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>b});var s=t(98768),n=t(60343),i=t(39544),a=t(15580),o=t(13842),l=t(79418),c=t(26100),u=t(66263),d=t(7678),p=t.n(d),g=t(14826),m=t.n(g),x=t(94060),v=t(69424),h=t(51742),f=t(30905),y=t(39650),j=t(94446),w=t(27514),C=t(22995),q=t(9210);let P=()=>{let{isMobile:e}=(0,C.Ap)();return(0,s.jsxs)(w.DropdownMenu,{children:[s.jsx(w.DropdownMenuTrigger,{asChild:!0,children:s.jsx(q.HV,{size:36})}),s.jsx(w.DropdownMenuContent,{side:e?"bottom":"right",align:e?"end":"start",children:s.jsx(u.default,{href:"/inventory/new",children:s.jsx(w.DropdownMenuItem,{children:"New Inventory Item"})})})]})};var A=t(25394);function S(){(0,v.useRouter)();let e=(0,v.usePathname)(),r=(0,v.useSearchParams)(),[t,d]=(0,n.useState)([]),[g,w]=(0,n.useState)(null),[C,q]=(0,n.useState)({}),[S,b]=(0,n.useState)([]),[N,M]=(0,n.useState)(!0),[_,L]=(0,n.useState)(1),[I,T]=(0,n.useState)(1),[R]=(0,l.t)(x.HV,{fetchPolicy:"cache-and-network",onCompleted:e=>{let r=e.readInventories.nodes;r&&(d(r),T(Math.ceil(e.readInventories.pageInfo.totalCount/20)))},onError:e=>{console.error("queryInventories error",e)}});(0,o.Fb)(w);let B=async(e={},r=S)=>{if(r.length>0){let t=r.map(async r=>await R({variables:{filter:{...e,...r},offset:(_-1)*20}})),s=await Promise.all(t);d(s=(s=(s=s.filter(e=>e.data.readInventories.nodes.length>0)).flatMap(e=>e.data.readInventories.nodes)).filter((e,r,t)=>t.findIndex(r=>r.id===e.id)===r))}else d([]),await R({variables:{filter:e,offset:(_-1)*20}})},V=(0,h.wu)([{accessorKey:"title",header:"Item",cell:({row:t})=>{let n=t.original;return s.jsx(u.default,{href:`/inventory/view/?id=${n.id}&redirect_to=${e}?${r.toString()}&tab=inventory`,className:"flex items-center",children:n.quantity+" x "+n.item})},filterFn:(e,r,t)=>(e.original.item||"").toLowerCase().includes(t.toLowerCase())},{accessorKey:"location",header:({column:e})=>s.jsx(f.u,{column:e,title:"Location"}),cell:({row:e})=>{let r=e.original;return s.jsx("div",{children:r.vessel?.title||r.location||"N/A"})},cellAlignment:"left",filterFn:(e,r,t)=>{let s=e.original;return(s.vessel?.title||s.location||"").toLowerCase().includes(t.toLowerCase())},sortingFn:(e,r)=>{let t=e?.original?.vessel?.title||e?.original?.location||"",s=r?.original?.vessel?.title||r?.original?.location||"";return t.localeCompare(s)}},{accessorKey:"maintenance",header:"Maintenance",cell:({row:e})=>{let r=(e=>{let r=e.componentMaintenanceChecks?.nodes||[];if(0===r.length)return null;let t=r.filter(e=>!e?.archived).filter(e=>"High"===(0,o.AT)(e).status);return t.length>0?{type:"overdue",count:t.length}:{type:"good"}})(e.original)||{type:"good"};return s.jsx(s.Fragment,{children:r?.type==="overdue"?s.jsx(A.Ct,{variant:"destructive",children:r.count}):r?.type==="good"?s.jsx(A.Ct,{variant:"success",children:s.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"#27AB83","aria-hidden":"true",children:s.jsx("path",{fillRule:"evenodd",d:"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z",clipRule:"evenodd"})})}):null})}},{accessorKey:"categories",header:"Categories",cellAlignment:"left",cell:({row:e})=>{let r=e.original;return(0,s.jsxs)("div",{className:"flex gap-2 items-center",children:[r.categories?.nodes?.slice(0,2).map((e,r)=>s.jsx(A.Ct,{type:"normal",variant:"outline",className:"font-normal",children:e.name},String(r))),r.categories?.nodes?.length>2&&(0,s.jsxs)(a.Popover,{children:[s.jsx(a.PopoverTrigger,{asChild:!0,children:(0,s.jsxs)(i.Button,{variant:"outline",className:"!p-2 bg-transparent",children:["+"," ",r.categories.nodes.length-2," ","more"]})}),s.jsx(a.PopoverContent,{className:"w-80",children:(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(A.P,{className:"font-medium text-sm",children:"All Categories"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:r.categories.nodes.map((e,r)=>s.jsx(A.Ct,{type:"normal",variant:"outline",className:"font-normal",children:e.name},String(r)))})]})})]})]})}},{accessorKey:"suppliers",header:({column:e})=>s.jsx(f.u,{column:e,title:"Suppliers"}),cellAlignment:"right",cell:({row:e})=>{let r=e.original;return s.jsx("div",{className:"flex flex-col",children:r.suppliers?.nodes?.map(e=>s.jsx("div",{children:s.jsx(u.default,{href:`/inventory/suppliers/view?id=${e.id}`,children:e.name})},String(e.id)))})},filterFn:(e,r,t)=>{let s=e.original;return!t||(s.suppliers?.nodes||[]).map(e=>e.name.toLowerCase()).join(" ").includes(t.toLowerCase())},sortingFn:(e,r)=>{let t=e?.original?.suppliers?.nodes?.[0]?.name||"",s=r?.original?.suppliers?.nodes?.[0]?.name||"";return t.localeCompare(s)}}]);return(0,s.jsxs)(s.Fragment,{children:[s.jsx(y.ListHeader,{icon:s.jsx(j.A,{className:"h-12 w-12 ring-1 bg-curious-blue-50 p-1 rounded-full"}),title:"All inventory",actions:s.jsx(P,{})}),s.jsx("div",{className:"mt-16",children:N?s.jsx(c.Z,{}):s.jsx(h.wQ,{columns:V,data:t,showToolbar:!0,pageSize:20,onChange:({type:e,data:r})=>{let t={...C};"vessel"===e&&(Array.isArray(r)&&r.length>0?t.vesselID={in:r.map(e=>+e.value)}:r&&!Array.isArray(r)?t.vesselID={eq:+r.value}:delete t.vesselID),"supplier"===e&&(Array.isArray(r)&&r.length>0?t.suppliers={id:{in:r.map(e=>+e.value)}}:r&&!Array.isArray(r)?t.suppliers={id:{in:[+r.value]}}:delete t.suppliers),"category"===e&&(Array.isArray(r)&&r.length>0?t.categories={id:{eq:r.map(e=>String(e.value))}}:r&&!Array.isArray(r)?t.categories={id:{eq:String(r.value)}}:delete t.categories),"keyword"===e&&(p()(m()(r?.value))?b([]):b([{item:{contains:r.value}},{title:{contains:r.value}},{productCode:{contains:r.value}},{description:{contains:r.value}},{comments:{contains:r.value}}])),q(t),L(1),B(t,S)}})})]})}function b(){return s.jsx(S,{})}},2223:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\inventory\layout.tsx#default`)},77603:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\inventory\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7944],()=>t(1805));module.exports=s})();