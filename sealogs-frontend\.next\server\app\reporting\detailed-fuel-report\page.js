(()=>{var e={};e.id=428,e.ids=[428],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},42348:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,originalPathname:()=>p,pages:()=>u,routeModule:()=>g,tree:()=>d}),r(76757),r(71238),r(78398),r(57757),r(48045);var s=r(40060),i=r(33581),n=r(57567),a=r.n(n),o=r(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["reporting",{children:["detailed-fuel-report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,76757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\detailed-fuel-report\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\detailed-fuel-report\\page.tsx"],p="/reporting/detailed-fuel-report/page",c={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/reporting/detailed-fuel-report/page",pathname:"/reporting/detailed-fuel-report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},37986:(e,t,r)=>{Promise.resolve().then(r.bind(r,70345))},88332:(e,t,r)=>{Promise.resolve().then(r.bind(r,44517))},75546:(e,t,r)=>{"use strict";r.d(t,{Br:()=>c,fU:()=>g,o0:()=>u,p6:()=>d,vq:()=>p});var s=r(83179),i=r.n(s),n=r(7678),a=r.n(n),o=r(14826),l=r.n(o);let d=(e="",t=!0)=>{let r;if(a()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,s,i]=e.split("-"),n=t?r.slice(-2):r,a=parseInt(i,10).toString().padStart(2,"0"),o=parseInt(s,10).toString().padStart(2,"0");return`${a}/${o}/${n}`}if(!(r=e&&"object"==typeof e?i()(e.toString()):i()(e)).isValid())return"";let s=r.format("DD"),n=r.format("MM"),o=t?r.format("YY"):r.format("YYYY");return`${s}/${n}/${o}`},u=(e="",t=!0)=>{let r;if(a()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,s,i]=e.split("-"),n=t?r.slice(-2):r,a=parseInt(i,10).toString().padStart(2,"0"),o=parseInt(s,10).toString().padStart(2,"0");return`${a}/${o}/${n} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[r,s]=e.split(" "),[i,n,a]=r.split("-"),o=t?i.slice(-2):i,l=s.split(":"),d=l[0].padStart(2,"0"),u=l[1].padStart(2,"0"),p=parseInt(a,10).toString().padStart(2,"0"),c=parseInt(n,10).toString().padStart(2,"0");return`${p}/${c}/${o} ${d}:${u}`}if(!(r=e&&"object"==typeof e?i()(e.toString()):i()(e)).isValid())return"";let s=r.format("DD"),n=r.format("MM"),o=t?r.format("YY"):r.format("YYYY"),d=r.format("HH:mm");return`${s}/${n}/${o} ${d}`},p=(e="")=>a()(l()(e))?"":i()(e).format("YYYY-MM-DD HH:mm:ss"),c=(e="")=>a()(l()(e))?new Date:new Date(`${e}T10:00:00Z`),g=(e,t)=>{let r=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),s=e=>e.includes(" ")?e.replace(" ","T"):e,i=e=>{if(!e||"string"!=typeof e)return null;if(r(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(s(e))},n=i(e),a=i(t);return!n||!a||isNaN(n.getTime())||isNaN(a.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):a>n}},70345:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(98768),i=r(37042),n=r(75546),a=r(94060),o=r(79418),l=r(83179),d=r.n(l),u=r(69424),p=r(60343),c=r(89546),g=r(25394);let f=["Date","Fuel Tank","Fuel used"];function x(){let e=(0,u.useRouter)(),[t,r]=(0,p.useState)(""),[l,x]=(0,p.useState)(!1),[h,m]=(0,p.useState)([]),[v]=(0,o.t)(a.Js,{onCompleted:e=>{var t=[];e.readOneVessel.logBookEntries.nodes?.map(e=>e.logBookEntrySections.nodes?.map(e=>{t.push(e.id)})),j({variables:{id:t}})},onError:e=>{console.error(e)}}),[j]=(0,o.t)(a.Zl,{onCompleted:e=>{let t=e.readTripReport_LogBookEntrySections.nodes;var r=[];t.forEach(e=>{var t=[];e.tripEvents.nodes.forEach(r=>{"PassengerDropFacility"===r.eventCategory&&r.eventType_PassengerDropFacility.fuelLog.nodes.length>0&&t.push({id:e.id,fuelLog:r.eventType_PassengerDropFacility.fuelLog.nodes,created:e.created,category:r.eventCategory}),"Tasking"===r.eventCategory&&r.eventType_Tasking.fuelLog.nodes.length>0&&t.push({id:e.id,fuelLog:r.eventType_Tasking.fuelLog.nodes,created:e.created,category:r.eventType_Tasking.type}),"RefuellingBunkering"===r.eventCategory&&r.eventType_RefuellingBunkering.fuelLog.nodes.length>0&&t.push({id:e.id,fuelLog:r.eventType_RefuellingBunkering.fuelLog.nodes,created:e.created,category:r.eventCategory})}),t.length>0&&r.push({id:e.id,fuelLog:t,created:e.created})}),m(r)},onError:e=>{console.error("TripReport_LogBookEntrySection error",e)}}),y=e=>!l||d()(e.created).isAfter(d()(l.startDate))&&d()(e.created).isBefore(d()(l.endDate)),S=e=>e.fuelBefore===e.fuelAfter&&0===e.fuelAdded,$=e=>s.jsx(s.Fragment,{children:e.fuelLog.filter(e=>e.fuelLog.filter(e=>!S(e)).length>0).map((e,t)=>s.jsx("div",{className:"mb-4",children:s.jsx(c.Z,{headClasses:"  ",headings:[e.category.replace(/([a-z])([A-Z])/g,"$1 $2")+":left","Start","Added","End"],children:e.fuelLog.filter(e=>!S(e)).map((t,r)=>(0,s.jsxs)("tr",{children:[s.jsx("td",{children:s.jsx("div",{className:"text-left p-2",children:t.fuelTank.title})}),s.jsx("td",{children:t.fuelBefore}),s.jsx("td",{children:t.fuelAdded}),s.jsx("td",{children:t.fuelAfter})]},t.fuelTank.id+"-"+e.id+"-"+r))})},e.id+"-"+t))}),L=e=>{let t=e.fuelLog[0].fuelLog.reduce((e,t)=>e+t.fuelBefore,0),r=e.fuelLog[e.fuelLog.length-1].fuelLog.reduce((e,t)=>e+t.fuelAfter,0);return+t+ +e.fuelLog.reduce((e,t)=>e+t.fuelLog.reduce((e,t)=>e+t.fuelAdded,0),0)-+r};return(0,s.jsxs)(s.Fragment,{children:[s.jsx(g.Bu,{title:"Detailed Fuel Report",actions:s.jsx(g.zx,{variant:"back",onClick:()=>e.push("/reporting"),children:"Back"})}),s.jsx(g.Zb,{className:"mt-8",children:(0,s.jsxs)(g.aY,{className:"flex flex-col gap-4",children:[s.jsx(i.Z,{onChange:e=>{"vessel"===e.type&&e.data?.value&&r(e.data?.value),"dateRange"===e.type&&x(e.data)}}),(0,s.jsxs)(g.iA,{children:[s.jsx(g.xD,{children:s.jsx(g.SC,{children:f.map(e=>s.jsx(g.ss,{children:e},e))})}),s.jsx(g.RM,{children:0==h.length?s.jsx(g.SC,{children:s.jsx(g.pj,{colSpan:f.length,className:"text-center h-32",children:"No Data Available"})}):h.filter(e=>y(e)).map(e=>(0,s.jsxs)(g.SC,{children:[s.jsx(g.pj,{children:(0,n.p6)(e.created)}),s.jsx(g.pj,{children:$(e)}),s.jsx(g.pj,{children:L(e)})]},e.id))})]})]})})]})}r(46776);var h=r(26100);function m(){let[e,t]=(0,p.useState)(!1),[r,i]=(0,p.useState)(!1);return e&&r?s.jsx(x,{}):e?s.jsx(h.Z,{errorMessage:"Oops You do not have the permission to view this section."}):s.jsx(h.Z,{})}},44517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(98768);r(60343);var i=r(32993),n=r(64837);function a({children:e}){return s.jsx(i.default,{children:s.jsx(n.Z,{children:e})})}},89546:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var s=r(98768);let i=e=>(0,s.jsxs)("table",{className:" table-auto w-full ",cellPadding:"10",children:[s.jsx("thead",{children:s.jsx("tr",{className:e.showHeader?"":"hidden",children:e.headings.map((t,r)=>s.jsx("th",{scope:"col",className:`pb-3 pt-6 px-2 ${0===r?"rounded-tl-lg":" "}   ${e.headings.length===r+1?"rounded-tr-lg":" "}
                                ${t.includes(":")&&"last"===t.split(":")[1]?"rounded-tr-lg":""}
                                ${t.includes(":")&&"smhidden"===t.split(":")[1]?"hidden sm:block":""}
                                ${t.includes(":")&&"left"===t.split(":")[1]?"text-left":""}
                                ${t.includes(":")&&"firstHead"===t.split(":")[1]?"text-left text-nowrap font-thin  md: lg:text-2xl pl-6 rounded-tl-lg":""}  `,children:t.includes(":")?t.split(":")[0]:t},r))})}),s.jsx("tbody",{className:`  text-foreground ${e?.bodyClass}`,children:e.children})]})},76757:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\detailed-fuel-report\page.tsx#default`)},71238:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042],()=>r(42348));module.exports=s})();