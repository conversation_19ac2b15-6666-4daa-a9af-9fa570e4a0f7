'use client'

import * as React from 'react'
import * as TooltipPrimitive from '@radix-ui/react-tooltip'

import { cn } from '@/app/lib/utils'
import { useIsMobile } from '@/components/hooks/use-mobile'

const TooltipProvider = TooltipPrimitive.Provider

// Enhanced Tooltip component with mobile click support
interface TooltipProps
    extends React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Root> {
    mobileClickable?: boolean
}

const Tooltip: React.FC<TooltipProps> = ({
    children,
    mobileClickable = true,
    ...props
}) => {
    const isMobile = useIsMobile()
    const [open, setOpen] = React.useState(false)

    // Use click behavior on mobile when mobileClickable is true
    const shouldUseClickBehavior = isMobile && mobileClickable

    // Handle click outside to close tooltip on mobile
    React.useEffect(() => {
        if (!shouldUseClickBehavior || !open) return

        const handleClickOutside = (event: MouseEvent) => {
            const target = event.target as Element
            // Check if click is outside tooltip content and trigger
            if (
                !target.closest('[data-radix-tooltip-content]') &&
                !target.closest('[data-radix-tooltip-trigger]')
            ) {
                setOpen(false)
            }
        }

        document.addEventListener('click', handleClickOutside)
        return () => document.removeEventListener('click', handleClickOutside)
    }, [shouldUseClickBehavior, open])

    if (shouldUseClickBehavior) {
        return (
            <TooltipPrimitive.Root
                open={open}
                onOpenChange={setOpen}
                {...props}>
                {children}
            </TooltipPrimitive.Root>
        )
    }

    // Default hover behavior for desktop
    return <TooltipPrimitive.Root {...props}>{children}</TooltipPrimitive.Root>
}

// Enhanced TooltipTrigger with mobile click support
interface TooltipTriggerProps
    extends React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Trigger> {
    mobileClickable?: boolean
}

const TooltipTrigger = React.forwardRef<
    React.ElementRef<typeof TooltipPrimitive.Trigger>,
    TooltipTriggerProps
>(({ mobileClickable = true, onClick, ...props }, ref) => {
    const isMobile = useIsMobile()
    const shouldUseClickBehavior = isMobile && mobileClickable

    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
        if (shouldUseClickBehavior) {
            // Prevent default behavior and let the tooltip state handle opening/closing
            event.preventDefault()
        }
        onClick?.(event)
    }

    return (
        <TooltipPrimitive.Trigger
            ref={ref}
            onClick={handleClick}
            data-radix-tooltip-trigger=""
            {...props}
        />
    )
})
TooltipTrigger.displayName = TooltipPrimitive.Trigger.displayName

const TooltipContent = React.forwardRef<
    React.ElementRef<typeof TooltipPrimitive.Content>,
    React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
    <TooltipPrimitive.Portal>
        <TooltipPrimitive.Content
            ref={ref}
            sideOffset={sideOffset}
            data-radix-tooltip-content=""
            className={cn(
                'z-50 overflow-hidden px-3 py-1.5  text-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 bg-fire-bush-100 text-fire-bush-600 rounded-md border border-fire-bush-500',
                className,
            )}
            {...props}
        />
    </TooltipPrimitive.Portal>
))
TooltipContent.displayName = TooltipPrimitive.Content.displayName

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }
