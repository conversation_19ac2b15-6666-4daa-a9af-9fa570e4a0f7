(()=>{var e={};e.id=2515,e.ids=[2515],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},23833:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>l}),r(61165),r(78398),r(57757),r(48045);var n=r(40060),s=r(33581),a=r(57567),i=r.n(a),o=r(51650),u={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);r.d(t,u);let l=["",{children:["select-client",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,61165)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\select-client\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\select-client\\page.tsx"],d="/select-client/page",h={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/select-client/page",pathname:"/select-client",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},98730:(e,t,r)=>{Promise.resolve().then(r.bind(r,13189))},83179:function(e){var t;t=function(){"use strict";var e="millisecond",t="second",r="minute",n="hour",s="week",a="month",i="quarter",o="year",u="date",l="Invalid Date",c=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,d=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,h=function(e,t,r){var n=String(e);return!n||n.length>=t?e:""+Array(t+1-n.length).join(r)+e},f="en",p={};p[f]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(e){var t=["th","st","nd","rd"],r=e%100;return"["+e+(t[(r-20)%10]||t[r]||"th")+"]"}};var g="$isDayjsObject",m=function(e){return e instanceof x||!(!e||!e[g])},y=function e(t,r,n){var s;if(!t)return f;if("string"==typeof t){var a=t.toLowerCase();p[a]&&(s=a),r&&(p[a]=r,s=a);var i=t.split("-");if(!s&&i.length>1)return e(i[0])}else{var o=t.name;p[o]=t,s=o}return!n&&s&&(f=s),s||!n&&f},v=function(e,t){if(m(e))return e.clone();var r="object"==typeof t?t:{};return r.date=e,r.args=arguments,new x(r)},$={s:h,z:function(e){var t=-e.utcOffset(),r=Math.abs(t);return(t<=0?"+":"-")+h(Math.floor(r/60),2,"0")+":"+h(r%60,2,"0")},m:function e(t,r){if(t.date()<r.date())return-e(r,t);var n=12*(r.year()-t.year())+(r.month()-t.month()),s=t.clone().add(n,a),i=r-s<0,o=t.clone().add(n+(i?-1:1),a);return+(-(n+(r-s)/(i?s-o:o-s))||0)},a:function(e){return e<0?Math.ceil(e)||0:Math.floor(e)},p:function(l){return({M:a,y:o,w:s,d:"day",D:u,h:n,m:r,s:t,ms:e,Q:i})[l]||String(l||"").toLowerCase().replace(/s$/,"")},u:function(e){return void 0===e}};$.l=y,$.i=m,$.w=function(e,t){return v(e,{locale:t.$L,utc:t.$u,x:t.$x,$offset:t.$offset})};var x=function(){function h(e){this.$L=y(e.locale,null,!0),this.parse(e),this.$x=this.$x||e.x||{},this[g]=!0}var f=h.prototype;return f.parse=function(e){this.$d=function(e){var t=e.date,r=e.utc;if(null===t)return new Date(NaN);if($.u(t))return new Date;if(t instanceof Date)return new Date(t);if("string"==typeof t&&!/Z$/i.test(t)){var n=t.match(c);if(n){var s=n[2]-1||0,a=(n[7]||"0").substring(0,3);return r?new Date(Date.UTC(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,a)):new Date(n[1],s,n[3]||1,n[4]||0,n[5]||0,n[6]||0,a)}}return new Date(t)}(e),this.init()},f.init=function(){var e=this.$d;this.$y=e.getFullYear(),this.$M=e.getMonth(),this.$D=e.getDate(),this.$W=e.getDay(),this.$H=e.getHours(),this.$m=e.getMinutes(),this.$s=e.getSeconds(),this.$ms=e.getMilliseconds()},f.$utils=function(){return $},f.isValid=function(){return this.$d.toString()!==l},f.isSame=function(e,t){var r=v(e);return this.startOf(t)<=r&&r<=this.endOf(t)},f.isAfter=function(e,t){return v(e)<this.startOf(t)},f.isBefore=function(e,t){return this.endOf(t)<v(e)},f.$g=function(e,t,r){return $.u(e)?this[t]:this.set(r,e)},f.unix=function(){return Math.floor(this.valueOf()/1e3)},f.valueOf=function(){return this.$d.getTime()},f.startOf=function(e,i){var l=this,c=!!$.u(i)||i,d=$.p(e),h=function(e,t){var r=$.w(l.$u?Date.UTC(l.$y,t,e):new Date(l.$y,t,e),l);return c?r:r.endOf("day")},f=function(e,t){return $.w(l.toDate()[e].apply(l.toDate("s"),(c?[0,0,0,0]:[23,59,59,999]).slice(t)),l)},p=this.$W,g=this.$M,m=this.$D,y="set"+(this.$u?"UTC":"");switch(d){case o:return c?h(1,0):h(31,11);case a:return c?h(1,g):h(0,g+1);case s:var v=this.$locale().weekStart||0,x=(p<v?p+7:p)-v;return h(c?m-x:m+(6-x),g);case"day":case u:return f(y+"Hours",0);case n:return f(y+"Minutes",1);case r:return f(y+"Seconds",2);case t:return f(y+"Milliseconds",3);default:return this.clone()}},f.endOf=function(e){return this.startOf(e,!1)},f.$set=function(s,i){var l,c=$.p(s),d="set"+(this.$u?"UTC":""),h=((l={}).day=d+"Date",l[u]=d+"Date",l[a]=d+"Month",l[o]=d+"FullYear",l[n]=d+"Hours",l[r]=d+"Minutes",l[t]=d+"Seconds",l[e]=d+"Milliseconds",l)[c],f="day"===c?this.$D+(i-this.$W):i;if(c===a||c===o){var p=this.clone().set(u,1);p.$d[h](f),p.init(),this.$d=p.set(u,Math.min(this.$D,p.daysInMonth())).$d}else h&&this.$d[h](f);return this.init(),this},f.set=function(e,t){return this.clone().$set(e,t)},f.get=function(e){return this[$.p(e)]()},f.add=function(e,i){var u,l=this;e=Number(e);var c=$.p(i),d=function(t){var r=v(l);return $.w(r.date(r.date()+Math.round(t*e)),l)};if(c===a)return this.set(a,this.$M+e);if(c===o)return this.set(o,this.$y+e);if("day"===c)return d(1);if(c===s)return d(7);var h=((u={})[r]=6e4,u[n]=36e5,u[t]=1e3,u)[c]||1,f=this.$d.getTime()+e*h;return $.w(f,this)},f.subtract=function(e,t){return this.add(-1*e,t)},f.format=function(e){var t=this,r=this.$locale();if(!this.isValid())return r.invalidDate||l;var n=e||"YYYY-MM-DDTHH:mm:ssZ",s=$.z(this),a=this.$H,i=this.$m,o=this.$M,u=r.weekdays,c=r.months,h=r.meridiem,f=function(e,r,s,a){return e&&(e[r]||e(t,n))||s[r].slice(0,a)},p=function(e){return $.s(a%12||12,e,"0")},g=h||function(e,t,r){var n=e<12?"AM":"PM";return r?n.toLowerCase():n};return n.replace(d,function(e,n){return n||function(e){switch(e){case"YY":return String(t.$y).slice(-2);case"YYYY":return $.s(t.$y,4,"0");case"M":return o+1;case"MM":return $.s(o+1,2,"0");case"MMM":return f(r.monthsShort,o,c,3);case"MMMM":return f(c,o);case"D":return t.$D;case"DD":return $.s(t.$D,2,"0");case"d":return String(t.$W);case"dd":return f(r.weekdaysMin,t.$W,u,2);case"ddd":return f(r.weekdaysShort,t.$W,u,3);case"dddd":return u[t.$W];case"H":return String(a);case"HH":return $.s(a,2,"0");case"h":return p(1);case"hh":return p(2);case"a":return g(a,i,!0);case"A":return g(a,i,!1);case"m":return String(i);case"mm":return $.s(i,2,"0");case"s":return String(t.$s);case"ss":return $.s(t.$s,2,"0");case"SSS":return $.s(t.$ms,3,"0");case"Z":return s}return null}(e)||s.replace(":","")})},f.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},f.diff=function(e,u,l){var c,d=this,h=$.p(u),f=v(e),p=(f.utcOffset()-this.utcOffset())*6e4,g=this-f,m=function(){return $.m(d,f)};switch(h){case o:c=m()/12;break;case a:c=m();break;case i:c=m()/3;break;case s:c=(g-p)/6048e5;break;case"day":c=(g-p)/864e5;break;case n:c=g/36e5;break;case r:c=g/6e4;break;case t:c=g/1e3;break;default:c=g}return l?c:$.a(c)},f.daysInMonth=function(){return this.endOf(a).$D},f.$locale=function(){return p[this.$L]},f.locale=function(e,t){if(!e)return this.$L;var r=this.clone(),n=y(e,t,!0);return n&&(r.$L=n),r},f.clone=function(){return $.w(this.$d,this)},f.toDate=function(){return new Date(this.valueOf())},f.toJSON=function(){return this.isValid()?this.toISOString():null},f.toISOString=function(){return this.$d.toISOString()},f.toString=function(){return this.$d.toUTCString()},h}(),S=x.prototype;return v.prototype=S,[["$ms",e],["$s",t],["$m",r],["$H",n],["$W","day"],["$M",a],["$y",o],["$D",u]].forEach(function(e){S[e[1]]=function(t){return this.$g(t,e[0],e[1])}}),v.extend=function(e,t){return e.$i||(e(t,x,v),e.$i=!0),v},v.locale=y,v.isDayjs=m,v.unix=function(e){return v(1e3*e)},v.en=p[f],v.Ls=p,v.p={},v},e.exports=t()},18479:e=>{e.exports=function(e,t){for(var r=-1,n=null==e?0:e.length,s=Array(n);++r<n;)s[r]=t(e[r],r,e);return s}},22060:(e,t,r)=>{var n=r(51858),s=r(18479),a=r(55813),i=r(15903),o=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(a(t))return s(t,e)+"";if(i(t))return l?l.call(t):"";var r=t+"";return"0"==r&&1/t==-o?"-0":r}},15903:(e,t,r)=>{var n=r(55296),s=r(48377);e.exports=function(e){return"symbol"==typeof e||s(e)&&"[object Symbol]"==n(e)}},16266:(e,t,r)=>{var n=r(22060);e.exports=function(e){return null==e?"":n(e)}},3233:(e,t,r)=>{var n=r(16266),s=0;e.exports=function(e){var t=++s;return n(e)+t}},13189:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var n=r(98768),s=r(69424),a=r(60343),i=r(98771),o=r(66263),u=r(72548),l=r(79418),c=r(76342),d=r(81524);let h=({value:e,onChange:t})=>{let[r,s]=(0,a.useState)([]);return(0,a.useEffect)(()=>{s(JSON.parse(localStorage.getItem("availableClients")||"[]").map(e=>({label:e.Title,value:e.ID})))},[]),n.jsx(d.Combobox,{defaultValues:e,onChange:t,options:r,placeholder:"Select client"})};var f=r(94060),p=r(46776),g=r(60797),m=r(64837);function y(){let e=(0,s.useSearchParams)().get("from"),t=(0,s.useRouter)(),[r,d]=(0,a.useState)(0),y=`${localStorage.getItem("firstName")??""} ${localStorage.getItem("surname")??""}`,[v,$]=(0,a.useState)(""),[x,{loading:S}]=(0,u.D)(c.AXh,{onCompleted:e=>{localStorage.setItem("useDepartmentsaveby","client-form"),null===e.updateSeaLogsMember.client.useDepartment?w({variables:{filter:{id:{eq:+(localStorage.getItem("userId")??0)}}}}):(localStorage.setItem("useDepartment",e.updateSeaLogsMember.client.useDepartment),t.push("/"))},onError:e=>{console.error("mutationUpdateUser error",e),$(e.message)}}),[w]=(0,l.t)(f.jl,{fetchPolicy:"no-cache",onCompleted:e=>{let r=e.readOneSeaLogsMember;r&&(localStorage.setItem("useDepartment",r.client.useDepartment),localStorage.setItem("usePilotTransfer",r.client.usePilotTransfer),localStorage.setItem("useTripSchedule",r.client.useTripSchedule),t.push("/"))},onError:e=>{console.error("getUserByID error",e),$(e.message)}});async function M(){$(""),localStorage.setItem("uploadSuccessResult","0"),localStorage.setItem("fetchSuccessResult","0"),(0,p.j5)()&&await (0,p.io)();let e=localStorage.getItem("userId")??0;if(0==+r){$("Please select a client.");return}if(0==+e){$("Opps, there was an error with your login. Please try and login again.");return}let t=JSON.parse(localStorage.getItem("availableClients")||"[]").find(e=>e.ID===r);localStorage.setItem("clientTitle",t.Title),localStorage.setItem("clientId",r.toString()),await x({variables:{input:{id:+e,clientID:r}}})}let b=(0,n.jsxs)("div",{className:"flex h-screen flex-col items-center justify-center gap-4 ",children:[n.jsx(i.Z,{}),(0,n.jsxs)("div",{className:" shadow rounded py-8 px-4 w-80 md:w-auto",children:[n.jsx("h1",{className:" text-bold mb-4",children:"login"!==e?"Switch Client":"Select Client"}),(0,n.jsxs)("div",{className:"py-4",children:["You are logged in as ",y]}),n.jsx("div",{className:"text-red-500 text-center py-4",children:n.jsx("small",{children:v})}),(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[n.jsx(g.Label,{htmlFor:"client ",children:"Client"}),n.jsx(h,{value:r,onChange:e=>{d(+e?.value)}})]}),(0,n.jsxs)("div",{className:"md:flex md:justify-between py-4 gap-2",children:[n.jsx("div",{children:n.jsx("button",{onClick:M,type:"button",className:"0  px-4 py-2 rounded w-full md:w-auto my-2",disabled:S,children:"Select Client"})}),n.jsx("div",{children:n.jsx(o.default,{href:"/logout",className:"inline-block bg-yellow-500   text-center  py-2 px-4 rounded w-full md:w-auto my-2",children:"Log in as someone else"})})]})]})]});return"login"!==e&&(b=n.jsx(m.Z,{children:b})),b}let v=()=>{let e=(0,s.useRouter)(),[t,r]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let t=localStorage.getItem("superAdmin");r("true"===t),"true"!==t&&e.push("/login")},[]),n.jsx("div",{children:t&&n.jsx(y,{})})}},98771:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var n=r(98768),s=r(28147);let a=function({color:e="default"}){return n.jsx("div",{children:"white"===e?n.jsx(s.default,{src:"/sealogs-horizontal-logo-white.png",alt:"Sealogs Logo",width:300,height:92,className:""}):n.jsx(s.default,{src:"/sealogs-horizontal-logo.png",alt:"Sealogs Logo",width:300,height:92,className:""})})}},61165:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\select-client\page.tsx#default`)},84961:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},46020:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},23379:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},7671:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(97428).Z)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[864,8865,3563,6263,8189,9507,7602,6451,4234,2925,5394,4837,6342],()=>r(23833));module.exports=n})();