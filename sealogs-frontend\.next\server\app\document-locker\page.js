(()=>{var e={};e.id=9797,e.ids=[9797],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},77485:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d}),r(84856),r(59849),r(78398),r(57757),r(48045);var s=r(40060),n=r(33581),a=r(57567),i=r.n(a),o=r(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d=["",{children:["document-locker",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84856)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\document-locker\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,59849)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\document-locker\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\document-locker\\page.tsx"],u="/document-locker/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/document-locker/page",pathname:"/document-locker",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74865:(e,t,r)=>{Promise.resolve().then(r.bind(r,75862))},47682:(e,t,r)=>{Promise.resolve().then(r.bind(r,89749))},75862:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(98768);r(60343);var n=r(64837);function a({children:e}){return s.jsx(n.Z,{children:e})}},89749:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(98768),n=r(69424),a=r(60343),i=r(26100),o=r(79418),l=r(94060),d=r(7678),c=r.n(d),u=r(75546),p=r(51742),m=r(30905),f=r(39650),g=r(80248),y=r(25394),x=r(47634);function h(){let[e,t]=(0,a.useState)([]),[r,n]=(0,a.useState)([]),[d,h]=(0,a.useState)({}),[v,_]=(0,a.useState)(!0),[S]=(0,o.t)(l.dY,{fetchPolicy:"cache-and-network",onCompleted:async r=>{let s=[],a=r.readVessels.nodes;if(a&&a.length){for(let e of a)for(let t of e.documents.nodes){let r={...t,type:e.__typename,type_title:e.title,type_id:e.id};s.push(r)}t([...e,...s]),n([...e,...s])}},onError:e=>{console.error("queryInventoriesEntry error",e)}}),[q]=(0,o.t)(l.bK,{fetchPolicy:"cache-and-network",onCompleted:async r=>{let s=[],a=r.readInventories.nodes;if(a&&a.length){for(let e of a)for(let t of e.documents.nodes){let r={...t,type:e.__typename,type_title:e.title,type_id:e.id};s.push(r)}t([...e,...s]),n([...e,...s])}},onError:e=>{console.error("queryInventoriesEntry error",e)}}),[N]=(0,o.t)(l.sI,{fetchPolicy:"cache-and-network",onCompleted:async r=>{let s=[],a=r.readComponentMaintenanceChecks.nodes;if(a&&a.length){for(let e of a)for(let t of e.documents.nodes){let r={...t,type:"Maintenance",type_title:e.name,type_id:e.id};s.push(r)}t([...e,...s]),n([...e,...s])}},onError:e=>{console.error("queryInventoriesEntry error",e)}}),[j]=(0,o.t)(l.SL,{fetchPolicy:"cache-and-network",onCompleted:r=>{let s=r.readOneClient;if(s){let r=s.documents.nodes.map(e=>({...e,type:"Company",type_title:"",type_id:0}));r.length>0&&(t([...e,...r]),n([...e,...r]))}},onError:e=>{console.error("readOneClient error",e)}}),w=(0,p.wu)([{accessorKey:"name",header:({column:e})=>s.jsx(m.u,{column:e,title:"Name"}),cellAlignment:"left",cell:({row:e})=>{let t=e.original;return(0,s.jsxs)("div",{className:"space-y-1",children:[s.jsx("div",{className:"flex items-center gap-2",children:(0,s.jsxs)("a",{href:"https://api.sealogs.com/assets/"+t.fileFilename,target:"_blank",rel:"noopener noreferrer",className:"font-medium hover:underline flex items-center gap-1",children:[t.name,s.jsx(x.Z,{className:"h-3 w-3"})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-2 md:hidden",children:[s.jsx(y.Ct,{variant:"outline",className:"text-xs",children:t.type}),t.type_title&&s.jsx("span",{className:"text-sm text-muted-foreground",children:t.type_title})]}),(0,s.jsxs)("div",{className:"block md:hidden text-sm text-muted-foreground",children:["Created:"," ",t?.created?(0,u.p6)(t.created):"No Date"]})]})},sortingFn:(e,t)=>{let r=e?.original?.name||"",s=t?.original?.name||"";return r.localeCompare(s)}},{accessorKey:"type",header:({column:e})=>s.jsx(m.u,{column:e,title:"Module"}),cellAlignment:"left",cell:({row:e})=>{let t=e.original;return s.jsx("div",{className:"hidden md:block",children:t.type})},sortingFn:(e,t)=>{let r=e?.original?.type||"",s=t?.original?.type||"";return r.localeCompare(s)}},{accessorKey:"type_title",header:({column:e})=>s.jsx(m.u,{column:e,title:"Item"}),cellAlignment:"left",cell:({row:e})=>{let t=e.original;return s.jsx("div",{className:"hidden md:block text-sm",children:c()(t.type_title)?"-":t.type_title})},sortingFn:(e,t)=>{let r=e?.original?.type_title||"",s=t?.original?.type_title||"";return r.localeCompare(s)}},{accessorKey:"created",header:({column:e})=>s.jsx(m.u,{column:e,title:"Upload date"}),cellAlignment:"right",cell:({row:e})=>{let t=e.original;return s.jsx("div",{className:"hidden md:block text-sm",children:t?.created?(0,u.p6)(t.created):"No Date"})},sortingFn:(e,t)=>{let r=new Date(e?.original?.created||0).getTime();return new Date(t?.original?.created||0).getTime()-r}}]);return(0,s.jsxs)(s.Fragment,{children:[s.jsx(f.ListHeader,{icon:s.jsx(g.E,{className:"h-12 w-12 ring-1 p-1 rounded-full bg-[#fff]"}),title:"Document locker"}),s.jsx("div",{className:"mt-16",children:v?s.jsx(i.Z,{}):s.jsx(p.wQ,{columns:w,data:e,showToolbar:!0,pageSize:20,onChange:({type:e,data:s})=>{let n={...d},a=r;if("vessel"===e&&(Array.isArray(s)&&s.length>0?(n.vesselID={in:s.map(e=>+e.value)},a=a.filter(e=>n.vesselID.in.includes(+e.type_id))):s&&!Array.isArray(s)?(n.vesselID={eq:+s.value},a=a.filter(e=>e.type_id===s.value.toString())):delete n.vesselID),"Module"===e&&(Array.isArray(s)&&s.length>0?(n.moduleName={in:s.map(e=>e.value)},a=a.filter(e=>n.moduleName.in.includes(e.type))):s&&s.value&&!Array.isArray(s)?(n.moduleName=s.value,a=a.filter(e=>e.type===s.value)):delete n.moduleName),"keyword"===e){if(s?.value?.trim()){let e=s.value.trim().toLowerCase();n.item={contains:e},a=a.filter(t=>{let r=t.title.toLowerCase(),s=t.name.toLowerCase();return r.includes(e)||s.includes(e)})}else delete n.item}h(n),t(a)}})})]})}function v(){let e=(0,n.useSearchParams)();return e.get("taskID"),e.get("redirectTo"),s.jsx(h,{})}},75546:(e,t,r)=>{"use strict";r.d(t,{Br:()=>p,fU:()=>m,o0:()=>c,p6:()=>d,vq:()=>u});var s=r(83179),n=r.n(s),a=r(7678),i=r.n(a),o=r(14826),l=r.n(o);let d=(e="",t=!0)=>{let r;if(i()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,s,n]=e.split("-"),a=t?r.slice(-2):r,i=parseInt(n,10).toString().padStart(2,"0"),o=parseInt(s,10).toString().padStart(2,"0");return`${i}/${o}/${a}`}if(!(r=e&&"object"==typeof e?n()(e.toString()):n()(e)).isValid())return"";let s=r.format("DD"),a=r.format("MM"),o=t?r.format("YY"):r.format("YYYY");return`${s}/${a}/${o}`},c=(e="",t=!0)=>{let r;if(i()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[r,s,n]=e.split("-"),a=t?r.slice(-2):r,i=parseInt(n,10).toString().padStart(2,"0"),o=parseInt(s,10).toString().padStart(2,"0");return`${i}/${o}/${a} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[r,s]=e.split(" "),[n,a,i]=r.split("-"),o=t?n.slice(-2):n,l=s.split(":"),d=l[0].padStart(2,"0"),c=l[1].padStart(2,"0"),u=parseInt(i,10).toString().padStart(2,"0"),p=parseInt(a,10).toString().padStart(2,"0");return`${u}/${p}/${o} ${d}:${c}`}if(!(r=e&&"object"==typeof e?n()(e.toString()):n()(e)).isValid())return"";let s=r.format("DD"),a=r.format("MM"),o=t?r.format("YY"):r.format("YYYY"),d=r.format("HH:mm");return`${s}/${a}/${o} ${d}`},u=(e="")=>i()(l()(e))?"":n()(e).format("YYYY-MM-DD HH:mm:ss"),p=(e="")=>i()(l()(e))?new Date:new Date(`${e}T10:00:00Z`),m=(e,t)=>{let r=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),s=e=>e.includes(" ")?e.replace(" ","T"):e,n=e=>{if(!e||"string"!=typeof e)return null;if(r(e)){let t=new Date().toISOString().split("T")[0];return new Date(`${t}T${e}`)}return new Date(s(e))},a=n(e),i=n(t);return!a||!i||isNaN(a.getTime())||isNaN(i.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:t}),!1):i>a}},59849:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\document-locker\layout.tsx#default`)},84856:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\document-locker\page.tsx#default`)},47634:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(97428).Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7944],()=>r(77485));module.exports=s})();