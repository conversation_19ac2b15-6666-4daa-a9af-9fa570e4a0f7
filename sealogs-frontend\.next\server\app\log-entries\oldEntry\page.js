(()=>{var e={};e.id=8467,e.ids=[8467],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},23192:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>d,routeModule:()=>b,tree:()=>c}),r(34966),r(83569),r(78398),r(57757),r(48045);var l=r(40060),a=r(33581),t=r(57567),n=r.n(t),i=r(51650),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(s,o);let c=["",{children:["log-entries",{children:["oldEntry",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,34966)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\log-entries\\oldEntry\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,83569)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\log-entries\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\log-entries\\oldEntry\\page.tsx"],m="/log-entries/oldEntry/page",p={require:r,loadChunk:()=>Promise.resolve()},b=new l.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/log-entries/oldEntry/page",pathname:"/log-entries/oldEntry",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},11845:(e,s,r)=>{Promise.resolve().then(r.bind(r,29843))},60826:e=>{e.exports=function(e){return e.split("")}},829:e=>{e.exports=function(e,s,r,l){for(var a=e.length,t=r+(l?1:-1);l?t--:++t<a;)if(s(e[t],t,e))return t;return -1}},65337:(e,s,r)=>{var l=r(829),a=r(35447),t=r(28026);e.exports=function(e,s,r){return s==s?t(e,s,r):l(e,a,r)}},35447:e=>{e.exports=function(e){return e!=e}},77420:e=>{e.exports=function(e,s,r){var l=-1,a=e.length;s<0&&(s=-s>a?0:a+s),(r=r>a?a:r)<0&&(r+=a),a=s>r?0:r-s>>>0,s>>>=0;for(var t=Array(a);++l<a;)t[l]=e[l+s];return t}},49513:(e,s,r)=>{var l=r(70458),a=/^\s+/;e.exports=function(e){return e?e.slice(0,l(e)+1).replace(a,""):e}},30482:(e,s,r)=>{var l=r(77420);e.exports=function(e,s,r){var a=e.length;return r=void 0===r?a:r,!s&&r>=a?e:l(e,s,r)}},74783:(e,s,r)=>{var l=r(65337);e.exports=function(e,s){for(var r=e.length;r--&&l(s,e[r],0)>-1;);return r}},41200:(e,s,r)=>{var l=r(65337);e.exports=function(e,s){for(var r=-1,a=e.length;++r<a&&l(s,e[r],0)>-1;);return r}},73211:e=>{var s=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");e.exports=function(e){return s.test(e)}},28026:e=>{e.exports=function(e,s,r){for(var l=r-1,a=e.length;++l<a;)if(e[l]===s)return l;return -1}},66095:(e,s,r)=>{var l=r(60826),a=r(73211),t=r(92115);e.exports=function(e){return a(e)?t(e):l(e)}},70458:e=>{var s=/\s/;e.exports=function(e){for(var r=e.length;r--&&s.test(e.charAt(r)););return r}},92115:e=>{var s="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",l="\ud83c[\udffb-\udfff]",a="[^"+s+"]",t="(?:\ud83c[\udde6-\uddff]){2}",n="[\ud800-\udbff][\udc00-\udfff]",i="(?:"+r+"|"+l+")?",o="[\\ufe0e\\ufe0f]?",c="(?:\\u200d(?:"+[a,t,n].join("|")+")"+o+i+")*",d=RegExp(l+"(?="+l+")|(?:"+[a+r+"?",r,t,n,"["+s+"]"].join("|")+")"+(o+i+c),"g");e.exports=function(e){return e.match(d)||[]}},28288:e=>{e.exports=function(e){return null==e}},14826:(e,s,r)=>{var l=r(22060),a=r(49513),t=r(30482),n=r(74783),i=r(41200),o=r(66095),c=r(16266);e.exports=function(e,s,r){if((e=c(e))&&(r||void 0===s))return a(e);if(!e||!(s=l(s)))return e;var d=o(e),m=o(s),p=i(d,m),b=n(d,m)+1;return t(d,p,b).join("")}},29843:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u});var l=r(98768),a=r(69424),t=r(60343),n=r(79418),i=r(94060),o=r(81608),c=r(9707),d=r(28147),m=r(13842),p=r(75546);r(83962);var b=r(83179),h=r.n(b),x=r(56950),g=r(26100),j=r(76643),N=r(85283);function u(){let e=(0,a.useSearchParams)(),s=e.get("vesselID")??0,r=e.get("logentryID")??0;e.get("snapID");let[b,u]=(0,t.useState)(!0),y=(0,a.useRouter)(),[f,k]=(0,t.useState)(),[v,T]=(0,t.useState)(),[w,S]=(0,t.useState)(),[E,C]=(0,t.useState)(),[_,L]=(0,t.useState)(),[$,D]=(0,t.useState)(),[O,B]=(0,t.useState)(),[F,I]=(0,t.useState)(!1),[P,V]=(0,t.useState)(!1),[R,M]=(0,t.useState)(!1),[A,G]=(0,t.useState)(),[q,W]=(0,t.useState)(),[H,U]=(0,t.useState)(),[Z,J]=(0,t.useState)(),[z,K]=(0,t.useState)(!1),[Y,Q]=(0,t.useState)(),[X,ee]=(0,t.useState)(),[es,er]=(0,t.useState)(),[el,ea]=(0,t.useState)(),[et,en]=(0,t.useState)(),[ei,eo]=(0,t.useState)(),[ec,ed]=(0,t.useState)(),[em,ep]=(0,t.useState)(),[eb,eh]=(0,t.useState)(),[ex,eg]=(0,t.useState)(),[ej,eN]=(0,t.useState)(),[eu,ey]=(0,t.useState)(),[ef,ek]=(0,t.useState)(),[ev,eT]=(0,t.useState)(!1),[ew,eS]=(0,t.useState)(!1),[eE,eC]=(0,t.useState)(!1),[e_,eL]=(0,t.useState)({checks:!1,client:!1,crew_members:!1,daily_checks:!1,sign_off:!1,sign_off_comments:!1,trip_report:!1,vessel:!1}),[e$,eD]=(0,t.useState)(!1);(0,m.BJ)(+s,e=>{C(e),eL({...e_,vessel:!0})}),(0,m.s3)(e=>{T(e),eL({...e_,client:!0})});let[eO]=(0,n.t)(i.tg,{fetchPolicy:"cache-and-network",onCompleted:e=>{W(e.readEngine_LogBookEntrySections.nodes)},onError:e=>{console.error("Engine_LogBookEntrySection error",e)}}),[eB]=(0,n.t)(i.SB,{fetchPolicy:"cache-and-network",onCompleted:e=>{U(e.readFuel_LogBookEntrySections.nodes)},onError:e=>{console.error("Fuel_LogBookEntrySection error",e)}}),[eF]=(0,n.t)(i.gx,{fetchPolicy:"cache-and-network",onCompleted:e=>{J(e.readPorts_LogBookEntrySections.nodes)},onError:e=>{console.error("Ports_LogBookEntrySection error",e)}}),[eI]=(0,n.t)(i.Sk,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readVesselDailyCheck_LogBookEntrySections.nodes;K(s[0]),eL({...e_,daily_checks:!0}),eR({variables:{filter:{logBookEntrySectionID:{eq:s[0].id}}}})},onError:e=>{console.error("VesselDailyCheck_LogBookEntrySection error",e)}}),[eP]=(0,n.t)(i.w$,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readLogBookSignOff_LogBookEntrySections.nodes;Q(s[0]),eL({...e_,sign_off:!0}),eV({variables:{filter:{logBookEntrySectionID:{eq:s[0].id}}}})},onError:e=>{console.error("LogBookSignOff_LogBookEntrySection error",e)}}),[eV]=(0,n.t)(i.ec,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readSectionMemberComments.nodes;s&&(eN(s),eL({...e_,sign_off_comments:!0}))},onError:e=>{console.error("querySectionMemberComments error",e)}}),[eR]=(0,n.t)(i.ec,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readSectionMemberComments.nodes;s&&(ey(s),eL({...e_,daily_comments:!0}))},onError:e=>{console.error("queryDailyCheckMemberComments error",e)}}),[eM]=(0,n.t)(i.ui,{fetchPolicy:"cache-and-network",onCompleted:e=>{ee(e.readVoyageSummary_LogBookEntrySections.nodes)},onError:e=>{console.error("VoyageSummary_LogBookEntrySection error",e)}}),[eA]=(0,n.t)(i.LW,{fetchPolicy:"cache-and-network",onCompleted:e=>{eC(e.readInfringementNotices.nodes)},onError:e=>{console.error("getInfringements error",e)}}),[eG]=(0,n.t)(i.Ye,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readTripReport_LogBookEntrySections.nodes;er(s),eL({...e_,trip_report:!0});let r=s.map(e=>e.tripEvents.nodes.map(e=>e.infringementNoticeID)).flat().filter(e=>e&&0!==e);r.length>0&&eA({variables:{id:r}})},onError:e=>{console.error("TripReport_LogBookEntrySection error",e)}}),[eq]=(0,n.t)(i.Y,{fetchPolicy:"cache-and-network",onCompleted:e=>{ea(e.readCrewMembers_LogBookEntrySections.nodes),eL({...e_,crew_members:!0})},onError:e=>{console.error("CrewMembers_LogBookEntrySection error",e)}}),[eW]=(0,n.t)(i.dh,{fetchPolicy:"cache-and-network",onCompleted:e=>{eg(Array.from(new Set(e.readCrewMembers_LogBookEntrySections.nodes.flatMap(e=>e.crewMember).flatMap(e=>+e.id))))},onError:e=>{console.error("CrewMembers_LogBookEntrySection error",e)}});(0,m.d)(e=>{let s=e.filter(e=>!A?.filter(e=>"SeaLogs\\CrewMembers_LogBookEntrySection"===e.className)?.flatMap(e=>+e.ids)?.includes(e));s&&s.length>0&&eW({variables:{ids:s}})});let[eH]=(0,n.t)(i.JS,{fetchPolicy:"cache-and-network",onCompleted:e=>{en(e.readCrewTraining_LogBookEntrySections.nodes)},onError:e=>{console.error("CrewTraining_LogBookEntrySection error",e)}}),[eU]=(0,n.t)(i.Zy,{fetchPolicy:"cache-and-network",onCompleted:e=>{eo(e.readSupernumerary_LogBookEntrySections.nodes)},onError:e=>{console.error("Supernumerary_LogBookEntrySection error",e)}}),[eZ]=(0,n.t)(i.t1,{fetchPolicy:"cache-and-network",onCompleted:e=>{ed(e.readEngineer_LogBookEntrySections.nodes)},onError:e=>{console.error("Engineer_LogBookEntrySection error",e)}}),[eJ]=(0,n.t)(i.r3,{fetchPolicy:"cache-and-network",onCompleted:e=>{ep(e.readAssetReporting_LogBookEntrySections.nodes)},onError:e=>{console.error("AssetReporting_LogBookEntrySection error",e)}}),[ez]=(0,n.t)(i.UU,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readCrewWelfare_LogBookEntrySections.nodes;eh(s),eK({variables:{filter:{logBookEntrySectionID:{eq:s[0].id}}}})},onError:e=>{console.error("CrewWelfare_LogBookEntrySection error",e)}}),[eK]=(0,n.t)(i.ec,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readSectionMemberComments.nodes;s&&ek(s)},onError:e=>{console.error("querySectionMemberComments error",e)}}),eY=e=>{k(e.master),eL({...e_,master:!0,logbook:!0}),"Locked"===e.state?M(!0):M(!1);let s=Array.from(new Set(e.logBookEntrySections.nodes.map(e=>e.className))).map(s=>({className:s,ids:e.logBookEntrySections.nodes.filter(e=>e.className===s).map(e=>e.id)}));G(s),s.find(e=>"SeaLogs\\TripReport_LogBookEntrySection"===e.className)&&eL({...e_,daily_checks:!0,daily_comments:!0}),s.find(e=>"SeaLogs\\LogBookSignOff_LogBookEntrySection"===e.className)&&eL({...e_,sign_off:!0,sign_off_comments:!0}),s.forEach(e=>{if("SeaLogs\\Engine_LogBookEntrySection"===e.className&&eO({variables:{id:e.ids}}),"SeaLogs\\Fuel_LogBookEntrySection"===e.className&&eB({variables:{id:e.ids}}),"SeaLogs\\Ports_LogBookEntrySection"===e.className&&eF({variables:{id:e.ids}}),"SeaLogs\\VesselDailyCheck_LogBookEntrySection"===e.className&&eI({variables:{id:e.ids}}),"SeaLogs\\LogBookSignOff_LogBookEntrySection"===e.className&&(eL({...e_,sign_off:!1,sign_off_comments:!1}),eP({variables:{id:e.ids}})),"SeaLogs\\CrewWelfare_LogBookEntrySection"===e.className&&ez({variables:{id:e.ids}}),"SeaLogs\\VoyageSummary_LogBookEntrySection"===e.className&&eM({variables:{id:e.ids}}),"SeaLogs\\TripReport_LogBookEntrySection"===e.className&&eG({variables:{id:e.ids}}),"SeaLogs\\CrewMembers_LogBookEntrySection"===e.className){let s={};s.id={in:e.ids},eq({variables:{filter:s}})}"SeaLogs\\CrewTraining_LogBookEntrySection"===e.className&&eH({variables:{id:e.ids}}),"SeaLogs\\Supernumerary_LogBookEntrySection"===e.className&&eU({variables:{id:e.ids}}),"SeaLogs\\Engineer_LogBookEntrySection"===e.className&&eZ({variables:{id:e.ids}}),"SeaLogs\\AssetReporting_LogBookEntrySection"===e.className&&eJ({variables:{id:e.ids}})}),I(!0)},[eQ]=(0,n.t)(i.MI,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readOneLogBookEntry;s&&eY(s)},onError:e=>{console.error("queryLogBookEntry error",e)}}),[eX]=(0,n.t)(i.K7,{fetchPolicy:"cache-and-network",onCompleted:e=>{let s=e.readOneLogBookEntryOldConfigs;s&&(eT(JSON.parse(s.lastConfig)),S(JSON.parse(s.lastEntry)),V(!0),eL({...e_,vessel:!0}))},onError:e=>{console.error("queryLogBookEntryOldConfigs error",e)}}),e0=()=>{let e=document.getElementById("logReport"),l=document.getElementById("crew_section"),a=document.getElementById("daily_checks"),t=document.getElementById("trip_report");if(l&&a&&l?.clientHeight+a?.clientHeight>2240&&l?.style.setProperty("min-height","2240px"),t&&t.clientHeight>1920){t.style.setProperty("min-height","2240px");let e=Array.from(t.children),s=0,r=document.createElement("div");r.className="page_break min-h-[2240px]",e.forEach(e=>{let l=e.clientHeight;s+l>1920&&(t.appendChild(r),(r=document.createElement("div")).className="page_break min-h-[2240px] mt-8",s=0),r.appendChild(e),s+=l}),r.children.length>0&&t.appendChild(r),t.removeAttribute("id")}e?.style.setProperty("width","1600px"),e&&(e?.style.setProperty("width","1600px"),(0,o.default)(e,{scale:1,useCORS:!0,allowTaint:!0,logging:!0,windowWidth:e.scrollWidth,windowHeight:e.scrollHeight}).then(l=>{let a=l.toDataURL("image/png"),t=new c.jsPDF("p","mm",[1400,1979],!0),n=t.getImageProperties(a),i=t.internal.pageSize.getWidth(),o=n.height*i/n.width,d=0;for(;d<l.height-1979;)0!==d&&t.addPage(),t.addImage(a,"PNG",0,-d,i,o),d+=1979;t.save("report.pdf"),y.push(`/log-entries?vesselID=${+s}&logentryID=${+r}`),e.style.width="100%"}))},e4=(e,s="FieldComment")=>{let r=eu?.length>0&&eu.filter(r=>r.fieldName===e&&r.commentType===s);return r.length>0&&r[0]},e6=[{label:"Class 1 Explosives",value:"1"},{label:"Class 2 Gases",value:"2"},{label:"Class 2.1 - Flammable gases",value:"2.1"},{label:"Class 2.2 - Non-Flammable Non-Toxic Gases",value:"2.2"},{label:"Class 2.3 - Toxic Gases",value:"2.3"},{label:"Class 3 Flammable Liquids",value:"3"},{label:"Class 4 Flammable Solids",value:"4"},{label:"Class 4.1 - Flammable Solids",value:"4.1"},{label:"Class 4.2 - Spontaneously Combustible Substances",value:"4.2"},{label:"Class 4.3 - Substances Flammable When Wet",value:"4.3"},{label:"Class 5 Oxidizing Substances and Organic Peroxides",value:"5"},{label:"Class 5.1 - Oxidising Substances",value:"5.1"},{label:"Class 5.2 - Organic Peroxides",value:"5.2"},{label:"Class 6 Toxic and Infectious Substances",value:"6"},{label:"Class 6.1 - Toxic Substances",value:"6.1"},{label:"Class 6.2 - Infectious Substances",value:"6.2"},{label:"Class 7 Radioactive Substances",value:"7"},{label:"Class 8 Corrosive Substances",value:"8"},{label:"Class 9 Miscellaneous Hazardous Substance",value:"9"}],e1=(e,s)=>e?.parentTaskingID>0?s?.tripEvents?.nodes?.filter(s=>s.EventType_Tasking.id===e.parentTaskingID)[0]?.EventType_Tasking?.operationType?.replace(/_/g," "):e?.operationType?.replace(/_/g," "),e8=(e,s)=>s.tripEvents.nodes.find(s=>s.EventType_Tasking.id===e.parentTaskingID).EventType_Tasking.towingChecklist,e2=e=>{let s=ev?.customisedLogBookComponents?.nodes?.filter(e=>"TripReport_LogBookComponent"===e.componentClass);return!!(s?.length>0&&s[0]?.customisedComponentFields?.nodes.filter(s=>s.fieldName===e&&"Off"!==s.status).length>0)},e3=(e,s)=>{let r=e.map(e=>e.name),l=Array.from(new Set(s.map(e=>e.groupTo)));return s.filter(e=>r.includes(e.fieldName)&&!l.includes(e.fieldName)).map(s=>({...s,customisedFieldTitle:e.find(e=>e.name===s.fieldName)?.label}))},e5=(e,s,r=!1)=>e.filter(e=>"Ok"!==e.checked&&(0,x.w_)(e.name,ev)),e7=e=>"Hull"===e.value?(0,l.jsxs)(l.Fragment,{children:[j.i$(ev,z)[0]?.group?.map(e=>e5(e,ew).filter(e=>!e4(e.name,"FieldComment")).map(e=>e.label).join(", ")),e5(j.i$(ev,z)[1]?.individual,ew,!0).map(e=>e.label).join(", ")]}):"Engine"===e.value?(0,l.jsxs)(l.Fragment,{children:[j.mW(ev,z)[0]?.group?.map(e=>e5(e,ew).filter(e=>!e4(e.name,"FieldComment")).map(e=>e.label).join(", ")),e5(j.mW(ev,z)[1]?.individual,ew,!0).map(e=>e.label).join(", ")]}):"Safety"===e.value?(0,l.jsxs)(l.Fragment,{children:[j.VM(ev,z)[0]?.group?.map(e=>e5(e,ew).filter(e=>!e4(e.name,"FieldComment")).map(e=>e.label).join(", ")),e5(j.VM(ev,z)[1]?.individual,ew,!0).map(e=>e.label).join(", ")]}):"Navigation"===e.value?(0,l.jsxs)(l.Fragment,{children:[j.Ti(ev,z)[0]?.group?.map(e=>e5(e,ew).filter(e=>!e4(e.name,"FieldComment")).map(e=>e.label).join(", ")),e5(j.Ti(ev,z)[1]?.individual,ew,!0).map(e=>e.label).join(", ")]}):ew?.filter(s=>s.fieldSet===e.title&&"Not_Ok"===s.value&&!e4(s.fieldName)).map(e=>x.N3(e.fieldName,ev)).join(", "),e9=e=>"Hull"===e.value?(0,l.jsxs)(l.Fragment,{children:[j.i$(ev,z)[0]?.group?.map(s=>e3(s,ew)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e4(s.fieldName,"FieldComment")).map(e=>e.customisedFieldTitle??x.N3(e.fieldName,ev)).join(", ")).filter(e=>""!=e).join(", "),e3(j.i$(ev,z)[1]?.individual,ew)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e4(s.fieldName,"FieldComment")).map(e=>x.N3(e.fieldName,ev)).join(", ")]}):"Engine"===e.value?(0,l.jsxs)(l.Fragment,{children:[j.mW(ev,z)[0]?.group?.map(s=>e3(s,ew)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e4(s.fieldName,"FieldComment")).map(e=>e.customisedFieldTitle??x.N3(e.fieldName,ev)).join(", ")).filter(e=>""!=e).join(", "),e3(j.mW(ev,z)[1]?.individual,ew)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e4(s.fieldName,"FieldComment")).map(e=>x.N3(e.fieldName,ev)).join(", ")]}):"Safety"===e.value?(0,l.jsxs)(l.Fragment,{children:[j.VM(ev,z)[0]?.group?.map(s=>e3(s,ew)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e4(s.fieldName,"FieldComment")).map(e=>e.customisedFieldTitle??x.N3(e.fieldName,ev)).join(", ")).filter(e=>""!=e).join(", "),e3(j.VM(ev,z)[1]?.individual,ew)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e4(s.fieldName,"FieldComment")).map(e=>x.N3(e.fieldName,ev)).join(", ")]}):"Navigation"===e.value?(0,l.jsxs)(l.Fragment,{children:[j.Ti(ev,z)[0]?.group?.map(s=>e3(s,ew)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e4(s.fieldName,"FieldComment")).map(e=>e.customisedFieldTitle??x.N3(e.fieldName,ev)).join(", ")).filter(e=>""!=e).join(", "),e3(j.Ti(ev,z)[1]?.individual,ew)?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e4(s.fieldName,"FieldComment")).map(e=>x.N3(e.fieldName,ev)).join(", ")]}):ew?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&!e4(s.fieldName,"FieldComment")).map(e=>x.N3(e.fieldName,ev)).join(", "),se=()=>l.jsx(l.Fragment,{children:j.el(ev,e$)[1]?.individual?.filter(e=>"Ok"!==e.checked&&x.Pr(e.name,ev)).map(e=>e.label).join(", ")}),ss=()=>l.jsx(l.Fragment,{children:e3(j.el(ev,e$)[1]?.individual,e$)?.filter(e=>"Ok"===e.value&&!e4(e.fieldName,"FieldComment")).map(e=>x.g(e.fieldName,ev)).join(", ")}),sr=(e,s="FieldComment")=>(w?.LogBookEntrySections?.filter(e=>"SeaLogs\\CrewWelfare_LogBookEntrySection"===e.ClassName)[0]?.SectionMemberComment).find(r=>r.FieldName===e&&r.CommentType===s),sl=(e="Ok")=>{let s=w?.LogBookEntrySections?.filter(e=>"SeaLogs\\CrewWelfare_LogBookEntrySection"===e.ClassName)[0];return["Fitness","SafetyActions","WaterQuality","IMSafe"].map(r=>!!(s[r]===e&&sr(r,"FieldComment"))&&r).filter(e=>!1!==e)},sa=(e="Ok")=>{let s=w?.LogBookEntrySections?.filter(e=>"SeaLogs\\CrewWelfare_LogBookEntrySection"===e.ClassName)[0],r=["Fitness","SafetyActions","WaterQuality","IMSafe"].map(r=>!(s?.[r]!==e||sr(r,"FieldComment"))&&r).filter(e=>!1!==e);return r.length>0&&r.join(", ")},st=e=>{if(0==e.InfringementNoticeID)return null;let s=e.InfringementNotice,r=[],a=[];return s?.Time&&r.push({Time:s?.Time}),s?.GeoLocation?.ID>0&&r.push({Location:s.GeoLocation.Title}),s?.WaterwaysOfficer?.ID>0&&r.push({"Waterways officer":`${s.WaterwaysOfficer.FirstName} ${s.WaterwaysOfficer.Surname}`}),s?.VesselName&&r.push({"Vessel name":s.VesselName}),s?.VesselType&&r.push({"Vessel type":s.VesselType}),s?.VesselReg&&r.push({"Vessel registration":s.VesselReg}),s?.OwnerFullName&&r.push({"Owner name":s.OwnerFullName}),s?.OwnerEmail&&r.push({"Owner email":s.OwnerEmail}),s?.OwnerAddress&&r.push({Address:s.OwnerAddress}),s?.OwnerDOB&&r.push({DOB:s.OwnerDOB}),s?.OwnerOccupation&&r.push({"Owner occupation":s.OwnerOccupation}),s?.OwnerPhone&&r.push({"Owner phone":s.OwnerPhone}),s?.InfringementUsed?.length>0&&(a.push({"Infringement used":s.InfringementUsed}),s?.LifeJacket?.length>0&&a.push({"Life Jacket infringement issued":s.LifeJacket}),s?.Mooring?.length>0&&a.push({"Mooring / embarkation / ramps or jetty infringements":s.Mooring}),s?.SpeedOrNavigation?.length>0&&a.push({"Speed / navigation infringement issued":s.SpeedOrNavigation}),s?.SwimmingOrDiving?.length>0&&a.push({"Swimming / diving infringement issued":s.SwimmingOrDiving}),s?.Towing?.length>0&&a.push({"Towing infringement issued":s.Towing}),s?.Other?.length>0&&a.push({Other:s.Other})),l.jsx(l.Fragment,{children:(0,l.jsxs)("tbody",{children:[r.length>0&&Array.from({length:Math.ceil(r.length/2)}).map((e,s)=>{let a=r[2*s],t=r[2*s+1];return(0,l.jsxs)("tr",{className:" border-b last:border-b-0",children:[a&&(0,l.jsxs)("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:Object.keys(a)[0]}),a[Object.keys(a)[0]]]}),t&&(0,l.jsxs)("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:Object.keys(t)[0]}),t[Object.keys(t)[0]]]})]},s)}),a.length>0&&Array.from({length:Math.ceil(a.length)}).map((e,s)=>{let r=a[s];return(0,l.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:l.jsx("span",{className:"w-48 inline-block",children:Object.keys(r)[0]})}),l.jsx("td",{className:"px-6 pb-4",children:r[Object.keys(r)[0]]})]},s)}),s?.otherDescription&&(0,l.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:l.jsx("span",{className:"w-48 inline-block",children:"Other description"})}),l.jsx("td",{className:"px-6 pb-4",children:l.jsx("div",{dangerouslySetInnerHTML:{__html:s.otherDescription}})})]})]})})},sn=()=>w?.LogBookEntrySections?.find(e=>"SeaLogs\\LogBookSignOff_LogBookEntrySection"===e.ClassName);return(0,l.jsxs)(l.Fragment,{children:[w?(0,l.jsxs)("div",{children:[!Object.values(e_).some(e=>!1===e)&&(0,l.jsxs)("p",{children:["If the PDF does not download automatically,"," ",l.jsx("span",{onClick:e0,className:"inline-block underline cursor-pointer",children:"click here"})]}),l.jsx("button",{id:"downloadPdf",onClick:e0,className:"hidden",children:"Download PDF"}),(0,l.jsxs)("div",{id:"logReport",className:"w-full px-4",children:[(0,l.jsxs)("div",{className:"page_break min-h-[2240px]",children:[(0,l.jsxs)("div",{id:"crew_section",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsxs)("h5",{className:" ",children:[(0,l.jsxs)("span",{className:"font-medium",children:["Log Entry"," "]}),E?.title," -"," ",w?.LogBookEntry?.StartDate?(0,p.p6)(w.LogBookEntry.StartDate):""]}),l.jsx(d.default,{src:"/sealogs-horizontal-logo.png",alt:"",width:220,height:50})]}),l.jsx("div",{className:"w-full h-full-screen  items-center justify-center  rounded-lg",children:(0,l.jsxs)("div",{className:"w-full p-0 overflow-hidden  border-t border-l border-r rounded-lg  ",children:[l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:(0,l.jsxs)("tbody",{children:[(0,l.jsxs)("tr",{className:" border-b",children:[(0,l.jsxs)("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Company"})," ",w?.LogBookEntry?.Client?.Title]}),(0,l.jsxs)("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Vessel"})," ",w?.LogBookEntry?.Vehicle?.Title??E?.title]})]}),(0,l.jsxs)("tr",{className:" border-b    ",children:[(0,l.jsxs)("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:w?.LogBookEntry?.Client?.MasterTerm??"Master"})," ",w?.LogBookEntry?.Master?.FirstName," ",w?.LogBookEntry?.Master?.Surname]}),(0,l.jsxs)("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Sign off time"})," ",(0,p.p6)(sn()?.Created)," ",sn()?.CompletedTime]})]}),sn()?.SectionMemberComment&&l.jsx("tr",{className:" border-b    ",children:l.jsx("td",{className:"px-6 pb-4",colSpan:2,children:(0,l.jsxs)("div",{className:"flex",children:[l.jsx("span",{className:"min-w-48 inline-block",children:"Sign off comment"}),l.jsx("div",{className:"inline-block",children:sn()?.SectionMemberComment})]})})}),l.jsx("tr",{className:" border-b    ",children:(0,l.jsxs)("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"End location"})," ",sn()?.EndLocation?.GeoLocationID>0?sn()?.EndLocation?.GeoLocation?.Title:sn()?.EndLocation?.Lat&&sn()?.EndLocation?.Long?"Lat - "+sn()?.EndLocation?.Lat+" Long - "+sn()?.EndLocation?.Long:""]})})]})}),l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsx("tbody",{children:(0,l.jsxs)("tr",{className:" border-b    ",children:[(0,l.jsxs)("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Signature"})," "]}),l.jsx("td",{className:"px-6 pb-4",children:sn()?.SectionSignature?.id>0&&l.jsx(N.Z,{className:"w-1/2",id:sn()?.SectionSignature?.id,alt:""})})]})})})]})}),w?.LogBookEntrySections?.filter(e=>"SeaLogs\\CrewMembers_LogBookEntrySection"===e.ClassName).length>0&&(0,l.jsxs)(l.Fragment,{children:[l.jsx("div",{className:"mt-8 mb-4",children:l.jsx("h5",{className:" ",children:"Crew members"})}),l.jsx("div",{className:"w-full h-full-screen  items-center justify-center  rounded-lg",children:l.jsx("div",{className:"w-full p-0 overflow-hidden  border-t border-l border-r rounded-lg  ",children:l.jsx("div",{className:"relative overflow-x-auto",children:(0,l.jsxs)("table",{className:"w-full  text-left rtl:text-right  ",children:[l.jsx("thead",{className:"",children:(0,l.jsxs)("tr",{children:[l.jsx("th",{className:"px-6 pb-4",children:"Name"}),l.jsx("th",{className:"px-6 pb-4",children:"Duty"}),l.jsx("th",{className:"px-6 pb-4",children:"Sign In"}),l.jsx("th",{className:"px-6 pb-4",children:"Sign Out"})]})}),l.jsx("tbody",{children:w?.LogBookEntrySections?.filter(e=>"SeaLogs\\CrewMembers_LogBookEntrySection"===e.ClassName).map(e=>l.jsxs("tr",{className:" border-b  ",children:[l.jsxs("td",{className:"px-6 pb-4",children:[e.CrewMember.FirstName," ",e.CrewMember.Surname]}),l.jsx("td",{className:"px-6 pb-4",children:e.DutyPerformed.Title}),l.jsx("td",{className:"px-6 pb-4",children:p.o0(e.PunchIn)}),l.jsx("td",{className:"px-6 pb-4",children:p.o0(e.PunchOut)})]},e.ID))})]})})})})]})]}),z&&ew&&(0,l.jsxs)("div",{id:"daily_checks",children:[l.jsx("div",{className:"mt-8 mb-4",children:l.jsx("h5",{className:" ",children:"Daily Check"})}),l.jsx("div",{className:"w-full h-full-screen  items-center justify-center  rounded-lg",children:l.jsx("div",{className:"w-full p-0 overflow-hidden  border-t border-l border-r  rounded-lg  ",children:l.jsx("div",{className:"relative overflow-x-auto",children:(0,l.jsxs)("table",{className:"w-full  text-left rtl:text-right  ",children:[l.jsx("thead",{className:"    ",children:(0,l.jsxs)("tr",{children:[(0,l.jsxs)("th",{className:"px-6 pb-4",children:[" ","Section"," "]}),(0,l.jsxs)("th",{className:"px-6 pb-4",children:[" ","Fields"," "]}),(0,l.jsxs)("th",{className:"px-6 pb-4",children:[" ","Status"," "]})]})}),(0,l.jsxs)("tbody",{children:[w?.LogBookEntrySections?.filter(e=>"SeaLogs\\CrewWelfare_LogBookEntrySection"===e.ClassName).length>0&&l.jsx(l.Fragment,{children:(0,l.jsxs)("tr",{className:" border-b  ",children:[l.jsx("td",{className:"px-6 pb-4 border-r-1",children:l.jsx("div",{children:"Crew welfare"})}),l.jsx("td",{colSpan:2,children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:(0,l.jsxs)("tbody",{children:[sa()&&(0,l.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:sa()}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Ok"})]}),sl("Ok")?.map(e=>l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:`${e}: ${sr(e)?.Comment}`}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Ok"})]})),sa("Not Ok")&&(0,l.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:sa("Not Ok")}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]}),sl("Not Ok")?.map(e=>l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:`${e}: ${sr(e)?.Comment}`}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Ok"})]}))]})})})]})}),[{title:"Safety Checks",value:"Safety"},{title:"Engine Checks",value:"Engine"},{title:"Jet Specific Checks",value:"JetSpecific"},{title:"Cleaning Checks",value:"Cleaning"},{title:"Navigation",value:"Navigation"},{title:"Deck operations and exterior checks",value:"Hull"},{title:"HVAC",value:"HVAC"},{title:"Plumbing",value:"Plumbing"},{title:"Sail",value:"Sail"},{title:"Biosecurity",value:"Biosecurity"}].filter(e=>ew?.filter(s=>s.fieldSet===e.title).length>0).map(e=>l.jsx(l.Fragment,{children:(0,l.jsxs)("tr",{className:" border-b  ",children:[l.jsx("td",{className:"px-6 pb-4 border-r-1",children:l.jsx("div",{children:e4(e.value,"Section")?(0,l.jsxs)(l.Fragment,{children:[e.title,l.jsx("br",{}),l.jsx("br",{}),"Comment:"," ",e4(e.value,"Section")?.comment]}):e.title})}),l.jsx("td",{colSpan:2,children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:(0,l.jsxs)("tbody",{children:[e9(e)?.props?.children?.filter(e=>""!==e)?.length>0&&(0,l.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:e9(e)}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Ok"})]}),ew?.filter(s=>s.fieldSet===e.title&&"Ok"===s.value&&e4(s.fieldName)).map(e=>l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:`${x.N3(e.fieldName,ev)}: ${e4(e.fieldName)?.comment}`}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Ok"})]},e.id)),e7(e)?.props?.children[0]?.filter(e=>""!==e)?.length>0||e7(e)?.props?.children[1]?.length>0&&(0,l.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:e7(e)}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},e.title),"Hull"===e.value?l.jsx(l.Fragment,{children:j.i$(ev,z)[0]?.group?.map(e=>e5(e,ew).filter(e=>e4(e.name,"FieldComment")).map(e=>l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:`${e.label}: ${e4(e.name)?.comment}`}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},e.id)))}):"Engine"===e.value?l.jsx(l.Fragment,{children:j.mW(ev,z)[0]?.group?.map(e=>e5(e,ew).filter(e=>e4(e.name,"FieldComment")).map(e=>l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:`${e.label}: ${e4(e.name)?.comment}`}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},e.id)))}):"Safety"===e.value?l.jsx(l.Fragment,{children:j.VM(ev,z)[0]?.group?.map(e=>e5(e,ew).filter(e=>e4(e.name,"FieldComment")).map(e=>l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:`${e.label}: ${e4(e.name)?.comment}`}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},e.id)))}):"Navigation"===e.value?l.jsx(l.Fragment,{children:j.Ti(ev,z)[0]?.group?.map(e=>e5(e,ew).filter(e=>e4(e.name,"FieldComment")).map(e=>l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:`${e.label}: ${e4(e.name)?.comment}`}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},e.id)))}):ew?.filter(s=>s.fieldSet===e.title&&"Not_Ok"===s.value&&e4(s.fieldName)).map(e=>l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:`${x.N3(e.fieldName,ev)}: ${e4(e.fieldName)?.comment}`}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},e.id))]})})})]},`${e.title}-1`)})),e$&&e$.length>0&&l.jsx(l.Fragment,{children:(0,l.jsxs)("tr",{className:" border-b  ",children:[l.jsx("td",{className:"px-6 pb-4 border-r-1",children:l.jsx("div",{children:"Sign off"})}),l.jsx("td",{colSpan:2,children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:(0,l.jsxs)("tbody",{children:[ss()?.props?.children?.length>0&&(0,l.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:ss()}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Ok"})]}),se()?.props?.children?.length>0&&(0,l.jsxs)("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4",children:se()}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]})]})})})]})})]})]})})})})]})]}),(0,l.jsxs)("div",{className:"page_break min-h-[2240px]",id:"trip_report",children:[es&&l.jsx("div",{className:"mt-8 mb-4",children:l.jsx("h5",{className:" ",children:"Trip report"})}),w?.LogBookEntrySections?.filter(e=>"SeaLogs\\TripReport_LogBookEntrySection"===e.ClassName).length>0&&w?.LogBookEntrySections?.filter(e=>"SeaLogs\\TripReport_LogBookEntrySection"===e.ClassName).map(e=>l.jsx("div",{className:"w-full mb-8 last:mb-0 h-full-screen  items-center justify-center  rounded-lg",children:l.jsx("div",{className:"w-full p-0 overflow-hidden  border-t border-l border-r  rounded-lg  ",children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsxs("tbody",{children:[l.jsxs("tr",{className:" border-b    ",children:[l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Departure"})," ",e?.DepartTime]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Depart location"})," ",e?.FromLocation?.ID>0?e?.FromLocation?.Title:e?.FromLocation?.Lat+" "+e?.FromLocation?.Long]})]}),l.jsxs("tr",{className:" border-b    ",children:[l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"P.O.B"})," ",l.jsx("div",{className:" inline-block",children:e?.POB})]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Vehicles on board"})," ",e?.TotalVehiclesCarried]})]}),e?.DesignatedDangerousGoodsSailing===1&&e2("DesignatedDangerousGoodsSailing")&&l.jsx("tr",{className:" border-b    ",children:l.jsx("td",{className:"px-6 pb-4",colSpan:2,children:"This is a designated dangerous goods sailing"})}),e?.DangerousGoodsRecords.length>0&&l.jsx("tr",{className:" border-b    ",children:l.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[l.jsx("span",{className:"w-48 inline-block",children:"Dangerous goods"}),l.jsx("div",{className:"inline-block",children:e?.DangerousGoodsRecords?.map((e,s)=>l.jsxs(l.Fragment,{children:[s>0&&", ",e6.find(s=>s.value===e?.Type)?.label," - ",l.jsx("div",{className:"inline-block",dangerouslySetInnerHTML:{__html:e?.Comment}})]}))})]})}),e?.DangerousGoodsChecklistID>0&&l.jsx("tr",{className:" border-b    ",children:l.jsx("td",{className:"",colSpan:2,children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsx("tbody",{children:l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:l.jsx("span",{className:"w-48 inline-block",children:"Dangerous goods checklist"})}),l.jsx("td",{className:"",children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsxs("tbody",{children:[Object.entries(e.DangerousGoodsChecklist).filter(([e,s])=>1===s).length>0&&l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 border-r-1",children:Object.entries(e.DangerousGoodsChecklist).filter(([e,s])=>1===s).map(([e,s])=>({key:e,value:s})).map(e=>e.key.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),l.jsx("td",{className:"px-6 pb-4",children:"Ok"})]}),Object.entries(e.DangerousGoodsChecklist).filter(([e,s])=>0===s).length>0&&l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 border-r-1",children:Object.entries(e.DangerousGoodsChecklist).filter(([e,s])=>0===s).map(([e,s])=>({key:e,value:s})).map(e=>e.key.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]}),e?.DangerousGoodsChecklist?.RiskFactors?.length>0&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsx("td",{className:"px-6 pb-4",colSpan:2,children:e?.DangerousGoodsChecklist.RiskFactors.map((e,s)=>l.jsxs("div",{className:"inline-block",children:[s>0?", ":"Risk factors: ",`${e.Title} - ${e.Impact} - ${e.Probability}/10`]},e.ID))})}),e?.DangerousGoodsChecklist?.Member?.ID>0&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsx("td",{className:"px-6 pb-4",colSpan:2,children:`Author: ${e.DangerousGoodsChecklist.Member?.FirstName} ${e.dangerousGoodsChecklist.member?.surname}`})})]})})})]})})})})}),e?.TripReport_Stops?.length>0&&l.jsx(l.Fragment,{children:e?.TripReport_Stops?.map(e=>l.jsx("tr",{className:" border-b    ",children:l.jsx("td",{className:"",colSpan:2,children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsx("tbody",{children:l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:l.jsx("span",{className:"w-48 inline-block",children:"Trip stop"})}),l.jsx("td",{className:"",children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsxs("tbody",{children:[l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${e?.StopLocation?.ID>0?e?.StopLocation?.Title:""}`]}),l.jsx("td",{className:"px-6 pb-4",children:e?.DesignatedDangerousGoodsSailing&&e2("DesignatedDangerousGoodsSailing")?"This is a designated dangerous goods sailing":"-"})]},`${e.ID}-stop-location`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Arrival"}),`${e?.ArriveTime}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Departure"}),`${e?.DepartTime}`]})]},`${e.ID}-arr-time`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Pax joined"}),`${e?.PaxJoined}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Pax departed"}),`${e?.PaxDeparted}`]})]},`${e.ID}-pax`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Vehicles joined"}),`${e?.VehiclesJoined}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Vehicles departed"}),`${e?.VehiclesDeparted}`]})]},`${e.ID}-vehicles`),e?.OtherCargo&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[l.jsx("span",{className:"w-48 inline-block",children:"Other cargo: "}),`${e?.OtherCargo}`]})},`${e.ID}-otherCargo`),e?.Comments&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[l.jsx("span",{className:"w-48 inline-block",children:"Comments: "}),`${e?.Comments}`]})},`${e.ID}-comments`)]})})})]})})})})}))}),e?.TripEvents?.length>0&&l.jsx(l.Fragment,{children:l.jsx("tr",{className:" border-b    ",children:l.jsx("td",{className:"",colSpan:2,children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsx("tbody",{children:e?.TripEvents?.map(s=>l.jsxs(l.Fragment,{children:["BarCrossing"===s.EventCategory&&l.jsx(l.Fragment,{children:l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:l.jsx("span",{className:"w-48 inline-block",children:"Activity - Bar crossing"})}),l.jsx("td",{className:"",children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsxs("tbody",{children:[l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Event Type"}),"Bar crossing"]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${s?.EventType_BarCrossing?.GeoLocation?.ID>0?s?.EventType_BarCrossing?.GeoLocation?.Title:""}`]})]},`${s.EventType_BarCrossing.ID}-bc-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Crossing start"}),`${s?.EventType_BarCrossing.Time}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Crossing end"}),`${s?.EventType_BarCrossing.TimeCompleted}`]})]},`${s.ID}-crossing-time`),s?.EventType_BarCrossing?.BarCrossingChecklist?.ID>0&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsx("td",{className:"",colSpan:2,children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsx("tbody",{children:l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:l.jsx("span",{className:"w-48 inline-block",children:"Bar crossing checklist"})}),l.jsx("td",{className:"p-0",children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsxs("tbody",{children:[Object.entries(s?.EventType_BarCrossing?.BarCrossingChecklist).filter(([e,s])=>1===s).length>0&&l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 border-r-1",children:Object.entries(s?.EventType_BarCrossing?.BarCrossingChecklist).filter(([e,s])=>1===s).map(([e,s])=>e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),l.jsx("td",{className:"px-6 pb-4",children:"Ok"})]},`${s.id}-checklist-y`),Object.entries(s?.EventType_BarCrossing?.BarCrossingChecklist).filter(([e,s])=>0===s).length>0&&l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 border-r-1",children:Object.entries(s?.EventType_BarCrossing?.BarCrossingChecklist).filter(([e,s])=>0===s).map(([e,s])=>e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},`${s.id}-checklist-n`)]})})})]})})})})},`${s.ID}-checklist`),s?.EventType_BarCrossing?.BarCrossingChecklist?.RiskFactors?.length>0&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsx("td",{className:"px-6 pb-4",colSpan:2,children:s?.EventType_BarCrossing?.BarCrossingChecklist.RiskFactors.map((e,s)=>l.jsxs("div",{className:"inline-block",children:[s>0?", ":"Risk factors: ",`${e.Title} - ${e.Impact} - ${e.Probability}/10`]},e.ID))})})]})})})]})}),"RestrictedVisibility"===s.EventCategory&&l.jsx(l.Fragment,{children:l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:l.jsx("span",{className:"w-48 inline-block",children:"Activity - Restricted visibility"})}),l.jsx("td",{className:"",children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsxs("tbody",{children:[l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[l.jsx("span",{className:"w-48 inline-block",children:"Event Type"}),"Restricted visibility"]})},`${s.EventType_RestrictedVisibility.ID}-rv-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Start location"}),`${s?.EventType_RestrictedVisibility?.StartLocation?.ID>0?s?.EventType_RestrictedVisibility?.StartLocation?.Title:""}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"End location"}),`${s?.EventType_RestrictedVisibility?.EndLocation?.ID>0?s?.EventType_RestrictedVisibility?.EndLocation?.Title:""}`]})]},`${s.EventType_RestrictedVisibility.ID}-locationrv`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Crossing Time"}),`${s?.EventType_RestrictedVisibility?.CrossingTime??"-"}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Crossed time"}),`${s?.EventType_RestrictedVisibility?.CrossedTime??"-"}`]})]},`${s.ID}-locationbc`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Safe speed"}),`${s?.EventType_RestrictedVisibility?.EstSafeSpeed??"-"}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Avg speed"}),`${s?.EventType_RestrictedVisibility?.ApproxSafeSpeed??"-"}`]})]},`${s.ID}-speed`),l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsx("td",{className:"",colSpan:2,children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsx("tbody",{children:l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:l.jsx("span",{className:"w-48 inline-block",children:"Safe operating procedures checklist"})}),l.jsx("td",{className:"p-0",children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsxs("tbody",{children:[Object.entries(s?.EventType_RestrictedVisibility).filter(([e,s])=>1===s).length>0&&l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 border-r-1",children:Object.entries(s?.EventType_RestrictedVisibility).filter(([e,s])=>1===s).map(([e,s])=>e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),l.jsx("td",{className:"px-6 pb-4",children:"Ok"})]},`${s.id}-checklist-y`),Object.entries(s?.EventType_RestrictedVisibility).filter(([e,s])=>0===s).length>0&&l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 border-r-1",children:Object.entries(s?.EventType_RestrictedVisibility).filter(([e,s])=>0===s).map(([e,s])=>e.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]},`${s.id}-checklist-n`)]})})})]})})})})},`${s.ID}-checklist`)]})})})]})}),"CrewTraining"===s.EventCategory&&l.jsx(l.Fragment,{children:l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:l.jsx("span",{className:"w-48 inline-block",children:"Activity - Training"})}),l.jsx("td",{className:"",children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsxs("tbody",{children:[l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Event type"}),"Crew training"]}),l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Training date"}),`${p.p6(s.CrewTraining.Date)}`]})]},`${s.CrewTraining.ID}-ct-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Start Time"}),`${s?.CrewTraining?.StartTime}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"End time"}),`${s?.CrewTraining?.FinishTime}`]})]},`${s.CrewTraining.ID}-locationbc`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Trainer"}),`${s?.CrewTraining?.Trainer?.ID>0?s?.CrewTraining?.Trainer?.FirstName+" "+s?.CrewTraining?.Trainer?.Surname:""}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${s?.CrewTraining?.GeoLocation?.ID>0?s?.CrewTraining?.GeoLocation?.Title:""}`]})]},`${s.CrewTraining.ID}-trainer`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Crew trained"}),l.jsx("div",{className:"inline-block max-w-[calc(100%-12rem)]",children:s?.CrewTraining?.Members?.map((e,s)=>l.jsxs(l.Fragment,{children:[s>0&&", ",`${e.FirstName} ${e.Surname}`]}))})]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Training type"}),l.jsx("div",{className:"inline-block max-w-[calc(100%-12rem)]",children:s?.CrewTraining?.TrainingTypes?.map((e,s)=>l.jsxs(l.Fragment,{children:[s>0&&", ",`${e.title}`]}))})]})]},`${s.CrewTraining.id}-crew-trained`),s?.CrewTraining?.TrainingSummary&&l.jsx("tr",{className:" border-b last:border-b-0    ",children:l.jsx("td",{className:"px-6 pb-4",colSpan:2,children:l.jsxs("div",{className:"flex",children:[l.jsx("span",{className:"min-w-48 inline-block",children:"Training summary"}),l.jsx("div",{className:"inline-block",children:l.jsx("div",{dangerouslySetInnerHTML:{__html:s?.CrewTraining?.TrainingSummary}})})]})})})]})})})]})}),"Tasking"===s.EventCategory&&l.jsx(l.Fragment,{children:l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:l.jsx("span",{className:"w-48 inline-block",children:"Activity - Tasking"})}),l.jsx("td",{className:"",children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsxs("tbody",{children:[l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Event type"}),`${s?.EventType_Tasking?.Type.replace(/([A-Z])/g," $1")}`]}),l.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Time"}),`${s?.EventType_Tasking?.Time??"-"}`]})]},`${s.EventType_Tasking.ID}-et-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Title"}),`${s?.EventType_Tasking?.Title??"-"}`]}),l.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${s?.EventType_Tasking?.GeoLocation?.ID>0?s?.EventType_Tasking?.GeoLocation?.Title:""}`]})]},`${s.EventType_Tasking.ID}-et2-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Activity type"}),e1(s.EventType_Tasking,e)]}),l.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Fuel"}),`${s?.EventType_Tasking?.FuelLog?.length?s?.EventType_Tasking?.FuelLog[0]?.FuelAfter:"-"}`]})]},`${s.EventType_Tasking.ID}-et3-type`),s?.EventType_Tasking?.VesselRescueID>0&&s?.EventType_Tasking?.Type==="TaskingStartUnderway"&&l.jsxs(l.Fragment,{children:[l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Target vessel"}),`${s?.EventType_Tasking?.VesselRescue?.VesselName??"-"}`]}),l.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Call sign"}),`${s?.EventType_Tasking?.VesselRescue?.CallSign??"-"}`]})]},`${s.EventType_Tasking.ID}-et-vr1-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"P.O.B"}),`${s?.EventType_Tasking?.VesselRescue?.POB??"-"}`]}),l.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${s?.EventType_Tasking?.VesselRescue?.VesselLocation?.ID>0?s?.EventType_Tasking?.VesselRescue?.VesselLocation?.Title:""}`]})]},`${s.EventType_Tasking.ID}-et-vr2-type`),s?.EventType_Tasking?.VesselRescue?.LocationDescription&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[l.jsx("span",{className:"w-48 inline-block",children:"Location description"}),l.jsx("div",{className:"inline-block max-w-[calc(100%-12rem)]",children:`${s?.EventType_Tasking?.VesselRescue?.LocationDescription??"-"}`})]})},`${s.EventType_Tasking.ID}-et-vr3-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Vessel length"}),`${s?.EventType_Tasking?.VesselRescue?.VesselLength??"-"}`]}),l.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Vessel type"}),`${s?.EventType_Tasking?.VesselRescue?.VesselType??"-"}`]})]},`${s.EventType_Tasking.ID}-et-vr4-type`),s?.EventType_Tasking?.VesselRescue?.VesselTypeDescription&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[l.jsx("span",{className:"w-48 inline-block",children:"Vessel description"}),l.jsx("div",{className:"inline-block max-w-[calc(100%-12rem)]",children:`${s?.EventType_Tasking?.VesselRescue?.VesselTypeDescription??"-"}`})]})},`${s.EventType_Tasking.ID}-et-vr5-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Make and model"}),`${s?.EventType_Tasking?.VesselRescue?.MakeAndModel??"-"}`]}),l.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Vessel color"}),`${s?.EventType_Tasking?.VesselRescue?.Color??"-"}`]})]},`${s.EventType_Tasking.ID}-et-vr6-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Owner name"}),`${s?.EventType_Tasking?.VesselRescue?.OwnerName??"-"}`]}),l.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Owner on board?"}),`${s?.EventType_Tasking?.vesselRescue?.OwnerOnBoard?"Yes":"No"}`]})]},`${s.EventType_Tasking.id}-et-vr7-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Owner phone"}),`${s?.EventType_Tasking?.VesselRescue?.Phone??"-"}`]}),l.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"CG Membership"}),`${s?.EventType_Tasking?.VesselRescue?.CGMembership??"-"}`]})]},`${s.EventType_Tasking.ID}-et-vr8-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Owner email"}),`${s?.EventType_Tasking?.VesselRescue?.Email??"-"}`]}),l.jsxs("td",{className:"px-6 pb-4 w-1/2",children:[l.jsx("span",{className:"w-48 inline-block",children:"Owner address"}),`${s?.EventType_Tasking?.VesselRescue?.Address??"-"}`]})]},`${s.EventType_Tasking.ID}-et-vr9-type`),s?.EventType_Tasking?.CGOP&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[l.jsx("span",{className:"w-48 inline-block",children:"Incident details"}),`CoastGuard Rescue - ${s?.EventType_Tasking?.CGOP??"-"}`]})},`${s.EventType_Tasking.ID}-et-incident-cgop`),s?.EventType_Tasking?.SAROP&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[l.jsx("span",{className:"w-48 inline-block",children:"Incident details"}),`SAROP - ${s?.EventType_Tasking?.SAROP??"-"}`]})},`${s.EventType_Tasking.ID}-et-incident-sarop`)]}),s?.EventType_Tasking?.VesselRescueID>0&&s?.EventType_Tasking?.Type==="TaskingComplete"&&l.jsxs(l.Fragment,{children:[s?.EventType_Tasking?.VesselRescue?.Mission?.ID>0&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[l.jsx("span",{className:"w-48 inline-block",children:"Mission outcome"}),`${s?.EventType_Tasking?.VesselRescue?.Mission?.OperationOutcome?.replace(/_/g," ")}`]})},`${s.EventType_Tasking.ID}-et-mission`),s?.EventType_Tasking?.VesselRescue?.MissionTimeline?.length>0&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsxs("td",{className:"px-6",colSpan:2,children:[l.jsx("div",{className:"pb-4",children:"Mission notes/comments"}),l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsx("tbody",{children:s?.EventType_Tasking?.VesselRescue?.MissionTimeline?.map(e=>l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"pr-6 pb-4",children:`${p.o0(e?.Time)} - ${e?.CommentType}`}),l.jsx("td",{className:"px-6 pb-4",children:l.jsx("div",{dangerouslySetInnerHTML:{__html:e?.Description}})}),l.jsx("td",{className:"px-6 pb-4",children:`${e?.Author?.ID>0?e?.Author?.FirstName+" "+e?.Author?.Surname:""}`})]},`${e.id}-et-mission-timeline`))})})]})},`${s.EventType_Tasking.ID}-et-mission-timeline`)]}),s?.EventType_Tasking?.VesselRescueID>0&&s?.EventType_Tasking?.Type==="TaskingOnTow"&&l.jsx(l.Fragment,{children:s?.EventType_Tasking?.VesselRescue?.Mission?.ID>0&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsxs("td",{className:"px-6",colSpan:2,children:[l.jsx("div",{className:"pb-4",children:"Towing checklist - risk analysis"}),l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsxs("tbody",{children:[Object.entries(e8(s.EventType_Tasking,e)).filter(([e,s])=>!0===s).length>0&&l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"pr-6 pb-4 border-r-1",children:Object.entries(e8(s.EventType_Tasking,e)).filter(([e,s])=>!0===s).map(([e,s])=>({key:e,value:s})).map(e=>e.key.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),l.jsx("td",{className:"px-6 pb-4",children:"Ok"})]}),Object.entries(e8(s.EventType_Tasking,e)).filter(([e,s])=>!1===s).length>0&&l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"pr-6 pb-4 border-r-1",children:Object.entries(e8(s.EventType_Tasking,e)).filter(([e,s])=>!1===s).map(([e,s])=>({key:e,value:s})).map(e=>e.key.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())).join(", ")}),l.jsx("td",{className:"px-6 pb-4 w-32",children:"Not Ok"})]}),e8(s.EventType_Tasking,e)?.RiskFactors?.length>0&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsx("td",{className:"pr-6 pb-4",colSpan:2,children:e8(s.EventType_Tasking,e)?.RiskFactors?.map((e,s)=>l.jsxs("div",{className:"inline-block",children:[s>0?", ":"Risk factors: ",`${e.Title} - ${e.Impact} - ${e.Probability}/10`]},e.ID))})}),e8(s.EventType_Tasking,e)?.Member?.ID>0&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsx("td",{className:"pr-6 pb-4",colSpan:2,children:`Author: ${e8(s.EventType_Tasking,e)?.Member?.FirstName} ${e8(s.EventType_Tasking,e)?.Member?.Surname}`})})]})})]})},`${s.EventType_Tasking.ID}-et-mission`)})]})})})]})}),!1&&0,"PassengerDropFacility"===s.EventCategory&&l.jsx(l.Fragment,{children:l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:l.jsx("span",{className:"w-48 inline-block",children:`Activity - ${s?.EventType_PassengerDropFacility?.Type.replace("Passenger","")}`})}),l.jsx("td",{className:"",children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsxs("tbody",{children:[l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Title"}),`${s?.EventType_PassengerDropFacility?.Title}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Time"}),`${s?.EventType_PassengerDropFacility?.Time}`]})]},`${s.EventType_PassengerDropFacility.ID}-pdf-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Location"}),`${s?.EventType_PassengerDropFacility?.GeoLocation?.ID>0?s?.EventType_PassengerDropFacility?.GeoLocation?.Title:"-"}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Fuel"}),`${s?.EventType_PassengerDropFacility?.FuelLog?.length?s?.EventType_PassengerDropFacility?.FuelLog?.[0]?.FuelAfter:"-"}`]})]},`${s.id}-location-fuel`)]})})})]})}),"EventSupernumerary"===s.EventCategory&&l.jsx(l.Fragment,{children:l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:l.jsx("span",{className:"w-48 inline-block",children:"Activity - Supernumerary"})}),l.jsx("td",{className:"",children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:l.jsxs("tbody",{children:[l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Title"}),`${s?.Supernumerary?.Title}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Briefing time"}),`${s?.Supernumerary?.BriefingTime?s?.Supernumerary?.BriefingTime:"-"}`]})]},`${s.Supernumerary.ID}-sup-type`),l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsxs("td",{className:"px-6 pb-4 border-r-1",children:[l.jsx("span",{className:"w-48 inline-block",children:"Total guests"}),`${s?.Supernumerary?.TotalGuest}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Is briefed?"}),`${s?.Supernumerary?.IsBriefed?"Yes":"No"}`]})]},`${s.ID}-sup-time`),s?.Supernumerary?.GuestList?.length>0&&l.jsx("tr",{className:" border-b last:border-b-0  ",children:l.jsxs("td",{className:"px-6 pb-4",colSpan:2,children:[l.jsx("span",{className:"w-48 inline-block",children:"Guests list"}),s?.Supernumerary?.GuestList?.map(e=>l.jsx("div",{children:`${e.FirstName} ${e.Surname}`},`${e.ID}-sup-gl`))]})},`${s.ID}-guest-list`)]})})})]})}),"InfringementNotice"===s.EventCategory&&l.jsx(l.Fragment,{children:l.jsxs("tr",{className:" border-b last:border-b-0  ",children:[l.jsx("td",{className:"px-6 pb-4 w-48 border-r-1",children:l.jsx("span",{className:"w-48 inline-block",children:"Activity - InfringementNotice"})}),l.jsx("td",{className:"",children:l.jsx("table",{className:"w-full  text-left rtl:text-right  ",children:st(s)})})]})})]}))})})})})}),l.jsxs("tr",{className:" border-b    ",children:[l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Arrival"}),`Expected time: ${e?.arriveTime} - Actual time: ${h()(e?.Arrive).format("HH:mm:ss")}`]}),l.jsxs("td",{className:"px-6 pb-4",children:[l.jsx("span",{className:"w-48 inline-block",children:"Arrival location"})," ",e?.ToLocation?.ID>0?e?.ToLocation?.Title:e?.ToLocation?.Lat+" "+e?.ToLocation?.Long]})]}),e?.Comment&&l.jsx("tr",{className:" border-b    ",children:l.jsx("td",{className:"px-6 pb-4",colSpan:2,children:l.jsxs("div",{className:"flex",children:[l.jsx("span",{className:"min-w-48 inline-block",children:"Comment"}),l.jsx("div",{className:"inline-block",children:l.jsx("div",{dangerouslySetInnerHTML:{__html:e?.Comment}})})]})})})]})})})},e.ID))]})]})]}):l.jsx(g.Z,{message:"Generating the PDF report..."}),l.jsx("pre",{className:" p-4 rounded overflow-auto",hidden:!0,children:JSON.stringify({logbook:w,vesselDailyCheck:z},null,2)})]})}},85283:(e,s,r)=>{"use strict";r.d(s,{Z:()=>n});var l=r(98768);r(60343);var a=r(83048);let t=new(r.n(a)()).S3({endpoint:"https://ddde1c1cd1aa25641691808dcbafdeb7.r2.cloudflarestorage.com",accessKeyId:"06c3e13a539f24e6fdf7075bf381bf5e",secretAccessKey:"****************************************************************",signatureVersion:"v4",region:"auto"});async function n(e){let s="signature/"+e.id+".png";try{let r=await t.getObject({Bucket:"signature",Key:s}).promise();if(r.Body){let s=Buffer.from(r.Body).toString("base64"),a=`data:image/png;base64,${s}`;return l.jsx("img",{src:a,alt:e?.alt,width:e?.width,height:e?.height,className:e?.className})}}catch(e){console.error(e)}return l.jsx(l.Fragment,{})}},34966:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});let l=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\log-entries\oldEntry\page.tsx#default`)}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),l=s.X(0,[864,8865,3563,6263,8189,9507,7602,2935,9707,1608,6451,4234,2925,5394,4837,3842,7033,2654],()=>r(23192));module.exports=l})();