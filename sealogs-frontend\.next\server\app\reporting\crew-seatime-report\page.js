(()=>{var e={};e.id=9107,e.ids=[9107],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},67306:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>m,tree:()=>c}),r(96563),r(71238),r(78398),r(57757),r(48045);var s=r(40060),i=r(33581),n=r(57567),o=r.n(n),a=r(51650),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let c=["",{children:["reporting",{children:["crew-seatime-report",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96563)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\crew-seatime-report\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71238)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\reporting\\crew-seatime-report\\page.tsx"],d="/reporting/crew-seatime-report/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/reporting/crew-seatime-report/page",pathname:"/reporting/crew-seatime-report",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55405:(e,t,r)=>{Promise.resolve().then(r.bind(r,27188))},88332:(e,t,r)=>{Promise.resolve().then(r.bind(r,44517))},91973:(e,t,r)=>{"use strict";r.d(t,{u:()=>s});let s=(e,t="report.csv")=>{let r=new Blob([e.map(e=>e.join(",")).join("\n")],{type:"text/csv;charset=utf-8,"}),s=URL.createObjectURL(r);Object.assign(document.createElement("a"),{href:s,download:t}).click(),URL.revokeObjectURL(s)}},2604:(e,t,r)=>{"use strict";r.d(t,{S:()=>o});var s=r(9707),i=r(58774),n=r.n(i);function o(e,t){let r=new s.default(t);n()(r,{head:e.headers,body:e.body,foot:e.footers,margin:5,...e.userOptions}),r.save(e.fileName||"report.pdf")}},27188:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var s=r(98768);r(46776);var i=r(26100),n=r(37042),o=r(94060),a=r(79418),l=r(60343),c=r(83179),u=r.n(c),d=r(91973),p=r(2604),m=r(49517),g=r(69424),h=r(26659),x=r(25394);function f(){let e=(0,g.useRouter)(),[t,r]=(0,l.useState)([]),[i,c]=(0,l.useState)([]),[f,j]=(0,l.useState)([]),[v,y]=(0,l.useState)("detailed"),[w,b]=(0,l.useState)({startDate:new Date,endDate:new Date}),[N,{called:S,loading:k,data:P}]=(0,a.t)(o.Y,{fetchPolicy:"cache-and-network",onError:e=>{console.error("queryLogBookEntrySections error",e)}}),M=(0,l.useCallback)(()=>{let e={},r={vehicleID:null,startDate:null};t.length>0&&(e.crewMemberID={in:t.map(e=>e.value)}),i.length>0&&(e.dutyPerformedID={in:i.map(e=>e.value)}),f.length>0&&(r.vehicleID={in:f.map(e=>e.value)}),null!==w.startDate&&null!==w.endDate&&(r.startDate={gte:w.startDate,lte:w.endDate}),(null!==r.vehicleID||null!==r.startDate)&&(null===r.vehicleID&&delete r.vehicleID,null===r.startDate&&delete r.startDate,e.logBookEntry=r),N({variables:{filter:e}})},[t,f,w,N]),q=(0,l.useMemo)(()=>{let e=(P?.readCrewMembers_LogBookEntrySections?.nodes??[]).filter(e=>null!==e.punchOut).map(e=>{let t=u()(e.punchIn),r=u()(e.punchOut).diff(t,"minutes");return{crewID:+e.crewMember.id,crewName:`${e.crewMember.firstName} ${e.crewMember.surname}`,totalLoggedMinutes:r,loggedDuration:{hours:r>=60?Math.floor(r/60):0,minutes:r%60},loginTime:new Date(e.punchIn),logoutTime:new Date(e.punchOut),dutyPerformedID:+e.dutyPerformedID,primaryDuty:e.dutyPerformed.title,vesselID:+e.logBookEntry.vehicleID,vesselName:e.logBookEntry.vehicle.title,workDetails:e.workDetails}});if("detailed"===v)return e;let t=e.map(e=>`${e.crewID}|${e.dutyPerformedID}|${e.vesselID}`),r=[];return new Set(t).forEach(t=>{let[s,i,n]=t.split("|"),o=e.filter(e=>e.crewID===+s&&e.dutyPerformedID===+i&&e.vesselID===+n),a=o.reduce((e,t)=>e+t.totalLoggedMinutes,0),l={...o[0],loginTime:w.startDate,logoutTime:w.endDate,totalLoggedMinutes:a,loggedDuration:{hours:a>=60?Math.floor(a/60):0,minutes:a%60}};r.push(l)}),r},[S,P,k,v]);return(0,s.jsxs)(s.Fragment,{children:[s.jsx(x.Bu,{title:"Crew Seatime Report",actions:s.jsx("div",{className:"flex",children:s.jsx(x.zx,{variant:"back",onClick:()=>e.push("/reporting"),children:"Back"})})}),s.jsx(x.Zb,{className:"mt-8",children:(0,s.jsxs)(x.aY,{className:"flex flex-col gap-4",children:[s.jsx(n.Z,{onChange:({type:e,data:t})=>{switch(e){case"dateRange":b(t);break;case"members":r(t);break;case"vessels":j(t);break;case"crewDuty":c(t);break;case"reportMode":y(t)}},onClick:M}),s.jsx(m.Z,{onDownloadPdf:()=>{if(0===q.length)return;let e=q.map(function(e){return[e.crewName+"",e.vesselName+"",e.primaryDuty+"",u()(e.loginTime).format("DD/MM/YY HH:mm")+"",u()(e.logoutTime).format("DD/MM/YY HH:mm")+"",`${e.loggedDuration.hours>0?`${e.loggedDuration.hours}h `:""}${e.loggedDuration.minutes}m`]});(0,p.S)({headers:[["Crew","Vessel","Duty","Signed in","Signed out","Time spent"]],body:e})},onDownloadCsv:()=>{if(0===q.length)return;let e=[];e.push(["crew","vessel","duty","signed in","signed out","time spent"]),q.forEach(t=>{e.push([t.crewName,t.vesselName,t.primaryDuty,t.loginTime.toISOString(),t.logoutTime.toISOString(),`${t.loggedDuration.hours>0?`${t.loggedDuration.hours}h `:""}${t.loggedDuration.minutes}m`])}),(0,d.u)(e)}}),(0,s.jsxs)(h.iA,{children:[s.jsx(h.xD,{children:(0,s.jsxs)(h.SC,{children:[s.jsx(h.ss,{children:"Crew member name"}),s.jsx(h.ss,{children:"Vessel"}),s.jsx(h.ss,{children:"Duty"}),s.jsx(h.ss,{children:"Signed in"}),s.jsx(h.ss,{children:"Signed out"}),s.jsx(h.ss,{children:"Time spent"})]})}),s.jsx(h.RM,{children:s.jsx(D,{isLoading:S&&k,reportData:q})})]})]})})]})}function D({reportData:e,isLoading:t}){return t?s.jsx(h.SC,{children:s.jsx(h.pj,{colSpan:6,className:"text-center  h-32",children:"Loading..."})}):0==e.length?s.jsx(h.SC,{children:s.jsx(h.pj,{colSpan:6,className:"text-center  h-32",children:"No Data Found"})}):e.map((e,t)=>(0,s.jsxs)(h.SC,{className:"group border-b  hover: ",children:[s.jsx(h.pj,{className:"px-2 py-3 text-left",children:s.jsx("div",{className:" inline-block ml-3",children:e.crewName})}),s.jsx(h.pj,{className:"px-2 py-3 text-left",children:s.jsx("div",{className:" inline-block ",children:e.vesselName})}),s.jsx(h.pj,{className:"px-2 py-3 text-left",children:s.jsx("div",{className:" inline-block ",children:e.primaryDuty})}),s.jsx(h.pj,{className:"px-2 py-3 text-left",children:s.jsx("div",{className:" inline-block ",children:u()(e.loginTime).format("DD/M/YY HH:mm")})}),s.jsx(h.pj,{className:"px-2 py-3 text-left",children:s.jsx("div",{className:" inline-block ",children:u()(e.logoutTime).format("DD/M/YY HH:mm")})}),s.jsx(h.pj,{className:"px-2 py-3 text-left",children:(0,s.jsxs)("div",{className:" inline-block ",children:[0!=e.loggedDuration.hours?`${e.loggedDuration.hours}h, `:"",e.loggedDuration.minutes,"m"]})})]},`${e.crewID}-${t}`))}function j(){let[e,t]=(0,l.useState)(!1),[r,n]=(0,l.useState)(!1);return e&&r?s.jsx(f,{}):e?s.jsx(i.Z,{errorMessage:"Oops You do not have the permission to view this section."}):s.jsx(i.Z,{})}},44517:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(98768);r(60343);var i=r(32993),n=r(64837);function o({children:e}){return s.jsx(i.default,{children:s.jsx(n.Z,{children:e})})}},49517:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var s=r(98768),i=r(39544),n=r(76915);function o({onDownloadCsv:e,onDownloadPdf:t}){return(0,s.jsxs)("div",{className:"flex gap-3 mb-3",children:[e&&s.jsx(i.Button,{iconLeft:n.Z,type:"button",onClick:e,children:"Download CSV"}),t&&s.jsx(i.Button,{iconLeft:n.Z,type:"button",onClick:t,children:"Download PDF"})]})}},96563:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\crew-seatime-report\page.tsx#default`)},71238:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\reporting\layout.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,9707,6250,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042],()=>r(67306));module.exports=s})();