(()=>{var e={};e.id=6262,e.ids=[6262],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},29029:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),n(91194),n(2223),n(78398),n(57757),n(48045);var a=n(40060),s=n(33581),r=n(57567),l=n.n(r),i=n(51650),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);n.d(t,o);let d=["",{children:["inventory",{children:["view",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,91194)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\view\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,2223)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(n.bind(n,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\inventory\\view\\page.tsx"],u="/inventory/view/page",m={require:n,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/inventory/view/page",pathname:"/inventory/view",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},85363:(e,t,n)=>{Promise.resolve().then(n.bind(n,71462))},38041:(e,t,n)=>{Promise.resolve().then(n.bind(n,80527))},47520:(e,t,n)=>{"use strict";n.d(t,{default:()=>s.a});var a=n(19821),s=n.n(a)},19821:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}});let a=n(41034);n(98768),n(60343);let s=a._(n(40907));function r(e,t){var n;let a={loading:e=>{let{error:t,isLoading:n,pastDelay:a}=e;return null}};"function"==typeof e&&(a.loader=e);let r={...a,...t};return(0,s.default)({...r,modules:null==(n=r.loadableGenerated)?void 0:n.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96359:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return s}});let a=n(90408);function s(e){let{reason:t,children:n}=e;throw new a.BailoutToCSRError(t)}},40907:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let a=n(98768),s=n(60343),r=n(96359),l=n(58902);function i(e){return{default:e&&"default"in e?e.default:e}}let o={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},d=function(e){let t={...o,...e},n=(0,s.lazy)(()=>t.loader().then(i)),d=t.loading;function c(e){let i=d?(0,a.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,o=t.ssr?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.PreloadCss,{moduleIds:t.modules}),(0,a.jsx)(n,{...e})]}):(0,a.jsx)(r.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(n,{...e})});return(0,a.jsx)(s.Suspense,{fallback:i,children:o})}return c.displayName="LoadableComponent",c}},58902:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return r}});let a=n(98768),s=n(54580);function r(e){let{moduleIds:t}=e,n=(0,s.getExpectedRequestStore)("next/dynamic css"),r=[];if(n.reactLoadableManifest&&t){let e=n.reactLoadableManifest;for(let n of t){if(!e[n])continue;let t=e[n].files.filter(e=>e.endsWith(".css"));r.push(...t)}}return 0===r.length?null:(0,a.jsx)(a.Fragment,{children:r.map(e=>(0,a.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:n.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},71462:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});var a=n(98768);n(60343);var s=n(64837);function r({children:e}){return a.jsx(s.Z,{children:e})}},80527:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>q});var a=n(98768),s=n(60343),r=n(79418),l=n(72548),i=n(94060),o=n(33849),d=n(35024),c=n(76342),u=n(69424),m=n(66263),p=n(17380),v=n(13842);n(46776);var h=n(26100),x=n(71890),g=n(81524),y=n(52269),f=n(36895),j=n(26509),b=n(87175),I=n(39544),C=n(78965),w=n(25394),N=n(84340),S=n(13609),k=n(60309),E=n(52241),D=n(50058),P=n(93488),B=n(34376),_=n(93778);function T({inventoryID:e,inventoryTab:t=""}){let n=(0,u.useSearchParams)(),[T,q]=(0,s.useState)("info"),[L,O]=(0,s.useState)(),[M,V]=(0,s.useState)(),[U,A]=(0,s.useState)(),[F,R]=(0,s.useState)(),[Z,H]=(0,s.useState)(),[G,z]=(0,s.useState)(),[Y,$]=(0,s.useState)(),[Q,K]=(0,s.useState)(),[W,X]=(0,s.useState)(0),[J,ee]=(0,s.useState)(!1),[et,en]=(0,s.useState)(),[ea,es]=(0,s.useState)(!1),[er,el]=(0,s.useState)(!1),[ei,eo]=(0,s.useState)(!1),[ed,ec]=(0,s.useState)(!1),[eu,em]=(0,s.useState)([]),[ep,ev]=(0,s.useState)([]),[eh,ex]=(0,s.useState)([]),eg=(0,u.useRouter)(),[ey,ef]=(0,s.useState)(!1),[ej,eb]=(0,s.useState)(!1),[eI,eC]=(0,s.useState)(!1),[ew,eN]=(0,s.useState)(!1),[eS,ek]=(0,s.useState)(!1),[eE,eD]=(0,s.useState)(!1),{getVesselWithIcon:eP}=(0,E.P)(),eB=(0,D.k)(),{toast:e_}=(0,B.pm)();(0,v.ig)(e,n=>{O({...n,content:(()=>{let e="null"!==n.content?n.content??"":"",t="null"!==n.description?n.description??"":"";return`${e} ${t}`.trim()})()}),"maintenance"===t&&ee(!0),z({label:n?.location,value:0}),A(n?.categories?.nodes?.map(e=>({label:e.name,value:e.id}))),H(n?.suppliers?.nodes?.map(e=>({label:e.name,value:e.id}))),eT({variables:{inventoryID:+e,vesselID:0}}),em(n?.documents?.nodes),ex(n?.attachmentLinks?.nodes.map(e=>({label:e.link,value:e.id})))}),(0,v.sy)(e=>{$([...e.filter(e=>!e.archived).map(e=>({...e})),{title:"Other",id:"0"}])}),(0,v.Fb)(e=>{R([{label:" ---- Create supplier ---- ",value:"newSupplier"},...e?.filter(e=>null!==e.name).map(e=>({label:e.name,value:e.id}))])}),(0,v.oA)(e=>{V([{label:" ---- Create Category ---- ",value:"newCategory"},...e?.filter(e=>null!==e.name&&!1===e.archived).map(e=>({label:e.name,value:e.id}))])});let[eT]=(0,r.t)(i.gQ,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readComponentMaintenanceCheckList[0].list;if(t){let e=t.filter(e=>e?.archived!=1);K(t),X(e.filter(e=>"High"===e.isOverDue.status).length),eL(Array.from(new Set(t.filter(e=>e.assignedToID>0).map(e=>e.assignedToID))))}},onError:e=>{console.error("queryMaintenanceCheck error",e)}}),[eq]=(0,r.t)(i.rd,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readSeaLogsMembers.nodes;t&&en(t)},onError:e=>{console.error("queryCrewMemberInfo error",e)}}),eL=async e=>{await eq({variables:{crewMemberIDs:e.length>0?e:[0]}})},eO=async()=>{if(!L){console.error("Inventory page has not been initialised, possibly a slow internet connection, please try after a few seconds"),e_({description:"Please wait to initialize the inventory before saving",variant:"destructive"});return}if(!eI){e_({description:"You do not have permission to edit this inventory",variant:"destructive"});return}let e={input:{id:+L.id,item:document.getElementById("inventory-name").value?document.getElementById("inventory-name").value:L.item,title:document.getElementById("inventory-name").value?document.getElementById("inventory-name").value:L.title,location:document.getElementById("inventory-location").value?document.getElementById("inventory-location").value:L.location,description:null,content:L.content,quantity:document.getElementById("inventory-qty").value?parseInt(document.getElementById("inventory-qty").value):L.quantity,productCode:document.getElementById("inventory-code").value?document.getElementById("inventory-code").value:L.productCode,costingDetails:document.getElementById("inventory-cost").value?document.getElementById("inventory-cost").value:L.costingDetails,comments:document.getElementById("inventory-comments").value?document.getElementById("inventory-comments").value:L.comments,archived:L.archived,inventoryImportID:L.inventoryImportID,vesselID:G.value?G.value:L.vesselID,documents:eu.map(e=>e.id).join(","),categories:U?.map(e=>e.value).length?U.map(e=>e.value).join(","):L.categories.nodes.map(e=>e.id).join(","),suppliers:Z?.map(e=>e.value).length?Z.map(e=>e.value).join(","):L.suppliers.nodes.map(e=>e.id).join(","),attachmentLinks:eh?eh.map(e=>e.value).join(","):L.attachmentLinks?.nodes.map(e=>e.id).join(",")}};await eM({variables:e})},[eM,{loading:eV}]=(0,l.D)(c.HcK,{onCompleted:e=>{e.updateInventory.id>0?n.get("redirect_to")?eg.push(n?.get("redirect_to")+""):eg.back():console.error("mutationupdateInventory error",e)},onError:e=>{console.error("mutationupdateInventory error",e)}}),eU=async()=>{let e=document.getElementById("inventory-new-category").value;return await eA({variables:{input:{name:e}}})},[eA,{loading:eF}]=(0,l.D)(c.CQz,{onCompleted:e=>{let t=e.createInventoryCategory;t.id>0?(V([...M,{label:t.name,value:t.id}]),A([...U,{label:t.name,value:t.id}]),eo(!1)):console.error("mutationcreateInventoryCategory error",e)},onError:e=>{console.error("mutationcreateInventoryCategory error",e)}}),eR=async()=>{if(!ew){e_({description:"You do not have permission to delete this inventory",variant:"destructive"});return}await eZ({variables:{ids:[+L.id]}})},[eZ,{loading:eH}]=(0,l.D)(c.pmE,{onCompleted:e=>{e.deleteInventories&&e.deleteInventories.length>0?eg.push("/inventory"):console.error("mutationdeleteInventories failed to delete:",e)},onError:e=>{console.error("mutationdeleteInventories error:",e.message)}}),eG=e=>{var t={label:"",value:""};"string"==typeof e&&(t={label:e,value:e}),"object"==typeof e&&(t={label:document.getElementById("inventory-new-location").value,value:document.getElementById("inventory-new-location-id").value?document.getElementById("inventory-new-location-id").value:document.getElementById("inventory-new-location").value}),$([...Y.map(e=>({...e})),{Title:t.label,ID:t.value}]),z(t),es(!1)},ez=async()=>{let e=document.getElementById("supplier-name").value,t=document.getElementById("supplier-website").value,n=document.getElementById("supplier-phone").value,a=document.getElementById("supplier-email").value,s=document.getElementById("supplier-address").value;""!==e&&await eY({variables:{input:{name:e,address:s,website:t,email:a,phone:n}}}),el(!1)},[eY,{loading:e$}]=(0,l.D)(c.aL5,{onCompleted:e=>{let t=e.createSupplier;t.id>0?(R([...F,{label:t.name,value:t.id}]),H([...Z,{label:t.name,value:t.id}])):console.error("mutationcreateSupplier error",e)},onError:e=>{console.error("mutationcreateSupplier error",e)}}),[eQ]=(0,l.D)(c.zfn,{onCompleted:e=>{let t=e.createSeaLogsFileLinks;t.id>0&&(ev([...ep,t]),eh?ex([...eh,{label:t.link,value:t.id}]):ex([{label:t.link,value:t.id}]))},onError:e=>{console.error("createSeaLogsFileLinksEntry error",e)}}),eK=e=>{ex(eh.filter(t=>t!==e))},eW=e=>e.label?(0,a.jsxs)("div",{className:"flex justify-between align-middle mr-2 w-fit",children:[a.jsx(m.default,{href:e.label,target:"_blank",className:"ml-2 ",children:e.label}),a.jsx("div",{className:"ml-2 ",children:a.jsx(I.Button,{variant:"destructive",iconOnly:!0,iconLeft:S.Z,onClick:()=>eK(e)})})]}):null;return ey&&eS?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(d.Zb,{children:[a.jsx(d.Ol,{children:(0,a.jsxs)(d.ll,{className:"text-2xl font-medium",children:[a.jsx("span",{className:"text-muted-foreground mr-2",children:"Inventory:"}),L?.item]})}),a.jsx(j.Separator,{className:"mb-3"}),a.jsx(d.aY,{children:(0,a.jsxs)(f.Tabs,{defaultValue:"info",value:T,onValueChange:q,children:[(0,a.jsxs)(f.TabsList,{children:[a.jsx(f.TabsTrigger,{value:"info",children:"Item Info"}),(0,a.jsxs)(f.TabsTrigger,{value:"tasks",className:"relative",children:["Tasks / Maintenance",W>0&&a.jsx(b.C,{variant:"outline",className:`ml-2 h-4 ${W>0?"bg-rose-100 text-rose-700 hover:bg-rose-200":"bg-emerald-100 text-emerald-700 hover:bg-emerald-200"}`,children:W})]})]}),a.jsx(f.TabsContent,{value:"tasks",className:"pt-4",children:a.jsx("div",{className:"w-full",children:Q&&Y?a.jsx(N.nA,{maintenanceChecks:Q,vessels:Y,crewInfo:et}):a.jsx(p.hM,{})})}),a.jsx(f.TabsContent,{value:"info",children:(0,a.jsxs)("div",{className:"space-y-6 py-4",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid sm:grid-cols-2 gap-4",children:[a.jsx(x.I,{id:"inventory-name",type:"text",defaultValue:L?.item,placeholder:"Inventory name",readOnly:!eI}),Y&&L?a.jsx(g.Combobox,{id:"inventory-vessel",options:Y?.map(e=>{let t=eP(e.id,e);return{label:e.title,value:e.id,vessel:t}}),isDisabled:!eI,defaultValues:L?.vesselID&&L?.vesselID==0?{label:"Other",value:"0"}:L?.vessel?{label:L.vessel.title,value:L.vessel.id,vessel:eP(L.vessel.id,L.vessel)}:null,className:"w-full",placeholder:"Select Vessel",onChange:e=>{e&&"newLocation"===e.value&&es(!0),z(e)}}):a.jsx(p.U3,{})]}),(0,a.jsxs)("div",{className:"grid sm:grid-cols-2 gap-4",children:[a.jsx(x.I,{id:"inventory-location",type:"text",defaultValue:L?.location,placeholder:"Location",readOnly:!eI}),a.jsx(x.I,{id:"inventory-qty",type:"number",defaultValue:L?.quantity,placeholder:"Quantity",readOnly:!eI})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"col-span-1",children:[a.jsx("h3",{className:"text-lg font-medium mb-2",children:"Description"}),a.jsx("p",{className:"text-muted-foreground text-sm leading-relaxed",children:"Enter details that might help with the maintenance or operation of this item."})]}),a.jsx("div",{className:"col-span-2 space-y-4",children:L&&a.jsx(o.Z,{id:"inventory-Content",content:L?.content,handleEditorChange:e=>{O({...L,content:e})}})})]}),a.jsx(j.Separator,{}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"col-span-1",children:[a.jsx("h3",{className:"text-lg font-medium mb-2",children:"Inventory details"}),a.jsx("p",{className:"text-muted-foreground text-sm leading-relaxed",children:"In this section categorise the item and add the suppliers where you normally purchase this item and the expected cost. This will help replacing the item in the future."})]}),a.jsx("div",{className:"col-span-2",children:(0,a.jsxs)(d.Zb,{className:"space-y-5",children:[a.jsx(w.__,{label:"Product code",htmlFor:"inventory-code",children:a.jsx(x.I,{id:"inventory-code",type:"text",defaultValue:L?.productCode,placeholder:"Product code",readOnly:!eI})}),L&&M?a.jsx(g.Combobox,{className:"w-full",label:"Categories",id:"inventory-categories",defaultValues:L.categories&&L.categories.nodes.map(e=>({label:e.name,value:e.id})),isDisabled:!eI,value:U,multi:!0,options:M,onChange:e=>{e.find(e=>"newCategory"===e.value)&&eo(!0),A(e.filter(e=>"newCategory"!==e.value))}}):a.jsx(p.U3,{}),L&&F?a.jsx(g.Combobox,{label:"Supplier",className:"w-full",id:"inventory-suppliers",defaultValues:L.Suppliers&&F?.filter(e=>L?.Suppliers&&Object.keys(L.Suppliers).includes(e?.value?.toString())).map(e=>({label:e.label,value:e.value})),multi:!0,isDisabled:!eI,value:Z,onChange:e=>{e.find(e=>"newSupplier"===e.value)&&el(!0),H(e.filter(e=>"newSupplier"!==e.value))},options:F}):a.jsx(p.U3,{}),a.jsx(w.__,{label:"Cost",htmlFor:"inventory-cost",children:a.jsx(x.I,{id:"inventory-cost",type:"text",defaultValue:L?.costingDetails,placeholder:"Costing details",readOnly:!eI})}),(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx(w.__,{label:"Links",htmlFor:"task-title",children:a.jsx(x.I,{id:"task-title",type:"text",placeholder:"Type a link and press Enter",readOnly:!eI,onKeyDown:async e=>{if("Enter"===e.key){let t=e.target.value;await eQ({variables:{input:{link:t}}}),e.target.value=""}}})}),a.jsx("div",{className:"flex flex-wrap gap-2",children:eh?eh.map(e=>a.jsx("div",{children:eW(e)},e.value)):ep.map(e=>a.jsx("div",{children:eW(e)},e.value))})]})]})})]}),a.jsx(j.Separator,{}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"col-span-1",children:[a.jsx("h3",{className:"text-lg font-medium mb-2",children:"Attachment"}),a.jsx("p",{className:"text-muted-foreground text-sm leading-relaxed",children:"Upload things like photos of the item, plus warranty and guarantee documents or operating manuals. Add links to any online manuals or product descriptions."})]}),a.jsx("div",{className:"col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6",children:a.jsx("div",{className:"col-span-2 flex items-end",children:a.jsx("div",{className:"w-full flex flex-col space-y-2",children:a.jsx(_.Z,{inputId:1,sectionId:e,buttonType:"button",sectionName:"inventoryID",editable:eI})})})})]}),a.jsx(j.Separator,{}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"col-span-1",children:[a.jsx("h3",{className:"text-lg font-medium mb-2",children:"Comment"}),a.jsx("p",{className:"text-muted-foreground text-sm leading-relaxed",children:"Comments are sent to directly to a person (use @name) to send a comment to someone."})]}),a.jsx("div",{className:"col-span-2 space-y-4",children:a.jsx(y.Textarea,{id:"inventory-comments",rows:5,defaultValue:L?.comments,placeholder:"Comments",readOnly:!eI})})]})]})})]})}),(0,a.jsxs)(C.V,{children:[a.jsx(I.Button,{variant:"back",onClick:()=>eg.back(),children:"Cancel"}),eB["tablet-md"]||"tasks"===T?a.jsx(I.Button,{variant:"primaryOutline",onClick:()=>{if(!ej){e_({description:"You do not have permission to edit this section",variant:"destructive"});return}eg.push("/maintenance/new?inventoryId="+e+"&vesselId="+L?.vesselID+"&redirectTo=inventory")},children:(0,P.p)(eB.phablet,"Create","Create task/maintenance")}):null,"tasks"!==T?(0,a.jsxs)(a.Fragment,{children:[a.jsx(I.Button,{variant:"destructive",onClick:()=>{L&&ec(!0)},children:eB.phablet?"Delete":a.jsx(k.Z,{})}),a.jsx(I.Button,{onClick:eO,children:(0,P.p)(eB.phablet,"Update","Update inventory")})]}):null]})]}),(0,a.jsxs)(w.h9,{openDialog:ea,setOpenDialog:es,handleCreate:()=>eG({}),actionText:"Create Location",children:[a.jsx(w.H3,{children:"Create New Location"}),a.jsx("div",{className:"my-4 flex items-center",children:a.jsx(x.I,{id:"inventory-new-location",type:"text",placeholder:"Location"})}),a.jsx("div",{className:"flex items-center",children:a.jsx(x.I,{id:"inventory-new-location-id",type:"text",placeholder:"Location ID"})})]}),(0,a.jsxs)(w.h9,{openDialog:er,setOpenDialog:el,handleCreate:ez,actionText:"Create supplier",className:"lg:max-w-lg",children:[a.jsx(w.H3,{children:"Create new supplier"}),(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("div",{className:"mb-4",children:a.jsx(x.I,{id:"supplier-name",type:"text",placeholder:"Supplier name"})}),a.jsx("div",{className:"mb-4",children:a.jsx(x.I,{id:"supplier-website",type:"text",placeholder:"Website"})}),a.jsx("div",{className:"mb-4",children:a.jsx(x.I,{id:"supplier-phone",type:"text",placeholder:"Phone"})}),a.jsx("div",{className:"mb-4",children:a.jsx(x.I,{id:"supplier-email",type:"email",placeholder:"Email"})}),a.jsx("div",{children:a.jsx(y.Textarea,{id:"supplier-address",rows:4,className:" p-2",placeholder:"Supplier address"})})]})]}),(0,a.jsxs)(w.h9,{openDialog:ei,setOpenDialog:eo,handleCreate:eU,actionText:"Create Category",children:[a.jsx(w.H3,{children:"Create new category"}),a.jsx("div",{className:"my-4 flex items-center",children:a.jsx(x.I,{id:"inventory-new-category",type:"text",placeholder:"Category"})})]}),(0,a.jsxs)(w.h9,{openDialog:ed,setOpenDialog:ec,handleCreate:eR,actionText:"Delete Inventory",children:[a.jsx(w.H3,{children:"Delete Inventory"}),(0,a.jsxs)("div",{className:"my-4 flex items-center",children:["Are you sure you want to delete ",L?.item,"?"]})]})]}):ey?a.jsx(h.Z,{errorMessage:"Oops You do not have the permission to view this section."}):a.jsx(h.Z,{})}function q(){let e=(0,u.useSearchParams)(),t=parseInt(e.get("id")||""),n=e.get("inventoryTab")||"";return a.jsx(T,{inventoryID:t,inventoryTab:n})}},93778:(e,t,n)=>{"use strict";n.d(t,{Z:()=>m});var a=n(98768),s=n(60343),r=n(79418),l=n(75776),i=n(94060),o=n(39544),d=n(78853),c=n(69422),u=n(34376);function m({inputId:e=0,buttonType:t="icon",sectionName:n="logBookEntryID",sectionId:m=0,editable:p=!0}){let[v,h]=(0,s.useState)([]),[x,g]=(0,s.useState)([]),[y]=(0,r.t)(i.HC,{fetchPolicy:"cache-and-network",onCompleted:e=>{let t=e.readCaptureImages.nodes;t&&h(t)},onError:e=>{console.error("getFieldImages error",e)}}),f=async()=>{await y({variables:{filter:{[n]:{eq:m}}}})};return a.jsx(a.Fragment,{children:0===e||0===m?a.jsx("div",{className:"w-full flex",children:a.jsx(o.Button,{variant:"icon"===t?"ghost":"outline",size:"icon",iconOnly:"icon"===t,title:"Add comment",className:"icon"===t?"group":"h-10",iconLeft:a.jsx(d.Z,{className:"icon"===t?(0,c.cn)("text-curious-blue-400 group-hover:text-curious-blue-400/50","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300"):"",size:24}),onClick:()=>(0,u.Am)({title:"Please save the section first",description:"You need to save the section in order to capture or upload images.",variant:"destructive"}),children:"button"===t&&"Capture / Upload Image"})}):(0,a.jsxs)(a.Fragment,{children:[x.length>0&&a.jsx("div",{className:"flex flex-wrap mb-4",children:x.map((e,t)=>a.jsx("div",{className:"w-1/5 p-1 rounded-md relative",children:a.jsx("img",{src:e.imageData,alt:`Captured ${t}`,className:"object-cover rounded-md"},t)},t))}),p&&a.jsx("div",{className:"w-full flex",children:a.jsx(l.Z,{file:!!(v&&Array.isArray(v))&&v.filter(t=>t.fieldName===e).sort((e,t)=>t.id-e.id),setFile:f,inputId:e.toString(),buttonType:"button",sectionData:{id:m,sectionName:n}})})]})})}},2223:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});let a=(0,n(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\inventory\layout.tsx#default`)},91194:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});let a=(0,n(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\inventory\view\page.tsx#default`)},30854:()=>{},60309:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});let a=(0,n(97428).Z)("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),a=t.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,9707,6250,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7380,7944,5776,8410],()=>n(29029));module.exports=a})();