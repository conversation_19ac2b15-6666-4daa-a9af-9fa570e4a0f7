(()=>{var e={};e.id=5368,e.ids=[5368],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},18139:e=>{"use strict";e.exports=require("dgram")},82266:e=>{"use strict";e.exports=require("domain")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},85807:e=>{"use strict";e.exports=require("module")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},74026:e=>{"use strict";e.exports=require("string_decoder")},62758:e=>{"use strict";e.exports=require("timers")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},80154:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>u,routeModule:()=>g,tree:()=>d}),t(63180),t(86439),t(78398),t(57757),t(48045);var i=t(40060),s=t(33581),a=t(57567),n=t.n(a),o=t(51650),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d=["",{children:["maintenance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,63180)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\maintenance\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,86439)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\maintenance\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,78398)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,57757)),"C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,48045,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,88570))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\src\\app\\maintenance\\page.tsx"],c="/maintenance/page",p={require:t,loadChunk:()=>Promise.resolve()},g=new i.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/maintenance/page",pathname:"/maintenance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},27536:(e,r,t)=>{Promise.resolve().then(t.bind(t,24012))},56109:(e,r,t)=>{Promise.resolve().then(t.bind(t,57566))},75546:(e,r,t)=>{"use strict";t.d(r,{Br:()=>p,fU:()=>g,o0:()=>u,p6:()=>d,vq:()=>c});var i=t(83179),s=t.n(i),a=t(7678),n=t.n(a),o=t(14826),l=t.n(o);let d=(e="",r=!0)=>{let t;if(n()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[t,i,s]=e.split("-"),a=r?t.slice(-2):t,n=parseInt(s,10).toString().padStart(2,"0"),o=parseInt(i,10).toString().padStart(2,"0");return`${n}/${o}/${a}`}if(!(t=e&&"object"==typeof e?s()(e.toString()):s()(e)).isValid())return"";let i=t.format("DD"),a=t.format("MM"),o=r?t.format("YY"):t.format("YYYY");return`${i}/${a}/${o}`},u=(e="",r=!0)=>{let t;if(n()(l()(e)))return"";if("string"==typeof e&&/^\d{4}-\d{2}-\d{2}$/.test(e)){let[t,i,s]=e.split("-"),a=r?t.slice(-2):t,n=parseInt(s,10).toString().padStart(2,"0"),o=parseInt(i,10).toString().padStart(2,"0");return`${n}/${o}/${a} 00:00`}if("string"==typeof e&&/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}(:\d{2})?$/.test(e)){let[t,i]=e.split(" "),[s,a,n]=t.split("-"),o=r?s.slice(-2):s,l=i.split(":"),d=l[0].padStart(2,"0"),u=l[1].padStart(2,"0"),c=parseInt(n,10).toString().padStart(2,"0"),p=parseInt(a,10).toString().padStart(2,"0");return`${c}/${p}/${o} ${d}:${u}`}if(!(t=e&&"object"==typeof e?s()(e.toString()):s()(e)).isValid())return"";let i=t.format("DD"),a=t.format("MM"),o=r?t.format("YY"):t.format("YYYY"),d=t.format("HH:mm");return`${i}/${a}/${o} ${d}`},c=(e="")=>n()(l()(e))?"":s()(e).format("YYYY-MM-DD HH:mm:ss"),p=(e="")=>n()(l()(e))?new Date:new Date(`${e}T10:00:00Z`),g=(e,r)=>{let t=e=>/^\d{1,2}:\d{2}(:\d{2})?$/.test(e),i=e=>e.includes(" ")?e.replace(" ","T"):e,s=e=>{if(!e||"string"!=typeof e)return null;if(t(e)){let r=new Date().toISOString().split("T")[0];return new Date(`${r}T${e}`)}return new Date(i(e))},a=s(e),n=s(r);return!a||!n||isNaN(a.getTime())||isNaN(n.getTime())?(console.warn("Invalid input passed to isLate():",{expectedArrival:e,actualArrival:r}),!1):n>a}},24012:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var i=t(98768),s=t(64837);function a({children:e}){return i.jsx(s.Z,{children:e})}t(60343)},57566:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var i=t(98768),s=t(76889),a=t(69424),n=t(84340);function o(){let e=(0,a.useSearchParams)(),r=e.get("taskID")??0,t=e.get("redirectTo")??"",o=e.get("vesselID")??0;return i.jsx(i.Fragment,{children:0==+r?i.jsx(n.ZP,{}):i.jsx(s.Z,{taskId:+r,redirectTo:t,vesselID:+o})})}},93778:(e,r,t)=>{"use strict";t.d(r,{Z:()=>p});var i=t(98768),s=t(60343),a=t(79418),n=t(75776),o=t(94060),l=t(39544),d=t(78853),u=t(69422),c=t(34376);function p({inputId:e=0,buttonType:r="icon",sectionName:t="logBookEntryID",sectionId:p=0,editable:g=!0}){let[v,m]=(0,s.useState)([]),[h,f]=(0,s.useState)([]),[b]=(0,a.t)(o.HC,{fetchPolicy:"cache-and-network",onCompleted:e=>{let r=e.readCaptureImages.nodes;r&&m(r)},onError:e=>{console.error("getFieldImages error",e)}}),x=async()=>{await b({variables:{filter:{[t]:{eq:p}}}})};return i.jsx(i.Fragment,{children:0===e||0===p?i.jsx("div",{className:"w-full flex",children:i.jsx(l.Button,{variant:"icon"===r?"ghost":"outline",size:"icon",iconOnly:"icon"===r,title:"Add comment",className:"icon"===r?"group":"h-10",iconLeft:i.jsx(d.Z,{className:"icon"===r?(0,u.cn)("text-curious-blue-400 group-hover:text-curious-blue-400/50","will-change-transform will-change-width will-change-padding transform-gpu","group-hover:transition-colors group-hover:ease-out group-hover:duration-300"):"",size:24}),onClick:()=>(0,c.Am)({title:"Please save the section first",description:"You need to save the section in order to capture or upload images.",variant:"destructive"}),children:"button"===r&&"Capture / Upload Image"})}):(0,i.jsxs)(i.Fragment,{children:[h.length>0&&i.jsx("div",{className:"flex flex-wrap mb-4",children:h.map((e,r)=>i.jsx("div",{className:"w-1/5 p-1 rounded-md relative",children:i.jsx("img",{src:e.imageData,alt:`Captured ${r}`,className:"object-cover rounded-md"},r)},r))}),g&&i.jsx("div",{className:"w-full flex",children:i.jsx(n.Z,{file:!!(v&&Array.isArray(v))&&v.filter(r=>r.fieldName===e).sort((e,r)=>r.id-e.id),setFile:x,inputId:e.toString(),buttonType:"button",sectionData:{id:p,sectionName:t}})})]})})}},8416:(e,r,t)=>{"use strict";t.d(r,{tz:()=>p});var i=t(98768),s=t(60343),a=t(85745),n=t(8750),o=t(70906),l=t(56937),d=t(74602);let u=(0,a.j)("cursor-pointer w-full px-4 rounded-e-lg will-change-transform will-change-color will-change-padding transform-gpu hover:transition-all hover:ease-out hover:duration-300",{variants:{variant:{default:"hover:bg-fire-bush-100 hover:border-yellow-vivid-600",primary:"hover:bg-light-blue-vivid-50 hover:border-light-blue-vivid-600",secondary:"hover:bg-background hover:border-neutral-400",success:"hover:bg-bright-turquoise-100 hover:border-teal-600",destructive:"hover:bg-red-vivid-50 hover:border-red-vivid-600",warning:"hover:bg-fire-bush-100 hover:border-yellow-vivid-600",pink:"hover:bg-pink-vivid-50 hover:border-pink-vivid-600",outline:"hover:bg-background hover:border-neutral-400","light-blue":"hover:bg-light-blue-vivid-50 hover:border-light-blue-vivid-600"},size:{default:"py-[10.5px]",sm:"py-2",lg:"py-6"},disabled:{true:"hover:bg-transparent hover:border-border",false:""}},defaultVariants:{variant:"default",size:"default",disabled:!1}}),c=(0,a.j)("relative inset-x-0 w-12 rounded-s-lg justify-center flex items-center",{variants:{variant:{default:"bg-light-blue-vivid-50 border-light-blue-vivid-600",primary:"bg-light-blue-vivid-50 border-light-blue-vivid-600",secondary:"bg-background border-neutral-400",success:"bg-bright-turquoise-100 border-teal-600",destructive:"bg-red-vivid-50 border-red-vivid-600",warning:"bg-fire-bush-100 border-yellow-vivid-600",pink:"bg-pink-vivid-50 border-pink-vivid-600",outline:"bg-background border-neutral-400","light-blue":"bg-light-blue-vivid-50 border-light-blue-vivid-600"}},defaultVariants:{variant:"default"}}),p=s.forwardRef(({type:e="checkbox",id:r,checked:t,onCheckedChange:a,disabled:p,value:g,name:v,label:m,children:h,className:f,variant:b,size:x,radioGroupValue:y,isRadioStyle:w=!0,rightContent:j,leftContent:S,onClick:q,...N},$)=>{let k=s.useId(),P=r||`${e}-${k}`,C="radio"===e?y===g:t;return(0,i.jsxs)("div",{ref:$,className:(0,l.cn)("flex rounded-lg relative group justify-evenly border border-border shadow-sm cursor-pointer",p&&"opacity-50 cursor-not-allowed",f),onClick:r=>{!p&&("checkbox"===e&&a?a(!t):"radio"===e&&a&&!C&&a(!0),q&&q(r))},...N,children:[i.jsx("div",{className:(0,l.cn)(c({variant:b})),children:"checkbox"===e?i.jsx(n.Checkbox,{id:P,isRadioStyle:w,checked:t,onCheckedChange:e=>{"boolean"==typeof e&&a&&a(e)},disabled:p,name:v,variant:b,size:"lg",className:"pointer-events-none"}):i.jsx(o.mJ,{id:P,value:g||"",disabled:p,variant:b,size:"md",checked:C,className:"pointer-events-none"})}),i.jsx("div",{className:(0,l.cn)("flex items-center",u({variant:"secondary",size:x,disabled:p})),children:(0,i.jsxs)("div",{className:(0,l.cn)("flex flex-1 items-center",{"gap-2":S||j}),children:[S&&i.jsx("div",{className:"inline-flex items-center",children:S}),h||m&&i.jsx(d.P,{className:(0,l.cn)("text-wrap text-foreground text-base"),children:m}),j&&i.jsx("div",{className:"inline-flex items-center",children:j})]})})]})});p.displayName="CheckFieldLabel"},86439:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});let i=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\maintenance\layout.tsx#default`)},63180:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});let i=(0,t(38851).createProxy)(String.raw`C:\Users\<USER>\Music\SeaLogsV2\sealogs-frontend\src\app\maintenance\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[864,8865,3563,6263,8189,9507,7602,2935,2964,1863,9707,6250,8822,5959,6451,4234,2925,5394,4837,6342,3842,88,8712,90,7346,7042,7380,7944,8410,6889],()=>t(80154));module.exports=i})();